{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "oysa0lp3Ym1j", "outputId": "1b53e723-22b5-49f6-e57a-8aa1488caeda"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain\n", "  Downloading langchain-0.1.17-py3-none-any.whl (867 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m867.6/867.6 kB\u001b[0m \u001b[31m8.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-google-genai\n", "  Downloading langchain_google_genai-1.0.3-py3-none-any.whl (31 kB)\n", "Collecting faiss-cpu\n", "  Downloading faiss_cpu-1.8.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (27.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m27.0/27.0 MB\u001b[0m \u001b[31m40.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting pypdf\n", "  Downloading pypdf-4.2.0-py3-none-any.whl (290 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m290.4/290.4 kB\u001b[0m \u001b[31m27.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.0.29)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.9.5)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (4.0.3)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain)\n", "  Downloading dataclasses_json-0.6.5-py3-none-any.whl (28 kB)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Collecting langchain-community<0.1,>=0.0.36 (from langchain)\n", "  Downloading langchain_community-0.0.36-py3-none-any.whl (2.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m69.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-core<0.2.0,>=0.1.48 (from langchain)\n", "  Downloading langchain_core-0.1.48-py3-none-any.whl (302 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m302.9/302.9 kB\u001b[0m \u001b[31m29.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-text-splitters<0.1,>=0.0.1 (from langchain)\n", "  Downloading langchain_text_splitters-0.0.1-py3-none-any.whl (21 kB)\n", "Collecting langsmith<0.2.0,>=0.1.17 (from langchain)\n", "  Downloading langsmith-0.1.52-py3-none-any.whl (116 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m116.4/116.4 kB\u001b[0m \u001b[31m10.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.25.2)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.7.1)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (8.2.3)\n", "Requirement already satisfied: google-generativeai<0.6.0,>=0.5.2 in /usr/local/lib/python3.10/dist-packages (from langchain-google-genai) (0.5.2)\n", "Collecting docarray[hnswlib]<0.33.0,>=0.32.0 (from langchain)\n", "  Downloading docarray-0.32.1-py3-none-any.whl (215 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m215.3/215.3 kB\u001b[0m \u001b[31m16.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: typing_extensions>=4.0 in /usr/local/lib/python3.10/dist-packages (from pypdf) (4.11.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.9.4)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain)\n", "  Downloading marshmallow-3.21.2-py3-none-any.whl (49 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.3/49.3 kB\u001b[0m \u001b[31m5.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Collecting orjson>=3.8.2 (from docarray[hnswlib]<0.33.0,>=0.32.0->langchain)\n", "  Downloading orjson-3.10.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (142 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m142.7/142.7 kB\u001b[0m \u001b[31m815.4 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: rich>=13.1.0 in /usr/local/lib/python3.10/dist-packages (from docarray[hnswlib]<0.33.0,>=0.32.0->langchain) (13.7.1)\n", "Collecting types-requests>=2.28.11.6 (from docarray[hnswlib]<0.33.0,>=0.32.0->langchain)\n", "  Downloading types_requests-2.31.0.20240406-py3-none-any.whl (15 kB)\n", "Collecting hnswlib>=0.6.2 (from docarray[hnswlib]<0.33.0,>=0.32.0->langchain)\n", "  Downloading hnswlib-0.8.0.tar.gz (36 kB)\n", "  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: protobuf>=3.19.0 in /usr/local/lib/python3.10/dist-packages (from docarray[hnswlib]<0.33.0,>=0.32.0->langchain) (3.20.3)\n", "Requirement already satisfied: google-ai-generativelanguage==0.6.2 in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (0.6.2)\n", "Requirement already satisfied: google-api-core in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (2.11.1)\n", "Requirement already satisfied: google-api-python-client in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (2.84.0)\n", "Requirement already satisfied: google-auth>=2.15.0 in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (2.27.0)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (4.66.2)\n", "Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.3 in /usr/local/lib/python3.10/dist-packages (from google-ai-generativelanguage==0.6.2->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (1.23.0)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain)\n", "  Downloading jsonpointer-2.4-py2.py3-none-any.whl (7.8 kB)\n", "Collecting packaging<24.0,>=23.2 (from langchain-core<0.2.0,>=0.1.48->langchain)\n", "  Downloading packaging-23.2-py3-none-any.whl (53 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m53.0/53.0 kB\u001b[0m \u001b[31m8.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.2 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (2.18.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2024.2.2)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.0.3)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth>=2.15.0->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (5.3.3)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth>=2.15.0->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (0.4.0)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth>=2.15.0->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (4.9)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /usr/local/lib/python3.10/dist-packages (from rich>=13.1.0->docarray[hnswlib]<0.33.0,>=0.32.0->langchain) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /usr/local/lib/python3.10/dist-packages (from rich>=13.1.0->docarray[hnswlib]<0.33.0,>=0.32.0->langchain) (2.16.1)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Requirement already satisfied: googleapis-common-protos<2.0.dev0,>=1.56.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (1.63.0)\n", "Requirement already satisfied: httplib2<1dev,>=0.15.0 in /usr/local/lib/python3.10/dist-packages (from google-api-python-client->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (0.22.0)\n", "Requirement already satisfied: google-auth-httplib2>=0.1.0 in /usr/local/lib/python3.10/dist-packages (from google-api-python-client->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (0.1.1)\n", "Requirement already satisfied: uritemplate<5,>=3.0.1 in /usr/local/lib/python3.10/dist-packages (from google-api-python-client->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (4.1.1)\n", "Requirement already satisfied: grpcio<2.0dev,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (1.62.2)\n", "Requirement already satisfied: grpcio-status<2.0.dev0,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (1.48.2)\n", "Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in /usr/local/lib/python3.10/dist-packages (from httplib2<1dev,>=0.15.0->google-api-python-client->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (3.1.2)\n", "Requirement already satisfied: mdurl~=0.1 in /usr/local/lib/python3.10/dist-packages (from markdown-it-py>=2.2.0->rich>=13.1.0->docarray[hnswlib]<0.33.0,>=0.32.0->langchain) (0.1.2)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth>=2.15.0->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (0.6.0)\n", "Building wheels for collected packages: hnswlib\n", "  Building wheel for hnswlib (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for hnswlib: filename=hnswlib-0.8.0-cp310-cp310-linux_x86_64.whl size=2323492 sha256=b41a5336e1b974d12564a2970e6ccec6abddb9ea617027315d59a204c38d4412\n", "  Stored in directory: /root/.cache/pip/wheels/af/a9/3e/3e5d59ee41664eb31a4e6de67d1846f86d16d93c45f277c4e7\n", "Successfully built hnswlib\n", "Installing collected packages: types-requests, pypdf, packaging, orjson, mypy-extensions, jsonpointer, hnswlib, faiss-cpu, typing-inspect, marshmallow, jsonpatch, langsmith, docarray, dataclasses-json, langchain-core, langchain-text-splitters, langchain-community, langchain, langchain-google-genai\n", "  Attempting uninstall: packaging\n", "    Found existing installation: packaging 24.0\n", "    Uninstalling packaging-24.0:\n", "      Successfully uninstalled packaging-24.0\n", "Successfully installed dataclasses-json-0.6.5 docarray-0.32.1 faiss-cpu-1.8.0 hnswlib-0.8.0 jsonpatch-1.33 jsonpointer-2.4 langchain-0.1.17 langchain-community-0.0.36 langchain-core-0.1.48 langchain-google-genai-1.0.3 langchain-text-splitters-0.0.1 langsmith-0.1.52 marshmallow-3.21.2 mypy-extensions-1.0.0 orjson-3.10.2 packaging-23.2 pypdf-4.2.0 types-requests-2.31.0.20240406 typing-inspect-0.9.0\n"]}], "source": ["%pip install --upgrade  langchain langchain-google-genai \"langchain[docarray]\" faiss-cpu pypdf"]}, {"cell_type": "code", "source": ["import os\n", "import requests\n", "from PIL import Image"], "metadata": {"id": "SuBM06ben3nZ"}, "execution_count": 2, "outputs": []}, {"cell_type": "code", "source": ["import matplotlib.pyplot as plt\n", "import matplotlib.image as mpimg\n", "from IPython.display import display, Markdown"], "metadata": {"id": "5wkdBia9oMKh"}, "execution_count": 3, "outputs": []}, {"cell_type": "code", "source": ["from langchain_google_genai import ChatGoogleGenerativeAI"], "metadata": {"id": "NYdyB53coS2E"}, "execution_count": 4, "outputs": []}, {"cell_type": "code", "source": ["from langchain_core.messages import HumanMessage, SystemMessage"], "metadata": {"id": "kRbG38lzoVyk"}, "execution_count": 5, "outputs": []}, {"cell_type": "code", "source": ["from langchain.vectorstores import DocArrayInMemorySearch"], "metadata": {"id": "JN7UyGProXxS"}, "execution_count": 6, "outputs": []}, {"cell_type": "code", "source": ["from langchain_google_genai import GoogleGenerativeAIEmbeddings"], "metadata": {"id": "Y65k-jUioZcD"}, "execution_count": 7, "outputs": []}, {"cell_type": "code", "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate"], "metadata": {"id": "YAxMXEaloP5J"}, "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": ["from langchain.schema.document import Document\n", "from langchain_community.document_loaders import TextLoader"], "metadata": {"id": "cIwDydB5obpB"}, "execution_count": 9, "outputs": []}, {"cell_type": "code", "source": ["from langchain_text_splitters import CharacterTextSplitter"], "metadata": {"id": "hp2OWo5Ooe9Y"}, "execution_count": 10, "outputs": []}, {"cell_type": "code", "source": ["from langchain_community.vectorstores import FAISS"], "metadata": {"id": "qLxSPRlMog3S"}, "execution_count": 11, "outputs": []}, {"cell_type": "code", "source": ["from google.colab import userdata\n", "GOOGLE_API_KEY=userdata.get('GOOGLE_API_KEY')\n", "os.environ[\"GOOGLE_API_KEY\"] = GOOGLE_API_KEY"], "metadata": {"id": "DPMkR5BloiiB"}, "execution_count": 12, "outputs": []}, {"cell_type": "code", "source": ["def load_model(model_name):\n", "  if model_name==\"gemini-pro\":\n", "    llm = ChatGoogleGenerativeAI(model=\"gemini-pro\")\n", "  else:\n", "    llm=ChatGoogleGenerativeAI(model=\"gemini-pro-vision\")\n", "\n", "  return llm\n"], "metadata": {"id": "v6dATsOFo0VJ"}, "execution_count": 13, "outputs": []}, {"cell_type": "code", "source": ["model_text=load_model(\"gemini-pro\")"], "metadata": {"id": "67oGZQvHo7tC"}, "execution_count": 14, "outputs": []}, {"cell_type": "code", "source": ["model_text.invoke(\"please come up with the best funny line.\").content"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "q1WmHYc4pB1Y", "outputId": "832a2680-253d-46c4-b5a2-12c54578717c"}, "execution_count": 18, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'\"Why did the scarecrow win an award? Because he was outstanding in his field!\"'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 18}]}, {"cell_type": "code", "source": ["model_text(\n", "    [\n", "        HumanMessage(content=\"Answer with Simple 'Yes' or 'No'. Question: Is apple a Fruit?\")\n", "    ]\n", ").content"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 92}, "id": "0b2Ycj8ypNGi", "outputId": "11776336-3cd8-4966-fd0a-3df0d279a5ac"}, "execution_count": 19, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/langchain_core/_api/deprecation.py:119: LangChainDeprecationWarning: The method `BaseChatModel.__call__` was deprecated in langchain-core 0.1.7 and will be removed in 0.2.0. Use invoke instead.\n", "  warn_deprecated(\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'Yes'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 19}]}, {"cell_type": "code", "source": ["def get_image(url,filename,extension):\n", "  content = requests.get(url).content\n", "  with open(f'/content/{filename}.{extension}', 'wb') as f:\n", "    f.write(content)\n", "  image = Image.open(f\"/content/{filename}.{extension}\")\n", "  image.show()\n", "  return image\n", "\n"], "metadata": {"id": "Xa1fVCCBplBR"}, "execution_count": 20, "outputs": []}, {"cell_type": "code", "source": ["image = get_image(\"https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/1705ca64-fbc8-4b79-a451-4ab77760c219/dunk-low-older-shoes-C7T1cx.png\",\n", "                  \"nike-shoes\",\n", "                  \"png\")"], "metadata": {"id": "RgHHEYjjp206"}, "execution_count": 21, "outputs": []}, {"cell_type": "code", "source": ["plt.imshow(image)\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 435}, "id": "0EhD9Lbjp7AS", "outputId": "feca7266-a48c-465a-c052-2df87d7a65b5"}, "execution_count": 22, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["vision_model=load_model(\"gemini-pro-vision\")"], "metadata": {"id": "g8GURmtIqAzJ"}, "execution_count": 23, "outputs": []}, {"cell_type": "code", "source": ["prompt=\"give me summary of this image in 5 words\""], "metadata": {"id": "sZVlrxYLqsNI"}, "execution_count": 29, "outputs": []}, {"cell_type": "code", "source": ["message= HumanMessage(\n", "    content=[\n", "         {\n", "            \"type\": \"text\",\n", "            \"text\": prompt,\n", "        },\n", "        {\n", "\n", "            \"type\": \"image_url\", \"image_url\": image\n", "        }\n", "    ]\n", ")"], "metadata": {"id": "zJ116KMkqSfU"}, "execution_count": 30, "outputs": []}, {"cell_type": "code", "source": ["print(vision_model.invoke([message]).content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UaIyYPpPqN8h", "outputId": "92430ddc-b902-42b2-b41a-6813faa0fa7b"}, "execution_count": 31, "outputs": [{"output_type": "stream", "name": "stdout", "text": [" A gray and white Nike Dunk Low.\n"]}]}, {"cell_type": "code", "source": ["loader = TextLoader(\"/content/nike_shoes.txt\")\n", "print(loader.load()[0].page_content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Ttdz3y0pqcAy", "outputId": "13c867fe-0fec-4894-c380-aa3be708503d"}, "execution_count": 32, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Nike Air Max Plus sneakers. They feature a brown upper with a black Nike Swoosh logo on the side and a visible Air Max unit in the heel. The sole is white.\n", "Here are some more details about the Nike Air Max Plus:\n", "Style: TN\n", "Release date: January 1, 2017\n", "Style code: 852630-300\n", "Original retail price: $150 USD\n", "The Air Max Plus, also known as the TN, is a popular Nike running shoe that was first released in 1998. It is known for its unique design, which includes a gradient upper, visible Air Max units, and a wavy outsole. The TN has been a popular shoe among sneakerheads and casual wearers alike for over two decades.\n", "It features a brown upper with a black Swoosh logo and a white sole. The shoe is currently available for resale on the StockX marketplace for an average price of around $150 USD.\n", "Nike Air Max Plus Store Location: \"Kings Way, Kathmandu, Nepal\".\n", "\\n\\n\\n\n", "\n", "\n", "\n", "Nike Dunk Low Retro sneakers.\n", "Here are some more details about the Nike Dunk Low Retro:\n", "Style: Low-top\n", "Release date: October 31, 2020\n", "Style code: DD1391-100\n", "Original retail price: $100 USD\n", "The Dunk Low Retro is a popular Nike skateboarding shoe that was first released in 1985. It is a classic silhouette that has been released in a wide variety of colors and materials over the years.\n", "The shoes in the image are currently available for resale on the StockX marketplace for an average price of around $200 USD.\n", "Nike Dunk Low Retro sneakers Store Location: \"Patan, Lalitpur, Nepal\".\n", "\\n\\n\\n\n", "\n", "\n", "\n", "\n", "Nike slide/sandal.\n", "They appear to be the Nike Slide/Sandal, which is a unisex slide/sandal.\n", "Here are some of the features:\n", "Soft, one-piece upper: The upper is made of a soft, synthetic material that provides comfort and support.\n", "Phylon midsole: The midsole is made of Phylon, which provides cushioning and support.\n", "Rubber outsole: The outsole is made of rubber for traction and durability.\n", "Swoosh logo: The Nike Swoosh logo is on the strap of the sandal.\n", "Available in a variety of colors: The Nike Benassi Solarsoft Sandal is available in a variety of colors, including black, white, and beige.\n", "Nike off courte slides store location: \"Bhaktapur, Nepal\".\n", "\n", "\n"]}]}, {"cell_type": "code", "source": ["text=loader.load()[0].page_content"], "metadata": {"id": "Pw2Ibaver5iu"}, "execution_count": 36, "outputs": []}, {"cell_type": "code", "source": ["def get_text_chunks_langchain(text):\n", "  text_splitter = CharacterTextSplitter(chunk_size=20, chunk_overlap=10)\n", "  docs = [Document(page_content=x) for x in text_splitter.split_text(text)]\n", "  return docs"], "metadata": {"id": "fehnPFPGrnzJ"}, "execution_count": 37, "outputs": []}, {"cell_type": "code", "source": ["docs = get_text_chunks_langchain(text)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-uu45AFvrwex", "outputId": "465fd0e3-a652-4fe7-a1ec-505cb431accf"}, "execution_count": 38, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["WARNING:langchain_text_splitters.base:Created a chunk of size 869, which is longer than the specified 20\n", "WARNING:langchain_text_splitters.base:Created a chunk of size 588, which is longer than the specified 20\n"]}]}, {"cell_type": "code", "source": ["embeddings = GoogleGenerativeAIEmbeddings(model=\"models/embedding-001\")"], "metadata": {"id": "rPmyFEBKr31r"}, "execution_count": 39, "outputs": []}, {"cell_type": "code", "source": ["vectorstore = FAISS.from_documents(docs,embedding=embeddings)"], "metadata": {"id": "yi3NMD0pr_yI"}, "execution_count": 40, "outputs": []}, {"cell_type": "code", "source": ["retriever=vectorstore.as_retriever()"], "metadata": {"id": "taarsyO-sBXB"}, "execution_count": 41, "outputs": []}, {"cell_type": "code", "source": ["retriever.invoke(\"Nike slide/sandal.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fEW4gvOlsJAQ", "outputId": "ad430ac0-560e-4d8a-eb41-94830784a781"}, "execution_count": 42, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='Nike slide/sandal.\\nThey appear to be the Nike Slide/Sandal, which is a unisex slide/sandal.\\nHere are some of the features:\\nSoft, one-piece upper: The upper is made of a soft, synthetic material that provides comfort and support.\\nPhylon midsole: The midsole is made of Phylon, which provides cushioning and support.\\nRubber outsole: The outsole is made of rubber for traction and durability.\\nSwoosh logo: The Nike Swoosh logo is on the strap of the sandal.\\nAvailable in a variety of colors: The Nike Benassi Solarsoft Sandal is available in a variety of colors, including black, white, and beige.\\nNike off courte slides store location: \"Bhaktapur, Nepal\".'),\n", " Document(page_content='Nike Dunk Low Retro sneakers.\\nHere are some more details about the Nike Dunk Low Retro:\\nStyle: Low-top\\nRelease date: October 31, 2020\\nStyle code: DD1391-100\\nOriginal retail price: $100 USD\\nThe Dunk Low Retro is a popular Nike skateboarding shoe that was first released in 1985. It is a classic silhouette that has been released in a wide variety of colors and materials over the years.\\nThe shoes in the image are currently available for resale on the StockX marketplace for an average price of around $200 USD.\\nNike Dunk Low Retro sneakers Store Location: \"Patan, Lalitpur, Nepal\".\\n\\\\n\\\\n\\\\n'),\n", " Document(page_content='Nike Air Max Plus sneakers. They feature a brown upper with a black Nike Swoosh logo on the side and a visible Air Max unit in the heel. The sole is white.\\nHere are some more details about the Nike Air Max Plus:\\nStyle: TN\\nRelease date: January 1, 2017\\nStyle code: 852630-300\\nOriginal retail price: $150 USD\\nThe Air Max Plus, also known as the TN, is a popular Nike running shoe that was first released in 1998. It is known for its unique design, which includes a gradient upper, visible Air Max units, and a wavy outsole. The TN has been a popular shoe among sneakerheads and casual wearers alike for over two decades.\\nIt features a brown upper with a black Swoosh logo and a white sole. The shoe is currently available for resale on the StockX marketplace for an average price of around $150 USD.\\nNike Air Max Plus Store Location: \"Kings Way, Kathmandu, Nepal\".\\n\\\\n\\\\n\\\\n')]"]}, "metadata": {}, "execution_count": 42}]}, {"cell_type": "code", "source": ["from langchain_core.runnables import RunnableLambda, RunnablePassthrough"], "metadata": {"id": "lHaUxE20sM0x"}, "execution_count": 43, "outputs": []}, {"cell_type": "code", "source": ["llm_vision = load_model(\"gemini-pro-vision\")"], "metadata": {"id": "984WtM2AsRnh"}, "execution_count": 44, "outputs": []}, {"cell_type": "code", "source": ["llm_text = load_model(\"gemini-pro\")"], "metadata": {"id": "5uVuoC4qsq3M"}, "execution_count": 47, "outputs": []}, {"cell_type": "code", "source": ["template = \"\"\"\n", "```\n", "{context}\n", "```\n", "\n", "{query}\n", "\n", "\n", "Provide brief information and store location.\n", "\"\"\""], "metadata": {"id": "FovmYztwsVPh"}, "execution_count": 50, "outputs": []}, {"cell_type": "code", "source": ["prompt = ChatPromptTemplate.from_template(template)"], "metadata": {"id": "cF2y5fvUseFB"}, "execution_count": 51, "outputs": []}, {"cell_type": "code", "source": ["rag_chain = (\n", "    {\"context\": retriever, \"query\": RunnablePassthrough()}\n", "    | prompt\n", "    | llm_text\n", "    | StrOutputParser()\n", ")"], "metadata": {"id": "LummLlRtsf3p"}, "execution_count": 52, "outputs": []}, {"cell_type": "code", "source": ["result = rag_chain.invoke(\"can you give me a detail of nike sandal?\")"], "metadata": {"id": "IMSzYUOnsu0q"}, "execution_count": 53, "outputs": []}, {"cell_type": "code", "source": ["display(Markdown(result))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 186}, "id": "_uOV4g31s03y", "outputId": "703092ad-09fa-4cbd-8d93-d7ec793fc003"}, "execution_count": 54, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Markdown object>"], "text/markdown": "**Nike Benassi Solarsoft Sandal**\n\n**Features:**\n\n* Soft, one-piece upper for comfort and support\n* Phylon midsole for cushioning and support\n* Rubber outsole for traction and durability\n* Swoosh logo on the strap\n* Available in a variety of colors\n\n**Store Location:** \"Bhaktapur, Nepal\""}, "metadata": {}}]}, {"cell_type": "code", "source": ["rag_chain"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "S0NCym1_tMMM", "outputId": "1ed205c5-15be-47d2-e35a-e3ec5f007020"}, "execution_count": 55, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{\n", "  context: VectorStoreRetriever(tags=['FAISS', 'GoogleGenerativeAIEmbeddings'], vectorstore=<langchain_community.vectorstores.faiss.FAISS object at 0x7d2b6e953e80>),\n", "  query: RunnablePassthrough()\n", "}\n", "| ChatPromptTemplate(input_variables=['context', 'query'], messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['context', 'query'], template='\\n```\\n{context}\\n```\\n\\n{query}\\n\\n\\nProvide brief information and store location.\\n'))])\n", "| ChatGoogleGenerativeAI(model='gemini-pro', client=genai.GenerativeModel(\n", "      model_name='models/gemini-pro',\n", "      generation_config={},\n", "      safety_settings={},\n", "      tools=None,\n", "      system_instruction=None,\n", "  ))\n", "| StrOutputParser()"]}, "metadata": {}, "execution_count": 55}]}, {"cell_type": "code", "source": ["full_chain = (\n", "    RunnablePassthrough() | llm_vision | StrOutputParser() | rag_chain\n", ")"], "metadata": {"id": "US0kn6zFs63Z"}, "execution_count": 56, "outputs": []}, {"cell_type": "code", "source": ["full_chain"], "metadata": {"id": "qlIS7wlatPlo"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["url_1 = \"https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/252f2db6-d426-4931-80a0-8b7f8f875536/calm-slides-K7mr3W.png\""], "metadata": {"id": "lXZuKCq0tVOk"}, "execution_count": 57, "outputs": []}, {"cell_type": "code", "source": ["image = get_image(url_1, \"nike3\", \"png\")"], "metadata": {"id": "_mERPfRjtWYZ"}, "execution_count": 58, "outputs": []}, {"cell_type": "code", "source": ["plt.imshow(image)\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 435}, "id": "WBsa4tEjtYv5", "outputId": "828138f0-8433-4310-9e8c-2a52b90cd99e"}, "execution_count": 59, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["message = HumanMessage(\n", "    content=[\n", "        {\n", "            \"type\": \"text\",\n", "            \"text\": \"Provide information on given sandle image Brand and model.\",\n", "        },  # You can optionally provide text parts\n", "        {\"type\": \"image_url\", \"image_url\": image},\n", "    ]\n", ")\n"], "metadata": {"id": "3-kXDQdVtaMZ"}, "execution_count": 63, "outputs": []}, {"cell_type": "code", "source": ["result = full_chain.invoke([message])"], "metadata": {"id": "exYSFX8Vtkym"}, "execution_count": 64, "outputs": []}, {"cell_type": "code", "source": ["display(Markdown(result))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 139}, "id": "JM5HWElVtlxV", "outputId": "42d96e3b-5159-4388-8c70-8658a109b3c6"}, "execution_count": 65, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Markdown object>"], "text/markdown": "**Nike Offcourt Slide Sandal**\n\n* Soft, durable foam upper\n* Contoured footbed for arch support and cushioning\n* Rubber outsole for traction and durability\n* Available in a variety of colors\n* **Store Location:** Bhaktapur, Nepal"}, "metadata": {}}]}, {"cell_type": "code", "source": [], "metadata": {"id": "4VdTbmHXtwuB"}, "execution_count": null, "outputs": []}]}