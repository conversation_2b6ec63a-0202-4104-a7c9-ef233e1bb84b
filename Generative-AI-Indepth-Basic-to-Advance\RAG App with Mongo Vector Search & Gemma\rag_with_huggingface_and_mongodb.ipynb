{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU", "widgets": {"application/vnd.jupyter.widget-state+json": {"3f309906c3df4839adda52a49f4fe21b": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_41a6d5e69aa348a7983d25177d2214a7", "IPY_MODEL_998cbe356eb347648285856b2f7352ab", "IPY_MODEL_a46b2eee5ec843febb1c21b3e9c3fd7b"], "layout": "IPY_MODEL_de0c2802cf5141cf8668c4f21204393b"}}, "41a6d5e69aa348a7983d25177d2214a7": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ba2694c02ac5440ca03832e3a225c4f4", "placeholder": "​", "style": "IPY_MODEL_35cdcadceab24b2c8dccd6e76b3cc5a6", "value": "Downloading readme: 100%"}}, "998cbe356eb347648285856b2f7352ab": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6201d6e452094d38aa8d00137c4c9669", "max": 6176, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5737bec22aa044ac84a25d2f4e9ca449", "value": 6176}}, "a46b2eee5ec843febb1c21b3e9c3fd7b": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_58355a851a9744c98141c6fe11658914", "placeholder": "​", "style": "IPY_MODEL_72940d04f0ce4dd89ceadfb860d12f0e", "value": " 6.18k/6.18k [00:00&lt;00:00, 371kB/s]"}}, "de0c2802cf5141cf8668c4f21204393b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ba2694c02ac5440ca03832e3a225c4f4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "35cdcadceab24b2c8dccd6e76b3cc5a6": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6201d6e452094d38aa8d00137c4c9669": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5737bec22aa044ac84a25d2f4e9ca449": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "58355a851a9744c98141c6fe11658914": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "72940d04f0ce4dd89ceadfb860d12f0e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5d7b37b44c28421995adf61a7d5108f4": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_45496f36318d451bac01fa3e75b5c03d", "IPY_MODEL_934c2f44ca00478d8d5c1c9a600f69e9", "IPY_MODEL_7639123873a04981bce3eebcedb05bc2"], "layout": "IPY_MODEL_50251331bf394aa9891ff6d86d5b40b6"}}, "45496f36318d451bac01fa3e75b5c03d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_eab40b86b6bd44babb6460e699642353", "placeholder": "​", "style": "IPY_MODEL_9adaa78a04f2467e987e03429cb078c4", "value": "Downloading data: 100%"}}, "934c2f44ca00478d8d5c1c9a600f69e9": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d75ca9da26ce430ab50984cdff9f194a", "max": 42271667, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a349772820384569bf7813f5438fffad", "value": 42271667}}, "7639123873a04981bce3eebcedb05bc2": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_23bb33cf23ce4e18907b3ed502888a17", "placeholder": "​", "style": "IPY_MODEL_ef852d5daab24444aeb111a47f458e26", "value": " 42.3M/42.3M [00:00&lt;00:00, 70.2MB/s]"}}, "50251331bf394aa9891ff6d86d5b40b6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "eab40b86b6bd44babb6460e699642353": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9adaa78a04f2467e987e03429cb078c4": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d75ca9da26ce430ab50984cdff9f194a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a349772820384569bf7813f5438fffad": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "23bb33cf23ce4e18907b3ed502888a17": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ef852d5daab24444aeb111a47f458e26": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ea1c95a13aca491292e89b5a131e1588": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_486aed18a359459399cc033abc2174d2", "IPY_MODEL_4a4b2569d3354662872f4bd40af9276e", "IPY_MODEL_10c2bbf999554c6bbd5d53d6c6d4d834"], "layout": "IPY_MODEL_7f9e54f173c349f79541e6e0c4fb1764"}}, "486aed18a359459399cc033abc2174d2": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6e56ba4c8c2647d49f2c2f17ae85c675", "placeholder": "​", "style": "IPY_MODEL_19c192e61e614935ace983a7a7a7604e", "value": "Generating train split: 100%"}}, "4a4b2569d3354662872f4bd40af9276e": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ceeee6ba7bd945a6bf21f7d387cf1506", "max": 1500, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9aa6542ddea540a2b8d88c1097948efc", "value": 1500}}, "10c2bbf999554c6bbd5d53d6c6d4d834": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d0ec19cd581942e49f7df50434a2aa1e", "placeholder": "​", "style": "IPY_MODEL_2eb8224adfb44f8ab9ecb973b42ce0f2", "value": " 1500/1500 [00:00&lt;00:00, 2159.26 examples/s]"}}, "7f9e54f173c349f79541e6e0c4fb1764": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6e56ba4c8c2647d49f2c2f17ae85c675": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "19c192e61e614935ace983a7a7a7604e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ceeee6ba7bd945a6bf21f7d387cf1506": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9aa6542ddea540a2b8d88c1097948efc": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d0ec19cd581942e49f7df50434a2aa1e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2eb8224adfb44f8ab9ecb973b42ce0f2": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e0855a93f7b6468e8bff6162436bef3f": {"model_module": "@jupyter-widgets/controls", "model_name": "VBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "VBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "VBoxView", "box_style": "", "children": ["IPY_MODEL_8d8a779bf1ee4fcd83597d31235bc0b3", "IPY_MODEL_58c864f40c204de8972a829f1729581b", "IPY_MODEL_7a24372fd1e24730916bd9eeb6ed7f9b", "IPY_MODEL_75860f7b7f494dac9867e4aedcaca9d6"], "layout": "IPY_MODEL_016d5c2235a9477e8d873334d09a7466"}}, "444f505d10484eee82ed6f5d514a71ad": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ee1603395c9c48ca802f174e3bb5124e", "placeholder": "​", "style": "IPY_MODEL_f8faaf294ba14b74bd79bca279bf2cdc", "value": "<center> <img\nsrc=https://huggingface.co/front/assets/huggingface_logo-noborder.svg\nalt='Hugging Face'> <br> Copy a token from <a\nhref=\"https://huggingface.co/settings/tokens\" target=\"_blank\">your Hugging Face\ntokens page</a> and paste it below. <br> Immediately click login after copying\nyour token or it might be stored in plain text in this notebook file. </center>"}}, "e240ea85aff04edd809836b43fa9d505": {"model_module": "@jupyter-widgets/controls", "model_name": "PasswordModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "PasswordModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "PasswordView", "continuous_update": true, "description": "Token:", "description_tooltip": null, "disabled": false, "layout": "IPY_MODEL_3a615a0bf70a42b5b707d59cb2ebf814", "placeholder": "​", "style": "IPY_MODEL_3f3ad2143dc449bdb7ecb2f25f612be9", "value": ""}}, "a34630c975fe4f6c9fdf9edc34c2e33b": {"model_module": "@jupyter-widgets/controls", "model_name": "CheckboxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "CheckboxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "CheckboxView", "description": "Add token as git credential?", "description_tooltip": null, "disabled": false, "indent": true, "layout": "IPY_MODEL_db398f3bf987456cb4aac8af36799184", "style": "IPY_MODEL_1a691494160e412982fc18e45fb03b7e", "value": true}}, "80ec675222844c2581c94f6bfbf74274": {"model_module": "@jupyter-widgets/controls", "model_name": "ButtonModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ButtonModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ButtonView", "button_style": "", "description": "<PERSON><PERSON>", "disabled": false, "icon": "", "layout": "IPY_MODEL_4bc717574a9e45aca41967accaaa9dbc", "style": "IPY_MODEL_0a30b4c4c66f4e8a8925340167ac6663", "tooltip": ""}}, "a5b65e6845b94fd8adeb074084ffe7a8": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d53443b290014ee59053d15fb92f5445", "placeholder": "​", "style": "IPY_MODEL_24dea166a3e24ef89e954dd2408162bf", "value": "\n<b>Pro Tip:</b> If you don't already have one, you can create a dedicated\n'notebooks' token with 'write' access, that you can then easily reuse for all\nnotebooks. </center>"}}, "016d5c2235a9477e8d873334d09a7466": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": "center", "align_self": null, "border": null, "bottom": null, "display": "flex", "flex": null, "flex_flow": "column", "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "50%"}}, "ee1603395c9c48ca802f174e3bb5124e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f8faaf294ba14b74bd79bca279bf2cdc": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3a615a0bf70a42b5b707d59cb2ebf814": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3f3ad2143dc449bdb7ecb2f25f612be9": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "db398f3bf987456cb4aac8af36799184": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1a691494160e412982fc18e45fb03b7e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4bc717574a9e45aca41967accaaa9dbc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0a30b4c4c66f4e8a8925340167ac6663": {"model_module": "@jupyter-widgets/controls", "model_name": "ButtonStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ButtonStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "button_color": null, "font_weight": ""}}, "d53443b290014ee59053d15fb92f5445": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "24dea166a3e24ef89e954dd2408162bf": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "26605b34bce049bfb2859122ecb0ad41": {"model_module": "@jupyter-widgets/controls", "model_name": "LabelModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "LabelModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "LabelView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a9fa44da58bf4d949f4517dace95ad7d", "placeholder": "​", "style": "IPY_MODEL_2bd1e78816ee4c80877b67d786f430ae", "value": "Connecting..."}}, "a9fa44da58bf4d949f4517dace95ad7d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2bd1e78816ee4c80877b67d786f430ae": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8d8a779bf1ee4fcd83597d31235bc0b3": {"model_module": "@jupyter-widgets/controls", "model_name": "LabelModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "LabelModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "LabelView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8ac102ec74754fc2a9c4f8fc771335a6", "placeholder": "​", "style": "IPY_MODEL_4b5335731f124774a93704b615c27a8b", "value": "Token is valid (permission: read)."}}, "58c864f40c204de8972a829f1729581b": {"model_module": "@jupyter-widgets/controls", "model_name": "LabelModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "LabelModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "LabelView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f59ac8c7be464d00a96775ad3145bbc9", "placeholder": "​", "style": "IPY_MODEL_d014da1e7eb4421997f674cabf49debb", "value": "Your token has been saved in your configured git credential helpers (store)."}}, "7a24372fd1e24730916bd9eeb6ed7f9b": {"model_module": "@jupyter-widgets/controls", "model_name": "LabelModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "LabelModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "LabelView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8320fb213c1045b6922968638a8c958a", "placeholder": "​", "style": "IPY_MODEL_ff64e6a1ea2d4fa7801e867b5572b205", "value": "Your token has been saved to /root/.cache/huggingface/token"}}, "75860f7b7f494dac9867e4aedcaca9d6": {"model_module": "@jupyter-widgets/controls", "model_name": "LabelModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "LabelModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "LabelView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_59595d60c3ae4aafbd4803535233d857", "placeholder": "​", "style": "IPY_MODEL_5d05a0a67e224406b83ec1d51cc64b48", "value": "Login successful"}}, "8ac102ec74754fc2a9c4f8fc771335a6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4b5335731f124774a93704b615c27a8b": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f59ac8c7be464d00a96775ad3145bbc9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d014da1e7eb4421997f674cabf49debb": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8320fb213c1045b6922968638a8c958a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ff64e6a1ea2d4fa7801e867b5572b205": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "59595d60c3ae4aafbd4803535233d857": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5d05a0a67e224406b83ec1d51cc64b48": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6eb1d3d3c8bf4d56aef808c6b3bd89f4": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4d67ea2973114972b98485402f5d8d6f", "IPY_MODEL_9790f948c9b449cbb0036eb94d2e41fa", "IPY_MODEL_3757925570654ae896bac66ba60b94b6"], "layout": "IPY_MODEL_51182936959e4e679a462de9d0d3ef1b"}}, "4d67ea2973114972b98485402f5d8d6f": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b6b89009090d43f2836ffd4077b376c2", "placeholder": "​", "style": "IPY_MODEL_4a555f28bedd421f85dcdb7d6279d63f", "value": "config.json: 100%"}}, "9790f948c9b449cbb0036eb94d2e41fa": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c88acc50c0cc48b1b2fc376985d8478c", "max": 627, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_61a6055a422b4a8ca005ea0a2fd82dbf", "value": 627}}, "3757925570654ae896bac66ba60b94b6": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_47c1f461a56d4cf6af1d7c881c06cab1", "placeholder": "​", "style": "IPY_MODEL_05332eff981c4ad7b4a02436c80be3b8", "value": " 627/627 [00:00&lt;00:00, 44.4kB/s]"}}, "51182936959e4e679a462de9d0d3ef1b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b6b89009090d43f2836ffd4077b376c2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4a555f28bedd421f85dcdb7d6279d63f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c88acc50c0cc48b1b2fc376985d8478c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "61a6055a422b4a8ca005ea0a2fd82dbf": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "47c1f461a56d4cf6af1d7c881c06cab1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "05332eff981c4ad7b4a02436c80be3b8": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "20ff1e88a1f54123bb3b33b08ea9a1c8": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_10266043f9114d28bf848eac2005ccf4", "IPY_MODEL_e78f13c26b60442186db553226c4d5ec", "IPY_MODEL_15ff1ac2e12841b3804a6aa2ad96aab4"], "layout": "IPY_MODEL_76ca9667b6804f52aaefd2d11a8e059d"}}, "10266043f9114d28bf848eac2005ccf4": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_31994a6f7a7e4a8d940ac51dfaa6ac45", "placeholder": "​", "style": "IPY_MODEL_06b057d181fe4a349af7f4b92f5ce0dd", "value": "model.safetensors.index.json: 100%"}}, "e78f13c26b60442186db553226c4d5ec": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_26be190591434d04a7bec1bc1f669686", "max": 13489, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_95cb93b87a6c434091f59fa6de57d8e7", "value": 13489}}, "15ff1ac2e12841b3804a6aa2ad96aab4": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fb9928d287f84853b07e3643166b6442", "placeholder": "​", "style": "IPY_MODEL_5c36f59e886e4b919dd7e7af973d1eb8", "value": " 13.5k/13.5k [00:00&lt;00:00, 753kB/s]"}}, "76ca9667b6804f52aaefd2d11a8e059d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "31994a6f7a7e4a8d940ac51dfaa6ac45": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "06b057d181fe4a349af7f4b92f5ce0dd": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "26be190591434d04a7bec1bc1f669686": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "95cb93b87a6c434091f59fa6de57d8e7": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "fb9928d287f84853b07e3643166b6442": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5c36f59e886e4b919dd7e7af973d1eb8": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8e8dcf66c9c44bbbbb4997d4710e8287": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_64773f9cc5df4855ae14688300fe3aa5", "IPY_MODEL_d12532307781486ca7a03ebd01cd241a", "IPY_MODEL_a153892754a1482291103a58c2ddd520"], "layout": "IPY_MODEL_fa1da6680d3e48b2a10c0cc2eb4cdb3d"}}, "64773f9cc5df4855ae14688300fe3aa5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3ce876dc96aa42008a5514a247b503e5", "placeholder": "​", "style": "IPY_MODEL_84be888f3d1b4cb8a11e9ce3330b2e2b", "value": "Downloading shards: 100%"}}, "d12532307781486ca7a03ebd01cd241a": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3e811c162f784869ae11b4470b052ff1", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b4e3edc3a31e42b7ac9c45905dc30d2b", "value": 2}}, "a153892754a1482291103a58c2ddd520": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_437086a01d864a07a6bb5017987a0047", "placeholder": "​", "style": "IPY_MODEL_3c48bcee76224da380251c413e19225f", "value": " 2/2 [01:06&lt;00:00, 27.55s/it]"}}, "fa1da6680d3e48b2a10c0cc2eb4cdb3d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3ce876dc96aa42008a5514a247b503e5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "84be888f3d1b4cb8a11e9ce3330b2e2b": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3e811c162f784869ae11b4470b052ff1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b4e3edc3a31e42b7ac9c45905dc30d2b": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "437086a01d864a07a6bb5017987a0047": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3c48bcee76224da380251c413e19225f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fef483e4d45241d39d9938194e789905": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0605e8e7d28f404da608da824675415a", "IPY_MODEL_f97adde44a95445ebd39e8bfeb171ff4", "IPY_MODEL_76683333237f44a8add1f6a9a8196128"], "layout": "IPY_MODEL_a13ef5b831404c54b5f061907b00fdbc"}}, "0605e8e7d28f404da608da824675415a": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5e080a4acd6e4ea3a5a45ae4cc459012", "placeholder": "​", "style": "IPY_MODEL_0bca73a9861d44e394bc8993da52989d", "value": "model-00001-of-00002.safetensors: 100%"}}, "f97adde44a95445ebd39e8bfeb171ff4": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ce25d50ec03f4c179c5f9020d0abcc87", "max": 4945242264, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_87ad808e10414608af47a44cd62c84d1", "value": 4945242264}}, "76683333237f44a8add1f6a9a8196128": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6ae5acfd6b9e4b81a0359d0dd22466de", "placeholder": "​", "style": "IPY_MODEL_2d61f7f1689d43628229b454efa34bf7", "value": " 4.95G/4.95G [01:04&lt;00:00, 82.4MB/s]"}}, "a13ef5b831404c54b5f061907b00fdbc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5e080a4acd6e4ea3a5a45ae4cc459012": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0bca73a9861d44e394bc8993da52989d": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ce25d50ec03f4c179c5f9020d0abcc87": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "87ad808e10414608af47a44cd62c84d1": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "6ae5acfd6b9e4b81a0359d0dd22466de": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2d61f7f1689d43628229b454efa34bf7": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d88becf303a748369a5b314614443116": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_3ef221c8c6924055aa8ef7e4905db17d", "IPY_MODEL_4571a28e55e44fd494a401300b0ddab8", "IPY_MODEL_12e44a765871464b833908837d718083"], "layout": "IPY_MODEL_8ceb43ad70944eb698aaa5fb99ff1110"}}, "3ef221c8c6924055aa8ef7e4905db17d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ccd6fb4130734b428f14f97862d7803a", "placeholder": "​", "style": "IPY_MODEL_85fe3cd737c442df9712fad106a9cbbe", "value": "model-00002-of-00002.safetensors: 100%"}}, "4571a28e55e44fd494a401300b0ddab8": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_56ccff9ccdb7450881d75a8bea8f22b3", "max": 67121608, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_65f34680ba8d4e03b0abde0cbc847b56", "value": 67121608}}, "12e44a765871464b833908837d718083": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_abf6d58d80214433a4fe2de42a557ec8", "placeholder": "​", "style": "IPY_MODEL_883eb8629e8b45cf9237c5ccc56a2d2b", "value": " 67.1M/67.1M [00:00&lt;00:00, 77.3MB/s]"}}, "8ceb43ad70944eb698aaa5fb99ff1110": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ccd6fb4130734b428f14f97862d7803a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "85fe3cd737c442df9712fad106a9cbbe": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "56ccff9ccdb7450881d75a8bea8f22b3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "65f34680ba8d4e03b0abde0cbc847b56": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "abf6d58d80214433a4fe2de42a557ec8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "883eb8629e8b45cf9237c5ccc56a2d2b": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b433221312b442e195b449bba090b1fa": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b5e5b04111d64a4688ed7d41d8aead68", "IPY_MODEL_f081afd78e2841b0a4ad33f1b0cf9e03", "IPY_MODEL_9eebaad76ccd410eb06428a44e1b5f06"], "layout": "IPY_MODEL_c52d07ee754d4c43b8936feffcba32a1"}}, "b5e5b04111d64a4688ed7d41d8aead68": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ce96d0ff79d54332b5042400e0db6964", "placeholder": "​", "style": "IPY_MODEL_1e49eb479e04475a91411cb24a709717", "value": "Loading checkpoint shards: 100%"}}, "f081afd78e2841b0a4ad33f1b0cf9e03": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1869e671ca7848b1a4fb2649d85850c0", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_458eed408e79435e8c607689e50723fc", "value": 2}}, "9eebaad76ccd410eb06428a44e1b5f06": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c2ae724cfccf4bacb1d96cec613b7f7d", "placeholder": "​", "style": "IPY_MODEL_7fda043ec959475d93f515416bd5655f", "value": " 2/2 [00:28&lt;00:00, 11.97s/it]"}}, "c52d07ee754d4c43b8936feffcba32a1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ce96d0ff79d54332b5042400e0db6964": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1e49eb479e04475a91411cb24a709717": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1869e671ca7848b1a4fb2649d85850c0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "458eed408e79435e8c607689e50723fc": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c2ae724cfccf4bacb1d96cec613b7f7d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7fda043ec959475d93f515416bd5655f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9d747b7ad7034e80a8b04ce2c0057f64": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_076c9b718192470caaab601e6e7680eb", "IPY_MODEL_7245344850be44fdb324b71c66f6ebb5", "IPY_MODEL_4b10b4b6f7154952ba758630bbc1d77a"], "layout": "IPY_MODEL_78fafeca67ca413eb576882d54dc9836"}}, "076c9b718192470caaab601e6e7680eb": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8708554f42ed441c94f72427ffd44f77", "placeholder": "​", "style": "IPY_MODEL_cbc5116f87d141359c800ac8fbae0bed", "value": "generation_config.json: 100%"}}, "7245344850be44fdb324b71c66f6ebb5": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a40680ddcdda442d8a14247b3699d7c8", "max": 137, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5ea7d095ef744c4a9c5b0139345ea3c3", "value": 137}}, "4b10b4b6f7154952ba758630bbc1d77a": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1f7ab5f7f853469885e7f9ae2088220b", "placeholder": "​", "style": "IPY_MODEL_e620b034ee35425d970148c5e8f64d89", "value": " 137/137 [00:00&lt;00:00, 9.60kB/s]"}}, "78fafeca67ca413eb576882d54dc9836": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8708554f42ed441c94f72427ffd44f77": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cbc5116f87d141359c800ac8fbae0bed": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a40680ddcdda442d8a14247b3699d7c8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5ea7d095ef744c4a9c5b0139345ea3c3": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "1f7ab5f7f853469885e7f9ae2088220b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e620b034ee35425d970148c5e8f64d89": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "L1-5cYCKA4XS"}, "outputs": [], "source": ["!pip install datasets pandas pymongo sentence_transformers"]}, {"cell_type": "code", "source": ["!pip install -U transformers accelerate"], "metadata": {"id": "M6NY-e6rBSc-"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from datasets import load_dataset"], "metadata": {"id": "a4Jz416BBa24"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["import pandas as pd"], "metadata": {"id": "GfCrKhm4Bo6A"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["#dataset=load_dataset(\"AIatMongoDB/embedded_movies\")\n", "dataset=load_dataset(\"MongoDB/embedded_movies\")\n", "#MongoDB/embedded_movies"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 241, "referenced_widgets": ["3f309906c3df4839adda52a49f4fe21b", "41a6d5e69aa348a7983d25177d2214a7", "998cbe356eb347648285856b2f7352ab", "a46b2eee5ec843febb1c21b3e9c3fd7b", "de0c2802cf5141cf8668c4f21204393b", "ba2694c02ac5440ca03832e3a225c4f4", "35cdcadceab24b2c8dccd6e76b3cc5a6", "6201d6e452094d38aa8d00137c4c9669", "5737bec22aa044ac84a25d2f4e9ca449", "58355a851a9744c98141c6fe11658914", "72940d04f0ce4dd89ceadfb860d12f0e", "5d7b37b44c28421995adf61a7d5108f4", "45496f36318d451bac01fa3e75b5c03d", "934c2f44ca00478d8d5c1c9a600f69e9", "7639123873a04981bce3eebcedb05bc2", "50251331bf394aa9891ff6d86d5b40b6", "eab40b86b6bd44babb6460e699642353", "9adaa78a04f2467e987e03429cb078c4", "d75ca9da26ce430ab50984cdff9f194a", "a349772820384569bf7813f5438fffad", "23bb33cf23ce4e18907b3ed502888a17", "ef852d5daab24444aeb111a47f458e26", "ea1c95a13aca491292e89b5a131e1588", "486aed18a359459399cc033abc2174d2", "4a4b2569d3354662872f4bd40af9276e", "10c2bbf999554c6bbd5d53d6c6d4d834", "7f9e54f173c349f79541e6e0c4fb1764", "6e56ba4c8c2647d49f2c2f17ae85c675", "19c192e61e614935ace983a7a7a7604e", "ceeee6ba7bd945a6bf21f7d387cf1506", "9aa6542ddea540a2b8d88c1097948efc", "d0ec19cd581942e49f7df50434a2aa1e", "2eb8224adfb44f8ab9ecb973b42ce0f2"]}, "id": "E-nODsvZBrdF", "outputId": "8c87f156-7055-4df5-e598-c4bfa754a2b9"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/huggingface_hub/utils/_token.py:89: UserWarning: \n", "The secret `HF_TOKEN` does not exist in your Colab secrets.\n", "To authenticate with the Hugging Face Hub, create a token in your settings tab (https://huggingface.co/settings/tokens), set it as secret in your Google Colab and restart your session.\n", "You will be able to reuse this secret in all of your notebooks.\n", "Please note that authentication is recommended but still optional to access public models or datasets.\n", "  warnings.warn(\n"]}, {"output_type": "display_data", "data": {"text/plain": ["Downloading readme:   0%|          | 0.00/6.18k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "3f309906c3df4839adda52a49f4fe21b"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading data:   0%|          | 0.00/42.3M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "5d7b37b44c28421995adf61a7d5108f4"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Generating train split:   0%|          | 0/1500 [00:00<?, ? examples/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "ea1c95a13aca491292e89b5a131e1588"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["dataset"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3wk5EYTxCAZf", "outputId": "2237dded-075c-4cb9-ec50-7a4d76279534"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatasetDict({\n", "    train: Dataset({\n", "        features: ['directors', 'title', 'imdb', 'plot_embedding', 'awards', 'rated', 'countries', 'type', 'writers', 'metacritic', 'languages', 'fullplot', 'genres', 'poster', 'cast', 'runtime', 'plot', 'num_mflix_comments'],\n", "        num_rows: 1500\n", "    })\n", "})"]}, "metadata": {}, "execution_count": 6}]}, {"cell_type": "code", "source": ["dataset_df=pd.DataFrame(dataset[\"train\"])"], "metadata": {"id": "xltqOeu6COVW"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["dataset_df.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 145}, "id": "waeeI5UTCS3H", "outputId": "011ffcf2-0c7e-4792-8fd6-b11bb2acf009"}, "execution_count": null, "outputs": [{"output_type": "error", "ename": "NameError", "evalue": "name 'dataset_df' is not defined", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-41-45045c29941a>\u001b[0m in \u001b[0;36m<cell line: 1>\u001b[0;34m()\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mdataset_df\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mhead\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 'dataset_df' is not defined"]}]}, {"cell_type": "code", "source": ["dataset_df.columns"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Zjhr9tDmDaLK", "outputId": "b7587c56-4a47-4a25-81a7-579a83d9e46d"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Index(['directors', 'title', 'imdb', 'plot_embedding', 'awards', 'rated',\n", "       'countries', 'type', 'writers', 'metacritic', 'languages', 'fullplot',\n", "       'genres', 'poster', 'cast', 'runtime', 'plot', 'num_mflix_comments'],\n", "      dtype='object')"]}, "metadata": {}, "execution_count": 9}]}, {"cell_type": "code", "source": ["dataset_df[\"plot\"][0]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 54}, "id": "LPF7YagwDdYp", "outputId": "9eb3e65b-30a0-41d4-8eae-f8d0298a40b4"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["\"Young <PERSON> is left a lot of money when her wealthy uncle dies. However, her uncle's secretary has been named as her guardian until she marries, at which time she will officially take ...\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "code", "source": ["dataset_df[\"fullplot\"][0]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 90}, "id": "zXWsEeAEDqH5", "outputId": "ed409b1e-7931-4fa7-eb15-cd9cb0522dea"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Young <PERSON> is left a lot of money when her wealthy uncle dies. However, her uncle\\'s secretary has been named as her guardian until she marries, at which time she will officially take possession of her inheritance. Meanwhile, her \"guardian\" and his confederates constantly come up with schemes to get rid of <PERSON> so that he can get his hands on the money himself.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "code", "source": ["dataset_df[\"num_mflix_comments\"][0]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "d8_96aRXErjt", "outputId": "905f2434-e51a-464a-82e8-d8ba85d58475"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0"]}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "code", "source": ["dataset_df[\"fullplot\"].isnull().sum()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kvtEJnEFD_Xz", "outputId": "ece6f77d-14c7-4774-876c-21d9433eaa95"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["48"]}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "code", "source": ["dataset_df.shape"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Xxx5lbXnEHma", "outputId": "80dd89e2-b7d6-421d-affe-521741d45e44"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(1500, 18)"]}, "metadata": {}, "execution_count": 14}]}, {"cell_type": "code", "source": ["dataset_df[\"poster\"][0]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 54}, "id": "dey3wEVxETOs", "outputId": "141c3b67-c983-4c5a-f719-5da55ebf9f8b"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'https://m.media-amazon.com/images/M/MV5BMzgxODk1Mzk2Ml5BMl5BanBnXkFtZTgwMDg0NzkwMjE@._V1_SY1000_SX677_AL_.jpg'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 15}]}, {"cell_type": "code", "source": ["dataset_df.isnull().sum()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "IjLSpkw9ChG3", "outputId": "e975d9d5-8e7c-47d3-e127-411aed90df80"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["directors              13\n", "title                   0\n", "imdb                    0\n", "plot_embedding         28\n", "awards                  0\n", "rated                 308\n", "countries               0\n", "type                    0\n", "writers                13\n", "metacritic            928\n", "languages               1\n", "fullplot               48\n", "genres                  0\n", "poster                 89\n", "cast                    1\n", "runtime                15\n", "plot                   27\n", "num_mflix_comments      0\n", "dtype: int64"]}, "metadata": {}, "execution_count": 16}]}, {"cell_type": "code", "source": ["dataset_df=dataset_df.dropna(subset=[\"fullplot\"])"], "metadata": {"id": "KGeAho8GDCSK"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["dataset_df[\"fullplot\"].isnull().sum()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5TFDYus1FQTn", "outputId": "bdcbd6a3-f946-40b0-904b-b18b8a3c3565"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0"]}, "metadata": {}, "execution_count": 18}]}, {"cell_type": "code", "source": ["dataset_df = dataset_df.drop(columns=[\"plot_embedding\"])"], "metadata": {"id": "9ANR6TtxFVZe"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["dataset_df.head(2)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 340}, "id": "abCIIPGXFhU_", "outputId": "0d303adb-e8ea-47d7-b23d-aed3e70cecde"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                              directors                  title  \\\n", "0  [<PERSON>, <PERSON>]  The Perils of Pauline   \n", "1       [<PERSON>, <PERSON>]     From Hand to Mouth   \n", "\n", "                                         imdb  \\\n", "0   {'id': 4465, 'rating': 7.6, 'votes': 744}   \n", "1  {'id': 10146, 'rating': 7.0, 'votes': 639}   \n", "\n", "                                              awards rated countries   type  \\\n", "0    {'nominations': 0, 'text': '1 win.', 'wins': 1}  None     [USA]  movie   \n", "1  {'nominations': 1, 'text': '1 nomination.', 'w...  TV-G     [USA]  movie   \n", "\n", "                                             writers  metacritic  languages  \\\n", "0  [<PERSON> (screenplay), <PERSON>.<PERSON><PERSON>  [English]   \n", "1                             [<PERSON><PERSON><PERSON><PERSON> (titles)]         NaN  [English]   \n", "\n", "                                            fullplot                   genres  \\\n", "0  Young <PERSON> is left a lot of money when her ...                 [Action]   \n", "1  As a penniless man worries about how he will m...  [Comedy, Short, Action]   \n", "\n", "                                              poster  \\\n", "0  https://m.media-amazon.com/images/M/MV5BMzgxOD...   \n", "1  https://m.media-amazon.com/images/M/MV5BNzE1OW...   \n", "\n", "                                                cast  runtime  \\\n", "0  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...    199.0   \n", "1  [<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON><PERSON> <PERSON>, ...     22.0   \n", "\n", "                                                plot  num_mflix_comments  \n", "0  Young <PERSON> is left a lot of money when her ...                   0  \n", "1  A penniless young man tries to save an heiress...                   0  "], "text/html": ["\n", "  <div id=\"df-03e309de-0b08-4826-b7bd-f7fb71c28539\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>directors</th>\n", "      <th>title</th>\n", "      <th>imdb</th>\n", "      <th>awards</th>\n", "      <th>rated</th>\n", "      <th>countries</th>\n", "      <th>type</th>\n", "      <th>writers</th>\n", "      <th>metacritic</th>\n", "      <th>languages</th>\n", "      <th>fullplot</th>\n", "      <th>genres</th>\n", "      <th>poster</th>\n", "      <th>cast</th>\n", "      <th>runtime</th>\n", "      <th>plot</th>\n", "      <th>num_mflix_comments</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>The Perils of Pauline</td>\n", "      <td>{'id': 4465, 'rating': 7.6, 'votes': 744}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>None</td>\n", "      <td>[USA]</td>\n", "      <td>movie</td>\n", "      <td>[<PERSON> (screenplay), <PERSON>...</td>\n", "      <td>NaN</td>\n", "      <td>[English]</td>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>[Action]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzgxOD...</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...</td>\n", "      <td>199.0</td>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>From Hand to Mouth</td>\n", "      <td>{'id': 10146, 'rating': 7.0, 'votes': 639}</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>TV-G</td>\n", "      <td>[USA]</td>\n", "      <td>movie</td>\n", "      <td>[<PERSON><PERSON><PERSON><PERSON> (titles)]</td>\n", "      <td>NaN</td>\n", "      <td>[English]</td>\n", "      <td>As a penniless man worries about how he will m...</td>\n", "      <td>[Comedy, Short, Action]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BNzE1OW...</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON>' <PERSON><PERSON>, ...</td>\n", "      <td>22.0</td>\n", "      <td>A penniless young man tries to save an heiress...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-03e309de-0b08-4826-b7bd-f7fb71c28539')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-03e309de-0b08-4826-b7bd-f7fb71c28539 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-03e309de-0b08-4826-b7bd-f7fb71c28539');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-81b4815c-2c82-49da-8c41-69ee48067a46\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-81b4815c-2c82-49da-8c41-69ee48067a46')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-81b4815c-2c82-49da-8c41-69ee48067a46 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "dataset_df", "summary": "{\n  \"name\": \"dataset_df\",\n  \"rows\": 1452,\n  \"fields\": [\n    {\n      \"column\": \"directors\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1391,\n        \"samples\": [\n          \"Superhero Movie\",\n          \"Hooper\",\n          \"Sivaji\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"imdb\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"awards\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rated\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 12,\n        \"samples\": [\n          \"TV-MA\",\n          \"TV-14\",\n          \"TV-G\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"countries\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"type\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"series\",\n          \"movie\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"writers\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"metacritic\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16.855402595666057,\n        \"min\": 9.0,\n        \"max\": 97.0,\n        \"num_unique_values\": 83,\n        \"samples\": [\n          50.0,\n          97.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"languages\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"fullplot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1409,\n        \"samples\": [\n          \"An undercover cop infiltrates a gang of thieves who plan to rob a jewelry store.\",\n          \"Godzilla returns in a brand-new movie that ignores all preceding movies except for the original with a brand new look and a powered up atomic ray. This time he battles a mysterious UFO that later transforms into a mysterious kaiju dubbed Orga. They meet up for the final showdown in the city of Shinjuku.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"poster\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1332,\n        \"samples\": [\n          \"https://m.media-amazon.com/images/M/MV5BMTQ2NTMxODEyNV5BMl5BanBnXkFtZTcwMDgxMjA0MQ@@._V1_SY1000_SX677_AL_.jpg\",\n          \"https://m.media-amazon.com/images/M/MV5BMTY5OTg1ODk0MV5BMl5BanBnXkFtZTcwMTEwMjU1MQ@@._V1_SY1000_SX677_AL_.jpg\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cast\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"runtime\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 42.5693352357647,\n        \"min\": 6.0,\n        \"max\": 1256.0,\n        \"num_unique_values\": 137,\n        \"samples\": [\n          60.0,\n          151.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"plot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1409,\n        \"samples\": [\n          \"An undercover cop infiltrates a gang of thieves who plan to rob a jewelry store.\",\n          \"Godzilla saves Tokyo from a flying saucer that transforms into the beast Orga.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"num_mflix_comments\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 27,\n        \"min\": 0,\n        \"max\": 158,\n        \"num_unique_values\": 40,\n        \"samples\": [\n          117,\n          134\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 20}]}, {"source": ["# @title metacritic\n", "\n", "from matplotlib import pyplot as plt\n", "dataset_df['metacritic'].plot(kind='hist', bins=20, title='metacritic')\n", "plt.gca().spines[['top', 'right',]].set_visible(False)"], "cell_type": "code", "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}], "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "kdqa7Mv8F3UI", "outputId": "afc3e3d3-c2d6-4562-b9f6-23d871f7af36"}}, {"cell_type": "code", "source": ["from sentence_transformers import SentenceTransformer\n", "embedding_model = SentenceTransformer(\"thenlper/gte-large\")"], "metadata": {"id": "5XrEBgWmFjWe"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["dataset_df[\"fullplot\"][2]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 72}, "id": "ayplIvvLGyk_", "outputId": "a24bbaca-c893-41b9-a317-83cf80025401"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'<PERSON> \"<PERSON>\" <PERSON> leaves England in disgrace and joins the infamous French Foreign Legion. He is reunited with his two brothers in North Africa, where they face greater danger from their own sadistic commander than from the rebellious Arabs.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 22}]}, {"cell_type": "code", "source": ["text=\"   sunny savi<PERSON> is  a data scientist who create prodcut of data\""], "metadata": {"id": "5YCL4funHlqB"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["text=\"   sunny savi<PERSON> is  a data scientist who create prodcut of data     \"\n"], "metadata": {"id": "zlLEC4-THzi9"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["text"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "fXxPwQGCH2LM", "outputId": "29323172-ea5a-4c6a-ac97-a8c80ef13d99"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'   <PERSON> savi<PERSON> is  a data scientist who create prodcut of data     '"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 25}]}, {"cell_type": "code", "source": ["text.strip()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "eVqlQFEXHtnK", "outputId": "5fe2fa48-8c99-4f12-9070-fd6b55c4e5f4"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'<PERSON> savi<PERSON> is  a data scientist who create prodcut of data'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 26}]}, {"cell_type": "code", "source": ["def get_embedding(text:str)->list[float]:\n", "\n", "  if not text.strip():\n", "    print(\"attempted to get embedding for empty text.\")\n", "    return []\n", "\n", "  embedding=embedding_model.encode(text)\n", "  return embedding.tolist()\n"], "metadata": {"id": "Zge4b2p_HAV0"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["dataset_df[\"embedding\"]=dataset_df[\"fullplot\"].apply(get_embedding)"], "metadata": {"id": "mtLUR8QwIJcP"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["dataset_df.head(3)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 475}, "id": "6guCtolpIWt5", "outputId": "84eb4d62-b5ed-47de-9e8c-e66b30d4279e"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                              directors                  title  \\\n", "0  [<PERSON>, <PERSON>]  The Perils of Pauline   \n", "1       [<PERSON>, <PERSON>]     From Hand to Mouth   \n", "2                      [<PERSON>]             <PERSON>   \n", "\n", "                                         imdb  \\\n", "0   {'id': 4465, 'rating': 7.6, 'votes': 744}   \n", "1  {'id': 10146, 'rating': 7.0, 'votes': 639}   \n", "2  {'id': 16634, 'rating': 6.9, 'votes': 222}   \n", "\n", "                                              awards rated countries   type  \\\n", "0    {'nominations': 0, 'text': '1 win.', 'wins': 1}  None     [USA]  movie   \n", "1  {'nominations': 1, 'text': '1 nomination.', 'w...  TV-G     [USA]  movie   \n", "2    {'nominations': 0, 'text': '1 win.', 'wins': 1}  None     [USA]  movie   \n", "\n", "                                             writers  metacritic  languages  \\\n", "0  [<PERSON> (screenplay), <PERSON>.<PERSON><PERSON>  [English]   \n", "1                             [<PERSON><PERSON><PERSON><PERSON> (titles)]         NaN  [English]   \n", "2  [<PERSON> (adaptation), <PERSON> (ad...         Na<PERSON>  [English]   \n", "\n", "                                            fullplot  \\\n", "0  Young <PERSON> is left a lot of money when her ...   \n", "1  As a penniless man worries about how he will m...   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...   \n", "\n", "                       genres  \\\n", "0                    [Action]   \n", "1     [Comedy, Short, Action]   \n", "2  [Action, Adventure, Drama]   \n", "\n", "                                              poster  \\\n", "0  https://m.media-amazon.com/images/M/MV5BMzgxOD...   \n", "1  https://m.media-amazon.com/images/M/MV5BNzE1OW...   \n", "2                                               None   \n", "\n", "                                                cast  runtime  \\\n", "0  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...    199.0   \n", "1  [<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON><PERSON> <PERSON>, ...     22.0   \n", "2  [<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>.    101.0   \n", "\n", "                                                plot  num_mflix_comments  \\\n", "0  Young <PERSON> is left a lot of money when her ...                   0   \n", "1  A penniless young man tries to save an heiress...                   0   \n", "2  <PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...                   0   \n", "\n", "                                           embedding  \n", "0  [-0.009285839274525642, -0.005062091629952192,...  \n", "1  [-0.002439370146021247, 0.023095937445759773, ...  \n", "2  [0.012204294092953205, -0.011455751955509186, ...  "], "text/html": ["\n", "  <div id=\"df-a79150b6-6480-458b-9e54-f1f814724a81\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>directors</th>\n", "      <th>title</th>\n", "      <th>imdb</th>\n", "      <th>awards</th>\n", "      <th>rated</th>\n", "      <th>countries</th>\n", "      <th>type</th>\n", "      <th>writers</th>\n", "      <th>metacritic</th>\n", "      <th>languages</th>\n", "      <th>fullplot</th>\n", "      <th>genres</th>\n", "      <th>poster</th>\n", "      <th>cast</th>\n", "      <th>runtime</th>\n", "      <th>plot</th>\n", "      <th>num_mflix_comments</th>\n", "      <th>embedding</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>The Perils of Pauline</td>\n", "      <td>{'id': 4465, 'rating': 7.6, 'votes': 744}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>None</td>\n", "      <td>[USA]</td>\n", "      <td>movie</td>\n", "      <td>[<PERSON> (screenplay), <PERSON>...</td>\n", "      <td>NaN</td>\n", "      <td>[English]</td>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>[Action]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzgxOD...</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...</td>\n", "      <td>199.0</td>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>0</td>\n", "      <td>[-0.009285839274525642, -0.005062091629952192,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>From Hand to Mouth</td>\n", "      <td>{'id': 10146, 'rating': 7.0, 'votes': 639}</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>TV-G</td>\n", "      <td>[USA]</td>\n", "      <td>movie</td>\n", "      <td>[<PERSON><PERSON><PERSON><PERSON> (titles)]</td>\n", "      <td>NaN</td>\n", "      <td>[English]</td>\n", "      <td>As a penniless man worries about how he will m...</td>\n", "      <td>[Comedy, Short, Action]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BNzE1OW...</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON>' <PERSON><PERSON>, ...</td>\n", "      <td>22.0</td>\n", "      <td>A penniless young man tries to save an heiress...</td>\n", "      <td>0</td>\n", "      <td>[-0.002439370146021247, 0.023095937445759773, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>[<PERSON>]</td>\n", "      <td><PERSON></td>\n", "      <td>{'id': 16634, 'rating': 6.9, 'votes': 222}</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>None</td>\n", "      <td>[USA]</td>\n", "      <td>movie</td>\n", "      <td>[<PERSON> (adaptation), <PERSON> (ad...</td>\n", "      <td>NaN</td>\n", "      <td>[English]</td>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>[Action, Adventure, Drama]</td>\n", "      <td>None</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, A<PERSON>..</td>\n", "      <td>101.0</td>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>0</td>\n", "      <td>[0.012204294092953205, -0.011455751955509186, ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-a79150b6-6480-458b-9e54-f1f814724a81')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-a79150b6-6480-458b-9e54-f1f814724a81 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-a79150b6-6480-458b-9e54-f1f814724a81');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-002bc5c5-d1b4-4420-bedb-00a0936bb360\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-002bc5c5-d1b4-4420-bedb-00a0936bb360')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-002bc5c5-d1b4-4420-bedb-00a0936bb360 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "dataset_df", "summary": "{\n  \"name\": \"dataset_df\",\n  \"rows\": 1452,\n  \"fields\": [\n    {\n      \"column\": \"directors\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1391,\n        \"samples\": [\n          \"Superhero Movie\",\n          \"Hooper\",\n          \"Sivaji\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"imdb\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"awards\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rated\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 12,\n        \"samples\": [\n          \"TV-MA\",\n          \"TV-14\",\n          \"TV-G\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"countries\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"type\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"series\",\n          \"movie\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"writers\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"metacritic\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16.855402595666057,\n        \"min\": 9.0,\n        \"max\": 97.0,\n        \"num_unique_values\": 83,\n        \"samples\": [\n          50.0,\n          97.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"languages\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"fullplot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1409,\n        \"samples\": [\n          \"An undercover cop infiltrates a gang of thieves who plan to rob a jewelry store.\",\n          \"Godzilla returns in a brand-new movie that ignores all preceding movies except for the original with a brand new look and a powered up atomic ray. This time he battles a mysterious UFO that later transforms into a mysterious kaiju dubbed Orga. They meet up for the final showdown in the city of Shinjuku.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"poster\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1332,\n        \"samples\": [\n          \"https://m.media-amazon.com/images/M/MV5BMTQ2NTMxODEyNV5BMl5BanBnXkFtZTcwMDgxMjA0MQ@@._V1_SY1000_SX677_AL_.jpg\",\n          \"https://m.media-amazon.com/images/M/MV5BMTY5OTg1ODk0MV5BMl5BanBnXkFtZTcwMTEwMjU1MQ@@._V1_SY1000_SX677_AL_.jpg\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cast\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"runtime\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 42.5693352357647,\n        \"min\": 6.0,\n        \"max\": 1256.0,\n        \"num_unique_values\": 137,\n        \"samples\": [\n          60.0,\n          151.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"plot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1409,\n        \"samples\": [\n          \"An undercover cop infiltrates a gang of thieves who plan to rob a jewelry store.\",\n          \"Godzilla saves Tokyo from a flying saucer that transforms into the beast Orga.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"num_mflix_comments\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 27,\n        \"min\": 0,\n        \"max\": 158,\n        \"num_unique_values\": 40,\n        \"samples\": [\n          117,\n          134\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"embedding\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 29}]}, {"cell_type": "code", "source": ["!python --version"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gBa-qzx3RNdV", "outputId": "8cc9a744-259c-40bb-d690-0a6a0f3f8b7c"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Python 3.10.12\n"]}]}, {"cell_type": "code", "source": ["import pymongo"], "metadata": {"id": "kXbZFM5RIqYU"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["#!python -m pip install \"pymongo[srv]\"\n"], "metadata": {"id": "xbEDquRoMrAx"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from pymongo.mongo_client import MongoClient"], "metadata": {"id": "lNB6bSnNRmUy"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from google.colab import userdata\n", "uri=userdata.get('MONGO_URI')"], "metadata": {"id": "GfV1Qe1YSX8f"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Create a new client and connect to the server\n", "client = MongoClient(uri)"], "metadata": {"id": "hIPRAlsRRq2v"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Send a ping to confirm a successful connection\n", "try:\n", "    client.admin.command('ping')\n", "    print(\"Pinged your deployment. You successfully connected to MongoDB!\")\n", "except Exception as e:\n", "    print(e)"], "metadata": {"id": "mInjJ-kLMvSV"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["def get_mongo_client(uri):\n", "  try:\n", "    client = MongoClient(uri)\n", "    client.admin.command('ping')\n", "    print(\"Pinged your deployment. You successfully connected to MongoDB!\")\n", "    return client\n", "  except Exception as e:\n", "    print(e)\n", "    return None"], "metadata": {"id": "pcrtipaDRtbm"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["mongo_client=get_mongo_client(uri)"], "metadata": {"id": "4LG9ETvISsHL"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["db=mongo_client[\"moviedb2\"]"], "metadata": {"id": "Vl0eY7amTHje"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["collection=db[\"moviecollection2\"]"], "metadata": {"id": "TPTjqFyZUGwc"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["collection.insert_one({\"name\":\"sunny\",\n", "                       \"designation\": \"genai engineer\",\n", "                       \"location\":\"bangaluru\",\n", "                       \"mailid\":\"<EMAIL>\"})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UYhC_ocpUMLL", "outputId": "2dbd62d2-a543-42b6-c1dc-d8c286adaae3"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["InsertOneResult(ObjectId('662c02b471d807af7238859c'), acknowledged=True)"]}, "metadata": {}, "execution_count": 40}]}, {"cell_type": "code", "source": ["collection.insert_one({\"name\":\"dipesh\",\n", "                       \"designation\": \"ops manager\",\n", "                       \"location\":\"bangaluru\"})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "b06bukanU8U1", "outputId": "41b72c83-a2b2-4ce4-8bbe-5ba7136fb1d7"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["InsertOneResult(ObjectId('662c02c271d807af7238859d'), acknowledged=True)"]}, "metadata": {}, "execution_count": 41}]}, {"cell_type": "code", "source": ["collection2=db[\"moviecollectionsecond\"]"], "metadata": {"id": "zTZA1nVCVhyk"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["collection2.insert_one({\"name\":\"krish\",\n", "                       \"designation\": \"tech lead\",\n", "                       \"location\":\"bangaluru\",\n", "                        \"phonenumber\":57454745834})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5zEeouaAVsus", "outputId": "3a190c16-0376-4f52-d374-805f1a650074"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["InsertOneResult(ObjectId('6624b2076f3d00cb646ca829'), acknowledged=True)"]}, "metadata": {}, "execution_count": 71}]}, {"cell_type": "code", "source": ["collection.delete_many({})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TPGUbL3-V2Y0", "outputId": "d3c2e655-f617-4e1d-9fcf-ef1d0614dbfa"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DeleteResult({'n': 2, 'electionId': ObjectId('7fffffff000000000000001f'), 'opTime': {'ts': Timestamp(1714160327, 1), 't': 31}, 'ok': 1.0, '$clusterTime': {'clusterTime': Timestamp(1714160327, 2), 'signature': {'hash': b'zB\\xefV\\x8f\\xe2\\xea\\x11\\x8ax\\tR\\xafg2\\x07\\xf5\\x01\\xf2R', 'keyId': 7322590180760616966}}, 'operationTime': Timestamp(1714160327, 1)}, acknowledged=True)"]}, "metadata": {}, "execution_count": 42}]}, {"cell_type": "code", "source": ["dataset_df.tail(3)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "I-vVy61IWP-5", "outputId": "74eab1fc-374f-47de-eafe-4790fcd37200"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["              directors            title  \\\n", "1497  [<PERSON><PERSON><PERSON>]           Omkara   \n", "1498      [<PERSON>]  Day of the Dead   \n", "1499       [<PERSON>]           Jumper   \n", "\n", "                                                imdb  \\\n", "1497    {'id': 488414, 'rating': 8.2, 'votes': 9800}   \n", "1498   {'id': 489018, 'rating': 4.5, 'votes': 17177}   \n", "1499  {'id': 489099, 'rating': 6.1, 'votes': 226607}   \n", "\n", "                                                 awards  rated      countries  \\\n", "1497  {'nominations': 13, 'text': '14 wins & 13 nomi...   None        [India]   \n", "1498  {'nominations': 1, 'text': '1 nomination.', 'w...      R          [USA]   \n", "1499  {'nominations': 4, 'text': '2 wins & 4 nominat...  PG-13  [USA, Canada]   \n", "\n", "       type                                            writers  metacritic  \\\n", "1497  movie  [<PERSON><PERSON><PERSON> (screenplay), <PERSON> (sc...         NaN   \n", "1498  movie  [<PERSON> (screenplay), <PERSON>...         <PERSON>   \n", "1499  movie  [<PERSON> (screenplay), <PERSON> (screen...        35.0   \n", "\n", "                                   languages  \\\n", "1497                                 [Hindi]   \n", "1498                               [English]   \n", "1499  [English, Italian, Japanese, Mandarin]   \n", "\n", "                                               fullplot  \\\n", "1497  Advocate <PERSON><PERSON><PERSON><PERSON> has arranged the mar...   \n", "1498  In Leadville, Colorado, Captain <PERSON> and his...   \n", "1499  <PERSON> is a high school student in Ann Arb...   \n", "\n", "                           genres  \\\n", "1497       [Action, Crime, Drama]   \n", "1498             [Action, Horror]   \n", "1499  [Action, Adventure, Sci-Fi]   \n", "\n", "                                                 poster  \\\n", "1497  https://m.media-amazon.com/images/M/MV5BY2NmNj...   \n", "1498  https://m.media-amazon.com/images/M/MV5BNzg1Mj...   \n", "1499  https://m.media-amazon.com/images/M/MV5BMjEwOT...   \n", "\n", "                                                   cast  runtime  \\\n", "1497  [<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Ko...    155.0   \n", "1498  [<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>...     86.0   \n", "1499  [<PERSON>, <PERSON>, <PERSON>...     88.0   \n", "\n", "                                                   plot  num_mflix_comments  \\\n", "1497  <PERSON>'s masterpiece \"Othello\" set in mod...                   1   \n", "1498  When a small Colorado town is overrun by the f...                   1   \n", "1499  A teenager with teleportation abilities sudden...                   0   \n", "\n", "                                              embedding  \n", "1497  [0.001624124008230865, -0.012888927012681961, ...  \n", "1498  [0.0051591419614851475, -0.007672053761780262,...  \n", "1499  [-0.004183384124189615, -0.005530036520212889,...  "], "text/html": ["\n", "  <div id=\"df-b2f88c94-381d-4df0-a3c3-e7671887cf71\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>directors</th>\n", "      <th>title</th>\n", "      <th>imdb</th>\n", "      <th>awards</th>\n", "      <th>rated</th>\n", "      <th>countries</th>\n", "      <th>type</th>\n", "      <th>writers</th>\n", "      <th>metacritic</th>\n", "      <th>languages</th>\n", "      <th>fullplot</th>\n", "      <th>genres</th>\n", "      <th>poster</th>\n", "      <th>cast</th>\n", "      <th>runtime</th>\n", "      <th>plot</th>\n", "      <th>num_mflix_comments</th>\n", "      <th>embedding</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1497</th>\n", "      <td>[<PERSON><PERSON><PERSON>]</td>\n", "      <td>Omkara</td>\n", "      <td>{'id': 488414, 'rating': 8.2, 'votes': 9800}</td>\n", "      <td>{'nominations': 13, 'text': '14 wins &amp; 13 nomi...</td>\n", "      <td>None</td>\n", "      <td>[India]</td>\n", "      <td>movie</td>\n", "      <td>[<PERSON><PERSON><PERSON> (screenplay), <PERSON> (sc...</td>\n", "      <td>NaN</td>\n", "      <td>[Hindi]</td>\n", "      <td>Advocate <PERSON><PERSON><PERSON><PERSON> has arranged the mar...</td>\n", "      <td>[Action, Crime, Drama]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BY2NmNj...</td>\n", "      <td>[<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>..</td>\n", "      <td>155.0</td>\n", "      <td>Shakespeare's masterpiece \"Othello\" set in mod...</td>\n", "      <td>1</td>\n", "      <td>[0.001624124008230865, -0.012888927012681961, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1498</th>\n", "      <td>[<PERSON>]</td>\n", "      <td>Day of the Dead</td>\n", "      <td>{'id': 489018, 'rating': 4.5, 'votes': 17177}</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>R</td>\n", "      <td>[USA]</td>\n", "      <td>movie</td>\n", "      <td>[<PERSON> (screenplay), <PERSON>...</td>\n", "      <td>NaN</td>\n", "      <td>[English]</td>\n", "      <td>In Leadville, Colorado, Captain <PERSON> and his...</td>\n", "      <td>[Action, Horror]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BNzg1Mj...</td>\n", "      <td>[<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>...</td>\n", "      <td>86.0</td>\n", "      <td>When a small Colorado town is overrun by the f...</td>\n", "      <td>1</td>\n", "      <td>[0.0051591419614851475, -0.007672053761780262,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1499</th>\n", "      <td>[<PERSON>]</td>\n", "      <td>Jumper</td>\n", "      <td>{'id': 489099, 'rating': 6.1, 'votes': 226607}</td>\n", "      <td>{'nominations': 4, 'text': '2 wins &amp; 4 nominat...</td>\n", "      <td>PG-13</td>\n", "      <td>[USA, Canada]</td>\n", "      <td>movie</td>\n", "      <td>[<PERSON> (screenplay), <PERSON> (screen...</td>\n", "      <td>35.0</td>\n", "      <td>[English, Italian, Japanese, Mandarin]</td>\n", "      <td><PERSON> is a high school student in Ann Arb...</td>\n", "      <td>[Action, Adventure, Sci-Fi]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMjEwOT...</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>...</td>\n", "      <td>88.0</td>\n", "      <td>A teenager with teleportation abilities sudden...</td>\n", "      <td>0</td>\n", "      <td>[-0.004183384124189615, -0.005530036520212889,...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-b2f88c94-381d-4df0-a3c3-e7671887cf71')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-b2f88c94-381d-4df0-a3c3-e7671887cf71 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-b2f88c94-381d-4df0-a3c3-e7671887cf71');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-06e275b6-3d7b-42fc-90c0-180cc804db9a\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-06e275b6-3d7b-42fc-90c0-180cc804db9a')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-06e275b6-3d7b-42fc-90c0-180cc804db9a button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"dataset_df\",\n  \"rows\": 3,\n  \"fields\": [\n    {\n      \"column\": \"directors\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 3,\n        \"samples\": [\n          \"Omkara\",\n          \"Day of the Dead\",\n          \"Jumper\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"imdb\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"awards\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rated\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"PG-13\",\n          \"R\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"countries\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"type\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1,\n        \"samples\": [\n          \"movie\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"writers\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"metacritic\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": null,\n        \"min\": 35.0,\n        \"max\": 35.0,\n        \"num_unique_values\": 1,\n        \"samples\": [\n          35.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"languages\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"fullplot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 3,\n        \"samples\": [\n          \"Advocate Raghunath Mishra has arranged the marriage of his daughter, Dolly, with Rajan, knowing fully well that Dolly loves Omkara Shukla. Before the marriage could take place, the groom's party is attacked, they flee, and Dolly is reportedly abducted. Raghunath is able to trace Dolly to Omkara, a criminal and hit-man, a verbal confrontation ensues until Politician Bhaisaab telephonically intervenes, and a crestfallen Raghunath faces the reality that Dolly was not abducted but is here with Omkara by her own free will. He warns Omkara, and departs. Shortly thereafter, Bhaisaab is shot at and wounded, announces that Omkara should stand in the next election, and as a result, Omkara appoints one of his lieutenants', Keshav Upadhyay in his place as the 'Bahubali\\\". Omkara realizes that he may have blundered in having Keshav succeed him, as Keshav is unable to control his temper when under the influence of alcohol, which puts him in the bad books of Omkara. Then Omkara suspects that Keshav is having an affair with Dolly, as both had been fellow-collegians. With Omkara's marriage with Dolly getting close, he asks his other lieutenant, Ishwar Tyagi, to obtain proof of Keshav's affair with Dolly or face the consequences, as Omkara remembers Raghunath's warning that when a daughter is not true to her father, she will never be true to anyone. Watch what happens when Ishwar produces proof shortly after the marriage, and the impact this has on Dolly, Omkara, Keshav, Ishwar, and Ishwar's wife, Indu.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"poster\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 3,\n        \"samples\": [\n          \"https://m.media-amazon.com/images/M/MV5BY2NmNjlhYzQtMWU5MS00YTVmLWIyOWEtOGFmY2ZjMjY2ZGRkXkEyXkFqcGdeQXVyMTMxMTY0OTQ@._V1_SY1000_SX677_AL_.jpg\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cast\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"runtime\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 39.272551907577046,\n        \"min\": 86.0,\n        \"max\": 155.0,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          155.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"plot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 3,\n        \"samples\": [\n          \"Shakespeare's masterpiece \\\"Othello\\\" set in modern India. A politically-minded enforcer's misguided trust in his lieutenant leads him to suspect his wife of infidelity.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"num_mflix_comments\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 0,\n        \"max\": 1,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"embedding\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 43}]}, {"cell_type": "code", "source": ["document=dataset_df.to_dict(\"records\")"], "metadata": {"id": "UloWvipUWauA"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["collection.insert_many(document)\n", "\n", "print(\"data ingestion in mongodb is completed\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "JCkSCIGXWg1_", "outputId": "cdaac8fc-6239-4dc6-dded-6023a2570bd8"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["data ingestion in mongodb is completed\n"]}]}, {"cell_type": "markdown", "source": ["# Data Retrival"], "metadata": {"id": "YZzMTxVF-EZK"}}, {"cell_type": "code", "source": ["{\n", "    key:value\n", "}"], "metadata": {"id": "WKQxVJ-n-MAB"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["{\n", " \"fields\": [{\n", "     \"numDimensions\": 1024,\n", "     \"path\": \"embedding\",\n", "     \"similarity\": \"cosine\",\n", "     \"type\": \"vector\"\n", "   }]\n", "}"], "metadata": {"id": "1DwzWaZOYk4i", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "8721d1b5-7300-48aa-8fa7-2f2574572fa9"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'fields': [{'numDimensions': 256,\n", "   'path': 'embedding',\n", "   'similarity': 'cosine',\n", "   'type': 'vector'}]}"]}, "metadata": {}, "execution_count": 53}]}, {"cell_type": "code", "source": ["user_query=\"what is the best horror movie?\""], "metadata": {"id": "Yc0Ycu_J_e7O"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["query_embedding=get_embedding(user_query)"], "metadata": {"id": "RabgPRe1_YEn"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["query_embedding"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DFdCIpsZ9ThG", "outputId": "4b28823e-3e1f-4f34-b288-89639d675384"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[0.0320083424448967,\n", " -0.01637578010559082,\n", " -0.03466120362281799,\n", " 0.0014083574060350657,\n", " -0.0016439456958323717,\n", " -0.01220844779163599,\n", " -0.015490734949707985,\n", " 0.04027733579277992,\n", " -0.008300913497805595,\n", " 0.027359437197446823,\n", " 0.032420944422483444,\n", " -0.007007724605500698,\n", " 0.014090870507061481,\n", " -0.032076358795166016,\n", " -0.00868840143084526,\n", " -0.02813737653195858,\n", " -0.035497456789016724,\n", " -0.02312837727367878,\n", " -0.021521814167499542,\n", " 0.01007708441466093,\n", " -0.008985697291791439,\n", " 0.006707683205604553,\n", " -0.06067965179681778,\n", " -0.013847114518284798,\n", " -0.020732101052999496,\n", " 0.016766954213380814,\n", " 0.02398809790611267,\n", " -0.011809716932475567,\n", " 0.0367332324385643,\n", " 0.08142603933811188,\n", " 0.007920694537460804,\n", " -0.041727010160684586,\n", " 0.0431947186589241,\n", " -0.03834754228591919,\n", " -0.02800973504781723,\n", " -0.01896686851978302,\n", " 0.06600956618785858,\n", " -0.0005763605586253107,\n", " -0.021067526191473007,\n", " -0.03700760751962662,\n", " 0.020190652459859848,\n", " 0.0027971433009952307,\n", " 0.05587853491306305,\n", " -0.009170588105916977,\n", " -0.046688955277204514,\n", " -0.008952177129685879,\n", " 0.027583790943026543,\n", " -0.015085379593074322,\n", " 0.001716073602437973,\n", " -0.04377417638897896,\n", " -0.0013343007303774357,\n", " -0.029875807464122772,\n", " 0.016525188460946083,\n", " 0.017629249021410942,\n", " -0.003260757541283965,\n", " 0.0196536872535944,\n", " -0.02507338486611843,\n", " -0.008535761386156082,\n", " 0.00541296461597085,\n", " 0.032042283564805984,\n", " 0.033866774290800095,\n", " -0.01481974869966507,\n", " 0.029714861884713173,\n", " -0.03202426806092262,\n", " 0.010879479348659515,\n", " 0.023328861221671104,\n", " 0.01556407567113638,\n", " -0.022306367754936218,\n", " 0.0276019424200058,\n", " 0.023372985422611237,\n", " 0.02104932814836502,\n", " 0.0020659510046243668,\n", " -0.01805460825562477,\n", " 0.034766580909490585,\n", " 0.0030250963754951954,\n", " 0.037416160106658936,\n", " -0.015737848356366158,\n", " 0.029695739969611168,\n", " 0.0006013195379637182,\n", " -0.015980610623955727,\n", " 0.0007810760871507227,\n", " 0.04673542454838753,\n", " 0.020954906940460205,\n", " 0.02021043375134468,\n", " -0.0518590584397316,\n", " -0.031795650720596313,\n", " -0.02255077473819256,\n", " 0.018370671197772026,\n", " -0.012760679237544537,\n", " 0.034530848264694214,\n", " 0.026369286701083183,\n", " 0.037465933710336685,\n", " 0.01578361727297306,\n", " -0.02252531424164772,\n", " 0.059081580489873886,\n", " 0.046660181134939194,\n", " -0.029154876247048378,\n", " -0.00461193872615695,\n", " 0.00252658873796463,\n", " -0.01652183197438717,\n", " 0.0059542544186115265,\n", " 0.03130736202001572,\n", " 0.008508890867233276,\n", " 0.040518682450056076,\n", " -0.03147357329726219,\n", " -0.017474522814154625,\n", " 0.02691986784338951,\n", " -0.009386299178004265,\n", " -0.020911341533064842,\n", " -0.0633140355348587,\n", " 0.00048605314805172384,\n", " -0.027242420241236687,\n", " 0.01765912026166916,\n", " 0.010228556580841541,\n", " -0.01134394109249115,\n", " 0.025111092254519463,\n", " -0.0013617597287520766,\n", " 0.05826788395643234,\n", " -0.05354244261980057,\n", " 0.027653351426124573,\n", " 0.03721358999609947,\n", " 0.0005469551542773843,\n", " 0.042968153953552246,\n", " -0.024621400982141495,\n", " 0.038373176008462906,\n", " -0.05155014619231224,\n", " 0.0028678118251264095,\n", " 0.04243101552128792,\n", " -0.030731461942195892,\n", " -0.007609691470861435,\n", " 0.011489922180771828,\n", " -0.0258830226957798,\n", " -0.0030866959132254124,\n", " 0.04977764934301376,\n", " 0.005797508638352156,\n", " 0.014803319238126278,\n", " -0.0014377349289134145,\n", " -0.014330712147057056,\n", " 0.029788434505462646,\n", " -0.05668390169739723,\n", " 0.027582965791225433,\n", " 0.029079509899020195,\n", " 0.01149930153042078,\n", " 0.07020417600870132,\n", " -0.0038389668334275484,\n", " -0.015898872166872025,\n", " -0.003764204913750291,\n", " 0.009256451390683651,\n", " -0.006592732388526201,\n", " 0.029001282528042793,\n", " -0.017171422019600868,\n", " 0.003595024114474654,\n", " -0.011424308642745018,\n", " 0.005475982092320919,\n", " -0.0027443058788776398,\n", " -0.010675011202692986,\n", " -0.026074890047311783,\n", " 0.015689291059970856,\n", " 0.025124821811914444,\n", " 0.05992331728339195,\n", " 0.030047165229916573,\n", " -0.0002331603318452835,\n", " -0.01935049332678318,\n", " 0.030535591766238213,\n", " -0.006786844693124294,\n", " 0.042349763214588165,\n", " -0.01890731416642666,\n", " 0.0021701978985220194,\n", " -0.019379358738660812,\n", " -0.016652610152959824,\n", " 0.013443248346447945,\n", " 0.010916518978774548,\n", " 0.009990647435188293,\n", " 0.010918760672211647,\n", " 0.014142229221761227,\n", " 0.053590573370456696,\n", " 0.04675547778606415,\n", " -0.023213544860482216,\n", " 0.0531531497836113,\n", " 0.038327574729919434,\n", " -0.031362567096948624,\n", " -0.011219151318073273,\n", " -0.010264012031257153,\n", " 0.0847674161195755,\n", " -0.02824975922703743,\n", " -0.023977404460310936,\n", " 0.02091619186103344,\n", " -0.04389817267656326,\n", " -0.04698707535862923,\n", " -0.023805519565939903,\n", " -0.050646986812353134,\n", " 0.01854620687663555,\n", " -0.017234722152352333,\n", " 0.025632109493017197,\n", " 0.00038848695112392306,\n", " 0.017275435850024223,\n", " -0.01997286267578602,\n", " -0.0026897198986262083,\n", " -0.007493653334677219,\n", " -0.03711036965250969,\n", " -0.004230358637869358,\n", " 0.07117007672786713,\n", " -0.031584303826093674,\n", " 0.026847898960113525,\n", " 0.0165826678276062,\n", " -0.041716333478689194,\n", " 0.027508225291967392,\n", " 0.06431131064891815,\n", " -0.02559950388967991,\n", " -0.02757067047059536,\n", " 0.014944643713533878,\n", " -0.014692222699522972,\n", " 0.013505763374269009,\n", " 0.005856464616954327,\n", " -0.03337870165705681,\n", " 0.008211865089833736,\n", " -0.02221912145614624,\n", " 0.0487479642033577,\n", " -0.02276436612010002,\n", " 0.041680674999952316,\n", " -0.016766218468546867,\n", " 0.019577786326408386,\n", " 0.03149105980992317,\n", " 0.02446567267179489,\n", " 0.0048637608997523785,\n", " -0.009584919549524784,\n", " 0.026308679953217506,\n", " 0.03261299058794975,\n", " -0.020143497735261917,\n", " 0.01716172881424427,\n", " 0.011971843428909779,\n", " 0.05356647074222565,\n", " 0.03546483814716339,\n", " 0.02939128875732422,\n", " 0.015586011111736298,\n", " -0.006758914794772863,\n", " 0.04806090518832207,\n", " 0.005973189603537321,\n", " -0.017461877316236496,\n", " 0.031069448217749596,\n", " -0.01811039261519909,\n", " -0.03229124844074249,\n", " 0.04348602145910263,\n", " 0.03104819916188717,\n", " -0.011675715446472168,\n", " 0.036631666123867035,\n", " -0.0176128838211298,\n", " -0.028673851862549782,\n", " -0.04366979002952576,\n", " 0.01251201331615448,\n", " 0.022035634145140648,\n", " 0.03080233559012413,\n", " 0.039421916007995605,\n", " 0.018589366227388382,\n", " -0.013790071941912174,\n", " 0.03124174289405346,\n", " 0.031659409403800964,\n", " 0.06295467168092728,\n", " -0.017452511936426163,\n", " 0.002383571118116379,\n", " -0.04541051760315895,\n", " 0.020178791135549545,\n", " 0.020895741879940033,\n", " 0.016896972432732582,\n", " 0.05192938074469566,\n", " 0.010109004564583302,\n", " -0.01633111760020256,\n", " -0.006542182061821222,\n", " -0.03694801777601242,\n", " -0.06644240021705627,\n", " -0.022229937836527824,\n", " -0.03849165141582489,\n", " -0.023207129910588264,\n", " -0.02982049249112606,\n", " -0.042382609099149704,\n", " 0.0042299870401620865,\n", " 0.027059895917773247,\n", " -0.013148116879165173,\n", " 0.0024370437022298574,\n", " -0.01157749816775322,\n", " -0.024431761354207993,\n", " -0.01168099045753479,\n", " 0.000994108384475112,\n", " 0.0212993286550045,\n", " 0.0017652035458013415,\n", " 0.037856679409742355,\n", " -0.03670325502753258,\n", " 0.038694724440574646,\n", " -0.02452235296368599,\n", " 0.04106833413243294,\n", " -0.013157455250620842,\n", " 0.0009883997263386846,\n", " -0.010273286141455173,\n", " 0.002611482283100486,\n", " 0.037740230560302734,\n", " -0.0008730711997486651,\n", " -0.026039084419608116,\n", " -0.02080417238175869,\n", " -0.016539297997951508,\n", " -0.055078212171792984,\n", " 0.01880439929664135,\n", " -0.020810000598430634,\n", " -0.027608981356024742,\n", " -0.0008206721977330744,\n", " -0.005096815526485443,\n", " 0.034677904099226,\n", " 0.03466606140136719,\n", " -0.0044159251265227795,\n", " 0.048831574618816376,\n", " 0.03930937871336937,\n", " -0.04362413287162781,\n", " 0.04336021840572357,\n", " -0.001811290974728763,\n", " 0.01067870482802391,\n", " -0.0516183115541935,\n", " 0.06915754824876785,\n", " 0.02316734381020069,\n", " 0.00906798429787159,\n", " -0.005265364423394203,\n", " 0.03295065090060234,\n", " -0.011513889767229557,\n", " 0.007077887654304504,\n", " 0.01732967235147953,\n", " -0.02508668787777424,\n", " -0.031411487609148026,\n", " 0.043886080384254456,\n", " 0.008435872383415699,\n", " -0.04090367257595062,\n", " 0.02344157174229622,\n", " -0.024470267817378044,\n", " -0.012635266408324242,\n", " -0.028201404958963394,\n", " -0.033845942467451096,\n", " 0.04022417962551117,\n", " 0.02926657535135746,\n", " 0.05832132324576378,\n", " -0.03198074549436569,\n", " -0.04916398599743843,\n", " -0.029189376160502434,\n", " 0.0346238948404789,\n", " 0.04478517174720764,\n", " -0.0169807318598032,\n", " 0.01433201227337122,\n", " 0.03749685734510422,\n", " 0.0109624108299613,\n", " 0.011340126395225525,\n", " 0.006959011312574148,\n", " -0.016072366386651993,\n", " 0.030212482437491417,\n", " -0.0027883390430361032,\n", " -0.008492749184370041,\n", " -0.019293831661343575,\n", " 0.005968058947473764,\n", " -0.023891905322670937,\n", " 0.021176043897867203,\n", " 0.03496231511235237,\n", " -0.01381784025579691,\n", " 0.04341335594654083,\n", " -0.0057830726727843285,\n", " 0.024713363498449326,\n", " 0.03472551703453064,\n", " 0.012161928229033947,\n", " -0.011888762004673481,\n", " 0.01190529577434063,\n", " -0.051043059676885605,\n", " -0.058776505291461945,\n", " 0.025075113400816917,\n", " -0.0030087092891335487,\n", " 0.053557444363832474,\n", " -0.04627557843923569,\n", " 0.07040636986494064,\n", " 0.0034071144182235003,\n", " -0.03841283544898033,\n", " 0.04719385877251625,\n", " -0.03614811971783638,\n", " 0.03721558302640915,\n", " -0.0007028326508589089,\n", " -0.021172653883695602,\n", " 0.009281529113650322,\n", " -0.014774374663829803,\n", " 0.009757322259247303,\n", " -0.005283181089907885,\n", " 0.05101078748703003,\n", " 0.06840945035219193,\n", " 0.005296273622661829,\n", " 0.055748842656612396,\n", " -0.035592176020145416,\n", " -0.0420353002846241,\n", " -0.027692312374711037,\n", " 0.0016471701674163342,\n", " -0.012150180526077747,\n", " -0.03128162771463394,\n", " 0.03884274140000343,\n", " -0.025297319516539574,\n", " -0.07434152066707611,\n", " -0.05517898499965668,\n", " 0.035426296293735504,\n", " 0.0313832089304924,\n", " 0.02538769319653511,\n", " -0.030098365619778633,\n", " 0.00466163782402873,\n", " -0.02643025852739811,\n", " 0.0014811112778261304,\n", " 0.03453640639781952,\n", " -0.007776681799441576,\n", " 0.037065599113702774,\n", " -0.022990843281149864,\n", " 0.0476495586335659,\n", " 0.005677433218806982,\n", " -0.012237003073096275,\n", " -0.017186462879180908,\n", " -0.006583008915185928,\n", " -0.04958989471197128,\n", " 0.0371844545006752,\n", " -0.007009480148553848,\n", " -0.011384310200810432,\n", " -0.014562916941940784,\n", " 0.013777966611087322,\n", " 0.01991540752351284,\n", " 0.031234135851264,\n", " -0.02753225527703762,\n", " -0.001511333859525621,\n", " -0.0005704061477445066,\n", " 0.024375176057219505,\n", " 0.007497451268136501,\n", " -0.07129082828760147,\n", " 0.0008249030215665698,\n", " -0.04212255775928497,\n", " 0.044047024101018906,\n", " 0.05054446682333946,\n", " -0.012897291220724583,\n", " -0.029765047132968903,\n", " -0.019555123522877693,\n", " -0.03677171841263771,\n", " -0.07175540924072266,\n", " 0.002379509387537837,\n", " 0.04682141914963722,\n", " -0.017925726249814034,\n", " 0.03190775588154793,\n", " -0.05961049720644951,\n", " 0.009439926594495773,\n", " -0.0068567488342523575,\n", " 0.005058102309703827,\n", " -0.024898586794734,\n", " -0.013754217885434628,\n", " 0.024394964799284935,\n", " 0.010254024527966976,\n", " 0.03762292489409447,\n", " -0.010332221165299416,\n", " -0.03212360292673111,\n", " -0.01960546150803566,\n", " -0.062105316668748856,\n", " -0.020261142402887344,\n", " 0.024305524304509163,\n", " -0.03190203383564949,\n", " 0.04037296399474144,\n", " 0.03744792565703392,\n", " 0.006455634720623493,\n", " 0.048445697873830795,\n", " -0.032907791435718536,\n", " 0.06250669062137604,\n", " -0.0484582781791687,\n", " 0.02631102316081524,\n", " -0.018425755202770233,\n", " -0.03983233869075775,\n", " 0.04776257276535034,\n", " 0.0275929793715477,\n", " -0.017269691452383995,\n", " 0.030579032376408577,\n", " 0.000181119074113667,\n", " 0.006638096645474434,\n", " 0.00043141731293872,\n", " 0.04981871321797371,\n", " -0.04435674846172333,\n", " 0.04552777111530304,\n", " -0.05258849635720253,\n", " -0.003719284199178219,\n", " 0.006289223209023476,\n", " -0.02118157222867012,\n", " 0.02046760730445385,\n", " -0.037510089576244354,\n", " 0.02529483661055565,\n", " 0.0059031094424426556,\n", " 0.022844836115837097,\n", " -0.036923572421073914,\n", " -0.02455769293010235,\n", " -0.01900956593453884,\n", " -0.017485646530985832,\n", " 0.009114543907344341,\n", " -0.029008256271481514,\n", " 0.012588067911565304,\n", " 0.012716923840343952,\n", " -0.036013081669807434,\n", " -0.016410328447818756,\n", " -0.003712474601343274,\n", " 0.004540568683296442,\n", " 0.0004923197557218373,\n", " 0.019713427871465683,\n", " -0.014879216440021992,\n", " 0.0226570013910532,\n", " -0.015876080840826035,\n", " -0.0073432764038443565,\n", " -0.02549940161406994,\n", " -0.00011876621283590794,\n", " 0.017177455127239227,\n", " -0.00990022998303175,\n", " -0.07488550990819931,\n", " 0.007931282743811607,\n", " -0.042061056941747665,\n", " -0.006222485564649105,\n", " -0.03114369884133339,\n", " 0.010301882401108742,\n", " -0.04532897472381592,\n", " 0.0568619966506958,\n", " 0.021757401525974274,\n", " 0.0430845245718956,\n", " -0.001699781627394259,\n", " -0.0033718866761773825,\n", " -0.03672127425670624,\n", " 0.030745480209589005,\n", " -0.011234819889068604,\n", " -0.02500445954501629,\n", " -0.03705909848213196,\n", " 0.03834379091858864,\n", " -0.01625017262995243,\n", " 0.04239797964692116,\n", " -0.001444420893676579,\n", " -0.013778355903923512,\n", " -0.08461395651102066,\n", " -0.0365104116499424,\n", " 0.004832316190004349,\n", " -0.031362906098365784,\n", " -0.006644122768193483,\n", " -0.02927999012172222,\n", " -0.04272846505045891,\n", " -0.018359258770942688,\n", " 0.011553013697266579,\n", " 0.014389651827514172,\n", " -0.010743328370153904,\n", " -0.02447647415101528,\n", " -0.02671954780817032,\n", " -0.025764351710677147,\n", " 0.019049551337957382,\n", " -0.025418704375624657,\n", " -0.021218782290816307,\n", " 0.007712233811616898,\n", " -0.015075122006237507,\n", " 0.07588320970535278,\n", " -0.016935519874095917,\n", " 0.04526346176862717,\n", " -0.005377665627747774,\n", " 0.020906586199998856,\n", " 0.006312476005405188,\n", " -0.02108699269592762,\n", " -0.05657551810145378,\n", " -0.013907468877732754,\n", " 0.005273653194308281,\n", " -0.008176037110388279,\n", " -0.02289867028594017,\n", " 0.02458825334906578,\n", " -0.05025070905685425,\n", " 0.020176397636532784,\n", " -0.06626732647418976,\n", " 0.004980477038770914,\n", " -0.009706705808639526,\n", " -0.008748910389840603,\n", " -0.0023804849479347467,\n", " -0.00227232719771564,\n", " 0.05410038307309151,\n", " -0.040545158088207245,\n", " -0.015219778753817081,\n", " 0.0006153499707579613,\n", " 0.041631825268268585,\n", " 0.09947852790355682,\n", " -0.0022711886558681726,\n", " -0.0324297696352005,\n", " -0.02048969268798828,\n", " -0.01924304850399494,\n", " -0.0511394627392292,\n", " 0.038290899246931076,\n", " -0.05717520788311958,\n", " -0.005888740532100201,\n", " -0.042922597378492355,\n", " 0.004508807323873043,\n", " 0.06346166878938675,\n", " -0.01505326759070158,\n", " 0.04886005446314812,\n", " 0.05511261150240898,\n", " -0.0073094149120152,\n", " 0.015055007301270962,\n", " -0.002909553237259388,\n", " -0.004268262535333633,\n", " 0.05969817936420441,\n", " -0.06363614648580551,\n", " 0.01151854544878006,\n", " -0.00734414579346776,\n", " -0.03702259808778763,\n", " -0.012285761535167694,\n", " -0.05847303196787834,\n", " -0.06695907562971115,\n", " -0.05666105076670647,\n", " 0.015511224046349525,\n", " 0.10456276684999466,\n", " -0.03713815659284592,\n", " 0.039683081209659576,\n", " -0.023728804662823677,\n", " -0.06883618980646133,\n", " -0.03938070684671402,\n", " 0.04548387974500656,\n", " -0.007106712553650141,\n", " 0.008725299499928951,\n", " 0.04164428636431694,\n", " 0.030391355976462364,\n", " -0.049646105617284775,\n", " -0.03290489315986633,\n", " 0.030812090262770653,\n", " 0.0066393702290952206,\n", " -0.02446414902806282,\n", " 0.05510878935456276,\n", " 0.0034250968601554632,\n", " -0.03958488255739212,\n", " 0.05150347575545311,\n", " 0.029832938686013222,\n", " -0.056532569229602814,\n", " -0.046270228922367096,\n", " -0.004468561615794897,\n", " 0.01872154325246811,\n", " 0.00419368501752615,\n", " -0.03406982868909836,\n", " 0.021832160651683807,\n", " -0.029534582048654556,\n", " 0.007273093331605196,\n", " -0.013666501268744469,\n", " 0.029788056388497353,\n", " -0.030600469559431076,\n", " 0.029443614184856415,\n", " 0.04571687430143356,\n", " 0.004005624447017908,\n", " -0.03711233288049698,\n", " 0.014189121313393116,\n", " -0.018103118985891342,\n", " 0.008664075285196304,\n", " -0.04184175655245781,\n", " -0.0316946916282177,\n", " -0.037789274007081985,\n", " 0.01682756468653679,\n", " -0.011942954733967781,\n", " 0.025110019370913506,\n", " -0.0023684126790612936,\n", " 0.0003362417919561267,\n", " 0.02713538520038128,\n", " -0.01119802426546812,\n", " 0.08165594935417175,\n", " 0.008485783822834492,\n", " -0.024673910811543465,\n", " 0.001348857767879963,\n", " -0.03403171896934509,\n", " -0.0620630644261837,\n", " -0.04878373444080353,\n", " 0.019035078585147858,\n", " 0.016332002356648445,\n", " -0.0009149906691163778,\n", " -0.052474915981292725,\n", " 0.01125816535204649,\n", " 0.009756551124155521,\n", " -0.018441779538989067,\n", " 0.005976788699626923,\n", " -0.0631534531712532,\n", " -0.019527362659573555,\n", " -0.05885957553982735,\n", " -0.026832913979887962,\n", " -0.008003398776054382,\n", " -0.001131885452196002,\n", " -0.020799249410629272,\n", " 0.020075548440217972,\n", " 0.014112424105405807,\n", " -0.028260260820388794,\n", " 0.022664135321974754,\n", " -0.012251611799001694,\n", " -0.008958139456808567,\n", " 0.006114661693572998,\n", " -0.04157058894634247,\n", " 0.03876710683107376,\n", " -0.02798980474472046,\n", " -0.04241243377327919,\n", " -0.015379033982753754,\n", " 0.03176543489098549,\n", " -0.007517536170780659,\n", " -0.001908900449052453,\n", " -0.014084956608712673,\n", " 0.0033839670941233635,\n", " -0.002754684304818511,\n", " 0.014463189989328384,\n", " -0.015995923429727554,\n", " 0.01315355859696865,\n", " -0.005215244367718697,\n", " -0.0015100616728886962,\n", " 0.006563748233020306,\n", " 0.030717771500349045,\n", " -0.00033814573544077575,\n", " 0.05242041498422623,\n", " 0.008169949054718018,\n", " 0.0034865124616771936,\n", " 0.031300317496061325,\n", " 0.0025663464330136776,\n", " -0.04086348041892052,\n", " -0.01260207686573267,\n", " 0.014389990828931332,\n", " -0.015165163204073906,\n", " 0.019418084993958473,\n", " -0.03647715970873833,\n", " -0.03424395993351936,\n", " -0.04160663112998009,\n", " -0.04855550080537796,\n", " 0.030143311247229576,\n", " 0.030628224834799767,\n", " 0.022341107949614525,\n", " -0.02318219281733036,\n", " 0.02918735146522522,\n", " 0.014572209678590298,\n", " 0.05364776775240898,\n", " -0.01600802130997181,\n", " 0.02108350396156311,\n", " 0.01111538615077734,\n", " 0.013054749928414822,\n", " 0.022361362352967262,\n", " -0.037106189876794815,\n", " 0.029531657695770264,\n", " 0.030485432595014572,\n", " 0.02072439342737198,\n", " -0.05206810683012009,\n", " -0.013789581134915352,\n", " -0.02538866177201271,\n", " 0.019733643159270287,\n", " 0.02608751319348812,\n", " -0.02066013589501381,\n", " -0.023639556020498276,\n", " -0.041149526834487915,\n", " -0.038563087582588196,\n", " -0.0017569900956004858,\n", " 0.04974504932761192,\n", " 0.0016182779800146818,\n", " -0.010968808084726334,\n", " 0.030100123956799507,\n", " -0.0371130146086216,\n", " -0.06316851079463959,\n", " 0.01804158464074135,\n", " -0.04957375302910805,\n", " 0.005125928670167923,\n", " 0.011303829029202461,\n", " 0.013548918068408966,\n", " -0.017104551196098328,\n", " -0.051881514489650726,\n", " -0.062979556620121,\n", " 0.014042955823242664,\n", " -0.016030041500926018,\n", " 0.021975483745336533,\n", " 0.003758290782570839,\n", " 0.025800365954637527,\n", " -0.028287554159760475,\n", " 0.0701737031340599,\n", " -0.007792032789438963,\n", " -0.004604606423527002,\n", " 0.00175482255872339,\n", " 0.07390168309211731,\n", " -0.047458380460739136,\n", " -0.006214120425283909,\n", " 0.011195269413292408,\n", " -0.015644535422325134,\n", " 0.02258998528122902,\n", " -0.00768631836399436,\n", " -0.0005506377783603966,\n", " 0.0038528295699507,\n", " 0.008227605372667313,\n", " 0.02376684918999672,\n", " -0.021382054314017296,\n", " 0.01673649065196514,\n", " -0.01374427042901516,\n", " 0.0006771056214347482,\n", " 0.019893864169716835,\n", " -0.016554977744817734,\n", " -0.015912260860204697,\n", " 0.030273452401161194,\n", " 0.019155927002429962,\n", " -0.005888762418180704,\n", " -0.05043479800224304,\n", " 0.03606821224093437,\n", " 0.031460389494895935,\n", " -0.0017039498779922724,\n", " 0.042214285582304,\n", " 0.028882993385195732,\n", " 0.0744607001543045,\n", " -0.002153139328584075,\n", " 0.015405047684907913,\n", " -0.0023567541502416134,\n", " 0.04595622420310974,\n", " -0.016405511647462845,\n", " 0.022424982860684395,\n", " 0.008051193319261074,\n", " 0.010061045177280903,\n", " 0.03169863298535347,\n", " -0.007846961729228497,\n", " -0.009791666641831398,\n", " 0.04016551375389099,\n", " 0.018661074340343475,\n", " -0.03460422158241272,\n", " -0.013666559010744095,\n", " 0.017355021089315414,\n", " 0.013359742239117622,\n", " -0.01310182735323906,\n", " 0.006705667823553085,\n", " -0.0016606035642325878,\n", " 0.0006718587246723473,\n", " 0.0019553385209292173,\n", " 0.003385477466508746,\n", " 0.010304764844477177,\n", " 0.07004935294389725,\n", " -0.022485358640551567,\n", " -0.02073410339653492,\n", " -0.012895528227090836,\n", " -0.012766683474183083,\n", " 0.03132625296711922,\n", " 0.06282223761081696,\n", " 0.0006523951306007802,\n", " 0.010830894112586975,\n", " 0.02794673666357994,\n", " 0.033051177859306335,\n", " -0.03176870197057724,\n", " 0.03918156400322914,\n", " 0.045093584805727005,\n", " 0.0036892853677272797,\n", " -0.005855637602508068,\n", " 0.009873568080365658,\n", " 0.030747776851058006,\n", " -0.02780495397746563,\n", " -0.04286155104637146,\n", " -0.004389184061437845,\n", " -0.028925348073244095,\n", " 0.0307517908513546,\n", " -0.00373843708075583,\n", " -0.010019239969551563,\n", " 0.0649939551949501,\n", " -0.05315003916621208,\n", " 0.0067647844552993774,\n", " -0.08319016546010971,\n", " 0.029389485716819763,\n", " -0.035002969205379486,\n", " -0.016749711707234383,\n", " 0.04197307303547859,\n", " 0.00535077229142189,\n", " -0.0039594764821231365,\n", " -0.019097359851002693,\n", " 0.009515028446912766,\n", " 0.0374942347407341,\n", " -0.013465947471559048,\n", " 0.024685461074113846,\n", " -0.03243553265929222,\n", " 0.021538475528359413,\n", " 0.006090914830565453,\n", " -0.02511066012084484,\n", " -0.026674162596464157,\n", " 0.0259331613779068,\n", " -0.007551244925707579,\n", " -0.05354144796729088,\n", " -0.00018236663891002536,\n", " -0.05235429108142853,\n", " 0.0009750741883181036,\n", " -0.006890533491969109,\n", " -0.036300178617239,\n", " -0.008648942224681377,\n", " 0.03015824593603611,\n", " -0.02276630513370037,\n", " -0.057108763605356216,\n", " -0.014044168405234814,\n", " 0.010318667627871037,\n", " -0.07134224474430084,\n", " -0.005842370446771383,\n", " 0.018024610355496407,\n", " 0.019099488854408264,\n", " 0.015222783200442791,\n", " -0.014102432876825333,\n", " -0.04613926634192467,\n", " -0.034853555262088776,\n", " -0.020546825602650642,\n", " -0.011953460983932018,\n", " 0.04122937098145485,\n", " -0.014879101887345314,\n", " 0.002500436967238784,\n", " -0.016286693513393402,\n", " -0.05220145732164383,\n", " -0.030709249898791313,\n", " -0.049869515001773834,\n", " -0.002811237471178174,\n", " -0.013987752608954906,\n", " 0.048653408885002136,\n", " 0.05671068653464317,\n", " -0.006152629852294922,\n", " -0.019031355157494545,\n", " -0.013556762598454952,\n", " -0.00045089295599609613,\n", " -0.015632810071110725,\n", " 0.04184472933411598,\n", " -0.012746446765959263,\n", " 0.07049907743930817,\n", " 0.03773956745862961,\n", " -0.016590002924203873,\n", " 0.01605810597538948,\n", " -0.053631216287612915,\n", " 0.03562241047620773,\n", " -0.03417888656258583,\n", " -0.03328385576605797,\n", " -0.034346699714660645,\n", " 0.03821025416254997,\n", " -0.02180301584303379,\n", " -0.06833025068044662,\n", " 0.0030748581048101187,\n", " 0.009924974292516708,\n", " -0.00426869560033083,\n", " 0.004009474068880081,\n", " -0.09839314967393875,\n", " 0.009448913857340813,\n", " -0.028040437027812004,\n", " 0.023953326046466827,\n", " -0.04564105346798897,\n", " 0.02041853778064251,\n", " 0.006967624649405479,\n", " -0.035370420664548874,\n", " -0.030388236045837402,\n", " -0.04905392974615097,\n", " 0.1361858993768692,\n", " 0.008273168466985226,\n", " 0.04570212587714195,\n", " 0.03288442641496658,\n", " 0.012408381327986717,\n", " 0.01564021408557892,\n", " 0.027308642864227295,\n", " -0.0001947550190379843,\n", " 0.011951032094657421,\n", " -0.005731224548071623,\n", " 0.05363156273961067,\n", " 0.014646686613559723,\n", " 0.015805402770638466,\n", " 0.014462721534073353,\n", " 0.04982219636440277,\n", " 0.058464787900447845,\n", " -0.03827068582177162,\n", " 0.05949128419160843,\n", " 0.0074715823866426945,\n", " -0.06806308776140213,\n", " -0.060849033296108246,\n", " 0.04574258625507355,\n", " 0.008420946076512337,\n", " -0.000974111957475543,\n", " -0.03678227961063385,\n", " 0.010226192884147167,\n", " 0.036650411784648895,\n", " -0.06912534683942795,\n", " -0.008776746690273285,\n", " -0.023256370797753334,\n", " 0.06248997524380684,\n", " -0.02357020601630211,\n", " 0.03735416382551193,\n", " -0.03548459708690643,\n", " -0.019314024597406387,\n", " 0.03562819957733154,\n", " 0.01410192996263504,\n", " -0.02216494083404541,\n", " 0.03047507256269455,\n", " 0.01583467796444893,\n", " -0.019913606345653534,\n", " 0.0049051688984036446,\n", " 0.05732307583093643,\n", " -0.03402582183480263,\n", " -0.01404380053281784,\n", " 0.013969295658171177,\n", " -0.041592393070459366,\n", " -0.03990504518151283,\n", " -0.02626892738044262,\n", " -0.035489942878484726,\n", " 0.05097212642431259,\n", " -0.05769454687833786,\n", " 0.004891549237072468,\n", " -0.0018826875602826476,\n", " -0.047989267855882645,\n", " 0.023515528067946434,\n", " 0.02331513911485672,\n", " -0.03431390970945358,\n", " 0.0009962194599211216,\n", " 0.0007804037886671722,\n", " 0.04259341582655907,\n", " -0.013952085748314857,\n", " 0.006064048036932945,\n", " -0.02327600307762623,\n", " -0.0019168939907103777,\n", " -0.015255188569426537,\n", " 0.0185005534440279,\n", " 0.027002111077308655,\n", " -0.0008487559971399605,\n", " -0.015243718400597572,\n", " 0.010173475369811058,\n", " ...]"]}, "metadata": {}, "execution_count": 57}]}, {"cell_type": "code", "source": ["print(query_embedding)"], "metadata": {"id": "t2cu9AAT_YHI"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["https://www.mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/\n"], "metadata": {"id": "0xfcIBkkAwX-"}}, {"cell_type": "code", "source": ["pipeline = [\n", "\n", "    {\n", "        \"$vectorSearch\": {\n", "            \"index\": \"vector_index\",\n", "            \"queryVector\": query_embedding,\n", "            \"path\": \"embedding\",\n", "            \"numCandidates\": 150,  # Number of candidate matches to consider\n", "            \"limit\": 4,  # Return top 4 matches\n", "        }\n", "    },\n", "    {\n", "        \"$project\": {\n", "            \"fullplot\": 1,  # Include the plot field\n", "            \"title\": 1,  # Include the title field\n", "            \"genres\": 1,  # Include the genres field\n", "            \"score\": {\"$meta\": \"vectorSearchScore\"},  # Include the search score\n", "        }\n", "    }\n", "]"], "metadata": {"id": "EA1A6f0GEyhg"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["collection.aggregate(pipeline)"], "metadata": {"id": "gQSzWEaIGn3w", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "8404e42a-7bc8-4423-ecc4-87b6d0b8a9db"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<pymongo.command_cursor.CommandCursor at 0x7e98d09b51e0>"]}, "metadata": {}, "execution_count": 59}]}, {"cell_type": "code", "source": ["list(collection.aggregate(pipeline))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "N7C8l1_a_YM-", "outputId": "4c8186c1-f27c-4190-9b05-de0fb1e7f3c2"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[{'_id': ObjectId('662c02ce71d807af72388799'),\n", "  'title': 'Pet Sematary II',\n", "  'fullplot': 'The \"sematary\" is up to its old zombie-raising tricks again. This time, the protagonists are <PERSON>, whose mother died in a Hollywood stage accident, and <PERSON>, a boy coping with an abusive stepfather.',\n", "  'genres': ['Action', 'Horror', 'Thriller'],\n", "  'score': 0.910086989402771},\n", " {'_id': ObjectId('662c02ce71d807af723889e2'),\n", "  'title': 'House of the Dead',\n", "  'fullplot': 'This film is a prequel to all of the The House of the Dead video games. Set on an island off the coast, a techno rave party attracts a diverse group of college coeds and a Coast Guard officer. Soon, they discover that their X-laced escapades are to be interrupted by zombies and monsters that attack them on the ground, from the air, and in the sea, ruled by an evil entity in the House of the Dead...',\n", "  'genres': ['Action', 'Horror'],\n", "  'score': 0.9023109674453735},\n", " {'_id': ObjectId('662c02ce71d807af72388b0c'),\n", "  'title': '<PERSON><PERSON><PERSON><PERSON>',\n", "  'fullplot': 'A double-bill of thrillers that recall both filmmakers\\' favorite exploitation films. \"Grindhouse\" (a downtown movie theater in disrepair since its glory days as a movie palace known for \"grinding out\" non-stop double-bill programs of B-movies) is presented as one full-length feature comprised of two individual films helmed separately by each director. \"Death Proof,\" is a rip-roaring slasher flick where the killer pursues his victims with a car rather than a knife, while \"Planet Terror\" shows us a view of the world in the midst of a zombie outbreak. The films are joined together by clever faux trailers that recall the \\'50s exploitation drive-in classics.',\n", "  'genres': ['Action', 'Horror', 'Thriller'],\n", "  'score': 0.9010804891586304},\n", " {'_id': ObjectId('662c02ce71d807af72388a34'),\n", "  'title': 'Dawn of the Dead',\n", "  'fullplot': '<PERSON>, a young beautiful nurse finishes her day-shift at the hospital to return home to her beloved husband, they make love and sleep together. The next day, after her husband is killed by her neighbor next door, he suddenly comes back to life. She discovers the chaos happening in her neighborhood and escapes from her home. Soon after coming to her senses in the woods, she encounters a cop and other survivors, they decide to find safety in a mall. Soon more survivors come, and they learn that if they want to stay alive, they should stick together as the world is overrun by an army of undead. Can they survive the horror in this horrific global chaos? When there is no more room in hell, the dead will walk the earth',\n", "  'genres': ['Action', 'Horror', 'Sci-Fi'],\n", "  'score': 0.8991658091545105}]"]}, "metadata": {}, "execution_count": 60}]}, {"cell_type": "code", "source": ["def get_embedding(text:str)->list[float]:\n", "\n", "  if not text.strip():\n", "    print(\"attempted to get embedding for empty text.\")\n", "    return []\n", "\n", "  embedding=embedding_model.encode(text)\n", "  return embedding.tolist()\n"], "metadata": {"id": "9ToJEOAYD8gY"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["def vector_search(user_query,collection):\n", "\n", "  query_embedding=get_embedding(user_query)\n", "  print(query_embedding)\n", "\n", "  if query_embedding is None:\n", "    return \"Invalid query or embeddig is failed\"\n", "\n", "  pipeline=[\n", "\n", "            {\n", "                \"$vectorSearch\":{\n", "\n", "                \"index\": \"vector_index\",\n", "                \"queryVector\": query_embedding,\n", "                \"path\": \"embedding\",\n", "                \"numCandidates\": 150,  # Number of candidate matches to consider\n", "                \"limit\": 4,  # Return top 4 matches\n", "\n", "\n", "                }\n", "\n", "            },\n", "\n", "              {\n", "                 \"$project\":{\n", "\n", "                \"fullplot\": 1,  # Include the plot field\n", "                \"title\": 1,  # Include the title field\n", "                \"genres\": 1,  # Include the genres field\n", "                \"score\": {\"$meta\": \"vectorSearchScore\"},  # Include the search score\n", "                 }\n", "\n", "            }\n", "\n", "           ]\n", "\n", "  result=collection.aggregate(pipeline)\n", "  return list(result)\n"], "metadata": {"id": "5kwa6hwXXI45"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["vector_search(\"what is the best horror movie to watch and why?\",collection)"], "metadata": {"id": "fZVDFWsDbWv-", "outputId": "dd1cd472-3bd6-4339-a5e4-22e03205ff6f", "colab": {"base_uri": "https://localhost:8080/"}}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[{'_id': ObjectId('662c02ce71d807af72388799'),\n", "  'title': 'Pet Sematary II',\n", "  'fullplot': 'The \"sematary\" is up to its old zombie-raising tricks again. This time, the protagonists are <PERSON>, whose mother died in a Hollywood stage accident, and <PERSON>, a boy coping with an abusive stepfather.',\n", "  'genres': ['Action', 'Horror', 'Thriller'],\n", "  'score': 0.9052295684814453},\n", " {'_id': ObjectId('662c02ce71d807af72388a34'),\n", "  'title': 'Dawn of the Dead',\n", "  'fullplot': '<PERSON>, a young beautiful nurse finishes her day-shift at the hospital to return home to her beloved husband, they make love and sleep together. The next day, after her husband is killed by her neighbor next door, he suddenly comes back to life. She discovers the chaos happening in her neighborhood and escapes from her home. Soon after coming to her senses in the woods, she encounters a cop and other survivors, they decide to find safety in a mall. Soon more survivors come, and they learn that if they want to stay alive, they should stick together as the world is overrun by an army of undead. Can they survive the horror in this horrific global chaos? When there is no more room in hell, the dead will walk the earth',\n", "  'genres': ['Action', 'Horror', 'Sci-Fi'],\n", "  'score': 0.8993223905563354},\n", " {'_id': ObjectId('662c02ce71d807af723889e2'),\n", "  'title': 'House of the Dead',\n", "  'fullplot': 'This film is a prequel to all of the The House of the Dead video games. Set on an island off the coast, a techno rave party attracts a diverse group of college coeds and a Coast Guard officer. Soon, they discover that their X-laced escapades are to be interrupted by zombies and monsters that attack them on the ground, from the air, and in the sea, ruled by an evil entity in the House of the Dead...',\n", "  'genres': ['Action', 'Horror'],\n", "  'score': 0.8981226682662964},\n", " {'_id': ObjectId('662c02ce71d807af72388a0c'),\n", "  'title': '<PERSON>',\n", "  'fullplot': \"<PERSON> is in the world to rid all evil, even if not everyone agrees with him. The Vatican sends the monster hunter and his ally, <PERSON>, to Transylvania. They have been sent to this land to stop the powerful Count <PERSON>. Whilst there they join forces with a Gypsy Princess called <PERSON>, who is determined to end an ancient curse on her family by destroying the vampire. They just don't know how!\",\n", "  'genres': ['Action', 'Adventure', 'Fantasy'],\n", "  'score': 0.8960311412811279}]"]}, "metadata": {}, "execution_count": 62}]}, {"cell_type": "code", "source": ["def get_search_result(query,collection):\n", "\n", "  get_knowledge=vector_search(query,collection)\n", "\n", "  search_result=\"\"\n", "\n", "  for result in get_knowledge:\n", "        search_result += f\"Title: {result.get('title', 'N/A')}, Plot: {result.get('fullplot', 'N/A')}\\n\"\n", "\n", "  return search_result\n", "\n"], "metadata": {"id": "iaSjGap8ZJtT"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["query=\"what is the best comedy movie to watch and why?\""], "metadata": {"id": "iotY_NQmDlIu"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["collection"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ToATwGLTDp5G", "outputId": "9cf39f1b-3f15-4046-e391-38d9f5f3fea6"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Collection(Database(MongoClient(host=['ac-h9niyuj-shard-00-02.u141hkk.mongodb.net:27017', 'ac-h9niyuj-shard-00-00.u141hkk.mongodb.net:27017', 'ac-h9niyuj-shard-00-01.u141hkk.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=True, retrywrites=True, w='majority', appname='Cluster0', authsource='admin', replicaset='atlas-gsdeas-shard-0', tls=True), 'moviedb2'), 'moviecollection2')"]}, "metadata": {}, "execution_count": 68}]}, {"cell_type": "code", "source": ["source_information=get_search_result(query,collection)"], "metadata": {"id": "EHZGjhJ7Z6b1"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["source_information"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 162}, "id": "t6k9DevnaRDc", "outputId": "4649da5a-497e-4c75-a0cd-98e0eeaa2813"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Title: <PERSON> Se<PERSON> II, Plot: The \"sematary\" is up to its old zombie-raising tricks again. This time, the protagonists are <PERSON>, whose mother died in a Hollywood stage accident, and <PERSON>, a boy coping with an abusive stepfather.\\nTitle: Dawn of the Dead, Plot: <PERSON>, a young beautiful nurse finishes her day-shift at the hospital to return home to her beloved husband, they make love and sleep together. The next day, after her husband is killed by her neighbor next door, he suddenly comes back to life. She discovers the chaos happening in her neighborhood and escapes from her home. Soon after coming to her senses in the woods, she encounters a cop and other survivors, they decide to find safety in a mall. Soon more survivors come, and they learn that if they want to stay alive, they should stick together as the world is overrun by an army of undead. Can they survive the horror in this horrific global chaos? When there is no more room in hell, the dead will walk the earth\\nTitle: House of the Dead, Plot: This film is a prequel to all of the The House of the Dead video games. Set on an island off the coast, a techno rave party attracts a diverse group of college coeds and a Coast Guard officer. Soon, they discover that their X-laced escapades are to be interrupted by zombies and monsters that attack them on the ground, from the air, and in the sea, ruled by an evil entity in the House of the Dead...\\nTitle: <PERSON>, Plot: <PERSON> is in the world to rid all evil, even if not everyone agrees with him. The Vatican sends the monster hunter and his ally, <PERSON>, to Transylvania. They have been sent to this land to stop the powerful Count Dracula. Whilst there they join forces with a Gypsy Princess called <PERSON> Valerious, who is determined to end an ancient curse on her family by destroying the vampire. They just don\\'t know how!\\n'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 65}]}, {"cell_type": "code", "source": ["combined_information = f\"Query: {query}\\nContinue to answer the query by using the Search Results:\\n{source_information}.\"\n", "\n", "print(combined_information)"], "metadata": {"id": "A4QC_8z8cfz8"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "4gjf2IDhEqQk"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["# generation"], "metadata": {"id": "WxxaFCFvEqkt"}}, {"cell_type": "code", "source": ["!pip install --upgrade huggingface_hub"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZWrbMuA3w1Uw", "outputId": "3383a626-58ed-431a-fe67-d03206fff2fb"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: huggingface_hub in /usr/local/lib/python3.10/dist-packages (0.22.2)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (3.13.4)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (2023.6.0)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (24.0)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (6.0.1)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (2.31.0)\n", "Requirement already satisfied: tqdm>=4.42.1 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (4.66.2)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (4.11.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface_hub) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface_hub) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface_hub) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface_hub) (2024.2.2)\n"]}]}, {"cell_type": "code", "source": ["HF_TOKEN=\"*************************************\""], "metadata": {"id": "zzWyfr858jdv"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from huggingface_hub import notebook_login\n", "notebook_login()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 145, "referenced_widgets": ["e0855a93f7b6468e8bff6162436bef3f", "444f505d10484eee82ed6f5d514a71ad", "e240ea85aff04edd809836b43fa9d505", "a34630c975fe4f6c9fdf9edc34c2e33b", "80ec675222844c2581c94f6bfbf74274", "a5b65e6845b94fd8adeb074084ffe7a8", "016d5c2235a9477e8d873334d09a7466", "ee1603395c9c48ca802f174e3bb5124e", "f8faaf294ba14b74bd79bca279bf2cdc", "3a615a0bf70a42b5b707d59cb2ebf814", "3f3ad2143dc449bdb7ecb2f25f612be9", "db398f3bf987456cb4aac8af36799184", "1a691494160e412982fc18e45fb03b7e", "4bc717574a9e45aca41967accaaa9dbc", "0a30b4c4c66f4e8a8925340167ac6663", "d53443b290014ee59053d15fb92f5445", "24dea166a3e24ef89e954dd2408162bf", "26605b34bce049bfb2859122ecb0ad41", "a9fa44da58bf4d949f4517dace95ad7d", "2bd1e78816ee4c80877b67d786f430ae", "8d8a779bf1ee4fcd83597d31235bc0b3", "58c864f40c204de8972a829f1729581b", "7a24372fd1e24730916bd9eeb6ed7f9b", "75860f7b7f494dac9867e4aedcaca9d6", "8ac102ec74754fc2a9c4f8fc771335a6", "4b5335731f124774a93704b615c27a8b", "f59ac8c7be464d00a96775ad3145bbc9", "d014da1e7eb4421997f674cabf49debb", "8320fb213c1045b6922968638a8c958a", "ff64e6a1ea2d4fa7801e867b5572b205", "59595d60c3ae4aafbd4803535233d857", "5d05a0a67e224406b83ec1d51cc64b48"]}, "id": "qHMWc9t79LHt", "outputId": "bb956ea0-80e6-46e3-e243-764169e2a0ef"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["VBox(children=(HTML(value='<center> <img\\nsrc=https://huggingface.co/front/assets/huggingface_logo-noborder.sv…"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "e0855a93f7b6468e8bff6162436bef3f"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["from transformers import AutoTokenizer, AutoModelForCausalLM\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(\"google/gemma-2b-it\")"], "metadata": {"id": "jxsf6vWtctkV", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "4d493502-1cf4-4eb7-d196-1475e8c00066"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/huggingface_hub/utils/_token.py:89: UserWarning: \n", "The secret `HF_TOKEN` does not exist in your Colab secrets.\n", "To authenticate with the Hugging Face Hub, create a token in your settings tab (https://huggingface.co/settings/tokens), set it as secret in your Google Colab and restart your session.\n", "You will be able to reuse this secret in all of your notebooks.\n", "Please note that authentication is recommended but still optional to access public models or datasets.\n", "  warnings.warn(\n"]}]}, {"cell_type": "code", "source": ["# CPU Enabled uncomment below 👇🏽\n", "# model = AutoModelForCausalLM.from_pretrained(\"google/gemma-2b-it\")\n", "# GPU Enabled use below 👇🏽\n", "model = AutoModelForCausalLM.from_pretrained(\"google/gemma-2b-it\", device_map=\"auto\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 297, "referenced_widgets": ["6eb1d3d3c8bf4d56aef808c6b3bd89f4", "4d67ea2973114972b98485402f5d8d6f", "9790f948c9b449cbb0036eb94d2e41fa", "3757925570654ae896bac66ba60b94b6", "51182936959e4e679a462de9d0d3ef1b", "b6b89009090d43f2836ffd4077b376c2", "4a555f28bedd421f85dcdb7d6279d63f", "c88acc50c0cc48b1b2fc376985d8478c", "61a6055a422b4a8ca005ea0a2fd82dbf", "47c1f461a56d4cf6af1d7c881c06cab1", "05332eff981c4ad7b4a02436c80be3b8", "20ff1e88a1f54123bb3b33b08ea9a1c8", "10266043f9114d28bf848eac2005ccf4", "e78f13c26b60442186db553226c4d5ec", "15ff1ac2e12841b3804a6aa2ad96aab4", "76ca9667b6804f52aaefd2d11a8e059d", "31994a6f7a7e4a8d940ac51dfaa6ac45", "06b057d181fe4a349af7f4b92f5ce0dd", "26be190591434d04a7bec1bc1f669686", "95cb93b87a6c434091f59fa6de57d8e7", "fb9928d287f84853b07e3643166b6442", "5c36f59e886e4b919dd7e7af973d1eb8", "8e8dcf66c9c44bbbbb4997d4710e8287", "64773f9cc5df4855ae14688300fe3aa5", "d12532307781486ca7a03ebd01cd241a", "a153892754a1482291103a58c2ddd520", "fa1da6680d3e48b2a10c0cc2eb4cdb3d", "3ce876dc96aa42008a5514a247b503e5", "84be888f3d1b4cb8a11e9ce3330b2e2b", "3e811c162f784869ae11b4470b052ff1", "b4e3edc3a31e42b7ac9c45905dc30d2b", "437086a01d864a07a6bb5017987a0047", "3c48bcee76224da380251c413e19225f", "fef483e4d45241d39d9938194e789905", "0605e8e7d28f404da608da824675415a", "f97adde44a95445ebd39e8bfeb171ff4", "76683333237f44a8add1f6a9a8196128", "a13ef5b831404c54b5f061907b00fdbc", "5e080a4acd6e4ea3a5a45ae4cc459012", "0bca73a9861d44e394bc8993da52989d", "ce25d50ec03f4c179c5f9020d0abcc87", "87ad808e10414608af47a44cd62c84d1", "6ae5acfd6b9e4b81a0359d0dd22466de", "2d61f7f1689d43628229b454efa34bf7", "d88becf303a748369a5b314614443116", "3ef221c8c6924055aa8ef7e4905db17d", "4571a28e55e44fd494a401300b0ddab8", "12e44a765871464b833908837d718083", "8ceb43ad70944eb698aaa5fb99ff1110", "ccd6fb4130734b428f14f97862d7803a", "85fe3cd737c442df9712fad106a9cbbe", "56ccff9ccdb7450881d75a8bea8f22b3", "65f34680ba8d4e03b0abde0cbc847b56", "abf6d58d80214433a4fe2de42a557ec8", "883eb8629e8b45cf9237c5ccc56a2d2b", "b433221312b442e195b449bba090b1fa", "b5e5b04111d64a4688ed7d41d8aead68", "f081afd78e2841b0a4ad33f1b0cf9e03", "9eebaad76ccd410eb06428a44e1b5f06", "c52d07ee754d4c43b8936feffcba32a1", "ce96d0ff79d54332b5042400e0db6964", "1e49eb479e04475a91411cb24a709717", "1869e671ca7848b1a4fb2649d85850c0", "458eed408e79435e8c607689e50723fc", "c2ae724cfccf4bacb1d96cec613b7f7d", "7fda043ec959475d93f515416bd5655f", "9d747b7ad7034e80a8b04ce2c0057f64", "076c9b718192470caaab601e6e7680eb", "7245344850be44fdb324b71c66f6ebb5", "4b10b4b6f7154952ba758630bbc1d77a", "78fafeca67ca413eb576882d54dc9836", "8708554f42ed441c94f72427ffd44f77", "cbc5116f87d141359c800ac8fbae0bed", "a40680ddcdda442d8a14247b3699d7c8", "5ea7d095ef744c4a9c5b0139345ea3c3", "1f7ab5f7f853469885e7f9ae2088220b", "e620b034ee35425d970148c5e8f64d89"]}, "id": "NMAP5qXfaSTU", "outputId": "822164c8-cbd9-48d2-ed96-5de1ba1273b1"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/627 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "6eb1d3d3c8bf4d56aef808c6b3bd89f4"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors.index.json:   0%|          | 0.00/13.5k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "20ff1e88a1f54123bb3b33b08ea9a1c8"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading shards:   0%|          | 0/2 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "8e8dcf66c9c44bbbbb4997d4710e8287"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model-00001-of-00002.safetensors:   0%|          | 0.00/4.95G [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "fef483e4d45241d39d9938194e789905"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model-00002-of-00002.safetensors:   0%|          | 0.00/67.1M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "d88becf303a748369a5b314614443116"}}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["<PERSON>'s activation function should be approximate GeLU and not exact GeLU.\n", "Changing the activation function to `gelu_pytorch_tanh`.if you want to use the legacy `gelu`, edit the `model.config` to set `hidden_activation=gelu`   instead of `hidden_act`. See https://github.com/huggingface/transformers/pull/29402 for more details.\n"]}, {"output_type": "display_data", "data": {"text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "b433221312b442e195b449bba090b1fa"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["generation_config.json:   0%|          | 0.00/137 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "9d747b7ad7034e80a8b04ce2c0057f64"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["# Moving tensors to GPU\n", "input_ids = tokenizer(combined_information, return_tensors=\"pt\").to(\"cuda\")"], "metadata": {"id": "cgaBBvZ3coGx"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["response = model.generate(**input_ids, max_new_tokens=500)"], "metadata": {"id": "SXoUYd1Ycprt"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(tokenizer.decode(response[0]))"], "metadata": {"id": "Kmjpg_yFcTlq"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["#https://python.langchain.com/docs/integrations/retrievers/weaviate-hybrid/\n", "\n", "\n", "https://towardsdatascience.com/improving-retrieval-performance-in-rag-pipelines-with-hybrid-search-c75203c2f2f5\n", "https://esteininger.medium.com/mongodb-and-pinecone-building-real-time-ai-applications-cd8e0482a3c7"], "metadata": {"id": "e3VU1q0ugQ-u"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# you are supposed to solve these two thing(hybrid search,combination of db(pinecone+mongodb)) you can send me this notebook"], "metadata": {"id": "vZpiaKKYGCgm"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# i will upload these notebook in resource section with your name"], "metadata": {"id": "JRH4rYUcGXKA"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# i will create one video which will be dedicated to that best solution and i will do linkedin post from my linkedin account and i wll mention that person as well."], "metadata": {"id": "lkV_aeoCGeAs"}, "execution_count": null, "outputs": []}]}