{"docstore/metadata": {"f82f40b2-29b4-4536-b1a3-9f272306d5cd": {"doc_hash": "233d2f3b87af08e48b322323d0b7ce130fce41f511ab2b93f31d7733e0583293"}, "488d9176-adb9-4aa4-be31-c79adbf45c9a": {"doc_hash": "a5227e9280f8e2078c1cceaf200c082abd32260a6523109cc76bce7fc6080dc7", "ref_doc_id": "f82f40b2-29b4-4536-b1a3-9f272306d5cd"}, "3df58e2a-eb97-4ce7-a7b8-8504521e12ef": {"doc_hash": "d12e001404d5dcf101b2b06cde42dfb3e030d2fe10873160bf87649abe0beb35", "ref_doc_id": "f82f40b2-29b4-4536-b1a3-9f272306d5cd"}, "cf355bc0-79e1-4c78-8f7e-e63738ba59f4": {"doc_hash": "e768326d3ad52040c74db90bfa292ec09ac418a3337534e16021fac1892db575", "ref_doc_id": "f82f40b2-29b4-4536-b1a3-9f272306d5cd"}, "2f38847c-3c65-41ae-84d8-b2bb890ae9ec": {"doc_hash": "12c67cbd452c9ff6c9f6523f769e458d0bb4686972a6bdac215aacc695bd8a52", "ref_doc_id": "f82f40b2-29b4-4536-b1a3-9f272306d5cd"}, "42325499-9ed6-4beb-87e5-ab16ddcfd70f": {"doc_hash": "d7f042335366cca2a16135b60ffcd39f6bbbcf8e349addb67afbf2116c49aea7", "ref_doc_id": "f82f40b2-29b4-4536-b1a3-9f272306d5cd"}, "bd38690f-c8a3-40b2-a999-f233f0c228e1": {"doc_hash": "7def49f1ecad1ffd85d5088aa9522447851647e8ff03a7af5ef5fb8bf19c8f26", "ref_doc_id": "f82f40b2-29b4-4536-b1a3-9f272306d5cd"}}, "docstore/data": {"488d9176-adb9-4aa4-be31-c79adbf45c9a": {"__data__": {"id_": "488d9176-adb9-4aa4-be31-c79adbf45c9a", "embedding": null, "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "f82f40b2-29b4-4536-b1a3-9f272306d5cd", "node_type": "4", "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}, "hash": "233d2f3b87af08e48b322323d0b7ce130fce41f511ab2b93f31d7733e0583293", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "3df58e2a-eb97-4ce7-a7b8-8504521e12ef", "node_type": "1", "metadata": {}, "hash": "b5bb6713ef0cad990c8e4fb8ef958b6362f13d1f8efc7c5ef6ca2d0745aaa8db", "class_name": "RelatedNodeInfo"}}, "text": "What is machine learning?\nMachine learning is a branch of artificial intelligence (AI) and computer science which\nfocuses on the use of data and algorithms to imitate the way that humans learn,\ngradually improving its accuracy.\nIBM has a rich history with machine learning. One of its own, <PERSON>, is credited\nfor coining the term, “machine learning” with his research (link resides outside ibm.com)\naround the game of checkers. <PERSON>, the self-proclaimed checkers master,\nplayed the game on an IBM 7094 computer in 1962, and he lost to the computer.\nCompared to what can be done today, this feat seems trivial, but it’s considered a major\nmilestone in the field of artificial intelligence.\nOver the last couple of decades, the technological advances in storage and processing\npower have enabled some innovative products based on machine learning, such as\nNetflix’s recommendation engine and self-driving cars.\nMachine learning is an important component of the growing field of data science.\nThrough the use of statistical methods, algorithms are trained to make classifications or\npredictions, and to uncover key insights in data mining projects. These insights\nsubsequently drive decision making within applications and businesses, ideally\nimpacting key growth metrics. As big data continues to expand and grow, the market\ndemand for new data scientists will increase. They will be required to help identify the\nmost relevant business questions and the data to answer them.\nMachine learning algorithms are typically created using frameworks such as Python that\naccelerate solution development by using platforms like TensorFlow or PyTorch.\nNow available: watsonx.ai\nThe all-new enterprise studio that brings together traditional machine learning along\nwith new generative AI capabilities powered by foundation models.\nTry watsonx.ai\nBegin your journey to AI\nLearn how to scale AI\nExplore the AI Academy\nMachine Learning vs. Deep Learning vs. Neural Networks\nSince deep learning and machine learning tend to be used interchangeably, it’s worth\nnoting the nuances between the two. Machine learning, deep learning, and neural\nnetworks are all sub-fields of artificial intelligence. However, neural networks is actually\na sub-field of machine learning, and deep learning is a sub-field of neural networks.\nThe way in which deep learning and machine learning differ is in how each algorithm\nlearns. \"Deep\" machine learning can use labeled datasets, also known as supervised\nlearning, to inform its algorithm, but it doesn’t necessarily require a labeled dataset. The\ndeep learning process can ingest unstructured data in its raw form (e.g., text or images),\nand it can automatically determine the set of features which distinguish different\ncategories of data from one another. This eliminates some of the human intervention\nrequired and enables the use of large amounts of data. You can think of deep learning\nas \"scalable machine learning\" as Lex Fridman notes in this MIT lecture (link resides\noutside ibm.com).\nClassical, or \"non-deep,\" machine learning is more dependent on human intervention to\nlearn. Human experts determine the set of features to understand the differences\nbetween data inputs, usually requiring more structured data to learn.\nNeural networks, or artificial neural networks (ANNs), are comprised of node layers,\ncontaining an input layer, one or more hidden layers, and an output layer. Each node, or\nartificial neuron, connects to another and has an associated weight and threshold. If the\noutput of any individual node is above the specified threshold value, that node is\nactivated, sending data to the next layer of the network. Otherwise, no data is passed\nalong to the next layer of the network by that node.", "start_char_idx": 0, "end_char_idx": 3750, "text_template": "{metadata_str}\n\n{content}", "metadata_template": "{key}: {value}", "metadata_seperator": "\n", "class_name": "TextNode"}, "__type__": "1"}, "3df58e2a-eb97-4ce7-a7b8-8504521e12ef": {"__data__": {"id_": "3df58e2a-eb97-4ce7-a7b8-8504521e12ef", "embedding": null, "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "f82f40b2-29b4-4536-b1a3-9f272306d5cd", "node_type": "4", "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}, "hash": "233d2f3b87af08e48b322323d0b7ce130fce41f511ab2b93f31d7733e0583293", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "488d9176-adb9-4aa4-be31-c79adbf45c9a", "node_type": "1", "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}, "hash": "a5227e9280f8e2078c1cceaf200c082abd32260a6523109cc76bce7fc6080dc7", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "cf355bc0-79e1-4c78-8f7e-e63738ba59f4", "node_type": "1", "metadata": {}, "hash": "7a55aae2f58a0465d55081eaddd06bb92fb3060cab95884bcd232cfc95f624c7", "class_name": "RelatedNodeInfo"}}, "text": "Otherwise, no data is passed\nalong to the next layer of the network by that node. The “deep” in deep learning is just\nreferring to the number of layers in a neural network. A neural network that consists of\nmore than three layers—which would be inclusive of the input and the output—can be\nconsidered a deep learning algorithm or a deep neural network. A neural network that\nonly has three layers is just a basic neural network.\nDeep learning and neural networks are credited with accelerating progress in areas\nsuch as computer vision, natural language processing, and speech recognition.\nSee the blog post “AI vs. Machine Learning vs. Deep Learning vs. Neural Networks:\nWhat’s the Difference?” for a closer look at how the different concepts relate.\nRelated content\nExplore the watsonx.ai interactive demo\nDownload “Machine learning for Dummies”\n- This link downloads a pdf\nExplore Gen AI for developers\nHow does machine learning work?\nUC Berkeley (link resides outside ibm.com) breaks out the learning system of a\nmachine learning algorithm into three main parts.\nA Decision Process: In general, machine learning algorithms are used to make a\nprediction or classification. Based on some input data, which can be labeled or\nunlabeled, your algorithm will produce an estimate about a pattern in the data.\nAn Error Function: An error function evaluates the prediction of the model. If\nthere are known examples, an error function can make a comparison to assess\nthe accuracy of the model.\nA Model Optimization Process: If the model can fit better to the data points in the\ntraining set, then weights are adjusted to reduce the discrepancy between the\nknown example and the model estimate. The algorithm will repeat this iterative\n“evaluate and optimize” process, updating weights autonomously until a\nthreshold of accuracy has been met.\nMachine learning methods\nMachine learning models fall into three primary categories.\nSupervised machine learning\nSupervised learning, also known as supervised machine learning, is defined by its use\nof labeled datasets to train algorithms to classify data or predict outcomes accurately.\nAs input data is fed into the model, the model adjusts its weights until it has been fitted\nappropriately. This occurs as part of the cross validation process to ensure that the\nmodel avoids overfitting or underfitting. Supervised learning helps organizations solve a\nvariety of real-world problems at scale, such as classifying spam in a separate folder\nfrom your inbox. Some methods used in supervised learning include neural networks,\nnaïve bayes, linear regression, logistic regression, random forest, and support vector\nmachine (SVM).\nUnsupervised machine learning\nUnsupervised learning, also known as unsupervised machine learning, uses machine\nlearning algorithms to analyze and cluster unlabeled datasets (subsets called clusters).\nThese algorithms discover hidden patterns or data groupings without the need for\nhuman intervention. This method’s ability to discover similarities and differences in\ninformation make it ideal for exploratory data analysis, cross-selling strategies,\ncustomer segmentation, and image and pattern recognition. It’s also used to reduce the\nnumber of features in a model through the process of dimensionality reduction. Principal\ncomponent analysis (PCA) and singular value decomposition (SVD) are two common\napproaches for this. Other algorithms used in unsupervised learning include neural\nnetworks, k-means clustering, and probabilistic clustering methods.\nSemi-supervised learning\nSemi-supervised learning offers a happy medium between supervised and\nunsupervised learning. During training, it uses a smaller labeled data set to guide\nclassification and feature extraction from a larger, unlabeled data set. Semi-supervised\nlearning can solve the problem of not having enough labeled data for a supervised\nlearning algorithm.", "start_char_idx": 3669, "end_char_idx": 7558, "text_template": "{metadata_str}\n\n{content}", "metadata_template": "{key}: {value}", "metadata_seperator": "\n", "class_name": "TextNode"}, "__type__": "1"}, "cf355bc0-79e1-4c78-8f7e-e63738ba59f4": {"__data__": {"id_": "cf355bc0-79e1-4c78-8f7e-e63738ba59f4", "embedding": null, "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "f82f40b2-29b4-4536-b1a3-9f272306d5cd", "node_type": "4", "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}, "hash": "233d2f3b87af08e48b322323d0b7ce130fce41f511ab2b93f31d7733e0583293", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "3df58e2a-eb97-4ce7-a7b8-8504521e12ef", "node_type": "1", "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}, "hash": "d12e001404d5dcf101b2b06cde42dfb3e030d2fe10873160bf87649abe0beb35", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "2f38847c-3c65-41ae-84d8-b2bb890ae9ec", "node_type": "1", "metadata": {}, "hash": "98bd0c2ea89796e2a164b7e6a49d4a22646993923c40216341d6ff153d82f5f0", "class_name": "RelatedNodeInfo"}}, "text": "It also helps if it’s too costly to label enough data.\nFor a deep dive into the differences between these approaches, check out \"Supervised\nvs. Unsupervised Learning: What's the Difference?\"\nReinforcement machine learning\nReinforcement machine learning is a machine learning model that is similar to\nsupervised learning, but the algorithm isn’t trained using sample data. This model learns\nas it goes by using trial and error. A sequence of successful outcomes will be reinforced\nto develop the best recommendation or policy for a given problem.\nThe IBM Watson® system that won the Jeopardy! challenge in 2011 is a good example.\nThe system used reinforcement learning to learn when to attempt an answer (or\nquestion, as it were), which square to select on the board, and how much to\nwager—especially on daily doubles.\nLearn more about reinforcement learning\nCommon machine learning algorithms\nA number of machine learning algorithms are commonly used. These include:\nNeural networks: Neural networks simulate the way the human brain works, with\na huge number of linked processing nodes. Neural networks are good at\nrecognizing patterns and play an important role in applications including natural\nlanguage translation, image recognition, speech recognition, and image creation.\nLinear regression: This algorithm is used to predict numerical values, based on a\nlinear relationship between different values. For example, the technique could be\nused to predict house prices based on historical data for the area.\nLogistic regression: This supervised learning algorithm makes predictions for\ncategorical response variables, such as “yes/no” answers to questions. It can be\nused for applications such as classifying spam and quality control on a\nproduction line.\nClustering: Using unsupervised learning, clustering algorithms can identify\npatterns in data so that it can be grouped. Computers can help data scientists by\nidentifying differences between data items that humans have overlooked.\nDecision trees: Decision trees can be used for both predicting numerical values\n(regression) and classifying data into categories. Decision trees use a branching\nsequence of linked decisions that can be represented with a tree diagram. One of\nthe advantages of decision trees is that they are easy to validate and audit,\nunlike the black box of the neural network.\nRandom forests: In a random forest, the machine learning algorithm predicts a\nvalue or category by combining the results from a number of decision trees.\nAdvantages and disadvantages of machine learning algorithms\nDepending on your budget, need for speed and precision required, each algorithm\ntype—supervised, unsupervised, semi-supervised, or reinforcement—has its own\nadvantages and disadvantages. For example, decision tree algorithms are used for both\npredicting numerical values (regression problems) and classifying data into categories.\nDecision trees use a branching sequence of linked decisions that may be represented\nwith a tree diagram. A prime advantage of decision trees is that they are easier to\nvalidate and audit than a neural network. The bad news is that they can be more\nunstable than other decision predictors.\nOverall, there are many advantages to machine learning that businesses can leverage\nfor new efficiencies. These include machine learning identifying patterns and trends in\nmassive volumes of data that humans might not spot at all. And this analysis requires\nlittle human intervention: just feed in the dataset of interest and let the machine learning\nsystem assemble and refine its own algorithms—which will continually improve with\nmore data input over time. Customers and users can enjoy a more personalized\nexperience as the model learns more with every experience with that person.\nOn the downside, machine learning requires large training datasets that are accurate\nand unbiased. GIGO is the operative factor: garbage in / garbage out.", "start_char_idx": 7559, "end_char_idx": 11486, "text_template": "{metadata_str}\n\n{content}", "metadata_template": "{key}: {value}", "metadata_seperator": "\n", "class_name": "TextNode"}, "__type__": "1"}, "2f38847c-3c65-41ae-84d8-b2bb890ae9ec": {"__data__": {"id_": "2f38847c-3c65-41ae-84d8-b2bb890ae9ec", "embedding": null, "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "f82f40b2-29b4-4536-b1a3-9f272306d5cd", "node_type": "4", "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}, "hash": "233d2f3b87af08e48b322323d0b7ce130fce41f511ab2b93f31d7733e0583293", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "cf355bc0-79e1-4c78-8f7e-e63738ba59f4", "node_type": "1", "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}, "hash": "e768326d3ad52040c74db90bfa292ec09ac418a3337534e16021fac1892db575", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "42325499-9ed6-4beb-87e5-ab16ddcfd70f", "node_type": "1", "metadata": {}, "hash": "1cab36cf26e435222503b8fdc466f00ea23e4c859f0b0d136eb6431cbf925bf8", "class_name": "RelatedNodeInfo"}}, "text": "GIGO is the operative factor: garbage in / garbage out. Gathering\nsufficient data and having a system robust enough to run it might also be a drain on\nresources. Machine learning can also be prone to error, depending on the input. With\ntoo small a sample, the system could produce a perfectly logical algorithm that is\ncompletely wrong or misleading. To avoid wasting budget or displeasing customers,\norganizations should act on the answers only when there is high confidence in the\noutput.\nReal-world machine learning use cases\nHere are just a few examples of machine learning you might encounter every day:\nSpeech recognition: It is also known as automatic speech recognition (ASR), computer\nspeech recognition, or speech-to-text, and it is a capability which uses natural language\nprocessing (NLP) to translate human speech into a written format. Many mobile devices\nincorporate speech recognition into their systems to conduct voice search—e.g. Siri—or\nimprove accessibility for texting.\nCustomer service: Online chatbots are replacing human agents along the customer\njourney, changing the way we think about customer engagement across websites and\nsocial media platforms. Chatbots answer frequently asked questions (FAQs) about\ntopics such as shipping, or provide personalized advice, cross-selling products or\nsuggesting sizes for users. Examples include virtual agents on e-commerce sites;\nmessaging bots, using Slack and Facebook Messenger; and tasks usually done by\nvirtual assistants and voice assistants.\nComputer vision: This AI technology enables computers to derive meaningful\ninformation from digital images, videos, and other visual inputs, and then take the\nappropriate action. Powered by convolutional neural networks, computer vision has\napplications in photo tagging on social media, radiology imaging in healthcare, and\nself-driving cars in the automotive industry.\nRecommendation engines: Using past consumption behavior data, AI algorithms can\nhelp to discover data trends that can be used to develop more effective cross-selling\nstrategies. Recommendation engines are used by online retailers to make relevant\nproduct recommendations to customers during the checkout process.\nRobotic process automation (RPA): Also known as software robotics, RPA uses\nintelligent automation technologies to perform repetitive manual tasks.\nAutomated stock trading: Designed to optimize stock portfolios, AI-driven\nhigh-frequency trading platforms make thousands or even millions of trades per day\nwithout human intervention.\nFraud detection: Banks and other financial institutions can use machine learning to spot\nsuspicious transactions. Supervised learning can train a model using information about\nknown fraudulent transactions. Anomaly detection can identify transactions that look\natypical and deserve further investigation.\nChallenges of machine learning\nAs machine learning technology has developed, it has certainly made our lives easier.\nHowever, implementing machine learning in businesses has also raised a number of\nethical concerns about AI technologies. Some of these include:\nTechnological singularity\nWhile this topic garners a lot of public attention, many researchers are not concerned\nwith the idea of AI surpassing human intelligence in the near future. Technological\nsingularity is also referred to as strong AI or superintelligence. Philosopher Nick\nBostrum defines superintelligence as “any intellect that vastly outperforms the best\nhuman brains in practically every field, including scientific creativity, general wisdom,\nand social skills.” Despite the fact that superintelligence is not imminent in society, the\nidea of it raises some interesting questions as we consider the use of autonomous\nsystems, like self-driving cars. It’s unrealistic to think that a driverless car would never\nhave an accident, but who is responsible and liable under those circumstances? Should\nwe still develop autonomous vehicles, or do we limit this technology to\nsemi-autonomous vehicles which help people drive safely?", "start_char_idx": 11431, "end_char_idx": 15467, "text_template": "{metadata_str}\n\n{content}", "metadata_template": "{key}: {value}", "metadata_seperator": "\n", "class_name": "TextNode"}, "__type__": "1"}, "42325499-9ed6-4beb-87e5-ab16ddcfd70f": {"__data__": {"id_": "42325499-9ed6-4beb-87e5-ab16ddcfd70f", "embedding": null, "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "f82f40b2-29b4-4536-b1a3-9f272306d5cd", "node_type": "4", "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}, "hash": "233d2f3b87af08e48b322323d0b7ce130fce41f511ab2b93f31d7733e0583293", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "2f38847c-3c65-41ae-84d8-b2bb890ae9ec", "node_type": "1", "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}, "hash": "12c67cbd452c9ff6c9f6523f769e458d0bb4686972a6bdac215aacc695bd8a52", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "bd38690f-c8a3-40b2-a999-f233f0c228e1", "node_type": "1", "metadata": {}, "hash": "245f82c83f6c9ab4743d5c35b0d85af2c0d153e11ad7aff32569e35fc4e656bd", "class_name": "RelatedNodeInfo"}}, "text": "The jury is still out on this,\nbut these are the types of ethical debates that are occurring as new, innovative AI\ntechnology develops.\nAI impact on jobs\nWhile a lot of public perception of artificial intelligence centers around job losses, this\nconcern should probably be reframed. With every disruptive, new technology, we see\nthat the market demand for specific job roles shifts. For example, when we look at the\nautomotive industry, many manufacturers, like GM, are shifting to focus on electric\nvehicle production to align with green initiatives. The energy industry isn’t going away,\nbut the source of energy is shifting from a fuel economy to an electric one.\nIn a similar way, artificial intelligence will shift the demand for jobs to other areas. There\nwill need to be individuals to help manage AI systems. There will still need to be people\nto address more complex problems within the industries that are most likely to be\naffected by job demand shifts, such as customer service. The biggest challenge with\nartificial intelligence and its effect on the job market will be helping people to transition\nto new roles that are in demand.\nPrivacy\nPrivacy tends to be discussed in the context of data privacy, data protection, and data\nsecurity. These concerns have allowed policymakers to make more strides in recent\nyears. For example, in 2016, GDPR legislation was created to protect the personal data\nof people in the European Union and European Economic Area, giving individuals more\ncontrol of their data. In the United States, individual states are developing policies, such\nas the California Consumer Privacy Act (CCPA), which was introduced in 2018 and\nrequires businesses to inform consumers about the collection of their data. Legislation\nsuch as this has forced companies to rethink how they store and use personally\nidentifiable information (PII). As a result, investments in security have become an\nincreasing priority for businesses as they seek to eliminate any vulnerabilities and\nopportunities for surveillance, hacking, and cyberattacks.\nBias and discrimination\nInstances of bias and discrimination across a number of machine learning systems have\nraised many ethical questions regarding the use of artificial intelligence. How can we\nsafeguard against bias and discrimination when the training data itself may be\ngenerated by biased human processes? While companies typically have good\nintentions for their automation efforts, Reuters (link resides outside ibm.com) highlights\nsome of the unforeseen consequences of incorporating AI into hiring practices. In their\neffort to automate and simplify a process, Amazon unintentionally discriminated against\njob candidates by gender for technical roles, and the company ultimately had to scrap\nthe project. Harvard Business Review (link resides outside ibm.com) has raised other\npointed questions about the use of AI in hiring practices, such as what data you should\nbe able to use when evaluating a candidate for a role.\nBias and discrimination aren’t limited to the human resources function either; they can\nbe found in a number of applications from facial recognition software to social media\nalgorithms.\nAs businesses become more aware of the risks with AI, they’ve also become more\nactive in this discussion around AI ethics and values. For example, IBM has sunset its\ngeneral purpose facial recognition and analysis products. IBM CEO Arvind Krishna\nwrote: “IBM firmly opposes and will not condone uses of any technology, including facial\nrecognition technology offered by other vendors, for mass surveillance, racial profiling,\nviolations of basic human rights and freedoms, or any purpose which is not consistent\nwith our values and Principles of Trust and Transparency.”\nAccountability\nSince there isn’t significant legislation to regulate AI practices, there is no real\nenforcement mechanism to ensure that ethical AI is practiced.", "start_char_idx": 15468, "end_char_idx": 19378, "text_template": "{metadata_str}\n\n{content}", "metadata_template": "{key}: {value}", "metadata_seperator": "\n", "class_name": "TextNode"}, "__type__": "1"}, "bd38690f-c8a3-40b2-a999-f233f0c228e1": {"__data__": {"id_": "bd38690f-c8a3-40b2-a999-f233f0c228e1", "embedding": null, "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "f82f40b2-29b4-4536-b1a3-9f272306d5cd", "node_type": "4", "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}, "hash": "233d2f3b87af08e48b322323d0b7ce130fce41f511ab2b93f31d7733e0583293", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "42325499-9ed6-4beb-87e5-ab16ddcfd70f", "node_type": "1", "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}, "hash": "d7f042335366cca2a16135b60ffcd39f6bbbcf8e349addb67afbf2116c49aea7", "class_name": "RelatedNodeInfo"}}, "text": "The current incentives for\ncompanies to be ethical are the negative repercussions of an unethical AI system on the\nbottom line. To fill the gap, ethical frameworks have emerged as part of a collaboration\nbetween ethicists and researchers to govern the construction and distribution of AI\nmodels within society. However, at the moment, these only serve to guide. Some\nresearch (link resides outside ibm.com) shows that the combination of distributed\nresponsibility and a lack of foresight into potential consequences aren’t conducive to\npreventing harm to society.\nRead more about IBM's position on AI Ethics\nHow to choose the right AI platform for machine learning\nSelecting a platform can be a challenging process, as the wrong system can drive up\ncosts, or limit the use of other valuable tools or technologies. When reviewing multiple\nvendors to select an AI platform, there is often a tendency to think that more features =\na better system. Maybe so, but reviewers should start by thinking through what the AI\nplatform will be doing for their organization. What machine learning capabilities need to\nbe delivered and what features are important to accomplish them? One missing feature\nmight doom the usefulness of an entire system. Here are some features to consider.\nMLOps capabilities. Does the system have:\na unified interface for ease of management?\nautomated machine learning tools for faster model creation with low-code\nand no-code functionality?\ndecision optimization to streamline the selection and deployment of\noptimization models?\nvisual modeling to combine visual data science with open-source libraries\nand notebook-based interfaces on a unified data and AI studio?\nautomated development for beginners to get started quickly and more\nadvanced data scientists to experiment?\nsynthetic data generator as an alternative or supplement to real-world data\nwhen real-world data is not readily available?\nGenerative AI capabilities. Does the system have:\na content generator that can generate text, images and other content\nbased on the data it was trained on?\nautomated classification to read and classify written input, such as\nevaluating and sorting customer complaints or reviewing customer\nfeedback sentiment?\na summary generator that can transform dense text into a high-quality\nsummary, capture key points from financial reports, and generate meeting\ntranscriptions?\na data extraction capability to sort through complex details and quickly pull\nthe necessary information from large documents?", "start_char_idx": 19379, "end_char_idx": 21888, "text_template": "{metadata_str}\n\n{content}", "metadata_template": "{key}: {value}", "metadata_seperator": "\n", "class_name": "TextNode"}, "__type__": "1"}}, "docstore/ref_doc_info": {"f82f40b2-29b4-4536-b1a3-9f272306d5cd": {"node_ids": ["488d9176-adb9-4aa4-be31-c79adbf45c9a", "3df58e2a-eb97-4ce7-a7b8-8504521e12ef", "cf355bc0-79e1-4c78-8f7e-e63738ba59f4", "2f38847c-3c65-41ae-84d8-b2bb890ae9ec", "42325499-9ed6-4beb-87e5-ab16ddcfd70f", "bd38690f-c8a3-40b2-a999-f233f0c228e1"], "metadata": {"file_path": "Data\\MLDOC.txt", "file_name": "MLDOC.txt", "file_type": "text/plain", "file_size": 22273, "creation_date": "2024-02-15", "last_modified_date": "2024-02-15", "last_accessed_date": "2024-02-15"}}}}