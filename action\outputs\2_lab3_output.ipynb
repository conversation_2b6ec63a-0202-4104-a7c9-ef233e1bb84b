{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Lab 3\n", "\n", "## Let's try out 3 MCP Servers\n", "\n", "We will add new powers to our agents:  \n", "1. Memory\n", "2. Internet search\n", "3. Live market data\n", "\n", "This is where you can really appreciate the benefits of MCP: it makes it so easy to equip our agents with tools that others have developed."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from dotenv import load_dotenv\n", "from agents import Agent, Runner, trace\n", "from agents.mcp import MCPServerStdio\n", "import os\n", "from IPython.display import Markdown, display\n", "from datetime import datetime\n", "load_dotenv(override=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## First new MCP Server: a persistent knowledge-graph based memory\n", "\n", "Here's a really interesting one: a knowledge-graph based memory.\n", "\n", "It's a persistent memory store of entities, observations about them, and relationships between them.\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Tool(name='create_entities', description='Create new entities with observations and optional embeddings', inputSchema={'type': 'object', 'properties': {'entities': {'type': 'array', 'items': {'type': 'object', 'properties': {'name': {'type': 'string'}, 'entityType': {'type': 'string'}, 'observations': {'type': 'array', 'items': {'type': 'string'}}, 'embedding': {'type': 'array', 'items': {'type': 'number'}, 'description': 'Optional vector embedding for similarity search'}}, 'required': ['name', 'entityType', 'observations']}}}, 'required': ['entities']}, annotations=None),\n", " Tool(name='search_nodes', description='Search for entities and their relations using text or vector similarity', inputSchema={'type': 'object', 'properties': {'query': {'oneOf': [{'type': 'string', 'description': 'Text search query'}, {'type': 'array', 'items': {'type': 'number'}, 'description': 'Vector for similarity search'}]}}, 'required': ['query']}, annotations=None),\n", " Tool(name='read_graph', description='Get recent entities and their relations', inputSchema={'type': 'object', 'properties': {}, 'required': []}, annotations=None),\n", " Tool(name='create_relations', description='Create relations between entities', inputSchema={'type': 'object', 'properties': {'relations': {'type': 'array', 'items': {'type': 'object', 'properties': {'source': {'type': 'string'}, 'target': {'type': 'string'}, 'type': {'type': 'string'}}, 'required': ['source', 'target', 'type']}}}, 'required': ['relations']}, annotations=None),\n", " Tool(name='delete_entity', description='Delete an entity and all its associated data (observations and relations)', inputSchema={'type': 'object', 'properties': {'name': {'type': 'string', 'description': 'Name of the entity to delete'}}, 'required': ['name']}, annotations=None),\n", " Tool(name='delete_relation', description='Delete a specific relation between entities', inputSchema={'type': 'object', 'properties': {'source': {'type': 'string', 'description': 'Source entity name'}, 'target': {'type': 'string', 'description': 'Target entity name'}, 'type': {'type': 'string', 'description': 'Type of relation'}}, 'required': ['source', 'target', 'type']}, annotations=None)]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["params = {\n", "  \"command\": \"npx\",\n", "  \"args\": [\"-y\", \"mcp-memory-libsql\"],\n", "  \"env\": {\n", "\t\t\t\t\"LIBSQL_URL\": \"file:./memory/ed.db\"\n", "\t\t\t}\n", "}\n", "\n", "\n", "async with MCPServerStdio(params=params) as server:\n", "    mcp_tools = await server.list_tools()\n", "\n", "mcp_tools"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["instructions = \"You use your entity tools as a persistent memory to store and recall information about your conversations.\"\n", "request = \"My name's <PERSON>. I'm running a workshop live about AI Agents right now, \\\n", "and I'm co-presenting with the legendary presenter, <PERSON>, from the SuperDataScience podcast.\"\n", "model = \"gpt-4o-mini\""]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/markdown": ["I've noted that your name is <PERSON> and that you're running a live workshop on AI Agents, co-presenting with <PERSON> from the SuperDataScience podcast. If you need any specific information or assistance during your workshop, feel free to ask!"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["async with MCPServerStdio(params=params) as mcp_server:\n", "    agent = Agent(name=\"agent\", instructions=instructions, model=model, mcp_servers=[mcp_server])\n", "    with trace(\"conversation\"):\n", "        result = await Runner.run(agent, request)\n", "    display(Markdown(result.final_output))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/markdown": ["I know that your name is <PERSON>. You've been involved in running a workshop live about AI Agents and co-presenting with <PERSON> from the SuperDataScience podcast. If there's anything else you'd like to share or discuss, feel free!"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["question = \"My name's <PERSON>. What do you know about me?\"\n", "\n", "async with MCPServerStdio(params=params) as mcp_server:\n", "    agent = Agent(name=\"agent\", instructions=instructions, model=model, mcp_servers=[mcp_server])\n", "    with trace(\"conversation\"):\n", "        result = await Runner.run(agent, question)\n", "    display(Markdown(result.final_output))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Check the trace:\n", "\n", "https://platform.openai.com/traces"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Second new MCP Server: Brave Search\n", "\n", "\n", "\n", "https://brave.com/search/api/\n", "\n", "Set up your account, and put your key in the .env under `BRAVE_API_KEY`"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["env = {\"BRAVE_API_KEY\": os.getenv(\"BRAVE_API_KEY\")}\n", "params = {\"command\": \"npx\", \"args\": [\"-y\", \"@modelcontextprotocol/server-brave-search\"], \"env\": env}\n", "\n", "async with MCPServerStdio(params=params) as server:\n", "    mcp_tools = await server.list_tools()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Tool(name='brave_web_search', description='Performs a web search using the Brave Search API, ideal for general queries, news, articles, and online content. Use this for broad information gathering, recent events, or when you need diverse web sources. Supports pagination, content filtering, and freshness controls. Maximum 20 results per request, with offset for pagination. ', inputSchema={'type': 'object', 'properties': {'query': {'type': 'string', 'description': 'Search query (max 400 chars, 50 words)'}, 'count': {'type': 'number', 'description': 'Number of results (1-20, default 10)', 'default': 10}, 'offset': {'type': 'number', 'description': 'Pagination offset (max 9, default 0)', 'default': 0}}, 'required': ['query']}, annotations=None),\n", " Tool(name='brave_local_search', description=\"Searches for local businesses and places using Brave's Local Search API. Best for queries related to physical locations, businesses, restaurants, services, etc. Returns detailed information including:\\n- Business names and addresses\\n- Ratings and review counts\\n- Phone numbers and opening hours\\nUse this when the query implies 'near me' or mentions specific locations. Automatically falls back to web search if no local results are found.\", inputSchema={'type': 'object', 'properties': {'query': {'type': 'string', 'description': \"Local search query (e.g. 'pizza near Central Park')\"}, 'count': {'type': 'number', 'description': 'Number of results (1-20, default 5)', 'default': 5}}, 'required': ['query']}, annotations=None)]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["mcp_tools"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["instructions = \"You are able to search the web for information and briefly summarize the takeaways.\"\n", "request = f\"Please research the latest news on Amazon stock price and briefly summarize its outlook. \\\n", "For context, the current date is {datetime.now().strftime('%Y-%m-%d')}\"\n", "model = \"gpt-4o-mini\""]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/markdown": ["As of May 11, 2025, here’s a summary of the latest news and outlook regarding Amazon's stock price:\n", "\n", "1. **Recent Performance**: Amazon's stock (AMZN) has faced significant challenges in 2025, currently down approximately 14.63% year-to-date. In May alone, the stock dropped about 11.43%, reflecting a rough month for investors.\n", "\n", "2. **Earnings and Indicators**: The company recently reported strong growth in its advertising division, generating $13.9 billion with a 19% year-over-year increase. Additionally, Amazon Web Services (AWS) experienced a 17% year-over-year growth. However, these positives have not yet translated into a recovery in stock price.\n", "\n", "3. **Market Sentiment**: Analysts are debating whether to buy the dip or to avoid the stock, with mixed feelings about its future. There are concerns over economic factors, including tariffs introduced by the <PERSON> administration, which may affect operational costs and margins.\n", "\n", "4. **Future Predictions**: Forecasts suggest that Amazon's stock could average around $207 in May, with potential volatility leading to a maximum price of $243 and a minimum of $178.\n", "\n", "Overall, while Amazon shows growth in key operational areas, its stock performance in 2025 has been underwhelming, causing concern among investors about its near-term outlook."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["async with MCPServerStdio(params=params) as mcp_server:\n", "    agent = Agent(name=\"agent\", instructions=instructions, model=model, mcp_servers=[mcp_server])\n", "    with trace(\"conversation\"):\n", "        result = await Runner.run(agent, request)\n", "    display(Markdown(result.final_output))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Third MCP Server: Polygon.io MCP Server"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Introducing polygon.io\n", "\n", "Polygon.io is a hugely popular financial data provider. It has a free plan and a paid plan. And it also has an MCP Server!\n", "\n", "First, read up on polygon.io on their excellent website, including looking at their pricing:\n", "\n", "https://polygon.io\n", "\n", "1. Please sign up for polygon.io (top right)  \n", "2. Once signed in, please select \"<PERSON>\" in the left hand navigation\n", "3. Press the blue \"New Key\" button\n", "4. <PERSON><PERSON> the key name\n", "5. Edit your .env file and add the row:\n", "\n", "`POLYGON_API_KEY=xxxx`\n", "\n", "This section covers the paid plan, but I've set up a free alternative too. Just use the `.env` file to control which one is used:\n", "\n", "If you do decide to have a paid plan, please add this to your .env file to indicate:\n", "\n", "`POLYGON_PLAN=paid`\n", "\n", "And if you decide to go all the way for the realtime API, then please do:\n", "\n", "`POLYGON_PLAN=realtime`"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Tool(name='get_aggs', description='\\nList aggregate bars for a ticker over a given date range in custom time window sizes.\\n', inputSchema={'properties': {'ticker': {'title': 'Ticker', 'type': 'string'}, 'multiplier': {'title': 'Multiplier', 'type': 'integer'}, 'timespan': {'title': 'Timespan', 'type': 'string'}, 'from_': {'anyOf': [{'type': 'string'}, {'type': 'integer'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}], 'title': 'From'}, 'to': {'anyOf': [{'type': 'string'}, {'type': 'integer'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}], 'title': 'To'}, 'adjusted': {'anyOf': [{'type': 'boolean'}, {'type': 'null'}], 'default': None, 'title': 'Adjusted'}, 'sort': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Sort'}, 'limit': {'anyOf': [{'type': 'integer'}, {'type': 'null'}], 'default': None, 'title': 'Limit'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['ticker', 'multiplier', 'timespan', 'from_', 'to'], 'title': 'get_aggsArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='list_aggs', description='\\nIterate through aggregate bars for a ticker over a given date range.\\n', inputSchema={'properties': {'ticker': {'title': 'Ticker', 'type': 'string'}, 'multiplier': {'title': 'Multiplier', 'type': 'integer'}, 'timespan': {'title': 'Timespan', 'type': 'string'}, 'from_': {'anyOf': [{'type': 'string'}, {'type': 'integer'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}], 'title': 'From'}, 'to': {'anyOf': [{'type': 'string'}, {'type': 'integer'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}], 'title': 'To'}, 'adjusted': {'anyOf': [{'type': 'boolean'}, {'type': 'null'}], 'default': None, 'title': 'Adjusted'}, 'sort': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Sort'}, 'limit': {'anyOf': [{'type': 'integer'}, {'type': 'null'}], 'default': None, 'title': 'Limit'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['ticker', 'multiplier', 'timespan', 'from_', 'to'], 'title': 'list_aggsArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_grouped_daily_aggs', description='\\nGet grouped daily bars for entire market for a specific date.\\n', inputSchema={'properties': {'date': {'title': 'Date', 'type': 'string'}, 'adjusted': {'anyOf': [{'type': 'boolean'}, {'type': 'null'}], 'default': None, 'title': 'Adjusted'}, 'include_otc': {'anyOf': [{'type': 'boolean'}, {'type': 'null'}], 'default': None, 'title': 'Include Otc'}, 'locale': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Locale'}, 'market_type': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Market Type'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['date'], 'title': 'get_grouped_daily_aggsArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_daily_open_close_agg', description='\\nGet daily open, close, high, and low for a specific ticker and date.\\n', inputSchema={'properties': {'ticker': {'title': 'Ticker', 'type': 'string'}, 'date': {'title': 'Date', 'type': 'string'}, 'adjusted': {'anyOf': [{'type': 'boolean'}, {'type': 'null'}], 'default': None, 'title': 'Adjusted'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['ticker', 'date'], 'title': 'get_daily_open_close_aggArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_previous_close_agg', description=\"\\nGet previous day's open, close, high, and low for a specific ticker.\\n\", inputSchema={'properties': {'ticker': {'title': 'Ticker', 'type': 'string'}, 'adjusted': {'anyOf': [{'type': 'boolean'}, {'type': 'null'}], 'default': None, 'title': 'Adjusted'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['ticker'], 'title': 'get_previous_close_aggArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='list_trades', description='\\nGet trades for a ticker symbol.\\n', inputSchema={'properties': {'ticker': {'title': 'Ticker', 'type': 'string'}, 'timestamp': {'anyOf': [{'type': 'string'}, {'type': 'integer'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Timestamp'}, 'timestamp_lt': {'anyOf': [{'type': 'string'}, {'type': 'integer'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Timestamp Lt'}, 'timestamp_lte': {'anyOf': [{'type': 'string'}, {'type': 'integer'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Timestamp Lte'}, 'timestamp_gt': {'anyOf': [{'type': 'string'}, {'type': 'integer'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Timestamp Gt'}, 'timestamp_gte': {'anyOf': [{'type': 'string'}, {'type': 'integer'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Timestamp Gte'}, 'limit': {'anyOf': [{'type': 'integer'}, {'type': 'null'}], 'default': None, 'title': 'Limit'}, 'sort': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Sort'}, 'order': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Order'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['ticker'], 'title': 'list_tradesArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_last_trade', description='\\nGet the most recent trade for a ticker symbol.\\n', inputSchema={'properties': {'ticker': {'title': 'Ticker', 'type': 'string'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['ticker'], 'title': 'get_last_tradeArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_last_crypto_trade', description='\\nGet the most recent trade for a crypto pair.\\n', inputSchema={'properties': {'from_': {'title': 'From', 'type': 'string'}, 'to': {'title': 'To', 'type': 'string'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['from_', 'to'], 'title': 'get_last_crypto_tradeArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='list_quotes', description='\\nGet quotes for a ticker symbol.\\n', inputSchema={'properties': {'ticker': {'title': 'Ticker', 'type': 'string'}, 'timestamp': {'anyOf': [{'type': 'string'}, {'type': 'integer'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Timestamp'}, 'timestamp_lt': {'anyOf': [{'type': 'string'}, {'type': 'integer'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Timestamp Lt'}, 'timestamp_lte': {'anyOf': [{'type': 'string'}, {'type': 'integer'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Timestamp Lte'}, 'timestamp_gt': {'anyOf': [{'type': 'string'}, {'type': 'integer'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Timestamp Gt'}, 'timestamp_gte': {'anyOf': [{'type': 'string'}, {'type': 'integer'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Timestamp Gte'}, 'limit': {'anyOf': [{'type': 'integer'}, {'type': 'null'}], 'default': None, 'title': 'Limit'}, 'sort': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Sort'}, 'order': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Order'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['ticker'], 'title': 'list_quotesArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_last_quote', description='\\nGet the most recent quote for a ticker symbol.\\n', inputSchema={'properties': {'ticker': {'title': 'Ticker', 'type': 'string'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['ticker'], 'title': 'get_last_quoteArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_last_forex_quote', description='\\nGet the most recent forex quote.\\n', inputSchema={'properties': {'from_': {'title': 'From', 'type': 'string'}, 'to': {'title': 'To', 'type': 'string'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['from_', 'to'], 'title': 'get_last_forex_quoteArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_real_time_currency_conversion', description='\\nGet real-time currency conversion.\\n', inputSchema={'properties': {'from_': {'title': 'From', 'type': 'string'}, 'to': {'title': 'To', 'type': 'string'}, 'amount': {'anyOf': [{'type': 'number'}, {'type': 'null'}], 'default': None, 'title': 'Amount'}, 'precision': {'anyOf': [{'type': 'integer'}, {'type': 'null'}], 'default': None, 'title': 'Precision'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['from_', 'to'], 'title': 'get_real_time_currency_conversionArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='list_universal_snapshots', description='\\nGet universal snapshots for multiple assets of a specific type.\\n', inputSchema={'properties': {'type': {'title': 'Type', 'type': 'string'}, 'ticker_any_of': {'anyOf': [{'items': {'type': 'string'}, 'type': 'array'}, {'type': 'null'}], 'default': None, 'title': 'Ticker Any Of'}, 'order': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Order'}, 'limit': {'anyOf': [{'type': 'integer'}, {'type': 'null'}], 'default': None, 'title': 'Limit'}, 'sort': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Sort'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['type'], 'title': 'list_universal_snapshotsArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_snapshot_all', description='\\nGet a snapshot of all tickers in a market.\\n', inputSchema={'properties': {'market_type': {'title': 'Market Type', 'type': 'string'}, 'tickers': {'anyOf': [{'items': {'type': 'string'}, 'type': 'array'}, {'type': 'null'}], 'default': None, 'title': 'Tickers'}, 'include_otc': {'anyOf': [{'type': 'boolean'}, {'type': 'null'}], 'default': None, 'title': 'Include Otc'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['market_type'], 'title': 'get_snapshot_allArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_snapshot_direction', description='\\nGet gainers or losers for a market.\\n', inputSchema={'properties': {'market_type': {'title': 'Market Type', 'type': 'string'}, 'direction': {'title': 'Direction', 'type': 'string'}, 'include_otc': {'anyOf': [{'type': 'boolean'}, {'type': 'null'}], 'default': None, 'title': 'Include Otc'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['market_type', 'direction'], 'title': 'get_snapshot_directionArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_snapshot_ticker', description='\\nGet snapshot for a specific ticker.\\n', inputSchema={'properties': {'market_type': {'title': 'Market Type', 'type': 'string'}, 'ticker': {'title': 'Ticker', 'type': 'string'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['market_type', 'ticker'], 'title': 'get_snapshot_tickerArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_snapshot_option', description='\\nGet snapshot for a specific option contract.\\n', inputSchema={'properties': {'underlying_asset': {'title': 'Underlying Asset', 'type': 'string'}, 'option_contract': {'title': 'Option Contract', 'type': 'string'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['underlying_asset', 'option_contract'], 'title': 'get_snapshot_optionArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_snapshot_crypto_book', description=\"\\nGet snapshot for a crypto ticker's order book.\\n\", inputSchema={'properties': {'ticker': {'title': 'Ticker', 'type': 'string'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['ticker'], 'title': 'get_snapshot_crypto_bookArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_market_holidays', description='\\nGet upcoming market holidays and their open/close times.\\n', inputSchema={'properties': {'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'title': 'get_market_holidaysArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_market_status', description='\\nGet current trading status of exchanges and financial markets.\\n', inputSchema={'properties': {'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'title': 'get_market_statusArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='list_tickers', description='\\nQ<PERSON>y supported ticker symbols across stocks, indices, forex, and crypto.\\n', inputSchema={'properties': {'ticker': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Ticker'}, 'type': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Type'}, 'market': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Market'}, 'exchange': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Exchange'}, 'cusip': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Cusip'}, 'cik': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Cik'}, 'date': {'anyOf': [{'type': 'string'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Date'}, 'search': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Search'}, 'active': {'anyOf': [{'type': 'boolean'}, {'type': 'null'}], 'default': None, 'title': 'Active'}, 'sort': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Sort'}, 'order': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Order'}, 'limit': {'anyOf': [{'type': 'integer'}, {'type': 'null'}], 'default': None, 'title': 'Limit'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'title': 'list_tickersArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_ticker_details', description='\\nGet detailed information about a specific ticker.\\n', inputSchema={'properties': {'ticker': {'title': 'Ticker', 'type': 'string'}, 'date': {'anyOf': [{'type': 'string'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Date'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'required': ['ticker'], 'title': 'get_ticker_detailsArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='list_ticker_news', description='\\nGet recent news articles for a stock ticker.\\n', inputSchema={'properties': {'ticker': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Ticker'}, 'published_utc': {'anyOf': [{'type': 'string'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Published Utc'}, 'limit': {'anyOf': [{'type': 'integer'}, {'type': 'null'}], 'default': None, 'title': 'Limit'}, 'sort': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Sort'}, 'order': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Order'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'title': 'list_ticker_newsArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_ticker_types', description='\\nList all ticker types supported by Polygon.io.\\n', inputSchema={'properties': {'asset_class': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Asset Class'}, 'locale': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Locale'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'title': 'get_ticker_typesArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='list_splits', description='\\nGet historical stock splits.\\n', inputSchema={'properties': {'ticker': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Ticker'}, 'execution_date': {'anyOf': [{'type': 'string'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Execution Date'}, 'reverse_split': {'anyOf': [{'type': 'boolean'}, {'type': 'null'}], 'default': None, 'title': 'Reverse Split'}, 'limit': {'anyOf': [{'type': 'integer'}, {'type': 'null'}], 'default': None, 'title': 'Limit'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'title': 'list_splitsArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='list_dividends', description='\\nGet historical cash dividends.\\n', inputSchema={'properties': {'ticker': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Ticker'}, 'ex_dividend_date': {'anyOf': [{'type': 'string'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Ex Dividend Date'}, 'frequency': {'anyOf': [{'type': 'integer'}, {'type': 'null'}], 'default': None, 'title': 'Frequency'}, 'dividend_type': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Dividend Type'}, 'limit': {'anyOf': [{'type': 'integer'}, {'type': 'null'}], 'default': None, 'title': 'Limit'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'title': 'list_dividendsArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='list_conditions', description='\\nList conditions used by Polygon.io.\\n', inputSchema={'properties': {'asset_class': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Asset Class'}, 'data_type': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Data Type'}, 'id': {'anyOf': [{'type': 'integer'}, {'type': 'null'}], 'default': None, 'title': 'Id'}, 'sip': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Sip'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'title': 'list_conditionsArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_exchanges', description='\\nList exchanges known by Polygon.io.\\n', inputSchema={'properties': {'asset_class': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Asset Class'}, 'locale': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Locale'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'title': 'get_exchangesArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='list_stock_financials', description='\\nGet fundamental financial data for companies.\\n', inputSchema={'properties': {'ticker': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Ticker'}, 'cik': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Cik'}, 'company_name': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Company Name'}, 'company_name_search': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Company Name Search'}, 'sic': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Sic'}, 'filing_date': {'anyOf': [{'type': 'string'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Filing Date'}, 'filing_date_lt': {'anyOf': [{'type': 'string'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Filing Date Lt'}, 'filing_date_lte': {'anyOf': [{'type': 'string'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Filing Date Lte'}, 'filing_date_gt': {'anyOf': [{'type': 'string'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Filing Date Gt'}, 'filing_date_gte': {'anyOf': [{'type': 'string'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Filing Date Gte'}, 'period_of_report_date': {'anyOf': [{'type': 'string'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Period Of Report Date'}, 'period_of_report_date_lt': {'anyOf': [{'type': 'string'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Period Of Report Date Lt'}, 'period_of_report_date_lte': {'anyOf': [{'type': 'string'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Period Of Report Date Lte'}, 'period_of_report_date_gt': {'anyOf': [{'type': 'string'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Period Of Report Date Gt'}, 'period_of_report_date_gte': {'anyOf': [{'type': 'string'}, {'format': 'date-time', 'type': 'string'}, {'format': 'date', 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Period Of Report Date Gte'}, 'timeframe': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Timeframe'}, 'include_sources': {'anyOf': [{'type': 'boolean'}, {'type': 'null'}], 'default': None, 'title': 'Include Sources'}, 'limit': {'anyOf': [{'type': 'integer'}, {'type': 'null'}], 'default': None, 'title': 'Limit'}, 'sort': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Sort'}, 'order': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Order'}, 'params': {'anyOf': [{'additionalProperties': True, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Params'}}, 'title': 'list_stock_financialsArguments', 'type': 'object'}, annotations=None)]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["polygon_api_key = os.getenv(\"POLYGON_API_KEY\")\n", "\n", "params = {\"command\": \"uvx\",\n", "          \"args\": [\"--from\", \"git+https://github.com/polygon-io/mcp_polygon@master\", \"mcp_polygon\"],\n", "          \"env\": {\"POLYGON_API_KEY\": polygon_api_key}\n", "          }\n", "async with MCPServerStdio(params=params) as server:\n", "    mcp_tools = await server.list_tools()\n", "mcp_tools\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Wow that's a lot of tools!\n", "\n", "Let's try them out - hopefully the sheer number of tools doesn't overwhelm gpt-4o-mini!\n", "\n", "With the $29 monthly plan, we don't have access to some of the APIs, so I've needed to specify which APIs can be called.\n", "\n", "If you've splashed out on a bigger plan, feel free to remove my extra constraint.."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/markdown": ["The latest share price of Apple (AAPL) is **$198.53**. Here are some additional details:\n", "\n", "- **Today's Change**: +$1.03 (+0.52%)\n", "- **Opening Price**: $199.00\n", "- **Day's High**: $200.54\n", "- **Day's Low**: $197.54\n", "- **Volume**: 36,452,015 shares\n", "\n", "If you need more information, feel free to ask!"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["instructions = \"You answer questions about the stock market.\"\n", "request = \"What's the share price of Apple? Use your get_snapshot_ticker tool to get the latest price.\"\n", "model = \"gpt-4o-mini\"\n", "\n", "async with MCPServerStdio(params=params) as mcp_server:\n", "    agent = Agent(name=\"agent\", instructions=instructions, model=model, mcp_servers=[mcp_server])\n", "    with trace(\"conversation\"):\n", "        result = await Runner.run(agent, request)\n", "    display(Markdown(result.final_output))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## In total, how many MCP servers and tools have we looked at?"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["We have 6 MCP servers, and 44 tools\n"]}], "source": ["from mcp_params import trader_mcp_server_params, researcher_mcp_server_params\n", "\n", "all_params = trader_mcp_server_params + researcher_mcp_server_params(\"ed\")\n", "\n", "count = 0\n", "for each_params in all_params:\n", "    async with MCPServerStdio(params=each_params) as server:\n", "        mcp_tools = await server.list_tools()\n", "        count += len(mcp_tools)\n", "print(f\"We have {len(all_params)} MCP servers, and {count} tools\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## OK - Let's build an application that uses all of these tools!!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}