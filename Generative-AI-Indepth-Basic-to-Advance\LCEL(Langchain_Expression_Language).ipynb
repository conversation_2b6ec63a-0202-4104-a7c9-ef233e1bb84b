{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyNeJZ3T3sy685liBqGIgoGw", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"f227a0992bde4673af0fb062aa5b35e8": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0f82ee1dd69942bb9c3b7768619237ca", "IPY_MODEL_6f535b58873248698e94c5bf9cff6a58", "IPY_MODEL_3fbcde1c16644a8083f5ad7141d584a9"], "layout": "IPY_MODEL_5b3ea04a18f341cab660bb8750548975"}}, "0f82ee1dd69942bb9c3b7768619237ca": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5bbb9df62d394236885e2e2866846581", "placeholder": "​", "style": "IPY_MODEL_9ce089032c6b49f9bf40d637c0efef6e", "value": "modules.json: 100%"}}, "6f535b58873248698e94c5bf9cff6a58": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_37733f7c7f584057b9cf7faa0b0c3760", "max": 349, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c02b648dd77d4ef0af423890c32f2f30", "value": 349}}, "3fbcde1c16644a8083f5ad7141d584a9": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3c0573e4f8744ddea2654f31cc85ff2d", "placeholder": "​", "style": "IPY_MODEL_a77916c64d1d46b3afe624ad02ca4137", "value": " 349/349 [00:00&lt;00:00, 15.5kB/s]"}}, "5b3ea04a18f341cab660bb8750548975": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5bbb9df62d394236885e2e2866846581": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9ce089032c6b49f9bf40d637c0efef6e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "37733f7c7f584057b9cf7faa0b0c3760": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c02b648dd77d4ef0af423890c32f2f30": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3c0573e4f8744ddea2654f31cc85ff2d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a77916c64d1d46b3afe624ad02ca4137": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "33b81cf377fb42a585f77e67399e082b": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7fd723a318324a79bf33abfe56d3620d", "IPY_MODEL_918ef72969bb4815a8876164cc56d1fc", "IPY_MODEL_3483fb7f00f04cef9958e370fb9c8a8c"], "layout": "IPY_MODEL_6da2c67cf3bf4814bcd64dfca244776e"}}, "7fd723a318324a79bf33abfe56d3620d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_24721c2aad2c4fa590e5bb486341402a", "placeholder": "​", "style": "IPY_MODEL_e7f09a2c0ff84825867ee6ec25bc10f7", "value": "config_sentence_transformers.json: 100%"}}, "918ef72969bb4815a8876164cc56d1fc": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_45f9a0c73f004ef69a76e200309570f4", "max": 116, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5566c6a8f8ff40818436490b9e07173e", "value": 116}}, "3483fb7f00f04cef9958e370fb9c8a8c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e08b06fb92d8449db0b357558c25d620", "placeholder": "​", "style": "IPY_MODEL_628c5f16be4045ca99d9467fa07bd888", "value": " 116/116 [00:00&lt;00:00, 5.75kB/s]"}}, "6da2c67cf3bf4814bcd64dfca244776e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "24721c2aad2c4fa590e5bb486341402a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e7f09a2c0ff84825867ee6ec25bc10f7": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "45f9a0c73f004ef69a76e200309570f4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5566c6a8f8ff40818436490b9e07173e": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e08b06fb92d8449db0b357558c25d620": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "628c5f16be4045ca99d9467fa07bd888": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "29b6b20ce1b847309bc9549f208bfe39": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_954fe98f3261408aa10491d96f19e272", "IPY_MODEL_0927025465b0492d81409fb71018bd60", "IPY_MODEL_e14d291267454ec7a181ddf6929c33bb"], "layout": "IPY_MODEL_0b78598e186a4d1980d5196ffd44cde7"}}, "954fe98f3261408aa10491d96f19e272": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_876c972040d24ca8a1e04816afa89866", "placeholder": "​", "style": "IPY_MODEL_8b537c3657de40999e86bfecafc583f9", "value": "README.md: 100%"}}, "0927025465b0492d81409fb71018bd60": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9d71e1827f574c9eadc792509f4d17fb", "max": 10659, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9101047b368a47418b1af2852dca6309", "value": 10659}}, "e14d291267454ec7a181ddf6929c33bb": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_248c31eb970f4f08aaab64374420adb4", "placeholder": "​", "style": "IPY_MODEL_7460ad56137c4789bd01ee3770ebb4d4", "value": " 10.7k/10.7k [00:00&lt;00:00, 462kB/s]"}}, "0b78598e186a4d1980d5196ffd44cde7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "876c972040d24ca8a1e04816afa89866": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8b537c3657de40999e86bfecafc583f9": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9d71e1827f574c9eadc792509f4d17fb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9101047b368a47418b1af2852dca6309": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "248c31eb970f4f08aaab64374420adb4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7460ad56137c4789bd01ee3770ebb4d4": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e14b79c1eb09499f925c5abc87f9e358": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_07c4f947bfe245f4adb3cf375f089939", "IPY_MODEL_d8f943cdce2a4e30a4a47dad04f05d28", "IPY_MODEL_a2114d55fda4451d902c0ef1aa12a428"], "layout": "IPY_MODEL_2965df6c49044b35b3ba6d2cdc24d74d"}}, "07c4f947bfe245f4adb3cf375f089939": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d7c44beb266141d3875b800de14214aa", "placeholder": "​", "style": "IPY_MODEL_423c43f9333c464594d744a6c5d8be8a", "value": "sentence_bert_config.json: 100%"}}, "d8f943cdce2a4e30a4a47dad04f05d28": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ec1075c521644d3f953a16cbe1f2e030", "max": 53, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2ce57d428da04626be6995389469ab28", "value": 53}}, "a2114d55fda4451d902c0ef1aa12a428": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_48ca0355d5c740f59db88d4332353ee7", "placeholder": "​", "style": "IPY_MODEL_19789d9a79fd4742af4e1dafee03c1b1", "value": " 53.0/53.0 [00:00&lt;00:00, 3.15kB/s]"}}, "2965df6c49044b35b3ba6d2cdc24d74d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d7c44beb266141d3875b800de14214aa": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "423c43f9333c464594d744a6c5d8be8a": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ec1075c521644d3f953a16cbe1f2e030": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2ce57d428da04626be6995389469ab28": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "48ca0355d5c740f59db88d4332353ee7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "19789d9a79fd4742af4e1dafee03c1b1": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6076bc5b88e640a39a45107139036090": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7b9ed53198aa46fea9f0875fd07999c5", "IPY_MODEL_708cf9ffbd7c4dfd9bb35581026f12d9", "IPY_MODEL_bdde8937a9a0463388d84a721b95d31f"], "layout": "IPY_MODEL_a28656b202ea4b75a3dc564616d209f8"}}, "7b9ed53198aa46fea9f0875fd07999c5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_09caf4b59d1a4b579a9a590b748c53b6", "placeholder": "​", "style": "IPY_MODEL_ba06a80754314d20836a87ded5becb99", "value": "config.json: 100%"}}, "708cf9ffbd7c4dfd9bb35581026f12d9": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f88d5e5fb2734091b1916b578cf4a8b9", "max": 612, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_381aa0cbbe4044999bca9df41dd00664", "value": 612}}, "bdde8937a9a0463388d84a721b95d31f": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f9da47d45cbd423e9c74e48e82452b83", "placeholder": "​", "style": "IPY_MODEL_074a6471fdbe40ed9acfbe2f5e949c2f", "value": " 612/612 [00:00&lt;00:00, 33.0kB/s]"}}, "a28656b202ea4b75a3dc564616d209f8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "09caf4b59d1a4b579a9a590b748c53b6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ba06a80754314d20836a87ded5becb99": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f88d5e5fb2734091b1916b578cf4a8b9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "381aa0cbbe4044999bca9df41dd00664": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f9da47d45cbd423e9c74e48e82452b83": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "074a6471fdbe40ed9acfbe2f5e949c2f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e8d779e8ed5a4d659c74aa586c169c57": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ff51e0815d29426d9304a0d781dafb12", "IPY_MODEL_72b6c4c385af44e1bba560296276db76", "IPY_MODEL_211627521efb4a829aebb5e64cdd2115"], "layout": "IPY_MODEL_2bcfc3e35bd54e0f8eb411e2accaffd4"}}, "ff51e0815d29426d9304a0d781dafb12": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f71bb3b2677c43bd8b918346db36be48", "placeholder": "​", "style": "IPY_MODEL_b5526335d665446daf7e5837c8dea650", "value": "model.safetensors: 100%"}}, "72b6c4c385af44e1bba560296276db76": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2ff329f286054bcaae5fc27cf67ed36f", "max": 90868376, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_fe4a5fec3f0f46eda3d9b1b44a20a4e2", "value": 90868376}}, "211627521efb4a829aebb5e64cdd2115": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b264cfc0a5c24aa193d246fbc3de15e5", "placeholder": "​", "style": "IPY_MODEL_110d0c5bef664638b7977f46f0735905", "value": " 90.9M/90.9M [00:00&lt;00:00, 125MB/s]"}}, "2bcfc3e35bd54e0f8eb411e2accaffd4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f71bb3b2677c43bd8b918346db36be48": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b5526335d665446daf7e5837c8dea650": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2ff329f286054bcaae5fc27cf67ed36f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fe4a5fec3f0f46eda3d9b1b44a20a4e2": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b264cfc0a5c24aa193d246fbc3de15e5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "110d0c5bef664638b7977f46f0735905": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b3651292353c458099e7e96a8a002096": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e220c44bbc18421d8d147ca77e6788bd", "IPY_MODEL_53cd5af714e6466cacc1dc92d478b775", "IPY_MODEL_d3606dc8cdb141ed8fca43bb6ce043c7"], "layout": "IPY_MODEL_cdc13486cd4d4b35afa107c79675a19d"}}, "e220c44bbc18421d8d147ca77e6788bd": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_58d206a51602494abfbffab3f862015b", "placeholder": "​", "style": "IPY_MODEL_8fc3256efadb4fb3827e8a88e6acb998", "value": "tokenizer_config.json: 100%"}}, "53cd5af714e6466cacc1dc92d478b775": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2e525eb63279444fb86403a0ea77addb", "max": 350, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_1a32af7ea56843d982e2d4f794c6dfe8", "value": 350}}, "d3606dc8cdb141ed8fca43bb6ce043c7": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_85e14014592f492782b195a283ba74ad", "placeholder": "​", "style": "IPY_MODEL_131c46f2ecf6494b97829e9ba56d49ee", "value": " 350/350 [00:00&lt;00:00, 12.8kB/s]"}}, "cdc13486cd4d4b35afa107c79675a19d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "58d206a51602494abfbffab3f862015b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8fc3256efadb4fb3827e8a88e6acb998": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2e525eb63279444fb86403a0ea77addb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1a32af7ea56843d982e2d4f794c6dfe8": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "85e14014592f492782b195a283ba74ad": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "131c46f2ecf6494b97829e9ba56d49ee": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d12085da77d84d0ab0884a36c3b63a8c": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_972a61bd9c4d499b938c16a982410ca7", "IPY_MODEL_cad4498ad3f64a9c93065415c36adb29", "IPY_MODEL_4c7a2b0542674b7aaeccce12591528ed"], "layout": "IPY_MODEL_830add38427f45568eb0700b8136a737"}}, "972a61bd9c4d499b938c16a982410ca7": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8eac6546a3af4e1597184bd879817cf7", "placeholder": "​", "style": "IPY_MODEL_b824f2ae1ac04cd78a093d6e196a7f97", "value": "vocab.txt: 100%"}}, "cad4498ad3f64a9c93065415c36adb29": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cb1c6c4e740b4a91b4a0f12a5adbe9d7", "max": 231508, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_12061393e24442a1ac19f59df7c8ed02", "value": 231508}}, "4c7a2b0542674b7aaeccce12591528ed": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_02b9bc0c2f2f4201b6ff2a2412c5e7a7", "placeholder": "​", "style": "IPY_MODEL_18009a48070c44c3a428ebfc174efe15", "value": " 232k/232k [00:00&lt;00:00, 4.39MB/s]"}}, "830add38427f45568eb0700b8136a737": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8eac6546a3af4e1597184bd879817cf7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b824f2ae1ac04cd78a093d6e196a7f97": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cb1c6c4e740b4a91b4a0f12a5adbe9d7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "12061393e24442a1ac19f59df7c8ed02": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "02b9bc0c2f2f4201b6ff2a2412c5e7a7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "18009a48070c44c3a428ebfc174efe15": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d1ae802dba1149ca9e4def59a72ec3ba": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_3b252b6cee3c4543bb8919f80eebe288", "IPY_MODEL_56efd36f6e5c4ab29702cf374d442039", "IPY_MODEL_290d595d8e58474faee18e36c503249f"], "layout": "IPY_MODEL_aecd0585304f43e5aa8086877e41e679"}}, "3b252b6cee3c4543bb8919f80eebe288": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f408d390719b4587bf89a2e6d4245f39", "placeholder": "​", "style": "IPY_MODEL_b4444854e39f4b3eb9c20553aa509790", "value": "tokenizer.json: 100%"}}, "56efd36f6e5c4ab29702cf374d442039": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7548118226e74462b1fb7ccf470707dc", "max": 466247, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_fce994a8f0a742a39d7a009cbcd91db7", "value": 466247}}, "290d595d8e58474faee18e36c503249f": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c666a1a873e74e1f87570d25e5839c3b", "placeholder": "​", "style": "IPY_MODEL_fb08084b416f40818b123916cac094de", "value": " 466k/466k [00:00&lt;00:00, 5.41MB/s]"}}, "aecd0585304f43e5aa8086877e41e679": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f408d390719b4587bf89a2e6d4245f39": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b4444854e39f4b3eb9c20553aa509790": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7548118226e74462b1fb7ccf470707dc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fce994a8f0a742a39d7a009cbcd91db7": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c666a1a873e74e1f87570d25e5839c3b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fb08084b416f40818b123916cac094de": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0201bd357a964ef28e97bf63947147a0": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9b326f532e5f46dd8c64a37ca7d23bfa", "IPY_MODEL_b95bc3eb580248ec8f31ef90419f9ce1", "IPY_MODEL_316acc82952243649ee78e5bf7357811"], "layout": "IPY_MODEL_1cd8d4ce444d4bf185bf1ce8668486dc"}}, "9b326f532e5f46dd8c64a37ca7d23bfa": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_487f9f61b45e4365b0ac0dfc7a64eca3", "placeholder": "​", "style": "IPY_MODEL_a5b48868639f494fb1dc7a52ad9a2ab5", "value": "special_tokens_map.json: 100%"}}, "b95bc3eb580248ec8f31ef90419f9ce1": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_53e3b30a041d4a31938adb1cf6d55c46", "max": 112, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7c8dbffeb2294ad590974f644847a2c3", "value": 112}}, "316acc82952243649ee78e5bf7357811": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9c45a4095bc34f15b4415f58edbb17df", "placeholder": "​", "style": "IPY_MODEL_49125bd1de2b46899a52c69d54e6ace0", "value": " 112/112 [00:00&lt;00:00, 4.68kB/s]"}}, "1cd8d4ce444d4bf185bf1ce8668486dc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "487f9f61b45e4365b0ac0dfc7a64eca3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a5b48868639f494fb1dc7a52ad9a2ab5": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "53e3b30a041d4a31938adb1cf6d55c46": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7c8dbffeb2294ad590974f644847a2c3": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9c45a4095bc34f15b4415f58edbb17df": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "49125bd1de2b46899a52c69d54e6ace0": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e8e8700a0b45490992fae82f6e04ccf0": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ef43923528a84424bde4490feb99cf6e", "IPY_MODEL_dd26fb9910ba4bffaa5d4db648e951ef", "IPY_MODEL_715c8f7ed318476faf6dd2309530162f"], "layout": "IPY_MODEL_b15aecf8ca824865934329dd8fb6b232"}}, "ef43923528a84424bde4490feb99cf6e": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d132cbcf72a346c9974b776cd7d55d47", "placeholder": "​", "style": "IPY_MODEL_dc464f3338c2435b84b75db1f8e6e416", "value": "1_Pooling/config.json: 100%"}}, "dd26fb9910ba4bffaa5d4db648e951ef": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1f29ae9f38694368adc61335125598e0", "max": 190, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_367c6767a186473d83432d8f38aba8cb", "value": 190}}, "715c8f7ed318476faf6dd2309530162f": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c625fadf89a945a098c8cab8c79edaed", "placeholder": "​", "style": "IPY_MODEL_00ddbe52e41b48a890d99c146637f9fc", "value": " 190/190 [00:00&lt;00:00, 9.92kB/s]"}}, "b15aecf8ca824865934329dd8fb6b232": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d132cbcf72a346c9974b776cd7d55d47": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dc464f3338c2435b84b75db1f8e6e416": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1f29ae9f38694368adc61335125598e0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "367c6767a186473d83432d8f38aba8cb": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c625fadf89a945a098c8cab8c79edaed": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "00ddbe52e41b48a890d99c146637f9fc": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/sunnysavita10/Generative-AI-Indepth-Basic-to-Advance/blob/main/LCEL(Langchain_Expression_Language).ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["## Transitioning from Old class to New Pipe Base Operator\n", "\n", "## 1. Understanding `Runnables`\n", "- `Runnables` are self-contained units of work.\n", "- Can be executed in isolation or combined for complex operations.\n", "- Provides flexibility in execution (sync, async, parallel).\n", "\n", "## 2. `<PERSON><PERSON><PERSON>P<PERSON>llel`\n", "- Executes tasks concurrently.\n", "- Useful for performance enhancement in scenarios where tasks can run independently.\n", "- Syntax example:\n", "    ```python\n", "    from some_module import RunnableParallel\n", "    ```\n", "\n", "## 3. `RunnablePassthrough`\n", "- A simple `Runnable` that passes inputs directly to outputs without modification.\n", "- Helpful for debugging or chaining in pipelines.\n", "- Example use case:\n", "    ```python\n", "    from some_module import RunnablePassthrough\n", "    passthrough = RunnablePassthrough()\n", "    result = passthrough.run(input_data)\n", "    ```\n", "\n", "## 4. `RunnableLambda`\n", "- Allows quick, inline definitions of small, custom functions.\n", "- Example:\n", "    ```python\n", "    from some_module import RunnableLambda\n", "    lambda_op = RunnableLambda(lambda x: x * 2)\n", "    result = lambda_op.run(5)  # Output: 10\n", "    ```\n", "\n", "## 5. Assign Functions\n", "- Used to assign values or parameters during execution.\n", "- Useful in data pipelines to update intermediate values.\n", "\n", "## 6. Performance Improvement (Inference Speed)\n", "- Focus on optimizing the inference speed by leveraging parallel execution.\n", "- Use `RunnableParallel` or batching techniques.\n", "- Consider optimizing data pipelines by removing unnecessary steps.\n", "\n", "## 7. <PERSON><PERSON> Invoke\n", "- Executes operations asynchronously, improving the overall throughput of the system.\n", "- Syntax example:\n", "    ```python\n", "    async def async_operation():\n", "        result = await some_async_function()\n", "    ```\n", "\n", "## 8. <PERSON><PERSON>\n", "- Handles multiple inputs at once to improve performance.\n", "- Can be combined with `RunnableParallel` for parallel batch execution.\n", "\n", "## 9. Async Batch Execution\n", "- Combines asynchronous execution with batch processing for high-performance tasks.\n", "- Reduces overall execution time for larger datasets.\n", "\n", "## 10. Using `<PERSON><PERSON><PERSON><PERSON>` with `LCEL`\n", "- `Itemgetter` is used to extract specific items from collections.\n", "- When combined with `LCEL` (LangChain Execution Layer), it can streamline complex operations.\n", "\n", "## 11. <PERSON><PERSON>ls\n", "- `Bind` tools help to connect different steps in the pipeline.\n", "- Ensures smooth data flow between various `Runnable` components.\n", "\n", "## 12. Stream Support\n", "- Keep your pipelines more responsive by incorporating stream support for data.\n", "- This allows continuous data processing and near real-time outputs.\n", "  \n"], "metadata": {"id": "e9BSbrZq-Uqj"}}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3einjXsX3RgD", "outputId": "ed88ee6f-4081-492b-a10f-0cf8c79121d3"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain_google_genai\n", "  Downloading langchain_google_genai-2.0.0-py3-none-any.whl.metadata (3.9 kB)\n", "Requirement already satisfied: google-generativeai<0.8.0,>=0.7.0 in /usr/local/lib/python3.10/dist-packages (from langchain_google_genai) (0.7.2)\n", "Collecting langchain-core<0.4,>=0.3.0 (from langchain_google_genai)\n", "  Downloading langchain_core-0.3.0-py3-none-any.whl.metadata (6.2 kB)\n", "Requirement already satisfied: pydantic<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain_google_genai) (2.9.1)\n", "Requirement already satisfied: google-ai-generativelanguage==0.6.6 in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (0.6.6)\n", "Requirement already satisfied: google-api-core in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.19.2)\n", "Requirement already satisfied: google-api-python-client in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.137.0)\n", "Requirement already satisfied: google-auth>=2.15.0 in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.27.0)\n", "Requirement already satisfied: protobuf in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (3.20.3)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (4.66.5)\n", "Requirement already satisfied: typing-extensions in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (4.12.2)\n", "Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.3 in /usr/local/lib/python3.10/dist-packages (from google-ai-generativelanguage==0.6.6->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (1.24.0)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4,>=0.3.0->langchain_google_genai) (6.0.2)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain-core<0.4,>=0.3.0->langchain_google_genai)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl.metadata (3.0 kB)\n", "Collecting langsmith<0.2.0,>=0.1.117 (from langchain-core<0.4,>=0.3.0->langchain_google_genai)\n", "  Downloading langsmith-0.1.121-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4,>=0.3.0->langchain_google_genai) (24.1)\n", "Collecting tenacity!=8.4.0,<9.0.0,>=8.1.0 (from langchain-core<0.4,>=0.3.0->langchain_google_genai)\n", "  Downloading tenacity-8.5.0-py3-none-any.whl.metadata (1.2 kB)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=2->langchain_google_genai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.23.3 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=2->langchain_google_genai) (2.23.3)\n", "Requirement already satisfied: googleapis-common-protos<2.0.dev0,>=1.56.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (1.65.0)\n", "Requirement already satisfied: requests<3.0.0.dev0,>=2.18.0 in /usr/local/lib/python3.10/dist-packages (from google-api-core->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.32.3)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth>=2.15.0->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (5.5.0)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth>=2.15.0->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (0.4.1)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth>=2.15.0->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (4.9)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core<0.4,>=0.3.0->langchain_google_genai)\n", "  Downloading jsonpointer-3.0.0-py2.py3-none-any.whl.metadata (2.3 kB)\n", "Collecting httpx<1,>=0.23.0 (from langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3.0->langchain_google_genai)\n", "  Downloading httpx-0.27.2-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting or<PERSON><PERSON><4.0.0,>=3.9.14 (from langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3.0->langchain_google_genai)\n", "  Downloading orjson-3.10.7-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (50 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.4/50.4 kB\u001b[0m \u001b[31m847.0 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: httplib2<1.dev0,>=0.19.0 in /usr/local/lib/python3.10/dist-packages (from google-api-python-client->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (0.22.0)\n", "Requirement already satisfied: google-auth-httplib2<1.0.0,>=0.2.0 in /usr/local/lib/python3.10/dist-packages (from google-api-python-client->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (0.2.0)\n", "Requirement already satisfied: uritemplate<5,>=3.0.1 in /usr/local/lib/python3.10/dist-packages (from google-api-python-client->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (4.1.1)\n", "Requirement already satisfied: grpcio<2.0dev,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-ai-generativelanguage==0.6.6->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (1.64.1)\n", "Requirement already satisfied: grpcio-status<2.0.dev0,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-ai-generativelanguage==0.6.6->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (1.48.2)\n", "Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in /usr/local/lib/python3.10/dist-packages (from httplib2<1.dev0,>=0.19.0->google-api-python-client->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (3.1.4)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3.0->langchain_google_genai) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3.0->langchain_google_genai) (2024.8.30)\n", "Collecting httpcore==1.* (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3.0->langchain_google_genai)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl.metadata (20 kB)\n", "Requirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3.0->langchain_google_genai) (3.8)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3.0->langchain_google_genai) (1.3.1)\n", "Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3.0->langchain_google_genai)\n", "  Downloading h11-0.14.0-py3-none-any.whl.metadata (8.2 kB)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth>=2.15.0->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (0.6.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.0.7)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3.0->langchain_google_genai) (1.2.2)\n", "Downloading langchain_google_genai-2.0.0-py3-none-any.whl (39 kB)\n", "Downloading langchain_core-0.3.0-py3-none-any.whl (405 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m405.1/405.1 kB\u001b[0m \u001b[31m3.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Downloading langsmith-0.1.121-py3-none-any.whl (289 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m289.9/289.9 kB\u001b[0m \u001b[31m14.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading tenacity-8.5.0-py3-none-any.whl (28 kB)\n", "Downloading httpx-0.27.2-py3-none-any.whl (76 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m76.4/76.4 kB\u001b[0m \u001b[31m5.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m6.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading jsonpointer-3.0.0-py2.py3-none-any.whl (7.6 kB)\n", "Downloading orjson-3.10.7-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (141 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m141.9/141.9 kB\u001b[0m \u001b[31m11.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m3.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: tenacity, orjson, jsonpointer, h11, jsonpatch, httpcore, httpx, langsmith, langchain-core, langchain_google_genai\n", "  Attempting uninstall: tenacity\n", "    Found existing installation: tenacity 9.0.0\n", "    Uninstalling tenacity-9.0.0:\n", "      Successfully uninstalled tenacity-9.0.0\n", "Successfully installed h11-0.14.0 httpcore-1.0.5 httpx-0.27.2 jsonpatch-1.33 jsonpointer-3.0.0 langchain-core-0.3.0 langchain_google_genai-2.0.0 langsmith-0.1.121 orjson-3.10.7 tenacity-8.5.0\n", "Collecting langchain_community\n", "  Downloading langchain_community-0.3.0-py3-none-any.whl.metadata (2.8 kB)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (6.0.2)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (2.0.34)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (3.10.5)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain_community)\n", "  Downloading dataclasses_json-0.6.7-py3-none-any.whl.metadata (25 kB)\n", "Collecting langchain<0.4.0,>=0.3.0 (from langchain_community)\n", "  Downloading langchain-0.3.0-py3-none-any.whl.metadata (7.1 kB)\n", "Requirement already satisfied: langchain-core<0.4.0,>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (0.3.0)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.112 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (0.1.121)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (1.26.4)\n", "Collecting pydantic-settings<3.0.0,>=2.4.0 (from langchain_community)\n", "  Downloading pydantic_settings-2.5.2-py3-none-any.whl.metadata (3.5 kB)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (2.32.3)\n", "Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (8.5.0)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (2.4.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (24.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (6.1.0)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.11.1)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (4.0.3)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain_community)\n", "  Downloading marshmallow-3.22.0-py3-none-any.whl.metadata (7.2 kB)\n", "Collecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain_community)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl.metadata (1.5 kB)\n", "Collecting langchain-text-splitters<0.4.0,>=0.3.0 (from langchain<0.4.0,>=0.3.0->langchain_community)\n", "  Downloading langchain_text_splitters-0.3.0-py3-none-any.whl.metadata (2.3 kB)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in /usr/local/lib/python3.10/dist-packages (from langchain<0.4.0,>=0.3.0->langchain_community) (2.9.1)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4.0,>=0.3.0->langchain_community) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4.0,>=0.3.0->langchain_community) (24.1)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4.0,>=0.3.0->langchain_community) (4.12.2)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.112->langchain_community) (0.27.2)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.112->langchain_community) (3.10.7)\n", "Collecting python-dotenv>=0.21.0 (from pydantic-settings<3.0.0,>=2.4.0->langchain_community)\n", "  Downloading python_dotenv-1.0.1-py3-none-any.whl.metadata (23 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (2024.8.30)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain_community) (3.1.0)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.112->langchain_community) (3.7.1)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.112->langchain_community) (1.0.5)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.112->langchain_community) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.112->langchain_community) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.4.0,>=0.3.0->langchain_community) (3.0.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain<0.4.0,>=0.3.0->langchain_community) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.23.3 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain<0.4.0,>=0.3.0->langchain_community) (2.23.3)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain_community)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl.metadata (1.1 kB)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.112->langchain_community) (1.2.2)\n", "Downloading langchain_community-0.3.0-py3-none-any.whl (2.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.3/2.3 MB\u001b[0m \u001b[31m12.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading dataclasses_json-0.6.7-py3-none-any.whl (28 kB)\n", "Downloading langchain-0.3.0-py3-none-any.whl (1.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m50.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pydantic_settings-2.5.2-py3-none-any.whl (26 kB)\n", "Downloading langchain_text_splitters-0.3.0-py3-none-any.whl (25 kB)\n", "Downloading marshmallow-3.22.0-py3-none-any.whl (49 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.3/49.3 kB\u001b[0m \u001b[31m3.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading python_dotenv-1.0.1-py3-none-any.whl (19 kB)\n", "Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: python-dotenv, mypy-extensions, marshmallow, typing-inspect, pydantic-settings, dataclasses-json, langchain-text-splitters, langchain, langchain_community\n", "Successfully installed dataclasses-json-0.6.7 langchain-0.3.0 langchain-text-splitters-0.3.0 langchain_community-0.3.0 marshmallow-3.22.0 mypy-extensions-1.0.0 pydantic-settings-2.5.2 python-dotenv-1.0.1 typing-inspect-0.9.0\n", "Requirement already satisfied: langchain in /usr/local/lib/python3.10/dist-packages (0.3.0)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (6.0.2)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.0.34)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.10.5)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (4.0.3)\n", "Requirement already satisfied: langchain-core<0.4.0,>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (0.3.0)\n", "Requirement already satisfied: langchain-text-splitters<0.4.0,>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (0.3.0)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.17 in /usr/local/lib/python3.10/dist-packages (from langchain) (0.1.121)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.26.4)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.9.1)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.32.3)\n", "Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (8.5.0)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (2.4.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (24.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.1.0)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.11.1)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4.0,>=0.3.0->langchain) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4.0,>=0.3.0->langchain) (24.1)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4.0,>=0.3.0->langchain) (4.12.2)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.17->langchain) (0.27.2)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.17->langchain) (3.10.7)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.23.3 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (2.23.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2024.8.30)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.1.0)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (3.7.1)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (1.0.5)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.4.0,>=0.3.0->langchain) (3.0.0)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (1.2.2)\n", "Collecting langchain_huggingface\n", "  Downloading langchain_huggingface-0.1.0-py3-none-any.whl.metadata (1.3 kB)\n", "Requirement already satisfied: huggingface-hub>=0.23.0 in /usr/local/lib/python3.10/dist-packages (from langchain_huggingface) (0.24.7)\n", "Requirement already satisfied: langchain-core<0.4,>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from langchain_huggingface) (0.3.0)\n", "Collecting sentence-transformers>=2.6.0 (from langchain_huggingface)\n", "  Downloading sentence_transformers-3.1.0-py3-none-any.whl.metadata (23 kB)\n", "Requirement already satisfied: tokenizers>=0.19.1 in /usr/local/lib/python3.10/dist-packages (from langchain_huggingface) (0.19.1)\n", "Requirement already satisfied: transformers>=4.39.0 in /usr/local/lib/python3.10/dist-packages (from langchain_huggingface) (4.44.2)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.23.0->langchain_huggingface) (3.16.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.23.0->langchain_huggingface) (2024.6.1)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.23.0->langchain_huggingface) (24.1)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.23.0->langchain_huggingface) (6.0.2)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.23.0->langchain_huggingface) (2.32.3)\n", "Requirement already satisfied: tqdm>=4.42.1 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.23.0->langchain_huggingface) (4.66.5)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.23.0->langchain_huggingface) (4.12.2)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4,>=0.3.0->langchain_huggingface) (1.33)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.117 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4,>=0.3.0->langchain_huggingface) (0.1.121)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.5.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4,>=0.3.0->langchain_huggingface) (2.9.1)\n", "Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4,>=0.3.0->langchain_huggingface) (8.5.0)\n", "Requirement already satisfied: torch>=1.11.0 in /usr/local/lib/python3.10/dist-packages (from sentence-transformers>=2.6.0->langchain_huggingface) (2.4.0+cu121)\n", "Requirement already satisfied: numpy<2.0.0 in /usr/local/lib/python3.10/dist-packages (from sentence-transformers>=2.6.0->langchain_huggingface) (1.26.4)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.10/dist-packages (from sentence-transformers>=2.6.0->langchain_huggingface) (1.3.2)\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.10/dist-packages (from sentence-transformers>=2.6.0->langchain_huggingface) (1.13.1)\n", "Requirement already satisfied: Pillow in /usr/local/lib/python3.10/dist-packages (from sentence-transformers>=2.6.0->langchain_huggingface) (9.4.0)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.10/dist-packages (from transformers>=4.39.0->langchain_huggingface) (2024.5.15)\n", "Requirement already satisfied: safetensors>=0.4.1 in /usr/local/lib/python3.10/dist-packages (from transformers>=4.39.0->langchain_huggingface) (0.4.5)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.4,>=0.3.0->langchain_huggingface) (3.0.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3.0->langchain_huggingface) (0.27.2)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3.0->langchain_huggingface) (3.10.7)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0.0,>=2.5.2->langchain-core<0.4,>=0.3.0->langchain_huggingface) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.23.3 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0.0,>=2.5.2->langchain-core<0.4,>=0.3.0->langchain_huggingface) (2.23.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.23.0->langchain_huggingface) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.23.0->langchain_huggingface) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.23.0->langchain_huggingface) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.23.0->langchain_huggingface) (2024.8.30)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers>=2.6.0->langchain_huggingface) (1.13.2)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers>=2.6.0->langchain_huggingface) (3.3)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers>=2.6.0->langchain_huggingface) (3.1.4)\n", "Requirement already satisfied: joblib>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from scikit-learn->sentence-transformers>=2.6.0->langchain_huggingface) (1.4.2)\n", "Requirement already satisfied: threadpoolctl>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from scikit-learn->sentence-transformers>=2.6.0->langchain_huggingface) (3.5.0)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3.0->langchain_huggingface) (3.7.1)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3.0->langchain_huggingface) (1.0.5)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3.0->langchain_huggingface) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3.0->langchain_huggingface) (0.14.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch>=1.11.0->sentence-transformers>=2.6.0->langchain_huggingface) (2.1.5)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from sympy->torch>=1.11.0->sentence-transformers>=2.6.0->langchain_huggingface) (1.3.0)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3.0->langchain_huggingface) (1.2.2)\n", "Downloading langchain_huggingface-0.1.0-py3-none-any.whl (20 kB)\n", "Downloading sentence_transformers-3.1.0-py3-none-any.whl (249 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m249.1/249.1 kB\u001b[0m \u001b[31m2.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: sentence-transformers, langchain_huggingface\n", "Successfully installed langchain_huggingface-0.1.0 sentence-transformers-3.1.0\n", "Collecting langchain_groq\n", "  Downloading langchain_groq-0.2.0-py3-none-any.whl.metadata (2.9 kB)\n", "Collecting groq<1,>=0.4.1 (from langchain_groq)\n", "  Downloading groq-0.11.0-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: langchain-core<0.4,>=0.3 in /usr/local/lib/python3.10/dist-packages (from langchain_groq) (0.3.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.10/dist-packages (from groq<1,>=0.4.1->langchain_groq) (3.7.1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from groq<1,>=0.4.1->langchain_groq) (1.7.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.10/dist-packages (from groq<1,>=0.4.1->langchain_groq) (0.27.2)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /usr/local/lib/python3.10/dist-packages (from groq<1,>=0.4.1->langchain_groq) (2.9.1)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from groq<1,>=0.4.1->langchain_groq) (1.3.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.7 in /usr/local/lib/python3.10/dist-packages (from groq<1,>=0.4.1->langchain_groq) (4.12.2)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4,>=0.3->langchain_groq) (6.0.2)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4,>=0.3->langchain_groq) (1.33)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.117 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4,>=0.3->langchain_groq) (0.1.121)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4,>=0.3->langchain_groq) (24.1)\n", "Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4,>=0.3->langchain_groq) (8.5.0)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->groq<1,>=0.4.1->langchain_groq) (3.8)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->groq<1,>=0.4.1->langchain_groq) (1.2.2)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->groq<1,>=0.4.1->langchain_groq) (2024.8.30)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->groq<1,>=0.4.1->langchain_groq) (1.0.5)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->groq<1,>=0.4.1->langchain_groq) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.4,>=0.3->langchain_groq) (3.0.0)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3->langchain_groq) (3.10.7)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3->langchain_groq) (2.32.3)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1.9.0->groq<1,>=0.4.1->langchain_groq) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.23.3 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1.9.0->groq<1,>=0.4.1->langchain_groq) (2.23.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3->langchain_groq) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langsmith<0.2.0,>=0.1.117->langchain-core<0.4,>=0.3->langchain_groq) (2.0.7)\n", "Downloading langchain_groq-0.2.0-py3-none-any.whl (14 kB)\n", "Downloading groq-0.11.0-py3-none-any.whl (106 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m106.5/106.5 kB\u001b[0m \u001b[31m1.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: groq, langchain_groq\n", "Successfully installed groq-0.11.0 langchain_groq-0.2.0\n"]}], "source": ["!pip install langchain_google_genai\n", "!pip install langchain_community\n", "!pip install langchain\n", "!pip install langchain_huggingface\n", "!pip install langchain_groq"]}, {"cell_type": "code", "source": ["from google.colab import userdata\n", "GROQ_API_KEY=userdata.get('GROQ_API_KEY')\n", "import os\n", "os.environ[\"GROQ_API_KEY\"]=GROQ_API_KEY"], "metadata": {"id": "rxKWe5nxCpRj"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from google.colab import userdata\n", "GOOGLE_API_KEY=userdata.get('GOOGLE_API_KEY')\n", "import os\n", "os.environ[\"GOOGLE_API_KEY\"]=GOOGLE_API_KEY"], "metadata": {"id": "gigsz8A_Cswd"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from langchain_google_genai import GoogleGenerativeAIEmbeddings\n", "embeddings = GoogleGenerativeAIEmbeddings(model=\"models/embedding-001\")\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "llm = ChatGoogleGenerativeAI(model=\"gemini-1.0-pro\")"], "metadata": {"id": "-T6Y59BhC_05"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["'''from langchain_huggingface import HuggingFaceEmbeddings\n", "embeddings=HuggingFaceEmbeddings(model_name=\"all-MiniLM-L6-v2\")\n", "from langchain_groq import ChatGroq\n", "import os\n", "llm=ChatGroq(model_name=\"Gemma2-9b-It\")'''"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 424, "referenced_widgets": ["f227a0992bde4673af0fb062aa5b35e8", "0f82ee1dd69942bb9c3b7768619237ca", "6f535b58873248698e94c5bf9cff6a58", "3fbcde1c16644a8083f5ad7141d584a9", "5b3ea04a18f341cab660bb8750548975", "5bbb9df62d394236885e2e2866846581", "9ce089032c6b49f9bf40d637c0efef6e", "37733f7c7f584057b9cf7faa0b0c3760", "c02b648dd77d4ef0af423890c32f2f30", "3c0573e4f8744ddea2654f31cc85ff2d", "a77916c64d1d46b3afe624ad02ca4137", "33b81cf377fb42a585f77e67399e082b", "7fd723a318324a79bf33abfe56d3620d", "918ef72969bb4815a8876164cc56d1fc", "3483fb7f00f04cef9958e370fb9c8a8c", "6da2c67cf3bf4814bcd64dfca244776e", "24721c2aad2c4fa590e5bb486341402a", "e7f09a2c0ff84825867ee6ec25bc10f7", "45f9a0c73f004ef69a76e200309570f4", "5566c6a8f8ff40818436490b9e07173e", "e08b06fb92d8449db0b357558c25d620", "628c5f16be4045ca99d9467fa07bd888", "29b6b20ce1b847309bc9549f208bfe39", "954fe98f3261408aa10491d96f19e272", "0927025465b0492d81409fb71018bd60", "e14d291267454ec7a181ddf6929c33bb", "0b78598e186a4d1980d5196ffd44cde7", "876c972040d24ca8a1e04816afa89866", "8b537c3657de40999e86bfecafc583f9", "9d71e1827f574c9eadc792509f4d17fb", "9101047b368a47418b1af2852dca6309", "248c31eb970f4f08aaab64374420adb4", "7460ad56137c4789bd01ee3770ebb4d4", "e14b79c1eb09499f925c5abc87f9e358", "07c4f947bfe245f4adb3cf375f089939", "d8f943cdce2a4e30a4a47dad04f05d28", "a2114d55fda4451d902c0ef1aa12a428", "2965df6c49044b35b3ba6d2cdc24d74d", "d7c44beb266141d3875b800de14214aa", "423c43f9333c464594d744a6c5d8be8a", "ec1075c521644d3f953a16cbe1f2e030", "2ce57d428da04626be6995389469ab28", "48ca0355d5c740f59db88d4332353ee7", "19789d9a79fd4742af4e1dafee03c1b1", "6076bc5b88e640a39a45107139036090", "7b9ed53198aa46fea9f0875fd07999c5", "708cf9ffbd7c4dfd9bb35581026f12d9", "bdde8937a9a0463388d84a721b95d31f", "a28656b202ea4b75a3dc564616d209f8", "09caf4b59d1a4b579a9a590b748c53b6", "ba06a80754314d20836a87ded5becb99", "f88d5e5fb2734091b1916b578cf4a8b9", "381aa0cbbe4044999bca9df41dd00664", "f9da47d45cbd423e9c74e48e82452b83", "074a6471fdbe40ed9acfbe2f5e949c2f", "e8d779e8ed5a4d659c74aa586c169c57", "ff51e0815d29426d9304a0d781dafb12", "72b6c4c385af44e1bba560296276db76", "211627521efb4a829aebb5e64cdd2115", "2bcfc3e35bd54e0f8eb411e2accaffd4", "f71bb3b2677c43bd8b918346db36be48", "b5526335d665446daf7e5837c8dea650", "2ff329f286054bcaae5fc27cf67ed36f", "fe4a5fec3f0f46eda3d9b1b44a20a4e2", "b264cfc0a5c24aa193d246fbc3de15e5", "110d0c5bef664638b7977f46f0735905", "b3651292353c458099e7e96a8a002096", "e220c44bbc18421d8d147ca77e6788bd", "53cd5af714e6466cacc1dc92d478b775", "d3606dc8cdb141ed8fca43bb6ce043c7", "cdc13486cd4d4b35afa107c79675a19d", "58d206a51602494abfbffab3f862015b", "8fc3256efadb4fb3827e8a88e6acb998", "2e525eb63279444fb86403a0ea77addb", "1a32af7ea56843d982e2d4f794c6dfe8", "85e14014592f492782b195a283ba74ad", "131c46f2ecf6494b97829e9ba56d49ee", "d12085da77d84d0ab0884a36c3b63a8c", "972a61bd9c4d499b938c16a982410ca7", "cad4498ad3f64a9c93065415c36adb29", "4c7a2b0542674b7aaeccce12591528ed", "830add38427f45568eb0700b8136a737", "8eac6546a3af4e1597184bd879817cf7", "b824f2ae1ac04cd78a093d6e196a7f97", "cb1c6c4e740b4a91b4a0f12a5adbe9d7", "12061393e24442a1ac19f59df7c8ed02", "02b9bc0c2f2f4201b6ff2a2412c5e7a7", "18009a48070c44c3a428ebfc174efe15", "d1ae802dba1149ca9e4def59a72ec3ba", "3b252b6cee3c4543bb8919f80eebe288", "56efd36f6e5c4ab29702cf374d442039", "290d595d8e58474faee18e36c503249f", "aecd0585304f43e5aa8086877e41e679", "f408d390719b4587bf89a2e6d4245f39", "b4444854e39f4b3eb9c20553aa509790", "7548118226e74462b1fb7ccf470707dc", "fce994a8f0a742a39d7a009cbcd91db7", "c666a1a873e74e1f87570d25e5839c3b", "fb08084b416f40818b123916cac094de", "0201bd357a964ef28e97bf63947147a0", "9b326f532e5f46dd8c64a37ca7d23bfa", "b95bc3eb580248ec8f31ef90419f9ce1", "316acc82952243649ee78e5bf7357811", "1cd8d4ce444d4bf185bf1ce8668486dc", "487f9f61b45e4365b0ac0dfc7a64eca3", "a5b48868639f494fb1dc7a52ad9a2ab5", "53e3b30a041d4a31938adb1cf6d55c46", "7c8dbffeb2294ad590974f644847a2c3", "9c45a4095bc34f15b4415f58edbb17df", "49125bd1de2b46899a52c69d54e6ace0", "e8e8700a0b45490992fae82f6e04ccf0", "ef43923528a84424bde4490feb99cf6e", "dd26fb9910ba4bffaa5d4db648e951ef", "715c8f7ed318476faf6dd2309530162f", "b15aecf8ca824865934329dd8fb6b232", "d132cbcf72a346c9974b776cd7d55d47", "dc464f3338c2435b84b75db1f8e6e416", "1f29ae9f38694368adc61335125598e0", "367c6767a186473d83432d8f38aba8cb", "c625fadf89a945a098c8cab8c79edaed", "00ddbe52e41b48a890d99c146637f9fc"]}, "id": "PP7O_-5CDLs5", "outputId": "621a1489-08f7-46bd-b1e0-93901ae9b04c"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["modules.json:   0%|          | 0.00/349 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "f227a0992bde4673af0fb062aa5b35e8"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config_sentence_transformers.json:   0%|          | 0.00/116 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "33b81cf377fb42a585f77e67399e082b"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["README.md:   0%|          | 0.00/10.7k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "29b6b20ce1b847309bc9549f208bfe39"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["sentence_bert_config.json:   0%|          | 0.00/53.0 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "e14b79c1eb09499f925c5abc87f9e358"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/612 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "6076bc5b88e640a39a45107139036090"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors:   0%|          | 0.00/90.9M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "e8d779e8ed5a4d659c74aa586c169c57"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json:   0%|          | 0.00/350 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "b3651292353c458099e7e96a8a002096"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["vocab.txt:   0%|          | 0.00/232k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "d12085da77d84d0ab0884a36c3b63a8c"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.json:   0%|          | 0.00/466k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "d1ae802dba1149ca9e4def59a72ec3ba"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["special_tokens_map.json:   0%|          | 0.00/112 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "0201bd357a964ef28e97bf63947147a0"}}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/transformers/tokenization_utils_base.py:1601: FutureWarning: `clean_up_tokenization_spaces` was not set. It will be set to `True` by default. This behavior will be depracted in transformers v4.45, and will be then set to `False` by default. For more details check this issue: https://github.com/huggingface/transformers/issues/31884\n", "  warnings.warn(\n"]}, {"output_type": "display_data", "data": {"text/plain": ["1_Pooling/config.json:   0%|          | 0.00/190 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "e8e8700a0b45490992fae82f6e04ccf0"}}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["# this is my simple chain (old chaining concept)"], "metadata": {"id": "imELHoVZMR53"}}, {"cell_type": "code", "source": ["template= 'Hi! I am learning {skill}. Can you suggest me top 5 things to learn?\\n'"], "metadata": {"id": "pZOC6k1_DSpM"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from langchain import PromptTemplate"], "metadata": {"id": "l6nUqQ7NDf0h"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["prompt = PromptTemplate(template=template,input_variables=[\"skill\"])"], "metadata": {"id": "cqgbDPABDhkL"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(prompt)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "iVMhFTXfDpmz", "outputId": "9d269bdd-2fce-4ef4-aea7-83444fd39597"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["input_variables=['skill'] input_types={} partial_variables={} template='Hi! I am learning {skill}. Can you suggest me top 5 things to learn?\\n'\n"]}]}, {"cell_type": "code", "source": ["from langchain import LLMChain"], "metadata": {"id": "2n2FbzaUDsHx"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["llm_chain = LLMChain(prompt=prompt,llm=llm)"], "metadata": {"id": "bgv6v3kRD1wh"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(llm_chain.run('Data Science'))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "y0vv9CH-EAuR", "outputId": "e77a91e6-e5aa-4388-b500-754f41c4134d"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["**Top 5 Essential Skills for Data Science Learners:**\n", "\n", "1. **Programming Languages:** Python and R are the most popular programming languages for data science. Focus on mastering data manipulation, analysis, and visualization capabilities.\n", "\n", "2. **Statistics and Probability:** Understand fundamental statistical concepts such as probability distributions, hypothesis testing, and regression analysis. These skills are crucial for data analysis and modeling.\n", "\n", "3. **Machine Learning Algorithms:** Explore different machine learning algorithms, including supervised (e.g., linear regression, decision trees) and unsupervised (e.g., clustering, dimensionality reduction) techniques.\n", "\n", "4. **Data Manipulation and Visualization:** Learn to efficiently import, clean, manipulate, and visualize data using libraries like pandas, NumPy, and Matplotlib. Effective data visualization is key for communicating insights.\n", "\n", "5. **Cloud Computing:** Familiarize yourself with cloud platforms like AWS, Azure, or Google Cloud. Cloud computing offers scalable and cost-effective solutions for handling large datasets and computational tasks.\n"]}]}, {"cell_type": "code", "source": ["print(llm_chain.run({'skill':'Data Science'}))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QSlUMR_BELrZ", "outputId": "a5e201d1-f434-4193-a7ee-db7caed30369"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["**Top 5 Things to Learn for Data Science:**\n", "\n", "1. **Programming Language:** Proficiency in a programming language is essential for data manipulation, analysis, and visualization. Python and R are the most popular choices for data science.\n", "\n", "2. **Data Manipulation and Analysis:** Understand how to clean, transform, and explore data using tools like Pandas (Python) or dplyr (R). Learn statistical concepts like descriptive statistics, hypothesis testing, and regression analysis.\n", "\n", "3. **Machine Learning:** Study supervised and unsupervised learning algorithms, such as linear regression, logistic regression, decision trees, and clustering. Understand the principles of model selection, evaluation, and deployment.\n", "\n", "4. **Data Visualization:** Learn to create informative and visually appealing visualizations using libraries like Matplotlib (Python) or ggplot2 (R). This enables effective communication of insights and facilitates data exploration.\n", "\n", "5. **Cloud Computing:** Familiarize yourself with cloud platforms like AWS or Azure. Cloud computing provides scalable and cost-effective resources for data storage, processing, and analysis. It also facilitates collaboration and remote work.\n"]}]}, {"cell_type": "markdown", "source": ["# this is a implementation  using LCEL"], "metadata": {"id": "Sw-ow6nHNIZQ"}}, {"cell_type": "code", "source": ["llm"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "BkHP_36eEXNp", "outputId": "989af42e-3465-4ea3-bf53-3a792bdd8557"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["ChatGoogleGenerativeAI(model='models/gemini-1.0-pro', google_api_key=SecretStr('**********'), client=<google.ai.generativelanguage_v1beta.services.generative_service.client.GenerativeServiceClient object at 0x7b10ed2405e0>, async_client=<google.ai.generativelanguage_v1beta.services.generative_service.async_client.GenerativeServiceAsyncClient object at 0x7b10ed2418d0>, default_metadata=())"]}, "metadata": {}, "execution_count": 19}]}, {"cell_type": "code", "source": ["prompt"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "J-SyysJqMd5T", "outputId": "a7c01f22-7c96-491d-ee4f-43cbfb7c312f"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["PromptTemplate(input_variables=['skill'], input_types={}, partial_variables={}, template='Hi! I am learning {skill}. Can you suggest me top 5 things to learn?\\n')"]}, "metadata": {}, "execution_count": 20}]}, {"cell_type": "code", "source": ["chain = prompt | llm"], "metadata": {"id": "0x3jF1zHMgrT"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(chain.invoke({'skill':'Big Data'}))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "HlWhjIxJMlMz", "outputId": "718b5767-42b4-45a1-d663-de5d1ec77080"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["content='**Top 5 Essential Concepts for Big Data:**\\n\\n1. **Big Data Frameworks:** Understand the fundamentals of Hadoop, Spark, and other popular frameworks used for processing and managing large datasets.\\n2. **Data Ingestion and Storage:** Learn the techniques for importing and storing data from various sources, including structured, semi-structured, and unstructured data.\\n3. **Data Analysis and Visualization:** Explore methods for analyzing big data, including statistical techniques, machine learning algorithms, and data visualization tools.\\n4. **Data Management and Security:** Understand the principles of data management, data governance, and data security in a big data environment.\\n5. **Cloud Computing for Big Data:** Familiarize yourself with the role of cloud platforms, such as AWS, Azure, and GCP, in supporting big data storage, processing, and analysis.' additional_kwargs={} response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'safety_ratings': [{'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE', 'blocked': False}]} id='run-6e7a956f-de9a-410c-8015-789952d62d96-0' usage_metadata={'input_tokens': 21, 'output_tokens': 168, 'total_tokens': 189}\n"]}]}, {"cell_type": "code", "source": ["from langchain_core.output_parsers import StrOutputParser"], "metadata": {"id": "D1g1TnedM08c"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["parser = StrOutputParser()"], "metadata": {"id": "7hLevRtnM4v1"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chain = prompt | llm | parser"], "metadata": {"id": "Y8m8mq1wM7wj"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(chain.invoke({'skill':'Machine Learning'}))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "rQ4mSKFgM_rM", "outputId": "d1e81fcf-175e-4efc-d103-718a5a74401c"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["**Top 5 Essential Concepts to Learn in Machine Learning:**\n", "\n", "1. **Supervised Learning:** Understanding how algorithms learn from labeled data and predict outcomes. This includes concepts like linear regression, logistic regression, and decision trees.\n", "\n", "2. **Unsupervised Learning:** Exploring algorithms that learn from unlabeled data to find patterns and structures. This encompasses methods like clustering, dimensionality reduction, and anomaly detection.\n", "\n", "3. **Model Evaluation:** Mastering techniques to assess the performance of machine learning models, such as accuracy, precision, recall, and confusion matrices.\n", "\n", "4. **Feature Engineering:** Learning how to transform and select the most informative features from raw data to improve model performance. This involves data cleaning, normalization, and feature selection techniques.\n", "\n", "5. **Model Tuning and Optimization:** Understanding how to adjust model parameters and hyperparameters to enhance its predictive capabilities. This includes methods like cross-validation, regularization, and gradient descent.\n"]}]}, {"cell_type": "markdown", "source": ["# lets discuss about the runnables"], "metadata": {"id": "99PwfyAjNNpv"}}, {"cell_type": "code", "source": ["from langchain_core.runnables import RunnableParallel, RunnablePassthrough , RunnableLambda"], "metadata": {"id": "6o2eHdFXNDhT"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chain = RunnablePassthrough()"], "metadata": {"id": "thIShr5mNhm_"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chain.invoke('Welcome to this youtube channel')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "AQRkFFeMNmLb", "outputId": "8999112b-e24b-4d5a-b053-4229ec0f5ee0"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Welcome to this youtube channel'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 29}]}, {"cell_type": "code", "source": ["chain = RunnablePassthrough() | RunnablePassthrough() | RunnablePassthrough()"], "metadata": {"id": "uZ_-Vw1RNzls"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chain.invoke('Welcome to my sunny\"s youtube channel')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "G1TYVRLYN83A", "outputId": "9784600c-fa77-4ff5-9745-3b762d6178ff"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Welcome to my sunny\"s youtube channel'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 35}]}, {"cell_type": "code", "source": ["def string_upper(input):\n", "  return input.upper()"], "metadata": {"id": "fPpAEYVJORUf"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chain = RunnablePassthrough() | RunnableLambda(string_upper)"], "metadata": {"id": "RkrgncYDN-ej"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chain.invoke('Welcome to my sunny\"s youtube channel')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "TfkJXhuKOYGj", "outputId": "59974653-d974-4513-b418-837231482548"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'WELCOME TO MY SUNNY\"S YOUTUBE CHANNEL'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 43}]}, {"cell_type": "code", "source": ["string_upper.invoke('Welcome to my sunny\"s youtube channel')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 141}, "id": "hbipHFRNOm3A", "outputId": "f4655d86-4682-416e-cf2f-5e078e298f62"}, "execution_count": null, "outputs": [{"output_type": "error", "ename": "AttributeError", "evalue": "'function' object has no attribute 'invoke'", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-44-9ecdd511418b>\u001b[0m in \u001b[0;36m<cell line: 1>\u001b[0;34m()\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mstring_upper\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0minvoke\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'Welcome to my sunny\"s youtube channel'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m: 'function' object has no attribute 'invoke'"]}]}, {"cell_type": "code", "source": ["chain = RunnableLambda(string_upper)"], "metadata": {"id": "dytKiiXVOm5O"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chain.invoke('Welcome to my sunny\"s youtube channel')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "d9LJwI12PAi1", "outputId": "263e35f7-4ab8-489f-8dfe-8183cdea32f3"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'WELCOME TO MY SUNNY\"S YOUTUBE CHANNEL'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 46}]}, {"cell_type": "code", "source": ["chain = RunnableParallel({'x':RunnablePassthrough(),'y':RunnablePassthrough()})"], "metadata": {"id": "3ZGkNnXjPEWU"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chain.invoke(\"<PERSON>\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9aBTG_OsPPA8", "outputId": "b1ea167d-e1b9-4c25-fe31-0858854cf4bc"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'x': '<PERSON>', 'y': '<PERSON>'}"]}, "metadata": {}, "execution_count": 48}]}, {"cell_type": "code", "source": ["chain.invoke({'Youtube': '@sunnysavita10','Blog': \"<PERSON>'s blog\"})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FSP5r-qxPSjU", "outputId": "3f3431bb-1255-42e0-fb48-64c0079666eb"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'x': {'Youtube': '@sunnysavita10', 'Blog': \"<PERSON>'s blog\"},\n", " 'y': {'Youtube': '@sunnysavita10', 'Blog': \"<PERSON>'s blog\"}}"]}, "metadata": {}, "execution_count": 49}]}, {"cell_type": "code", "source": ["lambda x: x['Blog']"], "metadata": {"id": "wmX6hCbPTJrI"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chain = RunnableParallel({'x':RunnablePassthrough(),'Blog':lambda x: x['Blog']})"], "metadata": {"id": "xD3lHrcIQazJ"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chain.invoke({'Youtube': '@sunnysavita10','Blog': \"<PERSON>'s blog\"})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3hM3gBqtQcCa", "outputId": "665da4fd-07b5-4d97-d70d-1d630b4df5f9"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'x': {'Youtube': '@sunnysavita10', 'Blog': \"<PERSON>'s blog\"},\n", " 'Blog': \"<PERSON>'s blog\"}"]}, "metadata": {}, "execution_count": 51}]}, {"cell_type": "code", "source": ["def fetch_website(input: dict):\n", "    output = input.get('Website','Not found')\n", "    return output"], "metadata": {"id": "U4SUTxQ0QdUW"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["mydict={'Youtube': '@sunnysavita10','Blog': \"<PERSON>'s blog\"}"], "metadata": {"id": "15vR9qJzTs5M"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["mydict.get(\"website\",\"Not found\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "DChnkIoFTwpn", "outputId": "554609b8-8c43-4dcd-b11d-4508af64d145"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Not found'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 60}]}, {"cell_type": "code", "source": ["chain = RunnableParallel({'Website':RunnablePassthrough() | RunnableLambda(fetch_website),\n", "                          'Blog':lambda z: z['Blog']})"], "metadata": {"id": "r4ApiixHQe0n"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chain.invoke({'Youtube': '@sunnysavita10','Blog': \"<PERSON>'s blog\"})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7tSnqZ9-QgCI", "outputId": "172f2641-4016-4191-bcc2-a0ee696700fd"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'Website': 'Not found', 'Blog': \"<PERSON>'s blog\"}"]}, "metadata": {}, "execution_count": 55}]}, {"cell_type": "code", "source": ["chain.invoke({'Youtube': '@sunnysavita10','Blog': \"<PERSON>'s blog\" , 'Website' : 'sunnysavita.com'})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "P9UWY3o2QhcI", "outputId": "6536bcbc-28c3-4802-a672-264f50b3e13f"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'Website': 'sunnysavita.com', 'Blog': \"<PERSON>'s blog\"}"]}, "metadata": {}, "execution_count": 61}]}, {"cell_type": "code", "source": ["def extra_func(input):\n", "    return 'Happy Learning'"], "metadata": {"id": "4xKCFIioQi8Y"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chain = RunnableParallel({'x' : RunnablePassthrough()}).assign(extra=RunnableLambda(extra_func))"], "metadata": {"id": "ymBvP7r9QkTv"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chain = RunnableParallel({'x' : RunnablePassthrough()}).assign(y=RunnableLambda(extra_func))"], "metadata": {"id": "W6ZoETBaUf4f"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chain.invoke('Hello')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cIYfRydBQliP", "outputId": "1fa3bb6c-c429-4a48-e353-9674a2ab7c9d"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'x': 'Hello', 'y': 'Happy Learning'}"]}, "metadata": {}, "execution_count": 66}]}, {"cell_type": "code", "source": ["!pip install chromadb"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "NfjIKZsvUZR2", "outputId": "61a639fc-65c7-4d83-bba8-5795271e99c5"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting chromadb\n", "  Downloading chromadb-0.5.5-py3-none-any.whl.metadata (6.8 kB)\n", "Requirement already satisfied: build>=1.0.3 in /usr/local/lib/python3.10/dist-packages (from chromadb) (1.2.2)\n", "Requirement already satisfied: pydantic>=1.9 in /usr/local/lib/python3.10/dist-packages (from chromadb) (2.9.1)\n", "Collecting chroma-hnswlib==0.7.6 (from chromadb)\n", "  Downloading chroma_hnswlib-0.7.6-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (252 bytes)\n", "Collecting fastapi>=0.95.2 (from chromadb)\n", "  Downloading fastapi-0.114.2-py3-none-any.whl.metadata (27 kB)\n", "Collecting uvicorn>=0.18.3 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading uvicorn-0.30.6-py3-none-any.whl.metadata (6.6 kB)\n", "Requirement already satisfied: numpy<2.0.0,>=1.22.5 in /usr/local/lib/python3.10/dist-packages (from chromadb) (1.26.4)\n", "Collecting posthog>=2.4.0 (from chromadb)\n", "  Downloading posthog-3.6.6-py2.py3-none-any.whl.metadata (2.0 kB)\n", "Requirement already satisfied: typing-extensions>=4.5.0 in /usr/local/lib/python3.10/dist-packages (from chromadb) (4.12.2)\n", "Collecting onnxruntime>=1.14.1 (from chromadb)\n", "  Downloading onnxruntime-1.19.2-cp310-cp310-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl.metadata (4.5 kB)\n", "Collecting opentelemetry-api>=1.2.0 (from chromadb)\n", "  Downloading opentelemetry_api-1.27.0-py3-none-any.whl.metadata (1.4 kB)\n", "Collecting opentelemetry-exporter-otlp-proto-grpc>=1.2.0 (from chromadb)\n", "  Downloading opentelemetry_exporter_otlp_proto_grpc-1.27.0-py3-none-any.whl.metadata (2.3 kB)\n", "Collecting opentelemetry-instrumentation-fastapi>=0.41b0 (from chromadb)\n", "  Downloading opentelemetry_instrumentation_fastapi-0.48b0-py3-none-any.whl.metadata (2.1 kB)\n", "Collecting opentelemetry-sdk>=1.2.0 (from chromadb)\n", "  Downloading opentelemetry_sdk-1.27.0-py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: tokenizers>=0.13.2 in /usr/local/lib/python3.10/dist-packages (from chromadb) (0.19.1)\n", "Collecting pypika>=0.48.9 (from chromadb)\n", "  Downloading PyPika-0.48.9.tar.gz (67 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.3/67.3 kB\u001b[0m \u001b[31m1.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: tqdm>=4.65.0 in /usr/local/lib/python3.10/dist-packages (from chromadb) (4.66.5)\n", "Collecting overrides>=7.3.1 (from chromadb)\n", "  Downloading overrides-7.7.0-py3-none-any.whl.metadata (5.8 kB)\n", "Requirement already satisfied: importlib-resources in /usr/local/lib/python3.10/dist-packages (from chromadb) (6.4.5)\n", "Requirement already satisfied: grpcio>=1.58.0 in /usr/local/lib/python3.10/dist-packages (from chromadb) (1.64.1)\n", "Collecting bcrypt>=4.0.1 (from chromadb)\n", "  Downloading bcrypt-4.2.0-cp39-abi3-manylinux_2_28_x86_64.whl.metadata (9.6 kB)\n", "Requirement already satisfied: typer>=0.9.0 in /usr/local/lib/python3.10/dist-packages (from chromadb) (0.12.5)\n", "Collecting kubernetes>=28.1.0 (from chromadb)\n", "  Downloading kubernetes-30.1.0-py2.py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: tenacity>=8.2.3 in /usr/local/lib/python3.10/dist-packages (from chromadb) (8.5.0)\n", "Requirement already satisfied: PyYAML>=6.0.0 in /usr/local/lib/python3.10/dist-packages (from chromadb) (6.0.2)\n", "Collecting mmh3>=4.0.1 (from chromadb)\n", "  Downloading mmh3-4.1.0-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (13 kB)\n", "Requirement already satisfied: orjson>=3.9.12 in /usr/local/lib/python3.10/dist-packages (from chromadb) (3.10.7)\n", "Requirement already satisfied: httpx>=0.27.0 in /usr/local/lib/python3.10/dist-packages (from chromadb) (0.27.2)\n", "Requirement already satisfied: packaging>=19.1 in /usr/local/lib/python3.10/dist-packages (from build>=1.0.3->chromadb) (24.1)\n", "Requirement already satisfied: pyproject_hooks in /usr/local/lib/python3.10/dist-packages (from build>=1.0.3->chromadb) (1.1.0)\n", "Requirement already satisfied: tomli>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from build>=1.0.3->chromadb) (2.0.1)\n", "Collecting starlette<0.39.0,>=0.37.2 (from fastapi>=0.95.2->chromadb)\n", "  Downloading starlette-0.38.5-py3-none-any.whl.metadata (6.0 kB)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->chromadb) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->chromadb) (2024.8.30)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->chromadb) (1.0.5)\n", "Requirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->chromadb) (3.8)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->chromadb) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx>=0.27.0->chromadb) (0.14.0)\n", "Requirement already satisfied: six>=1.9.0 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb) (1.16.0)\n", "Requirement already satisfied: python-dateutil>=2.5.3 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb) (2.8.2)\n", "Requirement already satisfied: google-auth>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb) (2.27.0)\n", "Requirement already satisfied: websocket-client!=0.40.0,!=0.41.*,!=0.42.*,>=0.32.0 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb) (1.8.0)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb) (2.32.3)\n", "Requirement already satisfied: requests-oauthlib in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb) (1.3.1)\n", "Requirement already satisfied: oauthlib>=3.2.2 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb) (3.2.2)\n", "Requirement already satisfied: urllib3>=1.24.2 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb) (2.0.7)\n", "Collecting coloredlogs (from onnxruntime>=1.14.1->chromadb)\n", "  Downloading coloredlogs-15.0.1-py2.py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: flatbuffers in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.14.1->chromadb) (24.3.25)\n", "Requirement already satisfied: protobuf in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.14.1->chromadb) (3.20.3)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.14.1->chromadb) (1.13.2)\n", "Collecting deprecated>=1.2.6 (from opentelemetry-api>=1.2.0->chromadb)\n", "  Downloading Deprecated-1.2.14-py2.py3-none-any.whl.metadata (5.4 kB)\n", "Collecting importlib-metadata<=8.4.0,>=6.0 (from opentelemetry-api>=1.2.0->chromadb)\n", "  Downloading importlib_metadata-8.4.0-py3-none-any.whl.metadata (4.7 kB)\n", "Requirement already satisfied: googleapis-common-protos~=1.52 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb) (1.65.0)\n", "Collecting opentelemetry-exporter-otlp-proto-common==1.27.0 (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb)\n", "  Downloading opentelemetry_exporter_otlp_proto_common-1.27.0-py3-none-any.whl.metadata (1.8 kB)\n", "Collecting opentelemetry-proto==1.27.0 (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb)\n", "  Downloading opentelemetry_proto-1.27.0-py3-none-any.whl.metadata (2.3 kB)\n", "Collecting opentelemetry-instrumentation-asgi==0.48b0 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb)\n", "  Downloading opentelemetry_instrumentation_asgi-0.48b0-py3-none-any.whl.metadata (2.0 kB)\n", "Collecting opentelemetry-instrumentation==0.48b0 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb)\n", "  Downloading opentelemetry_instrumentation-0.48b0-py3-none-any.whl.metadata (6.1 kB)\n", "Collecting opentelemetry-semantic-conventions==0.48b0 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb)\n", "  Downloading opentelemetry_semantic_conventions-0.48b0-py3-none-any.whl.metadata (2.4 kB)\n", "Collecting opentelemetry-util-http==0.48b0 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb)\n", "  Downloading opentelemetry_util_http-0.48b0-py3-none-any.whl.metadata (2.5 kB)\n", "Requirement already satisfied: setuptools>=16.0 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-instrumentation==0.48b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb) (71.0.4)\n", "Requirement already satisfied: wrapt<2.0.0,>=1.0.0 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-instrumentation==0.48b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb) (1.16.0)\n", "Collecting asgiref~=3.0 (from opentelemetry-instrumentation-asgi==0.48b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb)\n", "  Downloading asgiref-3.8.1-py3-none-any.whl.metadata (9.3 kB)\n", "Collecting monotonic>=1.5 (from posthog>=2.4.0->chromadb)\n", "  Downloading monotonic-1.6-py2.py3-none-any.whl.metadata (1.5 kB)\n", "Collecting backoff>=1.10.0 (from posthog>=2.4.0->chromadb)\n", "  Downloading backoff-2.2.1-py3-none-any.whl.metadata (14 kB)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.9->chromadb) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.23.3 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.9->chromadb) (2.23.3)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.16.4 in /usr/local/lib/python3.10/dist-packages (from tokenizers>=0.13.2->chromadb) (0.24.7)\n", "Requirement already satisfied: click>=8.0.0 in /usr/local/lib/python3.10/dist-packages (from typer>=0.9.0->chromadb) (8.1.7)\n", "Requirement already satisfied: shellingham>=1.3.0 in /usr/local/lib/python3.10/dist-packages (from typer>=0.9.0->chromadb) (1.5.4)\n", "Requirement already satisfied: rich>=10.11.0 in /usr/local/lib/python3.10/dist-packages (from typer>=0.9.0->chromadb) (13.8.1)\n", "Collecting httptools>=0.5.0 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading httptools-0.6.1-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.6 kB)\n", "Requirement already satisfied: python-dotenv>=0.13 in /usr/local/lib/python3.10/dist-packages (from uvicorn[standard]>=0.18.3->chromadb) (1.0.1)\n", "Collecting uvloop!=0.15.0,!=0.15.1,>=0.14.0 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading uvloop-0.20.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)\n", "Collecting watchfiles>=0.13 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading watchfiles-0.24.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)\n", "Collecting websockets>=10.4 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading websockets-13.0.1-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.7 kB)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb) (5.5.0)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb) (0.4.1)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb) (4.9)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb) (3.16.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb) (2024.6.1)\n", "Requirement already satisfied: zipp>=0.5 in /usr/local/lib/python3.10/dist-packages (from importlib-metadata<=8.4.0,>=6.0->opentelemetry-api>=1.2.0->chromadb) (3.20.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->kubernetes>=28.1.0->chromadb) (3.3.2)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /usr/local/lib/python3.10/dist-packages (from rich>=10.11.0->typer>=0.9.0->chromadb) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /usr/local/lib/python3.10/dist-packages (from rich>=10.11.0->typer>=0.9.0->chromadb) (2.16.1)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx>=0.27.0->chromadb) (1.2.2)\n", "Collecting humanfriendly>=9.1 (from coloredlogs->onnxruntime>=1.14.1->chromadb)\n", "  Downloading humanfriendly-10.0-py2.py3-none-any.whl.metadata (9.2 kB)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from sympy->onnxruntime>=1.14.1->chromadb) (1.3.0)\n", "Requirement already satisfied: mdurl~=0.1 in /usr/local/lib/python3.10/dist-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer>=0.9.0->chromadb) (0.1.2)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth>=1.0.1->kubernetes>=28.1.0->chromadb) (0.6.1)\n", "Downloading chromadb-0.5.5-py3-none-any.whl (584 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m584.3/584.3 kB\u001b[0m \u001b[31m5.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading chroma_hnswlib-0.7.6-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.4/2.4 MB\u001b[0m \u001b[31m21.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading bcrypt-4.2.0-cp39-abi3-manylinux_2_28_x86_64.whl (273 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m273.8/273.8 kB\u001b[0m \u001b[31m21.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading fastapi-0.114.2-py3-none-any.whl (94 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m94.0/94.0 kB\u001b[0m \u001b[31m8.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading kubernetes-30.1.0-py2.py3-none-any.whl (1.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.7/1.7 MB\u001b[0m \u001b[31m46.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mmh3-4.1.0-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (67 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.6/67.6 kB\u001b[0m \u001b[31m6.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading onnxruntime-1.19.2-cp310-cp310-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (13.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.2/13.2 MB\u001b[0m \u001b[31m39.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_api-1.27.0-py3-none-any.whl (63 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m64.0/64.0 kB\u001b[0m \u001b[31m5.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_exporter_otlp_proto_grpc-1.27.0-py3-none-any.whl (18 kB)\n", "Downloading opentelemetry_exporter_otlp_proto_common-1.27.0-py3-none-any.whl (17 kB)\n", "Downloading opentelemetry_proto-1.27.0-py3-none-any.whl (52 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m52.5/52.5 kB\u001b[0m \u001b[31m3.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_instrumentation_fastapi-0.48b0-py3-none-any.whl (11 kB)\n", "Downloading opentelemetry_instrumentation-0.48b0-py3-none-any.whl (29 kB)\n", "Downloading opentelemetry_instrumentation_asgi-0.48b0-py3-none-any.whl (15 kB)\n", "Downloading opentelemetry_semantic_conventions-0.48b0-py3-none-any.whl (149 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m149.7/149.7 kB\u001b[0m \u001b[31m11.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_util_http-0.48b0-py3-none-any.whl (6.9 kB)\n", "Downloading opentelemetry_sdk-1.27.0-py3-none-any.whl (110 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m110.5/110.5 kB\u001b[0m \u001b[31m9.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading overrides-7.7.0-py3-none-any.whl (17 kB)\n", "Downloading posthog-3.6.6-py2.py3-none-any.whl (54 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m54.3/54.3 kB\u001b[0m \u001b[31m3.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading uvicorn-0.30.6-py3-none-any.whl (62 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m62.8/62.8 kB\u001b[0m \u001b[31m4.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading backoff-2.2.1-py3-none-any.whl (15 kB)\n", "Downloading Deprecated-1.2.14-py2.py3-none-any.whl (9.6 kB)\n", "Downloading httptools-0.6.1-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (341 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m341.4/341.4 kB\u001b[0m \u001b[31m25.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading importlib_metadata-8.4.0-py3-none-any.whl (26 kB)\n", "Downloading monotonic-1.6-py2.py3-none-any.whl (8.2 kB)\n", "Downloading starlette-0.38.5-py3-none-any.whl (71 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m71.4/71.4 kB\u001b[0m \u001b[31m5.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading uvloop-0.20.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.4/3.4 MB\u001b[0m \u001b[31m66.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading watchfiles-0.24.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (425 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m425.7/425.7 kB\u001b[0m \u001b[31m27.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading websockets-13.0.1-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (157 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m157.3/157.3 kB\u001b[0m \u001b[31m10.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading coloredlogs-15.0.1-py2.py3-none-any.whl (46 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.0/46.0 kB\u001b[0m \u001b[31m3.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading asgiref-3.8.1-py3-none-any.whl (23 kB)\n", "Downloading humanfriendly-10.0-py2.py3-none-any.whl (86 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.8/86.8 kB\u001b[0m \u001b[31m7.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hBuilding wheels for collected packages: pypika\n", "  Building wheel for pypika (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for pypika: filename=PyPika-0.48.9-py2.py3-none-any.whl size=53723 sha256=36236bcdb400e9a4022eae33e6c6df659e25e430249762add428f2dd895fd9ec\n", "  Stored in directory: /root/.cache/pip/wheels/e1/26/51/d0bffb3d2fd82256676d7ad3003faea3bd6dddc9577af665f4\n", "Successfully built pypika\n", "Installing collected packages: pypika, monotonic, mmh3, websockets, uvloop, uvicorn, overrides, opentelemetry-util-http, opentelemetry-proto, importlib-metadata, humanfriendly, httptools, deprecated, chroma-hnswlib, bcrypt, backoff, asgiref, watchfiles, starlette, posthog, opentelemetry-exporter-otlp-proto-common, opentelemetry-api, coloredlogs, opentelemetry-semantic-conventions, opentelemetry-instrumentation, onnxruntime, kubernetes, fastapi, opentelemetry-sdk, opentelemetry-instrumentation-asgi, opentelemetry-instrumentation-fastapi, opentelemetry-exporter-otlp-proto-grpc, chromadb\n", "  Attempting uninstall: importlib-metadata\n", "    Found existing installation: importlib_metadata 8.5.0\n", "    Uninstalling importlib_metadata-8.5.0:\n", "      Successfully uninstalled importlib_metadata-8.5.0\n", "Successfully installed asgiref-3.8.1 backoff-2.2.1 bcrypt-4.2.0 chroma-hnswlib-0.7.6 chromadb-0.5.5 coloredlogs-15.0.1 deprecated-1.2.14 fastapi-0.114.2 httptools-0.6.1 humanfriendly-10.0 importlib-metadata-8.4.0 kubernetes-30.1.0 mmh3-4.1.0 monotonic-1.6 onnxruntime-1.19.2 opentelemetry-api-1.27.0 opentelemetry-exporter-otlp-proto-common-1.27.0 opentelemetry-exporter-otlp-proto-grpc-1.27.0 opentelemetry-instrumentation-0.48b0 opentelemetry-instrumentation-asgi-0.48b0 opentelemetry-instrumentation-fastapi-0.48b0 opentelemetry-proto-1.27.0 opentelemetry-sdk-1.27.0 opentelemetry-semantic-conventions-0.48b0 opentelemetry-util-http-0.48b0 overrides-7.7.0 posthog-3.6.6 pypika-0.48.9 starlette-0.38.5 uvicorn-0.30.6 uvloop-0.20.0 watchfiles-0.24.0 websockets-13.0.1\n"]}]}, {"cell_type": "code", "source": ["from langchain_community.document_loaders import TextLoader, DirectoryLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_community.vectorstores import Chroma\n", "\n", "### Reading the txt files from source directory\n", "\n", "loader = DirectoryLoader('./source', glob=\"./*.txt\", loader_cls=TextLoader)\n", "docs = loader.load()\n", "\n", "### Creating Chunks using RecursiveCharacterTextSplitter\n", "\n", "text_splitter = RecursiveCharacterTextSplitter(\n", "    chunk_size=50,\n", "    chunk_overlap=10,\n", "    length_function=len\n", ")\n", "new_docs = text_splitter.split_documents(documents=docs)\n", "doc_strings = [doc.page_content for doc in new_docs]\n", "\n", "###  <PERSON><PERSON> Embddings\n", "\n", "'''from langchain.embeddings import HuggingFaceBgeEmbeddings\n", "\n", "model_name = \"BAAI/bge-base-en-v1.5\"\n", "model_kwargs = {'device': 'cpu'}\n", "encode_kwargs = {'normalize_embeddings': True} # set True to compute cosine similarity\n", "embeddings = HuggingFaceBgeEmbeddings(\n", "    model_name=model_name,\n", "    model_kwargs=model_kwargs,\n", "    encode_kwargs=encode_kwargs,\n", ")\n", "'''\n", "\n", "### Creating Retriever using Vector DB\n", "\n", "db = Chroma.from_documents(new_docs, embeddings)\n", "retriever = db.as_retriever(search_kwargs={\"k\": 4})"], "metadata": {"id": "oxWNmfPJUrZs"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["template = \"\"\"Answer the question based only on the following context:\n", "{context}\n", "\n", "Question: {question}\n", "\"\"\"\n", "prompt = PromptTemplate.from_template(template)\n"], "metadata": {"id": "mRV0rT9bVRqo"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["retrieval_chain = (\n", "    RunnableParallel({\"context\": retriever, \"question\": RunnablePassthrough()})\n", "    | prompt\n", "    | llm\n", "    | StrOutputParser()\n", "    )"], "metadata": {"id": "gRn_Zn3fUusE"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["question =\"what is llama3? can you highlight 3 important points?\""], "metadata": {"id": "xH0TxuFLU06x"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["retrieval_chain.invoke(question)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 53}, "id": "lQbsuB43Vc-L", "outputId": "0a35d312-2949-4368-856c-d89d4e729da3"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'- Llama 3 is a large language model developed by Meta AI.\\n- It was released in April 2024.\\n- It is used in various applications, including language translation, question answering, and text generation.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 75}]}, {"cell_type": "code", "source": ["import time\n", "\n", "start_time = time.time()\n", "\n", "result = retrieval_chain.invoke(question)\n", "\n", "print('Time taken:',time.time() - start_time)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kHqEw55uVgVd", "outputId": "37a7e6e7-2c1d-40f0-a352-59b14c01095b"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Time taken: 1.6216003894805908\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Exception ignored in: <coroutine object RunnableSequence.ainvoke at 0x7b0fd7ee3c30>\n", "Traceback (most recent call last):\n", "  File \"/usr/local/lib/python3.10/dist-packages/google/colab/_variable_inspector.py\", line 27, in run\n", "KeyError: '__builtins__'\n", "Exception ignored in: <coroutine object RunnableSequence.ainvoke at 0x7b0fd7ee3c30>\n", "Traceback (most recent call last):\n", "  File \"/usr/local/lib/python3.10/dist-packages/google/colab/_variable_inspector.py\", line 27, in run\n", "KeyError: '__builtins__'\n"]}]}, {"cell_type": "code", "source": ["start_time = time.time()\n", "\n", "result = retrieval_chain.ainvoke(question)\n", "\n", "print('Time taken:',time.time() - start_time)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Tk71kT4ZVnrT", "outputId": "6d2de3fd-57b3-4a66-9057-5be9fbabc25d"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Time taken: 0.00014090538024902344\n"]}]}, {"cell_type": "code", "source": ["start_time = time.time()\n", "\n", "batch_output = retrieval_chain.batch([\n", "                        \"what is llama3?\",\n", "                        \"can you highlight 3 main properties?\"\n", "                       ])\n", "\n", "print('Time taken:',time.time() - start_time)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2bjWjZHKV4WS", "outputId": "b92165ae-7cd2-4b15-eb39-8fc2d325a30f"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Time taken: 0.9684896469116211\n"]}]}, {"cell_type": "code", "source": ["batch_output"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YlEAL3OPV55X", "outputId": "5144a8bc-0a40-484d-d17b-a301c3d1bc0b"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['Llama 3 is a family of large language models developed by Meta AI.',\n", " 'The provided context does not mention anything about the main properties, so I cannot extract the requested data from the provided context.']"]}, "metadata": {}, "execution_count": 84}]}, {"cell_type": "code", "source": ["start_time = time.time()\n", "\n", "batch_output = await retrieval_chain.abatch([\n", "                        \"what is llama3?\",\n", "                        \"can you highlight 3 main properties?\"\n", "                       ])\n", "\n", "print('Time taken:',time.time() - start_time)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "A0Odh6PiV7i4", "outputId": "0d03839c-6f1c-4938-9936-5b0c99ebed2f"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Time taken: 0.9242796897888184\n"]}]}, {"cell_type": "code", "source": ["batch_output"], "metadata": {"id": "uye5uA4SV83i"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["my_dict = {'Youtube': '@sunnysavita10','Blog': \"sunny's blog\" , 'Website' : 'sunnysavita.com'}\n", "my_dict"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "im5Guk7eWDwl", "outputId": "86fb4bcc-e6cd-493f-dbdf-a0bcc10a449c"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'Youtube': '@sunnysavita10',\n", " 'Blog': \"<PERSON>'s blog\",\n", " 'Website': 'sunnysavita.com'}"]}, "metadata": {}, "execution_count": 90}]}, {"cell_type": "code", "source": ["from operator import itemgetter\n", "\n", "website = itemgetter('Website')"], "metadata": {"id": "tZ4vG0XaWFbB"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["website(my_dict)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "zEPPAK3yWGiw", "outputId": "a3cabbb1-66d0-4b0e-b919-9a1006a6c434"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'sunnysavita.com'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 92}]}, {"cell_type": "code", "source": ["template = \"\"\"Answer the question based only on the following context:\n", "{context}\n", "\n", "Question: {question}\n", "\n", "Answer in the following language: {language}\n", "\"\"\"\n", "prompt = PromptTemplate.from_template(template)\n"], "metadata": {"id": "gvKpRIcHYQeh"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["retrieval_chain = (\n", "    RunnableParallel({\"context\": itemgetter('question') | retriever,\n", "                       \"question\": itemgetter('question'),\n", "                       \"language\": itemgetter('language')\n", "                       })\n", "    | prompt\n", "    | llm\n", "    | StrOutputParser()\n", "    )"], "metadata": {"id": "lhl9vll7WIEY"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["### itemgetter only works with dictionaries , input has to be a dict\n", "\n", "response = retrieval_chain.invoke({'question': \"what is llama3?\",\n", "                        'language': \"Spnish\"})\n", "\n", "print(response)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "73Rl5KYZWJCh", "outputId": "596b6ccf-bf12-45f4-c6a6-c628c87a5710"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Llama (Large Language Model Meta AI) es una familia\n"]}]}, {"cell_type": "code", "source": ["template = 'Hi! I am learning {skill}. Can you suggest me top 5 things to learn?\\n'\n", "\n", "prompt = PromptTemplate.from_template(template=template)\n", "\n", "chain = prompt | llm"], "metadata": {"id": "EDevsLI3WL8w"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["for s in chain.stream({'skill':'Big Data'}):\n", "    print(s.content,end='')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pJv0bVtUWNCG", "outputId": "c2679e08-6dcc-43af-e29a-898a93c0cd4e"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["**Top 5 Must-Learn Concepts for Big Data:**\n", "\n", "1. **Data Management and Storage:** Understand various data storage technologies like Hadoop Distributed File System (HDFS), NoSQL databases (e.g., MongoDB, Cassandra), and data warehousing concepts.\n", "\n", "2. **Data Processing and Analysis:** Learn about frameworks like Apache Spark, MapReduce, and Hadoop for processing and analyzing large datasets. Explore data mining, machine learning, and statistical techniques for extracting insights.\n", "\n", "3. **Cloud Computing:** Familiarize yourself with cloud platforms like AWS, Azure, and GCP that offer scalable and cost-effective solutions for storing, processing, and analyzing big data.\n", "\n", "4. **Data Visualization and Communication:** Gain expertise in tools like Tableau, Power BI, and QlikView to effectively communicate data insights to stakeholders. Learn about data visualization best practices and storytelling techniques.\n", "\n", "5. **Data Governance and Security:** Understand data governance principles, data quality management, and security measures to protect sensitive data in big data environments. Learn about compliance regulations and best practices for data privacy and security."]}]}, {"cell_type": "code", "source": ["import json\n", "from langchain_core.messages import ToolMessage\n", "from langchain_core.tools import tool\n", "from langchain_core.utils.function_calling import convert_to_openai_tool\n", "\n", "@tool\n", "def multiply(first_number: int, second_number: int):\n", "    \"\"\"Multiplies two numbers together.\"\"\"\n", "    return first_number * second_number\n", "\n", "model_with_tools = llm.bind(tools=[convert_to_openai_tool(multiply)])"], "metadata": {"id": "bpmGR1mbWPdp"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["response = model_with_tools.invoke('What is 35 * 46?')"], "metadata": {"id": "mMG5ND6rWRDr"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["response"], "metadata": {"id": "PijeN4AUWSFN"}, "execution_count": null, "outputs": []}]}