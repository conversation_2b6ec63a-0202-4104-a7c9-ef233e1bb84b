{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU", "widgets": {"application/vnd.jupyter.widget-state+json": {"8e035199e06b40eabbe34b3852c53034": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_75777df2725d4509adaddc144ee52baa", "IPY_MODEL_61c802dc2e7a486e91ed26b43a579a65", "IPY_MODEL_9123e7f5130a4f498130e117726e8430"], "layout": "IPY_MODEL_2ed0f1ed939949518905dbcd850f9ee8"}}, "75777df2725d4509adaddc144ee52baa": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_73d7a2d3325a469c89c275c0d6912551", "placeholder": "​", "style": "IPY_MODEL_7cf8bdbebd52448bb8444099cfb70886", "value": "yolox_l0.05.onnx: 100%"}}, "61c802dc2e7a486e91ed26b43a579a65": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0fb6312ab0b94e92a8989059794038ce", "max": 216625723, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_bcc6d619f2ce49b6bb2ad45099d229a2", "value": 216625723}}, "9123e7f5130a4f498130e117726e8430": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_80b344425b2e46868c6988d0b6bf0a60", "placeholder": "​", "style": "IPY_MODEL_f12e15d72d634c8ba643a468f4d76735", "value": " 217M/217M [00:01&lt;00:00, 202MB/s]"}}, "2ed0f1ed939949518905dbcd850f9ee8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "73d7a2d3325a469c89c275c0d6912551": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7cf8bdbebd52448bb8444099cfb70886": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0fb6312ab0b94e92a8989059794038ce": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bcc6d619f2ce49b6bb2ad45099d229a2": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "80b344425b2e46868c6988d0b6bf0a60": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f12e15d72d634c8ba643a468f4d76735": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4e840cdb44a44a99b851cbe1673db6b5": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4f02de36c68c4a12978129ce6856a104", "IPY_MODEL_c2f04856ddcb4fd1bbc1ead4274ce0aa", "IPY_MODEL_bc28ccf6311547daaa88e14861fc653d"], "layout": "IPY_MODEL_8e12ba7b025e42b09bff0115dd840e49"}}, "4f02de36c68c4a12978129ce6856a104": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b4207375ca9442d9b88cbaa5810f5041", "placeholder": "​", "style": "IPY_MODEL_51fb8e49361e48479028d3112a4bcd90", "value": "config.json: 100%"}}, "c2f04856ddcb4fd1bbc1ead4274ce0aa": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6216f9fd90b44c97bc251ad1b554047d", "max": 1469, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_64e9f9d5e7504dbea334234d4788089d", "value": 1469}}, "bc28ccf6311547daaa88e14861fc653d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0b912a2a673f4daea2da687bb94547c6", "placeholder": "​", "style": "IPY_MODEL_e4d2f8a121c54c49b681a767ac1fc3b1", "value": " 1.47k/1.47k [00:00&lt;00:00, 61.8kB/s]"}}, "8e12ba7b025e42b09bff0115dd840e49": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b4207375ca9442d9b88cbaa5810f5041": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "51fb8e49361e48479028d3112a4bcd90": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6216f9fd90b44c97bc251ad1b554047d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "64e9f9d5e7504dbea334234d4788089d": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "0b912a2a673f4daea2da687bb94547c6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e4d2f8a121c54c49b681a767ac1fc3b1": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8adde71b0962495c80261b2dd1d4abf3": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9596bb6c2fa149b4945ab2d10e207e84", "IPY_MODEL_bcd1278264fa44518f09164105271b22", "IPY_MODEL_93edc57d3b134be88f4b5d0fdf12ebbc"], "layout": "IPY_MODEL_88c95ef5ee33412c8141ffc7c11c702e"}}, "9596bb6c2fa149b4945ab2d10e207e84": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_af1fea75b14f4b0a936513c4f3074fbc", "placeholder": "​", "style": "IPY_MODEL_3f9760917bcf4e249f16f34a2361b73c", "value": "model.safetensors: 100%"}}, "bcd1278264fa44518f09164105271b22": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_037f7836ab6f465caf2b87dc5b7aef63", "max": 115434268, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_829923a46f24479ea648945c677d9e3a", "value": 115434268}}, "93edc57d3b134be88f4b5d0fdf12ebbc": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_db4ea8e3882c493cb980e9dfd8151a84", "placeholder": "​", "style": "IPY_MODEL_a67ed49aae1544e3b5a9b141d1c5dd3e", "value": " 115M/115M [00:00&lt;00:00, 204MB/s]"}}, "88c95ef5ee33412c8141ffc7c11c702e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "af1fea75b14f4b0a936513c4f3074fbc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3f9760917bcf4e249f16f34a2361b73c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "037f7836ab6f465caf2b87dc5b7aef63": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "829923a46f24479ea648945c677d9e3a": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "db4ea8e3882c493cb980e9dfd8151a84": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a67ed49aae1544e3b5a9b141d1c5dd3e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9d9f277060934802932d690307fc9685": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1a3c537f212645fda454ffa103aac256", "IPY_MODEL_1646ce29bbb5425a9262a009f7fa2a13", "IPY_MODEL_3b8846ae905f4b7683e4f5e422e21f75"], "layout": "IPY_MODEL_5b9853b590fe415fb559ae396a7bc3c7"}}, "1a3c537f212645fda454ffa103aac256": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f63494b47dbe412cb82f29a350cbbbc2", "placeholder": "​", "style": "IPY_MODEL_12d2ab6a477e4b94a83dae2651c6fb4b", "value": "model.safetensors: 100%"}}, "1646ce29bbb5425a9262a009f7fa2a13": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d7e400593ed24394a24fb07c069b83c9", "max": 46807446, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_bc2a5e16203d4c83a976cd85e9622467", "value": 46807446}}, "3b8846ae905f4b7683e4f5e422e21f75": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5b38a4d2ed5b4078b13b8397d6439ae8", "placeholder": "​", "style": "IPY_MODEL_c38f90a27964497db1c6f500510b4c03", "value": " 46.8M/46.8M [00:00&lt;00:00, 214MB/s]"}}, "5b9853b590fe415fb559ae396a7bc3c7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f63494b47dbe412cb82f29a350cbbbc2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "12d2ab6a477e4b94a83dae2651c6fb4b": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d7e400593ed24394a24fb07c069b83c9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bc2a5e16203d4c83a976cd85e9622467": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5b38a4d2ed5b4078b13b8397d6439ae8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c38f90a27964497db1c6f500510b4c03": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "cells": [{"cell_type": "code", "source": ["!pip install langchain\n", "!pip install unstructured\n", "!pip install openai\n", "!pip install Cython\n", "!pip install tiktoken"], "metadata": {"id": "9RDOffvrZ3F4"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "i929xxKLnRgr", "outputId": "a3e71b8a-85a9-4dc0-c259-c19cb5039baf"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain-astradb\n", "  Downloading langchain_astradb-0.3.0-py3-none-any.whl (26 kB)\n", "Collecting astrapy<2,>=1 (from langchain-astradb)\n", "  Downloading astrapy-1.1.0-py3-none-any.whl (124 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m124.4/124.4 kB\u001b[0m \u001b[31m3.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-core<0.2.0,>=0.1.31 (from langchain-astradb)\n", "  Downloading langchain_core-0.1.52-py3-none-any.whl (302 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m302.9/302.9 kB\u001b[0m \u001b[31m6.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain-astradb) (1.25.2)\n", "Collecting bson<0.6.0,>=0.5.10 (from astrapy<2,>=1->langchain-astradb)\n", "  Downloading bson-0.5.10.tar.gz (10 kB)\n", "  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Collecting cassio<0.2.0,>=0.1.4 (from astrapy<2,>=1->langchain-astradb)\n", "  Downloading cassio-0.1.7-py3-none-any.whl (44 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m44.9/44.9 kB\u001b[0m \u001b[31m4.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting deprecation<2.2.0,>=2.1.0 (from astrapy<2,>=1->langchain-astradb)\n", "  Downloading deprecation-2.1.0-py2.py3-none-any.whl (11 kB)\n", "Collecting httpx[http2]<1,>=0.25.2 (from astrapy<2,>=1->langchain-astradb)\n", "  Downloading httpx-0.27.0-py3-none-any.whl (75 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m6.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: toml<0.11.0,>=0.10.2 in /usr/local/lib/python3.10/dist-packages (from astrapy<2,>=1->langchain-astradb) (0.10.2)\n", "Collecting uuid6<2024.2.0,>=2024.1.12 (from astrapy<2,>=1->langchain-astradb)\n", "  Downloading uuid6-2024.1.12-py3-none-any.whl (6.4 kB)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.31->langchain-astradb) (6.0.1)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain-core<0.2.0,>=0.1.31->langchain-astradb)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Collecting langsmith<0.2.0,>=0.1.0 (from langchain-core<0.2.0,>=0.1.31->langchain-astradb)\n", "  Downloading langsmith-0.1.57-py3-none-any.whl (121 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m121.0/121.0 kB\u001b[0m \u001b[31m9.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting packaging<24.0,>=23.2 (from langchain-core<0.2.0,>=0.1.31->langchain-astradb)\n", "  Downloading packaging-23.2-py3-none-any.whl (53 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m53.0/53.0 kB\u001b[0m \u001b[31m7.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.31->langchain-astradb) (2.7.1)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.31->langchain-astradb) (8.3.0)\n", "Requirement already satisfied: python-dateutil>=2.4.0 in /usr/local/lib/python3.10/dist-packages (from bson<0.6.0,>=0.5.10->astrapy<2,>=1->langchain-astradb) (2.8.2)\n", "Requirement already satisfied: six>=1.9.0 in /usr/local/lib/python3.10/dist-packages (from bson<0.6.0,>=0.5.10->astrapy<2,>=1->langchain-astradb) (1.16.0)\n", "Collecting cassandra-driver<4.0.0,>=3.28.0 (from cassio<0.2.0,>=0.1.4->astrapy<2,>=1->langchain-astradb)\n", "  Downloading cassandra_driver-3.29.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (18.9 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m18.9/18.9 MB\u001b[0m \u001b[31m32.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: requests<3.0.0,>=2.31.0 in /usr/local/lib/python3.10/dist-packages (from cassio<0.2.0,>=0.1.4->astrapy<2,>=1->langchain-astradb) (2.31.0)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain-astradb) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain-astradb) (2024.2.2)\n", "Collecting httpcore==1.* (from httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain-astradb)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m11.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain-astradb) (3.7)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain-astradb) (1.3.1)\n", "Collecting h2<5,>=3 (from httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain-astradb)\n", "  Downloading h2-4.1.0-py3-none-any.whl (57 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m57.5/57.5 kB\u001b[0m \u001b[31m6.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting h11<0.15,>=0.13 (from httpcore==1.*->httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain-astradb)\n", "  Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m8.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core<0.2.0,>=0.1.31->langchain-astradb)\n", "  Downloading jsonpointer-2.4-py2.py3-none-any.whl (7.8 kB)\n", "Collecting or<PERSON><PERSON><4.0.0,>=3.9.14 (from langsmith<0.2.0,>=0.1.0->langchain-core<0.2.0,>=0.1.31->langchain-astradb)\n", "  Downloading orjson-3.10.3-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (142 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m142.5/142.5 kB\u001b[0m \u001b[31m18.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain-core<0.2.0,>=0.1.31->langchain-astradb) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.2 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain-core<0.2.0,>=0.1.31->langchain-astradb) (2.18.2)\n", "Requirement already satisfied: typing-extensions>=4.6.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain-core<0.2.0,>=0.1.31->langchain-astradb) (4.11.0)\n", "Collecting geomet<0.3,>=0.1 (from cassandra-driver<4.0.0,>=3.28.0->cassio<0.2.0,>=0.1.4->astrapy<2,>=1->langchain-astradb)\n", "  Downloading geomet-0.2.1.post1-py3-none-any.whl (18 kB)\n", "Collecting hyperframe<7,>=6.0 (from h2<5,>=3->httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain-astradb)\n", "  Downloading hyperframe-6.0.1-py3-none-any.whl (12 kB)\n", "Collecting hpack<5,>=4.0 (from h2<5,>=3->httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain-astradb)\n", "  Downloading hpack-4.0.0-py3-none-any.whl (32 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.31.0->cassio<0.2.0,>=0.1.4->astrapy<2,>=1->langchain-astradb) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.31.0->cassio<0.2.0,>=0.1.4->astrapy<2,>=1->langchain-astradb) (2.0.7)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain-astradb) (1.2.1)\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from geomet<0.3,>=0.1->cassandra-driver<4.0.0,>=3.28.0->cassio<0.2.0,>=0.1.4->astrapy<2,>=1->langchain-astradb) (8.1.7)\n", "Building wheels for collected packages: bson\n", "  Building wheel for bson (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for bson: filename=bson-0.5.10-py3-none-any.whl size=11976 sha256=438bfedc50fe11086f0c3b40e33825cbf9404e38e45744c264d7c5a93b22f717\n", "  Stored in directory: /root/.cache/pip/wheels/36/49/3b/8b33954dfae7a176009c4d721a45af56c8a9c1cdc3ee947945\n", "Successfully built bson\n", "Installing collected packages: uuid6, packaging, orjson, jsonpointer, hyperframe, hpack, h11, geomet, jsonpatch, httpcore, h2, deprecation, cassandra-driver, bson, langsmith, httpx, cassio, langchain-core, astrapy, langchain-astradb\n", "  Attempting uninstall: packaging\n", "    Found existing installation: packaging 24.0\n", "    Uninstalling packaging-24.0:\n", "      Successfully uninstalled packaging-24.0\n", "Successfully installed astrapy-1.1.0 bson-0.5.10 cassandra-driver-3.29.1 cassio-0.1.7 deprecation-2.1.0 geomet-0.2.1.post1 h11-0.14.0 h2-4.1.0 hpack-4.0.0 httpcore-1.0.5 httpx-0.27.0 hyperframe-6.0.1 jsonpatch-1.33 jsonpointer-2.4 langchain-astradb-0.3.0 langchain-core-0.1.52 langsmith-0.1.57 orjson-3.10.3 packaging-23.2 uuid6-2024.1.12\n"]}], "source": ["!pip install --upgrade langchain-astradb"]}, {"cell_type": "code", "source": ["!pip install langchain langchain-openai datasets pypdf"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "IWdY3uvRnZKn", "outputId": "4fadc829-460e-410d-fc7d-f4013ee62966"}, "execution_count": 16, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain\n", "  Downloading langchain-0.1.20-py3-none-any.whl (1.0 MB)\n", "\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/1.0 MB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[91m━━━━━━━━━━━━\u001b[0m\u001b[91m╸\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.3/1.0 MB\u001b[0m \u001b[31m9.4 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K     \u001b[91m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[91m╸\u001b[0m\u001b[90m━━━━\u001b[0m \u001b[32m0.9/1.0 MB\u001b[0m \u001b[31m13.3 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m11.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-openai\n", "  Downloading langchain_openai-0.1.6-py3-none-any.whl (34 kB)\n", "Collecting datasets\n", "  Downloading datasets-2.19.1-py3-none-any.whl (542 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m542.0/542.0 kB\u001b[0m \u001b[31m16.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pypdf in /usr/local/lib/python3.10/dist-packages (4.2.0)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.0.30)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.9.5)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (4.0.3)\n", "Requirement already satisfied: dataclasses-json<0.7,>=0.5.7 in /usr/local/lib/python3.10/dist-packages (from langchain) (0.6.6)\n", "Requirement already satisfied: langchain-community<0.1,>=0.0.38 in /usr/local/lib/python3.10/dist-packages (from langchain) (0.0.38)\n", "Requirement already satisfied: langchain-core<0.2.0,>=0.1.52 in /usr/local/lib/python3.10/dist-packages (from langchain) (0.1.52)\n", "Collecting langchain-text-splitters<0.1,>=0.0.1 (from langchain)\n", "  Downloading langchain_text_splitters-0.0.1-py3-none-any.whl (21 kB)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.17 in /usr/local/lib/python3.10/dist-packages (from langchain) (0.1.57)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.25.2)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.7.1)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (8.3.0)\n", "Collecting openai<2.0.0,>=1.24.0 (from langchain-openai)\n", "  Downloading openai-1.29.0-py3-none-any.whl (320 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m320.3/320.3 kB\u001b[0m \u001b[31m18.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting tiktoken<1,>=0.5.2 (from langchain-openai)\n", "  Downloading tiktoken-0.7.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.1 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m23.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from datasets) (3.14.0)\n", "Requirement already satisfied: pyarrow>=12.0.0 in /usr/local/lib/python3.10/dist-packages (from datasets) (14.0.2)\n", "Requirement already satisfied: pyarrow-hotfix in /usr/local/lib/python3.10/dist-packages (from datasets) (0.6)\n", "Collecting dill<0.3.9,>=0.3.0 (from datasets)\n", "  Downloading dill-0.3.8-py3-none-any.whl (116 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m116.3/116.3 kB\u001b[0m \u001b[31m19.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from datasets) (2.0.3)\n", "Requirement already satisfied: tqdm>=4.62.1 in /usr/local/lib/python3.10/dist-packages (from datasets) (4.66.4)\n", "Collecting xxhash (from datasets)\n", "  Downloading xxhash-3.4.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (194 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m194.1/194.1 kB\u001b[0m \u001b[31m26.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting multiprocess (from datasets)\n", "  Downloading multiprocess-0.70.16-py310-none-any.whl (134 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m134.8/134.8 kB\u001b[0m \u001b[31m19.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: fsspec[http]<=2024.3.1,>=2023.1.0 in /usr/local/lib/python3.10/dist-packages (from datasets) (2023.6.0)\n", "Collecting huggingface-hub>=0.21.2 (from datasets)\n", "  Downloading huggingface_hub-0.23.0-py3-none-any.whl (401 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m401.2/401.2 kB\u001b[0m \u001b[31m23.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: packaging in /usr/local/lib/python3.10/dist-packages (from datasets) (23.2)\n", "Requirement already satisfied: typing_extensions>=4.0 in /usr/local/lib/python3.10/dist-packages (from pypdf) (4.11.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.9.4)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json<0.7,>=0.5.7->langchain) (3.21.2)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json<0.7,>=0.5.7->langchain) (0.9.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.52->langchain) (1.33)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.17->langchain) (3.10.3)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.24.0->langchain-openai) (3.7.1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai<2.0.0,>=1.24.0->langchain-openai) (1.7.0)\n", "Collecting httpx<1,>=0.23.0 (from openai<2.0.0,>=1.24.0->langchain-openai)\n", "  Downloading httpx-0.27.0-py3-none-any.whl (75 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m12.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.24.0->langchain-openai) (1.3.1)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.2 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (2.18.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2024.2.2)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.0.3)\n", "Requirement already satisfied: regex>=2022.1.18 in /usr/local/lib/python3.10/dist-packages (from tiktoken<1,>=0.5.2->langchain-openai) (2023.12.25)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from pandas->datasets) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->datasets) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas->datasets) (2024.1)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.24.0->langchain-openai) (1.2.1)\n", "Collecting httpcore==1.* (from httpx<1,>=0.23.0->openai<2.0.0,>=1.24.0->langchain-openai)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m12.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->openai<2.0.0,>=1.24.0->langchain-openai)\n", "  Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m9.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.2.0,>=0.1.52->langchain) (2.4)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.8.2->pandas->datasets) (1.16.0)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain) (1.0.0)\n", "Installing collected packages: xxhash, h11, dill, tiktoken, multiprocess, huggingface-hub, httpcore, httpx, openai, datasets, langchain-text-splitters, langchain-openai, langchain\n", "  Attempting uninstall: huggingface-hub\n", "    Found existing installation: huggingface-hub 0.20.3\n", "    Uninstalling huggingface-hub-0.20.3:\n", "      Successfully uninstalled huggingface-hub-0.20.3\n", "Successfully installed datasets-2.19.1 dill-0.3.8 h11-0.14.0 httpcore-1.0.5 httpx-0.27.0 huggingface-hub-0.23.0 langchain-0.1.20 langchain-openai-0.1.6 langchain-text-splitters-0.0.1 multiprocess-0.70.16 openai-1.29.0 tiktoken-0.7.0 xxhash-3.4.1\n"]}, {"output_type": "display_data", "data": {"application/vnd.colab-display-data+json": {"pip_warning": {"packages": ["huggingface_hub"]}, "id": "305c89cd29214fffb4847fc3abb43234"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["!pip install pdf2image"], "metadata": {"id": "B6oJrqqRauvY"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["!pip install pdfminer.six"], "metadata": {"id": "Ox_1QUszavjV"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["!pip install unstructured[pdf]"], "metadata": {"id": "fvp_dAEWayjg"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["import os\n", "from getpass import getpass\n", "\n", "from datasets import (\n", "    load_dataset,\n", ")\n", "from langchain_community.document_loaders import PyPDFLoader\n", "from langchain_core.documents import Document\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_openai import ChatOpenAI, OpenAIEmbeddings\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "from langchain.document_loaders import UnstructuredPDFLoader\n", "from langchain.indexes import VectorstoreIndexCreator"], "metadata": {"id": "gPuH-fXlnaiX"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": ["import os\n", "from google.colab import userdata\n", "OPENAI_API_KEY=userdata.get('OPENAI_API_KEY')\n", "os.environ[\"OPENAI_API_KEY\"] = OPENAI_API_KEY\n", "\n", "embedding = OpenAIEmbeddings()"], "metadata": {"id": "Bost4y11ngS2"}, "execution_count": 5, "outputs": []}, {"cell_type": "code", "source": ["embedding = OpenAIEmbeddings()"], "metadata": {"id": "gXD1e0iknq9m"}, "execution_count": 6, "outputs": []}, {"cell_type": "markdown", "source": ["# Using Unstructured for loading Multiple Pdfs"], "metadata": {"id": "BhXC2nsaaao4"}}, {"cell_type": "code", "source": ["root_dir=\"/content/\""], "metadata": {"id": "obMEfgOUaYoI"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["pdf_folder_path = f'{root_dir}/docs/'"], "metadata": {"id": "fHwmBphmaMrJ"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["os.listdir(pdf_folder_path)"], "metadata": {"id": "EXg7WYjmaMx6"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# location of the pdf file/files.\n", "loaders = [UnstructuredPDFLoader(os.path.join(pdf_folder_path, fn)) for fn in os.listdir(pdf_folder_path)]"], "metadata": {"id": "gdyyz5uDbF65"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["loaders"], "metadata": {"id": "cIOOjInebHHR"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["index = VectorstoreIndexCreator().from_loaders(loaders)"], "metadata": {"id": "C6sNGjHsaM05"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["index.query('What is the tokenization in RAG?')"], "metadata": {"id": "TyONx7bRaM6q"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["index.query_with_sources('What is the tokenization in RAG?')"], "metadata": {"id": "1kCaJmvhaM9o"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["# Pypdf loader with Multiple Pdfs."], "metadata": {"id": "2X3IRcpxbSKZ"}}, {"cell_type": "code", "source": ["from langchain_astradb import AstraDBVectorStore"], "metadata": {"id": "btAgdVVknvyd"}, "execution_count": 34, "outputs": []}, {"cell_type": "code", "source": ["from langchain_astradb import AstraDBVectorStore\n", "ASTRA_DB_API_ENDPOINT=\"https://d2357619-8f04-4cfd-bc3a-16e410893ba3-us-east-2.apps.astra.datastax.com\"\n", "ASTRA_DB_APPLICATION_TOKEN=\"AstraCS:hTmlZSqmAOUHSWZaeNqzEDOR:1128826e960e49c2508b3014ae7fa40e6b5d0490d8565702a30b4ea338083a4a\"\n", "ASTRA_DB_KEYSPACE=\"default_keyspace\""], "metadata": {"id": "DgLFd0Kd2nIO"}, "execution_count": 36, "outputs": []}, {"cell_type": "code", "source": ["root_dir=\"/content/\"\n", "pdf_folder_path = f'{root_dir}/data/'\n", "pdfs=os.listdir(pdf_folder_path)"], "metadata": {"id": "fLh8RfMwaNLM"}, "execution_count": 37, "outputs": []}, {"cell_type": "code", "source": ["pdfs"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Quw8romYBEpV", "outputId": "d3a645f6-8cce-4d4a-b0c5-3d35f2ae51ae"}, "execution_count": 39, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['MachineTranslationwithAttention.pdf',\n", " 'Retrieval-Augmented-Generation-for-NLP.pdf']"]}, "metadata": {}, "execution_count": 39}]}, {"cell_type": "code", "source": ["data=PyPDFLoader(\"/content/data/MachineTranslationwithAttention.pdf\")"], "metadata": {"id": "iGWaBKx7BSiP"}, "execution_count": 43, "outputs": []}, {"cell_type": "code", "source": ["data"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Z4Y6bmItBhbB", "outputId": "0ba46137-2b3e-42b7-90ae-b9afefcad5b4"}, "execution_count": 44, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<langchain_community.document_loaders.pdf.PyPDFLoader at 0x7f575ae96890>"]}, "metadata": {}, "execution_count": 44}]}, {"cell_type": "code", "source": ["splitter = RecursiveCharacterTextSplitter(chunk_size=512, chunk_overlap=64)"], "metadata": {"id": "u280YuAtCCzX"}, "execution_count": 48, "outputs": []}, {"cell_type": "code", "source": ["data.load_and_split(text_splitter=splitter)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "NRH8dsh5B-n9", "outputId": "c092b97a-84e9-4605-bf8b-010ee09482c8"}, "execution_count": 49, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='Retrieval-Augmented Generation for\\nKnowledge-Intensive NLP Tasks\\n<PERSON><PERSON><PERSON>†‡, <PERSON>,\\n<PERSON><PERSON><PERSON><PERSON>†, <PERSON><PERSON><PERSON>†, <PERSON>†, <PERSON><PERSON>†, <PERSON>†,\\<PERSON><PERSON><PERSON>†, <PERSON><PERSON><PERSON><PERSON>†, <PERSON>†‡, <PERSON>†‡, <PERSON><PERSON><PERSON>†\\n†Facebook AI Research;‡University College London;⋆New York University;\\<EMAIL>\\nAbstract\\nLarge pre-trained language models have been shown to store factual knowledge', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 0}),\n", " Document(page_content='in their parameters, and achieve state-of-the-art results when ﬁne-tuned on down-\\nstream NLP tasks. However, their ability to access and precisely manipulate knowl-\\nedge is still limited, and hence on knowledge-intensive tasks, their performance\\nlags behind task-speciﬁc architectures. Additionally, providing provenance for their\\ndecisions and updating their world knowledge remain open research problems. Pre-\\ntrained models with a differentiable access mechanism to explicit non-parametric', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 0}),\n", " Document(page_content='memory have so far been only investigated for extractive downstream tasks. We\\nexplore a general-purpose ﬁne-tuning recipe for retrieval-augmented generation\\n(RAG) — models which combine pre-trained parametric and non-parametric mem-\\nory for language generation. We introduce RAG models where the parametric\\nmemory is a pre-trained seq2seq model and the non-parametric memory is a dense\\nvector index of Wikipedia, accessed with a pre-trained neural retriever. We com-', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 0}),\n", " Document(page_content='pare two RAG formulations, one which conditions on the same retrieved passages\\nacross the whole generated sequence, and another which can use different passages\\nper token. We ﬁne-tune and evaluate our models on a wide range of knowledge-\\nintensive NLP tasks and set the state of the art on three open domain QA tasks,\\noutperforming parametric seq2seq models and task-speciﬁc retrieve-and-extract\\narchitectures. For language generation tasks, we ﬁnd that RAG models generate', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 0}),\n", " Document(page_content='more speciﬁc, diverse and factual language than a state-of-the-art parametric-only\\nseq2seq baseline.\\n1 Introduction\\nPre-trained neural language models have been shown to learn a substantial amount of in-depth knowl-\\nedge from data [ 47]. They can do so without any access to an external memory, as a parameterized\\nimplicit knowledge base [ 51,52]. While this development is exciting, such models do have down-\\nsides: They cannot easily expand or revise their memory, can’t straightforwardly provide insight into', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 0}),\n", " Document(page_content='their predictions, and may produce “hallucinations” [ 38]. Hybrid models that combine parametric\\nmemory with non-parametric (i.e., retrieval-based) memories [ 20,26,48] can address some of these\\nissues because knowledge can be directly revised and expanded, and accessed knowledge can be\\ninspected and interpreted. REALM [ 20] and ORQA [ 31], two recently introduced models that', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 0}),\n", " Document(page_content='combine masked language models [ 8] with a differentiable retriever, have shown promising results,arXiv:2005.11401v4  [cs.CL]  12 Apr 2021', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 0}),\n", " Document(page_content='The\\tDivine\\nComedy\\t(x) qQuery\\nEncoder\\nq(x)\\nMIPS p θGenerator \\xa0pθ\\n(Parametric)\\nMargin-\\nalize\\nThis\\t14th\\tcentury\\twork\\nis\\tdivided\\tinto\\t3\\nsections:\\t\"Inferno\",\\n\"Purgatorio\"\\t&\\n\"Paradiso\"\\t\\t\\t\\t\\t\\t\\t\\t\\t (y)End-to-End Backprop through q and\\xa0 p θ\\nBarack\\tObama\\twas\\nborn\\tin\\tHawaii. (x)\\nFact V eriﬁcation: Fact Querysupports \\t(y)\\nQuestion GenerationFact V eriﬁcation:\\nLabel GenerationDocument\\nIndexDefine\\t\"middle\\tear\" (x)\\nQuestion Answering:\\nQuestion QueryThe\\tmiddle\\tear\\tincludes\\nthe\\ttympanic\\tcavity\\tand\\nthe\\tthree\\tossicles.\\t\\t (y)', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 1}),\n", " Document(page_content='the\\ttympanic\\tcavity\\tand\\nthe\\tthree\\tossicles.\\t\\t (y)\\nQuestion Answering:\\nAnswer GenerationRetriever pη\\n(Non-Parametric)\\nz 4\\nz3\\nz2\\nz 1d(z)\\nJeopardy Question\\nGeneration:\\nAnswer QueryFigure 1: Overview of our approach. We combine a pre-trained retriever ( Query Encoder +Document\\nIndex ) with a pre-trained seq2seq model ( Generator ) and ﬁne-tune end-to-end. For query x, we use\\nMaximum Inner Product Search (MIPS) to ﬁnd the top-K documents zi. For ﬁnal prediction y, we', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 1}),\n", " Document(page_content='treatzas a latent variable and marginalize over seq2seq predictions given different documents.\\nbut have only explored open-domain extractive question answering. Here, we bring hybrid parametric\\nand non-parametric memory to the “workhorse of NLP,” i.e. sequence-to-sequence (seq2seq) models.\\nWe endow pre-trained, parametric-memory generation models with a non-parametric memory through\\na general-purpose ﬁne-tuning approach which we refer to as retrieval-augmented generation (RAG).', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 1}),\n", " Document(page_content='We build RAG models where the parametric memory is a pre-trained seq2seq transformer, and the\\nnon-parametric memory is a dense vector index of Wikipedia, accessed with a pre-trained neural\\nretriever. We combine these components in a probabilistic model trained end-to-end (Fig. 1). The\\nretriever (Dense Passage Retriever [ 26], henceforth DPR) provides latent documents conditioned on\\nthe input, and the seq2seq model (BART [ 32]) then conditions on these latent documents together with', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 1}),\n", " Document(page_content='the input to generate the output. We marginalize the latent documents with a top-K approximation,\\neither on a per-output basis (assuming the same document is responsible for all tokens) or a per-token\\nbasis (where different documents are responsible for different tokens). Like T5 [ 51] or BART, RAG\\ncan be ﬁne-tuned on any seq2seq task, whereby both the generator and retriever are jointly learned.\\nThere has been extensive previous work proposing architectures to enrich systems with non-parametric', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 1}),\n", " Document(page_content='memory which are trained from scratch for speciﬁc tasks, e.g. memory networks [ 64,55], stack-\\naugmented networks [ 25] and memory layers [ 30]. In contrast, we explore a setting where both\\nparametric and non-parametric memory components are pre-trained and pre-loaded with extensive\\nknowledge. Crucially, by using pre-trained access mechanisms, the ability to access knowledge is\\npresent without additional training.', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 1}),\n", " Document(page_content='present without additional training.\\nOur results highlight the beneﬁts of combining parametric and non-parametric memory with genera-\\ntion for knowledge-intensive tasks —tasks that humans could not reasonably be expected to perform\\nwithout access to an external knowledge source. Our RAG models achieve state-of-the-art results\\non open Natural Questions [ 29], WebQuestions [ 3] and CuratedTrec [ 2] and strongly outperform', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 1}),\n", " Document(page_content='recent approaches that use specialised pre-training objectives on TriviaQA [ 24]. Despite these being\\nextractive tasks, we ﬁnd that unconstrained generation outperforms previous extractive approaches.\\nFor knowledge-intensive generation, we experiment with MS-MARCO [ 1] and Je<PERSON><PERSON>y question\\ngeneration, and we ﬁnd that our models generate responses that are more factual, speciﬁc, and\\ndiverse than a BART baseline. For FEVER [ 56] fact veriﬁcation, we achieve results within 4.3% of', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 1}),\n", " Document(page_content='state-of-the-art pipeline models which use strong retrieval supervision. Finally, we demonstrate that\\nthe non-parametric memory can be replaced to update the models’ knowledge as the world changes.1\\n2 Methods\\nWe explore RAG models, which use the input sequence xto retrieve text documents zand use them\\nas additional context when generating the target sequence y. As shown in Figure 1, our models\\nleverage two components: (i) a retriever pη(z|x)with parameters ηthat returns (top-K truncated)', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 1}),\n", " Document(page_content='distributions over text passages given a query xand (ii) a generator pθ(yi|x,z,y 1:i−1)parametrized\\n1Code to run experiments with RAG has been open-sourced as part of the HuggingFace Transform-\\ners Library [ 66] and can be found at https://github.com/huggingface/transformers/blob/master/\\nexamples/rag/ . An interactive demo of RAG models can be found at https://huggingface.co/rag/\\n2', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 1}),\n", " Document(page_content='byθthat generates a current token based on a context of the previous i−1tokensy1:i−1, the original\\ninputxand a retrieved passage z.\\nTo train the retriever and generator end-to-end, we treat the retrieved document as a latent variable.\\nWe propose two models that marginalize over the latent documents in different ways to produce a\\ndistribution over generated text. In one approach, RAG-Sequence , the model uses the same document', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 2}),\n", " Document(page_content='to predict each target token. The second approach, RAG-Token , can predict each target token based\\non a different document. In the following, we formally introduce both models and then describe the\\npηandpθcomponents, as well as the training and decoding procedure.\\n2.1 Models\\nRAG-Sequence Model The RAG-Sequence model uses the same retrieved document to generate\\nthe complete sequence . Technically, it treats the retrieved document as a single latent variable that', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 2}),\n", " Document(page_content='is marginalized to get the seq2seq probability p(y|x)via a top-K approximation. Concretely, the\\ntop K documents are retrieved using the retriever, and the generator produces the output sequence\\nprobability for each document, which are then marginalized,\\npRAG-Sequence (y|x)≈∑\\nz∈top-k(p(·|x))pη(z|x)pθ(y|x,z) =∑\\nz∈top-k(p(·|x))pη(z|x)N∏\\nipθ(yi|x,z,y 1:i−1)\\nRAG-Token Model In the RAG-Token model we can draw a different latent document for each', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 2}),\n", " Document(page_content='target token and marginalize accordingly. This allows the generator to choose content from several\\ndocuments when producing an answer. Concretely, the top K documents are retrieved using the\\nretriever, and then the generator produces a distribution for the next output token for each document,\\nbefore marginalizing, and repeating the process with the following output token, Formally, we deﬁne:\\npRAG-Token (y|x)≈N∏\\ni∑\\nz∈top-k(p(·|x))pη(z|x)pθ(yi|x,z,y 1:i−1)', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 2}),\n", " Document(page_content='pRAG-Token (y|x)≈N∏\\ni∑\\nz∈top-k(p(·|x))pη(z|x)pθ(yi|x,z,y 1:i−1)\\nFinally, we note that RAG can be used for sequence classiﬁcation tasks by considering the target class\\nas a target sequence of length one, in which case RAG-Sequence and RAG-Token are equivalent.\\n2.2 Retriever: DPR\\nThe retrieval component pη(z|x)is based on DPR [26]. DPR follows a bi-encoder architecture:\\npη(z|x)∝exp(\\nd(z)⊤q(x))\\nd(z) =BERTd(z),q(x) =BERTq(x)', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 2}),\n", " Document(page_content='pη(z|x)∝exp(\\nd(z)⊤q(x))\\nd(z) =BERTd(z),q(x) =BERTq(x)\\nwhere d(z)is a dense representation of a document produced by a BERT BASE document encoder [8],\\nandq(x)a query representation produced by a query encoder , also based on BERT BASE. Calculating\\ntop-k (pη(·|x)), the list ofkdocumentszwith highest prior probability pη(z|x), is a Maximum Inner\\nProduct Search (MIPS) problem, which can be approximately solved in sub-linear time [ 23]. We use', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 2}),\n", " Document(page_content='a pre-trained bi-encoder from DPR to initialize our retriever and to build the document index. This\\nretriever was trained to retrieve documents which contain answers to TriviaQA [ 24] questions and\\nNatural Questions [29]. We refer to the document index as the non-parametric memory .\\n2.3 Generator: BART\\nThe generator component pθ(yi|x,z,y 1:i−1)could be modelled using any encoder-decoder. We use\\nBART-large [ 32], a pre-trained seq2seq transformer [ 58] with 400M parameters. To combine the input', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 2}),\n", " Document(page_content='xwith the retrieved content zwhen generating from BART, we simply concatenate them. BART was\\npre-trained using a denoising objective and a variety of different noising functions. It has obtained\\nstate-of-the-art results on a diverse set of generation tasks and outperforms comparably-sized T5\\nmodels [32]. We refer to the BART generator parameters θas the parametric memory henceforth.\\n2.4 Training\\nWe jointly train the retriever and generator components without any direct supervision on what', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 2}),\n", " Document(page_content='document should be retrieved. Given a ﬁne-tuning training corpus of input/output pairs (xj,yj), we\\n3', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 2}),\n", " Document(page_content='minimize the negative marginal log-likelihood of each target,∑\\nj−logp(yj|xj)using stochastic\\ngradient descent with <PERSON> [ 28]. Updating the document encoder BERTdduring training is costly as\\nit requires the document index to be periodically updated as REALM does during pre-training [ 20].\\nWe do not ﬁnd this step necessary for strong performance, and keep the document encoder (and\\nindex) ﬁxed, only ﬁne-tuning the query encoder BERT qand the BART generator.\\n2.5 Decoding', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 3}),\n", " Document(page_content='2.5 Decoding\\nAt test time, RAG-Sequence and RAG-Token require different ways to approximate arg maxyp(y|x).\\nRAG-Token The RAG-Token model can be seen as a standard, autoregressive seq2seq genera-\\ntor with transition probability: p′\\nθ(yi|x,y 1:i−1) =∑\\nz∈top-k(p(·|x))pη(zi|x)pθ(yi|x,zi,y1:i−1)To\\ndecode, we can plug p′\\nθ(yi|x,y 1:i−1)into a standard beam decoder.\\nRAG-Sequence For RAG-Sequence, the likelihood p(y|x)does not break into a conventional per-', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 3}),\n", " Document(page_content='token likelihood, hence we cannot solve it with a single beam search. Instead, we run beam search for\\neach document z, scoring each hypothesis using pθ(yi|x,z,y 1:i−1). This yields a set of hypotheses\\nY, some of which may not have appeared in the beams of all documents. To estimate the probability\\nof an hypothesis ywe run an additional forward pass for each document zfor whichydoes not\\nappear in the beam, multiply generator probability with pη(z|x)and then sum the probabilities across', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 3}),\n", " Document(page_content='beams for the marginals. We refer to this decoding procedure as “Thorough Decoding.” For longer\\noutput sequences,|Y|can become large, requiring many forward passes. For more efﬁcient decoding,\\nwe can make a further approximation that pθ(y|x,zi)≈0whereywas not generated during beam\\nsearch from x,zi. This avoids the need to run additional forward passes once the candidate set Yhas\\nbeen generated. We refer to this decoding procedure as “Fast Decoding.”\\n3 Experiments', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 3}),\n", " Document(page_content='3 Experiments\\nWe experiment with RAG in a wide range of knowledge-intensive tasks. For all experiments, we use\\na single Wikipedia dump for our non-parametric knowledge source. Following <PERSON> et al. [31] and\\<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al. [26], we use the December 2018 dump. Each Wikipedia article is split into disjoint\\n100-word chunks, to make a total of 21M documents. We use the document encoder to compute an\\nembedding for each document, and build a single MIPS index using FAISS [ 23] with a Hierarchical', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 3}),\n", " Document(page_content='Navigable Small World approximation for fast retrieval [ 37]. During training, we retrieve the top\\nkdocuments for each query. We consider k∈{5,10}for training and set kfor test time using dev\\ndata. We now discuss experimental details for each task.\\n3.1 Open-domain Question Answering\\nOpen-domain question answering (QA) is an important real-world application and common testbed\\nfor knowledge-intensive tasks [ 20]. We treat questions and answers as input-output text pairs (x,y)', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 3}),\n", " Document(page_content='and train RAG by directly minimizing the negative log-likelihood of answers. We compare RAG to\\nthe popular extractive QA paradigm [ 5,7,31,26], where answers are extracted spans from retrieved\\ndocuments, relying primarily on non-parametric knowledge. We also compare to “Closed-Book\\nQA” approaches [ 52], which, like RAG, generate answers, but which do not exploit retrieval, instead\\nrelying purely on parametric knowledge. We consider four popular open-domain QA datasets: Natural', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 3}),\n", " Document(page_content='Questions (NQ) [ 29], TriviaQA (TQA) [ 24]. WebQuestions (WQ) [ 3] and CuratedTrec (CT) [ 2]. As\\nCT and WQ are small, we follow DPR [ 26] by initializing CT and WQ models with our NQ RAG\\nmodel. We use the same train/dev/test splits as prior work [ 31,26] and report Exact Match (EM)\\nscores. For TQA, to compare with T5 [52], we also evaluate on the TQA Wiki test set.\\n3.2 Abstractive Question Answering\\nRAG models can go beyond simple extractive QA and answer questions with free-form, abstractive', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 3}),\n", " Document(page_content='text generation. To test RAG’s natural language generation (NLG) in a knowledge-intensive setting,\\nwe use the MSMARCO NLG task v2.1 [ 43]. The task consists of questions, ten gold passages\\nretrieved from a search engine for each question, and a full sentence answer annotated from the\\nretrieved passages. We do not use the supplied passages, only the questions and answers, to treat\\n4', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 3}),\n", " Document(page_content='MSMARCO as an open-domain abstractive QA task. MSMARCO has some questions that cannot be\\nanswered in a way that matches the reference answer without access to the gold passages, such as\\n“What is the weather in V olcano, CA?” so performance will be lower without using gold passages.\\nWe also note that some MSMARCO questions cannot be answered using Wikipedia alone. Here,\\nRAG can rely on parametric knowledge to generate reasonable responses.\\n3.3 Jeopardy Question Generation', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 4}),\n", " Document(page_content='3.3 Jeopardy Question Generation\\nTo evaluate RAG’s generation abilities in a non-QA setting, we study open-domain question gen-\\neration. Rather than use questions from standard open-domain QA tasks, which typically consist\\nof short, simple questions, we propose the more demanding task of generating Jeopardy questions.\\nJeopardy is an unusual format that consists of trying to guess an entity from a fact about that entity.', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 4}),\n", " Document(page_content='For example, “The World Cup” is the answer to the question “In 1986 Mexico scored as the ﬁrst\\ncountry to host this international sports competition twice.” As Jeopardy questions are precise,\\nfactual statements, generating Jeopardy questions conditioned on their answer entities constitutes a\\nchallenging knowledge-intensive generation task.\\nWe use the splits from SearchQA [ 10], with 100K train, 14K dev, and 27K test examples. As', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 4}),\n", " Document(page_content='this is a new task, we train a BART model for comparison. Following [ 67], we evaluate using the\\nSQuAD-tuned Q-BLEU-1 metric [ 42]. Q-BLEU is a variant of BLEU with a higher weight for\\nmatching entities and has higher correlation with human judgment for question generation than\\nstandard metrics. We also perform two human evaluations, one to assess generation factuality, and\\none for speciﬁcity. We deﬁne factuality as whether a statement can be corroborated by trusted external', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 4}),\n", " Document(page_content='sources, and speciﬁcity as high mutual dependence between the input and output [ 33]. We follow\\nbest practice and use pairwise comparative evaluation [ 34]. Evaluators are shown an answer and two\\ngenerated questions, one from BART and one from RAG. They are then asked to pick one of four\\noptions—quuestion A is better, question B is better, both are good, or neither is good.\\n3.4 Fact Veriﬁcation\\nFEVER [ 56] requires classifying whether a natural language claim is supported or refuted by', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 4}),\n", " Document(page_content='Wikipedia, or whether there is not enough information to decide. The task requires retrieving\\nevidence from Wikipedia relating to the claim and then reasoning over this evidence to classify\\nwhether the claim is true, false, or unveriﬁable from Wikipedia alone. FEVER is a retrieval problem\\ncoupled with an challenging entailment reasoning task. It also provides an appropriate testbed for\\nexploring the RAG models’ ability to handle classiﬁcation rather than generation. We map FEVER', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 4}),\n", " Document(page_content='class labels (supports, refutes, or not enough info) to single output tokens and directly train with\\nclaim-class pairs. Crucially, unlike most other approaches to FEVER, we do not use supervision on\\nretrieved evidence. In many real-world applications, retrieval supervision signals aren’t available, and\\nmodels that do not require such supervision will be applicable to a wider range of tasks. We explore\\ntwo variants: the standard 3-way classiﬁcation task (supports/refutes/not enough info) and the 2-way', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 4}),\n", " Document(page_content='(supports/refutes) task studied in Thorne and Vlachos [57]. In both cases we report label accuracy.\\n4 Results\\n4.1 Open-domain Question Answering\\nTable 1 shows results for RAG along with state-of-the-art models. On all four open-domain QA\\ntasks, RAG sets a new state of the art (only on the T5-comparable split for TQA). RAG combines\\nthe generation ﬂexibility of the “closed-book” (parametric only) approaches and the performance of', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 4}),\n", " Document(page_content='\"open-book\" retrieval-based approaches. Unlike REALM and T5+SSM, RAG enjoys strong results\\nwithout expensive, specialized “salient span masking” pre-training [ 20]. It is worth noting that RAG’s\\nretriever is initialized using DPR’s retriever, which uses retrieval supervision on Natural Questions\\nand TriviaQA. RAG compares favourably to the DPR QA system, which uses a BERT-based “cross-\\nencoder” to re-rank documents, along with an extractive reader. RAG demonstrates that neither a', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 4}),\n", " Document(page_content='re-ranker nor extractive reader is necessary for state-of-the-art performance.\\nThere are several advantages to generating answers even when it is possible to extract them. Docu-\\nments with clues about the answer but do not contain the answer verbatim can still contribute towards\\na correct answer being generated, which is not possible with standard extractive approaches, leading\\n5', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 4}),\n", " Document(page_content='Table 1: Open-Domain QA Test Scores. For TQA,\\nleft column uses the standard test set for Open-\\nDomain QA, right column uses the TQA-Wiki\\ntest set. See Appendix D for further details.\\nModel NQ TQA WQ CT\\nClosed\\nBookT5-11B [52] 34.5 - /50.1 37.4 -\\nT5-11B+SSM[52] 36.6 - /60.5 44.7 -\\nOpen\\nBookREALM [20] 40.4 - / - 40.7 46.8\\nDPR [26] 41.5 57.9/ - 41.1 50.6\\nRAG-Token 44.1 55.2/66.1 45.5 50.0\\nRAG-Seq. 44.5 56.8/ 68.0 45.2 52.2Table 2: Generation and classiﬁcation Test Scores.', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 5}),\n", " Document(page_content='MS-MARCO SotA is [ 4], FEVER-3 is [ 68] and\\nFEVER-2 is [ 57] *Uses gold context/evidence.\\nBest model without gold access underlined.\\nModel Jeopardy MSMARCO FVR3 FVR2\\nB-1 QB-1 R-L B-1 Label Acc.\\nSotA - - 49.8*49.9*76.8 92.2 *\\nBART 15.1 19.7 38.2 41.6 64.0 81.1\\nRAG-Tok. 17.3 22.2 40.1 41.572.5 89.5RAG-Seq. 14.7 21.4 40.8 44.2\\nto more effective marginalization over documents. Furthermore, RAG can generate correct answers', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 5}),\n", " Document(page_content='even when the correct answer is not in any retrieved document, achieving 11.8% accuracy in such\\ncases for NQ, where an extractive model would score 0%.\\n4.2 Abstractive Question Answering\\nAs shown in Table 2, RAG-Sequence outperforms BART on Open MS-MARCO NLG by 2.6 Bleu\\npoints and 2.6 Rouge-L points. RAG approaches state-of-the-art model performance, which is\\nimpressive given that (i) those models access gold passages with speciﬁc information required to', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 5}),\n", " Document(page_content='generate the reference answer , (ii) many questions are unanswerable without the gold passages, and\\n(iii) not all questions are answerable from Wikipedia alone. Table 3 shows some generated answers\\nfrom our models. Qualitatively, we ﬁnd that RAG models hallucinate less and generate factually\\ncorrect text more often than BART. Later, we also show that RAG generations are more diverse than\\nBART generations (see §4.5).\\n4.3 Jeopardy Question Generation', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 5}),\n", " Document(page_content='BART generations (see §4.5).\\n4.3 Jeopardy Question Generation\\nTable 2 shows that RAG-Token performs better than RAG-Sequence on Jeopardy question generation,\\nwith both models outperforming BART on Q-BLEU-1. 4 shows human evaluation results, over 452\\npairs of generations from BART and RAG-Token. Evaluators indicated that BART was more factual\\nthan RAG in only 7.1% of cases, while RAG was more factual in 42.7% of cases, and both RAG and', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 5}),\n", " Document(page_content='BART were factual in a further 17% of cases, clearly demonstrating the effectiveness of RAG on\\nthe task over a state-of-the-art generation model. Evaluators also ﬁnd RAG generations to be more\\nspeciﬁc by a large margin. Table 3 shows typical generations from each model.\\nJeopardy questions often contain two separate pieces of information, and RAG-Token may perform\\nbest because it can generate responses that combine content from several documents. Figure 2 shows', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 5}),\n", " Document(page_content='an example. When generating “Sun”, the posterior is high for document 2 which mentions “The\\nSun Also Rises”. Similarly, document 1 dominates the posterior when “A Farewell to Arms” is\\ngenerated. Intriguingly, after the ﬁrst token of each book is generated, the document posterior ﬂattens.\\nThis observation suggests that the generator can complete the titles without depending on speciﬁc\\ndocuments. In other words, the model’s parametric knowledge is sufﬁcient to complete the titles. We', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 5}),\n", " Document(page_content='ﬁnd evidence for this hypothesis by feeding the BART-only baseline with the partial decoding \"The\\nSun. BART completes the generation \"The SunAlso Rises\" isanovel bythis author of\"The Sun\\nAlso Rises\" indicating the title \"The Sun Also Rises\" is stored in BART’s parameters. Similarly,\\nBART will complete the partial decoding \"The SunAlso Rises\" isanovel bythis author of\"A\\nwith \"The SunAlso Rises\" isanovel bythis author of\"AFarewell toArms\" . This example shows', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 5}),\n", " Document(page_content='how parametric and non-parametric memories work together —the non-parametric component helps\\nto guide the generation, drawing out speciﬁc knowledge stored in the parametric memory.\\n4.4 Fact Veriﬁcation\\nTable 2 shows our results on FEVER. For 3-way classiﬁcation, RAG scores are within 4.3% of\\nstate-of-the-art models, which are complex pipeline systems with domain-speciﬁc architectures and\\nsubstantial engineering, trained using intermediate retrieval supervision, which RAG does not require.\\n6', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 5}),\n", " Document(page_content='Document 1 : his works are considered classics of American\\nliterature ... His wartime experiences formed the basis for his novel\\n”A Farewell to Arms” (1929) ...\\nDocument 2 : ... artists of the 1920s ”Lost Generation” expatriate\\ncommunity. His debut novel, ”The Sun Also Rises” , was published\\nin 1926.\\nBOS”\\nTheSunAlsoRises”isa\\nnovelbythis\\nauthorof”A\\nFarewellto\\nArms”Doc 1\\nDoc 2\\nDoc 3\\nDoc 4\\nDoc 5Figure 2: RAG-Token document posterior p(zi|x,yi,y−i)for each generated token for input “Hem-', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 6}),\n", " Document(page_content='ingway\" for Jeopardy generation with 5 retrieved documents. The posterior for document 1 is high\\nwhen generating “A Farewell to Arms\" and for document 2 when generating “The Sun Also Rises\".\\nTable 3: Examples from generation tasks. RAG models generate more speciﬁc and factually accurate\\nresponses. ‘?’ indicates factually incorrect responses, * indicates partially correct responses.\\nTask Input Model Generation\\nMS-\\nMARCOdeﬁne middle', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 6}),\n", " Document(page_content='Task Input Model Generation\\nMS-\\nMARCOdeﬁne middle\\nearBART?The middle ear is the part of the ear between the middle ear and the nose.\\nRAG-T The middle ear is the portion of the ear internal to the eardrum.\\nRAG-S The middle ear includes the tympanic cavity and the three ossicles.\\nwhat currency\\nneeded in\\nscotlandBART The currency needed in Scotland is Pound sterling.\\nRAG-T Pound is the currency needed in Scotland.\\nRAG-S The currency needed in Scotland is the pound sterling.\\nJeopardy\\nQuestion\\nGener', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 6}),\n", " Document(page_content='Jeopardy\\nQuestion\\nGener\\n-ationWashingtonBART?This state has the largest number of counties in the U.S.\\nRAG-T It’s the only U.S. state named for a U.S. president\\nRAG-S It’s the state where you’ll ﬁnd Mount Rainier National Park\\nThe Divine\\nComedyBART*This epic poem by <PERSON> is divided into 3 parts: the Inferno, the Purgatorio & the Purgatorio\\nRAG-T <PERSON>’s \"Inferno\" is the ﬁrst part of this epic poem\\nRAG-S This 14th century work is divided into 3 sections: \"Inferno\", \"Purgatorio\" & \"Paradiso\"', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 6}),\n", " Document(page_content='For 2-way classiﬁcation, we compare against <PERSON> and <PERSON><PERSON><PERSON><PERSON> [57], who train RoBERTa [ 35]\\nto classify the claim as true or false given the gold evidence sentence. RAG achieves an accuracy\\nwithin 2.7% of this model, despite being supplied with only the claim and retrieving its own evidence.\\nWe also analyze whether documents retrieved by RAG correspond to documents annotated as gold\\nevidence in FEVER. We calculate the overlap in article titles between the top kdocuments retrieved', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 6}),\n", " Document(page_content='by RAG and gold evidence annotations. We ﬁnd that the top retrieved document is from a gold article\\nin 71% of cases, and a gold article is present in the top 10 retrieved articles in 90% of cases.\\n4.5 Additional Results\\nGeneration Diversity Section 4.3 shows that RAG models are more factual and speciﬁc than\\nBART for Jeopardy question generation. Following recent work on diversity-promoting decoding\\n[33,59,39], we also investigate generation diversity by calculating the ratio of distinct ngrams to', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 6}),\n", " Document(page_content='total ngrams generated by different models. Table 5 shows that RAG-Sequence’s generations are\\nmore diverse than RAG-Token’s, and both are signiﬁcantly more diverse than BART without needing\\nany diversity-promoting decoding.\\nRetrieval Ablations A key feature of RAG is learning to retrieve relevant information for the task.\\nTo assess the effectiveness of the retrieval mechanism, we run ablations where we freeze the retriever', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 6}),\n", " Document(page_content='during training. As shown in Table 6, learned retrieval improves results for all tasks.\\nWe compare RAG’s dense retriever to a word overlap-based BM25 retriever [ 53]. Here, we replace\\nRAG’s retriever with a ﬁxed BM25 system, and use BM25 retrieval scores as logits when calculating\\np(z|x). Table 6 shows the results. For FEVER, BM25 performs best, perhaps since FEVER claims are\\nheavily entity-centric and thus well-suited for word overlap-based retrieval. Differentiable retrieval', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 6}),\n", " Document(page_content='improves results on all other tasks, especially for Open-Domain QA, where it is crucial.\\nIndex hot-swapping An advantage of non-parametric memory models like RAG is that knowledge\\ncan be easily updated at test time. Parametric-only models like T5 or BART need further training to\\nupdate their behavior as the world changes. To demonstrate, we build an index using the DrQA [ 5]\\nWikipedia dump from December 2016 and compare outputs from RAG using this index to the newer', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 6}),\n", " Document(page_content='index from our main results (December 2018). We prepare a list of 82 world leaders who had changed\\n7', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 6}),\n", " Document(page_content='Table 4: Human assessments for the Jeopardy\\nQuestion Generation Task.\\nFactuality Speciﬁcity\\nBART better 7.1% 16.8%\\nRAG better 42.7% 37.4%\\nBoth good 11.7% 11.8%\\nBoth poor 17.7% 6.9%\\nNo majority 20.8% 20.1%Table 5: Ratio of distinct to total tri-grams for\\ngeneration tasks.\\nMSMARCO Jeopardy QGen\\nGold 89.6% 90.0%\\nBART 70.7% 32.4%\\nRAG-Token 77.8% 46.8%\\nRAG-Seq. 83.5% 53.8%\\nTable 6: Ablations on the dev set. As FEVER is a classiﬁcation task, both RAG models are equivalent.', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 7}),\n", " Document(page_content='Model NQ TQA WQ CT Jeopardy-QGen MSMarco FVR-3 FVR-2\\nExact Match B-1 QB-1 R-L B-1 Label Accuracy\\nRAG-Token-BM25 29.7 41.5 32.1 33.1 17.5 22.3 55.5 48.475.1 91.6RAG-Sequence-BM25 31.8 44.1 36.6 33.8 11.1 19.5 56.5 46.9\\nRAG-Token-Frozen 37.8 50.1 37.1 51.1 16.7 21.7 55.9 49.472.9 89.4RAG-Sequence-Frozen 41.2 52.1 41.8 52.6 11.8 19.6 56.7 47.3\\nRAG-Token 43.5 54.8 46.5 51.9 17.9 22.6 56.2 49.474.5 90.6RAG-Sequence 44.0 55.8 44.9 53.4 15.3 21.5 57.2 47.5', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 7}),\n", " Document(page_content='between these dates and use a template “Who is {position}?” (e.g. “Who is the President of Peru?”)\\nto query our NQ RAG model with each index. RAG answers 70% correctly using the 2016 index for\\n2016 world leaders and 68% using the 2018 index for 2018 world leaders. Accuracy with mismatched\\nindices is low (12% with the 2018 index and 2016 leaders, 4% with the 2016 index and 2018 leaders).\\nThis shows we can update RAG’s world knowledge by simply replacing its non-parametric memory.', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 7}),\n", " Document(page_content='Effect of Retrieving more documents Models are trained with either 5 or 10 retrieved latent\\ndocuments, and we do not observe signiﬁcant differences in performance between them. We have the\\nﬂexibility to adjust the number of retrieved documents at test time, which can affect performance and\\nruntime. Figure 3 (left) shows that retrieving more documents at test time monotonically improves\\nOpen-domain QA results for RAG-Sequence, but performance peaks for RAG-Token at 10 retrieved', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 7}),\n", " Document(page_content='documents. Figure 3 (right) shows that retrieving more documents leads to higher Rouge-L for\\nRAG-Token at the expense of Bleu-1, but the effect is less pronounced for RAG-Sequence.\\n10 20 30 40 50\\nKR e t r i e v e dD o c s394041424344NQ Exact MatchRAG-Tok\\nRAG-Seq\\n10 20 30 40 50\\nKR e t r i e v e dD o c s4050607080NQ Answer Recall @ KRAG-Tok\\nRAG-Seq\\nFixed DPR\\nBM25\\n10 20 30 40 50\\nKR e t r i e v e dD o c s4850525456Bleu-1 / Rouge-L scoreRAG-Tok R-L\\nRAG-Tok B-1\\nRAG-Seq R-L\\nRAG-Seq B-1', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 7}),\n", " Document(page_content='RAG-Tok B-1\\nRAG-Seq R-L\\nRAG-Seq B-1\\nFigure 3: Left: NQ performance as more documents are retrieved. Center: Retrieval recall perfor-\\nmance in NQ. Right: MS-MARCO Bleu-1 and Rouge-<PERSON> as more documents are retrieved.\\n5 Related Work\\nSingle-Task Retrieval Prior work has shown that retrieval improves performance across a variety of\\nNLP tasks when considered in isolation. Such tasks include open-domain question answering [ 5,29],', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 7}),\n", " Document(page_content='fact checking [ 56], fact completion [ 48], long-form question answering [ 12], Wikipedia article\\ngeneration [ 36], dialogue [ 41,65,9,13], translation [ 17], and language modeling [ 19,27]. Our\\nwork uniﬁes previous successes in incorporating retrieval into individual tasks, showing that a single\\nretrieval-based architecture is capable of achieving strong performance across several tasks.\\n8', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 7}),\n", " Document(page_content='General-Purpose Architectures for NLP Prior work on general-purpose architectures for NLP\\ntasks has shown great success without the use of retrieval. A single, pre-trained language model\\nhas been shown to achieve strong performance on various classiﬁcation tasks in the GLUE bench-\\nmarks [ 60,61] after ﬁne-tuning [ 49,8]. GPT-2 [ 50] later showed that a single, left-to-right, pre-trained\\nlanguage model could achieve strong performance across both discriminative and generative tasks.', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 8}),\n", " Document(page_content='For further improvement, BART [ 32] and T5 [ 51,52] propose a single, pre-trained encoder-decoder\\nmodel that leverages bi-directional attention to achieve stronger performance on discriminative\\nand generative tasks. Our work aims to expand the space of possible tasks with a single, uniﬁed\\narchitecture, by learning a retrieval module to augment pre-trained, generative language models.\\nLearned Retrieval There is signiﬁcant work on learning to retrieve documents in information', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 8}),\n", " Document(page_content='retrieval, more recently with pre-trained, neural language models [ 44,26] similar to ours. Some\\nwork optimizes the retrieval module to aid in a speciﬁc, downstream task such as question answering,\\nusing search [ 46], reinforcement learning [ 6,63,62], or a latent variable approach [ 31,20] as in our\\nwork. These successes leverage different retrieval-based architectures and optimization techniques to\\nachieve strong performance on a single task, while we show that a single retrieval-based architecture', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 8}),\n", " Document(page_content='can be ﬁne-tuned for strong performance on a variety of tasks.\\nMemory-based Architectures Our document index can be seen as a large external memory for\\nneural networks to attend to, analogous to memory networks [ 64,55]. Concurrent work [ 14] learns\\nto retrieve a trained embedding for each entity in the input, rather than to retrieve raw text as in our\\nwork. Other work improves the ability of dialog models to generate factual text by attending over', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 8}),\n", " Document(page_content='fact embeddings [ 15,13]. A key feature of our memory is that it is comprised of raw text rather\\ndistributed representations, which makes the memory both (i) human-readable, lending a form of\\ninterpretability to our model, and (ii) human-writable, enabling us to dynamically update the model’s\\nmemory by editing the document index. This approach has also been used in knowledge-intensive\\ndialog, where generators have been conditioned on retrieved text directly, albeit obtained via TF-IDF', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 8}),\n", " Document(page_content='rather than end-to-end learnt retrieval [9].\\nRetrieve-and-Edit approaches Our method shares some similarities with retrieve-and-edit style\\napproaches, where a similar training input-output pair is retrieved for a given input, and then edited\\nto provide a ﬁnal output. These approaches have proved successful in a number of domains including\\nMachine Translation [ 18,22] and Semantic Parsing [ 21]. Our approach does have several differences,', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 8}),\n", " Document(page_content='including less of emphasis on lightly editing a retrieved item, but on aggregating content from several\\npieces of retrieved content, as well as learning latent retrieval, and retrieving evidence documents\\nrather than related training pairs. This said, RAG techniques may work well in these settings, and\\ncould represent promising future work.\\n6 Discussion\\nIn this work, we presented hybrid generation models with access to parametric and non-parametric', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 8}),\n", " Document(page_content='memory. We showed that our RAG models obtain state of the art results on open-domain QA. We\\nfound that people prefer RAG’s generation over purely parametric BART, ﬁnding RAG more factual\\nand speciﬁc. We conducted an thorough investigation of the learned retrieval component, validating\\nits effectiveness, and we illustrated how the retrieval index can be hot-swapped to update the model\\nwithout requiring any retraining. In future work, it may be fruitful to investigate if the two components', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 8}),\n", " Document(page_content='can be jointly pre-trained from scratch, either with a denoising objective similar to BART or some\\nanother objective. Our work opens up new research directions on how parametric and non-parametric\\nmemories interact and how to most effectively combine them, showing promise in being applied to a\\nwide variety of NLP tasks.\\n9', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 8}),\n", " Document(page_content='Broader Impact\\nThis work offers several positive societal beneﬁts over previous work: the fact that it is more\\nstrongly grounded in real factual knowledge (in this case Wikipedia) makes it “hallucinate” less\\nwith generations that are more factual, and offers more control and interpretability. RAG could be\\nemployed in a wide variety of scenarios with direct beneﬁt to society, for example by endowing it\\nwith a medical index and asking it open-domain questions on that topic, or by helping people be more', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 9}),\n", " Document(page_content='effective at their jobs.\\nWith these advantages also come potential downsides: Wikipedia, or any potential external knowledge\\nsource, will probably never be entirely factual and completely devoid of bias. Since RAG can be\\nemployed as a language model, similar concerns as for GPT-2 [ 50] are valid here, although arguably\\nto a lesser extent, including that it might be used to generate abuse, faked or misleading content in', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 9}),\n", " Document(page_content='the news or on social media; to impersonate others; or to automate the production of spam/phishing\\ncontent [ 54]. Advanced language models may also lead to the automation of various jobs in the\\ncoming decades [ 16]. In order to mitigate these risks, AI systems could be employed to ﬁght against\\nmisleading content and automated spam/phishing.\\nAcknowledgments\\nThe authors would like to thank the reviewers for their thoughtful and constructive feedback on this', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 9}),\n", " Document(page_content='paper, as well as HuggingFace for their help in open-sourcing code to run RAG models. The authors\\nwould also like to thank <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> for productive discussions and advice. EP\\nthanks supports from the NSF Graduate Research Fellowship. PL is supported by the FAIR PhD\\nprogram.\\nReferences\\n[1]<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nMajumder, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 9}),\n", " Document(page_content=<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. MS MARCO: A Human Generated MAchine\\nReading COmprehension Dataset. arXiv:1611.09268 [cs] , November 2016. URL http:\\n//arxiv.org/abs/1611.09268 . arXiv: 1611.09268.\\n[2]<PERSON><PERSON> and <PERSON> `y. Modeling of the question answering task in the yodaqa system. In\\nInternational Conference of the Cross-Language Evaluation Forum for European Languages ,\\npages 222–228. Springer, 2015. URL https://link.springer.com/chapter/10.1007%\\n2F978-3-319-24027-5_20 .', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 9}),\n", " Document(page_content='2F978-3-319-24027-5_20 .\\n[3]<PERSON>, <PERSON>, <PERSON>, and <PERSON>. Semantic Parsing on Freebase\\nfrom Question-Answer Pairs. In Proceedings of the 2013 Conference on Empirical Methods\\nin Natural Language Processing , pages 1533–1544, Seattle, Washington, USA, October 2013.\\nAssociation for Computational Linguistics. URL http://www.aclweb.org/anthology/\\nD13-1160 .\\n[4]<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Palm: Pre-training an autoencod-', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 9}),\n", " Document(page_content='ing&autoregressive language model for context-conditioned generation. ArXiv , abs/2004.07159,\\n2020. URL https://arxiv.org/abs/2004.07159 .\\n[5]<PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Reading Wikipedia to Answer\\nOpen-Domain Questions. In Proceedings of the 55th Annual Meeting of the Association for\\nComputational Linguistics (Volume 1: Long Papers) , pages 1870–1879, Vancouver, Canada,\\nJuly 2017. Association for Computational Linguistics. doi: 10.18653/v1/P17-1171. URL', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 9}),\n", " Document(page_content='https://www.aclweb.org/anthology/P17-1171 .\\n[6]<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and\\n<PERSON><PERSON><PERSON>. Coarse-to-ﬁne question answering for long documents. In Proceedings of the\\n55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers) ,\\npages 209–220, Vancouver, Canada, July 2017. Association for Computational Linguistics. doi:\\n10.18653/v1/P17-1020. URL https://www.aclweb.org/anthology/P17-1020 .\\n10', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 9}),\n", " Document(page_content='[7]<PERSON> and <PERSON>. Simple and Effective Multi-Paragraph Reading Compre-\\nhension. arXiv:1710.10723 [cs] , October 2017. URL http://arxiv.org/abs/1710.10723 .\\narXiv: 1710.10723.\\n[8]<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. BERT: Pre-training of\\nDeep Bidirectional Transformers for Language Understanding. In Proceedings of the 2019 Con-\\nference of the North American Chapter of the Association for Computational Linguistics: Human', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 10}),\n", " Document(page_content='Language Technologies, Volume 1 (Long and Short Papers) , pages 4171–4186, Minneapolis,\\nMinnesota, June 2019. Association for Computational Linguistics. doi: 10.18653/v1/N19-1423.\\nURL https://www.aclweb.org/anthology/N19-1423 .\\n[9]<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Wiz-\\nard of wikipedia: Knowledge-powered conversational agents. In International Conference on\\nLearning Representations , 2019. URL https://openreview.net/forum?id=r1l73iRqKm .', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 10}),\n", " Document(page_content='[10] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>\\nCho. SearchQA: A New Q&A Dataset Augmented with Context from a Search Engine.\\narXiv:1704.05179 [cs] , April 2017. URL http://arxiv.org/abs/1704.05179 . arXiv:\\n1704.05179.\\n[11] <PERSON>, <PERSON>, and <PERSON><PERSON>. Hierarchical neural story generation. In Proceed-\\nings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 1:', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 10}),\n", " Document(page_content='Long Papers) , pages 889–898, Melbourne, Australia, July 2018. Association for Computational\\nLinguistics. doi: 10.18653/v1/P18-1082. URL https://www.aclweb.org/anthology/\\nP18-1082 .\\n[12] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. ELI5:\\nLong form question answering. In Proceedings of the 57th Annual Meeting of the Association\\nfor Computational Linguistics , pages 3558–3567, Florence, Italy, July 2019. Association for', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 10}),\n", " Document(page_content='Computational Linguistics. doi: 10.18653/v1/P19-1346. URL https://www.aclweb.org/\\nanthology/P19-1346 .\\n[13] <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Augmenting transformers\\nwith KNN-based composite memory, 2020. URL https://openreview.net/forum?id=\\nH1gx1CNKPH .\\n[14] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.\\nEntities as experts: Sparse memory access with entity supervision. ArXiv , abs/2004.07202,', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 10}),\n", " Document(page_content='2020. URL https://arxiv.org/abs/2004.07202 .\\n[15] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON>. A knowledge-grounded neural conversation model. In AAAI\\nConference on Artiﬁcial Intelligence , 2018. URL https://www.aaai.org/ocs/index.php/\\nAAAI/AAAI18/paper/view/16710 .\\n[16] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. When will AI\\nexceed human performance? evidence from AI experts. CoRR , abs/1705.08807, 2017. URL', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 10}),\n", " Document(page_content='http://arxiv.org/abs/1705.08807 .\\n[17] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Search engine guided neural\\nmachine translation. In AAAI Conference on Artiﬁcial Intelligence , 2018. URL https:\\n//www.aaai.org/ocs/index.php/AAAI/AAAI18/paper/view/17282 .\\n[18] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON> Li. Search engine guided neural\\nmachine translation. In 32nd AAAI Conference on Artiﬁcial Intelligence, AAAI 2018 , 32nd', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 10}),\n", " Document(page_content='AAAI Conference on Artiﬁcial Intelligence, AAAI 2018, pages 5133–5140. AAAI press, 2018.\\n32nd AAAI Conference on Artiﬁcial Intelligence, AAAI 2018 ; Conference date: 02-02-2018\\nThrough 07-02-2018.\\n[19] <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Generating sentences by\\nediting prototypes. Transactions of the Association for Computational Linguistics , 6:437–450,\\n2018. doi: 10.1162/tacl_a_00030. URL https://www.aclweb.org/anthology/Q18-1031 .\\n11', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 10}),\n", " Document(page_content='[20] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. REALM:\\nRetrieval-augmented language model pre-training. ArXiv , abs/2002.08909, 2020. URL https:\\n//arxiv.org/abs/2002.08909 .\\n[21] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A\\nretrieve-and-edit framework for predicting structured outputs. In <PERSON>. <PERSON>,\\nH. <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, ed-\\nitors, Advances in Neural Information Processing Systems 31 , pages 10052–', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 11}),\n", " Document(page_content='10062. Curran Associates, Inc., 2018. URL http://papers.nips.cc/paper/\\n8209-a-retrieve-and-edit-framework-for-predicting-structured-outputs.\\npdf.\\n[22] <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Simple and effective retrieve-\\nedit-rerank text generation. In Proceedings of the 58th Annual Meeting of the Association for\\nComputational Linguistics , pages 2532–2538, Online, July 2020. Association for Computa-\\ntional Linguistics. doi: 10.18653/v1/2020.acl-main.228. URL https://www.aclweb.org/', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 11}),\n", " Document(page_content='anthology/2020.acl-main.228 .\\n[23] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Billion-scale similarity search with gpus. arXiv\\npreprint arXiv:1702.08734 , 2017. URL https://arxiv.org/abs/1702.08734 .\\n[24] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. TriviaQA: A Large Scale\\nDistantly Supervised Challenge Dataset for Reading Comprehension. In Proceedings of the\\n55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers) ,', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 11}),\n", " Document(page_content='pages 1601–1611, Vancouver, Canada, July 2017. Association for Computational Linguistics.\\ndoi: 10.18653/v1/P17-1147. URL https://www.aclweb.org/anthology/P17-1147 .\\n[25] <PERSON> and <PERSON>. Inferring algorithmic patterns with stack-\\naugmented recurrent nets. In Proceedings of the 28th International Conference on\\nNeural Information Processing Systems - Volume 1 , NIPS’15, page 190–198, Cam-\\nbridge, MA, USA, 2015. MIT Press. URL https://papers.nips.cc/paper/', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 11}),\n", " Document(page_content='5857-inferring-algorithmic-patterns-with-stack-augmented-recurrent-nets .\\n[26] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and\\n<PERSON><PERSON>-<PERSON><PERSON>. Dense passage retrieval for open-domain question answering. arXiv preprint\\narXiv:2004.04906 , 2020. URL https://arxiv.org/abs/2004.04906 .\\n[27] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Generaliza-\\ntion through memorization: Nearest neighbor language models. In International Conference on', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 11}),\n", " Document(page_content='Learning Representations , 2020. URL https://openreview.net/forum?id=HklBjCEKvH .\\n[28] <PERSON><PERSON><PERSON> and <PERSON>: A method for stochastic optimization. In Yoshu<PERSON>\\n<PERSON><PERSON><PERSON> and <PERSON><PERSON>, editors, 3rd International Conference on Learning Representations,\\nICLR 2015, San Diego, CA, USA, May 7-9, 2015, Conference Track Proceedings , 2015. URL\\nhttp://arxiv.org/abs/1412.6980 .\\n[29] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 11}),\n", " Document(page_content=<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Natural Questions: a Benchmark for Ques-\\ntion Answering Research. Transactions of the Association of Computational Lin-\\nguistics , 2019. URL https://tomkwiat.users.x20web.corp.google.com/papers/\\nnatural-questions/main-1455-kwiatkowski.pdf .', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 11}),\n", " Document(page_content='natural-questions/main-1455-kwi<PERSON><PERSON>.pdf .\\n[30] <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and\\nHerve Jegou. Large memory layers with product keys. In <PERSON><PERSON>, <PERSON><PERSON>,\\n<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural In-\\nformation Processing Systems 32 , pages 8548–8559. Curran Associates, Inc., 2019. URL http:\\n//papers.nips.cc/paper/9061-large-memory-layers-with-product-keys.pdf .', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 11}),\n", " Document(page_content='[31] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Latent retrieval for weakly supervised\\nopen domain question answering. In Proceedings of the 57th Annual Meeting of the Association\\n12', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 11}),\n", " Document(page_content='for Computational Linguistics , pages 6086–6096, Florence, Italy, July 2019. Association for\\nComputational Linguistics. doi: 10.18653/v1/P19-1612. URL https://www.aclweb.org/\\nanthology/P19-1612 .\\n[32] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,\\<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. BART: Denoising sequence-to-sequence\\npre-training for natural language generation, translation, and comprehension. arXiv preprint', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 12}),\n", " Document(page_content='arXiv:1910.13461 , 2019. URL https://arxiv.org/abs/1910.13461 .\\n[33] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A diversity-promoting\\nobjective function for neural conversation models. In Proceedings of the 2016 Conference of the\\nNorth American Chapter of the Association for Computational Linguistics: Human Language\\nTechnologies , pages 110–119, San Diego, California, June 2016. Association for Computational', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 12}),\n", " Document(page_content='Linguistics. doi: 10.18653/v1/N16-1014. URL https://www.aclweb.org/anthology/\\nN16-1014 .\\n[34] <PERSON>, <PERSON>, and <PERSON>. Acute-eval: Improved dialogue evaluation\\nwith optimized questions and multi-turn comparisons. ArXiv , abs/1909.03087, 2019. URL\\nhttps://arxiv.org/abs/1909.03087 .\\n[35] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Robust neural machine\\ntranslation with joint textual and phonetic embedding. In Proceedings of the 57th Annual', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 12}),\n", " Document(page_content='Meeting of the Association for Computational Linguistics , pages 3044–3049, Florence, Italy,\\nJuly 2019. Association for Computational Linguistics. doi: 10.18653/v1/P19-1291. URL\\nhttps://www.aclweb.org/anthology/P19-1291 .\\n[36] <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,\\nand <PERSON>. Generating wikipedia by summarizing long sequences. In International\\nConference on Learning Representations , 2018. URL https://openreview.net/forum?\\nid=Hyg0vbWC- .', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 12}),\n", " Document(page_content='id=Hyg0vbWC- .\\n[37] <PERSON><PERSON> and <PERSON><PERSON> <PERSON><PERSON>. Efﬁcient and robust approximate nearest neighbor search\\nusing hierarchical navigable small world graphs. IEEE Transactions on Pattern Analysis and\\nMachine Intelligence , 42:824–836, 2016. URL https://arxiv.org/abs/1603.09320 .\\n[38] <PERSON>. The next decade in ai: four steps towards robust artiﬁcial intelligence. arXiv\\npreprint arXiv:2002.06177 , 2020. URL https://arxiv.org/abs/2002.06177 .', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 12}),\n", " Document(page_content='[39] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>\\<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. How decoding strategies affect the\\nveriﬁability of generated text. arXiv preprint arXiv:1911.03587 , 2019. URL https:\\n//arxiv.org/abs/1911.03587 .\\n[40] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,\\n<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Mixed', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 12}),\n", " Document(page_content='precision training. In ICLR , 2018. URL https://openreview.net/forum?id=r1gs9JgRZ .\\n[41] <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Towards exploit-\\ning background knowledge for building conversation systems. In Proceedings of the 2018\\nConference on Empirical Methods in Natural Language Processing , pages 2322–2332, Brus-\\nsels, Belgium, October-November 2018. Association for Computational Linguistics. doi:\\n10.18653/v1/D18-1255. URL https://www.aclweb.org/anthology/D18-1255 .', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 12}),\n", " Document(page_content='[42] <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Towards a better metric for evaluating question generation\\nsystems. In Proceedings of the 2018 Conference on Empirical Methods in Natural Language\\nProcessing , pages 3950–3959, Brussels, Belgium, October-November 2018. Association for\\nComputational Linguistics. doi: 10.18653/v1/D18-1429. URL https://www.aclweb.org/\\nanthology/D18-1429 .\\n[43] <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 12}),\n", " Document(page_content='and <PERSON> Deng. MS MARCO: A human generated machine reading comprehension dataset. In\\nTare<PERSON>, <PERSON>, <PERSON>ur <PERSON><PERSON><PERSON>, and <PERSON>, editors,\\nProceedings of the Workshop on Cognitive Computation: Integrating neural and symbolic\\n13', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 12}),\n", " Document(page_content='approaches 2016 co-located with the 30th Annual Conference on Neural Information Processing\\nSystems (NIPS 2016), Barcelona, Spain, December 9, 2016 , volume 1773 of CEUR Workshop\\nProceedings . CEUR-WS.org, 2016. URL http://ceur-ws.org/Vol-1773/CoCoNIPS_\\n2016_paper9.pdf .\\n[44] <PERSON> and <PERSON><PERSON><PERSON><PERSON>. <PERSON> re-ranking with BERT. arXiv preprint\\narXiv:1901.04085 , 2019. URL https://arxiv.org/abs/1901.04085 .', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 13}),\n", " Document(page_content='arXiv:1901.04085 , 2019. URL https://arxiv.org/abs/1901.04085 .\\n[45] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,\\nand <PERSON>. fairseq: A fast, extensible toolkit for sequence modeling. In Proceedings\\nof the 2019 Conference of the North American Chapter of the Association for Computational\\nLinguistics (Demonstrations) , pages 48–53, Minneapolis, Minnesota, June 2019. Association', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 13}),\n", " Document(page_content='for Computational Linguistics. doi: 10.18653/v1/N19-4009. URL https://www.aclweb.\\norg/anthology/N19-4009 .\\n[46] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>\\n<PERSON><PERSON>. Finding generalizable evidence by learning to convince q&a models. In Proceedings\\nof the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th\\nInternational Joint Conference on Natural Language Processing (EMNLP-IJCNLP) , pages', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 13}),\n", " Document(page_content='2402–2411, Hong Kong, China, November 2019. Association for Computational Linguistics.\\ndoi: 10.18653/v1/D19-1244. URL https://www.aclweb.org/anthology/D19-1244 .\\n[47] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,\\nand <PERSON>. Language models as knowledge bases? In Proceedings of the 2019\\nConference on Empirical Methods in Natural Language Processing and the 9th International', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 13}),\n", " Document(page_content='Joint Conference on Natural Language Processing (EMNLP-IJCNLP) , pages 2463–2473, Hong\\nKong, China, November 2019. Association for Computational Linguistics. doi: 10.18653/v1/\\nD19-1250. URL https://www.aclweb.org/anthology/D19-1250 .\\n[48] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Miller, and <PERSON>. How context affects language models’ factual predictions. In\\nAutomated Knowledge Base Construction , 2020. URL https://openreview.net/forum?', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 13}),\n", " Document(page_content='id=025X0zPfn .\\n[49] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Im-\\nproving Language Understanding by Generative Pre-Training, 2018. URL\\nhttps://s3-us-west-2.amazonaws.com/openai-assets/research-covers/\\nlanguage-unsupervised/language_understanding_paper.pdf .\\n[50] <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>\\nSutskever. Language models are unsupervised multitask learners, 2019. URL\\nhttps://d4mucfpksywv.cloudfront.net/better-language-models/language_', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 13}),\n", " Document(page_content='models_are_unsupervised_multitask_learners.pdf .\\n[51] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>,\\<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Exploring the limits of transfer learning with a uniﬁed\\ntext-to-text transformer. arXiv e-prints , 2019. URL https://arxiv.org/abs/1910.10683 .\\n[52] <PERSON>, <PERSON>, and <PERSON><PERSON>. How much knowledge can you pack into\\nthe parameters of a language model? arXiv e-prints , 2020. URL https://arxiv.org/abs/\\n2002.08910 .', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 13}),\n", " Document(page_content='2002.08910 .\\n[53] <PERSON> and <PERSON>. The probabilistic relevance framework: Bm25 and\\nbeyond. Found. Trends Inf. Retr. , 3(4):333–389, April 2009. ISSN 1554-0669. doi: 10.1561/\\n1500000019. URL https://doi.org/10.1561/1500000019 .\\n[54] <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>\\n<PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Release strategies and the social impacts of language models.\\nArXiv , abs/1908.09203, 2019.', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 13}),\n", " Document(page_content='ArXiv , abs/1908.09203, 2019.\\n[55] <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. End-to-end memory net-\\nworks. In <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances\\nin Neural Information Processing Systems 28 , pages 2440–2448. Curran Associates, Inc., 2015.\\nURL http://papers.nips.cc/paper/5846-end-to-end-memory-networks.pdf .\\n14', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 13}),\n", " Document(page_content='[56] <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. FEVER: a\\nlarge-scale dataset for fact extraction and VERiﬁcation. In Proceedings of the 2018 Conference\\nof the North American Chapter of the Association for Computational Linguistics: Human\\nLanguage Technologies, Volume 1 (Long Papers) , pages 809–819, New Orleans, Louisiana,\\nJune 2018. Association for Computational Linguistics. doi: 10.18653/v1/N18-1074. URL\\nhttps://www.aclweb.org/anthology/N18-1074 .', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 14}),\n", " Document(page_content='https://www.aclweb.org/anthology/N18-1074 .\\n[57] <PERSON> and <PERSON>. Avoiding catastrophic forgetting in mitigating model\\nbiases in sentence-pair classiﬁcation with elastic weight consolidation. ArXiv , abs/2004.14366,\\n2020. URL https://arxiv.org/abs/2004.14366 .\\n[58] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>,\\nŁ uk<PERSON><PERSON>, and <PERSON><PERSON>. Attention is all you need. In <PERSON><PERSON>, U. V . Luxburg,', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 14}),\n", " Document(page_content='<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural\\nInformation Processing Systems 30 , pages 5998–6008. Curran Associates, Inc., 2017. URL\\nhttp://papers.nips.cc/paper/7181-attention-is-all-you-need.pdf .\\n[59] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>\\<PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Diverse beam search for improved description of complex scenes.', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 14}),\n", " Document(page_content='AAAI Conference on Artiﬁcial Intelligence , 2018. URL https://www.aaai.org/ocs/index.\\nphp/AAAI/AAAI18/paper/view/17329 .\\n[60] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>.\\nGLUE: A multi-task benchmark and analysis platform for natural language understanding.\\nInProceedings of the 2018 EMNLP Workshop BlackboxNLP: Analyzing and Interpreting\\nNeural Networks for NLP , pages 353–355, Brussels, Belgium, November 2018. Association for', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 14}),\n", " Document(page_content='Computational Linguistics. doi: 10.18653/v1/W18-5446. URL https://www.aclweb.org/\\nanthology/W18-5446 .\\n[61] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. SuperGLUE: A Stickier Benchmark for General-\\nPurpose Language Understanding Systems. In <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>,\\nF. d\\\\textquotesingle Al<PERSON>é-<PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural Information', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 14}),\n", " Document(page_content='Processing Systems 32 , pages 3261–3275. Curran Associates, Inc., 2019. URL https://\\narxiv.org/abs/1905.00537 .\\n[62] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>,\\<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. R3: Reinforced ranker-reader for open-domain\\nquestion answering. In <PERSON> and <PERSON><PERSON>, editors, Proceedings of\\nthe Thirty-Second AAAI Conference on Artiﬁcial Intelligence, (AAAI-18), the 30th innovative', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 14}),\n", " Document(page_content='Applications of Artiﬁcial Intelligence (IAAI-18), and the 8th AAAI Symposium on Educational\\nAdvances in Artiﬁcial Intelligence (EAAI-18), New Orleans, Louisiana, USA, February 2-7,\\n2018 , pages 5981–5988. AAAI Press, 2018. URL https://www.aaai.org/ocs/index.\\nphp/AAAI/AAAI18/paper/view/16712 .\\n[63] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,\\n<PERSON><PERSON>, <PERSON>, and <PERSON>. Evidence aggregation for answer re-', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 14}),\n", " Document(page_content='ranking in open-domain question answering. In ICLR , 2018. URL https://openreview.\\nnet/forum?id=rJl3yM-Ab .\\n[64] <PERSON>, <PERSON><PERSON>, and <PERSON>. Memory networks. In <PERSON><PERSON><PERSON>\\nand <PERSON><PERSON>, editors, 3rd International Conference on Learning Representations, ICLR\\n2015, San Diego, CA, USA, May 7-9, 2015, Conference Track Proceedings , 2015. URL\\nhttp://arxiv.org/abs/1410.3916 .\\n[65] <PERSON>, <PERSON>, and <PERSON>. Retrieve and reﬁne: Improved sequence', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 14}),\n", " Document(page_content='generation models for dialogue. In Proceedings of the 2018 EMNLP Workshop SCAI: The 2nd\\nInternational Workshop on Search-Oriented Conversational AI , pages 87–92, Brussels, Belgium,\\nOctober 2018. Association for Computational Linguistics. doi: 10.18653/v1/W18-5713. URL\\nhttps://www.aclweb.org/anthology/W18-5713 .\\n15', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 14}),\n", " Document(page_content='[66] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>,\\<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>face’s transformers:\\nState-of-the-art natural language processing. ArXiv , abs/1910.03771, 2019.', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 15}),\n", " Document(page_content='[67] <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Addressing semantic drift in question generation for semi-\\nsupervised question answering. In Proceedings of the 2019 Conference on Empirical Meth-\\nods in Natural Language Processing and the 9th International Joint Conference on Natural\\nLanguage Processing (EMNLP-IJCNLP) , pages 2495–2509, Hong Kong, China, Novem-\\nber 2019. Association for Computational Linguistics. doi: 10.18653/v1/D19-1253. URL\\nhttps://www.aclweb.org/anthology/D19-1253 .', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 15}),\n", " Document(page_content='https://www.aclweb.org/anthology/D19-1253 .\\n[68] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and\\n<PERSON><PERSON>. Reasoning over semantic-level graph for fact checking. ArXiv , abs/1909.03745, 2019.\\nURL https://arxiv.org/abs/1909.03745 .\\n16', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 15}),\n", " Document(page_content='Appendices for Retrieval-Augmented Generation for\\nKnowledge-Intensive NLP Tasks\\nA Implementation Details\\nFor Open-domain QA we report test numbers using 15 retrieved documents for RAG-Token models.\\nFor RAG-Sequence models, we report test results using 50 retrieved documents, and we use the\\nThorough Decoding approach since answers are generally short. We use greedy decoding for QA as\\nwe did not ﬁnd beam search improved results. For Open-MSMarco and Jeopardy question generation,', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 16}),\n", " Document(page_content='we report test numbers using ten retrieved documents for both RAG-Token and RAG-Sequence,\\nand we also train a BART-large model as a baseline. We use a beam size of four, and use the Fast\\nDecoding approach for RAG-Sequence models, as Thorough Decoding did not improve performance.\\nB Human Evaluation\\nFigure 4: Annotation interface for human evaluation of factuality. A pop-out for detailed instructions\\nand a worked example appear when clicking \"view tool guide\".', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 16}),\n", " Document(page_content='and a worked example appear when clicking \"view tool guide\".\\nFigure 4 shows the user interface for human evaluation. To avoid any biases for screen position,\\nwhich model corresponded to sentence A and sentence B was randomly selected for each example.\\nAnnotators were encouraged to research the topic using the internet, and were given detailed instruc-\\ntions and worked examples in a full instructions tab. We included some gold sentences in order to', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 16}),\n", " Document(page_content='assess the accuracy of the annotators. Two annotators did not perform well on these examples and\\ntheir annotations were removed from the results.\\nC Training setup Details\\nWe train all RAG models and BART baselines using Fairseq [ 45].2We train with mixed precision\\nﬂoating point arithmetic [ 40], distributing training across 8, 32GB NVIDIA V100 GPUs, though\\ntraining and inference can be run on one GPU. We ﬁnd that doing Maximum Inner Product Search', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 16}),\n", " Document(page_content='with FAISS is sufﬁciently fast on CPU, so we store document index vectors on CPU, requiring ∼100\\nGB of CPU memory for all of Wikipedia. After submission, We have ported our code to HuggingFace\\nTransformers [ 66]3, which achieves equivalent performance to the previous version but is a cleaner\\nand easier to use implementation. This version is also open-sourced. We also compress the document\\nindex using FAISS’s compression tools, reducing the CPU memory requirement to 36GB. Scripts to', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 16}),\n", " Document(page_content='run experiments with RAG can be found at https://github.com/huggingface/transformers/\\nblob/master/examples/rag/README.md and an interactive demo of a RAG model can be found\\nathttps://huggingface.co/rag/\\n2https://github.com/pytorch/fairseq\\n3https://github.com/huggingface/transformers\\n17', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 16}),\n", " Document(page_content='D Further Details on Open-Domain QA\\nFor open-domain QA, multiple answer annotations are often available for a given question. These\\nanswer annotations are exploited by extractive models during training as typically all the answer\\nannotations are used to ﬁnd matches within documents when preparing training data. For RAG, we\\nalso make use of multiple annotation examples for Natural Questions and WebQuestions by training', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 17}),\n", " Document(page_content='the model with each (q,a)pair separately, leading to a small increase in accuracy. For TriviaQA,\\nthere are often many valid answers to a given question, some of which are not suitable training targets,\\nsuch as emoji or spelling variants. For TriviaQA, we ﬁlter out answer candidates if they do not occur\\nin top 1000 documents for the query.\\nCuratedTrec preprocessing The answers for CuratedTrec are given in the form of regular expres-', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 17}),\n", " Document(page_content='sions, which has been suggested as a reason why it is unsuitable for answer-generation models [20].\\nTo overcome this, we use a pre-processing step where we ﬁrst retrieve the top 1000 documents for\\neach query, and use the answer that most frequently matches the regex pattern as the supervision\\ntarget. If no matches are found, we resort to a simple heuristic: generate all possible permutations for\\neach regex, replacing non-deterministic symbols in the regex nested tree structure with a whitespace.', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 17}),\n", " Document(page_content='TriviaQA Evaluation setups The open-domain QA community customarily uses public develop-\\nment datasets as test datasets, as test data for QA datasets is often restricted and dedicated to reading\\ncompehension purposes. We report our results using the datasets splits used in DPR [ 26], which are\\nconsistent with common practice in Open-domain QA. For TriviaQA, this test dataset is the public\\nTriviaQA Web Development split. <PERSON> et al. [52] used the TriviaQA ofﬁcial Wikipedia test set', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 17}),\n", " Document(page_content='instead. <PERSON><PERSON><PERSON><PERSON> et al. [14] follow this convention in order to compare with <PERSON> et al. [52] (See\\nappendix of [ 14]). We report results on both test sets to enable fair comparison to both approaches.\\nWe ﬁnd that our performance is much higher using the ofﬁcial Wiki test set, rather than the more\\nconventional open-domain test set, which we attribute to the ofﬁcial Wiki test set questions being\\nsimpler to answer from Wikipedia.\\nE Further Details on FEVER', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 17}),\n", " Document(page_content='simpler to answer from Wikipedia.\\nE Further Details on FEVER\\nFor FEVER classiﬁcation, we follow the practice from [ 32], and ﬁrst re-generate the claim, and\\nthen classify using the representation of the ﬁnal hidden state, before ﬁnally marginalizing across\\ndocuments to obtain the class probabilities. The FEVER task traditionally has two sub-tasks. The\\nﬁrst is to classify the claim as either \"Supported\", \"Refuted\" or \"Not Enough Info\", which is the task', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 17}),\n", " Document(page_content='we explore in the main paper. FEVER’s other sub-task involves extracting sentences from Wikipedia\\nas evidence supporting the classiﬁcation prediction. As FEVER uses a different Wikipedia dump to\\nus, directly tackling this task is not straightforward. We hope to address this in future work.\\nF Null Document Probabilities\\nWe experimented with adding \"Null document\" mechanism to RAG, similar to REALM [ 20] in order', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 17}),\n", " Document(page_content='to model cases where no useful information could be retrieved for a given input. Here, if kdocuments\\nwere retrieved, we would additionally \"retrieve\" an empty document and predict a logit for the null\\ndocument, before marginalizing over k+ 1predictions. We explored modelling this null document\\nlogit by learning (i) a document embedding for the null document, (ii) a static learnt bias term, or\\n(iii) a neural network to predict the logit. We did not ﬁnd that these improved performance, so in', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 17}),\n", " Document(page_content='the interests of simplicity, we omit them. For Open MS-MARCO, where useful retrieved documents\\ncannot always be retrieved, we observe that the model learns to always retrieve a particular set of\\ndocuments for questions that are less likely to beneﬁt from retrieval, suggesting that null document\\nmechanisms may not be necessary for RAG.\\nG Parameters\\nOur RAG models contain the trainable parameters for the BERT-base query and document encoder of', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 17}),\n", " Document(page_content='DPR, with 110M parameters each (although we do not train the document encoder ourselves) and\\n406M trainable parameters from BART-large, 406M parameters, making a total of 626M trainable\\n18', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 17}),\n", " Document(page_content='Table 7: Number of instances in the datasets used. *A hidden subset of this data is used for evaluation\\nTask Train Development Test\\nNatural Questions 79169 8758 3611\\nTriviaQA 78786 8838 11314\\nWebQuestions 3418 362 2033\\nCuratedTrec 635 134 635\\nJeopardy Question Generation 97392 13714 26849\\nMS-MARCO 153726 12468 101093*\\nFEVER-3-way 145450 10000 10000\\nFEVER-2-way 96966 6666 6666\\nparameters. The best performing \"closed-book\" (parametric only) open-domain QA model is T5-11B', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 18}),\n", " Document(page_content='with 11 Billion trainable parameters. The T5 model with the closest number of parameters to our\\nmodels is T5-large (770M parameters), which achieves a score of 28.9 EM on Natural Questions [ 52],\\nsubstantially below the 44.5 that RAG-Sequence achieves, indicating that hybrid parametric/non-\\nparametric models require far fewer trainable parameters for strong open-domain QA performance.\\nThe non-parametric memory index does not consist of trainable parameters, but does consists of 21M', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 18}),\n", " Document(page_content='728 dimensional vectors, consisting of 15.3B values. These can be easily be stored at 8-bit ﬂoating\\npoint precision to manage memory and disk footprints.\\nH Retrieval Collapse\\nIn preliminary experiments, we observed that for some tasks such as story generation [ 11], the\\nretrieval component would “collapse” and learn to retrieve the same documents regardless of the\\ninput. In these cases, once retrieval had collapsed, the generator would learn to ignore the documents,', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 18}),\n", " Document(page_content='and the RAG model would perform equivalently to BART. The collapse could be due to a less-explicit\\nrequirement for factual knowledge in some tasks, or the longer target sequences, which could result\\nin less informative gradients for the retriever. <PERSON> et al. [46] also found spurious retrieval results\\nwhen optimizing a retrieval component in order to improve performance on downstream tasks.\\nI Number of instances per dataset', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 18}),\n", " Document(page_content='I Number of instances per dataset\\nThe number of training, development and test datapoints in each of our datasets is shown in Table 7.\\n19', metadata={'source': '/content/data/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 18})]"]}, "metadata": {}, "execution_count": 49}]}, {"cell_type": "code", "source": ["docs=[]\n", "for pdf in pdfs:\n", "  data=PyPDFLoader(f\"/content/data/{pdf}\")\n", "  docs.append(data)"], "metadata": {"id": "hK6CgClrbbS5"}, "execution_count": 45, "outputs": []}, {"cell_type": "code", "source": ["\n", "docs_from_pdf = docs.load_and_split(text_splitter=splitter)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 158}, "id": "agk6IZLabd3p", "outputId": "ffdbaa2b-58a4-406f-c781-a9a9fa2b20c7"}, "execution_count": 47, "outputs": [{"output_type": "error", "ename": "AttributeError", "evalue": "'list' object has no attribute 'load_and_split'", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-47-d14b615aab55>\u001b[0m in \u001b[0;36m<cell line: 2>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0msplitter\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mRecursiveCharacterTextSplitter\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mchunk_size\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m512\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mchunk_overlap\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m64\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m \u001b[0mdocs_from_pdf\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mdocs\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mload_and_split\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mtext_splitter\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0msplitter\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m: 'list' object has no attribute 'load_and_split'"]}]}, {"cell_type": "code", "source": ["print(f\"Documents from PDF: {len(docs_from_pdf)}.\")\n", "inserted_ids_from_pdf = vstore.add_documents(docs_from_pdf)\n", "print(f\"Inserted {len(inserted_ids_from_pdf)} documents.\")"], "metadata": {"id": "oNllVkvIbgKM"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["vstore = AstraDBVectorStore(\n", "    embedding=embedding,\n", "    collection_name=\"astra_vector_demo\",\n", "    api_endpoint=ASTRA_DB_API_ENDPOINT,\n", "    token=ASTRA_DB_APPLICATION_TOKEN,\n", "    namespace=ASTRA_DB_KEYSPACE,\n", ")"], "metadata": {"id": "n4G743wn3i9F"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["retriever = vstore.as_retriever(search_kwargs={\"k\": 3})"], "metadata": {"id": "cfzD7a8naIEK"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["prompt_template = \"\"\"\n", "You are a philosopher that draws inspiration from great thinkers of the past\n", "to craft well-thought answers to user questions. Use the provided context as the basis\n", "for your answers and do not make up new reasoning paths - just mix-and-match what you are given.\n", "Your answers must be concise and to the point, and refrain from answering about other topics than philosophy.\n", "\n", "CONTEXT:\n", "{context}\n", "\n", "QUESTION: {question}\n", "\n", "YOUR ANSWER:\"\"\""], "metadata": {"id": "9Y2EFU9_aINQ"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["prompt_template = ChatPromptTemplate.from_template(prompt_template)"], "metadata": {"id": "Nx0rM706aIPo"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["llm = ChatOpenAI()\n", "\n", "chain = (\n", "    {\"context\": retriever, \"question\": RunnablePassthrough()}\n", "    | philo_prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")"], "metadata": {"id": "tRg2VFehaISq"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chain.invoke(\"How does <PERSON><PERSON> elaborate on <PERSON><PERSON><PERSON><PERSON>'s idea of the security blanket?\")"], "metadata": {"id": "D9pg2syhbyHI"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["# Directory loders(<PERSON><PERSON> With Multiple Doc)"], "metadata": {"id": "v2b452jhb6mh"}}, {"cell_type": "code", "source": ["!rm -rf \"/content/docs/.ipynb_checkpoints\""], "metadata": {"id": "tZS1rEQB7YOP"}, "execution_count": 2, "outputs": []}, {"cell_type": "code", "source": ["%pip install langchain_community"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1yqDtZ1M3z8U", "outputId": "1602047a-f75d-4544-e49d-d1ea5405e3f6"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain_community\n", "  Downloading langchain_community-0.0.38-py3-none-any.whl (2.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m21.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (2.0.30)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (3.9.5)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain_community)\n", "  Downloading dataclasses_json-0.6.6-py3-none-any.whl (28 kB)\n", "Collecting langchain-core<0.2.0,>=0.1.52 (from langchain_community)\n", "  Downloading langchain_core-0.1.52-py3-none-any.whl (302 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m302.9/302.9 kB\u001b[0m \u001b[31m24.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langsmith<0.2.0,>=0.1.0 (from langchain_community)\n", "  Downloading langsmith-0.1.57-py3-none-any.whl (121 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m121.0/121.0 kB\u001b[0m \u001b[31m15.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (1.25.2)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (8.3.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (4.0.3)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain_community)\n", "  Downloading marshmallow-3.21.2-py3-none-any.whl (49 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.3/49.3 kB\u001b[0m \u001b[31m6.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain_community)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain-core<0.2.0,>=0.1.52->langchain_community)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Collecting packaging<24.0,>=23.2 (from langchain-core<0.2.0,>=0.1.52->langchain_community)\n", "  Downloading packaging-23.2-py3-none-any.whl (53 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m53.0/53.0 kB\u001b[0m \u001b[31m8.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.52->langchain_community) (2.7.1)\n", "Collecting or<PERSON><PERSON><4.0.0,>=3.9.14 (from langsmith<0.2.0,>=0.1.0->langchain_community)\n", "  Downloading orjson-3.10.3-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (142 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m142.5/142.5 kB\u001b[0m \u001b[31m18.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (2024.2.2)\n", "Requirement already satisfied: typing-extensions>=4.6.0 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain_community) (4.11.0)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain_community) (3.0.3)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core<0.2.0,>=0.1.52->langchain_community)\n", "  Downloading jsonpointer-2.4-py2.py3-none-any.whl (7.8 kB)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain-core<0.2.0,>=0.1.52->langchain_community) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.2 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain-core<0.2.0,>=0.1.52->langchain_community) (2.18.2)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain_community)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: packaging, orjson, mypy-extensions, jsonpointer, typing-inspect, marshmallow, jsonpatch, langsmith, dataclasses-json, langchain-core, langchain_community\n", "  Attempting uninstall: packaging\n", "    Found existing installation: packaging 24.0\n", "    Uninstalling packaging-24.0:\n", "      Successfully uninstalled packaging-24.0\n", "Successfully installed dataclasses-json-0.6.6 jsonpatch-1.33 jsonpointer-2.4 langchain-core-0.1.52 langchain_community-0.0.38 langsmith-0.1.57 marshmallow-3.21.2 mypy-extensions-1.0.0 orjson-3.10.3 packaging-23.2 typing-inspect-0.9.0\n"]}]}, {"cell_type": "code", "source": ["!pip install unstructured"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1UuNkzrU5Q5q", "outputId": "3f4e6178-064f-406e-cf4a-909229fb3da6"}, "execution_count": 4, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting unstructured\n", "  Downloading unstructured-0.13.7-py3-none-any.whl (1.9 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.9/1.9 MB\u001b[0m \u001b[31m24.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: chardet in /usr/local/lib/python3.10/dist-packages (from unstructured) (5.2.0)\n", "Collecting filetype (from unstructured)\n", "  Downloading filetype-1.2.0-py2.py3-none-any.whl (19 kB)\n", "Collecting python-magic (from unstructured)\n", "  Downloading python_magic-0.4.27-py2.py3-none-any.whl (13 kB)\n", "Requirement already satisfied: lxml in /usr/local/lib/python3.10/dist-packages (from unstructured) (4.9.4)\n", "Requirement already satisfied: nltk in /usr/local/lib/python3.10/dist-packages (from unstructured) (3.8.1)\n", "Requirement already satisfied: tabulate in /usr/local/lib/python3.10/dist-packages (from unstructured) (0.9.0)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from unstructured) (2.31.0)\n", "Requirement already satisfied: beautifulsoup4 in /usr/local/lib/python3.10/dist-packages (from unstructured) (4.12.3)\n", "Collecting emoji (from unstructured)\n", "  Downloading emoji-2.11.1-py2.py3-none-any.whl (433 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m433.8/433.8 kB\u001b[0m \u001b[31m36.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: dataclasses-json in /usr/local/lib/python3.10/dist-packages (from unstructured) (0.6.6)\n", "Collecting python-iso639 (from unstructured)\n", "  Downloading python_iso639-2024.4.27-py3-none-any.whl (274 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m274.7/274.7 kB\u001b[0m \u001b[31m33.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langdetect (from unstructured)\n", "  Downloading langdetect-1.0.9.tar.gz (981 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m981.5/981.5 kB\u001b[0m \u001b[31m53.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from unstructured) (1.25.2)\n", "Collecting rapidfuzz (from unstructured)\n", "  Downloading rapidfuzz-3.9.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.4 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.4/3.4 MB\u001b[0m \u001b[31m40.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting backoff (from unstructured)\n", "  Downloading backoff-2.2.1-py3-none-any.whl (15 kB)\n", "Requirement already satisfied: typing-extensions in /usr/local/lib/python3.10/dist-packages (from unstructured) (4.11.0)\n", "Collecting unstructured-client (from unstructured)\n", "  Downloading unstructured_client-0.22.0-py3-none-any.whl (28 kB)\n", "Requirement already satisfied: wrapt in /usr/local/lib/python3.10/dist-packages (from unstructured) (1.14.1)\n", "Requirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.10/dist-packages (from beautifulsoup4->unstructured) (2.5)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->unstructured) (3.21.2)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->unstructured) (0.9.0)\n", "Requirement already satisfied: six in /usr/local/lib/python3.10/dist-packages (from langdetect->unstructured) (1.16.0)\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured) (8.1.7)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured) (1.4.2)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured) (2023.12.25)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured) (4.66.4)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured) (2024.2.2)\n", "Collecting deepdiff>=6.0 (from unstructured-client->unstructured)\n", "  Downloading deepdiff-7.0.1-py3-none-any.whl (80 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m80.8/80.8 kB\u001b[0m \u001b[31m10.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting jsonpath-python>=1.0.6 (from unstructured-client->unstructured)\n", "  Downloading jsonpath_python-1.0.6-py3-none-any.whl (7.6 kB)\n", "Requirement already satisfied: mypy-extensions>=1.0.0 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured) (1.0.0)\n", "Requirement already satisfied: packaging>=23.1 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured) (23.2)\n", "Collecting pypdf>=4.0 (from unstructured-client->unstructured)\n", "  Downloading pypdf-4.2.0-py3-none-any.whl (290 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m290.4/290.4 kB\u001b[0m \u001b[31m27.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured) (2.8.2)\n", "Collecting ordered-set<4.2.0,>=4.1.0 (from deepdiff>=6.0->unstructured-client->unstructured)\n", "  Downloading ordered_set-4.1.0-py3-none-any.whl (7.6 kB)\n", "Building wheels for collected packages: langdetect\n", "  Building wheel for langdetect (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for langdetect: filename=langdetect-1.0.9-py3-none-any.whl size=993227 sha256=1f09b72aaa02dc2ea6c1dbaa05856ac91c8fb233003bdb4614e30a8bc025eecc\n", "  Stored in directory: /root/.cache/pip/wheels/95/03/7d/59ea870c70ce4e5a370638b5462a7711ab78fba2f655d05106\n", "Successfully built langdetect\n", "Installing collected packages: filetype, rapidfuzz, python-magic, python-iso639, pypdf, ordered-set, langdetect, jsonpath-python, emoji, backoff, deepdiff, unstructured-client, unstructured\n", "Successfully installed backoff-2.2.1 deepdiff-7.0.1 emoji-2.11.1 filetype-1.2.0 jsonpath-python-1.0.6 langdetect-1.0.9 ordered-set-4.1.0 pypdf-4.2.0 python-iso639-2024.4.27 python-magic-0.4.27 rapidfuzz-3.9.0 unstructured-0.13.7 unstructured-client-0.22.0\n"]}]}, {"cell_type": "code", "source": ["!pip install \"unstructured[pdf]\""], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "ksK7gi4p5d1l", "outputId": "dc8ba9fb-b8fd-46cc-b38c-ebbafca693c7"}, "execution_count": 5, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: unstructured[pdf] in /usr/local/lib/python3.10/dist-packages (0.13.7)\n", "Requirement already satisfied: chardet in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (5.2.0)\n", "Requirement already satisfied: filetype in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (1.2.0)\n", "Requirement already satisfied: python-magic in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (0.4.27)\n", "Requirement already satisfied: lxml in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (4.9.4)\n", "Requirement already satisfied: nltk in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (3.8.1)\n", "Requirement already satisfied: tabulate in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (0.9.0)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (2.31.0)\n", "Requirement already satisfied: beautifulsoup4 in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (4.12.3)\n", "Requirement already satisfied: emoji in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (2.11.1)\n", "Requirement already satisfied: dataclasses-json in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (0.6.6)\n", "Requirement already satisfied: python-iso639 in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (2024.4.27)\n", "Requirement already satisfied: langdetect in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (1.0.9)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (1.25.2)\n", "Requirement already satisfied: rapidfuzz in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (3.9.0)\n", "Requirement already satisfied: backoff in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (2.2.1)\n", "Requirement already satisfied: typing-extensions in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (4.11.0)\n", "Requirement already satisfied: unstructured-client in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (0.22.0)\n", "Requirement already satisfied: wrapt in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (1.14.1)\n", "Collecting onnx (from unstructured[pdf])\n", "  Downloading onnx-1.16.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (15.9 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m15.9/15.9 MB\u001b[0m \u001b[31m23.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting pdf2image (from unstructured[pdf])\n", "  Downloading pdf2image-1.17.0-py3-none-any.whl (11 kB)\n", "Collecting pdfminer.six (from unstructured[pdf])\n", "  Downloading pdfminer.six-********-py3-none-any.whl (5.6 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.6/5.6 MB\u001b[0m \u001b[31m79.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting pikepdf (from unstructured[pdf])\n", "  Downloading pikepdf-8.15.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.4 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.4/2.4 MB\u001b[0m \u001b[31m82.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting pillow-heif (from unstructured[pdf])\n", "  Downloading pillow_heif-0.16.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (7.5 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7.5/7.5 MB\u001b[0m \u001b[31m63.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pypdf in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (4.2.0)\n", "Collecting unstructured-inference==0.7.31 (from unstructured[pdf])\n", "  Downloading unstructured_inference-0.7.31-py3-none-any.whl (59 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m59.5/59.5 kB\u001b[0m \u001b[31m8.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting unstructured.pytesseract>=0.3.12 (from unstructured[pdf])\n", "  Downloading unstructured.pytesseract-0.3.12-py3-none-any.whl (14 kB)\n", "Collecting google-cloud-vision (from unstructured[pdf])\n", "  Downloading google_cloud_vision-3.7.2-py2.py3-none-any.whl (459 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m459.6/459.6 kB\u001b[0m \u001b[31m37.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting layoutparser[layoutmodels,tesseract] (from unstructured-inference==0.7.31->unstructured[pdf])\n", "  Downloading layoutparser-0.3.4-py3-none-any.whl (19.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m19.2/19.2 MB\u001b[0m \u001b[31m45.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting python-multipart (from unstructured-inference==0.7.31->unstructured[pdf])\n", "  Downloading python_multipart-0.0.9-py3-none-any.whl (22 kB)\n", "Requirement already satisfied: huggingface-hub in /usr/local/lib/python3.10/dist-packages (from unstructured-inference==0.7.31->unstructured[pdf]) (0.20.3)\n", "Requirement already satisfied: opencv-python!=******** in /usr/local/lib/python3.10/dist-packages (from unstructured-inference==0.7.31->unstructured[pdf]) (********)\n", "Collecting onnxruntime>=1.17.0 (from unstructured-inference==0.7.31->unstructured[pdf])\n", "  Downloading onnxruntime-1.17.3-cp310-cp310-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (6.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.8/6.8 MB\u001b[0m \u001b[31m52.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: transformers>=4.25.1 in /usr/local/lib/python3.10/dist-packages (from unstructured-inference==0.7.31->unstructured[pdf]) (4.40.2)\n", "Requirement already satisfied: packaging>=21.3 in /usr/local/lib/python3.10/dist-packages (from unstructured.pytesseract>=0.3.12->unstructured[pdf]) (23.2)\n", "Requirement already satisfied: Pillow>=8.0.0 in /usr/local/lib/python3.10/dist-packages (from unstructured.pytesseract>=0.3.12->unstructured[pdf]) (9.4.0)\n", "Requirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.10/dist-packages (from beautifulsoup4->unstructured[pdf]) (2.5)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->unstructured[pdf]) (3.21.2)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->unstructured[pdf]) (0.9.0)\n", "Requirement already satisfied: google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1 in /usr/local/lib/python3.10/dist-packages (from google-cloud-vision->unstructured[pdf]) (2.11.1)\n", "Requirement already satisfied: google-auth!=2.24.0,!=2.25.0,<3.0.0dev,>=2.14.1 in /usr/local/lib/python3.10/dist-packages (from google-cloud-vision->unstructured[pdf]) (2.27.0)\n", "Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.3 in /usr/local/lib/python3.10/dist-packages (from google-cloud-vision->unstructured[pdf]) (1.23.0)\n", "Requirement already satisfied: protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5 in /usr/local/lib/python3.10/dist-packages (from google-cloud-vision->unstructured[pdf]) (3.20.3)\n", "Requirement already satisfied: six in /usr/local/lib/python3.10/dist-packages (from langdetect->unstructured[pdf]) (1.16.0)\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured[pdf]) (8.1.7)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured[pdf]) (1.4.2)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured[pdf]) (2023.12.25)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured[pdf]) (4.66.4)\n", "Requirement already satisfied: charset-normalizer>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from pdfminer.six->unstructured[pdf]) (3.3.2)\n", "Requirement already satisfied: cryptography>=36.0.0 in /usr/local/lib/python3.10/dist-packages (from pdfminer.six->unstructured[pdf]) (42.0.7)\n", "Collecting Pillow>=8.0.0 (from unstructured.pytesseract>=0.3.12->unstructured[pdf])\n", "  Downloading pillow-10.3.0-cp310-cp310-manylinux_2_28_x86_64.whl (4.5 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.5/4.5 MB\u001b[0m \u001b[31m41.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting Deprecated (from pikepdf->unstructured[pdf])\n", "  Downloading Deprecated-1.2.14-py2.py3-none-any.whl (9.6 kB)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured[pdf]) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured[pdf]) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured[pdf]) (2024.2.2)\n", "Requirement already satisfied: deepdiff>=6.0 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured[pdf]) (7.0.1)\n", "Requirement already satisfied: jsonpath-python>=1.0.6 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured[pdf]) (1.0.6)\n", "Requirement already satisfied: mypy-extensions>=1.0.0 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured[pdf]) (1.0.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured[pdf]) (2.8.2)\n", "Requirement already satisfied: cffi>=1.12 in /usr/local/lib/python3.10/dist-packages (from cryptography>=36.0.0->pdfminer.six->unstructured[pdf]) (1.16.0)\n", "Requirement already satisfied: ordered-set<4.2.0,>=4.1.0 in /usr/local/lib/python3.10/dist-packages (from deepdiff>=6.0->unstructured-client->unstructured[pdf]) (4.1.0)\n", "Requirement already satisfied: googleapis-common-protos<2.0.dev0,>=1.56.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-cloud-vision->unstructured[pdf]) (1.63.0)\n", "Requirement already satisfied: grpcio<2.0dev,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-cloud-vision->unstructured[pdf]) (1.63.0)\n", "Requirement already satisfied: grpcio-status<2.0.dev0,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-cloud-vision->unstructured[pdf]) (1.48.2)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0dev,>=2.14.1->google-cloud-vision->unstructured[pdf]) (5.3.3)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0dev,>=2.14.1->google-cloud-vision->unstructured[pdf]) (0.4.0)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0dev,>=2.14.1->google-cloud-vision->unstructured[pdf]) (4.9)\n", "Collecting coloredlogs (from onnxruntime>=1.17.0->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Downloading coloredlogs-15.0.1-py2.py3-none-any.whl (46 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.0/46.0 kB\u001b[0m \u001b[31m5.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: flatbuffers in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.17.0->unstructured-inference==0.7.31->unstructured[pdf]) (24.3.25)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.17.0->unstructured-inference==0.7.31->unstructured[pdf]) (1.12)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from transformers>=4.25.1->unstructured-inference==0.7.31->unstructured[pdf]) (3.14.0)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from transformers>=4.25.1->unstructured-inference==0.7.31->unstructured[pdf]) (6.0.1)\n", "Requirement already satisfied: tokenizers<0.20,>=0.19 in /usr/local/lib/python3.10/dist-packages (from transformers>=4.25.1->unstructured-inference==0.7.31->unstructured[pdf]) (0.19.1)\n", "Requirement already satisfied: safetensors>=0.4.1 in /usr/local/lib/python3.10/dist-packages (from transformers>=4.25.1->unstructured-inference==0.7.31->unstructured[pdf]) (0.4.3)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub->unstructured-inference==0.7.31->unstructured[pdf]) (2023.6.0)\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.10/dist-packages (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf]) (1.11.4)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf]) (2.0.3)\n", "Collecting iopath (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Downloading iopath-0.1.10.tar.gz (42 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m42.2/42.2 kB\u001b[0m \u001b[31m4.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Collecting pdfplumber (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Downloading pdfplumber-0.11.0-py3-none-any.whl (56 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.4/56.4 kB\u001b[0m \u001b[31m7.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting pytesseract (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Downloading pytesseract-0.3.10-py3-none-any.whl (14 kB)\n", "Requirement already satisfied: torch in /usr/local/lib/python3.10/dist-packages (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf]) (2.2.1+cu121)\n", "Requirement already satisfied: torchvision in /usr/local/lib/python3.10/dist-packages (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf]) (0.17.1+cu121)\n", "Collecting effdet (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Downloading effdet-0.4.1-py3-none-any.whl (112 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m112.5/112.5 kB\u001b[0m \u001b[31m10.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pycparser in /usr/local/lib/python3.10/dist-packages (from cffi>=1.12->cryptography>=36.0.0->pdfminer.six->unstructured[pdf]) (2.22)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth!=2.24.0,!=2.25.0,<3.0.0dev,>=2.14.1->google-cloud-vision->unstructured[pdf]) (0.6.0)\n", "Collecting humanfriendly>=9.1 (from coloredlogs->onnxruntime>=1.17.0->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Downloading humanfriendly-10.0-py2.py3-none-any.whl (86 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.8/86.8 kB\u001b[0m \u001b[31m11.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting timm>=0.9.2 (from effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Downloading timm-0.9.16-py3-none-any.whl (2.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.2/2.2 MB\u001b[0m \u001b[31m43.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pycocotools>=2.0.2 in /usr/local/lib/python3.10/dist-packages (from effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf]) (2.0.7)\n", "Collecting omegaconf>=2.0 (from effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Downloading omegaconf-2.3.0-py3-none-any.whl (79 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m79.5/79.5 kB\u001b[0m \u001b[31m8.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf]) (3.3)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf]) (3.1.4)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.1.105 (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Using cached nvidia_cuda_nvrtc_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (23.7 MB)\n", "Collecting nvidia-cuda-runtime-cu12==12.1.105 (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Using cached nvidia_cuda_runtime_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (823 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.1.105 (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Using cached nvidia_cuda_cupti_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (14.1 MB)\n", "Collecting nvidia-cudnn-cu12==******** (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Using cached nvidia_cudnn_cu12-********-py3-none-manylinux1_x86_64.whl (731.7 MB)\n", "Collecting nvidia-cublas-cu12==******** (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Using cached nvidia_cublas_cu12-********-py3-none-manylinux1_x86_64.whl (410.6 MB)\n", "Collecting nvidia-cufft-cu12==********* (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Using cached nvidia_cufft_cu12-*********-py3-none-manylinux1_x86_64.whl (121.6 MB)\n", "Collecting nvidia-curand-cu12==********** (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Using cached nvidia_curand_cu12-**********-py3-none-manylinux1_x86_64.whl (56.5 MB)\n", "Collecting nvidia-cusolver-cu12==********** (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Using cached nvidia_cusolver_cu12-**********-py3-none-manylinux1_x86_64.whl (124.2 MB)\n", "Collecting nvidia-cusparse-cu12==********** (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Using cached nvidia_cusparse_cu12-**********-py3-none-manylinux1_x86_64.whl (196.0 MB)\n", "Collecting nvidia-nccl-cu12==2.19.3 (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Using cached nvidia_nccl_cu12-2.19.3-py3-none-manylinux1_x86_64.whl (166.0 MB)\n", "Collecting nvidia-nvtx-cu12==12.1.105 (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Using cached nvidia_nvtx_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (99 kB)\n", "Requirement already satisfied: triton==2.2.0 in /usr/local/lib/python3.10/dist-packages (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf]) (2.2.0)\n", "Collecting nvidia-nvjitlink-cu12 (from nvidia-cusolver-cu12==**********->torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Using cached nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n", "Collecting portalocker (from iopath->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Downloading portalocker-2.8.2-py3-none-any.whl (17 kB)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf]) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf]) (2024.1)\n", "Collecting pypdfium2>=4.18.0 (from pdfplumber->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Downloading pypdfium2-4.30.0-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.8/2.8 MB\u001b[0m \u001b[31m58.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->onnxruntime>=1.17.0->unstructured-inference==0.7.31->unstructured[pdf]) (1.3.0)\n", "Collecting antlr4-python3-runtime==4.9.* (from omegaconf>=2.0->effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf])\n", "  Downloading antlr4-python3-runtime-4.9.3.tar.gz (117 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m117.0/117.0 kB\u001b[0m \u001b[31m14.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: matplotlib>=2.1.0 in /usr/local/lib/python3.10/dist-packages (from pycocotools>=2.0.2->effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf]) (3.7.1)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf]) (2.1.5)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib>=2.1.0->pycocotools>=2.0.2->effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf]) (1.2.1)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.10/dist-packages (from matplotlib>=2.1.0->pycocotools>=2.0.2->effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf]) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.10/dist-packages (from matplotlib>=2.1.0->pycocotools>=2.0.2->effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf]) (4.51.0)\n", "Requirement already satisfied: kiwisolver>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib>=2.1.0->pycocotools>=2.0.2->effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf]) (1.4.5)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib>=2.1.0->pycocotools>=2.0.2->effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.31->unstructured[pdf]) (3.1.2)\n", "Building wheels for collected packages: iopath, antlr4-python3-runtime\n", "  Building wheel for iopath (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for iopath: filename=iopath-0.1.10-py3-none-any.whl size=31532 sha256=a7d4a93fc63c460d43b8874950eac5a7c61dc78433fd53526e44f3f49e884ddc\n", "  Stored in directory: /root/.cache/pip/wheels/9a/a3/b6/ac0fcd1b4ed5cfeb3db92e6a0e476cfd48ed0df92b91080c1d\n", "  Building wheel for antlr4-python3-runtime (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for antlr4-python3-runtime: filename=antlr4_python3_runtime-4.9.3-py3-none-any.whl size=144554 sha256=bc9d9edbe21b60620d3844230d6a2b02158fd1bf6c84b4015241c64abe88e004\n", "  Stored in directory: /root/.cache/pip/wheels/12/93/dd/1f6a127edc45659556564c5730f6d4e300888f4bca2d4c5a88\n", "Successfully built iopath antlr4-python3-runtime\n", "Installing collected packages: antlr4-python3-runtime, python-multipart, pypdfium2, portalocker, Pillow, onnx, omegaconf, nvidia-nvtx-cu12, nvidia-nvjitlink-cu12, nvidia-nccl-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, humanfriendly, Deprecated, unstructured.pytesseract, pytesseract, pillow-heif, pikepdf, pdf2image, nvidia-cusparse-cu12, nvidia-cudnn-cu12, iopath, coloredlogs, pdfminer.six, onnxruntime, nvidia-cusolver-cu12, pdfplumber, layoutparser, google-cloud-vision, timm, effdet, unstructured-inference\n", "  Attempting uninstall: Pillow\n", "    Found existing installation: Pillow 9.4.0\n", "    Uninstalling Pillow-9.4.0:\n", "      Successfully uninstalled Pillow-9.4.0\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "imageio 2.31.6 requires pillow<10.1.0,>=8.3.2, but you have pillow 10.3.0 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed Deprecated-1.2.14 Pillow-10.3.0 antlr4-python3-runtime-4.9.3 coloredlogs-15.0.1 effdet-0.4.1 google-cloud-vision-3.7.2 humanfriendly-10.0 iopath-0.1.10 layoutparser-0.3.4 nvidia-cublas-cu12-******** nvidia-cuda-cupti-cu12-12.1.105 nvidia-cuda-nvrtc-cu12-12.1.105 nvidia-cuda-runtime-cu12-12.1.105 nvidia-cudnn-cu12-******** nvidia-cufft-cu12-********* nvidia-curand-cu12-********** nvidia-cusolver-cu12-********** nvidia-cusparse-cu12-********** nvidia-nccl-cu12-2.19.3 nvidia-nvjitlink-cu12-12.4.127 nvidia-nvtx-cu12-12.1.105 omegaconf-2.3.0 onnx-1.16.0 onnxruntime-1.17.3 pdf2image-1.17.0 pdfminer.six-******** pdfplumber-0.11.0 pikepdf-8.15.1 pillow-heif-0.16.0 portalocker-2.8.2 pypdfium2-4.30.0 pytesseract-0.3.10 python-multipart-0.0.9 timm-0.9.16 unstructured-inference-0.7.31 unstructured.pytesseract-0.3.12\n"]}, {"output_type": "display_data", "data": {"application/vnd.colab-display-data+json": {"pip_warning": {"packages": ["PIL", "google", "pydevd_plugins"]}, "id": "4b6ecf9a269b43f2b6a02e0b908f58cf"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["!sudo apt-get update"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3uk7ezbu7OQp", "outputId": "1bbf4d20-f90d-4247-b38f-bbab19599190"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\r0% [Working]\r            \rHit:1 http://archive.ubuntu.com/ubuntu jammy InRelease\n", "\r0% [Connecting to security.ubuntu.com (**************)] [Waiting for headers] [\r                                                                               \rHit:2 http://archive.ubuntu.com/ubuntu jammy-updates InRelease\n", "\r                                                                               \rHit:3 https://cloud.r-project.org/bin/linux/ubuntu jammy-cran40/ InRelease\n", "\r0% [Waiting for headers] [Connecting to security.ubuntu.com (**************)] [\r                                                                               \rHit:4 http://archive.ubuntu.com/ubuntu jammy-backports InRelease\n", "\r0% [Connecting to security.ubuntu.com (**************)] [Connected to ppa.launc\r                                                                               \rHit:5 https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64  InRelease\n", "Get:6 http://security.ubuntu.com/ubuntu jammy-security InRelease [110 kB]\n", "Hit:7 https://ppa.launchpadcontent.net/c2d4u.team/c2d4u4.0+/ubuntu jammy InRelease\n", "Hit:8 https://ppa.launchpadcontent.net/deadsnakes/ppa/ubuntu jammy InRelease\n", "Hit:9 https://ppa.launchpadcontent.net/graphics-drivers/ppa/ubuntu jammy InRelease\n", "Hit:10 https://ppa.launchpadcontent.net/ubuntugis/ppa/ubuntu jammy InRelease\n", "Fetched 110 kB in 1s (86.3 kB/s)\n", "Reading package lists... Done\n"]}]}, {"cell_type": "code", "source": ["!sudo apt-get install poppler-utils"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "nkesIO_m7P9P", "outputId": "0c321129-5b04-4a63-fded-445eab6bb4a2"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Reading package lists... Done\n", "Building dependency tree... Done\n", "Reading state information... Done\n", "poppler-utils is already the newest version (22.02.0-2ubuntu0.4).\n", "0 upgraded, 0 newly installed, 0 to remove and 52 not upgraded.\n"]}]}, {"cell_type": "code", "source": ["!sudo apt-get install libleptonica-dev tesseract-ocr libtesseract-dev python3-pil tesseract-ocr-eng tesseract-ocr-script-latn\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "o9OycnSq7Tt9", "outputId": "689a781d-dfb9-4c9d-d386-9f17804a3006"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Reading package lists... Done\n", "Building dependency tree... Done\n", "Reading state information... Done\n", "The following additional packages will be installed:\n", "  libarchive-dev libimagequant0 libraqm0 mailcap mime-support python3-olefile\n", "  tesseract-ocr-osd\n", "Suggested packages:\n", "  python-pil-doc\n", "The following NEW packages will be installed:\n", "  libarchive-dev libimagequant0 libleptonica-dev libraqm0 libtesseract-dev\n", "  mailcap mime-support python3-olefile python3-pil tesseract-ocr\n", "  tesseract-ocr-eng tesseract-ocr-osd tesseract-ocr-script-latn\n", "0 upgraded, 13 newly installed, 0 to remove and 52 not upgraded.\n", "Need to get 40.0 MB of archives.\n", "After this operation, 123 MB of additional disk space will be used.\n", "Get:1 http://archive.ubuntu.com/ubuntu jammy/main amd64 libarchive-dev amd64 3.6.0-1ubuntu1 [581 kB]\n", "Get:2 http://archive.ubuntu.com/ubuntu jammy/main amd64 libimagequant0 amd64 2.17.0-1 [34.6 kB]\n", "Get:3 http://archive.ubuntu.com/ubuntu jammy/universe amd64 libleptonica-dev amd64 1.82.0-3build1 [1,562 kB]\n", "Get:4 http://archive.ubuntu.com/ubuntu jammy/main amd64 libraqm0 amd64 0.7.0-4ubuntu1 [11.7 kB]\n", "Get:5 http://archive.ubuntu.com/ubuntu jammy/universe amd64 libtesseract-dev amd64 4.1.1-2.1build1 [1,600 kB]\n", "Get:6 http://archive.ubuntu.com/ubuntu jammy/main amd64 mailcap all 3.70+nmu1ubuntu1 [23.8 kB]\n", "Get:7 http://archive.ubuntu.com/ubuntu jammy/main amd64 mime-support all 3.66 [3,696 B]\n", "Get:8 http://archive.ubuntu.com/ubuntu jammy/main amd64 python3-olefile all 0.46-3 [33.8 kB]\n", "Get:9 http://archive.ubuntu.com/ubuntu jammy-updates/main amd64 python3-pil amd64 9.0.1-1ubuntu0.3 [419 kB]\n", "Get:10 http://archive.ubuntu.com/ubuntu jammy/universe amd64 tesseract-ocr-eng all 1:4.00~git30-7274cfa-1.1 [1,591 kB]\n", "Get:11 http://archive.ubuntu.com/ubuntu jammy/universe amd64 tesseract-ocr-osd all 1:4.00~git30-7274cfa-1.1 [2,990 kB]\n", "Get:12 http://archive.ubuntu.com/ubuntu jammy/universe amd64 tesseract-ocr amd64 4.1.1-2.1build1 [236 kB]\n", "Get:13 http://archive.ubuntu.com/ubuntu jammy/universe amd64 tesseract-ocr-script-latn all 1:4.00~git30-7274cfa-1.1 [30.9 MB]\n", "Fetched 40.0 MB in 2s (21.9 MB/s)\n", "debconf: unable to initialize frontend: Dialog\n", "debconf: (No usable dialog-like program is installed, so the dialog based frontend cannot be used. at /usr/share/perl5/Debconf/FrontEnd/Dialog.pm line 78, <> line 13.)\n", "debconf: falling back to frontend: Readline\n", "debconf: unable to initialize frontend: Readline\n", "debconf: (This frontend requires a controlling tty.)\n", "debconf: falling back to frontend: Teletype\n", "dpkg-preconfigure: unable to re-open stdin: \n", "Selecting previously unselected package libarchive-dev:amd64.\n", "(Reading database ... 121948 files and directories currently installed.)\n", "Preparing to unpack .../00-libarchive-dev_3.6.0-1ubuntu1_amd64.deb ...\n", "Unpacking libarchive-dev:amd64 (3.6.0-1ubuntu1) ...\n", "Selecting previously unselected package libimagequant0:amd64.\n", "Preparing to unpack .../01-libimagequant0_2.17.0-1_amd64.deb ...\n", "Unpacking libimagequant0:amd64 (2.17.0-1) ...\n", "Selecting previously unselected package libleptonica-dev.\n", "Preparing to unpack .../02-libleptonica-dev_1.82.0-3build1_amd64.deb ...\n", "Unpacking libleptonica-dev (1.82.0-3build1) ...\n", "Selecting previously unselected package libraqm0:amd64.\n", "Preparing to unpack .../03-libraqm0_0.7.0-4ubuntu1_amd64.deb ...\n", "Unpacking libraqm0:amd64 (0.7.0-4ubuntu1) ...\n", "Selecting previously unselected package libtesseract-dev:amd64.\n", "Preparing to unpack .../04-libtesseract-dev_4.1.1-2.1build1_amd64.deb ...\n", "Unpacking libtesseract-dev:amd64 (4.1.1-2.1build1) ...\n", "Selecting previously unselected package mailcap.\n", "Preparing to unpack .../05-mailcap_3.70+nmu1ubuntu1_all.deb ...\n", "Unpacking mailcap (3.70+nmu1ubuntu1) ...\n", "Selecting previously unselected package mime-support.\n", "Preparing to unpack .../06-mime-support_3.66_all.deb ...\n", "Unpacking mime-support (3.66) ...\n", "Selecting previously unselected package python3-olefile.\n", "Preparing to unpack .../07-python3-olefile_0.46-3_all.deb ...\n", "Unpacking python3-ole<PERSON>le (0.46-3) ...\n", "Selecting previously unselected package python3-pil:amd64.\n", "Preparing to unpack .../08-python3-pil_9.0.1-1ubuntu0.3_amd64.deb ...\n", "Unpacking python3-pil:amd64 (9.0.1-1ubuntu0.3) ...\n", "Selecting previously unselected package tesseract-ocr-eng.\n", "Preparing to unpack .../09-tesseract-ocr-eng_1%3a4.00~git30-7274cfa-1.1_all.deb ...\n", "Unpacking tesseract-ocr-eng (1:4.00~git30-7274cfa-1.1) ...\n", "Selecting previously unselected package tesseract-ocr-osd.\n", "Preparing to unpack .../10-tesseract-ocr-osd_1%3a4.00~git30-7274cfa-1.1_all.deb ...\n", "Unpacking tesseract-ocr-osd (1:4.00~git30-7274cfa-1.1) ...\n", "Selecting previously unselected package tesseract-ocr.\n", "Preparing to unpack .../11-tesseract-ocr_4.1.1-2.1build1_amd64.deb ...\n", "Unpacking tesseract-ocr (4.1.1-2.1build1) ...\n", "Selecting previously unselected package tesseract-ocr-script-latn.\n", "Preparing to unpack .../12-tesseract-ocr-script-latn_1%3a4.00~git30-7274cfa-1.1_all.deb ...\n", "Unpacking tesseract-ocr-script-latn (1:4.00~git30-7274cfa-1.1) ...\n", "Setting up tesseract-ocr-script-latn (1:4.00~git30-7274cfa-1.1) ...\n", "Setting up python3-olefile (0.46-3) ...\n", "Setting up tesseract-ocr-eng (1:4.00~git30-7274cfa-1.1) ...\n", "Setting up libleptonica-dev (1.82.0-3build1) ...\n", "Setting up libraqm0:amd64 (0.7.0-4ubuntu1) ...\n", "Setting up libimagequant0:amd64 (2.17.0-1) ...\n", "Setting up libarchive-dev:amd64 (3.6.0-1ubuntu1) ...\n", "Setting up tesseract-ocr-osd (1:4.00~git30-7274cfa-1.1) ...\n", "Setting up mailcap (3.70+nmu1ubuntu1) ...\n", "Setting up libtesseract-dev:amd64 (4.1.1-2.1build1) ...\n", "Setting up mime-support (3.66) ...\n", "Setting up python3-pil:amd64 (9.0.1-1ubuntu0.3) ...\n", "Setting up tesseract-ocr (4.1.1-2.1build1) ...\n", "Processing triggers for man-db (2.10.2-1) ...\n", "Processing triggers for libc-bin (2.35-0ubuntu3.4) ...\n", "/sbin/ldconfig.real: /usr/local/lib/libtbbbind.so.3 is not a symbolic link\n", "\n", "/sbin/ldconfig.real: /usr/local/lib/libtbbmalloc_proxy.so.2 is not a symbolic link\n", "\n", "/sbin/ldconfig.real: /usr/local/lib/libtbb.so.12 is not a symbolic link\n", "\n", "/sbin/ldconfig.real: /usr/local/lib/libtbbmalloc.so.2 is not a symbolic link\n", "\n", "/sbin/ldconfig.real: /usr/local/lib/libtbbbind_2_0.so.3 is not a symbolic link\n", "\n", "/sbin/ldconfig.real: /usr/local/lib/libtbbbind_2_5.so.3 is not a symbolic link\n", "\n"]}]}, {"cell_type": "code", "source": ["!pip install unstructured-pytesseract\n", "!pip install tesseract-ocr"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TMP99Q_y7XWl", "outputId": "38690471-e581-4be4-d99c-9e5f0d07f120"}, "execution_count": 4, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: unstructured-pytesseract in /usr/local/lib/python3.10/dist-packages (0.3.12)\n", "Requirement already satisfied: packaging>=21.3 in /usr/local/lib/python3.10/dist-packages (from unstructured-pytesseract) (23.2)\n", "Requirement already satisfied: Pillow>=8.0.0 in /usr/local/lib/python3.10/dist-packages (from unstructured-pytesseract) (10.3.0)\n", "Collecting tesseract-ocr\n", "  Downloading tesseract-ocr-0.0.1.tar.gz (33 kB)\n", "  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: cython in /usr/local/lib/python3.10/dist-packages (from tesseract-ocr) (3.0.10)\n", "Building wheels for collected packages: tesseract-ocr\n", "  Building wheel for tesseract-ocr (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for tesseract-ocr: filename=tesseract_ocr-0.0.1-cp310-cp310-linux_x86_64.whl size=169756 sha256=34897be8ebc098165522b3bf91977750745c010f07b549bdb9781036df6c27cf\n", "  Stored in directory: /root/.cache/pip/wheels/bb/fd/f3/5c231ecbbb80a1fe33204ff3021d99b54ef6daf6f8099311b8\n", "Successfully built tesseract-ocr\n", "Installing collected packages: tesseract-ocr\n", "Successfully installed tesseract-ocr-0.0.1\n"]}]}, {"cell_type": "code", "source": ["!pip install \"unstructured[pptx]\""], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RruMFEmtMhVw", "outputId": "12220b5c-ef1f-451d-997d-9283aa4cbb84"}, "execution_count": 5, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: unstructured[pptx] in /usr/local/lib/python3.10/dist-packages (0.13.7)\n", "Requirement already satisfied: chardet in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (5.2.0)\n", "Requirement already satisfied: filetype in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (1.2.0)\n", "Requirement already satisfied: python-magic in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (0.4.27)\n", "Requirement already satisfied: lxml in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (4.9.4)\n", "Requirement already satisfied: nltk in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (3.8.1)\n", "Requirement already satisfied: tabulate in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (0.9.0)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (2.31.0)\n", "Requirement already satisfied: beautifulsoup4 in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (4.12.3)\n", "Requirement already satisfied: emoji in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (2.11.1)\n", "Requirement already satisfied: dataclasses-json in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (0.6.6)\n", "Requirement already satisfied: python-iso639 in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (2024.4.27)\n", "Requirement already satisfied: langdetect in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (1.0.9)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (1.25.2)\n", "Requirement already satisfied: rapidfuzz in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (3.9.0)\n", "Requirement already satisfied: backoff in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (2.2.1)\n", "Requirement already satisfied: typing-extensions in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (4.11.0)\n", "Requirement already satisfied: unstructured-client in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (0.22.0)\n", "Requirement already satisfied: wrapt in /usr/local/lib/python3.10/dist-packages (from unstructured[pptx]) (1.14.1)\n", "Collecting python-pptx<=0.6.23 (from unstructured[pptx])\n", "  Downloading python_pptx-0.6.23-py3-none-any.whl (471 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m471.6/471.6 kB\u001b[0m \u001b[31m9.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: Pillow>=3.3.2 in /usr/local/lib/python3.10/dist-packages (from python-pptx<=0.6.23->unstructured[pptx]) (10.3.0)\n", "Collecting XlsxWriter>=0.5.7 (from python-pptx<=0.6.23->unstructured[pptx])\n", "  Downloading XlsxWriter-3.2.0-py3-none-any.whl (159 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m159.9/159.9 kB\u001b[0m \u001b[31m17.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.10/dist-packages (from beautifulsoup4->unstructured[pptx]) (2.5)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->unstructured[pptx]) (3.21.2)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->unstructured[pptx]) (0.9.0)\n", "Requirement already satisfied: six in /usr/local/lib/python3.10/dist-packages (from langdetect->unstructured[pptx]) (1.16.0)\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured[pptx]) (8.1.7)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured[pptx]) (1.4.2)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured[pptx]) (2023.12.25)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured[pptx]) (4.66.4)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured[pptx]) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured[pptx]) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured[pptx]) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured[pptx]) (2024.2.2)\n", "Requirement already satisfied: deepdiff>=6.0 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured[pptx]) (7.0.1)\n", "Requirement already satisfied: jsonpath-python>=1.0.6 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured[pptx]) (1.0.6)\n", "Requirement already satisfied: mypy-extensions>=1.0.0 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured[pptx]) (1.0.0)\n", "Requirement already satisfied: packaging>=23.1 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured[pptx]) (23.2)\n", "Requirement already satisfied: pypdf>=4.0 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured[pptx]) (4.2.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured[pptx]) (2.8.2)\n", "Requirement already satisfied: ordered-set<4.2.0,>=4.1.0 in /usr/local/lib/python3.10/dist-packages (from deepdiff>=6.0->unstructured-client->unstructured[pptx]) (4.1.0)\n", "Installing collected packages: XlsxWriter, python-pptx\n", "Successfully installed XlsxWriter-3.2.0 python-pptx-0.6.23\n"]}]}, {"cell_type": "code", "source": ["!pip install langchain_astradb"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DR8YmEFX_bXo", "outputId": "6fbcfd3f-9d50-44b7-c210-7b2bc74abb06"}, "execution_count": 6, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain_astradb\n", "  Downloading langchain_astradb-0.3.0-py3-none-any.whl (26 kB)\n", "Collecting astrapy<2,>=1 (from langchain_astradb)\n", "  Downloading astrapy-1.1.0-py3-none-any.whl (124 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m124.4/124.4 kB\u001b[0m \u001b[31m2.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: langchain-core<0.2.0,>=0.1.31 in /usr/local/lib/python3.10/dist-packages (from langchain_astradb) (0.1.52)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain_astradb) (1.25.2)\n", "Collecting bson<0.6.0,>=0.5.10 (from astrapy<2,>=1->langchain_astradb)\n", "  Downloading bson-0.5.10.tar.gz (10 kB)\n", "  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Collecting cassio<0.2.0,>=0.1.4 (from astrapy<2,>=1->langchain_astradb)\n", "  Downloading cassio-0.1.7-py3-none-any.whl (44 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m44.9/44.9 kB\u001b[0m \u001b[31m5.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting deprecation<2.2.0,>=2.1.0 (from astrapy<2,>=1->langchain_astradb)\n", "  Downloading deprecation-2.1.0-py2.py3-none-any.whl (11 kB)\n", "Collecting httpx[http2]<1,>=0.25.2 (from astrapy<2,>=1->langchain_astradb)\n", "  Downloading httpx-0.27.0-py3-none-any.whl (75 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m9.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: toml<0.11.0,>=0.10.2 in /usr/local/lib/python3.10/dist-packages (from astrapy<2,>=1->langchain_astradb) (0.10.2)\n", "Collecting uuid6<2024.2.0,>=2024.1.12 (from astrapy<2,>=1->langchain_astradb)\n", "  Downloading uuid6-2024.1.12-py3-none-any.whl (6.4 kB)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.31->langchain_astradb) (6.0.1)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.31->langchain_astradb) (1.33)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.31->langchain_astradb) (0.1.57)\n", "Requirement already satisfied: packaging<24.0,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.31->langchain_astradb) (23.2)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.31->langchain_astradb) (2.7.1)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.31->langchain_astradb) (8.3.0)\n", "Requirement already satisfied: python-dateutil>=2.4.0 in /usr/local/lib/python3.10/dist-packages (from bson<0.6.0,>=0.5.10->astrapy<2,>=1->langchain_astradb) (2.8.2)\n", "Requirement already satisfied: six>=1.9.0 in /usr/local/lib/python3.10/dist-packages (from bson<0.6.0,>=0.5.10->astrapy<2,>=1->langchain_astradb) (1.16.0)\n", "Collecting cassandra-driver<4.0.0,>=3.28.0 (from cassio<0.2.0,>=0.1.4->astrapy<2,>=1->langchain_astradb)\n", "  Downloading cassandra_driver-3.29.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (18.9 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m18.9/18.9 MB\u001b[0m \u001b[31m56.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: requests<3.0.0,>=2.31.0 in /usr/local/lib/python3.10/dist-packages (from cassio<0.2.0,>=0.1.4->astrapy<2,>=1->langchain_astradb) (2.31.0)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain_astradb) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain_astradb) (2024.2.2)\n", "Collecting httpcore==1.* (from httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain_astradb)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m10.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain_astradb) (3.7)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain_astradb) (1.3.1)\n", "Collecting h2<5,>=3 (from httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain_astradb)\n", "  Downloading h2-4.1.0-py3-none-any.whl (57 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m57.5/57.5 kB\u001b[0m \u001b[31m7.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting h11<0.15,>=0.13 (from httpcore==1.*->httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain_astradb)\n", "  Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m7.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.2.0,>=0.1.31->langchain_astradb) (2.4)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.0->langchain-core<0.2.0,>=0.1.31->langchain_astradb) (3.10.3)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain-core<0.2.0,>=0.1.31->langchain_astradb) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.2 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain-core<0.2.0,>=0.1.31->langchain_astradb) (2.18.2)\n", "Requirement already satisfied: typing-extensions>=4.6.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain-core<0.2.0,>=0.1.31->langchain_astradb) (4.11.0)\n", "Collecting geomet<0.3,>=0.1 (from cassandra-driver<4.0.0,>=3.28.0->cassio<0.2.0,>=0.1.4->astrapy<2,>=1->langchain_astradb)\n", "  Downloading geomet-0.2.1.post1-py3-none-any.whl (18 kB)\n", "Collecting hyperframe<7,>=6.0 (from h2<5,>=3->httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain_astradb)\n", "  Downloading hyperframe-6.0.1-py3-none-any.whl (12 kB)\n", "Collecting hpack<5,>=4.0 (from h2<5,>=3->httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain_astradb)\n", "  Downloading hpack-4.0.0-py3-none-any.whl (32 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.31.0->cassio<0.2.0,>=0.1.4->astrapy<2,>=1->langchain_astradb) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.31.0->cassio<0.2.0,>=0.1.4->astrapy<2,>=1->langchain_astradb) (2.0.7)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx[http2]<1,>=0.25.2->astrapy<2,>=1->langchain_astradb) (1.2.1)\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from geomet<0.3,>=0.1->cassandra-driver<4.0.0,>=3.28.0->cassio<0.2.0,>=0.1.4->astrapy<2,>=1->langchain_astradb) (8.1.7)\n", "Building wheels for collected packages: bson\n", "  Building wheel for bson (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for bson: filename=bson-0.5.10-py3-none-any.whl size=11976 sha256=c94e1e293db9f31890191752c13da886d61da3ae13ae67b22a482ab0fde01661\n", "  Stored in directory: /root/.cache/pip/wheels/36/49/3b/8b33954dfae7a176009c4d721a45af56c8a9c1cdc3ee947945\n", "Successfully built bson\n", "Installing collected packages: uuid6, hyperframe, hpack, h11, geomet, deprecation, httpcore, h2, cassandra-driver, bson, httpx, cassio, astrapy, langchain_astradb\n", "Successfully installed astrapy-1.1.0 bson-0.5.10 cassandra-driver-3.29.1 cassio-0.1.7 deprecation-2.1.0 geomet-0.2.1.post1 h11-0.14.0 h2-4.1.0 hpack-4.0.0 httpcore-1.0.5 httpx-0.27.0 hyperframe-6.0.1 langchain_astradb-0.3.0 uuid6-2024.1.12\n"]}]}, {"cell_type": "code", "source": ["!pip install langchain langchain-openai datasets pypdf"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4rN3g_sxPLjN", "outputId": "93096ee0-bcca-4233-a170-ee4a68ad727e"}, "execution_count": 7, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain\n", "  Downloading langchain-0.1.20-py3-none-any.whl (1.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m11.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-openai\n", "  Downloading langchain_openai-0.1.6-py3-none-any.whl (34 kB)\n", "Collecting datasets\n", "  Downloading datasets-2.19.1-py3-none-any.whl (542 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m542.0/542.0 kB\u001b[0m \u001b[31m46.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pypdf in /usr/local/lib/python3.10/dist-packages (4.2.0)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.0.30)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.9.5)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (4.0.3)\n", "Requirement already satisfied: dataclasses-json<0.7,>=0.5.7 in /usr/local/lib/python3.10/dist-packages (from langchain) (0.6.6)\n", "Requirement already satisfied: langchain-community<0.1,>=0.0.38 in /usr/local/lib/python3.10/dist-packages (from langchain) (0.0.38)\n", "Requirement already satisfied: langchain-core<0.2.0,>=0.1.52 in /usr/local/lib/python3.10/dist-packages (from langchain) (0.1.52)\n", "Collecting langchain-text-splitters<0.1,>=0.0.1 (from langchain)\n", "  Downloading langchain_text_splitters-0.0.1-py3-none-any.whl (21 kB)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.17 in /usr/local/lib/python3.10/dist-packages (from langchain) (0.1.57)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.25.2)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.7.1)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (8.3.0)\n", "Collecting openai<2.0.0,>=1.24.0 (from langchain-openai)\n", "  Downloading openai-1.29.0-py3-none-any.whl (320 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m320.3/320.3 kB\u001b[0m \u001b[31m33.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting tiktoken<1,>=0.5.2 (from langchain-openai)\n", "  Downloading tiktoken-0.7.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.1 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m54.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from datasets) (3.14.0)\n", "Requirement already satisfied: pyarrow>=12.0.0 in /usr/local/lib/python3.10/dist-packages (from datasets) (14.0.2)\n", "Requirement already satisfied: pyarrow-hotfix in /usr/local/lib/python3.10/dist-packages (from datasets) (0.6)\n", "Collecting dill<0.3.9,>=0.3.0 (from datasets)\n", "  Downloading dill-0.3.8-py3-none-any.whl (116 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m116.3/116.3 kB\u001b[0m \u001b[31m12.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from datasets) (2.0.3)\n", "Requirement already satisfied: tqdm>=4.62.1 in /usr/local/lib/python3.10/dist-packages (from datasets) (4.66.4)\n", "Collecting xxhash (from datasets)\n", "  Downloading xxhash-3.4.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (194 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m194.1/194.1 kB\u001b[0m \u001b[31m22.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting multiprocess (from datasets)\n", "  Downloading multiprocess-0.70.16-py310-none-any.whl (134 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m134.8/134.8 kB\u001b[0m \u001b[31m17.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: fsspec[http]<=2024.3.1,>=2023.1.0 in /usr/local/lib/python3.10/dist-packages (from datasets) (2023.6.0)\n", "Collecting huggingface-hub>=0.21.2 (from datasets)\n", "  Downloading huggingface_hub-0.23.0-py3-none-any.whl (401 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m401.2/401.2 kB\u001b[0m \u001b[31m27.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: packaging in /usr/local/lib/python3.10/dist-packages (from datasets) (23.2)\n", "Requirement already satisfied: typing_extensions>=4.0 in /usr/local/lib/python3.10/dist-packages (from pypdf) (4.11.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.9.4)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json<0.7,>=0.5.7->langchain) (3.21.2)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json<0.7,>=0.5.7->langchain) (0.9.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.52->langchain) (1.33)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.17->langchain) (3.10.3)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.24.0->langchain-openai) (3.7.1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai<2.0.0,>=1.24.0->langchain-openai) (1.7.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.24.0->langchain-openai) (0.27.0)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.24.0->langchain-openai) (1.3.1)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.2 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (2.18.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2024.2.2)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.0.3)\n", "Requirement already satisfied: regex>=2022.1.18 in /usr/local/lib/python3.10/dist-packages (from tiktoken<1,>=0.5.2->langchain-openai) (2023.12.25)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from pandas->datasets) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->datasets) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas->datasets) (2024.1)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.24.0->langchain-openai) (1.2.1)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.24.0->langchain-openai) (1.0.5)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai<2.0.0,>=1.24.0->langchain-openai) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.2.0,>=0.1.52->langchain) (2.4)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.8.2->pandas->datasets) (1.16.0)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain) (1.0.0)\n", "Installing collected packages: xxhash, dill, tiktoken, multiprocess, huggingface-hub, openai, datasets, langchain-text-splitters, langchain-openai, langchain\n", "  Attempting uninstall: huggingface-hub\n", "    Found existing installation: huggingface-hub 0.20.3\n", "    Uninstalling huggingface-hub-0.20.3:\n", "      Successfully uninstalled huggingface-hub-0.20.3\n", "Successfully installed datasets-2.19.1 dill-0.3.8 huggingface-hub-0.23.0 langchain-0.1.20 langchain-openai-0.1.6 langchain-text-splitters-0.0.1 multiprocess-0.70.16 openai-1.29.0 tiktoken-0.7.0 xxhash-3.4.1\n"]}]}, {"cell_type": "code", "source": ["from langchain_text_splitters import RecursiveCharacterTextSplitter"], "metadata": {"id": "AjAFSJYlDpkA"}, "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": ["from langchain_community.document_loaders import DirectoryLoader\n"], "metadata": {"id": "nBVPhAdPDNE3"}, "execution_count": 9, "outputs": []}, {"cell_type": "code", "source": ["loader = DirectoryLoader('/content/docs')"], "metadata": {"id": "GYA9S1oaU1g3"}, "execution_count": 10, "outputs": []}, {"cell_type": "code", "source": ["splitter = RecursiveCharacterTextSplitter(chunk_size=512, chunk_overlap=64)"], "metadata": {"id": "icOls_EgDQy_"}, "execution_count": 11, "outputs": []}, {"cell_type": "code", "source": ["docs = loader.load_and_split(text_splitter=splitter)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 304, "referenced_widgets": ["8e035199e06b40eabbe34b3852c53034", "75777df2725d4509adaddc144ee52baa", "61c802dc2e7a486e91ed26b43a579a65", "9123e7f5130a4f498130e117726e8430", "2ed0f1ed939949518905dbcd850f9ee8", "73d7a2d3325a469c89c275c0d6912551", "7cf8bdbebd52448bb8444099cfb70886", "0fb6312ab0b94e92a8989059794038ce", "bcc6d619f2ce49b6bb2ad45099d229a2", "80b344425b2e46868c6988d0b6bf0a60", "f12e15d72d634c8ba643a468f4d76735", "4e840cdb44a44a99b851cbe1673db6b5", "4f02de36c68c4a12978129ce6856a104", "c2f04856ddcb4fd1bbc1ead4274ce0aa", "bc28ccf6311547daaa88e14861fc653d", "8e12ba7b025e42b09bff0115dd840e49", "b4207375ca9442d9b88cbaa5810f5041", "51fb8e49361e48479028d3112a4bcd90", "6216f9fd90b44c97bc251ad1b554047d", "64e9f9d5e7504dbea334234d4788089d", "0b912a2a673f4daea2da687bb94547c6", "e4d2f8a121c54c49b681a767ac1fc3b1", "8adde71b0962495c80261b2dd1d4abf3", "9596bb6c2fa149b4945ab2d10e207e84", "bcd1278264fa44518f09164105271b22", "93edc57d3b134be88f4b5d0fdf12ebbc", "88c95ef5ee33412c8141ffc7c11c702e", "af1fea75b14f4b0a936513c4f3074fbc", "3f9760917bcf4e249f16f34a2361b73c", "037f7836ab6f465caf2b87dc5b7aef63", "829923a46f24479ea648945c677d9e3a", "db4ea8e3882c493cb980e9dfd8151a84", "a67ed49aae1544e3b5a9b141d1c5dd3e", "9d9f277060934802932d690307fc9685", "1a3c537f212645fda454ffa103aac256", "1646ce29bbb5425a9262a009f7fa2a13", "3b8846ae905f4b7683e4f5e422e21f75", "5b9853b590fe415fb559ae396a7bc3c7", "f63494b47dbe412cb82f29a350cbbbc2", "12d2ab6a477e4b94a83dae2651c6fb4b", "d7e400593ed24394a24fb07c069b83c9", "bc2a5e16203d4c83a976cd85e9622467", "5b38a4d2ed5b4078b13b8397d6439ae8", "c38f90a27964497db1c6f500510b4c03"]}, "id": "gXfYNkYx5Lx7", "outputId": "7097f252-1c7e-45e2-bf13-044263056b27"}, "execution_count": 12, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["[nltk_data] Downloading package punkt to /root/nltk_data...\n", "[nltk_data]   Unzipping tokenizers/punkt.zip.\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /root/nltk_data...\n", "[nltk_data]   Unzipping taggers/averaged_perceptron_tagger.zip.\n"]}, {"output_type": "display_data", "data": {"text/plain": ["yolox_l0.05.onnx:   0%|          | 0.00/217M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "8e035199e06b40eabbe34b3852c53034"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/1.47k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "4e840cdb44a44a99b851cbe1673db6b5"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors:   0%|          | 0.00/115M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "8adde71b0962495c80261b2dd1d4abf3"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors:   0%|          | 0.00/46.8M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "9d9f277060934802932d690307fc9685"}}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["Some weights of the model checkpoint at microsoft/table-transformer-structure-recognition were not used when initializing TableTransformerForObjectDetection: ['model.backbone.conv_encoder.model.layer2.0.downsample.1.num_batches_tracked', 'model.backbone.conv_encoder.model.layer3.0.downsample.1.num_batches_tracked', 'model.backbone.conv_encoder.model.layer4.0.downsample.1.num_batches_tracked']\n", "- This IS expected if you are initializing TableTransformerForObjectDetection from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).\n", "- This IS NOT expected if you are initializing TableTransformerForObjectDetection from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).\n"]}]}, {"cell_type": "code", "source": ["len(docs)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "uaBLlukoN1in", "outputId": "91fbb728-e2b2-4eb1-e6e1-25531fcb53a9"}, "execution_count": 13, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["233"]}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "code", "source": ["import os\n", "from langchain_core.documents import Document\n", "from langchain_community.document_loaders import PyPDFLoader\n", "\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_openai import ChatOpenAI, OpenAIEmbeddings\n", "\n"], "metadata": {"id": "irbe3D7R_J_n"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["import os\n", "from google.colab import userdata\n", "OPENAI_API_KEY=userdata.get('OPENAI_API_KEY')\n", "os.environ[\"OPENAI_API_KEY\"] = OPENAI_API_KEY"], "metadata": {"id": "YoyE7fpl_pDB"}, "execution_count": 14, "outputs": []}, {"cell_type": "code", "source": ["embedding = OpenAIEmbeddings()"], "metadata": {"id": "mVnI4Sc5_pxr"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": ["from langchain_astradb import AstraDBVectorStore\n", "from langchain.indexes import VectorstoreIndexCreator"], "metadata": {"id": "8TCV0FA2YwxY"}, "execution_count": 18, "outputs": []}, {"cell_type": "code", "source": ["ASTRA_DB_API_ENDPOINT=\"https://79b63042-b3d1-4163-b10a-75c9979ebf59-us-east-2.apps.astra.datastax.com\"\n", "ASTRA_DB_APPLICATION_TOKEN=\"AstraCS:RyuexWdwLrGymMZnubGtbuZq:b7e36eae7d7f021e542f9f8b541a4ccdd7a5705e077b18887579f56bb0955ad4\"\n", "ASTRA_DB_KEYSPACE=\"default_keyspace\""], "metadata": {"id": "KLWvXEYS_WGA"}, "execution_count": 19, "outputs": []}, {"cell_type": "code", "source": ["vstore = AstraDBVectorStore(\n", "    embedding=embedding,\n", "    collection_name=\"multidoc_vector\",\n", "    api_endpoint=ASTRA_DB_API_ENDPOINT,\n", "    token=ASTRA_DB_APPLICATION_TOKEN,\n", "    namespace=ASTRA_DB_KEYSPACE,\n", ")"], "metadata": {"id": "qwf9jP-mFsho"}, "execution_count": 20, "outputs": []}, {"cell_type": "code", "source": ["inserted_ids = vstore.add_documents(docs)"], "metadata": {"id": "PB0OTyiPZYtj"}, "execution_count": 21, "outputs": []}, {"cell_type": "code", "source": ["print(f\"\\nInserted {len(inserted_ids)} documents.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MCZF7rhmOEBQ", "outputId": "3f41cd26-3df0-4d85-859e-a1815abaf89e"}, "execution_count": 22, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Inserted 233 documents.\n"]}]}, {"cell_type": "code", "source": ["prompt_template = \"\"\"\n", "You are an AI philosopher drawing insights from the roadmap of \"rag,\" \"llama3,\" and \"genai.\"\n", "Craft thoughtful answers based on this roadmap, mixing and matching existing paths.\n", "Your responses should be concise and strictly related to the provided context.\n", "\n", "ROADMAP CONTEXT:\n", "{context}\n", "\n", "QUESTION: {question}\n", "\n", "YOUR ANSWER:\"\"\""], "metadata": {"id": "U8IkQRVzF9pP"}, "execution_count": 25, "outputs": []}, {"cell_type": "code", "source": ["prompt_template = ChatPromptTemplate.from_template(prompt_template)"], "metadata": {"id": "DQp4n2tCG-F_"}, "execution_count": 26, "outputs": []}, {"cell_type": "code", "source": ["retriever = vstore.as_retriever(search_kwargs={\"k\": 3})"], "metadata": {"id": "HLTlpaHDGg6n"}, "execution_count": 27, "outputs": []}, {"cell_type": "code", "source": ["retriever"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QdvsgC2UG2F4", "outputId": "82af6575-982b-4b13-efe5-c35b6e23d109"}, "execution_count": 28, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["VectorStoreRetriever(tags=['AstraDBVectorStore', 'OpenAIEmbeddings'], vectorstore=<langchain_astradb.vectorstores.AstraDBVectorStore object at 0x7fefaeb23d90>, search_kwargs={'k': 3})"]}, "metadata": {}, "execution_count": 28}]}, {"cell_type": "code", "source": ["llm = ChatOpenAI()"], "metadata": {"id": "jp8EyMrWGxUx"}, "execution_count": 29, "outputs": []}, {"cell_type": "code", "source": ["chain = (\n", "    {\"context\": retriever, \"question\": RunnablePassthrough()}\n", "    | prompt_template\n", "    | llm\n", "    | StrOutputParser()\n", ")"], "metadata": {"id": "7HITJ2t3GtNf"}, "execution_count": 30, "outputs": []}, {"cell_type": "code", "source": ["chain.invoke(\"can you tell me the roadmap of generative ai?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 87}, "id": "uYnIVzpTcauK", "outputId": "fdf2ab30-f628-4b8b-d02e-5c8140c8d701"}, "execution_count": 31, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'The roadmap of Generative AI includes steps for beginners to advanced learners such as prerequisites, fundamentals, core generative models, developing applications with LLMs, projects, practical experience, and miscellaneous topics. It emphasizes continuous learning, setting specific goals, consistent learning, implementation, experimentation, feedback, and the importance of a background in machine learning and deep learning. Mathematics knowledge is also essential for Generative AI.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 31}]}, {"cell_type": "code", "source": ["chain.invoke(\"what is a llama can you tell me some important point on top of it.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 87}, "id": "jVag171QaHi2", "outputId": "b99cd96e-c72d-4d74-9f71-c1801cbd76ba"}, "execution_count": 32, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Llama (Large Language Model Meta AI) is a family of autoregressive large language models released by Meta AI. The latest version is Llama 3, which was released in April 2024. Llama models have shown superior performance compared to other large language models, such as GPT-3 and PaLM. These models range in parameter sizes from 7B to 70B and have been made accessible for both academic and commercial use.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 32}]}, {"cell_type": "code", "source": [], "metadata": {"id": "M0NfhTCIaRMF"}, "execution_count": null, "outputs": []}]}