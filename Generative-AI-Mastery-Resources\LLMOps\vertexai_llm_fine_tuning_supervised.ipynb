{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["https://codelabs.developers.google.com/llm-finetuning-supervised#4"], "metadata": {"id": "FxoyWn1TUi6h"}}, {"cell_type": "markdown", "source": ["# Model Tuning with Vertex AI Foundation Model"], "metadata": {"id": "80XPRJ8v5lgL"}}, {"cell_type": "markdown", "source": ["# Objective"], "metadata": {"id": "MpxwbaWD8Vyn"}}, {"cell_type": "markdown", "source": ["This lab teaches you how to tune a foundational model on new unseen data and you will use the following Google Cloud products:\n", "*   Vertex AI Pipelines\n", "*   Vertex AI Evaluation Services\n", "*   Vertex AI Model Registry\n", "*   Vertex AI Endpoints"], "metadata": {"id": "TwFJ03nC712l"}}, {"cell_type": "markdown", "source": ["# Use Case"], "metadata": {"id": "r2FysBTQ8Y0G"}}, {"cell_type": "markdown", "source": ["Using Generative AI we will generate a suitable TITLE for a news BODY from BBC FULLTEXT DATA (Sourced from BigQuery Public Dataset *bigquery-public-data.bbc_news.fulltext*). We will fine tune text-bison@002 to a new fine-tuned model called \"bbc-news-summary-tuned\" and compare the result with the response from the base model."], "metadata": {"id": "x-UZNmmw8a3K"}}, {"cell_type": "markdown", "source": ["# Install and Import Dependencies"], "metadata": {"id": "-B6XiWwF1Ja9"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "k5l7--obsUKv", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "23e98c73-d8f2-46b3-fab5-f1fd2ca08beb"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: google-cloud-aiplatform in /usr/local/lib/python3.10/dist-packages (1.48.0)\n", "Requirement already satisfied: google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (2.11.1)\n", "Requirement already satisfied: google-auth<3.0.0dev,>=2.14.1 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (2.27.0)\n", "Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (1.23.0)\n", "Requirement already satisfied: protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (3.20.3)\n", "Requirement already satisfied: packaging>=14.3 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (24.0)\n", "Requirement already satisfied: google-cloud-storage<3.0.0dev,>=1.32.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (2.8.0)\n", "Requirement already satisfied: google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (3.21.0)\n", "Requirement already satisfied: google-cloud-resource-manager<3.0.0dev,>=1.3.3 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (1.12.3)\n", "Requirement already satisfied: shapely<3.0.0dev in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (2.0.4)\n", "Requirement already satisfied: pydantic<3 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (2.7.1)\n", "Requirement already satisfied: docstring-parser<1 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (0.16)\n", "Requirement already satisfied: googleapis-common-protos<2.0.dev0,>=1.56.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (1.63.0)\n", "Requirement already satisfied: requests<3.0.0.dev0,>=2.18.0 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (2.31.0)\n", "Requirement already satisfied: grpcio<2.0dev,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (1.63.0)\n", "Requirement already satisfied: grpcio-status<2.0.dev0,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (1.48.2)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0.0dev,>=2.14.1->google-cloud-aiplatform) (5.3.3)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0.0dev,>=2.14.1->google-cloud-aiplatform) (0.4.0)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0.0dev,>=2.14.1->google-cloud-aiplatform) (4.9)\n", "Requirement already satisfied: google-cloud-core<3.0.0dev,>=1.6.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform) (2.3.3)\n", "Requirement already satisfied: google-resumable-media<3.0dev,>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform) (2.7.0)\n", "Requirement already satisfied: python-dateutil<3.0dev,>=2.7.2 in /usr/local/lib/python3.10/dist-packages (from google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform) (2.8.2)\n", "Requirement already satisfied: grpc-google-iam-v1<1.0.0dev,>=0.12.4 in /usr/local/lib/python3.10/dist-packages (from google-cloud-resource-manager<3.0.0dev,>=1.3.3->google-cloud-aiplatform) (0.13.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3->google-cloud-aiplatform) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.2 in /usr/local/lib/python3.10/dist-packages (from pydantic<3->google-cloud-aiplatform) (2.18.2)\n", "Requirement already satisfied: typing-extensions>=4.6.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3->google-cloud-aiplatform) (4.11.0)\n", "Requirement already satisfied: numpy<3,>=1.14 in /usr/local/lib/python3.10/dist-packages (from shapely<3.0.0dev->google-cloud-aiplatform) (1.25.2)\n", "Requirement already satisfied: google-crc32c<2.0dev,>=1.0 in /usr/local/lib/python3.10/dist-packages (from google-resumable-media<3.0dev,>=0.6.0->google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform) (1.5.0)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth<3.0.0dev,>=2.14.1->google-cloud-aiplatform) (0.6.0)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil<3.0dev,>=2.7.2->google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform) (1.16.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (2024.2.2)\n", "Collecting datasets\n", "  Downloading datasets-2.19.1-py3-none-any.whl (542 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m542.0/542.0 kB\u001b[0m \u001b[31m7.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from datasets) (3.14.0)\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.10/dist-packages (from datasets) (1.25.2)\n", "Requirement already satisfied: pyarrow>=12.0.0 in /usr/local/lib/python3.10/dist-packages (from datasets) (14.0.2)\n", "Requirement already satisfied: pyarrow-hotfix in /usr/local/lib/python3.10/dist-packages (from datasets) (0.6)\n", "Collecting dill<0.3.9,>=0.3.0 (from datasets)\n", "  Downloading dill-0.3.8-py3-none-any.whl (116 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m116.3/116.3 kB\u001b[0m \u001b[31m14.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from datasets) (2.0.3)\n", "Requirement already satisfied: requests>=2.19.0 in /usr/local/lib/python3.10/dist-packages (from datasets) (2.31.0)\n", "Requirement already satisfied: tqdm>=4.62.1 in /usr/local/lib/python3.10/dist-packages (from datasets) (4.66.4)\n", "Collecting xxhash (from datasets)\n", "  Downloading xxhash-3.4.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (194 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m194.1/194.1 kB\u001b[0m \u001b[31m21.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting multiprocess (from datasets)\n", "  Downloading multiprocess-0.70.16-py310-none-any.whl (134 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m134.8/134.8 kB\u001b[0m \u001b[31m16.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: fsspec[http]<=2024.3.1,>=2023.1.0 in /usr/local/lib/python3.10/dist-packages (from datasets) (2023.6.0)\n", "Requirement already satisfied: aiohttp in /usr/local/lib/python3.10/dist-packages (from datasets) (3.9.5)\n", "Collecting huggingface-hub>=0.21.2 (from datasets)\n", "  Downloading huggingface_hub-0.23.0-py3-none-any.whl (401 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m401.2/401.2 kB\u001b[0m \u001b[31m36.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: packaging in /usr/local/lib/python3.10/dist-packages (from datasets) (24.0)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from datasets) (6.0.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (4.0.3)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.21.2->datasets) (4.11.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.19.0->datasets) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests>=2.19.0->datasets) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.19.0->datasets) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests>=2.19.0->datasets) (2024.2.2)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from pandas->datasets) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->datasets) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas->datasets) (2024.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.8.2->pandas->datasets) (1.16.0)\n", "Installing collected packages: xxhash, dill, multiprocess, huggingface-hub, datasets\n", "\u001b[33m  WARNING: The script huggingface-cli is installed in '/root/.local/bin' which is not on PATH.\n", "  Consider adding this directory to PATH or, if you prefer to suppress this warning, use --no-warn-script-location.\u001b[0m\u001b[33m\n", "\u001b[0m\u001b[33m  WARNING: The script datasets-cli is installed in '/root/.local/bin' which is not on PATH.\n", "  Consider adding this directory to PATH or, if you prefer to suppress this warning, use --no-warn-script-location.\u001b[0m\u001b[33m\n", "\u001b[0mSuccessfully installed datasets-2.19.1 dill-0.3.8 huggingface-hub-0.23.0 multiprocess-0.70.16 xxhash-3.4.1\n", "Collecting google-cloud-pipeline-components\n", "  Downloading google_cloud_pipeline_components-2.14.0-py3-none-any.whl (1.4 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.4/1.4 MB\u001b[0m \u001b[31m9.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5 in /usr/local/lib/python3.10/dist-packages (from google-cloud-pipeline-components) (2.11.1)\n", "Collecting kfp<=2.7.0,>=2.6.0 (from google-cloud-pipeline-components)\n", "  Downloading kfp-2.7.0.tar.gz (441 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m441.8/441.8 kB\u001b[0m \u001b[31m12.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: google-cloud-aiplatform<2,>=1.14.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-pipeline-components) (1.48.0)\n", "Requirement already satisfied: Jinja2<4,>=3.1.2 in /usr/local/lib/python3.10/dist-packages (from google-cloud-pipeline-components) (3.1.4)\n", "Requirement already satisfied: googleapis-common-protos<2.0.dev0,>=1.56.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5->google-cloud-pipeline-components) (1.63.0)\n", "Requirement already satisfied: protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0.dev0,>=3.19.5 in /usr/local/lib/python3.10/dist-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5->google-cloud-pipeline-components) (3.20.3)\n", "Requirement already satisfied: google-auth<3.0.dev0,>=2.14.1 in /usr/local/lib/python3.10/dist-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5->google-cloud-pipeline-components) (2.27.0)\n", "Requirement already satisfied: requests<3.0.0.dev0,>=2.18.0 in /usr/local/lib/python3.10/dist-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5->google-cloud-pipeline-components) (2.31.0)\n", "Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform<2,>=1.14.0->google-cloud-pipeline-components) (1.23.0)\n", "Requirement already satisfied: packaging>=14.3 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform<2,>=1.14.0->google-cloud-pipeline-components) (24.0)\n", "Requirement already satisfied: google-cloud-storage<3.0.0dev,>=1.32.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform<2,>=1.14.0->google-cloud-pipeline-components) (2.8.0)\n", "Requirement already satisfied: google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform<2,>=1.14.0->google-cloud-pipeline-components) (3.21.0)\n", "Requirement already satisfied: google-cloud-resource-manager<3.0.0dev,>=1.3.3 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform<2,>=1.14.0->google-cloud-pipeline-components) (1.12.3)\n", "Requirement already satisfied: shapely<3.0.0dev in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform<2,>=1.14.0->google-cloud-pipeline-components) (2.0.4)\n", "Requirement already satisfied: pydantic<3 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform<2,>=1.14.0->google-cloud-pipeline-components) (2.7.1)\n", "Requirement already satisfied: docstring-parser<1 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform<2,>=1.14.0->google-cloud-pipeline-components) (0.16)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from Jinja2<4,>=3.1.2->google-cloud-pipeline-components) (2.1.5)\n", "Requirement already satisfied: click<9,>=8.0.0 in /usr/local/lib/python3.10/dist-packages (from kfp<=2.7.0,>=2.6.0->google-cloud-pipeline-components) (8.1.7)\n", "Collecting kfp-pipeline-spec==0.3.0 (from kfp<=2.7.0,>=2.6.0->google-cloud-pipeline-components)\n", "  Downloading kfp_pipeline_spec-0.3.0-py3-none-any.whl (12 kB)\n", "Collecting kfp-server-api<2.1.0,>=2.0.0 (from kfp<=2.7.0,>=2.6.0->google-cloud-pipeline-components)\n", "  Downloading kfp-server-api-2.0.5.tar.gz (63 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m63.4/63.4 kB\u001b[0m \u001b[31m8.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Collecting kubernetes<27,>=8.0.0 (from kfp<=2.7.0,>=2.6.0->google-cloud-pipeline-components)\n", "  Downloading kubernetes-26.1.0-py2.py3-none-any.whl (1.4 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.4/1.4 MB\u001b[0m \u001b[31m16.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0.dev0,>=3.19.5 (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5->google-cloud-pipeline-components)\n", "  Downloading protobuf-4.25.3-cp37-abi3-manylinux2014_x86_64.whl (294 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m294.6/294.6 kB\u001b[0m \u001b[31m21.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML<7,>=5.3 in /usr/local/lib/python3.10/dist-packages (from kfp<=2.7.0,>=2.6.0->google-cloud-pipeline-components) (6.0.1)\n", "Collecting requests-toolbelt<1,>=0.8.0 (from kfp<=2.7.0,>=2.6.0->google-cloud-pipeline-components)\n", "  Downloading requests_toolbelt-0.10.1-py2.py3-none-any.whl (54 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m54.5/54.5 kB\u001b[0m \u001b[31m7.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: tabulate<1,>=0.8.6 in /usr/local/lib/python3.10/dist-packages (from kfp<=2.7.0,>=2.6.0->google-cloud-pipeline-components) (0.9.0)\n", "Collecting urllib3<2.0.0 (from kfp<=2.7.0,>=2.6.0->google-cloud-pipeline-components)\n", "  Downloading urllib3-1.26.18-py2.py3-none-any.whl (143 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m143.8/143.8 kB\u001b[0m \u001b[31m19.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: grpcio<2.0dev,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5->google-cloud-pipeline-components) (1.63.0)\n", "Requirement already satisfied: grpcio-status<2.0.dev0,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5->google-cloud-pipeline-components) (1.48.2)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0.dev0,>=2.14.1->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5->google-cloud-pipeline-components) (5.3.3)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0.dev0,>=2.14.1->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5->google-cloud-pipeline-components) (0.4.0)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0.dev0,>=2.14.1->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5->google-cloud-pipeline-components) (4.9)\n", "Requirement already satisfied: google-cloud-core<3.0.0dev,>=1.6.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform<2,>=1.14.0->google-cloud-pipeline-components) (2.3.3)\n", "Requirement already satisfied: google-resumable-media<3.0dev,>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform<2,>=1.14.0->google-cloud-pipeline-components) (2.7.0)\n", "Requirement already satisfied: python-dateutil<3.0dev,>=2.7.2 in /usr/local/lib/python3.10/dist-packages (from google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform<2,>=1.14.0->google-cloud-pipeline-components) (2.8.2)\n", "Requirement already satisfied: grpc-google-iam-v1<1.0.0dev,>=0.12.4 in /usr/local/lib/python3.10/dist-packages (from google-cloud-resource-manager<3.0.0dev,>=1.3.3->google-cloud-aiplatform<2,>=1.14.0->google-cloud-pipeline-components) (0.13.0)\n", "Requirement already satisfied: six>=1.10 in /usr/local/lib/python3.10/dist-packages (from kfp-server-api<2.1.0,>=2.0.0->kfp<=2.7.0,>=2.6.0->google-cloud-pipeline-components) (1.16.0)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from kfp-server-api<2.1.0,>=2.0.0->kfp<=2.7.0,>=2.6.0->google-cloud-pipeline-components) (2024.2.2)\n", "Requirement already satisfied: setuptools>=21.0.0 in /usr/local/lib/python3.10/dist-packages (from kubernetes<27,>=8.0.0->kfp<=2.7.0,>=2.6.0->google-cloud-pipeline-components) (67.7.2)\n", "Requirement already satisfied: websocket-client!=0.40.0,!=0.41.*,!=0.42.*,>=0.32.0 in /usr/local/lib/python3.10/dist-packages (from kubernetes<27,>=8.0.0->kfp<=2.7.0,>=2.6.0->google-cloud-pipeline-components) (1.8.0)\n", "Requirement already satisfied: requests-oauthlib in /usr/local/lib/python3.10/dist-packages (from kubernetes<27,>=8.0.0->kfp<=2.7.0,>=2.6.0->google-cloud-pipeline-components) (1.3.1)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3->google-cloud-aiplatform<2,>=1.14.0->google-cloud-pipeline-components) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.2 in /usr/local/lib/python3.10/dist-packages (from pydantic<3->google-cloud-aiplatform<2,>=1.14.0->google-cloud-pipeline-components) (2.18.2)\n", "Requirement already satisfied: typing-extensions>=4.6.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3->google-cloud-aiplatform<2,>=1.14.0->google-cloud-pipeline-components) (4.11.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5->google-cloud-pipeline-components) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5->google-cloud-pipeline-components) (3.7)\n", "Requirement already satisfied: numpy<3,>=1.14 in /usr/local/lib/python3.10/dist-packages (from shapely<3.0.0dev->google-cloud-aiplatform<2,>=1.14.0->google-cloud-pipeline-components) (1.25.2)\n", "Requirement already satisfied: google-crc32c<2.0dev,>=1.0 in /usr/local/lib/python3.10/dist-packages (from google-resumable-media<3.0dev,>=0.6.0->google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform<2,>=1.14.0->google-cloud-pipeline-components) (1.5.0)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth<3.0.dev0,>=2.14.1->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5->google-cloud-pipeline-components) (0.6.0)\n", "Requirement already satisfied: oauthlib>=3.0.0 in /usr/local/lib/python3.10/dist-packages (from requests-oauthlib->kubernetes<27,>=8.0.0->kfp<=2.7.0,>=2.6.0->google-cloud-pipeline-components) (3.2.2)\n", "Building wheels for collected packages: kfp, kfp-server-api\n", "  Building wheel for kfp (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for kfp: filename=kfp-2.7.0-py3-none-any.whl size=610419 sha256=e94e649ab0db87da22158657ec96f76cc78a3ebca226f46c950dcadf03f0886e\n", "  Stored in directory: /root/.cache/pip/wheels/9e/7d/a4/f9d013e82681c9746ef10de3b00456163577a99279c5ed673d\n", "  Building wheel for kfp-server-api (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for kfp-server-api: filename=kfp_server_api-2.0.5-py3-none-any.whl size=114733 sha256=d77f55f20e9b03213a02f425bfb9756b3b5a8cdf72e4ebc298890f0e382aaef1\n", "  Stored in directory: /root/.cache/pip/wheels/ac/4f/f0/2f622aadcbf8921fb72d24f52efaffacc235f863c195c289c5\n", "Successfully built kfp kfp-server-api\n", "Installing collected packages: urllib3, protobuf, kfp-server-api, kfp-pipeline-spec, requests-toolbelt, kubernetes, kfp, google-cloud-pipeline-components\n", "\u001b[33m  WARNING: The scripts dsl-compile, dsl-compile-deprecated and kfp are installed in '/root/.local/bin' which is not on PATH.\n", "  Consider adding this directory to PATH or, if you prefer to suppress this warning, use --no-warn-script-location.\u001b[0m\u001b[33m\n", "\u001b[0m\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "tensorflow-metadata 1.15.0 requires protobuf<4.21,>=3.20.3; python_version < \"3.11\", but you have protobuf 4.25.3 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed google-cloud-pipeline-components-2.14.0 kfp-2.7.0 kfp-pipeline-spec-0.3.0 kfp-server-api-2.0.5 kubernetes-26.1.0 protobuf-4.25.3 requests-toolbelt-0.10.1 urllib3-1.26.18\n"]}], "source": ["!pip install google-cloud-aiplatform\n", "!pip install --user datasets\n", "!pip install --user google-cloud-pipeline-components"]}, {"cell_type": "code", "source": ["import IPython\n", "\n", "app = IPython.Application.instance()\n", "app.kernel.do_shutdown(True)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yWIsbb7RtAwN", "outputId": "2d89fbcf-5681-43bc-d7b5-3c01dcf928d5"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'status': 'ok', 'restart': True}"]}, "metadata": {}, "execution_count": 2}]}, {"cell_type": "code", "source": ["import IPython\n", "from google.cloud import aiplatform\n", "from google.colab import auth as google_auth\n", "google_auth.authenticate_user()"], "metadata": {"id": "TdMNkeTWspQS"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["import vertexai\n", "PROJECT_ID = \"red-delight-346705\" #@param\n", "vertexai.init(project=PROJECT_ID)"], "metadata": {"id": "Frvr_myhs1L9"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["region = \"us-central1\"\n", "REGION = \"us-central1\"\n", "project_id = \"red-delight-346705\""], "metadata": {"id": "9pog2mkBtP3E"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["! gcloud config set project {project_id}"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "InNk_-_TtkSt", "outputId": "5e45c399-30b7-484d-8286-e9e11f427661"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Updated property [core/project].\n"]}]}, {"cell_type": "code", "source": ["#Import the necessary libraries\n", "\n", "import os\n", "os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "import vertexai\n", "vertexai.init(project=PROJECT_ID, location=REGION)\n", "import kfp\n", "import sys\n", "import uuid\n", "import json\n", "import vertexai\n", "import pandas as pd\n", "from google.auth import default\n", "from datasets import load_dataset\n", "from google.cloud import aiplatform\n", "from vertexai.preview.language_models import TextGenerationModel, EvaluationTextSummarizationSpec\n"], "metadata": {"id": "FGfBIRmAuWl8"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["# Prepare & Load Training Data"], "metadata": {"id": "E4-peVbi1j4e"}}, {"cell_type": "code", "source": ["BUCKET_NAME = 'data-16-05-2024'\n", "BUCKET_URI = f\"gs://data-16-05-2024/TRAIN.jsonl\"\n", "REGION = \"us-central1\""], "metadata": {"id": "fin5SxNztrF4"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["json_url = 'https://storage.googleapis.com/data-16-05-2024/TRAIN.jsonl'\n", "df = pd.read_json(\"/content/TRAIN.jsonl\", lines=True)\n", "df.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "p98ecQiqvDZV", "outputId": "71ce2d85-de78-4f87-a83a-22d5be1794b2"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                                          input_text  \\\n", "0  The BBC News website takes a look at how games...   \n", "1  The explosion in consumer technology is to con...   \n", "2  The proportion of surfers using Microsoft's In...   \n", "3  'God games' in which players must control virt...   \n", "4  Online communities set up by the UK government...   \n", "\n", "                         output_text  \n", "0           Mobile games come of age  \n", "1    Gadget market 'to grow in 2005'  \n", "2  New browser wins over net surfers  \n", "3    Games help you 'learn and play'  \n", "4     Online commons to spark debate  "], "text/html": ["\n", "  <div id=\"df-40ae1c4e-7d4e-42af-b8a5-4cab3f4297b6\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>input_text</th>\n", "      <th>output_text</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>The BBC News website takes a look at how games...</td>\n", "      <td>Mobile games come of age</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>The explosion in consumer technology is to con...</td>\n", "      <td>Gadget market 'to grow in 2005'</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>The proportion of surfers using Microsoft's In...</td>\n", "      <td>New browser wins over net surfers</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>'God games' in which players must control virt...</td>\n", "      <td>Games help you 'learn and play'</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Online communities set up by the UK government...</td>\n", "      <td>Online commons to spark debate</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-40ae1c4e-7d4e-42af-b8a5-4cab3f4297b6')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-40ae1c4e-7d4e-42af-b8a5-4cab3f4297b6 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-40ae1c4e-7d4e-42af-b8a5-4cab3f4297b6');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-0128d878-2f95-4914-8447-001a827a8ec6\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-0128d878-2f95-4914-8447-001a827a8ec6')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-0128d878-2f95-4914-8447-001a827a8ec6 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 744,\n  \"fields\": [\n    {\n      \"column\": \"input_text\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 726,\n        \"samples\": [\n          \"The soaring cost of oil has hit global economic growth, although world's major economies should weather the storm of price rises, according to the OECD.\\n\\nIn its latest bi-annual report, the OECD cut its growth predictions for the world's main industrialised regions. US growth would reach 4.4% in 2004, but fall to 3.3% next year from a previous estimate of 3.7%, the OECD said. However, the Paris-based economics think tank said it believed the global economy could still regain momentum.\\n\\nForecasts for Japanese growth were also scaled back to 4.0% from 4.4% this year and 2.1% from 2.8% in 2005. But the outlook was worst for the 12-member eurozone bloc, with already sluggish growth forecasts slipping to 1.8% from 2.0% this year and 1.9% from 2.4% in 2005, the OECD said. Overall, the report forecast total growth of 3.6% in 2004 for the 30 member countries of the OECD, slipping to 2.9% next year before recovering to 3.1% in 2006. \\\"There are nonetheless good reasons to believe that despite recent oil price turbulence the world economy will regain momentum in a not-too-distant future,\\\" said <PERSON><PERSON><PERSON>, the OECD's chief economist. The price of crude is about 50% higher than it was at the start of 2004, but down on the record high of $55.67 set in late October.\\n\\nA dip in oil prices and improving jobs prospects would improve consumer confidence and spending, the OECD said. \\\"The oil shock is not enormous by historical standards - we have seen worse in the seventies. If the oil price does not rise any further, then we think the shock can be absorbed within the next few quarters,\\\" Vincent Koen, a senior economist with the OECD, told the BBC's World Business Report. \\\"The recovery that was underway, and has been interrupted a bit by the oil shock this year, would then regain momentum in the course of 2005.\\\" China's booming economy and a \\\"spectacular comeback\\\" in Japan - albeit one that has faltered in recent months - would help world economic recovery, the OECD said. \\\"Supported by strong balance sheets and high profits, the recovery of business investment should continue in North America and start in earnest in Europe,\\\" it added. However, the report warned: \\\"It remains to be seen whether continental Europe will play a strong supportive role through a marked upswing of final domestic demand.\\\" The OECD highlighted current depressed household expenditure in Germany and the eurozone's over-reliance on export-led growth.\\n\",\n          \"EU member states are failing to report fraud and irregularities in EU funds on a consistent basis, the UK's public spending watchdog has said.\\n\\nThe National Audit Office said although the latest figures showed reported fraud was falling, the EU still had no common definition of fraud. It also expressed concern that, for the 10th year, the European Court of Auditors had qualified the EU accounts. The NAO urged the government to push for improvements in reporting fraud. It said member states needed to be more accountable on how money was spent. The report said: \\\"Member states still do not report fraud and other irregularities to the European Anti-Fraud Office on a consistent basis.\\n\\n\\\"As the court has now qualified its opinion on the Community accounts for a decade, it is essential for all the authorities involved to contribute to the strengthening of the audit of EU revenue and expenditure and improving accountability for the financial management and use of EU resources.\\\" It said there were 922 cases of reported fraud or irregularities in EU funds in the UK in 2003, worth \\u00a338.5m (52m euros), up from 831 cases worth \\u00a335.7m in 2002. At the same time, reported fraud throughout the EU dropped from 10,276 cases worth \\u00a3808m to 8,177 cases worth \\u00a3644m. Edward Leigh, chairman of the Commons public accounts committee, said Britain had to set an example when it assumed the EU presidency.\\n\\n\\\"Any fraud in other member states is potentially fraud against the UK taxpayer, given that we are the second largest net contributor to the Community,\\\" he said. \\\"Departments responsible for administering EU funds need to make sure that they're doing everything possible to weed out improper spending. \\\"The government must take the opportunity afforded by the UK presidency of the EU to press the Commission and other member states to take an equally robust stance against fraud and irregularity, and raise overall standards of financial management.\\\" A spokesman for the European Anti-Fraud Office said the organisation agreed with the NAO's assessment of fraud reporting. \\\"The quality of reporting does differ from member state to member state, and there is room for improvement,\\\" spokesman Jorg Wojahn said. He added that there is generally good co-operation with member states and the anti-fraud office on specific cases of fraud, with the statistics studied by NAO providing a \\\"good overview for planning strategic ways of detecting fraud\\\".\\n\",\n          \"Labour's choice of a white candidate for one of the UK's most multi-racial seats proves the need for all-black short lists, says a race group.\\n\\nLocal councillor Lyn Brown was selected for West Ham, east London, in a contest between two white and five ethnic minority women. An Operation Black Vote spokesman said they now wanted to meet Labour party chairman Ian McCartney for discussions. Mr McCartney recently announced party consultation on all-black shortlists. However, Labour has so far unable been unable to comment on the implications of the West Ham result.\\n\\nAshok Vishwanathan of Operation Black Vote, which aims to increase ethnic minorities' participation in the political process and their representation, said the result again showed all-women shortlists were not effective in getting minority women selected. \\\"I think all-black shortlists are the only way to cut to the chase and address the lack of minority candidates,\\\" Mr Vishwanathan said.\\n\\nLast month the chairman of the Commission for Racial Equality (CRE) also called for ethnic minority shortlists in certain circumstances. A CRE spokesman said the organisation had nothing to add on the shortlist issue specifically but would be working with all the political parties to address the under-representation of ethnic minorities in Parliament. \\\"We will be raising it with each of the party leaders on a formal basis and helping them find the most appropriate way forward,\\\" the spokesman said. Ethnic minorities make up 8% of the United Kingdom population but only 2% of MPs - 13 out of 659 - are from a visible minority group. Twelve of them represent Labour, and one is a Liberal Democrat. If ethnic minorities were represented in the House of Commons in proportion to their numbers in the population, there would be 42 ethnic minority MPs.\\n\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"output_text\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 730,\n        \"samples\": [\n          \"Abortion not a poll issue - Blair\",\n          \"Robinson out of Six Nations\",\n          \"US data sparks inflation worries\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "code", "source": ["print(df.shape)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zyn4n4EzvDa9", "outputId": "d5811e24-561b-4638-e81e-d4208499f1cb"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["(744, 2)\n"]}]}, {"cell_type": "markdown", "source": ["Fine Tune Text Bison@002 Model"], "metadata": {"id": "mDh3c3kS15rs"}}, {"cell_type": "code", "source": ["model_display_name = 'bbc-finetuned-model' # @param {type:\"string\"}\n", "tuned_model = TextGenerationModel.from_pretrained(\"text-bison@002\")\n", "tuned_model.tune_model(\n", "training_data=df,\n", "train_steps=100,\n", "tuning_job_location=\"europe-west4\",\n", "tuned_model_location=\"europe-west4\",\n", ")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 654}, "id": "wGFyXDrqA7iD", "outputId": "461291ea-46d8-48ba-8d78-54bc8e125b24"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["INFO:google.cloud.aiplatform.pipeline_jobs:Creating PipelineJob\n", "INFO:google.cloud.aiplatform.pipeline_jobs:PipelineJob created. Resource name: projects/1060807096482/locations/europe-west4/pipelineJobs/tune-large-model-20240516161743\n", "INFO:google.cloud.aiplatform.pipeline_jobs:To use this PipelineJob in another session:\n", "INFO:google.cloud.aiplatform.pipeline_jobs:pipeline_job = aiplatform.PipelineJob.get('projects/1060807096482/locations/europe-west4/pipelineJobs/tune-large-model-20240516161743')\n", "INFO:google.cloud.aiplatform.pipeline_jobs:View Pipeline Job:\n", "https://console.cloud.google.com/vertex-ai/locations/europe-west4/pipelines/runs/tune-large-model-20240516161743?project=1060807096482\n", "INFO:google.cloud.aiplatform.pipeline_jobs:PipelineJob projects/1060807096482/locations/europe-west4/pipelineJobs/tune-large-model-20240516161743 current state:\n", "PipelineState.PIPELINE_STATE_RUNNING\n", "INFO:google.cloud.aiplatform.pipeline_jobs:PipelineJob projects/1060807096482/locations/europe-west4/pipelineJobs/tune-large-model-20240516161743 current state:\n", "PipelineState.PIPELINE_STATE_RUNNING\n", "INFO:google.cloud.aiplatform.pipeline_jobs:PipelineJob projects/1060807096482/locations/europe-west4/pipelineJobs/tune-large-model-20240516161743 current state:\n", "PipelineState.PIPELINE_STATE_RUNNING\n", "INFO:google.cloud.aiplatform.pipeline_jobs:PipelineJob projects/1060807096482/locations/europe-west4/pipelineJobs/tune-large-model-20240516161743 current state:\n", "PipelineState.PIPELINE_STATE_RUNNING\n", "INFO:google.cloud.aiplatform.pipeline_jobs:PipelineJob projects/1060807096482/locations/europe-west4/pipelineJobs/tune-large-model-20240516161743 current state:\n", "PipelineState.PIPELINE_STATE_RUNNING\n"]}, {"output_type": "error", "ename": "KeyboardInterrupt", "evalue": "", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-12-db23d520b908>\u001b[0m in \u001b[0;36m<cell line: 3>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0mmodel_display_name\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m'bbc-finetuned-model'\u001b[0m \u001b[0;31m# @param {type:\"string\"}\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[0mtuned_model\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mTextGenerationModel\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfrom_pretrained\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"text-bison@002\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 3\u001b[0;31m tuned_model.tune_model(\n\u001b[0m\u001b[1;32m      4\u001b[0m \u001b[0mtraining_data\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mdf\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0mtrain_steps\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m100\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/vertexai/language_models/_language_models.py\u001b[0m in \u001b[0;36mtune_model\u001b[0;34m(self, training_data, train_steps, learning_rate, learning_rate_multiplier, tuning_job_location, tuned_model_location, model_display_name, tuning_evaluation_spec, accelerator_type, max_context_length)\u001b[0m\n\u001b[1;32m    735\u001b[0m             \u001b[0mmax_context_length\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mmax_context_length\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    736\u001b[0m         )\n\u001b[0;32m--> 737\u001b[0;31m         \u001b[0mtuned_model\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mjob\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_tuned_model\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    738\u001b[0m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_endpoint\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mtuned_model\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_endpoint\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    739\u001b[0m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_endpoint_name\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mtuned_model\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_endpoint_name\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/vertexai/language_models/_language_models.py\u001b[0m in \u001b[0;36mget_tuned_model\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   3509\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_model\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   3510\u001b[0m             \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_model\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 3511\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_job\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mwait\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   3512\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   3513\u001b[0m         \u001b[0;31m# Getting tuned model from the pipeline.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/google/cloud/aiplatform/pipeline_jobs.py\u001b[0m in \u001b[0;36mwait\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    556\u001b[0m         \u001b[0;34m\"\"\"Wait for this PipelineJob to complete.\"\"\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    557\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_latest_future\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 558\u001b[0;31m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_block_until_complete\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    559\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    560\u001b[0m             \u001b[0msuper\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mwait\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/google/cloud/aiplatform/pipeline_jobs.py\u001b[0m in \u001b[0;36m_block_until_complete\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    716\u001b[0m                 \u001b[0mlog_wait\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mmin\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlog_wait\u001b[0m \u001b[0;34m*\u001b[0m \u001b[0mmultiplier\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmax_wait\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    717\u001b[0m                 \u001b[0mprevious_time\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcurrent_time\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 718\u001b[0;31m             \u001b[0mtime\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msleep\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mwait\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    719\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    720\u001b[0m         \u001b[0;31m# Error is only populated when the job state is\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}]}, {"cell_type": "markdown", "source": ["# Predict with the new Fine Tuned Model"], "metadata": {"id": "pPK75J9C1_je"}}, {"cell_type": "code", "source": ["response = tuned_model.predict(\"Summarize this text to generate a title: \\n Ever noticed how plane seats appear to be getting smaller and smaller? With increasing numbers of people taking to the skies, some experts are questioning if having such packed out planes is putting passengers at risk. They say that the shrinking space on aeroplanes is not only uncomfortable it it's putting our health and safety in danger. More than squabbling over the arm rest, shrinking space on planes putting our health and safety in danger? This week, a U.S consumer advisory group set up by the Department of Transportation said at a public hearing that while the government is happy to set standards for animals flying on planes, it doesn't stipulate a minimum amount of space for humans.\")\n", "print(response.text)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dLuZZQcGzCHJ", "outputId": "2ecc2a44-d7b7-4dc4-ab87-64d074c95b8d"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": [" Shrinking space on planes putting our health and safety in danger\n"]}]}, {"cell_type": "code", "source": ["tuned_model_name = tuned_model._endpoint.gca_resource.deployed_models[0].model\n", "tuned_model_1 = TextGenerationModel.get_tuned_model(tuned_model_name)\n", "#TextGenerationModel.get_tuned_model(\"bbc-finetuned-model\")\n", "response = tuned_model_1.predict(\"Summarize this text to generate a title: \\n Ever noticed how plane seats appear to be getting smaller and smaller? With increasing numbers of people taking to the skies, some experts are questioning if having such packed out planes is putting passengers at risk. They say that the shrinking space on aeroplanes is not only uncomfortable it it's putting our health and safety in danger. More than squabbling over the arm rest, shrinking space on planes putting our health and safety in danger? This week, a U.S consumer advisory group set up by the Department of Transportation said at a public hearing that while the government is happy to set standards for animals flying on planes, it doesn't stipulate a minimum amount of space for humans.\")\n", "print(response.text)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "76M2mMpl3W3C", "outputId": "93a596f4-1c0c-4f06-ceb2-38098fe39a7a"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": [" Shrinking space on planes putting our health and safety in danger\n"]}]}, {"cell_type": "markdown", "source": ["# Predict with Base Model for comparison"], "metadata": {"id": "2gnkdfyF0_H1"}}, {"cell_type": "code", "source": ["base_model = TextGenerationModel.from_pretrained(\"text-bison@002\")\n", "response = base_model.predict(\"Summarize this text to generate a title: \\n Ever noticed how plane seats appear to be getting smaller and smaller? With increasing numbers of people taking to the skies, some experts are questioning if having such packed out planes is putting passengers at risk. They say that the shrinking space on aeroplanes is not only uncomfortable it it's putting our health and safety in danger. More than squabbling over the arm rest, shrinking space on planes putting our health and safety in danger? This week, a U.S consumer advisory group set up by the Department of Transportation said at a public hearing that while the government is happy to set standards for animals flying on planes, it doesn't stipulate a minimum amount of space for humans.\")\n", "print(response.text)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "wQps8gQ52RJ9", "outputId": "8993bf8e-c820-4eed-bc51-35309244dc4e"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": [" Shrinking Space on Planes: Putting Our Health and Safety at Risk?\n"]}]}]}