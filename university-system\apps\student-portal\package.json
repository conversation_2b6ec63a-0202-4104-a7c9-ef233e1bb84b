{"name": "student-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"clsx": "^2.0.0", "lucide-react": "^0.294.0", "next": "14.0.3", "react": "^18", "react-dom": "^18", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.3", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}