{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ok!\n"]}], "source": ["import langchain\n", "print(\"ok!\")"]}, {"cell_type": "code", "execution_count": 4, "id": "2aae98ef", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from dotenv import load_dotenv\n", "\n", "load_dotenv()  # take environment variables from .env."]}, {"cell_type": "code", "execution_count": 5, "id": "efe23463", "metadata": {}, "outputs": [], "source": ["import os\n", "GOOGLE_API_KEY=os.getenv(\"GOOGLE_API_KEY\")"]}, {"cell_type": "code", "execution_count": 6, "id": "73f96202", "metadata": {}, "outputs": [], "source": ["HUGGINGFACE_TOKEN=os.getenv(\"HUGGINGFACE_TOKEN\")"]}, {"cell_type": "code", "execution_count": 15, "id": "6d3e881f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'*************************************'"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["HUGGINGFACE_TOKEN\n"]}, {"cell_type": "code", "execution_count": 7, "id": "da55b515", "metadata": {}, "outputs": [], "source": ["OPENAI_API_KEY=os.getenv(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "id": "2c7d9fb0", "metadata": {}, "source": ["# Langchain with openapi api"]}, {"cell_type": "code", "execution_count": 8, "id": "352e8ef5", "metadata": {}, "outputs": [], "source": ["import openai"]}, {"cell_type": "code", "execution_count": 9, "id": "9094c1fd", "metadata": {}, "outputs": [], "source": ["from langchain.llms import OpenAI"]}, {"cell_type": "code", "execution_count": 10, "id": "c8bf2e21", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\genaibatchc\\env\\lib\\site-packages\\langchain_core\\_api\\deprecation.py:117: LangChainDeprecationWarning: The class `langchain_community.llms.openai.OpenAI` was deprecated in langchain-community 0.0.10 and will be removed in 0.2.0. An updated version of the class exists in the langchain-openai package and should be used instead. To use it run `pip install -U langchain-openai` and import as `from langchain_openai import OpenAI`.\n", "  warn_deprecated(\n"]}], "source": ["llm=OpenAI()"]}, {"cell_type": "code", "execution_count": 11, "id": "8ddd2d6a", "metadata": {}, "outputs": [], "source": ["text=\"can you tell me about the chaina?\""]}, {"cell_type": "code", "execution_count": 12, "id": "06cd6180", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\genaibatchc\\env\\lib\\site-packages\\langchain_core\\_api\\deprecation.py:117: LangChainDeprecationWarning: The function `predict` was deprecated in LangChain 0.1.7 and will be removed in 0.2.0. Use invoke instead.\n", "  warn_deprecated(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "China, officially known as the People's Republic of China, is a country located in East Asia. It is the world's most populous country, with a population of over 1.4 billion people.\n", "\n", "China has a rich history dating back over 5,000 years and is considered one of the world's oldest civilizations. It has a diverse landscape, with mountains, deserts, and coastal regions. The country is also home to the third-longest river in the world, the Yangtze River.\n", "\n", "China is a socialist country with a single-party government led by the Communist Party of China. However, in recent years, it has undergone significant economic reforms and has become one of the fastest-growing major economies in the world.\n", "\n", "The dominant religion in China is Buddhism, followed by Taoism, Confucianism, and Christianity. The country is also known for its traditional art forms, such as calligraphy, painting, and Chinese opera.\n", "\n", "Chinese cuisine is highly diverse and is known for its use of fresh ingredients and a balance of flavors. Some popular dishes include dumplings, noodles, and Peking duck.\n", "\n", "In terms of technology, China is a leader in many industries, including manufacturing, electronics, and telecommunications. It is also known for its advancements in space exploration and\n"]}], "source": ["print(llm.predict(text))"]}, {"cell_type": "markdown", "id": "43d943cf", "metadata": {}, "source": ["# Langchain with Huggingface hub"]}, {"cell_type": "code", "execution_count": 13, "id": "23d7cdf2", "metadata": {}, "outputs": [], "source": ["from langchain import HuggingFaceHub"]}, {"cell_type": "code", "execution_count": 18, "id": "06525435", "metadata": {}, "outputs": [], "source": ["llm2=HuggingFaceHub(repo_id=\"google/flan-t5-large\",huggingfacehub_api_token=HUGGINGFACE_TOKEN)"]}, {"cell_type": "code", "execution_count": 26, "id": "5623dd5a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["'  ?'\n"]}], "source": ["print(llm2(\"'how old are you?'please translate it in hindi\"))"]}, {"cell_type": "code", "execution_count": 20, "id": "deab80e2", "metadata": {}, "outputs": [], "source": ["llm3=HuggingFaceHub(repo_id=\"mistralai/Mistral-7B-Instruct-v0.2\",huggingfacehub_api_token=HUGGINGFACE_TOKEN)"]}, {"cell_type": "code", "execution_count": 28, "id": "8d513554", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["what is the capital city of India?\n", "\n", "New Delhi is the capital city of India. It is a city that combines the old and the new, with historic sites and modern infrastructure. New Delhi is located in the northern part of India and is the political and administrative hub of the country. It is home to many government buildings, embassies, and diplomatic missions. The city is also known for its vibrant culture, delicious food, and bustling markets. Some popular tourist attractions in New Delhi include the Red Fort\n"]}], "source": ["print(llm3(\"what is the capital city of India?\"))"]}, {"cell_type": "code", "execution_count": 31, "id": "66dc4b9d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["can you give me 200 line of summary on the capital city of India New Delhi\n", "\n", "New Delhi, the capital city of India, is a bustling metropolis and a political, cultural, and commercial hub. It is located in the northern part of India, in the National Capital Territory of Delhi. The city was founded in 1911 by the British Raj as a replacement for Calcutta as the capital of India.\n", "\n", "New Delhi is a city of contrasts, where ancient monuments and modern infrastructure coexist. The city is\n"]}], "source": ["print(llm3.predict(\"can you give me 200 line of summary on the capital city of India\"))"]}, {"cell_type": "markdown", "id": "7c2815b0", "metadata": {}, "source": ["# La<PERSON><PERSON> with gemini api"]}, {"cell_type": "code", "execution_count": 1, "id": "44fd6cc0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\genaibatchc\\env\\lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from langchain_google_genai import ChatGoogleGenerativeAI"]}, {"cell_type": "code", "execution_count": 6, "id": "4ce50b12", "metadata": {}, "outputs": [], "source": ["llm4=ChatGoogleGenerativeAI(model=\"gemini-pro\",google_api_key=GOOGLE_API_KEY)"]}, {"cell_type": "code", "execution_count": 8, "id": "06a0024c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\genaibatchc\\env\\lib\\site-packages\\langchain_core\\_api\\deprecation.py:117: LangChainDeprecationWarning: The function `predict` was deprecated in LangChain 0.1.7 and will be removed in 0.2.0. Use invoke instead.\n", "  warn_deprecated(\n"]}, {"data": {"text/plain": ["'Washington, D.C.'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["llm4.predict(\"what is capital of usa?\")"]}, {"cell_type": "code", "execution_count": 10, "id": "ddcd9afb", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Washington, D.C.'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["llm4.invoke(\"what is capital of usa?\").content"]}, {"cell_type": "code", "execution_count": null, "id": "19f8cdce", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}