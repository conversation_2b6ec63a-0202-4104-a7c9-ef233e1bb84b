{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyPyBe/lGPyKYhr9vI4sYjzD", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/sunnysavita10/Generative-AI-Indepth-Basic-to-Advance/blob/main/chatbot_using_langchain_with_memory.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "w7nSj69uwsvG", "outputId": "9c3ee9f4-1cec-484b-d107-74ae36aa2ea4"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.4/50.4 kB\u001b[0m \u001b[31m2.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m393.9/393.9 kB\u001b[0m \u001b[31m10.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m149.1/149.1 kB\u001b[0m \u001b[31m8.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m4.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m4.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m141.9/141.9 kB\u001b[0m \u001b[31m7.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m3.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["!pip install -q langchain_google_genai"]}, {"cell_type": "code", "source": ["!pip install -q langchain"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "D46wSB0sxRQ1", "outputId": "51ff8460-c3bc-4e5b-ebe4-8a29321c28f2"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[?25l   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/997.8 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K   \u001b[91m━━━━━━\u001b[0m\u001b[91m╸\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m174.1/997.8 kB\u001b[0m \u001b[31m5.0 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K   \u001b[91m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[91m╸\u001b[0m \u001b[32m993.3/997.8 kB\u001b[0m \u001b[31m16.1 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m997.8/997.8 kB\u001b[0m \u001b[31m11.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}]}, {"cell_type": "code", "source": ["!pip install -q langchain_community"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6x0fQWmUxVOr", "outputId": "004891f6-d6e0-4778-ae52-9860b4954dd8"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[?25l   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/2.3 MB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K   \u001b[91m━━━━━━━━━━━━\u001b[0m\u001b[90m╺\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.7/2.3 MB\u001b[0m \u001b[31m21.7 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K   \u001b[91m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[91m╸\u001b[0m \u001b[32m2.3/2.3 MB\u001b[0m \u001b[31m43.2 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.3/2.3 MB\u001b[0m \u001b[31m30.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h\u001b[?25l   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/49.3 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.3/49.3 kB\u001b[0m \u001b[31m2.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}]}, {"cell_type": "code", "source": ["import os"], "metadata": {"id": "4S8C0UITxzUx"}, "execution_count": 4, "outputs": []}, {"cell_type": "code", "source": ["os.environ[\"LANGCHAIN_TRACING_V2\"] = \"true\"\n", "os.environ[\"LANGCHAIN_API_KEY\"] = \"***************************************************\"\n", "os.environ[\"LANGCHAIN_ENDPOINT\"] = \"https://api.smith.langchain.com\"\n", "os.environ[\"LANGCHAIN_PROJECT\"] = \"chatbot_with_langchain\"\n", "os.environ[\"GOOGLE_API_KEY\"] = \"AIzaSyD2ej6w4BipiA2YehXe3U_bO7kLC5yrLQY\""], "metadata": {"id": "-5iuio2VxbOd"}, "execution_count": 5, "outputs": []}, {"cell_type": "code", "source": ["from langchain_google_genai import ChatGoogleGenerativeAI"], "metadata": {"id": "FihGZzLnx1i1"}, "execution_count": 6, "outputs": []}, {"cell_type": "code", "source": ["model = ChatGoogleGenerativeAI(model=\"gemini-pro\",convert_system_message_to_human=True)\n", "gemini-1.5-pro-001"], "metadata": {"id": "U7XueMuWyKCt"}, "execution_count": 7, "outputs": []}, {"cell_type": "code", "source": ["model.invoke(\"hi\").content"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 90}, "id": "csq2KSv7yScs", "outputId": "56d948a8-ba61-4ac0-f4d2-42c3f3b3ec19"}, "execution_count": 8, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/langchain_google_genai/chat_models.py:381: UserWarning: Convert_system_message_to_human will be deprecated!\n", "  warnings.warn(\"Convert_system_message_to_human will be deprecated!\")\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'Hello! How can I assist you today?'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 8}]}, {"cell_type": "code", "source": ["from langchain_core.output_parsers import StrOutputParser"], "metadata": {"id": "DdS7zwgtyXNs"}, "execution_count": 9, "outputs": []}, {"cell_type": "code", "source": ["parser = StrOutputParser()"], "metadata": {"id": "_SUAIxLkylcl"}, "execution_count": 10, "outputs": []}, {"cell_type": "code", "source": ["parser.invoke(model.invoke(\"hi\"))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 90}, "id": "DS8er_IlyoD1", "outputId": "36328d76-87b9-4047-d834-28f6b40bdebd"}, "execution_count": 11, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/langchain_google_genai/chat_models.py:381: UserWarning: Convert_system_message_to_human will be deprecated!\n", "  warnings.warn(\"Convert_system_message_to_human will be deprecated!\")\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'Hello! How can I assist you today?'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "code", "source": ["from langchain_core.messages import HumanMessage"], "metadata": {"id": "jA649nEF1Bda"}, "execution_count": 12, "outputs": []}, {"cell_type": "code", "source": ["import warnings\n", "warnings.filterwarnings(\"ignore\")"], "metadata": {"id": "KV4-RZkJ1MUX"}, "execution_count": 13, "outputs": []}, {"cell_type": "code", "source": ["while True:\n", "  message = input(\"Write your query:\")\n", "  if message==\"bye\":\n", "    print(\"Good Bye have a great day!\")\n", "    break\n", "  else:\n", "    print(parser.invoke(model.invoke([HumanMessage(content=message)])))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "iksXylJ9yrae", "outputId": "1b52435c-e2a8-4117-864b-49d858ffef89"}, "execution_count": 14, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Write your query:hi\n", "Hello there! How may I assist you today?\n", "Write your query:bye\n", "Good Bye have a great day!\n"]}]}, {"cell_type": "code", "source": ["from langchain_core.chat_history import BaseChatMessageHistory"], "metadata": {"id": "2CUWZj0H1HjU"}, "execution_count": 15, "outputs": []}, {"cell_type": "code", "source": ["from langchain_core.chat_history import InMemoryChatMessageHistory"], "metadata": {"id": "ovwJtxBL2UUm"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["from langchain_core.runnables.history import RunnableWithMessageHistory"], "metadata": {"id": "QTZIqm7J2V2M"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": ["from langchain_core.messages import AIMessage"], "metadata": {"id": "hWOaeqby3Ttb"}, "execution_count": 18, "outputs": []}, {"cell_type": "code", "source": ["result = model.invoke(\n", "    [\n", "        HumanMessage(content=\"Hi! I'm cristina<PERSON>\"),\n", "        AIMessage(content=\"Hello cristinao! How can I assist you today?\"),\n", "        HumanMessage(content=\"What's my name?\")\n", "    ]\n", ")"], "metadata": {"id": "MNuPLxmd2Xtc"}, "execution_count": 19, "outputs": []}, {"cell_type": "code", "source": ["parser.invoke(result)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "nG_cTMDT3FRO", "outputId": "3adf32b7-4080-425e-bf50-b77a0fb98f04"}, "execution_count": 20, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'You did not provide me with your name, so I cannot answer this question.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 20}]}, {"cell_type": "code", "source": ["store={}"], "metadata": {"id": "EnmwiMRs3J4t"}, "execution_count": 21, "outputs": []}, {"cell_type": "code", "source": ["def get_session_history(session_id: str) -> BaseChatMessageHistory:\n", "    if session_id not in store:\n", "        store[session_id] = InMemoryChatMessageHistory()\n", "    return store[session_id]"], "metadata": {"id": "NJQ3DRcp4A5V"}, "execution_count": 22, "outputs": []}, {"cell_type": "code", "source": ["config = {\"configurable\": {\"session_id\": \"firstchat\"}}"], "metadata": {"id": "gNrB6mZX5KKK"}, "execution_count": 23, "outputs": []}, {"cell_type": "code", "source": ["model_with_memory=RunnableWithMessageHistory(model,get_session_history)"], "metadata": {"id": "CgNptPvs4WHF"}, "execution_count": 24, "outputs": []}, {"cell_type": "code", "source": ["model_with_memory.invoke([HumanMessage(content=\"Hi! I'm <PERSON><PERSON><PERSON><PERSON>\")],config=config).content"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 87}, "id": "z88Qe8yJ47zt", "outputId": "5910f3e0-28e7-4227-c795-cde6f15b4e6c"}, "execution_count": 25, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["\"Hello <PERSON><PERSON><PERSON><PERSON>! It's great to meet you. I'm <PERSON>, a multimodal AI language model developed by Google. I'm designed to understand and generate human language, and I'm always learning and improving. I can help you with a wide range of tasks, such as answering your questions, providing information, generating text, and translating languages. Just let me know how I can assist you today!\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 25}]}, {"cell_type": "code", "source": ["model_with_memory.invoke([HumanMessage(content=\"what is my name?\")],config=config).content"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "PF-z4iF95VT1", "outputId": "62dd352c-60f3-4bc4-f339-b6e3ec0a4a08"}, "execution_count": 26, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Your name is <PERSON><PERSON><PERSON><PERSON>. You told me this when you introduced yourself earlier.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 26}]}, {"cell_type": "code", "source": ["config = {\"configurable\": {\"session_id\": \"secondtchat\"}}"], "metadata": {"id": "-vXv3cMy5mGE"}, "execution_count": 27, "outputs": []}, {"cell_type": "code", "source": ["config"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "x5S2LGmP58OX", "outputId": "ee6f0210-d291-4cb8-f6a3-242baba730ba"}, "execution_count": 28, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'configurable': {'session_id': 'secondtchat'}}"]}, "metadata": {}, "execution_count": 28}]}, {"cell_type": "code", "source": ["model_with_memory.invoke([HumanMessage(content=\"what is my name?\")],config=config).content"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "urT4ryhL52JP", "outputId": "a8aeefe7-ccce-44b5-f93d-56c010329544"}, "execution_count": 29, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'I do not have access to your personal information, including your name.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 29}]}, {"cell_type": "code", "source": ["config = {\"configurable\": {\"session_id\": \"firstchat\"}}"], "metadata": {"id": "ypjgSwc76ERZ"}, "execution_count": 30, "outputs": []}, {"cell_type": "code", "source": ["config"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "T9sePfBK6HSJ", "outputId": "020ad6a8-c45a-40aa-e1ee-60e72db6ee45"}, "execution_count": 31, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'configurable': {'session_id': 'firstchat'}}"]}, "metadata": {}, "execution_count": 31}]}, {"cell_type": "code", "source": ["store"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xJd1mJYh6Tmi", "outputId": "9214991c-834a-4574-9227-cfbb15484e56"}, "execution_count": 32, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'firstchat': InMemoryChatMessageHistory(messages=[HumanMessage(content=\"Hi! I'm sunnysavita\"), AIMessage(content=\"Hello sunnysavita! It's great to meet you. I'm Gemini, a multimodal AI language model developed by Google. I'm designed to understand and generate human language, and I'm always learning and improving. I can help you with a wide range of tasks, such as answering your questions, providing information, generating text, and translating languages. Just let me know how I can assist you today!\", response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'safety_ratings': [{'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE', 'blocked': False}]}, id='run-709db503-1ab2-460c-8900-b618cf892fcb-0', usage_metadata={'input_tokens': 10, 'output_tokens': 86, 'total_tokens': 96}), HumanMessage(content='what is my name?'), AIMessage(content='Your name is sunnysavita. You told me this when you introduced yourself earlier.', response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'safety_ratings': [{'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE', 'blocked': False}]}, id='run-1413d545-5e01-4579-9cf5-55b69f695121-0', usage_metadata={'input_tokens': 103, 'output_tokens': 18, 'total_tokens': 121})]),\n", " 'secondtchat': InMemoryChatMessageHistory(messages=[HumanMessage(content='what is my name?'), AIMessage(content='I do not have access to your personal information, including your name.', response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'safety_ratings': [{'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE', 'blocked': False}]}, id='run-df5916d7-01be-401b-9299-8ffeb2ed3a7f-0', usage_metadata={'input_tokens': 6, 'output_tokens': 14, 'total_tokens': 20})])}"]}, "metadata": {}, "execution_count": 32}]}, {"cell_type": "code", "source": ["model_with_memory.invoke([HumanMessage(content=\"what is my name?\")],config=config).content"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "MUQz4Tu25_pl", "outputId": "e6396b10-89f9-421e-ddf1-d412f692ee14"}, "execution_count": 33, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Your name is <PERSON><PERSON><PERSON><PERSON>. You have told me this twice now. Is there anything else I can help you with today?'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 33}]}, {"cell_type": "code", "source": ["from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder"], "metadata": {"id": "DLDi0qEv6Jl9"}, "execution_count": 34, "outputs": []}, {"cell_type": "code", "source": ["prompt=ChatPromptTemplate.from_messages(\n", "    [\n", "      (\"system\",\"You are a helpful assistant. Answer all questions to the best of your ability.\",),\n", "      MessagesPlaceholder(variable_name=\"messages\"),\n", "    ]\n", ")"], "metadata": {"id": "_foKOtDBCrTN"}, "execution_count": 35, "outputs": []}, {"cell_type": "code", "source": ["chain = prompt | model"], "metadata": {"id": "iimbAou_DLV9"}, "execution_count": 36, "outputs": []}, {"cell_type": "code", "source": ["chain.invoke({\"messages\": [\"hi! I'm bob\"]})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sNpATGrFDctq", "outputId": "e5211956-3687-4393-b384-73a92c063378"}, "execution_count": 37, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["AIMessage(content='<PERSON> <PERSON>, how can I help you today?', response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'safety_ratings': [{'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE', 'blocked': False}]}, id='run-46e13726-ce87-4dee-96fc-6461eec8fbc2-0', usage_metadata={'input_tokens': 23, 'output_tokens': 10, 'total_tokens': 33})"]}, "metadata": {}, "execution_count": 37}]}, {"cell_type": "code", "source": ["chain.invoke({\"messages\": [HumanMessage(content=\"hi! I'm bob\")]}).content"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "2xqxlgmHDSOO", "outputId": "e6e46a29-8e04-436f-d37b-d58ae7943e93"}, "execution_count": 38, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["\"Hi <PERSON>, I'm here to help you with any questions you may have. Just ask and I'll do my best to answer.\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 38}]}, {"cell_type": "code", "source": ["chain.invoke({\"messages\": [HumanMessage(content=\"what is my name\")]}).content"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "e-Ruhp0aDqld", "outputId": "bb99ac8d-2577-482c-8c42-984185702bbc"}, "execution_count": 39, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'I do not have access to your personal information, therefore, I cannot provide you with your name.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 39}]}, {"cell_type": "code", "source": ["model_with_memory=RunnableWithMessageHistory(chain,get_session_history)"], "metadata": {"id": "ECEVEifaD2Xk"}, "execution_count": 40, "outputs": []}, {"cell_type": "code", "source": ["config = {\"configurable\": {\"session_id\": \"thirdchat\"}}"], "metadata": {"id": "srFSbTmLEecx"}, "execution_count": 41, "outputs": []}, {"cell_type": "code", "source": ["response=model_with_memory.invoke(\n", "    [HumanMessage(content=\"Hi! I'm <PERSON>\"),\n", "     ],config=config\n", ")"], "metadata": {"id": "DUOKZW96EIZl"}, "execution_count": 42, "outputs": []}, {"cell_type": "code", "source": ["response.content"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "jcPchc1bEVlM", "outputId": "52798466-bd98-4012-f0a0-a3ad0790b50b"}, "execution_count": 43, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["\"Hello <PERSON>! I'm a helpful assistant, and I'll try my best to answer your questions. Let's get started!\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 43}]}, {"cell_type": "code", "source": ["response=model_with_memory.invoke(\n", "    [HumanMessage(content=\"hi what is my name\"),\n", "     ],config=config\n", ")\n", "print(response.content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FLbAX0tGErVV", "outputId": "a63e2dbb-03e2-429d-dcbe-6967bfed1cb8"}, "execution_count": 44, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["You are <PERSON>. You told me so in your first message. Is there anything else I can help you with today?\n"]}]}, {"cell_type": "code", "source": ["response=model_with_memory.invoke(\n", "    [HumanMessage(content=\"what is 2+2?\"),\n", "     ],config=config\n", ")\n", "print(response.content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FZQ2bd6RExK1", "outputId": "4136c8b9-4ac8-44ac-d85a-551cc9ad6345"}, "execution_count": 45, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["2 + 2 is 4. Is there anything else I can help you with today?\n"]}]}, {"cell_type": "code", "source": ["response=model_with_memory.invoke(\n", "    [HumanMessage(content=\"who is a indian cricket team caption?\"),\n", "     ],config=config\n", ")\n", "print(response.content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pjZZ4ODmE1YE", "outputId": "f68d08e8-5e14-4f46-9eb7-44d294d192c9"}, "execution_count": 46, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The current captain of the Indian cricket team is <PERSON><PERSON><PERSON>. He was appointed as the captain in February 2022, taking over from <PERSON><PERSON><PERSON>. <PERSON> is a right-handed batsman and a right-arm off-break bowler. He has played for India in all three formats of the game - Tests, One Day Internationals (ODIs), and Twenty20 Internationals (T20Is).\n", "\n", "Is there anything else I can help you with today?\n"]}]}, {"cell_type": "code", "source": ["response=model_with_memory.invoke(\n", "    [HumanMessage(content=\"what should i add in previous addition so that i will become 10?\"),\n", "     ],config=config\n", ")\n", "print(response.content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MkbBe3iQE6Ge", "outputId": "2774dab5-b08a-4b6b-9557-703112d43fa8"}, "execution_count": 47, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["To get to 10 from the previous addition of 2 + 2, you need to add 6.\n", "\n", "2 + 2 + 6 = 10\n", "\n", "Is there anything else I can help you with today?\n"]}]}, {"cell_type": "code", "source": ["response=model_with_memory.invoke(\n", "    [HumanMessage(content=\"what is my name?\"),\n", "     ],config=config\n", ")\n", "print(response.content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "G0Nc-1SRFBVY", "outputId": "fd9e3a18-f5c1-4e11-f837-911af5b98d5b"}, "execution_count": 48, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["You are <PERSON>. You told me so in your first message. Is there anything else I can help you with today?\n"]}]}, {"cell_type": "code", "source": ["store"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "o-UASbCPFIg0", "outputId": "d63dde48-80ee-4178-d8c2-dee3b4ced341"}, "execution_count": 49, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'firstchat': InMemoryChatMessageHistory(messages=[HumanMessage(content=\"Hi! I'm sunnysavita\"), AIMessage(content=\"Hello sunnysavita! It's great to meet you. I'm Gemini, a multimodal AI language model developed by Google. I'm designed to understand and generate human language, and I'm always learning and improving. I can help you with a wide range of tasks, such as answering your questions, providing information, generating text, and translating languages. Just let me know how I can assist you today!\", response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'safety_ratings': [{'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE', 'blocked': False}]}, id='run-709db503-1ab2-460c-8900-b618cf892fcb-0', usage_metadata={'input_tokens': 10, 'output_tokens': 86, 'total_tokens': 96}), HumanMessage(content='what is my name?'), AIMessage(content='Your name is sunnysavita. You told me this when you introduced yourself earlier.', response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'safety_ratings': [{'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE', 'blocked': False}]}, id='run-1413d545-5e01-4579-9cf5-55b69f695121-0', usage_metadata={'input_tokens': 103, 'output_tokens': 18, 'total_tokens': 121}), HumanMessage(content='what is my name?'), AIMessage(content='Your name is sunnysavita. You have told me this twice now. Is there anything else I can help you with today?', response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'safety_ratings': [{'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE', 'blocked': False}]}, id='run-b0cb78a0-b3f0-4066-901e-292c658c95e6-0', usage_metadata={'input_tokens': 128, 'output_tokens': 27, 'total_tokens': 155})]),\n", " 'secondtchat': InMemoryChatMessageHistory(messages=[HumanMessage(content='what is my name?'), AIMessage(content='I do not have access to your personal information, including your name.', response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'safety_ratings': [{'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE', 'blocked': False}]}, id='run-df5916d7-01be-401b-9299-8ffeb2ed3a7f-0', usage_metadata={'input_tokens': 6, 'output_tokens': 14, 'total_tokens': 20})]),\n", " 'thirdchat': InMemoryChatMessageHistory(messages=[HumanMessage(content=\"Hi! I'm <PERSON>\"), AIMessage(content=\"Hello Jim! I'm a helpful assistant, and I'll try my best to answer your questions. Let's get started!\", response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'safety_ratings': [{'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE', 'blocked': False}]}, id='run-391fe1fd-4d7e-4c06-8bc1-900ae1161d7e-0', usage_metadata={'input_tokens': 23, 'output_tokens': 28, 'total_tokens': 51}), HumanMessage(content='hi what is my name'), AIMessage(content='You are Jim. You told me so in your first message. Is there anything else I can help you with today?', response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'safety_ratings': [{'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE', 'blocked': False}]}, id='run-4d111014-1d65-47df-b0a6-e25ad50c3a89-0', usage_metadata={'input_tokens': 58, 'output_tokens': 24, 'total_tokens': 82}), HumanMessage(content='what is 2+2?'), AIMessage(content='2 + 2 is 4. Is there anything else I can help you with today?', response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'safety_ratings': [{'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE', 'blocked': False}]}, id='run-9ccf7684-47e6-4dc5-87bc-ac6366859269-0', usage_metadata={'input_tokens': 91, 'output_tokens': 19, 'total_tokens': 110}), HumanMessage(content='who is a indian cricket team caption?'), AIMessage(content='The current captain of the Indian cricket team is Rohit Sharma. He was appointed as the captain in February 2022, taking over from Virat Kohli. Sharma is a right-handed batsman and a right-arm off-break bowler. He has played for India in all three formats of the game - Tests, One Day Internationals (ODIs), and Twenty20 Internationals (T20Is).\\n\\nIs there anything else I can help you with today?', response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'safety_ratings': [{'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE', 'blocked': False}]}, id='run-76a1be9b-a1c6-4cdf-b305-5b3d6e67ee58-0', usage_metadata={'input_tokens': 120, 'output_tokens': 96, 'total_tokens': 216}), HumanMessage(content='what should i add in previous addition so that i will become 10?'), AIMessage(content='To get to 10 from the previous addition of 2 + 2, you need to add 6.\\n\\n2 + 2 + 6 = 10\\n\\nIs there anything else I can help you with today?', response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'safety_ratings': [{'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE', 'blocked': False}]}, id='run-4fa389d8-0f72-4b54-a7d6-fccec22aeccd-0', usage_metadata={'input_tokens': 234, 'output_tokens': 48, 'total_tokens': 282}), HumanMessage(content='what is my name?'), AIMessage(content='You are Jim. You told me so in your first message. Is there anything else I can help you with today?', response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'safety_ratings': [{'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE', 'blocked': False}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE', 'blocked': False}]}, id='run-efafb57f-76f4-42e4-9d37-64fd2a0e9d68-0', usage_metadata={'input_tokens': 289, 'output_tokens': 24, 'total_tokens': 313})])}"]}, "metadata": {}, "execution_count": 49}]}, {"cell_type": "markdown", "source": ["## trimming the conversation"], "metadata": {"id": "O2-MqelcXLhU"}}, {"cell_type": "code", "source": ["from langchain_core.messages import SystemMessage, trim_messages"], "metadata": {"id": "DQTMqEgC1xnr"}, "execution_count": 108, "outputs": []}, {"cell_type": "code", "source": ["trimmer = trim_messages(\n", "    max_tokens=60,\n", "    strategy=\"last\",\n", "    token_counter=model,\n", "    include_system=True,\n", "    allow_partial=False,\n", "    start_on=\"human\",\n", ")"], "metadata": {"id": "UvgZL0Zl1zPp"}, "execution_count": 132, "outputs": []}, {"cell_type": "code", "source": ["messages = [\n", "    HumanMessage(content=\"hi! I'm bob\"),\n", "    AIMessage(content=\"hi!\"),\n", "    HumanMessage(content=\"I like vanilla ice cream\"),\n", "    AIMessage(content=\"nice\"),\n", "    HumanMessage(content=\"whats 2 + 2\"),\n", "    AIMessage(content=\"4\"),\n", "    HumanMessage(content=\"thanks\"),\n", "    AIMessage(content=\"no problem!\"),\n", "    HumanMessage(content=\"having fun?\"),\n", "    AIMessage(content=\"yes!\"),\n", "]"], "metadata": {"id": "13lKIaG82QBM"}, "execution_count": 109, "outputs": []}, {"cell_type": "code", "source": ["model.get_num_tokens_from_messages(messages)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kqGu2Zda2Ryy", "outputId": "0efa5731-f0b9-4751-ac1a-98615b25e170"}, "execution_count": 112, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["51"]}, "metadata": {}, "execution_count": 112}]}, {"cell_type": "code", "source": ["trimmer.invoke(messages)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "uN9_6nINlHz2", "outputId": "9dc4d59e-ea6c-481d-cd9a-b7e7d7b4029f"}, "execution_count": 113, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[HumanMessage(content='I like vanilla ice cream'),\n", " AIMessage(content='nice'),\n", " HumanMessage(content='whats 2 + 2'),\n", " AIMessage(content='4'),\n", " HumanMessage(content='thanks'),\n", " AIMessage(content='no problem!'),\n", " HumanMessage(content='having fun?'),\n", " AIMessage(content='yes!')]"]}, "metadata": {}, "execution_count": 113}]}, {"cell_type": "code", "source": ["trimmed_message = trimmer.invoke(messages)"], "metadata": {"id": "QOyyzqgX0lNR"}, "execution_count": 114, "outputs": []}, {"cell_type": "code", "source": ["model.get_num_tokens_from_messages(trimmed_message)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aPzYA49N0ngI", "outputId": "d33d109f-c160-40af-973d-230d8ef147fc"}, "execution_count": 115, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["39"]}, "metadata": {}, "execution_count": 115}]}, {"cell_type": "code", "source": ["prompt"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FxRP1ei0211m", "outputId": "517becd2-bd87-4f98-8902-81497fae1ef5"}, "execution_count": 116, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["ChatPromptTemplate(input_variables=['language', 'messages'], input_types={'messages': typing.List[typing.Union[langchain_core.messages.ai.AIMessage, langchain_core.messages.human.HumanMessage, langchain_core.messages.chat.ChatMessage, langchain_core.messages.system.SystemMessage, langchain_core.messages.function.FunctionMessage, langchain_core.messages.tool.ToolMessage]]}, messages=[SystemMessagePromptTemplate(prompt=PromptTemplate(input_variables=['language'], template='You are a helpful assistant. Answer all questions to the best of your ability in {language}.')), MessagesPlaceholder(variable_name='messages')])"]}, "metadata": {}, "execution_count": 116}]}, {"cell_type": "code", "source": ["prompt=ChatPromptTemplate.from_messages(\n", "    [\n", "      (\"system\",\"You are a helpful assistant. Answer all questions to the best of your ability.\",),\n", "      MessagesPlaceholder(variable_name=\"messages\"),\n", "    ]\n", ")"], "metadata": {"id": "o2kuCwBr2_77"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["messages"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "S2lrmXS23ILH", "outputId": "7ed6226a-2b2d-4f50-f4ac-f882c72b0464"}, "execution_count": 117, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[HumanMessage(content=\"hi! I'm bob\"),\n", " AIMessage(content='hi!'),\n", " HumanMessage(content='I like vanilla ice cream'),\n", " AIMessage(content='nice'),\n", " HumanMessage(content='whats 2 + 2'),\n", " AIMessage(content='4'),\n", " HumanMessage(content='thanks'),\n", " AIMessage(content='no problem!'),\n", " HumanMessage(content='having fun?'),\n", " AIMessage(content='yes!')]"]}, "metadata": {}, "execution_count": 117}]}, {"cell_type": "code", "source": ["trimmed_message"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "212g4NcG3LRB", "outputId": "c7093f2b-39e3-4f07-b4e4-743d2a70a809"}, "execution_count": 118, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[HumanMessage(content='I like vanilla ice cream'),\n", " AIMessage(content='nice'),\n", " HumanMessage(content='whats 2 + 2'),\n", " AIMessage(content='4'),\n", " HumanMessage(content='thanks'),\n", " AIMessage(content='no problem!'),\n", " HumanMessage(content='having fun?'),\n", " AIMessage(content='yes!')]"]}, "metadata": {}, "execution_count": 118}]}, {"cell_type": "code", "source": ["trimmed_message+[HumanMessage(content=\"what's my name?\")]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cPRZXXsy3Oag", "outputId": "bd4db7fb-86e0-4047-ef8e-94ab182c3a3b"}, "execution_count": 119, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[HumanMessage(content='I like vanilla ice cream'),\n", " AIMessage(content='nice'),\n", " HumanMessage(content='whats 2 + 2'),\n", " AIMessage(content='4'),\n", " HumanMessage(content='thanks'),\n", " AIMessage(content='no problem!'),\n", " HumanMessage(content='having fun?'),\n", " AIMessage(content='yes!'),\n", " HumanMessage(content=\"what's my name?\")]"]}, "metadata": {}, "execution_count": 119}]}, {"cell_type": "code", "source": ["messages+[HumanMessage(content=\"what's my name?\")]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hUJ3iuOG3Yak", "outputId": "1b570865-96a1-4729-99d7-789bf4924f39"}, "execution_count": 121, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[HumanMessage(content=\"hi! I'm bob\"),\n", " AIMessage(content='hi!'),\n", " HumanMessage(content='I like vanilla ice cream'),\n", " AIMessage(content='nice'),\n", " HumanMessage(content='whats 2 + 2'),\n", " AIMessage(content='4'),\n", " HumanMessage(content='thanks'),\n", " AIMessage(content='no problem!'),\n", " HumanMessage(content='having fun?'),\n", " AIMessage(content='yes!'),\n", " HumanMessage(content=\"what's my name?\")]"]}, "metadata": {}, "execution_count": 121}]}, {"cell_type": "code", "source": ["from operator import itemgetter\n", "\n", "from langchain_core.runnables import RunnablePassthrough\n", "\n", "chain = (\n", "    RunnablePassthrough.assign(messages=itemgetter(\"messages\") | trimmer)\n", "    | prompt\n", "    | model\n", ")\n", "\n", "response = chain.invoke(\n", "    {\n", "        \"messages\": messages + [HumanMessage(content=\"what's my name?\")],\n", "        \"language\": \"English\",\n", "    }\n", ")\n", "response.content"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "IG4Ux0YKlkTg", "outputId": "640fbdb9-6293-4b26-9049-407c481de524"}, "execution_count": 133, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["\"I don't know your name, as you have not told me.\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 133}]}, {"cell_type": "code", "source": ["response = chain.invoke(\n", "    {\n", "        \"messages\": messages + [HumanMessage(content=\"what math problem did i ask\")],\n", "        \"language\": \"English\",\n", "    }\n", ")\n", "response.content"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "4enzWFIXmRqX", "outputId": "b1a516fa-72ca-46b7-da0b-cfac6e30be2f"}, "execution_count": 134, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'2 + 2'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 134}]}, {"cell_type": "code", "source": ["model_with_memory = RunnableWithMessageHistory(\n", "    chain,\n", "    get_session_history,\n", "    input_messages_key=\"messages\",\n", ")\n"], "metadata": {"id": "pLIK9Vj14PY0"}, "execution_count": 135, "outputs": []}, {"cell_type": "code", "source": ["config = {\"configurable\": {\"session_id\": \"fifthchat\"}}"], "metadata": {"id": "hQpDrVrZmWFW"}, "execution_count": 136, "outputs": []}, {"cell_type": "code", "source": ["response = model_with_memory.invoke(\n", "    {\n", "        \"messages\": messages + [HumanMessage(content=\"whats my name?\")],\n", "        \"language\": \"English\",\n", "    },\n", "    config=config,\n", ")\n", "\n", "response.content"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "zomzpWPGm19R", "outputId": "0b1720fa-b733-4ed8-bcdd-e22e9cbf8e1c"}, "execution_count": 137, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'I do not know your name. You have not told me your name.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 137}]}, {"cell_type": "code", "source": ["response = model_with_memory.invoke(\n", "    {\n", "        \"messages\": [HumanMessage(content=\"what math problem did i ask?\")],\n", "        \"language\": \"English\",\n", "    },\n", "    config=config,\n", ")\n", "\n", "response.content"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "BLOpblwum4Nm", "outputId": "eb4cf00c-1cd6-4ff7-82f9-373d96c92f72"}, "execution_count": 127, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'i do not have access to that information'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 127}]}, {"cell_type": "code", "source": [], "metadata": {"id": "1ytmtmdBJJm9"}, "execution_count": null, "outputs": []}]}