{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4", "authorship_tag": "ABX9TyMiYSfyl0P/2phVKD60MU27", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU", "widgets": {"application/vnd.jupyter.widget-state+json": {"a73efd61756445ee8f59a9d4c477a496": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_68ccd8382e3b4b67918c1185619569be", "IPY_MODEL_abdde99c2beb414a85ec34c5786d6bb4", "IPY_MODEL_268eb8843398482fafafc7872d0ecfe3"], "layout": "IPY_MODEL_237e90ec67814d0dae44d87298d7832f"}}, "68ccd8382e3b4b67918c1185619569be": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_065e9d8dea90483e82a8b2245f4536ca", "placeholder": "​", "style": "IPY_MODEL_b79089b353334f93815eca389075e53e", "value": "modules.json: 100%"}}, "abdde99c2beb414a85ec34c5786d6bb4": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4e4fceb1c2694de983367ffbc14cc6b1", "max": 229, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_78a1ec72ded34a38be5a53b826a3f47e", "value": 229}}, "268eb8843398482fafafc7872d0ecfe3": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0e6e82e47c794f8c84635a9dbcfe2f41", "placeholder": "​", "style": "IPY_MODEL_9e2638ccb48848ecab369c3cfc3858d5", "value": " 229/229 [00:00&lt;00:00, 3.05kB/s]"}}, "237e90ec67814d0dae44d87298d7832f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "065e9d8dea90483e82a8b2245f4536ca": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b79089b353334f93815eca389075e53e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4e4fceb1c2694de983367ffbc14cc6b1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "78a1ec72ded34a38be5a53b826a3f47e": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "0e6e82e47c794f8c84635a9dbcfe2f41": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9e2638ccb48848ecab369c3cfc3858d5": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8a3090b91dda4a14a4ef1edcdcdcd169": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c9108a02b3ca4b3fa1fd27498841f5fe", "IPY_MODEL_d61f57fb6cad482090fe874268bb7fb0", "IPY_MODEL_e994f58d6e7e4639b9feb59166e49ece"], "layout": "IPY_MODEL_6116d2df9d8b4290981f7d45b0c1ece9"}}, "c9108a02b3ca4b3fa1fd27498841f5fe": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_147152f3f8314d5f93c35194e96bf41f", "placeholder": "​", "style": "IPY_MODEL_1a0b0b6b1848413baea8804af947cf7f", "value": "config_sentence_transformers.json: 100%"}}, "d61f57fb6cad482090fe874268bb7fb0": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d3603d3fdc314fca9132355c70948e28", "max": 122, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d6258c5bfc474ef59d9c106245dfa5f6", "value": 122}}, "e994f58d6e7e4639b9feb59166e49ece": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_918d839c76564c41ba9373b576de1cfd", "placeholder": "​", "style": "IPY_MODEL_47dad9a831044689b1926f2b9e608787", "value": " 122/122 [00:00&lt;00:00, 2.66kB/s]"}}, "6116d2df9d8b4290981f7d45b0c1ece9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "147152f3f8314d5f93c35194e96bf41f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1a0b0b6b1848413baea8804af947cf7f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d3603d3fdc314fca9132355c70948e28": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d6258c5bfc474ef59d9c106245dfa5f6": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "918d839c76564c41ba9373b576de1cfd": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "47dad9a831044689b1926f2b9e608787": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e767c41657d345a5ba8569b89d3347f7": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ea8879768efa416d9a032ac56760039f", "IPY_MODEL_61ad01c8d10d41579300176be2388cb5", "IPY_MODEL_7a2e7317b9d64b07b032e42fd1a7f021"], "layout": "IPY_MODEL_3a0feffa1d4b4f49bf875eeece9eda44"}}, "ea8879768efa416d9a032ac56760039f": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_eaf1862044564eefb3ae75a3f1d9b5cb", "placeholder": "​", "style": "IPY_MODEL_8f337c9564e748d5825b0338cbfe61ed", "value": "README.md: 100%"}}, "61ad01c8d10d41579300176be2388cb5": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_83362efd58e543b3b37fef730a92e472", "max": 3780, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a766e351652e480ba9c2bbe9e4654277", "value": 3780}}, "7a2e7317b9d64b07b032e42fd1a7f021": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f6c2e7b4ffd24a299f77966e6a01bfae", "placeholder": "​", "style": "IPY_MODEL_3722b793a3864a7ea37d17190502214f", "value": " 3.78k/3.78k [00:00&lt;00:00, 44.8kB/s]"}}, "3a0feffa1d4b4f49bf875eeece9eda44": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "eaf1862044564eefb3ae75a3f1d9b5cb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8f337c9564e748d5825b0338cbfe61ed": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "83362efd58e543b3b37fef730a92e472": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a766e351652e480ba9c2bbe9e4654277": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f6c2e7b4ffd24a299f77966e6a01bfae": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3722b793a3864a7ea37d17190502214f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4fafa1c7e2ba4226adcc96923366ad27": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_485bbf74d899477aa078deb9c041ed78", "IPY_MODEL_f99622285dd54f7ba2e53328281f4622", "IPY_MODEL_1cb793924d74484cab6ef3d25f03d6a1"], "layout": "IPY_MODEL_085ef7cf00f34d17b0a408d30edf2fe4"}}, "485bbf74d899477aa078deb9c041ed78": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_783b47a891e54914b6c1c02282d60ab4", "placeholder": "​", "style": "IPY_MODEL_316319c51c8e4641bf6a54b0faa115da", "value": "sentence_bert_config.json: 100%"}}, "f99622285dd54f7ba2e53328281f4622": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cdfe2c8c9e85479d85e9f8807a8e0d80", "max": 53, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_eaaf65e04ae643e0b345e4c9fb66b395", "value": 53}}, "1cb793924d74484cab6ef3d25f03d6a1": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a9655237479c4dda903e7cf2bce5ed47", "placeholder": "​", "style": "IPY_MODEL_6ed8f516577445a59b14d83cc70051b9", "value": " 53.0/53.0 [00:00&lt;00:00, 823B/s]"}}, "085ef7cf00f34d17b0a408d30edf2fe4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "783b47a891e54914b6c1c02282d60ab4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "316319c51c8e4641bf6a54b0faa115da": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cdfe2c8c9e85479d85e9f8807a8e0d80": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "eaaf65e04ae643e0b345e4c9fb66b395": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "a9655237479c4dda903e7cf2bce5ed47": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6ed8f516577445a59b14d83cc70051b9": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d0ad25f469704abc9f50593e7a700493": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ab96ab19368e4e0a9b9db9651e82dcfb", "IPY_MODEL_d7e49001505f423a875b4a104f802791", "IPY_MODEL_e0430cc1c475449cb7d7e95996346386"], "layout": "IPY_MODEL_310b3c09502d478cb4f6d5c368477d3d"}}, "ab96ab19368e4e0a9b9db9651e82dcfb": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_da40ed22f73d45e38c18a0840447f9f4", "placeholder": "​", "style": "IPY_MODEL_3b691de532754ca896537b9545b98b3c", "value": "config.json: 100%"}}, "d7e49001505f423a875b4a104f802791": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_23d6300688ed48f4854d3b1b1d95f7b5", "max": 718, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_07e8d136197c42e4bf13e268fe4a7e18", "value": 718}}, "e0430cc1c475449cb7d7e95996346386": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_adb7a266e6f544d69a431d639b7ec8ca", "placeholder": "​", "style": "IPY_MODEL_389621bc791d43009a1dc72f8b8c6255", "value": " 718/718 [00:00&lt;00:00, 12.9kB/s]"}}, "310b3c09502d478cb4f6d5c368477d3d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "da40ed22f73d45e38c18a0840447f9f4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3b691de532754ca896537b9545b98b3c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "23d6300688ed48f4854d3b1b1d95f7b5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "07e8d136197c42e4bf13e268fe4a7e18": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "adb7a266e6f544d69a431d639b7ec8ca": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "389621bc791d43009a1dc72f8b8c6255": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d0d22e982bae4a22bc3096a20d5df450": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5b15d268cb2f41f789fc64b69ecbfacd", "IPY_MODEL_e193101058a84d748d8d9d7de188206c", "IPY_MODEL_2260ad86c1fc4fd19a45c739a149a468"], "layout": "IPY_MODEL_12eebf8fb4754a3fb437826534a59122"}}, "5b15d268cb2f41f789fc64b69ecbfacd": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_aed192be683a4457a203023a767d9657", "placeholder": "​", "style": "IPY_MODEL_07804e100ec94400be6112715766edd4", "value": "model.safetensors: 100%"}}, "e193101058a84d748d8d9d7de188206c": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ca3c48a038ba41ad8cb8c63818efc56e", "max": 1112201288, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b7b6faa206d24c2aa170db8c1e4fdd5a", "value": 1112201288}}, "2260ad86c1fc4fd19a45c739a149a468": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_429081ab64d243658e23629aa5f5dd42", "placeholder": "​", "style": "IPY_MODEL_b48366eeb2764d538c7377dbd885e645", "value": " 1.11G/1.11G [00:08&lt;00:00, 146MB/s]"}}, "12eebf8fb4754a3fb437826534a59122": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "aed192be683a4457a203023a767d9657": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "07804e100ec94400be6112715766edd4": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ca3c48a038ba41ad8cb8c63818efc56e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b7b6faa206d24c2aa170db8c1e4fdd5a": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "429081ab64d243658e23629aa5f5dd42": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b48366eeb2764d538c7377dbd885e645": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d7551d3c5c7b40feaf5e10980941be6a": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_fc7e4928ab3d41399a353a635458b839", "IPY_MODEL_92c726d5833442a2b2370e39f1f3beb6", "IPY_MODEL_b66f31b83a7a45c2802f82e71977a484"], "layout": "IPY_MODEL_f23f5ac637634ce99d980ff914f5f2b5"}}, "fc7e4928ab3d41399a353a635458b839": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7b8d20d4f0ea491eb0c572b35632b491", "placeholder": "​", "style": "IPY_MODEL_b0c146ab051e469c9a221f7a4aa2dcb0", "value": "tokenizer_config.json: 100%"}}, "92c726d5833442a2b2370e39f1f3beb6": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7870aca389cd476f9f9fc3e18a3a7dc1", "max": 550, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b0196615760e4f8090bcefeb6dc75ad0", "value": 550}}, "b66f31b83a7a45c2802f82e71977a484": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2543d539ac1846bc9c8a60f5c9bf8b0f", "placeholder": "​", "style": "IPY_MODEL_0518c6d6fd6345a0bd4d8cd42624c9b0", "value": " 550/550 [00:00&lt;00:00, 29.9kB/s]"}}, "f23f5ac637634ce99d980ff914f5f2b5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7b8d20d4f0ea491eb0c572b35632b491": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b0c146ab051e469c9a221f7a4aa2dcb0": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7870aca389cd476f9f9fc3e18a3a7dc1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b0196615760e4f8090bcefeb6dc75ad0": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2543d539ac1846bc9c8a60f5c9bf8b0f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0518c6d6fd6345a0bd4d8cd42624c9b0": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a24d5de3191d4ab9b14042d1b74b2771": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_360e5501781641e28223c2ca04885712", "IPY_MODEL_61ba97ed908347bdb108414d00ae1da8", "IPY_MODEL_f532e2107a504f02b1c8aaf808fab285"], "layout": "IPY_MODEL_bbbe88e4bcb8419eae1287f873030c32"}}, "360e5501781641e28223c2ca04885712": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6fe021d4c3354f928864812d36c6f505", "placeholder": "​", "style": "IPY_MODEL_b858cf901d114e0fb8c5d4b5e60a37fd", "value": "sentencepiece.bpe.model: 100%"}}, "61ba97ed908347bdb108414d00ae1da8": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5f9cd04cd1ad45e797b5a19b59d12a53", "max": 5069051, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ed88bb06e121466990f46709fe4cb04a", "value": 5069051}}, "f532e2107a504f02b1c8aaf808fab285": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bb1918b54e5a49c7955615803d61d9fb", "placeholder": "​", "style": "IPY_MODEL_f2c7997c24034b339b32bc6c6e29caf1", "value": " 5.07M/5.07M [00:00&lt;00:00, 25.7MB/s]"}}, "bbbe88e4bcb8419eae1287f873030c32": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6fe021d4c3354f928864812d36c6f505": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b858cf901d114e0fb8c5d4b5e60a37fd": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5f9cd04cd1ad45e797b5a19b59d12a53": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ed88bb06e121466990f46709fe4cb04a": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "bb1918b54e5a49c7955615803d61d9fb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f2c7997c24034b339b32bc6c6e29caf1": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6251884fe0ed43649766f7564e6ad115": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c14ae62edc51484d963fa9160806d171", "IPY_MODEL_611c4d1847b34f17ae0e00dcd94154ce", "IPY_MODEL_29fd88b5e6e6476cbcce4f612905a8e6"], "layout": "IPY_MODEL_90f11cadc2ca48f58e2d63351593710c"}}, "c14ae62edc51484d963fa9160806d171": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3d35a1d2cfc94dcebf9e972fb156d966", "placeholder": "​", "style": "IPY_MODEL_d33d91cab584458ca5c739c4bc90c303", "value": "tokenizer.json: 100%"}}, "611c4d1847b34f17ae0e00dcd94154ce": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f4e5d0f719a04dc0b67589274369a421", "max": 9096735, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_81202f4eeb1c41378a67ef9b5841c1c0", "value": 9096735}}, "29fd88b5e6e6476cbcce4f612905a8e6": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b6acd772cff24e5d96d0624ddf4ff44e", "placeholder": "​", "style": "IPY_MODEL_b1c612692bb1479b8dec5f467ad58832", "value": " 9.10M/9.10M [00:00&lt;00:00, 17.0MB/s]"}}, "90f11cadc2ca48f58e2d63351593710c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3d35a1d2cfc94dcebf9e972fb156d966": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d33d91cab584458ca5c739c4bc90c303": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f4e5d0f719a04dc0b67589274369a421": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "81202f4eeb1c41378a67ef9b5841c1c0": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b6acd772cff24e5d96d0624ddf4ff44e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b1c612692bb1479b8dec5f467ad58832": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7a96b13409884197a28ec4b78a86282d": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_71f6ffd6635b40608b37066f9b67e176", "IPY_MODEL_70af227d52094d18a829639dd84955b1", "IPY_MODEL_17317293304644c5a891955e3b387964"], "layout": "IPY_MODEL_c74008e0e8e2429b89f1192f8bb0d4b7"}}, "71f6ffd6635b40608b37066f9b67e176": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e1cb3eb282a14850b48ab5a380a2acb5", "placeholder": "​", "style": "IPY_MODEL_7319aebddf3445b2b7e9a0306e0447e7", "value": "special_tokens_map.json: 100%"}}, "70af227d52094d18a829639dd84955b1": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5d19b88244904aa88059b6c9e27a8599", "max": 150, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_fffd1f3b84b443828a2f8b49d41be906", "value": 150}}, "17317293304644c5a891955e3b387964": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5f5f4d08736d4f2e96c439820fc64713", "placeholder": "​", "style": "IPY_MODEL_ced7c15e2caa4e73a6feb289b66a3dfe", "value": " 150/150 [00:00&lt;00:00, 9.26kB/s]"}}, "c74008e0e8e2429b89f1192f8bb0d4b7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e1cb3eb282a14850b48ab5a380a2acb5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7319aebddf3445b2b7e9a0306e0447e7": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5d19b88244904aa88059b6c9e27a8599": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fffd1f3b84b443828a2f8b49d41be906": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5f5f4d08736d4f2e96c439820fc64713": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ced7c15e2caa4e73a6feb289b66a3dfe": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "319c9d6c19af43a1bfdeea654417384f": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b57386b666e74e1e98b2dd63ddee47f7", "IPY_MODEL_5a1215e03da746c1afdd509b76ab1004", "IPY_MODEL_8ccec9a9c0f74eb8be6955cff6e53705"], "layout": "IPY_MODEL_ab8123ece1a240dfa5ca5745e6ab8292"}}, "b57386b666e74e1e98b2dd63ddee47f7": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_edd08d1dc4ba4372a0d922d6d3082aed", "placeholder": "​", "style": "IPY_MODEL_638363a050bb40e3bd7c4486bae71ed5", "value": "1_Pooling/config.json: 100%"}}, "5a1215e03da746c1afdd509b76ab1004": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4fbbae328003461a9ff3504ad3ea6ce1", "max": 190, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4261373691d54f818090945cc72bae02", "value": 190}}, "8ccec9a9c0f74eb8be6955cff6e53705": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1a8126495ffe4bbc9a25ee1815db4cbb", "placeholder": "​", "style": "IPY_MODEL_9b3cad44fe2548c48336193e7c4643c9", "value": " 190/190 [00:00&lt;00:00, 10.5kB/s]"}}, "ab8123ece1a240dfa5ca5745e6ab8292": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "edd08d1dc4ba4372a0d922d6d3082aed": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "638363a050bb40e3bd7c4486bae71ed5": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4fbbae328003461a9ff3504ad3ea6ce1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4261373691d54f818090945cc72bae02": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "1a8126495ffe4bbc9a25ee1815db4cbb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9b3cad44fe2548c48336193e7c4643c9": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "333ba957c0164cc7b1367fb4e77c165f": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_60dffc29c59743719ccbe57607b306be", "IPY_MODEL_1c1b5ab6594246699420d15f40dcbf9a", "IPY_MODEL_9d17552a55fc44be878dd307775ac321"], "layout": "IPY_MODEL_9e0dd06ae62747ffa2afd3603eece633"}}, "60dffc29c59743719ccbe57607b306be": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cbedaac8d74e4abca2d169ac86c1a637", "placeholder": "​", "style": "IPY_MODEL_255b200fe7844af5ae00e2db870baeca", "value": "config.json: 100%"}}, "1c1b5ab6594246699420d15f40dcbf9a": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8dd323012ba14f6eb099213e75490155", "max": 794, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9234b774c856496a93449f43840cdf22", "value": 794}}, "9d17552a55fc44be878dd307775ac321": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9945335709b245ca8ec2442cc91e3f17", "placeholder": "​", "style": "IPY_MODEL_f1ffd96a7aab475283cc4eeec7f51e79", "value": " 794/794 [00:00&lt;00:00, 41.6kB/s]"}}, "9e0dd06ae62747ffa2afd3603eece633": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cbedaac8d74e4abca2d169ac86c1a637": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "255b200fe7844af5ae00e2db870baeca": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8dd323012ba14f6eb099213e75490155": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9234b774c856496a93449f43840cdf22": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9945335709b245ca8ec2442cc91e3f17": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f1ffd96a7aab475283cc4eeec7f51e79": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "35a8f13d044b46839c6929f4b3c051b5": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_47f9be3cc02742e8a4a0631e32ebef7c", "IPY_MODEL_334ee20108d542f0adc8f90a03639ce2", "IPY_MODEL_e90378ec97604ff7a9d210d9ab813770"], "layout": "IPY_MODEL_51a10f9e1520468abe3aa8ab11b96fe4"}}, "47f9be3cc02742e8a4a0631e32ebef7c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8ab3c4817e1646e8997d3a7dbe2d8de0", "placeholder": "​", "style": "IPY_MODEL_c4d4e21690a8406db75c681fe5a982c3", "value": "pytorch_model.bin: 100%"}}, "334ee20108d542f0adc8f90a03639ce2": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d8a6c3f4dd5843808e9f892568528b2f", "max": 90903017, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_46cd6012a4764a61bc4d4afb901adfbf", "value": 90903017}}, "e90378ec97604ff7a9d210d9ab813770": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e35dfa10dc3343498f7d99b99f8f9bbe", "placeholder": "​", "style": "IPY_MODEL_06f393835dcd46bd826335e62c32de9d", "value": " 90.9M/90.9M [00:00&lt;00:00, 144MB/s]"}}, "51a10f9e1520468abe3aa8ab11b96fe4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8ab3c4817e1646e8997d3a7dbe2d8de0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c4d4e21690a8406db75c681fe5a982c3": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d8a6c3f4dd5843808e9f892568528b2f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "46cd6012a4764a61bc4d4afb901adfbf": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e35dfa10dc3343498f7d99b99f8f9bbe": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "06f393835dcd46bd826335e62c32de9d": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8e3c909019364932843a339d20f5b361": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_18dac171c82043688a6d2f181ff675db", "IPY_MODEL_49768866bcd04cd1a1d9a87bd52d8ece", "IPY_MODEL_01db3797f9bf4f538502c97de09c87d0"], "layout": "IPY_MODEL_d31a76180c5049768d150c88cdb56a6d"}}, "18dac171c82043688a6d2f181ff675db": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_876314d86ebf4879a3f831a982d6c9a0", "placeholder": "​", "style": "IPY_MODEL_5907a798063a4e5cb2c943a23eb82d70", "value": "tokenizer_config.json: 100%"}}, "49768866bcd04cd1a1d9a87bd52d8ece": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_acce923405544520ac3173e6d98e1c1c", "max": 316, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_754f2e87a56e410d961c8bb803258d22", "value": 316}}, "01db3797f9bf4f538502c97de09c87d0": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f9c9e5dd77294f47b227fceea135c663", "placeholder": "​", "style": "IPY_MODEL_1145cfda26014c00a372cad81fd7292f", "value": " 316/316 [00:00&lt;00:00, 19.9kB/s]"}}, "d31a76180c5049768d150c88cdb56a6d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "876314d86ebf4879a3f831a982d6c9a0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5907a798063a4e5cb2c943a23eb82d70": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "acce923405544520ac3173e6d98e1c1c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "754f2e87a56e410d961c8bb803258d22": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f9c9e5dd77294f47b227fceea135c663": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1145cfda26014c00a372cad81fd7292f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5e0aa6c336094cdcbff419ace9866327": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_bf81c8f43ac8436eaf7e4011c51a05be", "IPY_MODEL_38cef872a51f4ef499e0fd885144c593", "IPY_MODEL_b95e39b0d7304f6a8e8f3c1f539b5cfe"], "layout": "IPY_MODEL_7a87c8bf911f403d92563ac4b0c8e708"}}, "bf81c8f43ac8436eaf7e4011c51a05be": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0002e0e41c2246c8828d54ff07773cd7", "placeholder": "​", "style": "IPY_MODEL_ff569f3b6df641d48f6657e699c32c5a", "value": "vocab.txt: 100%"}}, "38cef872a51f4ef499e0fd885144c593": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e4f33c9295cf402e803f9f0ffa676894", "max": 231508, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_1b167f862fa44b199524107af690eaea", "value": 231508}}, "b95e39b0d7304f6a8e8f3c1f539b5cfe": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_be453dbcf0ab4bda8eb5b4586ea260df", "placeholder": "​", "style": "IPY_MODEL_f993ac5a08e948cebb08ce7b03cb1553", "value": " 232k/232k [00:00&lt;00:00, 5.15MB/s]"}}, "7a87c8bf911f403d92563ac4b0c8e708": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0002e0e41c2246c8828d54ff07773cd7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ff569f3b6df641d48f6657e699c32c5a": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e4f33c9295cf402e803f9f0ffa676894": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1b167f862fa44b199524107af690eaea": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "be453dbcf0ab4bda8eb5b4586ea260df": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f993ac5a08e948cebb08ce7b03cb1553": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "41feb9480c9d4766870aae759b13eb83": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b331725039514bdd855459d96399b6b4", "IPY_MODEL_186c4b647c444c3a8883ad7356057d82", "IPY_MODEL_2eee9553552c424c9c2af6a203122659"], "layout": "IPY_MODEL_00f97ae5e93b4b9c8a2875ad7261d920"}}, "b331725039514bdd855459d96399b6b4": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bb61044a9b8d4a0b8046323b1362d5c0", "placeholder": "​", "style": "IPY_MODEL_f82665fb752e424fa74862157349de6f", "value": "special_tokens_map.json: 100%"}}, "186c4b647c444c3a8883ad7356057d82": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_893ed7f5802543a1a91ad502cb4604c4", "max": 112, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7ed3ad5926fa416689ab21d83d3c4130", "value": 112}}, "2eee9553552c424c9c2af6a203122659": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1d89db0e3d1e44acbf306d20b8bf38fb", "placeholder": "​", "style": "IPY_MODEL_09987e3201424e1f9958604ea336e601", "value": " 112/112 [00:00&lt;00:00, 7.27kB/s]"}}, "00f97ae5e93b4b9c8a2875ad7261d920": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bb61044a9b8d4a0b8046323b1362d5c0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f82665fb752e424fa74862157349de6f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "893ed7f5802543a1a91ad502cb4604c4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7ed3ad5926fa416689ab21d83d3c4130": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "1d89db0e3d1e44acbf306d20b8bf38fb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "09987e3201424e1f9958604ea336e601": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/sunnysavita10/Indepth-GENAI/blob/main/Reranking_from_Scratch.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "zkZpN87d4HJf"}, "outputs": [], "source": ["documents = [\n", "    \"This is a list which containing sample documents.\",\n", "    \"Keywords are important for keyword-based search.\",\n", "    \"Document analysis involves extracting keywords.\",\n", "    \"Keyword-based search relies on sparse embeddings.\",\n", "    \"Understanding document structure aids in keyword extraction.\",\n", "    \"Efficient keyword extraction enhances search accuracy.\",\n", "    \"Semantic similarity improves document retrieval performance.\",\n", "    \"Machine learning algorithms can optimize keyword extraction methods.\"\n", "]"]}, {"cell_type": "code", "source": ["!pip install sentence_transformers"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MLF_E-ZQCYq_", "outputId": "d6663d67-6aaa-4d05-e6d6-f93a38bee6d0"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting sentence_transformers\n", "  Downloading sentence_transformers-3.0.1-py3-none-any.whl (227 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m227.1/227.1 kB\u001b[0m \u001b[31m1.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: transformers<5.0.0,>=4.34.0 in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (4.41.2)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (4.66.4)\n", "Requirement already satisfied: torch>=1.11.0 in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (2.3.0+cu121)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (1.25.2)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (1.2.2)\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (1.11.4)\n", "Requirement already satisfied: huggingface-hub>=0.15.1 in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (0.23.2)\n", "Requirement already satisfied: Pillow in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (9.4.0)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence_transformers) (3.14.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence_transformers) (2023.6.0)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence_transformers) (24.0)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence_transformers) (6.0.1)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence_transformers) (2.31.0)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence_transformers) (4.12.1)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence_transformers) (1.12.1)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence_transformers) (3.3)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence_transformers) (3.1.4)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.1.105 (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_cuda_nvrtc_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (23.7 MB)\n", "Collecting nvidia-cuda-runtime-cu12==12.1.105 (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_cuda_runtime_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (823 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.1.105 (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_cuda_cupti_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (14.1 MB)\n", "Collecting nvidia-cudnn-cu12==******** (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_cudnn_cu12-********-py3-none-manylinux1_x86_64.whl (731.7 MB)\n", "Collecting nvidia-cublas-cu12==******** (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_cublas_cu12-********-py3-none-manylinux1_x86_64.whl (410.6 MB)\n", "Collecting nvidia-cufft-cu12==********* (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_cufft_cu12-*********-py3-none-manylinux1_x86_64.whl (121.6 MB)\n", "Collecting nvidia-curand-cu12==********** (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_curand_cu12-**********-py3-none-manylinux1_x86_64.whl (56.5 MB)\n", "Collecting nvidia-cusolver-cu12==********** (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_cusolver_cu12-**********-py3-none-manylinux1_x86_64.whl (124.2 MB)\n", "Collecting nvidia-cusparse-cu12==********** (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_cusparse_cu12-**********-py3-none-manylinux1_x86_64.whl (196.0 MB)\n", "Collecting nvidia-nccl-cu12==2.20.5 (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_nccl_cu12-2.20.5-py3-none-manylinux2014_x86_64.whl (176.2 MB)\n", "Collecting nvidia-nvtx-cu12==12.1.105 (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_nvtx_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (99 kB)\n", "Requirement already satisfied: triton==2.3.0 in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence_transformers) (2.3.0)\n", "Collecting nvidia-nvjitlink-cu12 (from nvidia-cusolver-cu12==**********->torch>=1.11.0->sentence_transformers)\n", "  Downloading nvidia_nvjitlink_cu12-12.5.40-py3-none-manylinux2014_x86_64.whl (21.3 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.3/21.3 MB\u001b[0m \u001b[31m52.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.34.0->sentence_transformers) (2024.5.15)\n", "Requirement already satisfied: tokenizers<0.20,>=0.19 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.34.0->sentence_transformers) (0.19.1)\n", "Requirement already satisfied: safetensors>=0.4.1 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.34.0->sentence_transformers) (0.4.3)\n", "Requirement already satisfied: joblib>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from scikit-learn->sentence_transformers) (1.4.2)\n", "Requirement already satisfied: threadpoolctl>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from scikit-learn->sentence_transformers) (3.5.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch>=1.11.0->sentence_transformers) (2.1.5)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.15.1->sentence_transformers) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.15.1->sentence_transformers) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.15.1->sentence_transformers) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.15.1->sentence_transformers) (2024.6.2)\n", "Requirement already satisfied: mpmath<1.4.0,>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from sympy->torch>=1.11.0->sentence_transformers) (1.3.0)\n", "Installing collected packages: nvidia-nvtx-cu12, nvidia-nvjitlink-cu12, nvidia-nccl-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, nvidia-cusparse-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12, sentence_transformers\n", "Successfully installed nvidia-cublas-cu12-******** nvidia-cuda-cupti-cu12-12.1.105 nvidia-cuda-nvrtc-cu12-12.1.105 nvidia-cuda-runtime-cu12-12.1.105 nvidia-cudnn-cu12-******** nvidia-cufft-cu12-********* nvidia-curand-cu12-********** nvidia-cusolver-cu12-********** nvidia-cusparse-cu12-********** nvidia-nccl-cu12-2.20.5 nvidia-nvjitlink-cu12-12.5.40 nvidia-nvtx-cu12-12.1.105 sentence_transformers-3.0.1\n"]}]}, {"cell_type": "code", "source": ["from sentence_transformers import SentenceTransformer"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "A2TapY91Cde2", "outputId": "59730e9c-973c-4e42-9e62-319b0c783df2"}, "execution_count": 5, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/sentence_transformers/cross_encoder/CrossEncoder.py:11: TqdmExperimentalWarning: Using `tqdm.autonotebook.tqdm` in notebook mode. Use `tqdm.tqdm` instead to force console mode (e.g. in jupyter console)\n", "  from tqdm.autonotebook import tqdm, trange\n"]}]}, {"cell_type": "code", "source": ["# Load pre-trained Sentence Transformer model\n", "model_name = 'sentence-transformers/paraphrase-xlm-r-multilingual-v1'"], "metadata": {"id": "YcMjOGquCkSu"}, "execution_count": 6, "outputs": []}, {"cell_type": "code", "source": ["model = SentenceTransformer(model_name)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 528, "referenced_widgets": ["a73efd61756445ee8f59a9d4c477a496", "68ccd8382e3b4b67918c1185619569be", "abdde99c2beb414a85ec34c5786d6bb4", "268eb8843398482fafafc7872d0ecfe3", "237e90ec67814d0dae44d87298d7832f", "065e9d8dea90483e82a8b2245f4536ca", "b79089b353334f93815eca389075e53e", "4e4fceb1c2694de983367ffbc14cc6b1", "78a1ec72ded34a38be5a53b826a3f47e", "0e6e82e47c794f8c84635a9dbcfe2f41", "9e2638ccb48848ecab369c3cfc3858d5", "8a3090b91dda4a14a4ef1edcdcdcd169", "c9108a02b3ca4b3fa1fd27498841f5fe", "d61f57fb6cad482090fe874268bb7fb0", "e994f58d6e7e4639b9feb59166e49ece", "6116d2df9d8b4290981f7d45b0c1ece9", "147152f3f8314d5f93c35194e96bf41f", "1a0b0b6b1848413baea8804af947cf7f", "d3603d3fdc314fca9132355c70948e28", "d6258c5bfc474ef59d9c106245dfa5f6", "918d839c76564c41ba9373b576de1cfd", "47dad9a831044689b1926f2b9e608787", "e767c41657d345a5ba8569b89d3347f7", "ea8879768efa416d9a032ac56760039f", "61ad01c8d10d41579300176be2388cb5", "7a2e7317b9d64b07b032e42fd1a7f021", "3a0feffa1d4b4f49bf875eeece9eda44", "eaf1862044564eefb3ae75a3f1d9b5cb", "8f337c9564e748d5825b0338cbfe61ed", "83362efd58e543b3b37fef730a92e472", "a766e351652e480ba9c2bbe9e4654277", "f6c2e7b4ffd24a299f77966e6a01bfae", "3722b793a3864a7ea37d17190502214f", "4fafa1c7e2ba4226adcc96923366ad27", "485bbf74d899477aa078deb9c041ed78", "f99622285dd54f7ba2e53328281f4622", "1cb793924d74484cab6ef3d25f03d6a1", "085ef7cf00f34d17b0a408d30edf2fe4", "783b47a891e54914b6c1c02282d60ab4", "316319c51c8e4641bf6a54b0faa115da", "cdfe2c8c9e85479d85e9f8807a8e0d80", "eaaf65e04ae643e0b345e4c9fb66b395", "a9655237479c4dda903e7cf2bce5ed47", "6ed8f516577445a59b14d83cc70051b9", "d0ad25f469704abc9f50593e7a700493", "ab96ab19368e4e0a9b9db9651e82dcfb", "d7e49001505f423a875b4a104f802791", "e0430cc1c475449cb7d7e95996346386", "310b3c09502d478cb4f6d5c368477d3d", "da40ed22f73d45e38c18a0840447f9f4", "3b691de532754ca896537b9545b98b3c", "23d6300688ed48f4854d3b1b1d95f7b5", "07e8d136197c42e4bf13e268fe4a7e18", "adb7a266e6f544d69a431d639b7ec8ca", "389621bc791d43009a1dc72f8b8c6255", "d0d22e982bae4a22bc3096a20d5df450", "5b15d268cb2f41f789fc64b69ecbfacd", "e193101058a84d748d8d9d7de188206c", "2260ad86c1fc4fd19a45c739a149a468", "12eebf8fb4754a3fb437826534a59122", "aed192be683a4457a203023a767d9657", "07804e100ec94400be6112715766edd4", "ca3c48a038ba41ad8cb8c63818efc56e", "b7b6faa206d24c2aa170db8c1e4fdd5a", "429081ab64d243658e23629aa5f5dd42", "b48366eeb2764d538c7377dbd885e645", "d7551d3c5c7b40feaf5e10980941be6a", "fc7e4928ab3d41399a353a635458b839", "92c726d5833442a2b2370e39f1f3beb6", "b66f31b83a7a45c2802f82e71977a484", "f23f5ac637634ce99d980ff914f5f2b5", "7b8d20d4f0ea491eb0c572b35632b491", "b0c146ab051e469c9a221f7a4aa2dcb0", "7870aca389cd476f9f9fc3e18a3a7dc1", "b0196615760e4f8090bcefeb6dc75ad0", "2543d539ac1846bc9c8a60f5c9bf8b0f", "0518c6d6fd6345a0bd4d8cd42624c9b0", "a24d5de3191d4ab9b14042d1b74b2771", "360e5501781641e28223c2ca04885712", "61ba97ed908347bdb108414d00ae1da8", "f532e2107a504f02b1c8aaf808fab285", "bbbe88e4bcb8419eae1287f873030c32", "6fe021d4c3354f928864812d36c6f505", "b858cf901d114e0fb8c5d4b5e60a37fd", "5f9cd04cd1ad45e797b5a19b59d12a53", "ed88bb06e121466990f46709fe4cb04a", "bb1918b54e5a49c7955615803d61d9fb", "f2c7997c24034b339b32bc6c6e29caf1", "6251884fe0ed43649766f7564e6ad115", "c14ae62edc51484d963fa9160806d171", "611c4d1847b34f17ae0e00dcd94154ce", "29fd88b5e6e6476cbcce4f612905a8e6", "90f11cadc2ca48f58e2d63351593710c", "3d35a1d2cfc94dcebf9e972fb156d966", "d33d91cab584458ca5c739c4bc90c303", "f4e5d0f719a04dc0b67589274369a421", "81202f4eeb1c41378a67ef9b5841c1c0", "b6acd772cff24e5d96d0624ddf4ff44e", "b1c612692bb1479b8dec5f467ad58832", "7a96b13409884197a28ec4b78a86282d", "71f6ffd6635b40608b37066f9b67e176", "70af227d52094d18a829639dd84955b1", "17317293304644c5a891955e3b387964", "c74008e0e8e2429b89f1192f8bb0d4b7", "e1cb3eb282a14850b48ab5a380a2acb5", "7319aebddf3445b2b7e9a0306e0447e7", "5d19b88244904aa88059b6c9e27a8599", "fffd1f3b84b443828a2f8b49d41be906", "5f5f4d08736d4f2e96c439820fc64713", "ced7c15e2caa4e73a6feb289b66a3dfe", "319c9d6c19af43a1bfdeea654417384f", "b57386b666e74e1e98b2dd63ddee47f7", "5a1215e03da746c1afdd509b76ab1004", "8ccec9a9c0f74eb8be6955cff6e53705", "ab8123ece1a240dfa5ca5745e6ab8292", "edd08d1dc4ba4372a0d922d6d3082aed", "638363a050bb40e3bd7c4486bae71ed5", "4fbbae328003461a9ff3504ad3ea6ce1", "4261373691d54f818090945cc72bae02", "1a8126495ffe4bbc9a25ee1815db4cbb", "9b3cad44fe2548c48336193e7c4643c9"]}, "id": "3HLEx9rKCxdn", "outputId": "cf3da8e1-e2b6-4153-8d0e-384210001ba0"}, "execution_count": 7, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/huggingface_hub/utils/_token.py:89: UserWarning: \n", "The secret `HF_TOKEN` does not exist in your Colab secrets.\n", "To authenticate with the Hugging Face Hub, create a token in your settings tab (https://huggingface.co/settings/tokens), set it as secret in your Google Colab and restart your session.\n", "You will be able to reuse this secret in all of your notebooks.\n", "Please note that authentication is recommended but still optional to access public models or datasets.\n", "  warnings.warn(\n"]}, {"output_type": "display_data", "data": {"text/plain": ["modules.json:   0%|          | 0.00/229 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "a73efd61756445ee8f59a9d4c477a496"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config_sentence_transformers.json:   0%|          | 0.00/122 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "8a3090b91dda4a14a4ef1edcdcdcd169"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["README.md:   0%|          | 0.00/3.78k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "e767c41657d345a5ba8569b89d3347f7"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["sentence_bert_config.json:   0%|          | 0.00/53.0 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "4fafa1c7e2ba4226adcc96923366ad27"}}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/huggingface_hub/file_download.py:1132: FutureWarning: `resume_download` is deprecated and will be removed in version 1.0.0. Downloads always resume when possible. If you want to force a new download, use `force_download=True`.\n", "  warnings.warn(\n"]}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/718 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "d0ad25f469704abc9f50593e7a700493"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors:   0%|          | 0.00/1.11G [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "d0d22e982bae4a22bc3096a20d5df450"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json:   0%|          | 0.00/550 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "d7551d3c5c7b40feaf5e10980941be6a"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["sentencepiece.bpe.model:   0%|          | 0.00/5.07M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "a24d5de3191d4ab9b14042d1b74b2771"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.json:   0%|          | 0.00/9.10M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "6251884fe0ed43649766f7564e6ad115"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["special_tokens_map.json:   0%|          | 0.00/150 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "7a96b13409884197a28ec4b78a86282d"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["1_Pooling/config.json:   0%|          | 0.00/190 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "319c9d6c19af43a1bfdeea654417384f"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["documents"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "oj8kcRVZDDYs", "outputId": "814cd1b0-cacd-44c3-b3d1-65df0b6534cd"}, "execution_count": 10, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['This is a list which containing sample documents.',\n", " 'Keywords are important for keyword-based search.',\n", " 'Document analysis involves extracting keywords.',\n", " 'Keyword-based search relies on sparse embeddings.',\n", " 'Understanding document structure aids in keyword extraction.',\n", " 'Efficient keyword extraction enhances search accuracy.',\n", " 'Semantic similarity improves document retrieval performance.',\n", " 'Machine learning algorithms can optimize keyword extraction methods.']"]}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "code", "source": ["len(documents)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yaI-PRMwDGxf", "outputId": "00c9abfc-2371-4006-d76a-681e7b9c619b"}, "execution_count": 11, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["8"]}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "code", "source": ["document_embeddings = model.encode(documents)"], "metadata": {"id": "PYxjbDxdC0T_"}, "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": ["len(document_embeddings)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UUENS13LDJ5y", "outputId": "08c30846-cab5-41ce-a7de-fc7daaabc4a0"}, "execution_count": 12, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["8"]}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "code", "source": ["len(document_embeddings[0])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "25ZYBnhcDMcj", "outputId": "b9665b05-aea9-4d7b-fc4a-70e578f8eb79"}, "execution_count": 13, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["768"]}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "code", "source": ["for i, embedding in enumerate(document_embeddings):\n", "    print(f\"Document {i+1} embedding: {embedding}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "IcQ1Q9PtC9o-", "outputId": "15b836c5-7519-4ce7-dde0-6f0a8ff833ea"}, "execution_count": 9, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Document 1 embedding: [ 0.10894686  0.07812074  0.11626554 -0.3191249   0.46890235  0.4351442\n", "  0.01453751  0.44238743  0.29716408 -0.18982704  0.07389083 -0.27864912\n", "  0.21338145 -0.12077019  0.17891686 -0.00789894  0.0475487  -0.18204558\n", "  0.34227124 -0.06994265 -0.14288713  0.5714126  -0.11153252 -0.178954\n", "  0.0152314   0.26105717 -0.20555818  0.05203104 -0.02810767  0.23873205\n", "  0.01206982  0.0440495   0.02242325 -0.13895164 -0.7410038   0.2560102\n", "  0.08149666  0.18820472 -0.41237685  0.11368611  0.28121182  0.05860903\n", " -0.17318785  0.33549157  0.21803693 -0.05090728 -0.05457798 -0.8738478\n", " -0.24082269  0.3200701   0.44761682  0.06347839  0.53574854  0.16607253\n", " -0.33197004  0.33393636  0.28615928 -0.5419567  -0.27132416  0.24881189\n", " -0.2391937  -0.46926293  0.13836566  0.3784288  -0.01304432  0.01990609\n", "  0.323651    0.45857546  0.07600272  0.25299594 -0.42938972  0.10051935\n", " -0.33042622 -0.69876486  0.01035881  0.05666538  0.14731242 -0.47082362\n", "  0.08064     0.3387045  -0.2727816   0.04514604 -0.10689211  0.25125435\n", "  0.14075005 -0.13837183 -0.01316251 -0.08204988  0.1904552  -0.02820524\n", "  0.58813363  0.13334216 -0.13449602  0.09493806  0.10691478  0.25040096\n", "  0.09172603 -0.7621291   0.20531636  0.13852249 -0.01296959 -0.02651782\n", "  0.27626568  0.09329761  0.07306136 -0.03366381 -0.42343092  0.31263363\n", " -0.1544367   0.04113205  0.03063022  0.29360083  0.14560464 -0.16713317\n", "  0.17379875 -0.33332694  0.08415512  0.11696905  0.08691192 -0.32269332\n", "  0.05883429  0.34477314 -0.73807126 -0.01069327 -0.21012229  0.12280921\n", " -0.04847906  0.01519065 -0.13780273  0.31228817 -0.00413636 -0.03545758\n", "  0.37543544 -0.37829626  0.20408289  0.09740987  0.29005325  0.18211062\n", "  0.1314571   0.09926128  0.2615175   0.23927093  0.17477731  0.23885953\n", "  0.03784915 -0.14516921 -0.03978111 -0.10953913  0.33756885 -0.02056907\n", "  0.07034879  0.02593715  0.21191835 -0.07686583 -0.04871245  0.28239414\n", "  0.00821075 -0.36339426  0.21086389  0.2647715   0.21419017 -0.06008905\n", "  0.43244186 -0.18695158 -0.00801666 -0.0783243   0.2475638  -0.01955241\n", " -0.06818825 -0.06987115 -0.4053047  -0.190234    0.20279928 -0.31320336\n", " -0.24453075  0.09538133  0.13108973 -0.12597507 -0.4480728   0.03190854\n", "  0.09418988  0.23971884  0.1763835   0.08272775  0.01359398  0.24121822\n", " -0.304042   -0.05783929  0.20470291 -0.33536327 -0.3733953   0.17384817\n", " -0.33012962 -0.01525728 -0.03835648  0.05059854  0.2317351   0.05473083\n", " -0.29253325 -0.27821812  0.05093604  0.05279149  0.3205474   0.05592348\n", "  0.35959888 -0.00848673 -0.75681305  0.05002971  0.32525373 -0.21481812\n", "  0.03977856 -0.08960509 -0.06736063  0.4486347   0.14268357  0.01740344\n", "  0.41824412  0.16438727 -0.0948388  -0.04573472  0.52363616  0.24628724\n", "  0.3518207  -0.04889403 -0.10101118 -0.08248583 -0.01412654 -0.2675786\n", " -0.08640254  0.40484825  0.06958949 -0.0163503  -0.09526236 -0.38943943\n", " -0.11257734 -0.18040152  0.15751968  0.05518214  0.12871216  0.0676044\n", "  0.02841246 -0.06984201  0.14541107  0.15449494 -0.2715099  -0.07858375\n", " -0.08831798 -0.5686596   0.11179686  0.06151583  0.62109786  0.2261318\n", "  0.25881824  0.3298951   0.46241114 -0.3474246  -0.091006   -0.29342097\n", "  0.06796119  0.9189474   0.05728871 -0.13945594  0.30076876 -0.02424308\n", " -0.04346958 -0.61056155 -0.37477693  0.35081923  0.08448843 -0.23385899\n", " -0.2013125  -0.35771382  0.20734827  0.5026528  -0.12307347 -0.12063\n", " -0.17526482  0.46080807 -0.13686727  0.14876223  0.3541696  -0.147301\n", " -0.09704509 -0.03888969 -0.0324512  -0.5018156   0.03071503  0.5594521\n", "  0.41455635 -0.18101235  0.09718063 -0.3643957  -0.20616777  0.11054128\n", " -0.13337334  0.37173867 -0.01137181 -0.6187741  -0.02289008  0.2877618\n", "  0.0216828   0.0804544  -0.38640085  0.05813405 -0.16033287  0.00976302\n", " -0.00428785  0.17164278 -0.54816526 -0.45003343 -0.19957788  0.13479179\n", " -0.074345   -0.141686   -0.09570333  0.29391566  0.06792193 -0.18929665\n", " -0.43955722 -0.04992284 -0.01427226  0.25109693 -0.12303957 -0.03159784\n", "  0.18779325 -0.15937679  0.12718864 -0.06613653  0.1497479  -0.11221217\n", "  0.87082845 -0.07161967  0.207945   -0.19331135  0.12450663  0.02199439\n", " -0.20659618  0.15334249  0.06322309  0.06531787 -0.15451626 -0.00572078\n", " -0.37395144 -0.3697628   0.09083632 -0.01846419 -0.05021212  0.34558344\n", "  0.09177029 -0.27028406 -0.04788514 -0.10688827  0.07121807 -0.15162705\n", "  0.09233606 -0.08999991 -0.04620907  0.07708373  0.41357788 -0.04339367\n", "  0.2658394  -0.03123444 -0.16563423 -0.05528128 -0.32026336 -0.15563935\n", " -0.47263178 -0.25117865  0.11152033  0.03302994 -0.0435873   0.14702624\n", "  0.11031026  0.02989153  0.25202307  0.25913975 -0.08332172  0.13703473\n", " -0.36165366  0.00144987 -0.07376564  0.13378458  0.03748978  0.10026464\n", " -0.0526232   0.23649482 -0.25204208 -0.13112305  0.15270099  0.13191082\n", "  0.21389432 -0.34138837 -0.01145588  0.15924852  0.065829    0.20353764\n", " -0.23850429 -0.6289607  -0.04255277  0.13071631 -0.10320511 -0.24343933\n", " -0.0101575   0.495212    0.04811152 -0.2624816   0.23845744  0.42935887\n", " -0.00290463 -0.25829417 -0.05577669 -0.11551138 -0.15027457 -0.57713455\n", " -0.1389441   0.21642661  0.61496073 -0.02062663 -0.44431877  0.12558724\n", "  0.01642553  0.00971161  0.10244634  0.2593994   0.45667347  0.38375294\n", " -0.09792098 -0.00145595  0.13707139 -0.13067675  0.14760746 -0.30877692\n", "  0.2981552   0.10511562  0.14547898  0.16432174  0.11054059 -0.08480871\n", " -0.10421112 -0.10413987 -0.1573389   0.06919307  0.09708921  0.7196029\n", " -0.09304067  0.01050185  0.10612033  0.0803075  -0.18074457  0.05192309\n", " -0.01635353  0.21044624 -0.49729386 -0.16146003  0.17927788  0.08739501\n", "  0.14205222 -0.03179627 -0.63074803  0.15244873 -0.03003593  0.03267701\n", " -0.32170212  0.10798203  0.32727253  0.010137   -0.00629892 -0.17686331\n", " -0.53490835  0.6609333  -0.03025195 -0.08308632 -0.22757263 -0.68565136\n", " -0.09170749 -0.04083264 -0.34004846  0.1765777   0.17406355 -0.21817547\n", "  0.26114193  0.2353745  -0.22952335  0.03609668 -0.15657298  0.06637777\n", " -0.06659436  0.30703846  0.19096857 -0.006042    0.08049086  0.39053497\n", "  0.42577863 -0.12380602  0.6087422  -0.13564837 -0.30162832  0.08903811\n", "  0.10890009  0.14113925 -0.15143244  0.04853445  0.01452976  0.21235645\n", " -0.12724878 -0.20770413 -0.03884774 -0.02661083  0.16879411 -0.12514454\n", " -0.02546967 -0.18262012 -0.05913187  0.15497215 -0.0933871   0.0383888\n", "  0.48138657 -0.07772164  0.08606911  0.7269185   0.0234873   0.44500768\n", " -0.11393923 -0.14169995  0.03465773  0.2238551  -0.05334376  0.12343546\n", "  0.38339743 -0.27094245  0.01093796 -0.14340112  0.17762773 -0.10808446\n", " -0.32486954 -0.02960723 -0.04999433  0.04204023  0.4048793  -0.36206293\n", "  0.00511292 -0.02712639  0.37086475 -0.00579889  0.34742188 -0.01737498\n", " -0.20958805  0.07127148  0.06427362  0.1201817   0.15830423  0.46277156\n", " -0.17308037 -0.0534815   0.24282332 -0.2235835   0.39594555 -0.17343308\n", " -0.1930715  -0.0918351  -0.13993196  0.30330172 -0.06234607  0.25673833\n", " -0.24238057 -0.03150995 -0.23468013 -0.29968914  0.07237802  0.01594239\n", " -0.00296783 -0.11527465  0.12610476  0.09045685 -0.09658545  0.15707035\n", " -0.09947276 -0.18743987 -0.23670731 -0.31851408  0.13883488 -0.00838127\n", " -0.3056716  -0.12273419 -0.1409376  -0.00446086 -0.3423995   0.02159904\n", "  0.0052713   0.16635539  0.1371646   0.01221966 -0.15119958 -0.28593406\n", "  0.1228348   0.26601455 -0.043631    0.328668    0.2706565   0.40863967\n", "  0.08429155 -0.03095843  0.17827053  0.19506262  0.31711876  0.1033977\n", "  0.08699494  0.28558317 -0.41869807 -0.08031303  0.03345243  0.38053313\n", "  0.32696068 -0.1584155  -0.28905463  0.25194812  0.17411299 -0.18989141\n", " -0.14918925  0.35578513 -0.19816883  0.18838985 -0.07807645 -0.39388075\n", "  0.31942746 -0.2493689   0.00830528 -0.10637716 -0.02251615  0.08983681\n", "  0.1858759   0.30925873 -0.09477489  0.2532403  -0.05317459 -0.08661997\n", "  0.27023748  0.16237712  0.16615768  0.08810526  0.24230962 -0.0201176\n", "  0.21555686 -0.04511625 -0.05695157  0.13205521  0.23816855 -0.8784186\n", "  0.23911376  0.4716494  -0.04979043 -0.2532473   0.50335264 -0.17546149\n", "  0.0609825  -0.07619218 -0.4731779   0.3533777  -0.30171785 -0.06314047\n", " -0.26649368  0.10944445  0.12384504 -0.0979791   0.24730574 -0.09787244\n", " -0.03397463 -0.07810681  0.27784243  0.05931433 -0.439617   -0.02919743\n", "  0.35365602 -0.2757071  -0.28961188  0.03362826 -0.37680462  0.02411113\n", "  0.15524147 -0.19549309 -0.01953067  0.3798565  -0.05181422 -0.61179954\n", " -0.23797147 -0.0915096   0.06987713 -0.09380131  0.04851235  0.1926723\n", "  0.0212844  -0.02044378  0.11467722 -0.41102448  0.04740198 -0.15738669\n", " -0.15184234  0.33145523  0.1747948   0.09746649 -0.44637266 -0.08579556\n", "  0.11149848  0.11600866  0.12559426 -0.38466373  0.02945764 -0.2883894\n", " -0.2155339   0.06859124  0.07483184 -0.15771301  0.23605601 -0.01861016\n", "  0.32768062 -0.22040482  0.0477117   0.41143546  0.00141088  0.14120303\n", "  0.10294191 -0.01278328 -0.10655782 -0.32401007 -0.01414431  0.03964794\n", "  0.15636508 -0.2506568  -0.3596307   0.00866317  0.33646342 -0.07394421\n", " -0.05425258  0.15027604  0.11530928 -0.04479032 -0.11641898 -0.06732264\n", " -0.13375662 -0.17330612  0.08884119 -0.16739623 -0.22744964 -0.05996597\n", " -0.06338454 -0.21773903 -0.336975   -0.38983083  0.11697602 -0.3325689\n", "  0.5000604   0.00355426 -0.11615101  0.11668321  0.11313618 -0.19236732\n", "  0.15838777  0.19414383 -0.10895755  0.03261412  0.45586845  0.22387528\n", " -0.29267696  0.03908691  0.21098673 -0.09545066  0.04451442  0.3749065 ]\n", "Document 2 embedding: [ 3.65441918e-01  4.10456538e-01  2.04555374e-02  4.74734753e-02\n", "  1.51031137e-01 -9.90617946e-02 -1.27193868e-01 -2.20143840e-01\n", "  1.41393125e-01  1.51486889e-01  6.77310601e-02  1.57902673e-01\n", "  1.41753063e-01  4.31458324e-01 -1.41640678e-01 -2.35459805e-01\n", " -2.11967230e-01  3.98070663e-01 -8.69330540e-02 -3.89148235e-01\n", " -1.42641023e-01  2.75243193e-01  3.57259184e-01 -7.20716715e-01\n", "  4.09262419e-01 -1.09613568e-01  4.02320698e-02  1.10574327e-01\n", " -2.02977896e-01  4.04037148e-01 -2.26402283e-01  2.13998318e-01\n", "  3.85228079e-04 -4.04544957e-02 -1.58676311e-01  2.54247308e-01\n", "  1.14028931e-01 -4.95535396e-02  3.24577361e-01 -1.00432001e-01\n", "  2.56903768e-01  4.13934320e-01 -1.71749815e-01  2.43985727e-01\n", " -1.00617260e-01  5.05066924e-02 -4.89728212e-01  4.65484820e-02\n", "  2.74118688e-02  8.06381702e-02 -2.29409203e-01  6.13663234e-02\n", "  3.41901094e-01 -1.07678831e-01  1.11653946e-01 -5.18233590e-02\n", "  3.29367161e-01  1.41593710e-01 -1.85279176e-01 -1.16563819e-01\n", " -7.55024776e-02 -6.50719702e-02 -6.45603836e-02  1.67154238e-01\n", "  3.07257146e-01  1.20159112e-01 -2.65551478e-01 -5.39674222e-01\n", "  9.82385650e-02  1.35889560e-01  6.97304085e-02 -1.45822406e-01\n", "  5.81998490e-02  6.00554705e-01  1.97202656e-02  3.47727090e-01\n", " -2.25820735e-01 -8.07976052e-02 -5.86660504e-01  2.82446593e-01\n", " -5.72685264e-02  1.76048756e-01 -3.29957336e-01  7.25416318e-02\n", " -2.24216983e-01 -2.41705224e-01 -4.28353660e-02 -7.87989140e-01\n", " -6.15343601e-02 -4.30401601e-02 -2.32119992e-01  1.95323098e-02\n", " -2.95946449e-01  2.10399497e-02  2.05876634e-01 -2.09435284e-01\n", " -1.24618225e-01 -4.93079692e-01  7.82867894e-02  3.83117646e-02\n", "  2.96788096e-01 -4.13618125e-02  6.26854897e-01 -1.16348388e-02\n", "  2.06881091e-01  6.09469414e-01 -4.86197025e-01  1.07099868e-01\n", " -5.90338595e-02  2.33721092e-01  4.97523136e-02 -5.89799769e-02\n", "  1.03767598e+00 -9.21662226e-02  1.52682647e-01  1.40479267e-01\n", " -2.63124015e-02  1.93404853e-01 -3.87634151e-02 -1.08955465e-01\n", " -4.89647150e-01 -2.43708894e-01 -1.49643898e-01 -2.58228313e-02\n", " -8.66192043e-01  1.35908946e-02  7.46921599e-02 -4.63539124e-01\n", " -2.43784604e-03 -2.53916264e-01 -1.10001296e-01 -1.43825963e-01\n", "  9.56401005e-02 -1.84292987e-01 -2.63466209e-01 -1.86700091e-01\n", " -1.26739545e-02 -1.98634505e-01 -2.25995898e-01 -1.19598024e-01\n", " -2.23435745e-01 -3.91029380e-02 -1.82462018e-02  1.45346984e-01\n", " -3.12423706e-01  1.67524055e-01 -1.31783366e-01 -1.56624883e-01\n", "  1.41743228e-01 -1.01382576e-01  8.98875222e-02  3.49594019e-02\n", "  1.13826394e-01 -2.31945395e-01  4.07642931e-01  3.06010962e-01\n", "  5.30064106e-01 -1.09196663e-01 -1.01048030e-01 -1.27870888e-01\n", "  1.21438853e-01  1.94005743e-01  4.47824389e-01  1.97186455e-01\n", "  3.56394798e-01  1.37616068e-01  1.76590279e-01  7.91677237e-02\n", " -3.80666047e-01  9.27810296e-02 -4.37619120e-01 -4.09339905e-01\n", "  4.10491109e-01 -5.34354270e-01 -3.59759897e-01  1.76437125e-01\n", " -2.72833407e-01 -1.87561706e-01  8.59289169e-02  1.64013669e-01\n", "  2.62812227e-01  1.04633868e-01  7.20593333e-02  1.40465453e-01\n", " -1.82301905e-02  1.83680549e-01 -2.10537072e-02  4.71586853e-01\n", "  1.00157328e-01  1.91821352e-01 -3.01477522e-01  1.22709133e-01\n", " -3.39065224e-01  2.57659316e-01  3.44905220e-02 -1.89639270e-01\n", " -9.50723886e-02  9.18411091e-02 -5.33172727e-01 -7.38735199e-02\n", "  4.73843329e-02 -9.91621539e-02 -3.44562978e-01  1.70364991e-01\n", "  1.14685498e-01  2.04704210e-01 -5.80384433e-01  1.53788313e-01\n", " -6.43836483e-02 -9.10388902e-02  1.67839721e-01  3.18935096e-01\n", " -4.30166960e-01  1.29887477e-01  2.82628685e-01 -3.48518580e-01\n", "  2.15576634e-01  8.54966864e-02  5.67263924e-02  1.13703869e-02\n", "  2.46289432e-01 -1.76830348e-02  2.30499387e-01 -1.51277333e-01\n", "  7.22032964e-01 -1.80406615e-01 -1.17294483e-01 -1.14273064e-01\n", "  7.18398904e-03  5.59912920e-01  4.83285896e-02  3.86128783e-01\n", "  3.43952030e-01  1.19103454e-01  9.90343001e-03  1.07953511e-01\n", "  8.91370997e-02 -5.42886518e-02 -1.54764503e-01  1.73868045e-01\n", "  1.38176203e-01  9.66149196e-02 -3.64372246e-02 -7.17144459e-03\n", "  4.86579567e-01  2.22751394e-01  1.82577655e-01 -3.39215875e-01\n", "  2.80828625e-01 -5.59390426e-01  6.85733631e-02  1.19738333e-01\n", "  1.54326096e-01  1.98995367e-01  4.12704080e-01  2.66277313e-01\n", " -7.50432983e-02  1.04204036e-01 -3.59278172e-03  4.93489385e-01\n", "  9.86871943e-02 -3.31464171e-01 -2.97610193e-01  1.33915305e-01\n", " -1.88150361e-01  3.80469680e-01 -8.60841870e-02 -1.46863535e-01\n", " -1.35906041e-01 -5.17123163e-01  2.00770020e-01 -2.11196497e-01\n", "  1.14187598e-01  3.24331254e-01 -1.75499786e-02  1.82567928e-02\n", "  3.79002020e-02  1.64756645e-02 -3.76293212e-01  2.81579614e-01\n", "  6.80959448e-02  3.51855867e-02  3.46166998e-01  1.08962692e-01\n", "  1.45135656e-01 -9.74684134e-02  3.28326941e-01 -3.37713095e-03\n", "  4.24964190e-01  2.92906374e-01  1.24095716e-01 -4.00025696e-02\n", " -3.16992223e-01  6.43873662e-02  2.65983433e-01  4.86683935e-01\n", " -7.51490816e-02  4.75164037e-03  2.43013099e-01  1.80162728e-01\n", " -6.63782656e-01  6.61648437e-02 -4.18359429e-01  1.21143200e-01\n", " -1.11892186e-01  1.16944827e-01  4.99263138e-01  2.18020558e-01\n", " -5.89010380e-02 -3.06380600e-01  2.31537938e-01 -1.06585838e-01\n", "  4.39670950e-01 -4.01771702e-02 -1.52471969e-02  2.70138174e-01\n", "  2.92354971e-01 -2.46605035e-02 -5.17565757e-02 -3.68292093e-01\n", " -1.61248952e-01  1.83769628e-01  1.01273945e-02  2.62368713e-02\n", " -1.12172998e-01 -5.07591106e-02  5.82375228e-01 -4.71795052e-01\n", " -9.83169973e-02 -6.05032360e-03  1.30817160e-01 -4.99234162e-02\n", " -4.58797514e-02  2.79745702e-02 -9.30478200e-02  1.34278342e-01\n", "  5.37504591e-02  2.32647896e-01 -5.55275567e-02 -1.76637277e-01\n", "  2.13027298e-01  9.90175381e-02 -3.58552605e-01  1.83376789e-01\n", " -1.91496968e-01  1.09326221e-01 -3.49804491e-01  3.21454346e-01\n", "  1.96902081e-01 -1.27548710e-01 -2.97608823e-01 -7.72049427e-02\n", "  3.06473345e-01 -2.98386663e-01  3.34453106e-01  8.52244496e-02\n", "  2.20024288e-02 -5.78677170e-02  1.03413999e-01  3.98673534e-01\n", " -3.36603880e-01  5.53172715e-02  4.65598516e-02  4.85628061e-02\n", "  2.03923106e-01 -1.29976735e-01 -3.37754160e-01 -6.91793263e-01\n", " -7.88320303e-02 -1.61246717e-01 -2.15719268e-02  1.75945356e-01\n", "  2.18067184e-01  3.29032987e-01  1.68758273e-01  7.02530369e-02\n", " -8.12815949e-02 -4.77503799e-02  3.26929003e-01 -9.53463912e-02\n", "  1.23119205e-02  5.86808734e-02  3.11235607e-01  6.05370104e-02\n", " -8.52645282e-03  4.65980798e-01  7.64216363e-01 -4.43850040e-01\n", " -4.54193838e-02  2.21575070e-02  3.29049319e-01  1.37775555e-01\n", "  1.05163313e-01  2.81839054e-02  9.96745452e-02 -1.23219965e-02\n", "  4.51521993e-01 -5.69287725e-02  2.36310199e-01 -4.58643138e-02\n", " -1.75830290e-01  3.60476635e-02 -2.39766374e-01  1.59664482e-01\n", " -1.53509378e-01  9.59169492e-02 -4.66268033e-01 -5.25691211e-02\n", "  1.42641217e-01 -2.73873299e-01 -6.10287599e-02  4.37749892e-01\n", " -9.49112400e-02 -2.08667740e-01  4.73612905e-01 -1.07108094e-01\n", "  9.70694721e-02 -1.49395570e-01  3.41358152e-03 -1.68098047e-01\n", "  3.03494960e-01  5.70192933e-02 -1.07915819e-01  2.27199718e-02\n", "  3.23622018e-01  2.63471067e-01 -3.39580864e-01  3.32950979e-01\n", " -9.29429457e-02  1.95520952e-01  1.00855477e-01 -1.59494147e-01\n", " -3.41133565e-01  4.58754539e-01  2.43509367e-01 -2.53741771e-01\n", " -1.68775573e-01 -1.92347407e-01  1.91564243e-02  4.37090963e-01\n", "  2.02666342e-01  3.79772216e-01 -1.15678571e-01  2.94069856e-01\n", " -1.72381345e-02  1.87585190e-01 -3.44101191e-02 -3.09430305e-02\n", " -3.80205244e-01 -8.15542638e-02  3.73281360e-01  4.72013861e-01\n", " -4.44285721e-01 -5.06312065e-02  1.39522210e-01  1.77362919e-01\n", "  1.02345310e-01  2.60736734e-01 -1.60494760e-01  1.58499509e-01\n", " -8.63064360e-03 -2.61666566e-01 -2.23805904e-02  2.48761579e-01\n", " -6.06283583e-02  5.81608228e-02  3.96426708e-01  2.05501035e-01\n", " -6.31944478e-01  2.57822037e-01 -3.61365795e-01 -3.65062594e-01\n", " -3.00440133e-01  2.65246660e-01 -5.25987148e-02 -1.97790459e-01\n", "  3.54580097e-02  1.77511405e-02  1.89035401e-01  4.45108972e-02\n", "  2.81535149e-01  9.47472230e-02  2.90110052e-01 -8.32968652e-02\n", " -1.52720764e-04  1.23630412e-01  7.26474598e-02  2.78726727e-01\n", " -4.25039321e-01  2.14035138e-01  1.54093606e-02  2.45531395e-01\n", "  1.09163798e-01 -4.26372699e-02  2.61670440e-01 -2.07611933e-01\n", " -2.43886188e-01  6.54599816e-02  3.11544716e-01  3.51932734e-01\n", "  1.64761961e-01  3.41085106e-01 -1.03959940e-01  7.90366605e-02\n", " -3.21828835e-02 -8.68631825e-02  5.27848341e-02 -1.72304258e-01\n", "  3.22948210e-02 -2.90711280e-02  5.33481240e-02 -9.22102258e-02\n", "  3.63160342e-01  1.35736510e-01  9.97185633e-02  1.39850363e-01\n", "  1.98329926e-01  6.05109818e-02 -5.83892465e-02  8.55362639e-02\n", "  1.18411832e-01  4.66067791e-01  1.16724081e-01  4.49838825e-02\n", "  9.57865492e-02  2.32009932e-01 -2.07583711e-01 -1.98959157e-01\n", " -2.18312383e-01 -4.82696980e-01 -3.07463884e-01 -1.38975203e-01\n", " -1.06835753e-01  1.69990823e-01 -4.05767448e-02 -1.88225091e-01\n", "  2.43751958e-01 -3.53162616e-01  5.08826673e-01 -2.40598083e-01\n", "  2.19772458e-01 -2.03135774e-01  3.60513180e-02  4.37932499e-02\n", " -1.46743730e-01  1.77074984e-01 -6.57819152e-01  1.76013067e-01\n", " -2.76291538e-02 -1.12743601e-02 -6.34247065e-02  1.51093766e-01\n", "  1.30892560e-01 -1.77629068e-02  4.86068338e-01 -4.73789722e-01\n", "  1.52106628e-01 -2.55472008e-02  3.32683064e-02 -1.87107041e-01\n", "  2.02293530e-01  3.62572193e-01 -2.90185601e-01  2.40391269e-01\n", " -2.10584383e-02 -1.63267866e-01  1.35809556e-01 -1.22964077e-01\n", " -5.32830097e-02  3.38518061e-02  5.32586694e-01 -1.92581657e-02\n", "  5.20616882e-02 -6.09833188e-02 -4.17744160e-01 -2.42933750e-01\n", " -7.42872432e-02 -6.47154987e-01  2.37854242e-01  1.24091230e-01\n", "  6.59464538e-01  6.24046624e-02  2.71362334e-01  3.51968169e-01\n", "  4.18114103e-02  1.82260394e-01 -4.67207104e-01 -6.78439066e-02\n", "  1.75790146e-01 -1.56739995e-01 -9.05904397e-02 -2.73379773e-01\n", " -1.06691122e-01 -3.52589279e-01 -1.94399238e-01  2.16388419e-01\n", " -1.34443983e-01 -2.98803687e-01  3.25713724e-01 -1.14496104e-01\n", "  2.48216823e-01  4.20945883e-01  5.22564165e-02  1.76088333e-01\n", " -2.61485696e-01 -3.08226436e-01 -1.04353517e-01 -5.02865314e-02\n", "  7.35692009e-02  3.18659455e-01  2.21860841e-01  2.28363872e-01\n", "  2.56193489e-01  5.19551570e-03  7.32391700e-02  5.99084757e-02\n", "  2.45271623e-02 -9.59176645e-02 -2.85540611e-01  1.04387082e-01\n", "  1.67713597e-01 -1.43274352e-01 -4.03476506e-01 -2.24705502e-01\n", "  2.22262859e-01 -2.29707137e-01  1.59888119e-01  2.84516960e-02\n", " -5.70924699e-01 -1.75249740e-01 -2.10449234e-01 -1.48275688e-01\n", "  2.92464763e-01 -1.89080462e-01  1.29220560e-01 -1.93985537e-01\n", "  1.58409759e-01  2.83379704e-02  8.82356539e-02  1.60247400e-01\n", "  1.63195491e-01  1.46617278e-01 -7.48606697e-02 -1.92068979e-01\n", "  1.46041781e-01 -1.03161596e-01 -6.81683654e-03 -1.04820538e+00\n", " -2.51739413e-01  1.94998965e-01 -1.02204092e-01 -3.18589121e-01\n", "  3.78619552e-01 -4.20744658e-01  1.24672778e-01  3.08479726e-01\n", " -3.81624959e-02  1.17191873e-01  2.18102023e-01 -3.64836782e-01\n", " -2.03244492e-01  3.82304907e-01  9.24268141e-02  3.28776777e-01\n", " -1.59897953e-01  1.95552230e-01  5.30247390e-02  3.63940388e-01\n", "  3.63074355e-02  1.95903331e-02  2.60249991e-02  7.77837262e-02\n", "  2.64240116e-01 -1.51932418e-01 -3.93497348e-01  7.98477381e-02\n", "  2.37018038e-02  1.28154814e-01 -7.80084133e-02  1.25430832e-02\n", " -1.62659392e-01  9.22574401e-02  4.13278967e-01  4.96449322e-03\n", " -2.54573733e-01 -1.86245561e-01  3.18789780e-02 -3.17236185e-01\n", " -1.20193632e-02 -1.31386653e-01 -2.88018823e-01  2.85886019e-01\n", "  3.03514332e-01 -4.82135601e-02 -6.07277751e-01 -3.89433891e-01\n", " -3.81786734e-01  6.79151192e-02  2.33708066e-03 -6.67079926e-01\n", " -2.67223984e-01 -2.07003355e-01 -2.66223043e-01 -1.48188859e-01\n", " -7.92017803e-02 -5.77064790e-02 -1.70921788e-01 -1.41301945e-01\n", " -1.01207353e-01 -9.66955647e-02  1.85397863e-01  3.19202505e-02\n", " -2.37393141e-01 -1.57302037e-01 -9.27280709e-02 -2.46960402e-01\n", "  9.76103768e-02  1.99332371e-01  5.51764257e-02  1.43969446e-01\n", "  4.33225594e-02 -2.27606103e-01  2.42823169e-01  1.36675432e-01\n", "  5.38264848e-02 -1.18687272e-01  1.20186605e-01 -8.33743438e-02\n", " -2.21380159e-01  7.85494968e-02  1.68046858e-02  6.83362782e-02\n", "  2.27219537e-01  1.10640250e-01  8.51735175e-02  6.93826899e-02\n", "  9.45179686e-02  9.28881988e-02  4.42503303e-01 -4.44143498e-03\n", " -2.12151244e-01  2.91632023e-02  1.21721663e-01 -2.13112831e-01\n", "  5.35797291e-02  6.01086318e-02  5.81652112e-02 -1.86468493e-02\n", "  1.65653989e-01 -3.38823885e-01  1.99594513e-01 -2.27737129e-01\n", " -3.01025301e-01  1.01903200e-01 -7.88033083e-02  2.90998280e-01\n", "  1.28241384e-03  2.26296827e-01  2.96411902e-01 -3.10052365e-01\n", "  6.63461760e-02 -2.99862534e-01  2.19866320e-01  1.03975259e-01\n", " -3.67603153e-01  1.97539076e-01  2.28881702e-01  1.11587606e-01]\n", "Document 3 embedding: [ 2.87327826e-01  1.52029574e-01  2.43118659e-01  1.11790247e-01\n", "  2.37715825e-01  1.49395615e-01 -1.53982595e-01  1.02339275e-01\n", "  5.05681455e-01  1.11720137e-01 -3.69367078e-02 -1.99609622e-01\n", "  1.28696531e-01  3.23933244e-01  5.20582974e-01 -4.28138733e-01\n", " -8.89092758e-02 -1.73980892e-01  4.95384961e-01  5.42434752e-01\n", " -3.17073911e-01 -1.15340285e-01 -2.76579738e-01 -2.97300994e-01\n", "  4.02062863e-01 -3.49951833e-02 -1.15729712e-01  1.74154311e-01\n", " -5.50222397e-01  2.54321486e-01  1.58055678e-01  1.43850803e-01\n", " -1.68644115e-01  1.20240927e-01 -3.89419168e-01  1.63809136e-02\n", "  2.80473590e-01 -3.04261982e-01 -9.47216004e-02  1.88551560e-01\n", "  1.88392535e-01  5.33928990e-01 -2.79888481e-01  3.32850873e-01\n", " -4.83155847e-02 -1.24387229e-02 -3.92576724e-01 -4.50872213e-01\n", " -1.24052331e-01  2.84411758e-01 -1.34083042e-02  2.12254584e-01\n", "  4.87395972e-01  2.08545506e-01 -1.21426344e-01  1.67043611e-01\n", "  2.29054794e-01 -3.11424793e-03 -5.01388423e-02  2.43952528e-01\n", "  1.01312540e-01  5.10771036e-01 -2.93320697e-02 -9.69209746e-02\n", "  1.14339091e-01 -1.29430378e-02  4.72600795e-02 -4.09001298e-02\n", "  1.56282336e-01  5.97500764e-02 -2.23579362e-01  3.69729251e-01\n", "  2.51468569e-01  3.37006241e-01  1.79759994e-01  4.74119782e-01\n", "  4.13583398e-01 -4.68520164e-01 -2.73677737e-01  4.57687855e-01\n", "  8.71500745e-02  2.50028998e-01 -2.95129597e-01  5.72402030e-02\n", " -1.57422632e-01 -1.60521325e-02  2.63424534e-02 -6.22440279e-01\n", "  8.23012069e-02 -1.35783568e-01  9.65220854e-02  2.12550551e-01\n", " -1.78998783e-01 -9.90440398e-02  1.75177231e-01 -2.41972268e-01\n", " -2.75591295e-02 -6.65569425e-01  4.14337078e-03 -7.85264075e-02\n", "  2.62128979e-01  1.17243290e-01  5.43506444e-01 -1.55446678e-01\n", "  5.63672883e-03  4.93557096e-01 -2.80458987e-01  1.73417255e-02\n", "  8.95673260e-02  1.76933244e-01  1.15852267e-01 -1.63192272e-01\n", "  6.92721784e-01  1.25746755e-02  2.32517779e-01 -6.63849041e-02\n", "  3.17709416e-01  1.37362659e-01 -1.17861181e-01  1.28499344e-01\n", " -2.48843387e-01  1.29897550e-01 -9.45883244e-02  3.39497596e-01\n", " -7.14684725e-01 -7.79367238e-03  8.56582820e-03 -2.58982778e-01\n", "  2.82262534e-01  3.03655028e-01 -2.30655889e-03  2.96117753e-01\n", "  3.16706821e-02 -4.63273734e-01  1.61367282e-01  2.20434010e-01\n", "  4.86030757e-01  9.93651431e-03 -1.96402892e-02  7.49637187e-02\n", "  1.96411926e-03  1.00523710e-01 -1.04638850e-02  5.45809492e-02\n", "  1.64091483e-01 -4.18972448e-02  2.42616124e-02 -4.65500318e-02\n", " -1.95992932e-01  3.83813865e-02  3.78891826e-01 -1.64141700e-01\n", "  1.92049015e-02 -2.32169643e-01  1.72714949e-01  4.41872001e-01\n", " -1.85888857e-01 -3.37659389e-01  3.06826402e-02 -1.16157860e-01\n", "  2.49266088e-01  3.01592708e-01  4.96026546e-01  2.37002715e-01\n", "  4.74219233e-01 -1.27546266e-01  2.42181584e-01  6.82183206e-01\n", "  6.16097338e-02  1.24182366e-01  3.61806303e-02 -1.71769619e-01\n", " -5.69887608e-02 -2.56025970e-01 -2.19661012e-01  2.73721993e-01\n", "  2.05017179e-01 -5.32418191e-01 -1.19979888e-01  1.75538197e-01\n", " -8.57862532e-02  3.58072966e-02  1.02512956e-01  1.68467000e-01\n", "  1.05423719e-01  8.22950378e-02 -3.73831719e-01 -3.20758671e-02\n", "  2.24322855e-01 -2.31877387e-01 -1.20759187e-02  1.79475337e-01\n", " -1.91780001e-01  9.46062803e-02 -9.25219730e-02 -7.23775625e-02\n", "  1.10094301e-01  2.22259179e-01 -1.41002849e-01 -1.40562117e-01\n", "  1.99272111e-01  1.71368629e-01 -4.10100162e-01  1.53901473e-01\n", " -9.81996283e-02 -6.67988509e-02 -3.57632458e-01  2.30451256e-01\n", "  1.95547536e-01 -4.12799060e-01 -8.78733620e-02 -2.56610569e-02\n", " -3.10839444e-01  2.97663242e-01  2.51442939e-01 -2.48060808e-01\n", " -2.27352768e-01 -7.46054128e-02  2.58832294e-02 -2.14568544e-02\n", "  2.38456026e-01 -9.96314287e-02  4.51655865e-01  5.51000983e-02\n", "  4.44870681e-01 -1.17799871e-01 -2.35920250e-01 -3.55567969e-02\n", " -5.89654921e-03  5.34556866e-01 -3.91407348e-02 -8.53316393e-03\n", "  2.49531135e-01 -1.72533050e-01  2.13564917e-01 -2.63717901e-02\n", "  4.56607431e-01 -1.15073510e-01 -2.20802605e-01  4.71236221e-02\n", "  6.87346697e-01  5.24052829e-02  3.41994725e-02 -1.02733009e-01\n", "  1.66255519e-01 -3.90064687e-01 -2.93040220e-02 -2.81116724e-01\n", "  2.23740056e-01 -6.10505044e-01 -3.37201245e-02  2.72647858e-01\n", "  2.52201587e-01  2.88820952e-01  6.81586981e-01 -4.73075546e-02\n", " -4.08499874e-02 -8.14911127e-02 -9.67550091e-03  9.17977571e-01\n", "  3.05527657e-01 -4.62372959e-01 -2.51199380e-02  2.75544897e-02\n", " -1.98079929e-01  4.74427134e-01  1.03816621e-01  2.30119694e-02\n", "  1.06516510e-01 -4.26829994e-01 -4.89398129e-02 -1.27200514e-01\n", "  2.68907964e-01  1.49186909e-01  6.66034222e-02 -4.78408709e-02\n", " -4.54995334e-01  3.72447282e-01 -4.80125099e-02  3.21401566e-01\n", "  2.45798767e-01 -3.54116529e-01 -1.90953061e-01 -1.13740012e-01\n", "  8.57663825e-02 -2.29219094e-01  2.16782764e-01  6.73260987e-02\n", "  2.62158662e-01  5.30156232e-02  1.95330232e-01 -2.00720355e-01\n", " -3.67912412e-01  5.54425940e-02 -1.05279915e-01  3.84837538e-01\n", " -5.16588837e-02 -3.99665654e-01  3.33127603e-02 -5.28944954e-02\n", " -2.25384384e-01  4.95149754e-02 -5.23654401e-01  3.12769860e-01\n", "  6.72394037e-02  4.11043078e-01  6.23453379e-01  4.36681733e-02\n", " -2.30290025e-01 -8.98838267e-02 -9.57025364e-02  9.35897157e-02\n", "  1.68519855e-01  7.40610883e-02 -2.31552035e-01  3.56662959e-01\n", " -7.40739033e-02  1.79593801e-01 -1.31932735e-01 -2.33184814e-01\n", " -2.21826121e-01  1.67435154e-01 -8.24831203e-02  1.56225979e-01\n", "  5.02360314e-02  1.44198999e-01  8.42468068e-02 -4.70603257e-01\n", "  1.14024498e-01  1.08863167e-01  5.84291875e-01 -2.13416442e-01\n", "  2.07773829e-03 -1.82617292e-01  3.58286649e-02 -8.51107538e-02\n", " -1.74651459e-01  9.74158719e-02 -6.81807771e-02 -1.86662842e-02\n", "  2.84161389e-01 -5.32463454e-02 -5.25050700e-01  5.51663525e-02\n", " -2.56430507e-01 -3.31152141e-01 -1.13297114e-03  1.33170977e-01\n", " -1.00152548e-02 -4.30729181e-01  3.23790535e-02  2.87849307e-01\n", "  2.81931609e-01 -4.20051306e-01 -1.07336424e-01 -2.16123387e-02\n", " -1.90417200e-01  1.05657429e-01  3.41097087e-01  4.02150422e-01\n", "  4.10731100e-02  1.77964028e-02 -1.59084737e-01  1.00548416e-01\n", "  1.55530736e-01 -4.17682856e-01 -8.35056007e-02 -6.26979411e-01\n", "  2.21945658e-01 -1.27302364e-01 -5.06724231e-02  7.50382543e-02\n", "  1.81756437e-01  3.88141014e-02  5.56956567e-02  9.93239209e-02\n", "  2.93347925e-01 -3.50608071e-03  7.71878660e-02 -1.75727859e-01\n", "  1.56021178e-01 -1.28166556e-01 -1.22115828e-01 -4.97306362e-02\n", "  9.30827782e-02  3.51635814e-01  3.46157461e-01  3.55623923e-02\n", " -1.96277462e-02  1.30690873e-01  8.35458860e-02  1.17343709e-01\n", "  2.21457463e-02  3.19088280e-01  1.64899126e-01  2.58431374e-03\n", "  3.42082709e-01 -7.77331114e-01 -4.86180075e-02  1.12102311e-02\n", "  8.31342191e-02 -6.96902797e-02 -1.43988594e-01 -2.17679981e-02\n", " -1.71762090e-02 -1.38983622e-01  1.91149101e-01 -1.53107181e-01\n", " -3.34261388e-01  1.29050106e-01 -6.15019910e-02  4.83966649e-01\n", " -3.71661156e-01 -2.71718472e-01  2.02001613e-02 -2.68692076e-01\n", "  9.70299125e-01 -3.27194482e-02  2.24645004e-01  1.59385189e-01\n", "  2.85668969e-01  1.27536193e-01  2.38262877e-01 -8.72275382e-02\n", " -2.38810014e-03 -2.06179233e-04 -3.10885400e-01  2.01961264e-01\n", " -2.90788203e-01 -8.80199373e-02  1.25650018e-01 -2.36029491e-01\n", "  2.45772123e-01  1.48479134e-01  8.12297761e-02 -1.09913975e-01\n", "  1.33727223e-01 -2.44055644e-01 -1.07839614e-01  4.14398760e-02\n", "  8.55044201e-02  1.63333684e-01  5.25178947e-02  2.91656226e-01\n", " -1.12478040e-01 -1.31923035e-01  6.14683852e-02 -1.63787499e-01\n", " -2.75705844e-01 -8.45509022e-02 -7.66048953e-02  3.66834551e-01\n", " -4.35055420e-02  3.90274554e-01  1.92326069e-01  1.54914424e-01\n", "  3.06356966e-01  1.85407504e-01 -3.38636100e-01  1.48044690e-01\n", "  3.98849621e-02  1.50887640e-02 -1.66909352e-01  1.31289801e-02\n", "  7.06709698e-02  1.31629363e-01  1.40011087e-01 -2.11546600e-01\n", " -1.31043904e-02  2.64326125e-01  3.72858495e-02 -2.30542809e-01\n", " -1.61541402e-01 -1.10689834e-01 -3.16513211e-01 -6.78914087e-03\n", " -2.57335782e-01 -3.40765089e-01  1.85501441e-01 -1.75311565e-01\n", "  1.61450673e-02  1.68859497e-01 -1.15316818e-02 -1.95455059e-01\n", " -1.51082218e-01 -8.04962218e-02 -9.23845768e-02  4.25662808e-02\n", " -2.12475345e-01  2.65182793e-01 -4.02219862e-01  3.58416051e-01\n", "  3.88428688e-01  1.06774352e-01 -1.12997249e-01  2.63787299e-01\n", " -2.26294845e-01  1.57657847e-01 -3.31461802e-02  1.08647279e-01\n", "  8.55675638e-02  1.55392975e-01 -7.06457570e-02  4.78108019e-01\n", "  1.32614290e-02 -1.40932381e-01 -1.50404898e-02 -3.34157497e-02\n", " -1.34023294e-01  1.70299634e-01 -1.99628919e-01 -1.03048973e-01\n", "  2.25337639e-01  1.31275982e-01  2.25178152e-01 -1.10836647e-01\n", " -1.20282313e-03  3.35705554e-04 -5.57689704e-02 -2.23948866e-01\n", "  7.09248707e-02  3.18155050e-01  2.22689375e-01 -5.28043660e-04\n", "  2.08889112e-01  2.38536924e-01 -2.35177025e-01 -9.90035608e-02\n", "  7.22998828e-02 -4.21409726e-01 -5.70841469e-02 -7.96562880e-02\n", " -2.66612053e-01 -6.00856654e-02  1.70078631e-02 -2.58044273e-01\n", "  1.28563330e-01 -2.61763752e-01  1.20760269e-01 -3.63284200e-01\n", "  5.27324438e-01  3.72540839e-02  3.61869931e-01  1.90504715e-02\n", "  8.01306665e-02 -1.48781454e-02 -8.20562959e-01  3.01240802e-01\n", " -8.31921548e-02 -3.26622613e-02 -4.41841222e-03  4.96483237e-01\n", "  6.93900064e-02 -2.87650656e-02  3.75316709e-01 -2.87620097e-01\n", "  1.68265954e-01  1.63884893e-01 -3.95078473e-02 -1.13023587e-01\n", "  5.37848622e-02  3.91490728e-01  1.36616141e-01  4.48294990e-02\n", " -5.00643015e-01 -5.80541119e-02 -2.43780464e-01 -1.81841508e-01\n", " -6.35903776e-02  1.60445109e-01 -1.10952016e-02  9.53916088e-02\n", "  4.80659232e-02  1.87926710e-01 -3.23716670e-01 -2.10306272e-01\n", " -2.17327960e-02 -3.80409688e-01 -1.57979622e-01 -2.22982779e-01\n", "  1.63218245e-01 -2.63039231e-01  3.90135735e-01 -1.72957316e-01\n", " -1.45397678e-01 -1.51215374e-01 -5.14592946e-01 -1.62693873e-01\n", "  1.38399929e-01 -1.79213852e-01 -8.15674365e-02  1.45338085e-02\n", " -2.58234859e-01 -1.78026736e-01 -2.59206891e-02  1.85323656e-01\n", "  4.29845937e-02 -3.78430754e-01  1.64754301e-01 -1.11943811e-01\n", "  1.55424014e-01  3.06754172e-01  1.58021748e-01  2.75048912e-01\n", " -3.46151441e-01 -3.79307240e-01  2.40417004e-01 -2.39006191e-01\n", " -3.12840082e-02  3.29348862e-01  1.95570320e-01  1.27706274e-01\n", "  3.33660126e-01 -2.49963298e-01 -3.19594920e-01  6.29757270e-02\n", "  2.53027648e-01  1.10614754e-01 -9.30570662e-02  4.26734567e-01\n", "  2.89560795e-01 -3.21655013e-02 -1.86080784e-01 -5.93827486e-01\n", "  4.02115524e-01 -2.04081953e-01  3.12366754e-01 -7.67800733e-02\n", " -1.53791934e-01  1.61847845e-01  1.58081234e-01 -2.51752348e-03\n", "  2.67093092e-01  3.74689698e-05  2.59549052e-01 -1.71089113e-01\n", "  1.93670973e-01 -1.74964499e-02 -8.25840235e-03  2.98996996e-02\n", "  3.42054963e-01 -3.84940445e-01 -5.98334335e-02 -2.95805752e-01\n", "  1.04634523e-01 -1.84899852e-01 -9.22112912e-02 -5.50446868e-01\n", "  1.99565932e-01  3.83429527e-01 -2.43305832e-01 -3.26102823e-01\n", "  1.13774702e-01 -3.38365287e-01  2.45974977e-02  3.60562116e-01\n", " -5.55730641e-01  2.38807544e-01 -1.14534229e-01 -1.57784998e-01\n", "  9.04398318e-03  1.02193162e-01 -6.65840954e-02  4.05728340e-01\n", "  1.08524702e-01 -1.42017186e-01  3.35154012e-02 -3.43512744e-02\n", "  3.30955297e-01 -1.29608095e-01 -1.55434802e-01  1.98658079e-01\n", "  5.43236583e-02 -3.48170638e-01 -7.35391200e-01  5.01000620e-02\n", " -9.55183432e-02 -9.11764428e-02 -8.76795575e-02  6.09546676e-02\n", "  7.66349910e-03  1.68832466e-01  3.40134919e-01 -2.36108601e-01\n", "  7.73198903e-02 -6.18237033e-02 -3.13627273e-02 -2.18803450e-01\n", "  1.13409139e-01  9.77269709e-02 -2.69807637e-01  4.62391376e-01\n", "  2.42668852e-01 -4.59510624e-01 -1.01583101e-01 -3.92648429e-01\n", "  1.40136003e-01 -5.35718761e-02 -7.93533921e-02 -2.33505949e-01\n", " -2.60240436e-01  8.83458648e-03  2.02755496e-01 -1.74799860e-01\n", " -9.36383530e-02 -4.98439036e-02 -9.18221399e-02  1.83586732e-01\n", " -1.82651460e-01 -2.58853197e-01 -7.29235187e-02 -1.69090837e-01\n", " -1.00149535e-01 -4.99799810e-02 -9.27526653e-02 -1.86793178e-01\n", "  1.30927458e-01  4.15787488e-01  3.00071061e-01  6.00904189e-02\n", " -7.98628852e-02  6.83158711e-02 -2.13746354e-01 -2.17736334e-01\n", " -2.24921316e-01  7.84634706e-03 -1.18628003e-01 -9.63579118e-02\n", " -9.60394070e-02  8.72734189e-02  2.69345164e-01  3.55701149e-01\n", "  7.89234117e-02  1.64363861e-01  3.62662226e-02  3.91085804e-01\n", "  2.41245907e-02  1.54735998e-03 -3.53151113e-02  4.93622459e-02\n", "  1.85351651e-02 -6.28685430e-02 -7.04622790e-02 -1.78255901e-01\n", " -4.71795164e-02  1.71572894e-01 -4.00845110e-02 -1.65597811e-01\n", "  1.43786609e-01 -3.86617303e-01  1.85586080e-01 -3.37835371e-01\n", " -1.62884489e-01  1.91279262e-01  1.63325220e-01  1.80167869e-01\n", "  1.07890479e-01  1.12580601e-02  4.28638637e-01 -3.35226893e-01\n", "  1.95826039e-01  1.98740542e-01  9.55591127e-02  2.87223369e-01\n", " -5.38639605e-01  1.65692881e-01  3.13298434e-01  1.05953261e-01]\n", "Document 4 embedding: [ 5.17974421e-02  3.42111588e-01  4.11053374e-03  2.14854464e-01\n", " -1.83414668e-02  1.47757381e-02 -1.21146247e-01 -1.13875538e-01\n", "  1.95454627e-01 -1.86651558e-01  7.95193315e-02  1.07508391e-01\n", "  2.16933668e-01  4.31949198e-01 -1.79760486e-01  1.86223239e-01\n", " -2.15101376e-01  1.76641569e-02  7.20385835e-02 -3.30607295e-02\n", " -9.48428139e-02  1.34033322e-01 -4.56543453e-02 -5.71063638e-01\n", "  3.01211029e-01 -2.02855110e-01  1.18633233e-01  5.16667701e-02\n", "  3.15839872e-02  2.80138731e-01  1.89030915e-02  8.41787308e-02\n", " -1.28155900e-03  3.98546793e-02 -2.97422111e-01  8.21069628e-02\n", "  1.63065493e-01 -3.33382748e-04  1.04398131e-01  1.61664449e-02\n", "  3.55404496e-01  4.08575416e-01 -1.09677285e-01  2.12166727e-01\n", "  7.16030151e-02 -2.90518366e-02 -4.08048809e-01 -5.55360839e-02\n", "  2.56086998e-02  2.22381428e-01  7.79278427e-02  2.44460881e-01\n", "  3.39367330e-01  1.11816362e-01  1.24103934e-01 -2.27653291e-02\n", "  6.44973293e-02  1.92096382e-02 -2.67126322e-01 -3.24761979e-02\n", " -1.24410786e-01 -1.94727868e-01  2.77065486e-01  2.50752449e-01\n", "  5.68140745e-02  5.36914431e-02 -2.79290546e-02 -4.16739643e-01\n", " -2.10949391e-01  6.50027320e-02 -9.38431770e-02 -2.67318964e-01\n", "  2.56356150e-02  3.18895459e-01 -1.18459977e-01  2.06010386e-01\n", "  1.17299661e-01 -2.26841316e-01 -2.28566468e-01  3.21104169e-01\n", "  2.06968114e-01  2.24802017e-01 -3.80078316e-01  1.41084433e-01\n", " -2.09429532e-01 -2.75197923e-01  2.92537883e-02 -6.75395191e-01\n", " -1.15113985e-03 -1.74508113e-02 -3.11152816e-01  1.55243635e-01\n", " -2.41207525e-01 -8.98672938e-02  1.49514768e-02 -1.16768796e-02\n", " -2.30117947e-01 -6.80385351e-01  1.56225324e-01 -4.21040356e-01\n", " -1.87053345e-03  2.49884091e-02  7.23686874e-01  4.06138450e-02\n", "  1.45614058e-01  3.23485315e-01 -4.33161527e-01  2.69693315e-01\n", " -5.58910519e-02  1.68540880e-01  7.03367069e-02 -3.78716104e-02\n", "  8.78172278e-01 -2.23980144e-01 -7.51779824e-02 -1.97514758e-01\n", "  6.06281161e-02  1.90380946e-01 -3.04183476e-02 -1.03095025e-01\n", " -3.12016666e-01 -1.56019554e-01 -4.45846230e-01 -1.31538779e-01\n", " -7.59999275e-01  2.89788768e-02  6.67198226e-02 -2.48189420e-01\n", " -2.28642002e-01 -2.40913078e-01  3.62112969e-02 -2.06552058e-01\n", "  1.49540659e-02 -8.77401680e-02  8.01501572e-02 -1.24053799e-01\n", "  6.49465993e-02  1.34535939e-01 -8.08842033e-02 -1.15727164e-01\n", " -2.23730668e-01 -1.54318005e-01 -8.84764493e-02  8.69672447e-02\n", " -3.63449812e-01  1.50474101e-01 -2.64917791e-01 -1.29425600e-01\n", "  2.02272356e-01  4.29085642e-02  1.51612148e-01  2.84009203e-02\n", "  2.79348910e-01 -1.86014920e-04  4.12090659e-01  2.31328130e-01\n", "  2.31941685e-01 -3.02732080e-01 -1.90167040e-01 -6.99978322e-02\n", "  1.92591459e-01 -2.89745070e-02  4.08131391e-01  2.44564429e-01\n", "  2.44157672e-01  1.45511612e-01  3.98627698e-01 -7.88102448e-02\n", " -3.35060526e-04  2.38945514e-01 -4.40160632e-01 -1.71845198e-01\n", "  2.91512430e-01 -5.56763411e-01 -7.69730285e-02  2.19439626e-01\n", " -3.40732336e-01 -3.91929775e-01  2.07983911e-01  2.94924229e-01\n", "  2.29173705e-01  1.18265867e-01  3.48943859e-01  3.98792207e-01\n", "  1.20557193e-03  2.59866387e-01 -2.45255038e-01  5.44989824e-01\n", "  2.82761276e-01  3.32572192e-01 -2.92041063e-01  1.40076786e-01\n", " -2.83504516e-01  5.79844676e-02  1.02072880e-01 -3.26469660e-01\n", " -1.42231649e-02  1.95477456e-01 -2.10808724e-01 -1.95312187e-01\n", " -1.52701065e-01 -2.63296038e-01 -1.35116458e-01  4.21986207e-02\n", " -2.53680386e-02  1.81182340e-01 -5.77468514e-01 -2.53625177e-02\n", " -6.64323196e-02 -1.95959315e-01  5.00523858e-02  2.80498028e-01\n", " -3.83068144e-01  1.25446364e-01 -3.19841921e-01 -1.28242135e-01\n", "  1.40118182e-01  1.07557535e-01 -3.11658811e-02  1.31472036e-01\n", "  2.45265812e-01 -7.36772493e-02  2.74184406e-01 -3.13549429e-01\n", "  5.56080401e-01 -4.19416577e-01  1.05321957e-02 -1.47802711e-01\n", " -2.73192637e-02  5.43244362e-01  7.71595016e-02  3.62178445e-01\n", "  3.15009475e-01 -1.30081758e-01  3.67509527e-03 -1.35150433e-01\n", "  1.73991770e-01 -2.15631247e-01 -8.80339146e-02 -3.14852223e-02\n", "  4.20938611e-01  1.64327577e-01  2.51093924e-01  5.11674955e-02\n", "  4.97473896e-01  1.93201415e-02  2.28252318e-02 -4.61833358e-01\n", "  2.33347282e-01 -1.92731708e-01 -8.27560201e-02  9.30490047e-02\n", "  2.53379047e-01  8.00156221e-02  3.69196951e-01 -5.19312136e-02\n", "  1.31823584e-01  3.31487358e-02  2.05126256e-01 -5.36207855e-03\n", "  1.28209010e-01 -1.97933644e-01 -2.45045293e-02  3.05814922e-01\n", " -1.14702962e-01  1.16890907e-01  8.64569545e-02  8.73653889e-02\n", "  9.25911777e-03 -6.14655435e-01  1.36961818e-01 -2.11979330e-01\n", "  3.28370810e-01  2.71926105e-01 -1.26564443e-01  1.40525214e-02\n", "  1.02709949e-01  1.43475503e-01 -3.01829487e-01  1.90584928e-01\n", "  1.48642942e-01 -1.64610833e-01  2.07834169e-01  1.17542833e-01\n", "  1.87838867e-01  7.08625093e-03  3.42691183e-01  3.11358515e-02\n", "  3.21342707e-01  2.02104330e-01  6.61229342e-02 -1.32282227e-01\n", " -2.42550969e-01 -2.14050010e-01  2.03818992e-01  5.54889739e-01\n", "  7.41289333e-02 -1.18160240e-01  1.32655159e-01  1.36656195e-01\n", " -4.94924545e-01  6.55469447e-02 -2.94620931e-01  2.98201978e-01\n", " -2.40204111e-02 -7.75747001e-02  4.98233110e-01  2.06385851e-01\n", "  1.46048516e-01 -2.52722979e-01  2.01623272e-02 -2.82455564e-01\n", "  3.26292634e-01  7.65765309e-02  1.50797674e-02  4.88682210e-01\n", "  9.54382420e-02 -1.27124652e-01 -1.16206668e-02 -3.10517162e-01\n", " -4.20419425e-02  1.15425743e-01  2.59363294e-01 -5.90306856e-02\n", "  2.34921932e-01  1.32649168e-01  7.36991405e-01 -2.95262694e-01\n", "  4.68793288e-02 -1.56067491e-01  1.55173093e-01  1.14361405e-01\n", " -5.67281358e-02 -1.36984393e-01 -1.90994233e-01  8.77743065e-02\n", " -9.21657681e-03 -4.32329103e-02 -5.48875192e-03 -4.38539594e-01\n", "  3.47927153e-01 -7.72188455e-02 -6.06081665e-01  2.35903375e-02\n", " -3.22301775e-01  2.34938636e-01 -2.15266526e-01  9.38267037e-02\n", "  1.58901706e-01 -3.94033417e-02 -1.13630950e-01  5.05194589e-02\n", "  2.58626491e-01 -4.47935127e-02  2.55557775e-01 -9.72773507e-02\n", " -3.26424353e-02 -6.29649758e-02  2.07706869e-01  3.41016561e-01\n", " -2.21544579e-01  1.64436996e-01  1.16811562e-02 -2.19054192e-01\n", "  2.02305704e-01  5.08230478e-02 -7.58541971e-02 -4.63093132e-01\n", " -6.48021027e-02 -1.32237405e-01  1.00147247e-01  3.10963035e-01\n", "  3.06037426e-01  1.32121295e-01  3.16497535e-01  3.24510217e-01\n", " -2.50729918e-01 -2.05534250e-01  1.46264613e-01 -6.04052097e-02\n", " -9.61214527e-02  1.42383873e-01  2.58733593e-02  1.41153604e-01\n", " -1.16662562e-01  2.77234256e-01  4.71707463e-01 -3.12876940e-01\n", " -4.16455925e-01 -4.09419425e-02  4.21589911e-01  2.61631608e-02\n", " -3.25293154e-01  4.54539061e-02  1.39844626e-01 -1.09806638e-02\n", "  4.73444074e-01 -1.03882641e-01  1.50554091e-01  1.86759338e-01\n", " -2.39288941e-01 -1.31476551e-01 -3.82934928e-01  5.92294097e-01\n", " -3.01391542e-01  1.83228195e-01 -4.80670899e-01 -1.60242885e-01\n", "  1.32502630e-01 -1.54502958e-01  7.77277574e-02 -3.66861001e-02\n", " -2.44992375e-01 -3.28697205e-01  2.46491224e-01 -1.49726598e-02\n", "  2.06816629e-01 -3.90531346e-02  2.04527617e-01 -2.24530399e-01\n", "  2.92883396e-01  1.65639408e-02 -3.42501812e-02  1.57440081e-01\n", "  2.62817681e-01  3.04408193e-01 -3.05419773e-01  3.59297931e-01\n", "  2.80396454e-03  2.49132872e-01  3.59312072e-02 -2.45482221e-01\n", " -1.00163177e-01  2.84476966e-01 -2.52821036e-02 -1.93359464e-01\n", " -1.44035250e-01 -3.24920982e-01 -2.48178989e-02  3.00529450e-01\n", "  1.58201963e-01  4.52980459e-01 -1.81535304e-01  6.22603416e-01\n", " -5.06226867e-02  2.65484862e-02 -6.79814696e-01 -9.65458751e-02\n", " -3.77233773e-01  2.72948798e-02  2.15962276e-01  2.43328080e-01\n", " -1.89087257e-01 -7.66877159e-02  1.54555827e-01  2.11290512e-02\n", "  1.13048062e-01  4.00588244e-01 -2.23423332e-01 -7.13596046e-02\n", "  1.91565864e-02 -8.06827843e-02 -9.72112045e-02  1.94662735e-01\n", " -1.57872036e-01  2.87679024e-02  3.72023195e-01  1.23227015e-01\n", " -2.58917987e-01  5.20099163e-01 -2.50829786e-01 -4.02719319e-01\n", " -4.00661111e-01 -9.77517217e-02  1.71325449e-02 -1.40572727e-01\n", " -1.89225063e-01 -1.01846769e-01  3.13093662e-02  1.15365595e-01\n", "  1.49044782e-01  1.91746652e-02  2.78427064e-01 -7.02198893e-02\n", " -1.44051254e-01  2.51220018e-01  3.99191201e-01  1.31558225e-01\n", " -3.46931577e-01  4.98534143e-02 -1.49142325e-01  4.49522555e-01\n", "  2.14277327e-01  1.03821978e-02  4.48803961e-01 -2.46816985e-02\n", " -2.80834377e-01 -3.73895988e-02  3.16821963e-01  3.01810265e-01\n", "  2.98574954e-01  3.85159612e-01 -2.31406286e-01 -1.66026622e-01\n", " -3.04425322e-02  4.84041125e-02  1.23626471e-01 -1.27816483e-01\n", "  2.79142894e-02 -7.03401938e-02 -1.27710447e-01  2.01600701e-01\n", "  1.07416034e-01  1.38230324e-01  2.14937761e-01  3.39009821e-01\n", "  1.01190746e-01  1.86479867e-01  1.71870291e-01  3.22138906e-01\n", "  2.54425168e-01  4.30815995e-01  2.10323632e-01  1.04591250e-01\n", "  1.55479029e-01  9.10640061e-02  4.68670651e-02 -1.40818730e-01\n", " -1.30690172e-01 -5.81195354e-01 -1.14286102e-01 -2.16708109e-02\n", " -3.56523693e-01 -1.18202763e-02 -1.75506473e-01 -2.62086928e-01\n", "  3.32247436e-01 -2.80510128e-01  5.40599108e-01 -2.19361335e-01\n", "  3.68496105e-02 -1.54491529e-01  2.31261715e-01  4.68998179e-02\n", " -9.37676355e-02  1.82299107e-01 -3.55887949e-01  4.24108386e-01\n", " -3.95835042e-02  1.19073287e-01 -1.88730329e-01  1.75292134e-01\n", " -1.00660980e-01 -4.91000041e-02  4.26977068e-01 -5.87417841e-01\n", "  1.95056628e-02 -1.46821886e-01  9.23826694e-02 -4.01568651e-01\n", "  2.96840340e-01  4.71366704e-01 -1.37966454e-01  2.00373888e-01\n", " -3.01223934e-01  3.02347243e-02 -2.06332326e-01  6.83449656e-02\n", "  5.95804118e-03  3.70750964e-01  4.36573267e-01  1.39535308e-01\n", "  3.32630277e-02  1.18604302e-01 -4.62921441e-01 -4.39605117e-01\n", "  1.22697115e-01 -5.91623783e-01  7.76473731e-02 -2.70850271e-01\n", "  5.86046159e-01  5.28441742e-03  1.10425159e-01  5.92931390e-01\n", " -8.89152884e-02  4.24695194e-01 -5.36416888e-01 -2.28711143e-01\n", " -6.33748174e-02 -2.79527575e-01 -1.49499372e-01 -2.03251481e-01\n", "  2.86896173e-02 -2.80155182e-01 -6.26741886e-01  2.68723905e-01\n", " -1.54113069e-01 -3.55150133e-01  1.82841465e-01 -4.83013205e-02\n", "  2.21576422e-01  4.08897758e-01  1.09510019e-01 -5.01089953e-02\n", " -5.11454456e-02 -2.14687139e-01  1.51620269e-01  8.28262419e-02\n", " -1.10773847e-01  4.99529004e-01  1.98533982e-01  3.62420022e-01\n", "  4.32959199e-01 -4.32760864e-01 -5.61017133e-02 -2.79204734e-03\n", "  1.62222922e-01 -3.42880666e-01 -2.60770380e-01  4.93269190e-02\n", "  9.76810008e-02  4.86948853e-03 -3.51223797e-01 -1.53655916e-01\n", " -2.41012812e-01 -2.92240262e-01  2.21165866e-01 -1.08843215e-01\n", " -3.62697601e-01 -1.29027173e-01 -1.70905873e-01  1.96808446e-02\n", "  2.41159156e-01 -2.26696134e-01  2.93846488e-01 -6.03029691e-03\n", "  3.43817651e-01 -3.84270474e-02 -1.06227964e-01  4.57351655e-02\n", "  2.54115045e-01  1.42460978e-02 -1.41144514e-01 -9.04832259e-02\n", "  1.56746536e-01  1.16984859e-01  2.51673937e-01 -1.06538665e+00\n", " -1.35123551e-01  1.60147563e-01 -9.97087508e-02 -3.89985561e-01\n", "  8.11444521e-02 -2.87176311e-01  3.80035006e-02  1.03776559e-01\n", "  3.62626731e-01  1.05617248e-01  3.55561674e-01 -1.25076190e-01\n", " -2.09332809e-01  2.88081586e-01 -1.01441577e-01  4.18055087e-01\n", " -8.99410099e-02 -2.22438872e-02  1.10959008e-01  3.70313019e-01\n", " -7.90376961e-03  5.71380630e-02 -2.67271642e-02  4.05053258e-01\n", "  1.89615756e-01 -5.08692153e-02 -2.74953544e-01  5.01867831e-01\n", " -2.10409284e-01  8.75228643e-02 -1.77266657e-01 -1.01954043e-01\n", "  2.59971082e-01  6.24896213e-02  2.19760358e-01 -1.76194921e-01\n", " -1.70534015e-01 -1.75336450e-01  1.79290414e-01 -3.14216524e-01\n", " -1.01387948e-01  1.28656060e-01 -4.12123471e-01  9.61667001e-02\n", "  4.52959597e-01  1.07683897e-01 -4.82706338e-01 -5.19625962e-01\n", " -2.49917299e-01  1.78861037e-01  3.91055942e-02 -2.02681363e-01\n", " -3.62187028e-01 -2.06305161e-02 -1.97545171e-01 -2.34683901e-01\n", "  5.04235886e-02  1.94861636e-01 -5.53478152e-02 -2.64758002e-02\n", "  8.25857520e-02 -2.08466455e-01  1.88764572e-01 -1.01585589e-01\n", " -1.43747047e-01 -5.38135953e-02 -3.70111801e-02 -3.01665217e-01\n", "  2.30243742e-01  1.19674765e-01 -5.86239994e-03  2.53953308e-01\n", " -2.77163148e-01 -1.65396839e-01  1.31846189e-01 -7.06751198e-02\n", "  9.16990191e-02 -3.04174237e-02  1.00627668e-01 -1.00216314e-01\n", " -4.07995522e-01  1.99667960e-01  1.53665408e-01  2.66151965e-01\n", "  2.28067070e-01  2.79687315e-01  1.54935360e-01  4.05549765e-01\n", " -2.20636636e-01 -1.47675708e-01  4.22892421e-01  1.10002734e-01\n", " -2.83812016e-01 -2.04569072e-01  3.38126998e-03 -1.50561661e-01\n", "  2.35903058e-02  7.66320154e-04  1.29593670e-01 -1.21339284e-01\n", "  2.07350641e-01 -4.31642145e-01  2.97399044e-01 -2.16586888e-01\n", " -4.07908738e-01  1.40433028e-01 -1.84122086e-01  1.23902813e-01\n", " -3.84697139e-01  2.25571170e-01  2.62092471e-01 -2.47904405e-01\n", "  3.19565356e-01 -1.52464122e-01  9.66199189e-02  8.42476487e-02\n", " -4.16726619e-01  9.08984095e-02  8.21515396e-02  4.07547534e-01]\n", "Document 5 embedding: [ 3.35416943e-01  1.73701584e-01  1.34140864e-01  3.16974312e-01\n", " -7.31848851e-02  1.41363174e-01 -3.70673761e-02  2.66343147e-01\n", "  5.11844397e-01 -1.82009593e-01 -1.14014268e-01  1.66931804e-02\n", "  1.01508461e-01  3.49621564e-01  4.72411662e-01 -3.47625226e-01\n", "  5.88211752e-02  2.36802474e-01  2.31864378e-01  3.90124261e-01\n", " -9.69750434e-02 -2.35077783e-01 -1.47057883e-02 -2.33899355e-01\n", "  1.59444094e-01 -3.19996886e-02  2.06061214e-01  3.07664245e-01\n", " -5.41791081e-01  3.86126071e-01 -7.92963579e-02  1.83184922e-01\n", "  1.21512106e-02  2.06008494e-01 -2.26222888e-01  1.56155929e-01\n", "  3.57589751e-01 -2.29460746e-01 -4.95187461e-01  2.83411056e-01\n", "  2.42106952e-02  3.82072151e-01 -6.86172917e-02  3.33103508e-01\n", " -1.08096391e-01  3.48004922e-02 -3.57873112e-01 -5.37118018e-01\n", " -2.05812175e-02  7.50383884e-02  2.07576096e-01 -3.61002088e-02\n", "  1.64408684e-01  1.65667623e-01 -9.00212079e-02  2.01804005e-02\n", " -4.25930955e-02  7.06489682e-02  8.99347365e-02  1.52172714e-01\n", "  2.00112000e-01 -8.42972100e-02 -3.94912884e-02  3.97985637e-01\n", "  6.41125813e-02 -2.06640422e-01  3.83604728e-02 -9.31461006e-02\n", " -8.69831145e-02  9.26952437e-02 -2.73607492e-01  2.56017625e-01\n", "  2.85981387e-01  4.89795327e-01  1.25646293e-01  7.84105882e-02\n", " -5.20219356e-02 -4.20471191e-01 -4.79462832e-01  6.17562234e-01\n", "  4.82531101e-01  1.04012601e-01 -2.53906220e-01  3.68159963e-03\n", "  7.61351138e-02  7.80353695e-02  6.21247552e-02 -3.44820976e-01\n", " -9.30777639e-02  3.98438573e-02  2.58357935e-02  2.67079145e-01\n", " -4.80494976e-01 -2.48819992e-01  2.77857687e-02 -2.08539516e-01\n", "  2.11782664e-01 -6.09278142e-01 -1.77997604e-01  1.38212368e-01\n", " -1.52098939e-01  4.43888940e-02  3.21416557e-01  8.73136148e-02\n", " -1.03935353e-01  5.17970800e-01 -2.64893562e-01  1.06801108e-01\n", "  1.15217574e-01 -8.14799890e-02  4.80217338e-02 -2.57175416e-01\n", "  5.60095251e-01 -1.05122402e-01  1.71063989e-01  1.53454348e-01\n", "  5.81025109e-02 -3.05681944e-01  6.08017519e-02  1.13365874e-01\n", " -9.50187892e-02  2.09204048e-01 -4.52811450e-01  6.94470778e-02\n", " -7.64566362e-01  5.39634079e-02  1.06845692e-01  3.00148204e-02\n", "  2.08794087e-01  2.94772118e-01 -1.20916970e-01 -1.01817744e-02\n", "  1.73914671e-01 -8.15172136e-01  2.09443673e-01 -2.31177676e-02\n", "  6.10461354e-01  3.18755768e-02  7.96930119e-02  1.17833540e-01\n", " -8.09154212e-02 -7.55218491e-02 -2.28075430e-01  2.35072315e-01\n", " -3.41257542e-01  1.74436390e-01  1.08497821e-01 -7.38074630e-02\n", "  8.81478265e-02 -2.93504506e-01  1.28561750e-01  1.35614961e-01\n", "  6.73755854e-02 -1.58849970e-01  1.03654772e-01  4.56643403e-01\n", "  1.64192259e-01 -3.70521426e-01 -3.81256491e-02 -1.76955670e-01\n", "  9.90848541e-02  1.19857773e-01  3.27206016e-01  1.33690149e-01\n", "  1.87459454e-01 -7.73369819e-02  5.19616902e-01  1.97930440e-01\n", " -3.68472468e-03  5.06111205e-01 -8.20689574e-02 -2.07480088e-01\n", " -2.15973288e-01 -1.36670128e-01 -4.35164303e-01  1.51163608e-01\n", "  9.81129408e-02 -2.18317181e-01 -4.87198196e-02 -2.50474606e-02\n", " -3.72139990e-01  3.51066329e-02 -5.08547854e-03  1.79786131e-01\n", "  2.61219721e-02  2.74048239e-01 -2.48323306e-01  1.09822325e-01\n", "  1.94665179e-01 -3.18488240e-01  1.69265252e-02  3.20287853e-01\n", " -1.19739495e-01  1.14973024e-01  3.32917869e-01  3.46087962e-02\n", "  1.91412777e-01  3.41601968e-01  6.36629434e-03 -1.04418740e-01\n", "  2.41362583e-03  4.48195785e-02 -2.22545713e-01  5.46348572e-01\n", " -2.14979071e-02 -3.26935142e-01 -1.47397786e-01  2.02784389e-01\n", "  4.48146675e-05 -2.82832503e-01  9.93079022e-02 -3.90454769e-01\n", " -2.99493045e-01  2.57971793e-01  7.57619366e-02 -1.53843716e-01\n", " -6.74951449e-02  4.04194333e-02  3.24984491e-01 -4.09728661e-02\n", "  1.55059874e-01  2.75889318e-02  1.78856760e-01  6.84977770e-02\n", "  4.42084670e-01 -1.64401643e-02 -1.42440289e-01  1.12367356e-02\n", "  4.18522283e-02  1.84053496e-01 -8.44320357e-02  1.13341324e-02\n", " -2.18878344e-01 -3.24388981e-01  1.16209527e-02  1.42201886e-01\n", "  1.56506047e-01  8.03076774e-02  2.74191629e-02  2.81533778e-01\n", "  9.87499058e-02  1.43307820e-01  1.27772465e-01  2.39762202e-01\n", "  3.47780406e-01  1.66747198e-01 -9.54614803e-02 -4.48838294e-01\n", "  3.89056891e-01 -4.65012342e-01 -4.03054431e-02  3.20360363e-01\n", "  3.48519683e-01  2.00572032e-02  6.02177799e-01 -1.57268733e-01\n", " -1.68398455e-01  3.79744507e-02 -5.98256849e-02  4.43203654e-03\n", "  3.62605929e-01 -4.47280854e-01  1.56515434e-01  5.44709433e-03\n", " -2.64492184e-01  2.62968749e-01  1.06929734e-01  2.64906794e-01\n", " -6.02172688e-02 -4.39527959e-01  3.40187818e-01  2.52551794e-01\n", "  6.20397143e-02  1.47325784e-01 -1.82857495e-02  2.16207616e-02\n", " -1.95559353e-01  2.86765039e-01 -4.98777598e-01  2.69989491e-01\n", "  1.85685411e-01 -4.61455822e-01  1.96474977e-02  4.11738306e-02\n", "  4.07509431e-02 -4.59848672e-01  2.65329480e-01 -4.73654717e-02\n", "  1.87027678e-01  3.85377333e-02 -1.75528750e-02 -7.27777332e-02\n", " -1.50025755e-01 -1.24048233e-01  2.06304565e-01  5.64468384e-01\n", " -1.16216624e-02 -4.04520303e-01  1.07640706e-01 -1.05741099e-01\n", " -1.33986294e-01  2.85977155e-01 -1.30393803e-01  3.22496474e-01\n", "  2.64296606e-02  6.59828305e-01  6.43429458e-01 -4.20262739e-02\n", " -1.91495255e-01 -2.28447691e-01 -1.71431035e-01  1.33557335e-01\n", "  2.86795765e-01 -7.92064294e-02  7.78637752e-02  1.48098245e-01\n", " -5.68725318e-02  2.59365477e-02 -3.47936362e-01 -9.81920511e-02\n", " -3.56142893e-02  1.01302028e-01 -1.70059472e-01 -6.50527924e-02\n", " -1.75848469e-01  1.13215089e-01 -5.97724952e-02 -5.65197051e-01\n", "  5.61466776e-02 -1.59912765e-01  5.89411020e-01 -8.63590240e-02\n", "  1.35230988e-01  1.01149771e-02 -8.80197212e-02  1.59456596e-01\n", " -7.56073296e-02  2.94530571e-01 -1.20547183e-01 -1.36336699e-01\n", "  3.92600000e-01 -5.94872981e-02 -2.89594322e-01  3.52623649e-02\n", " -5.08668870e-02 -2.72656605e-02 -1.02586582e-01  9.75724161e-02\n", " -1.00230381e-01 -3.89714539e-01 -4.32035476e-02  2.03360379e-01\n", "  2.76646316e-01  7.04840720e-02  1.49863705e-01  3.55584845e-02\n", " -6.72270879e-02  1.30286038e-01 -2.01187208e-02  4.14000630e-01\n", " -2.26210766e-02 -5.77244572e-02 -1.64520815e-01  1.10773757e-01\n", "  1.83864176e-01 -1.63055927e-01 -8.03103298e-02 -2.62575269e-01\n", "  1.79555997e-01  2.95011001e-03 -1.02709398e-01 -5.92326745e-02\n", "  1.06419325e-01  3.25343907e-01  1.84214249e-01  1.98154956e-01\n", "  3.48714203e-01 -1.54698357e-01  1.00544915e-01  1.14494406e-01\n", "  3.70485604e-01  3.35274078e-02 -7.07960576e-02 -3.09993122e-02\n", " -1.81402519e-01  3.72729957e-01  4.10075933e-01  4.94383760e-02\n", " -3.20049785e-02  1.07785866e-01  2.64004856e-01 -1.88283682e-01\n", "  2.91109413e-01  1.42398417e-01  2.06337780e-01  5.53491339e-02\n", "  5.28088629e-01 -2.13473693e-01  1.54549852e-01 -3.82328480e-02\n", " -4.89611626e-02 -1.90724097e-02 -1.06292441e-01  4.84672748e-03\n", "  3.38689983e-01 -5.21591157e-02 -5.62829785e-02 -1.99366629e-01\n", " -1.12588517e-01  4.54709888e-01 -3.47667038e-02  3.58483106e-01\n", " -1.50165617e-01 -1.53327569e-01  1.37192175e-01 -2.74821788e-01\n", "  3.10945064e-01  2.77594447e-01  1.43337280e-01  1.01183400e-01\n", "  1.32110074e-01  6.90264702e-02 -9.91767347e-02 -1.42900094e-01\n", " -1.68438330e-01 -2.93964497e-03 -5.71762323e-01  2.74062574e-01\n", " -1.44227697e-02  5.38091585e-02  3.66743393e-02 -1.01888090e-01\n", "  1.92765623e-01  1.39865413e-01  2.61565000e-01  1.04607947e-01\n", "  3.58848035e-01 -2.88028270e-01 -2.49366194e-01  6.75321892e-02\n", "  5.57910129e-02  8.64567608e-02  1.56820551e-01  3.33803445e-01\n", "  4.18516397e-02 -1.60259426e-01 -4.58789766e-01 -1.66878954e-01\n", " -4.50467505e-02 -4.82821502e-02  1.77168027e-02  8.19471627e-02\n", " -2.71550208e-01  1.65849462e-01  1.26591131e-01  9.24608260e-02\n", "  2.53372550e-01  2.23940313e-01 -4.47040409e-01 -1.24109108e-02\n", " -2.57487714e-01  9.99541357e-02 -8.94477293e-02  6.25672638e-02\n", "  1.10124074e-01  1.59121215e-01  1.60280257e-01 -2.55044132e-01\n", " -3.23205620e-01  2.85088092e-01 -2.39941496e-02 -2.88867891e-01\n", " -7.97036514e-02  7.09924623e-02 -3.12437862e-01  1.04777388e-01\n", " -1.40336692e-01 -2.66842097e-01  1.26523867e-01 -5.60562462e-02\n", "  1.66697875e-01  1.12121336e-01  9.78942588e-02 -3.89708310e-01\n", " -1.03626385e-01 -2.32838005e-01  9.95225087e-02  1.02813594e-01\n", " -3.35704803e-01  2.10405737e-01 -6.79357499e-02  5.07761419e-01\n", "  2.95853436e-01  4.15760428e-02  1.74621478e-01 -3.86247449e-02\n", " -4.00537640e-01 -8.85974914e-02  2.45688677e-01  2.73292482e-01\n", " -1.04621246e-01  1.21570818e-01 -8.95375088e-02  1.34726360e-01\n", " -2.55527962e-02  2.54740924e-01 -3.06511000e-02  7.50069991e-02\n", " -1.14901811e-01 -6.75838813e-02 -3.50124426e-02  1.30186304e-01\n", "  4.55318511e-01  1.55281842e-01  5.94446585e-02 -1.21892504e-02\n", " -1.27364740e-01  2.29764115e-02  3.50291312e-01  1.24592982e-01\n", "  1.88115627e-01  5.58275990e-02  1.91220060e-01 -1.78136930e-01\n", "  1.32522404e-01 -1.51823431e-01 -4.94945794e-02 -2.94348568e-01\n", "  2.86186859e-02 -3.60009260e-02  2.70693660e-01 -2.17867211e-01\n", " -3.11540246e-01 -1.37025967e-01 -4.54692468e-02 -1.04253970e-01\n", "  1.63655922e-01 -8.70852917e-02 -6.34896234e-02 -2.01389641e-01\n", "  7.60108232e-01 -1.09754577e-01  4.47564662e-01 -4.26319465e-02\n", "  8.03755969e-02 -7.78363124e-02 -3.79425645e-01  2.39683405e-01\n", "  1.23422844e-02 -1.22704692e-01  8.63724761e-03  3.94420505e-01\n", " -1.82353094e-01  7.54463673e-02  3.48837405e-01 -1.82004362e-01\n", "  1.10299692e-01  9.86437798e-02 -1.29511565e-01  1.66355342e-01\n", "  1.43936621e-02 -1.40920244e-02 -7.88588896e-02 -2.23823413e-02\n", " -4.91426677e-01 -2.47280091e-01 -3.65744948e-01 -5.27105434e-03\n", " -1.23142995e-01  9.19140428e-02 -2.62426555e-01  3.08994949e-01\n", "  1.16497770e-01  4.18468900e-02 -6.16137564e-01 -2.01285779e-01\n", "  1.90623432e-01 -4.24518079e-01 -4.50757921e-01 -2.36924008e-01\n", "  1.88532129e-01 -1.25968874e-01  1.48194775e-01 -3.76064658e-01\n", " -1.83763534e-01 -5.21694541e-01 -7.16452599e-01  2.73249708e-02\n", "  1.11875772e-01  6.45310134e-02 -1.90779388e-01  1.37499318e-01\n", "  7.04695284e-02 -3.19476932e-01 -2.47270744e-02  9.39306691e-02\n", " -1.61454678e-01 -3.86484802e-01  9.31018069e-02 -6.68562353e-02\n", " -4.16215463e-03  4.68088746e-01  1.45432465e-02  2.37203017e-01\n", " -1.18080132e-01 -1.08023278e-01 -6.47406340e-01 -2.27299750e-01\n", " -2.41807520e-01  2.40602419e-01  3.36190820e-01 -3.34441066e-01\n", "  3.15067440e-01  2.67239034e-01 -3.18215013e-01 -2.28860274e-01\n", " -6.90353243e-03  3.05398375e-01 -4.44703288e-02  2.44373858e-01\n", "  2.73730099e-01 -3.31062749e-02 -3.07478309e-01 -4.61195171e-01\n", "  2.17102230e-01 -4.81840134e-01  7.71147311e-02  4.89289612e-02\n", "  3.45096849e-02  2.41537169e-01  2.93456048e-01 -1.38425186e-01\n", "  9.52412337e-02  8.84289891e-02 -7.19300583e-02 -2.09362745e-01\n", "  2.84259766e-01  1.51375271e-02 -1.06740847e-01  5.46059087e-02\n", "  1.05377309e-01 -5.95951378e-01  1.00963004e-01 -1.23368226e-01\n", " -3.56668174e-01  1.20087199e-01  6.61906525e-02 -9.20897126e-01\n", "  2.98462179e-03 -8.94708151e-04 -3.20032865e-01 -3.66072804e-01\n", " -8.88987631e-02 -3.81903499e-01  1.84223950e-02  1.42183676e-01\n", " -6.30479872e-01  5.21076843e-02 -2.13204965e-01  4.83015254e-02\n", "  2.99841762e-01  2.74128467e-01  2.05399469e-02  3.23739409e-01\n", " -1.50911827e-02 -1.18209630e-01  3.75671946e-02  3.15055192e-01\n", "  3.49980295e-01 -6.60978481e-02 -4.21410240e-02  2.92039931e-01\n", "  2.36352514e-02 -5.32280654e-02 -8.68144989e-01  6.08792566e-02\n", " -2.83872247e-01  8.43827575e-02  1.24259263e-01  1.33772239e-01\n", "  6.74535036e-02  1.28342375e-01  4.36534941e-01 -2.10960120e-01\n", " -2.34403670e-01  2.26915944e-02  1.10064380e-01 -1.37195170e-01\n", " -8.65997598e-02 -3.59760299e-02 -6.47387505e-02  3.04248542e-01\n", "  3.95609140e-02 -2.24414349e-01 -4.39631522e-01 -3.29044133e-01\n", " -4.30062450e-02 -6.72850683e-02 -1.31059796e-01 -4.10121866e-02\n", " -3.90782416e-01  8.96207020e-02  4.42670554e-01 -1.33839041e-01\n", "  1.43941678e-02  6.62575513e-02  6.00389615e-02  1.45503292e-02\n", "  2.10766699e-02 -1.18442923e-01 -1.42327294e-01 -7.26732314e-02\n", " -1.04875728e-01 -3.04269865e-02  1.62027091e-01  6.86597154e-02\n", " -8.96764547e-02  4.21788424e-01  1.24833398e-01  1.95028987e-02\n", " -6.56740740e-02 -1.25763163e-01 -1.68847710e-01 -6.27377182e-02\n", " -2.54589856e-01 -1.16201319e-01 -6.66959062e-02 -3.71380359e-01\n", " -2.39720978e-02  1.46755248e-01  2.78916001e-01  2.94250369e-01\n", "  7.74051100e-02  4.72066551e-01  2.14542538e-01  6.58234358e-01\n", "  3.18905056e-01 -4.09522690e-02  1.91759557e-01 -1.10248335e-01\n", "  1.53005138e-01  1.14480900e-02 -5.54834418e-02 -3.52354974e-01\n", " -1.50734469e-01  4.87684608e-01  1.75490707e-01 -1.08731180e-01\n", " -5.69183156e-02 -3.11847061e-01 -5.15866913e-02 -1.79440454e-02\n", " -6.26133621e-01  1.73989087e-01  7.18565658e-02  2.93017775e-01\n", "  3.11017692e-01  1.84340209e-01  1.89634651e-01 -1.90456226e-01\n", "  1.45693764e-01  3.55814487e-01  1.87963266e-02  1.01576380e-01\n", " -2.72044957e-01  1.02326065e-01  1.63259834e-01  5.94455861e-02]\n", "Document 6 embedding: [ 3.43921721e-01  2.39546150e-01  1.63267270e-01  1.55908242e-01\n", " -2.13283256e-01 -1.07979707e-01  1.96257327e-02 -1.12908445e-01\n", "  3.09570253e-01 -7.86426142e-02 -8.50207061e-02 -1.63244039e-01\n", "  4.35915738e-02  1.02420843e+00 -2.14237198e-01 -5.07657409e-01\n", " -3.14833909e-01 -1.63601339e-01  9.57083181e-02 -5.82829975e-02\n", " -9.11263078e-02  5.68361841e-02 -1.27463099e-02 -2.74212271e-01\n", "  2.04983726e-01 -3.33903432e-01 -9.55293030e-02 -1.08366376e-02\n", " -5.15778840e-01  2.39028901e-01 -1.65454134e-01  2.08842084e-01\n", "  2.95176506e-02  3.42049479e-01 -6.23944849e-02 -1.33290127e-01\n", "  3.15061122e-01 -2.77973354e-01  1.08361892e-01  1.24016337e-01\n", "  1.59127135e-02  5.20015836e-01  1.28902823e-01  3.24349910e-01\n", " -1.29584417e-01  4.67079366e-03 -5.20583332e-01 -2.08459392e-01\n", "  2.31072813e-01  2.07964644e-01 -6.43661246e-02  1.38425931e-01\n", "  2.55720727e-02  1.76344484e-01  1.99281368e-02 -1.06560905e-02\n", " -1.24170616e-01  2.37904206e-01  1.04975380e-01 -2.24159464e-01\n", "  4.03564749e-03 -9.90050882e-02  6.67909207e-03 -1.83566347e-01\n", "  1.77480921e-01  1.13612212e-01 -6.21951483e-02 -3.43502432e-01\n", "  9.31933299e-02  1.24729805e-01  9.57112759e-02  5.83805107e-02\n", "  6.30744994e-02  5.21468401e-01  1.64361037e-02  1.86312109e-01\n", " -1.24116793e-01 -2.87535012e-01 -1.50761545e-01  3.87582183e-01\n", "  2.63902158e-01  2.56544739e-01 -4.71714914e-01  8.34785122e-03\n", " -3.41318212e-02 -3.48654926e-01 -4.73322384e-02 -5.64180732e-01\n", "  7.46333878e-03 -9.43974480e-02 -3.14790905e-01  3.79164517e-01\n", " -4.13438350e-01  3.08215413e-02  3.14959064e-02 -3.68876517e-01\n", "  1.15991212e-01 -7.38987088e-01 -1.26723319e-01 -1.08958585e-02\n", " -3.44863869e-02  2.12994248e-01  4.97062087e-01 -6.34556487e-02\n", "  1.31601542e-01  5.09403586e-01 -7.99521953e-02 -6.52054965e-04\n", "  9.35245119e-03 -1.98793188e-01  1.27044365e-01  1.04341641e-01\n", "  7.60929048e-01 -2.14268081e-02  2.02613324e-01  1.11966863e-01\n", " -1.64946109e-01  1.20199248e-01 -1.17702790e-01 -6.74041808e-02\n", " -1.40279353e-01 -5.22744656e-02  2.91790925e-02  1.21600874e-01\n", " -6.89185321e-01  1.66817322e-01  8.97227135e-03 -7.89849088e-02\n", "  6.04320969e-03 -3.73802669e-02  2.63225529e-02 -9.78826359e-02\n", "  1.81136325e-01 -4.68190223e-01 -1.85555071e-01 -8.05051532e-03\n", "  2.68834174e-01  5.10568954e-02 -1.34468228e-01  2.01158062e-01\n", " -1.72956243e-01  4.70121726e-02 -1.95450842e-01  8.35211277e-02\n", " -2.95196444e-01  2.83001155e-01  5.43805808e-02 -1.65462345e-01\n", " -1.87724587e-02  2.67346561e-01  1.71718895e-01 -3.35088596e-02\n", " -9.94410273e-03  1.68523099e-02  1.36750013e-01 -6.24946877e-02\n", "  4.48926985e-01 -1.02540795e-02 -1.07472457e-01 -5.12349680e-02\n", "  2.41738081e-01 -1.85185869e-03  1.97276250e-01  6.81095123e-02\n", "  2.15733781e-01  1.52703345e-01  3.48196298e-01  5.66765428e-01\n", " -1.39132002e-02  2.40617275e-01 -2.03542754e-01 -3.46279204e-01\n", " -2.32654028e-02 -4.04588521e-01 -2.47177094e-01  3.57175767e-01\n", "  1.22986428e-01 -7.23148510e-02  1.20548584e-01  2.13571098e-02\n", "  1.24550993e-02  3.80491726e-02 -2.16488950e-02  2.65206635e-01\n", " -6.39329925e-02  1.10089779e-01  1.27004106e-02  2.50192434e-01\n", "  1.40647650e-01  2.12751813e-02 -2.24796496e-02  1.98168993e-01\n", " -9.74246189e-02 -1.00589752e-01  2.95600623e-01 -2.29291409e-01\n", " -2.91362584e-01  3.39806616e-01  7.25224316e-02 -1.94089666e-01\n", " -4.52887900e-02  2.55081542e-02 -3.04124150e-02  6.45100415e-01\n", " -4.38413536e-03  5.57716750e-02 -1.28057137e-01  2.38162011e-01\n", " -4.66913804e-02 -1.72854841e-01  6.86508939e-02  2.00728521e-01\n", " -2.77211040e-01  5.85209966e-01 -6.40116259e-02 -2.59822816e-01\n", "  1.24934003e-01 -3.18329819e-02  8.99660066e-02 -7.13757798e-02\n", "  2.07434908e-01 -1.05734222e-01  2.45570362e-01 -1.38266221e-01\n", "  9.12406921e-01 -4.88554351e-02 -3.19802202e-02 -1.85963452e-01\n", " -1.91829503e-01  1.32346943e-01  6.01813309e-02  2.90215611e-01\n", "  3.59645814e-01  8.00142586e-02  2.25596532e-01 -2.31691450e-01\n", "  1.43404588e-01 -2.69137919e-01  1.81768954e-01  5.33689521e-02\n", "  2.23882332e-01  8.78397748e-02  2.17919484e-01  1.40572056e-01\n", "  2.92024940e-01 -2.08455488e-01  1.12211101e-01 -4.56959516e-01\n", "  5.87293684e-01 -7.40511954e-01 -1.29981309e-01  1.92684010e-02\n", "  1.23003133e-01  1.23016514e-01  3.54794592e-01 -8.67435150e-03\n", " -5.33189438e-02 -6.87746853e-02  2.62535661e-01  4.42964822e-01\n", "  1.19480945e-01 -4.55711752e-01  1.88808054e-01  3.44695449e-02\n", "  4.72728573e-02  1.64539009e-01  1.39997900e-01  6.87986836e-02\n", "  1.05641410e-01 -3.65788192e-01  2.82300621e-01 -3.66274826e-02\n", "  2.56612480e-01  3.13729733e-01 -1.90096274e-02  6.18044212e-02\n", " -2.99843848e-02  1.43063158e-01 -6.02058768e-01  4.22013402e-01\n", " -2.80510411e-02 -2.90031344e-01 -1.19889877e-03  3.26834381e-01\n", "  8.60213935e-02  3.00144941e-01  1.93448558e-01 -3.04332584e-01\n", "  4.64940518e-01  2.22735241e-01  1.65532157e-01 -1.45091098e-02\n", " -5.34804523e-01 -9.18494016e-02  9.22759101e-02  5.62695682e-01\n", "  5.35530299e-02  1.92801788e-01 -2.86253858e-02 -1.66774765e-01\n", " -3.78847301e-01 -1.12436369e-01 -7.05657750e-02  2.89886206e-01\n", "  2.28515223e-01  4.17185098e-01  7.63074756e-01  2.34278478e-02\n", "  1.38758048e-01  7.18064979e-02  4.02638204e-02 -1.36567369e-01\n", "  2.03502357e-01 -2.21601352e-01  8.62471238e-02  3.25103402e-01\n", "  4.21968922e-02  2.14466408e-01  8.29752758e-02 -4.02794719e-01\n", " -6.77711889e-02  8.21724087e-02  4.69868109e-02 -9.72599164e-02\n", "  3.92357767e-01  1.27749473e-01  6.77936912e-01 -7.26100683e-01\n", "  3.41027826e-02  5.13568595e-02  3.21943253e-01 -1.06021643e-01\n", " -2.26268098e-01  3.73887241e-01 -1.63268492e-01  9.84106883e-02\n", " -1.12279087e-01  1.47570357e-01 -8.36101640e-03 -3.60851362e-02\n", "  3.73490080e-02 -9.31321904e-02 -1.00647278e-01  1.18855953e-01\n", " -1.52547464e-01  3.16176824e-02 -4.24036197e-02  4.64341007e-02\n", "  1.20089978e-01 -7.80308545e-02 -3.74242038e-01  1.23064689e-01\n", "  5.11110723e-01 -1.67958125e-01 -2.37147331e-01  2.04441607e-01\n", "  1.05737060e-01 -1.77538805e-02  7.76049681e-03  2.49938935e-01\n", " -1.25792608e-01  3.27063054e-01 -2.59596072e-02  1.64460897e-01\n", "  3.47802728e-01  1.80182204e-01 -6.49524527e-03 -4.44776505e-01\n", "  3.22798491e-02 -2.00431868e-01 -2.32043475e-01  2.58162897e-02\n", "  2.02653021e-01  1.46434046e-02  1.61413252e-01  3.01757932e-01\n", " -9.27513093e-02 -1.34345695e-01  6.28076717e-02 -2.66309440e-01\n", "  5.47420792e-02  1.31736360e-02  1.59841627e-01 -1.43859208e-01\n", " -2.14983374e-01  1.89647913e-01  5.70678830e-01 -7.84702450e-02\n", "  3.63395214e-02  6.47302568e-02  1.94737211e-01 -1.81829542e-01\n", "  1.88053429e-01  6.24679551e-02  2.39163265e-01  1.57585423e-02\n", "  5.66498160e-01 -1.52434245e-01  1.47347540e-01 -7.24990666e-03\n", " -1.57841265e-01  5.77922091e-02 -3.07619989e-01  2.87252545e-01\n", " -2.12089773e-02 -1.71202630e-01 -3.30295235e-01 -4.79658872e-01\n", " -1.96994439e-01  1.83470640e-02  1.13461562e-01  1.98611036e-01\n", " -2.12891072e-01 -1.87191628e-02  2.95256138e-01 -2.60760605e-01\n", "  3.47901851e-01  3.12566310e-01  3.37393105e-01 -7.37465620e-02\n", "  4.76350397e-01  1.39053360e-01 -5.87620251e-02  4.95400615e-02\n", " -1.95773602e-01  3.06754876e-02 -5.65738797e-01  3.83712262e-01\n", " -1.35820672e-01  1.98756635e-01 -1.04241587e-01 -2.60249227e-01\n", " -1.69658661e-02  4.45612729e-01 -1.28164832e-02 -1.00982569e-01\n", "  7.56777227e-02 -2.19411537e-01 -1.98426589e-01  1.75496101e-01\n", "  2.36661851e-01  3.23346317e-01 -6.88149035e-02 -1.19682336e-02\n", " -1.22683711e-01  4.18319181e-02 -2.95842439e-01 -3.52557480e-01\n", " -4.87165809e-01 -1.09986410e-01  8.64160061e-03  1.14052370e-01\n", " -1.31857246e-01  1.02949493e-01 -2.98386645e-02  1.34034559e-01\n", "  1.08420327e-01  3.00447524e-01 -1.31711990e-01 -1.82647794e-01\n", " -1.24741487e-01 -2.12368891e-01 -1.78982958e-01  1.32356226e-01\n", "  1.00739144e-01  2.51924992e-01  4.18989062e-01 -6.04334846e-02\n", "  4.03268546e-01 -1.12434424e-01  1.76997438e-01 -3.21001917e-01\n", " -1.46244749e-01  1.30979260e-02 -3.14372212e-01 -1.70493916e-01\n", "  4.29257564e-02 -1.54026255e-01 -2.30934650e-01  1.18548721e-01\n", "  1.21527165e-01  4.04926315e-02  1.41601041e-01 -1.38926715e-01\n", " -4.42258045e-02  3.09017505e-02  1.81621075e-01  2.30360124e-03\n", " -3.81402791e-01  1.66318849e-01  1.81342825e-01  1.77913427e-01\n", "  2.91784465e-01  2.82436818e-01  3.01965982e-01  8.79916176e-03\n", " -4.18713272e-01  2.00162977e-01  3.58268619e-01  4.29890811e-01\n", "  3.18728127e-02  1.83991671e-01 -1.42076164e-01  1.94083348e-01\n", " -1.07732072e-01 -1.65652260e-01 -2.59436257e-02  7.90109336e-02\n", " -2.07260475e-01  7.18685286e-03 -2.61782289e-01  1.78383514e-02\n", "  3.48679394e-01 -4.02241126e-02 -6.98313676e-03  2.03243479e-01\n", "  1.78306341e-01  1.30651072e-01  1.96756870e-01 -2.27804370e-02\n", "  7.66733587e-02  2.78532982e-01  6.43948317e-02  8.55030194e-02\n", "  3.56030203e-02  3.84612717e-02  1.21430364e-02 -1.99788004e-01\n", " -1.20607629e-01 -3.31586063e-01  7.25192577e-02 -4.44890969e-02\n", " -1.87282130e-01 -1.28096253e-01 -3.23042236e-02 -1.45179185e-03\n", "  2.11890504e-01 -2.45372862e-01  3.84471923e-01 -5.75694442e-02\n", "  6.00292861e-01  4.98407744e-02  1.90640643e-01 -4.33216654e-02\n", " -3.36550698e-02  2.55829155e-01 -4.84702379e-01  2.36957088e-01\n", " -1.00301720e-01  5.54479146e-03 -2.72524729e-02  2.97719836e-01\n", " -2.93520801e-02 -9.98020843e-02  2.55501002e-01 -2.84937412e-01\n", " -6.50217906e-02  6.19062446e-02  1.21989518e-01  2.97034457e-02\n", "  2.19710425e-01  4.04706270e-01  3.44927683e-02 -1.77034419e-02\n", " -3.63866717e-01  3.57490070e-02 -2.66711146e-01 -9.20661390e-02\n", "  1.92812679e-03  6.64257333e-02 -4.04664390e-02  2.63077796e-01\n", "  2.27188356e-02  1.26211762e-01 -2.87211776e-01 -3.54311824e-01\n", "  6.50250316e-02 -8.51583362e-01 -4.56088006e-01  8.82564858e-02\n", "  7.69066393e-01 -6.81026652e-02  1.88624829e-01  1.34713128e-01\n", " -1.00466244e-01 -2.05095708e-01 -2.18045965e-01  7.65073672e-02\n", "  1.20935753e-01 -7.30615631e-02 -9.69738606e-03 -1.28184184e-01\n", "  7.42633734e-03 -1.07595392e-01  3.68427038e-02 -1.22469746e-01\n", " -1.32881477e-01 -6.26747251e-01  1.46776587e-01 -1.61263555e-01\n", "  1.98654220e-01  3.76612335e-01 -4.98214960e-02  2.48004735e-01\n", " -1.82621516e-02 -1.96614355e-01 -4.59420890e-01 -1.78640142e-01\n", " -1.86885789e-01  3.29300046e-01  1.61171496e-01 -1.05811559e-01\n", "  3.48683625e-01 -3.70494090e-02 -3.74298729e-02 -7.38943145e-02\n", "  1.40884653e-01  3.54454666e-02 -8.43130872e-02 -2.85128076e-02\n", "  1.88740939e-01 -2.35779792e-01 -2.15804011e-01 -4.86416489e-01\n", "  4.98914152e-01 -5.17308474e-01  4.32137027e-02  1.22939236e-01\n", " -3.74168664e-01 -5.08825444e-02 -2.82784980e-02 -4.34548438e-01\n", "  1.81848660e-01 -1.22794151e-01  2.15487748e-01 -1.46668971e-01\n", "  4.33165967e-01  1.39656022e-01 -4.39864434e-02  1.19506590e-01\n", "  1.46237994e-02 -1.54717416e-01 -1.24597557e-01 -1.24384589e-01\n", " -3.30010266e-03 -1.24434501e-01  9.27280933e-02 -8.77554059e-01\n", " -2.24112824e-01  7.27861077e-02 -2.04590514e-01 -4.47212279e-01\n", " -4.96794544e-02 -7.82799423e-01  2.46126905e-01  1.42935887e-01\n", "  4.53603119e-02 -1.22086480e-01  2.11205289e-01 -2.83199787e-01\n", " -1.23965601e-02  4.23987895e-01 -1.45081535e-01  5.14208376e-01\n", "  2.27252945e-01  1.04301184e-01  9.23806727e-02  2.47600637e-02\n", "  1.44085882e-03 -1.12057060e-01  1.75629646e-01  3.28108013e-01\n", "  1.17256969e-01 -2.34253928e-02 -5.03238022e-01  3.04739505e-01\n", "  8.81242305e-02  3.18502426e-01 -2.05679417e-01 -9.39359218e-02\n", " -7.57100731e-02 -1.01259753e-01  2.59718150e-01 -3.32302690e-01\n", " -2.41666853e-01  1.71837315e-03  2.47147143e-01 -2.33400315e-01\n", " -4.03098851e-01  1.98494017e-01 -7.29607865e-02  2.99055248e-01\n", "  3.30540627e-01 -2.56050378e-01 -6.79503202e-01 -3.46591532e-01\n", " -1.68877736e-01 -1.39255077e-01 -1.92533717e-01 -4.75415498e-01\n", " -1.99264064e-01  3.84987704e-02 -6.57909662e-02 -1.86331138e-01\n", " -1.13302924e-01  1.19326462e-03 -1.68022171e-01  5.98706119e-02\n", " -2.16164201e-01 -2.69914776e-01 -1.79421932e-01  9.44435447e-02\n", "  5.84365204e-02 -2.50619233e-01 -1.82103917e-01  1.90542921e-01\n", "  3.08186680e-01  1.55596212e-01 -4.99591827e-02  3.83763433e-01\n", "  2.33968329e-02  1.93295211e-01  7.98918232e-02 -1.54381707e-01\n", "  1.25210643e-01 -2.02765122e-01  8.39155465e-02 -2.56686509e-01\n", "  1.18387051e-01  2.18956873e-01  2.47595564e-01  1.71502560e-01\n", "  2.09320381e-01 -3.15355472e-02  1.46863490e-01 -1.02022126e-01\n", "  4.84887920e-02 -1.65833235e-01  4.20550287e-01  1.21923881e-02\n", " -3.16756815e-01 -8.04712549e-02 -1.74587011e-01 -3.05973381e-01\n", " -1.42729461e-01  1.18731946e-01  3.36410016e-01 -1.80996403e-01\n", " -1.37335435e-01 -1.01519614e-01 -7.68816546e-02 -3.06621417e-02\n", " -4.55589741e-01 -3.47497091e-02  9.17371884e-02  4.34372723e-01\n", "  3.94577712e-01  1.49298698e-01  4.00080949e-01 -4.06447113e-01\n", "  1.06057562e-01 -3.26041169e-02  1.22695930e-01  7.38327503e-02\n", " -4.81932193e-01  1.09399445e-01  2.28992432e-01  2.13151619e-01]\n", "Document 7 embedding: [ 1.68697014e-01  3.71986598e-01  2.55523950e-01  2.40987748e-01\n", "  2.00748309e-01 -7.52293468e-02  2.76793659e-01  1.29457802e-01\n", "  1.55645579e-01  9.34780017e-02  4.04913612e-02  8.54637101e-02\n", " -2.16830671e-02  7.40957797e-01  1.11661740e-01 -3.44268084e-01\n", "  1.38037249e-01 -2.83598807e-02  6.38850480e-02 -2.02641152e-02\n", "  4.57675457e-02 -1.68287516e-01 -1.36568874e-01 -3.22236747e-01\n", " -1.63372859e-01 -2.86315531e-01 -1.39329538e-01  1.92778006e-01\n", " -1.58803090e-01  1.18005551e-01  8.27778354e-02  1.88372374e-01\n", " -1.18444577e-01  2.82949001e-01 -2.66082227e-01 -2.82468484e-03\n", "  4.39730614e-01 -2.32284889e-01 -5.62672138e-01  1.25702471e-01\n", " -3.61253887e-01  3.58688116e-01  1.05220281e-01  2.75373876e-01\n", " -2.92091072e-02 -1.08796787e-02 -1.74689189e-01 -3.45653683e-01\n", "  6.87861890e-02  1.58020765e-01  3.17735344e-01  1.05436109e-01\n", " -3.16500813e-01  3.57900649e-01 -3.56371969e-01  3.28451455e-01\n", "  1.58472992e-02 -2.76142091e-01 -3.55648994e-01  1.06736377e-01\n", "  3.24940413e-01  6.36930531e-03 -2.37944871e-01  3.70340079e-01\n", "  1.11716457e-01 -1.56167345e-02  5.43516695e-01  4.72034186e-01\n", "  7.64776673e-03  5.94046079e-02 -2.39216134e-01  1.91607445e-01\n", "  6.78435043e-02  3.20273459e-01  2.12551013e-01  5.48441291e-01\n", " -1.11448951e-01 -4.55604285e-01 -4.08188552e-01  8.23888123e-01\n", "  2.02333957e-01  2.74285346e-01 -2.50832379e-01  1.22087911e-01\n", "  7.45856762e-02 -2.21499994e-01 -3.18497181e-01 -5.64612031e-01\n", "  1.74168814e-02 -9.31747481e-02 -4.21842396e-01  3.71202320e-01\n", " -6.17657721e-01 -2.76229709e-01  1.62639663e-01 -1.45323858e-01\n", "  3.93770635e-01 -5.62484741e-01 -1.70227960e-01 -2.73414552e-01\n", " -4.26987588e-01  2.80129910e-01  6.65965915e-01  1.03852257e-01\n", " -5.47199249e-02  3.08804303e-01  1.92635190e-02 -5.00271916e-02\n", "  2.96099007e-01 -1.72060922e-01  2.50309885e-01  7.73544833e-02\n", "  3.55201036e-01 -1.47630736e-01  3.02324384e-01  2.93690234e-01\n", " -3.34001705e-02 -1.78318933e-01  2.02884842e-02  1.50679395e-01\n", "  8.01890567e-02  1.07155614e-01  3.63631666e-01  3.24666828e-01\n", " -1.07817256e+00  7.66613856e-02 -2.72001207e-01 -7.27771372e-02\n", "  8.30783993e-02  1.51476130e-01  1.60095215e-01  4.99832630e-01\n", "  6.03240468e-02 -7.15408862e-01  2.73206979e-01 -1.14507161e-01\n", "  3.00878435e-01  5.84894083e-02  2.36494049e-01  1.72874495e-01\n", "  5.49837612e-02 -1.34226650e-01 -3.20539564e-01  1.98915169e-01\n", " -2.22750947e-01  2.08686575e-01  2.59279963e-02 -3.87737453e-02\n", "  2.16509867e-02  2.75189150e-02  2.07821161e-01 -2.91210227e-02\n", "  4.52426642e-01  1.14294149e-01  1.26026077e-02  5.31750247e-02\n", "  1.29537955e-01 -1.76639661e-01 -3.38162929e-01 -9.20583159e-02\n", "  1.86139733e-01  1.16420262e-01  1.08698808e-01 -8.77387971e-02\n", "  3.21235768e-02  1.25488907e-01  5.39566576e-01  1.44729137e-01\n", "  1.97574701e-02  3.28083724e-01 -7.08531914e-03 -1.85818091e-01\n", " -7.65436888e-02  9.13978461e-03 -3.11779350e-01 -7.00687160e-05\n", "  9.03796311e-03 -5.26485503e-01  1.56364471e-01  3.11545748e-02\n", " -1.99618131e-01  2.04616055e-01 -1.27042383e-02  3.93154919e-01\n", "  2.69477461e-02  2.52339810e-01  7.48178139e-02  2.53547698e-01\n", "  2.67835796e-01 -2.30096608e-01 -1.30292207e-01  4.12596129e-02\n", " -1.42163783e-01  2.60408938e-01  2.44406149e-01 -5.10344915e-02\n", "  2.56256964e-02  2.41178140e-01  4.90851477e-02 -1.43399969e-01\n", " -2.20539235e-02  7.78708458e-02 -7.29997978e-02  8.18642139e-01\n", " -1.42152846e-01 -2.81279445e-01 -2.18265906e-01  1.17282107e-01\n", " -7.08301598e-03 -1.92759350e-01  1.19434111e-01 -4.11221415e-01\n", " -1.54813841e-01  2.14153528e-01  5.46078011e-02 -9.34776850e-03\n", "  1.09414361e-01  5.48250675e-02 -2.12095156e-01 -3.99622954e-02\n", "  1.60751734e-02  2.74627894e-01 -3.01620271e-02 -5.08369803e-01\n", "  3.67622554e-01 -2.43729316e-02 -1.15992285e-01  3.73964058e-03\n", " -1.01401277e-01 -1.58317089e-02  2.17326581e-02 -2.75370777e-01\n", "  9.04167518e-02 -3.18790019e-01  1.49331257e-01  2.19771340e-01\n", "  2.27342278e-01  2.02224148e-03  1.75319627e-01  1.02755800e-01\n", " -2.11972445e-01  3.35418172e-02  3.06575686e-01  3.14238101e-01\n", "  1.89802125e-01  3.51750702e-02 -1.07483171e-01 -2.04718426e-01\n", "  5.85626423e-01 -2.59483874e-01 -7.51072261e-03 -7.27070808e-01\n", "  3.99380215e-02  2.24811360e-01  4.68149692e-01 -5.53405941e-01\n", "  1.31332070e-01  4.64460030e-02  4.32123333e-01  3.75184983e-01\n", "  2.76058823e-01 -3.66318554e-01  1.31927624e-01 -1.99831158e-01\n", "  1.67579100e-01  3.05163115e-01  2.05754653e-01  3.04641664e-01\n", "  2.58512586e-01 -4.23439264e-01  2.23846570e-01  2.82707304e-01\n", "  1.55599803e-01  6.06654048e-01 -8.69311094e-02  1.59702867e-01\n", "  1.62098542e-01  1.49858192e-01 -4.78252709e-01  3.02093446e-01\n", " -8.96746069e-02 -2.86595881e-01 -3.66069764e-01 -1.00249849e-01\n", " -3.39969963e-01 -1.51263505e-01  2.09096998e-01 -1.14329316e-01\n", "  2.20681816e-01  9.97191202e-03 -1.97021402e-02 -3.42461407e-01\n", " -2.22738013e-01  1.06772162e-01  3.42367850e-02  6.03765190e-01\n", "  3.05909757e-02 -4.90600988e-02  7.16146603e-02  4.91561629e-02\n", " -1.28955439e-01  1.91194132e-01 -3.70919079e-01  3.47739428e-01\n", "  1.96919769e-01  3.16288769e-01  3.84666294e-01 -5.10112457e-02\n", " -3.44894022e-01 -8.51665214e-02 -3.55184644e-01 -1.93242073e-01\n", "  2.39157706e-01  3.76062730e-04  9.69590060e-03 -6.31810958e-03\n", "  2.35594422e-01  4.48838770e-02  2.39468459e-02  1.84919517e-02\n", "  1.95191696e-01  2.44793639e-01 -1.99681953e-01 -1.41164213e-01\n", " -1.29324704e-01  4.13767666e-01 -3.92763689e-02 -4.85166967e-01\n", " -7.99563974e-02 -3.52760047e-01  7.60351479e-01 -2.46153697e-01\n", " -1.77187711e-01  1.58377029e-02 -1.14188306e-01  3.14524978e-01\n", "  6.06092773e-02  1.98455229e-01  8.84377509e-02  4.55718823e-02\n", "  6.84862956e-02  8.48323330e-02 -2.68481016e-01 -1.57543898e-01\n", " -1.02358148e-01 -3.04109156e-01  2.75985688e-01  9.48046818e-02\n", "  4.92355740e-03 -3.39185178e-01 -3.62254046e-02  1.86692670e-01\n", "  2.57827967e-01  3.62570375e-01  1.31587058e-01 -5.10708272e-01\n", " -8.11066031e-02  1.83071777e-01 -6.15493990e-02  1.69756681e-01\n", " -3.34672630e-02  2.94208109e-01  4.32747975e-02  2.94006858e-02\n", "  5.54822445e-01  2.84978896e-01  3.39082219e-02 -5.81694007e-01\n", "  9.69276577e-03 -2.04991490e-01 -1.60529286e-01 -2.71715254e-01\n", "  2.08546877e-01  6.08559623e-02  2.05383882e-01  3.28454971e-01\n", "  4.22488116e-02 -3.26806515e-01 -3.23107243e-01 -2.79395849e-01\n", " -7.86901265e-02  6.40047016e-03  5.78195378e-02 -1.38257340e-01\n", " -2.58567184e-01  3.63171160e-01  2.91243255e-01 -3.35376024e-01\n", "  6.76055476e-02  1.28631264e-01  2.07487673e-01 -1.92179471e-01\n", " -4.61521745e-02 -1.55176282e-01  2.94455379e-01  1.76789433e-01\n", "  3.58037889e-01  3.54194254e-01  2.76605729e-02 -8.40234607e-02\n", " -8.77545029e-03 -8.17698613e-02 -1.93764180e-01  1.40309468e-01\n", "  1.67754024e-01  2.25244403e-01 -1.53350487e-01 -3.04129660e-01\n", "  3.43770355e-01 -1.80303112e-01 -4.05220054e-02  7.57012144e-02\n", " -1.19045243e-01 -5.80353709e-03  7.43180420e-03 -9.01698694e-02\n", "  2.29840547e-01  5.25456257e-02 -9.32861120e-02 -1.61530495e-01\n", "  2.00933456e-01  1.73029318e-01 -1.58110172e-01  1.91246435e-01\n", " -6.17806971e-01  1.29090831e-01 -6.06932461e-01  3.68281066e-01\n", "  2.61814147e-02  1.08751260e-01  2.19306815e-02 -3.04149896e-01\n", " -1.33394569e-01  2.60788202e-01  1.21266522e-01 -1.28639594e-01\n", "  2.08075121e-01 -2.36877233e-01 -3.49317133e-01 -2.81726748e-01\n", " -1.23907819e-01  4.82106991e-02  1.30051583e-01 -1.52305052e-01\n", " -4.90449257e-02 -2.19730526e-01 -5.23044527e-01 -2.85788447e-01\n", " -2.07386330e-01  6.54221252e-02 -1.74378723e-01 -2.82178849e-01\n", " -7.77752176e-02  6.22076452e-01 -4.40231226e-02  5.99010922e-02\n", "  1.99320689e-01  3.97493780e-01 -2.56361097e-01  4.19522859e-02\n", " -2.43523523e-01 -6.06387965e-02 -8.44035000e-02  1.99442402e-01\n", "  1.52829394e-01  8.55672136e-02  2.71485955e-01 -2.11486846e-01\n", "  1.51124731e-01 -4.86734100e-02  4.29240018e-01 -4.84391987e-01\n", " -1.13971464e-01 -4.26862426e-02 -2.88486063e-01  5.42138296e-04\n", " -3.67024213e-01 -2.14511350e-01 -2.01684877e-01 -1.93113565e-01\n", "  2.17957228e-01  2.70702749e-01 -4.60175239e-02 -3.77032250e-01\n", " -1.80732459e-01 -2.13929728e-01  3.69474620e-01  1.85709342e-01\n", " -1.59651786e-01  2.74901897e-01 -1.04361996e-01  7.01814651e-01\n", "  2.61698693e-01  1.28133506e-01  4.38401073e-01  1.41452909e-01\n", " -4.78317946e-01  1.62200794e-01  6.13336861e-01  3.98117363e-01\n", " -1.89829513e-01  6.37626722e-02 -6.48765713e-02  1.23282075e-01\n", " -7.90724978e-02  2.41675213e-01 -4.88725118e-02  8.88401419e-02\n", "  1.94455639e-01 -2.70560116e-01 -1.61139891e-01 -1.71287060e-01\n", "  4.68869478e-01  6.63365498e-02  1.12666674e-01  2.94407904e-01\n", " -7.29825348e-02 -5.37205860e-02 -2.07343493e-02  2.99381763e-01\n", "  2.46152580e-02  9.25778002e-02  8.56419131e-02  3.52828428e-02\n", "  6.39767498e-02 -6.16675280e-02 -2.39619732e-01 -2.13084608e-01\n", " -3.79230501e-03 -3.90214503e-01  1.36534825e-01 -4.00669992e-01\n", " -2.23358907e-02 -3.46135274e-02 -4.53009494e-02 -9.73383263e-02\n", "  2.90584534e-01 -6.83995290e-03  1.96393147e-01  1.44201413e-01\n", "  4.67006296e-01  1.31119743e-01  3.20674002e-01 -2.32522354e-01\n", "  2.15669665e-02  5.87046854e-02  1.51698785e-02  2.06448585e-01\n", " -7.19578117e-02 -2.37438530e-01 -2.85793394e-01  4.47055399e-01\n", "  2.79415888e-03  2.56609857e-01  4.39199097e-02 -2.44449228e-01\n", "  5.69480136e-02 -1.21185854e-02  1.19969979e-01  6.25797510e-02\n", " -5.43793514e-02  1.98430225e-01 -5.84568083e-02 -2.82056462e-02\n", " -6.84114873e-01  6.15739413e-02 -2.95648187e-01 -3.25070798e-01\n", "  6.12471215e-02 -7.94451311e-02 -1.06466874e-01  2.50140488e-01\n", "  5.58013581e-02  3.21460575e-01 -2.91539669e-01 -9.25641879e-02\n", " -2.63153017e-01 -2.56416708e-01 -1.88180223e-01 -3.12789716e-02\n", "  3.86389047e-01  2.99819767e-01  1.39195770e-01 -3.38486612e-01\n", " -9.80287567e-02 -5.01567304e-01 -2.48341545e-01  1.19735830e-01\n", "  1.44837618e-01  2.21778899e-01 -3.34841579e-01  4.88174260e-02\n", "  7.14954510e-02 -8.11251402e-02  5.17138243e-02 -1.93429776e-02\n", "  9.78944972e-02 -6.14999950e-01  1.78131625e-01  9.99108404e-02\n", " -1.70709062e-02  2.98567951e-01 -2.34081879e-01  1.61709875e-01\n", " -1.52258039e-01 -5.08134253e-02  6.88121095e-02 -2.81437248e-01\n", "  6.41248599e-02  5.82015812e-01 -1.01498319e-02  2.29458302e-01\n", "  2.32399181e-01  1.10457860e-01 -2.62023538e-01 -2.55061626e-01\n", " -7.46474043e-02  3.26663703e-01  8.68225172e-02  3.88362736e-01\n", "  4.00841683e-01 -1.07045449e-01 -1.63534299e-01 -1.93912864e-01\n", "  3.64387244e-01 -3.76567513e-01 -1.47777781e-01  1.66002110e-01\n", " -2.44572833e-01  1.04477167e-01  1.82056858e-03  1.62470434e-02\n", "  8.60171318e-02 -5.88439666e-02 -3.49487394e-01 -1.02718689e-01\n", "  1.45517111e-01  1.23048715e-01  1.07172094e-01 -1.28229499e-01\n", " -1.01667687e-01 -5.00302255e-01  1.49916619e-01 -3.67086023e-01\n", "  3.85125950e-02 -1.19975492e-01  1.47490634e-03 -4.20799524e-01\n", "  1.98562704e-02 -4.50372882e-02 -2.52483457e-01 -6.31687820e-01\n", "  3.32071111e-02 -5.91575980e-01  5.68285398e-02 -7.70940781e-02\n", " -4.10116106e-01  1.69124473e-02 -1.87023908e-01 -1.99121580e-01\n", "  8.81958231e-02  2.36390725e-01 -1.31039331e-02  1.81665555e-01\n", "  5.39424755e-02 -1.36059687e-01  2.12503642e-01  1.15116704e-02\n", " -1.27998352e-01 -1.83739495e-02 -1.54799730e-01 -1.29629569e-02\n", "  9.74167660e-02 -2.88553047e-03 -6.27900302e-01  1.04103368e-02\n", " -2.32766911e-01  6.53813779e-03  1.09381124e-03 -4.29373272e-02\n", "  7.67078400e-02  4.29773927e-02  4.37801987e-01 -2.27098197e-01\n", " -8.12455565e-02  1.94021896e-01 -8.15979615e-02 -2.69288808e-01\n", " -1.09766491e-01  1.58202067e-01  1.57948151e-01  1.49348006e-01\n", "  1.05640955e-01 -1.99159101e-01 -3.25488269e-01  2.11876184e-01\n", " -9.25540552e-02 -6.24556579e-02 -1.80040985e-01  1.86789125e-01\n", " -4.00587708e-01 -7.98472017e-02  2.38325700e-01 -3.42329405e-02\n", " -1.13599606e-01 -3.49425167e-01 -4.59083915e-03  2.63891518e-01\n", " -8.56983587e-02 -1.12460159e-01  9.88072827e-02 -8.75551850e-02\n", " -9.48584266e-03 -2.54048318e-01  2.30251208e-01  1.54899433e-01\n", " -2.04185724e-01  2.00550690e-01  6.70549646e-02  9.72847268e-02\n", " -1.61281422e-01  7.98821971e-02 -4.13672924e-02  1.36883751e-01\n", " -3.10590655e-01 -6.01519970e-03 -8.51873402e-03 -5.10341823e-01\n", " -1.32546648e-01  5.47655150e-02  1.05483346e-01  3.29933167e-01\n", "  4.88974946e-03  4.60286140e-02  4.09742355e-01 -2.59311497e-02\n", "  1.76105529e-01 -3.85665923e-01  7.12345960e-03  2.59944610e-02\n", "  1.55648440e-01 -1.03200506e-02  3.87568146e-01 -2.02115029e-01\n", " -7.91930407e-02  3.61820370e-01  1.11248322e-01 -1.00839972e-01\n", "  2.68748552e-01  2.95536280e-01 -1.49004623e-01  1.17922068e-01\n", " -3.76712054e-01  1.28392786e-01  5.10945857e-01  2.10376844e-01\n", "  4.07028764e-01  5.39574660e-02  2.80838192e-01 -1.59622654e-01\n", "  3.15446854e-01  3.80592287e-01 -3.69452722e-02 -1.32540897e-01\n", " -3.69358689e-01 -6.42839372e-02  2.03278899e-01  1.54272899e-01]\n", "Document 8 embedding: [ 0.33713755  0.12179309 -0.12275402  0.00132045  0.06015894  0.11401366\n", "  0.01136607  0.06097341  0.25486994 -0.10516952  0.03047739  0.03061133\n", "  0.08010934  0.5418789  -0.14324228 -0.09827847 -0.44437242 -0.29576674\n", "  0.08529232 -0.19042884 -0.10737104 -0.0596031  -0.25841352 -0.48839134\n", "  0.1460561   0.24175031  0.19295284  0.27185324 -0.38803175  0.37613392\n", " -0.07859587  0.0827793  -0.04703779  0.28302926 -0.06001858 -0.13538598\n", " -0.12402279  0.03283965  0.07093392 -0.06966732 -0.02087756  0.41898623\n", "  0.03786164  0.37990698 -0.14305378 -0.14125891 -0.54411393 -0.47124594\n", " -0.00601998  0.18291526 -0.26978233  0.1165937   0.04779964  0.0239257\n", "  0.04732656  0.24538776  0.07981305 -0.02077426  0.3863998  -0.10791964\n", "  0.09113296  0.04536107  0.13955429 -0.00786743 -0.06219992  0.03090459\n", "  0.01801541 -0.1964078   0.11367459  0.03573436 -0.25702268 -0.06817698\n", "  0.15213566  0.35581493  0.08358258 -0.17311023 -0.04755756 -0.4847698\n", " -0.20914054  0.35529742  0.12164157  0.159559   -0.4864588   0.07349689\n", " -0.01306096 -0.31800988 -0.07349356 -0.30656356  0.0034188  -0.10480324\n", " -0.3431683   0.14242505 -0.32712507  0.2272711  -0.13760762 -0.44157964\n", " -0.09573791 -0.7203744  -0.25637454  0.26363337 -0.01288556  0.2663022\n", "  0.5243153   0.03421656  0.07254053  0.585927    0.04670721 -0.03688986\n", " -0.13012007 -0.12426034  0.11313469  0.0266239   0.60319865 -0.1997771\n", "  0.05706332 -0.03865517  0.2591069  -0.07165485 -0.28688428  0.47520575\n", " -0.07320424  0.02932249 -0.07827295  0.4352646  -0.25833637 -0.09932776\n", "  0.16436899 -0.1238702   0.0245435   0.03060817  0.11468191 -0.2830915\n", "  0.1682735  -0.3896857  -0.22764577 -0.06367783  0.09656075  0.03015342\n", " -0.1064841   0.18842518 -0.15660106  0.0997981  -0.04052125  0.03918139\n", "  0.15137842  0.07407011  0.06975102 -0.10724269  0.18404332  0.12688307\n", "  0.11325087  0.18614851  0.03172395 -0.14790614 -0.10419848  0.094739\n", "  0.24681851 -0.1728768  -0.05469315  0.01195381  0.0886597  -0.00880133\n", "  0.33401385  0.15743412  0.32805642  0.07188811  0.2238853   0.40166387\n", " -0.04248109  0.11779397  0.02549875 -0.34197617 -0.24916455 -0.04567423\n", " -0.15916915  0.27207687  0.04353395 -0.12442058  0.07708217  0.02191613\n", " -0.15209916 -0.19799267  0.03861381  0.12422416  0.09749874  0.19717968\n", "  0.04861048  0.07171462  0.46463785  0.0954603   0.0840928   0.19365276\n", "  0.04946866  0.05210831  0.08305003 -0.02044442  0.19913964  0.30707487\n", "  0.23793648 -0.13912095  0.24564494 -0.24148841 -0.28405154  0.27080026\n", " -0.07546646 -0.27572525 -0.06047228  0.19032027  0.05414614 -0.16441686\n", "  0.05096055  0.1941262  -0.32354587  0.39121586 -0.39883804 -0.40728623\n", " -0.09994097  0.18846996  0.08222032  0.31314254  0.10019742 -0.11119849\n", "  0.18213202 -0.5445743   0.651087   -0.10318208 -0.25940582 -0.10057225\n", "  0.10344474 -0.20685366  0.08083568  0.48022604 -0.31251955  0.14528582\n", "  0.03772377 -0.21755937  0.19525515  0.05425128  0.03479901  0.11844824\n", "  0.2575738  -0.04202575  0.16402243 -0.11067853  0.2743081  -0.03265394\n", " -0.08360372 -0.22732653  0.26227495 -0.7784472  -0.05587849  0.09255431\n", "  0.13335104  0.04756852  0.48671052 -0.10786271 -0.09427307 -0.17544688\n", " -0.109387    0.68181187 -0.06439149 -0.3695283   0.1933293   0.08161867\n", " -0.07428597  0.06771497  0.07242141 -0.0020137   0.03252045 -0.23207043\n", "  0.26748994 -0.04086865  0.21107367 -0.00346648  0.09134997  0.05568549\n", " -0.02570193  0.06681005 -0.25558582  0.3139124  -0.03648485 -0.18300584\n", "  0.37730768  0.12216341  0.02537185  0.14501847  0.1238695  -0.46095178\n", "  0.39287424  0.43607384  0.26067182  0.06551071 -0.34330076 -0.03833202\n", "  0.01175497  0.46595746  0.07717874 -0.04249082  0.00586171  0.02501522\n", " -0.4143769   0.06633117  0.17852351  0.07884564  0.10682031  0.2314841\n", "  0.28490648  0.15248291 -0.09149861 -0.1351145  -0.10757955 -0.01187858\n", "  0.26286632  0.03794624 -0.22675608  0.1106925   0.00270926  0.3889815\n", " -0.06832606 -0.10164777  0.01445053 -0.04726291  0.01748444 -0.06469926\n", "  0.44576523  0.00661957  0.11219864 -0.8020688   0.08721974 -0.1708733\n", "  0.26992092 -0.21626706 -0.12298977  0.22031128 -0.02209348  0.31577137\n", " -0.13026308  0.05323326 -0.02396862 -0.12394606  0.2541888   0.19287862\n", " -0.16742101  0.166205   -0.17690778  0.0694199  -0.1286335   0.11333072\n", "  0.07105321 -0.09972813 -0.2959605   0.14674197  0.2438301  -0.08270095\n", " -0.10105079 -0.21326455  0.03609167  0.10118075  0.08446086  0.12336596\n", "  0.00880079  0.23297343 -0.15908888  0.10707169  0.3494452   0.13554998\n", " -0.18130423 -0.23156567 -0.03250493 -0.09869741 -0.04388818 -0.12085851\n", "  0.10841512  0.07447441  0.2150822   0.06979849 -0.05642657  0.08372048\n", " -0.19400838 -0.2143065   0.16164841 -0.01878054  0.03162381 -0.07901437\n", " -0.21420546  0.4337745   0.44861168 -0.19310519 -0.1579059  -0.04807857\n", " -0.07613904  0.16008142 -0.13075289 -0.08981099  0.12603335 -0.21045785\n", "  0.15725435 -0.3236472  -0.06191717  0.16761075 -0.10488866 -0.20070124\n", "  0.24728055  0.4626334  -0.04588989  0.03400848 -0.01918555 -0.10203184\n", "  0.16890237  0.16896425  0.10382783  0.15634665 -0.06020711 -0.133038\n", "  0.29097867 -0.38907072  0.3544452   0.4325802   0.20670235  0.06106158\n", "  0.38858113  0.04790066 -0.04488267 -0.03631592 -0.10355266  0.10362557\n", " -0.3732212   0.28520465 -0.3820458   0.19022831 -0.12658116 -0.09386365\n", "  0.10149884  0.14700355 -0.00874336 -0.20774293  0.21172926 -0.1724623\n", "  0.049248   -0.10112528  0.23778094  0.33791044 -0.05425435 -0.00737692\n", " -0.22006719  0.06787065 -0.52687114 -0.16429257 -0.4072621  -0.05875706\n", "  0.2085395   0.2516137  -0.01634122  0.02570122 -0.13711265  0.03366564\n", " -0.05176418  0.17913036 -0.11243354  0.04372338  0.06641123 -0.14346555\n", " -0.19191585  0.06585695 -0.12520996  0.27710193  0.25655156 -0.10712184\n", "  0.12742265  0.14862116  0.05260736 -0.2765364  -0.07250626  0.07116728\n", " -0.28479582  0.12495517  0.1541615  -0.26229528  0.06460321  0.07664413\n", " -0.0588579   0.00604315  0.05649801 -0.12808035  0.00697683  0.0902705\n", "  0.17484088  0.04698777 -0.24527884  0.05675308 -0.05999946  0.34637308\n", "  0.05822825  0.24177933 -0.05108193  0.07402003 -0.20550492  0.00296421\n", " -0.21773846  0.19662595  0.0653305   0.31502756  0.02885546  0.28366363\n", "  0.00250306 -0.01636691  0.0012738   0.11433501  0.02340568 -0.06474621\n", " -0.27079633  0.14668539  0.14931776  0.11698214  0.17175971 -0.00443144\n", "  0.00714748  0.2445413   0.21147336 -0.04738257  0.02456154  0.11474783\n", "  0.1064769   0.0898632   0.03844183  0.03595152  0.09591397  0.02486119\n", " -0.03458195 -0.4930306  -0.14034681  0.02813642 -0.20035617 -0.27425006\n", "  0.51397973 -0.09232432  0.05603102 -0.06261123  0.5142584  -0.14825466\n", "  0.19053507 -0.29753977  0.32990965  0.10288436 -0.07656957  0.01450445\n", " -0.45842737  0.20594044 -0.05437839  0.02416405  0.3475595   0.5247017\n", "  0.01659987 -0.22484961  0.51766366 -0.17166142 -0.17004618  0.03695673\n", "  0.14231288  0.07527496  0.27697185  0.8908468  -0.00873908  0.20637915\n", " -0.19614    -0.10834444 -0.32842463 -0.07850175  0.05964875 -0.02507236\n", "  0.16054188  0.1817942  -0.05468275  0.04541858 -0.22558117 -0.1939226\n", "  0.07840697 -0.25631964 -0.02870524  0.1910208   0.33445835  0.17219467\n", "  0.26626864  0.30679527  0.09047625  0.02801918 -0.34097624  0.32987565\n", "  0.26836842 -0.23288332 -0.06162088 -0.02900286 -0.18153404 -0.25059602\n", " -0.22540714  0.02490996 -0.11740891 -0.18656792  0.16083057 -0.21389784\n", "  0.22280572  0.28649917 -0.0199594  -0.10074568 -0.3914736  -0.2000793\n", " -0.04285717 -0.08939208 -0.21154802  0.3631096   0.07601619  0.15722762\n", "  0.21577819 -0.08347358 -0.17578985  0.02704147 -0.00787257  0.06492275\n", " -0.0121719   0.24753346  0.20055915 -0.26592395 -0.13944396 -0.07944504\n", " -0.01879144 -0.30468178  0.29421946  0.21600164 -0.06347958  0.02422603\n", "  0.09566093 -0.39259413  0.13893914 -0.218836   -0.06611771 -0.16166207\n", "  0.37754768  0.18751992  0.02368368 -0.05009735  0.25522244 -0.50240767\n", " -0.12178718 -0.11269888  0.02772415  0.02096085  0.18817608 -0.4688504\n", " -0.23916149 -0.00754179 -0.06431629 -0.37037286  0.11172251 -0.67929125\n", "  0.10399321  0.04070815 -0.00879031 -0.24337249  0.09455943  0.05962254\n", "  0.10799458  0.37230057 -0.31204134  0.2598206   0.421694   -0.05289252\n", "  0.16630022 -0.01526158  0.04502039  0.09030562 -0.11218003  0.30643454\n", "  0.11531483 -0.02301344 -0.4224297   0.03471109  0.19292463  0.18179138\n", " -0.04772146  0.14362955 -0.22913167 -0.02558379  0.33252048 -0.04300957\n", "  0.19593486 -0.04917775  0.00875786 -0.16296919 -0.01916161 -0.2205124\n", " -0.11063919  0.22919652  0.19361475 -0.04800059 -0.29150695 -0.2300524\n", "  0.0367591  -0.09415011 -0.23430838 -0.4252646  -0.1002996   0.03483565\n", "  0.04885751 -0.30034706 -0.1297635   0.10202743  0.02530895  0.07047857\n", " -0.07625493 -0.39449936 -0.20332423  0.14383724  0.06338537 -0.06719814\n", " -0.14170323 -0.11196347  0.3014033   0.1668737   0.0890739   0.20248641\n", " -0.3379465   0.12678097  0.09072071  0.08108     0.08028819 -0.14921378\n", "  0.04779908 -0.13997304  0.14713171  0.12914762 -0.00859688  0.57106096\n", "  0.17728816  0.09324724  0.05360301  0.3949757  -0.04864933  0.04122144\n", "  0.24576423 -0.28701815 -0.148674    0.21887527  0.11038782 -0.35625336\n", " -0.09344506  0.37657884  0.24214603 -0.09156068 -0.4850804  -0.00709688\n", " -0.01640138 -0.19995366 -0.22480866 -0.04866279  0.36462507  0.14198492\n", "  0.6309419  -0.07466953  0.520354   -0.19083916 -0.06907247 -0.02531717\n", " -0.02685022 -0.00101471  0.18715787  0.17741649 -0.00627339  0.20187055]\n"]}]}, {"cell_type": "code", "source": ["documents"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "z29WzyItDX8x", "outputId": "7da471bd-609b-493b-d371-c23acc609bed"}, "execution_count": 14, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['This is a list which containing sample documents.',\n", " 'Keywords are important for keyword-based search.',\n", " 'Document analysis involves extracting keywords.',\n", " 'Keyword-based search relies on sparse embeddings.',\n", " 'Understanding document structure aids in keyword extraction.',\n", " 'Efficient keyword extraction enhances search accuracy.',\n", " 'Semantic similarity improves document retrieval performance.',\n", " 'Machine learning algorithms can optimize keyword extraction methods.']"]}, "metadata": {}, "execution_count": 14}]}, {"cell_type": "code", "source": ["query = \"Natural language processing techniques enhance keyword extraction efficiency.\""], "metadata": {"id": "1nQF36rhDA9_"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["query_embedding = model.encode(query)"], "metadata": {"id": "bJatNM_4Da5y"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": ["print(\"Query embedding:\", query_embedding)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZxQf2v2TDc3I", "outputId": "1269d99d-8489-44ab-9782-b8b4cbcf9f02"}, "execution_count": 18, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Query embedding: [ 3.42821181e-01  3.26170564e-01  7.73251383e-03  1.60759035e-02\n", "  2.38518156e-02 -2.52880514e-01  8.15469772e-02  1.37177318e-01\n", "  3.33291799e-01  3.65743041e-02 -9.28227529e-02 -3.43726009e-01\n", "  7.12015256e-02  9.48833704e-01 -9.41289738e-02 -7.96414435e-01\n", " -3.20592433e-01 -2.67564565e-01  2.08664417e-01  7.99598247e-02\n", "  7.93347415e-03 -5.22295274e-02 -1.00512497e-01 -2.04229861e-01\n", "  2.19562605e-01  7.67739043e-02  1.25881582e-01  2.90843934e-01\n", " -4.99190986e-01  2.27709264e-01 -1.94742545e-01  7.21396282e-02\n", " -4.56500938e-03  1.21945657e-01 -1.46225825e-01 -9.64743719e-02\n", "  1.02258191e-01 -2.04421282e-01 -3.95658046e-01  7.32254162e-02\n", "  1.10103995e-01  4.84611303e-01  1.04519367e-01  3.86131942e-01\n", " -1.68697596e-01 -2.11251289e-01 -6.38036489e-01 -4.32641655e-01\n", "  2.74459004e-01  9.64056328e-02 -1.30053475e-01  5.76567650e-02\n", "  8.07964280e-02  6.48148209e-02 -2.14203727e-03  2.29773983e-01\n", " -6.66051432e-02  3.07675213e-01  2.11779296e-01 -1.57844439e-01\n", "  9.78859738e-02 -4.04545158e-01  4.61967736e-02 -3.20859738e-02\n", "  1.32365286e-01  3.01010579e-01 -1.32480701e-02 -2.67456591e-01\n", "  1.12665044e-02  4.68799993e-02 -1.80944264e-01  6.95835501e-02\n", "  1.60843447e-01  2.16982871e-01  1.64339930e-01 -1.74264535e-01\n", " -8.52752551e-02 -7.39663005e-01 -1.36380270e-01  4.56657857e-01\n", "  2.55824834e-01  1.87865242e-01 -2.87730545e-01 -6.57529905e-02\n", " -8.18324760e-02  1.06182091e-01 -1.90890819e-01 -3.63865346e-01\n", "  4.33045812e-02 -1.74884632e-01 -1.14985228e-01  9.89503711e-02\n", " -6.41397178e-01  1.05573654e-01 -1.11794196e-01 -5.29215395e-01\n", " -2.64178533e-02 -8.46410632e-01 -1.79423228e-01  5.69301210e-02\n", " -6.22964025e-01  3.68571490e-01  3.78759652e-01  3.49384427e-01\n", "  9.77627113e-02  2.56185889e-01 -5.00986725e-02 -2.54375264e-02\n", "  9.74192470e-02 -1.04563467e-01  3.08166057e-01 -8.58174730e-03\n", "  5.57814658e-01 -1.60096347e-01  1.19149968e-01 -4.85647395e-02\n", "  7.98248202e-02 -2.00571790e-01 -1.49871483e-01  5.08434117e-01\n", " -2.34897286e-01  1.81867048e-01  3.91888261e-01  6.05367362e-01\n", " -4.28476810e-01 -6.13591671e-02  1.33172050e-01 -1.47416042e-02\n", "  1.36126101e-01  9.11059380e-02  1.24083415e-01 -4.19148952e-01\n", "  9.05208960e-02 -4.87968922e-01 -2.65840232e-01 -5.25813475e-02\n", "  3.01154912e-01  2.99625825e-02  1.56982571e-01  1.32160708e-01\n", " -1.46008179e-01  6.35154098e-02 -4.96837497e-02  1.36892155e-01\n", " -3.09415549e-01  4.30748969e-01 -9.36299097e-03 -2.02480137e-01\n", " -2.58504245e-02  1.81920707e-01  1.51114985e-01  3.33720058e-01\n", "  2.76331566e-02  4.88269329e-02  1.50152430e-01  8.62696022e-02\n", "  2.90989071e-01 -1.69608220e-01 -2.10309491e-01 -9.43666026e-02\n", "  1.02815099e-01  1.19826913e-01  1.28988340e-01  2.19912827e-01\n", "  3.03075910e-01  1.02212220e-01  4.36704755e-01  6.84747815e-01\n", "  8.78548101e-02  4.24237847e-01  4.87482771e-02 -3.61062706e-01\n", " -3.73365760e-01 -2.06955537e-01 -2.18631923e-01  2.23735228e-01\n", "  1.45552695e-01 -1.64250750e-02  1.27087772e-01  1.50438294e-01\n", " -3.54754955e-01 -1.70345213e-02  5.32262139e-02  5.38887717e-02\n", "  1.23586774e-01  3.99657905e-01  6.01030849e-02 -2.05792561e-02\n", "  3.10624182e-01 -3.59872580e-02  1.77981794e-01  8.87050480e-02\n", "  7.22305477e-02  2.66187072e-01  3.00868928e-01 -3.63038510e-01\n", "  1.11119203e-01  2.56306708e-01  2.70661026e-01 -2.86072314e-01\n", " -6.86140656e-02  4.23483877e-03 -1.54327601e-01  5.86235464e-01\n", "  8.86248201e-02 -1.41954601e-01 -1.01986304e-01  1.98329151e-01\n", " -1.89840332e-01 -1.50765687e-01  4.85557951e-02 -1.98109701e-01\n", " -3.80640149e-01  5.95760107e-01  4.43344079e-02 -1.16062328e-01\n", " -2.93755122e-02 -2.01710835e-02  5.68334937e-01  8.57430175e-02\n", "  9.61901993e-02  2.86781155e-02  3.25626791e-01 -4.44512874e-01\n", "  7.34137595e-01 -3.79980654e-02 -7.64805898e-02 -7.63305053e-02\n", "  4.29921485e-02  5.80683090e-02 -4.47809733e-02 -1.06759697e-01\n", "  9.69007462e-02  5.57168089e-02  4.58754264e-02  3.32130343e-02\n", "  1.32275492e-01 -9.28025618e-02  3.29209000e-01  6.57105073e-02\n", "  5.98659635e-01 -1.37529433e-01  2.09896252e-01  2.52406776e-01\n", "  2.08480746e-01 -3.70731577e-02 -1.86414734e-01 -5.95779479e-01\n", "  5.02461791e-01 -6.15786195e-01 -1.09123826e-01 -6.65532202e-02\n", "  1.29821301e-01  1.17169902e-01  6.33773088e-01 -7.69985467e-02\n", " -1.51039034e-01  6.32343218e-02  2.14052141e-01  6.35673225e-01\n", "  6.44221231e-02 -3.70872676e-01  5.18233061e-01 -2.69450769e-02\n", " -9.72328708e-02  2.61310071e-01  2.67686620e-02  4.76237722e-02\n", "  9.04489681e-02 -1.66186437e-01  1.38177469e-01  1.71526939e-01\n", "  2.11233482e-01  1.08950466e-01  6.30202815e-02  8.45885798e-02\n", "  3.79931808e-01  3.28583181e-01 -5.82527220e-01  2.71035880e-01\n", " -2.19614804e-02 -1.29058436e-01  1.22674353e-01  2.95920193e-01\n", "  6.37544096e-02  1.78494588e-01  7.15337843e-02 -2.42942274e-01\n", "  4.51877147e-01  2.69141078e-01  1.76798031e-01  8.57900176e-03\n", " -5.06439447e-01 -1.52215585e-01 -8.13298970e-02  6.51896894e-01\n", " -6.50532842e-02 -1.67736515e-01  2.08840594e-02 -2.27739036e-01\n", " -1.56746417e-01 -1.11661345e-01  5.74863404e-02  6.99482039e-02\n", "  4.19288836e-02  3.37024003e-01  6.79772615e-01  1.02909595e-01\n", " -1.27214983e-01 -5.36733344e-02 -7.69102871e-02  5.24525391e-03\n", "  1.89192772e-01 -1.22298628e-01  4.55188192e-03  1.89226139e-02\n", " -2.93659773e-02  3.94762099e-01 -7.48027563e-02 -2.76816159e-01\n", "  1.65402561e-01  2.42076889e-02 -1.68769509e-01  4.94704060e-02\n", "  1.96057651e-02  2.38465622e-01  3.71787578e-01 -4.48410183e-01\n", " -6.09918386e-02 -2.17366710e-01  3.71027350e-01 -3.01229447e-01\n", " -2.30238289e-01  3.56197916e-02  1.98712423e-02  1.46002233e-01\n", "  2.58541796e-02  5.21061979e-02 -2.50305057e-01 -4.03652787e-02\n", "  1.64214343e-01 -6.24707565e-02 -9.01159421e-02  1.22721963e-01\n", " -1.18520185e-01  1.06499299e-01 -4.76352833e-02 -6.48378059e-02\n", "  5.16053066e-02 -1.34922877e-01 -3.32493722e-01  1.49980366e-01\n", "  2.63114095e-01 -1.02513440e-01 -1.97372764e-01 -5.80664426e-02\n", " -1.23437054e-01 -7.53865317e-02 -1.68164477e-01  3.15384835e-01\n", "  6.18048804e-03  1.73367471e-01 -3.33714128e-01  3.28383982e-01\n", "  4.71371859e-01  1.32659107e-01 -2.51648039e-01 -3.58769864e-01\n", " -4.09126095e-02 -2.48054430e-01 -7.71367028e-02 -6.27740324e-02\n", "  2.27572709e-01  3.53151597e-02  2.28378132e-01  2.25965023e-01\n", "  3.75651382e-02  7.97821581e-02 -2.67998248e-01 -2.88364321e-01\n", "  1.21273004e-01 -4.21771174e-03  8.10203627e-02 -1.65806293e-01\n", " -9.47970003e-02  3.65413934e-01  5.95796645e-01  1.46871343e-01\n", " -4.33355570e-03  1.45572037e-01  2.53966361e-01 -2.06314802e-01\n", " -9.96618122e-02 -2.30036512e-01  7.77852684e-02 -1.13770671e-01\n", "  1.86986074e-01 -3.54869574e-01  2.86667436e-01 -7.34788328e-02\n", "  2.49851122e-02 -1.10818502e-02  3.04064043e-02  4.76232320e-01\n", " -1.72916993e-01  2.89377987e-01 -8.37589055e-02 -3.42667162e-01\n", " -5.47049977e-02  2.30727971e-01 -2.52590552e-02 -4.83039990e-02\n", " -2.71303475e-01  1.96077861e-02  1.55769676e-01 -3.98244560e-01\n", "  5.64417303e-01  1.95435375e-01  4.80872959e-01  5.06067686e-02\n", "  4.62356627e-01  2.61315815e-02 -5.80524951e-02 -5.23831509e-02\n", " -2.17455551e-02  2.54892595e-02 -4.38398093e-01  3.54009032e-01\n", " -2.52778351e-01  1.68167412e-01  8.77701193e-02 -2.19432175e-01\n", "  1.85336009e-01  9.37575847e-02  6.25716671e-02 -1.67173892e-01\n", "  1.24475665e-01 -2.85737604e-01 -2.25994573e-03 -5.78572303e-02\n", "  3.29644829e-01  7.39951804e-02  2.72959936e-02 -3.18309963e-01\n", " -3.18794847e-01 -4.88582514e-02 -4.59834903e-01 -1.35861486e-01\n", " -3.64056379e-01 -2.00262368e-01 -9.53305662e-02  1.11736618e-01\n", "  5.69353253e-02 -2.49188440e-03 -3.16785537e-02  2.37123281e-01\n", "  1.43027142e-01  1.69401318e-01 -6.37499467e-02 -1.33482695e-01\n", "  6.47734702e-02 -2.11512163e-01 -2.22971290e-01 -8.10671970e-02\n", "  1.37273133e-01  1.71503007e-01  3.76601666e-01 -2.52396077e-01\n", "  6.75323308e-02  8.20795000e-02  2.50019014e-01 -3.94603908e-01\n", " -1.33266822e-01  4.46400568e-02 -3.67893904e-01 -3.19990925e-02\n", "  2.59161025e-01 -3.22783470e-01 -7.54938871e-02  1.32091455e-02\n", "  9.32448655e-02  6.67383522e-02  1.86795015e-02 -2.93797195e-01\n", "  7.08250143e-03 -9.24767926e-02  1.23357780e-01  1.35075636e-02\n", " -4.28227633e-01  1.42874584e-01 -8.34424645e-02  1.05039157e-01\n", "  2.95196678e-02  2.19158575e-01  9.86325294e-02 -5.62896170e-02\n", " -4.26228106e-01 -3.18960808e-02 -2.86903203e-01  3.94104958e-01\n", " -2.22178232e-02  1.45775616e-01 -1.52241066e-01  2.03591853e-01\n", " -7.13582209e-04  9.39149484e-02  2.79404335e-02  8.49645585e-02\n", "  1.24964900e-01  2.30420195e-02 -4.28128503e-02  3.13862175e-01\n", "  2.87980646e-01  1.70080841e-01  2.45663717e-01 -1.18184455e-01\n", " -1.37107611e-01  1.81748569e-01  4.41686779e-01 -2.39037290e-01\n", " -7.59362578e-02  1.50283501e-01  2.41503969e-01  2.44798586e-01\n", "  3.01485248e-02 -3.27506545e-03  2.89070427e-01  1.98940746e-02\n", " -3.04150973e-02 -8.87121260e-02  7.94830248e-02  9.98786241e-02\n", " -2.61719584e-01 -2.33500630e-01  1.69688910e-01 -9.00555104e-02\n", " -9.33134556e-02 -1.26774848e-01  1.83930084e-01 -7.09594786e-02\n", "  3.23752910e-01 -1.88386276e-01  1.66340932e-01 -4.41877060e-02\n", " -2.14285925e-01  5.60678169e-02 -5.58133066e-01  2.34487861e-01\n", " -2.82568615e-02  1.50307983e-01  6.09639101e-02  7.10646391e-01\n", " -5.48093282e-02  3.97793092e-02  3.40507627e-01 -2.92045087e-01\n", " -1.96878031e-01  2.33161598e-01 -9.75959450e-02  1.80478603e-01\n", "  1.51456088e-01  1.81774214e-01  3.27396742e-03  9.06809494e-02\n", "  5.78261651e-02 -1.57398153e-02 -4.57446069e-01 -1.26865670e-01\n", "  5.54023385e-02 -2.69046910e-02 -6.46368833e-03  9.65187550e-02\n", " -6.67992383e-02 -6.21608719e-02 -4.27822262e-01 -2.54691482e-01\n", "  2.06147268e-01 -3.21782529e-01 -4.83700603e-01  1.65552065e-01\n", "  3.71659696e-01  7.46784061e-02  9.60726738e-02  1.96236879e-01\n", " -1.37728408e-01 -4.05442387e-01 -6.30095363e-01  1.83377683e-01\n", "  2.20763177e-01  1.29253119e-01 -2.64483783e-02  1.28809810e-01\n", " -6.94139376e-02 -2.19108135e-01 -1.00885615e-01  1.92564175e-01\n", "  8.96308497e-02 -4.08931315e-01  2.54913986e-01 -1.26347825e-01\n", "  2.82889158e-01  5.14621496e-01 -1.06570974e-01  3.10878694e-01\n", " -5.37391864e-02 -1.52135506e-01 -2.53310204e-02 -2.48367056e-01\n", " -3.87963913e-02  4.33812648e-01 -3.72435488e-02 -1.22521929e-01\n", "  2.61050165e-01 -4.72593179e-04 -1.26758233e-01 -1.27047002e-01\n", "  6.07044287e-02  3.61429334e-01 -1.11900769e-01  2.77994052e-02\n", "  2.96374887e-01 -3.29669416e-01 -3.81410897e-01 -6.20655060e-01\n", "  3.44295293e-01 -4.10001785e-01  3.06075722e-01  3.05802692e-02\n", " -1.06001087e-01 -4.74850135e-03  2.78320849e-01 -2.86580831e-01\n", "  6.65059388e-02 -1.29662573e-01 -1.61715057e-02 -1.60481513e-01\n", "  4.67304528e-01  1.19920932e-01 -2.47418098e-02  5.22715226e-02\n", "  9.62850526e-02 -4.96304065e-01 -1.11981116e-01 -2.18703017e-01\n", " -3.83874699e-02  1.52137369e-01 -3.20620686e-02 -2.71888584e-01\n", " -3.86748761e-01 -5.20923696e-02 -1.83613673e-01 -4.52486485e-01\n", " -1.02377146e-01 -8.72911751e-01  7.43954927e-02  6.87793493e-02\n", " -6.02248073e-01 -1.18486367e-01  1.59249026e-02 -5.49740531e-02\n", "  1.10318609e-01  2.69724160e-01 -2.52206683e-01  3.19937199e-01\n", "  9.88895893e-02  5.11256605e-02 -2.68779900e-02 -1.34564564e-01\n", "  1.90518219e-02 -1.32577688e-01  5.10082170e-02  5.56460977e-01\n", "  3.44424099e-01 -9.63326991e-02 -4.62864131e-01  2.23615035e-01\n", "  1.54023720e-02 -6.58698604e-02 -2.43841901e-01 -4.22396511e-02\n", " -2.28394002e-01 -2.14719791e-02  6.13314271e-01 -4.03998554e-01\n", "  2.51582414e-01 -1.07154325e-01  7.65936673e-02 -2.36006513e-01\n", " -3.70901227e-01  6.35230690e-02  2.88644195e-01  3.45492005e-01\n", "  1.94970459e-01 -3.92094016e-01 -6.02980673e-01 -8.82573426e-02\n", " -9.36844498e-02 -1.34990692e-01 -3.57475340e-01 -1.44787535e-01\n", " -3.11221749e-01 -1.62908662e-04  1.10207595e-01 -2.37016678e-01\n", " -1.63779899e-01  1.58076063e-01  1.43431174e-02  1.41231000e-01\n", " -1.98302850e-01 -3.64524871e-01 -2.54462719e-01  4.10534590e-02\n", " -4.76663634e-02 -1.48341268e-01  5.31598665e-02  1.65342707e-02\n", "  3.86232823e-01  3.71005237e-01 -3.41344535e-01  2.47474641e-01\n", " -1.21335983e-01  3.70174199e-01  5.07940277e-02 -1.17685638e-01\n", "  1.28718197e-01 -3.12221676e-01  3.31888422e-02 -2.21366644e-01\n", " -2.19745822e-02  2.34993681e-01  2.13648394e-01  6.20998979e-01\n", "  2.69672036e-01  2.24654805e-02  2.51860261e-01 -8.58072490e-02\n", " -9.00257453e-02 -1.99066505e-01  4.03987378e-01 -1.95194721e-01\n", " -1.71065345e-01  1.01183698e-01  2.71370918e-01 -2.80465603e-01\n", " -1.75431341e-01  2.31305823e-01  3.62832278e-01  3.07524391e-02\n", " -3.54172975e-01 -1.47730783e-01 -1.68192759e-01 -1.90171227e-01\n", " -1.16841555e-01 -1.51364028e-01  6.80082142e-02  3.64318907e-01\n", "  8.47872376e-01  5.88235594e-02  5.89767873e-01 -2.12717056e-01\n", "  1.63261041e-01  1.62782699e-01  2.08858266e-01  1.12896882e-01\n", "  1.85328230e-01  1.93972975e-01  1.38236731e-01  4.02512550e-01]\n"]}]}, {"cell_type": "code", "source": ["len(query_embedding)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "161pAXWNE6Ch", "outputId": "6e81b715-5a8c-4aed-daf1-8f366e6ac0c8"}, "execution_count": 22, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["768"]}, "metadata": {}, "execution_count": 22}]}, {"cell_type": "code", "source": ["import numpy as np\n", "from sklearn.metrics.pairwise import cosine_similarity"], "metadata": {"id": "4dZjQsGDDj5m"}, "execution_count": 19, "outputs": []}, {"cell_type": "code", "source": ["similarities = cosine_similarity(np.array([query_embedding]), document_embeddings)"], "metadata": {"id": "Rcf5V3I7Dp-B"}, "execution_count": 20, "outputs": []}, {"cell_type": "code", "source": ["similarities"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sfk1qPUeDt_l", "outputId": "9795635a-1773-4f29-96dd-eab6c337cf5e"}, "execution_count": 21, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[0.16948138, 0.45802268, 0.56756926, 0.44123274, 0.6316117 ,\n", "        0.7521413 , 0.5503519 , 0.74481654]], dtype=float32)"]}, "metadata": {}, "execution_count": 21}]}, {"cell_type": "code", "source": ["most_similar_index = np.argmax(similarities)"], "metadata": {"id": "L_vQO9WoDvLF"}, "execution_count": 23, "outputs": []}, {"cell_type": "code", "source": ["most_similar_index"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kstZiJpZFKLe", "outputId": "63f259e1-4fa0-432b-eecb-b7575f064f77"}, "execution_count": 24, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["5"]}, "metadata": {}, "execution_count": 24}]}, {"cell_type": "code", "source": ["most_similar_document = documents[most_similar_index]"], "metadata": {"id": "NQiqJlgNFLHn"}, "execution_count": 25, "outputs": []}, {"cell_type": "code", "source": ["most_similar_document"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "ghiLFqGEFPxn", "outputId": "13def6c9-0535-4b37-a51a-c58199e973ed"}, "execution_count": 26, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Efficient keyword extraction enhances search accuracy.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 26}]}, {"cell_type": "code", "source": ["query"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "J0WDD1hvFQ62", "outputId": "6decb463-f85d-4720-f866-2eabd198767d"}, "execution_count": 27, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Natural language processing techniques enhance keyword extraction efficiency.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 27}]}, {"cell_type": "code", "source": ["similarity_score = similarities[0][most_similar_index]"], "metadata": {"id": "l0FK7NeAFR2V"}, "execution_count": 28, "outputs": []}, {"cell_type": "code", "source": ["similarity_score"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vVYM05pnFa6I", "outputId": "bb851a47-2724-43c2-b079-2d7f822435ea"}, "execution_count": 29, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0.7521413"]}, "metadata": {}, "execution_count": 29}]}, {"cell_type": "code", "source": ["sorted_indices = np.argsort(similarities[0])[::-1]"], "metadata": {"id": "0MDWnmaWFbwm"}, "execution_count": 61, "outputs": []}, {"cell_type": "code", "source": ["sorted_indices"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2ANc52lQFizG", "outputId": "d98ed7fc-8c1c-4cef-8d73-64a3203a46b7"}, "execution_count": 31, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([5, 7, 4, 2, 6, 1, 3, 0])"]}, "metadata": {}, "execution_count": 31}]}, {"cell_type": "code", "source": ["ranked_documents = [(documents[i], similarities[0][i]) for i in sorted_indices]"], "metadata": {"id": "K3ydRBXWFj0N"}, "execution_count": 32, "outputs": []}, {"cell_type": "code", "source": ["ranked_documents"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SC6o7OhYFrC_", "outputId": "204cc6d6-aa63-4296-cc92-003002dc80be"}, "execution_count": 33, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[('Efficient keyword extraction enhances search accuracy.', 0.7521413),\n", " ('Machine learning algorithms can optimize keyword extraction methods.',\n", "  0.74481654),\n", " ('Understanding document structure aids in keyword extraction.', 0.6316117),\n", " ('Document analysis involves extracting keywords.', 0.56756926),\n", " ('Semantic similarity improves document retrieval performance.', 0.5503519),\n", " ('Keywords are important for keyword-based search.', 0.45802268),\n", " ('Keyword-based search relies on sparse embeddings.', 0.44123274),\n", " ('This is a list which containing sample documents.', 0.16948138)]"]}, "metadata": {}, "execution_count": 33}]}, {"cell_type": "code", "source": ["query"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "XKIQNhJ2FsCd", "outputId": "51c38f25-a8c8-4c06-cdf0-dc1cf7513634"}, "execution_count": 34, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Natural language processing techniques enhance keyword extraction efficiency.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 34}]}, {"cell_type": "code", "source": ["print(\"Ranked Documents:\")\n", "for rank, (document, similarity) in enumerate(ranked_documents, start=1):\n", "    print(f\"Rank {rank}: Document - '{document}', Similarity Score - {similarity}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fyEQPRNaFwDn", "outputId": "58ac9e6f-8ab1-4c61-baa9-84fab33a3d36"}, "execution_count": 35, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Ranked Documents:\n", "Rank 1: Document - 'Efficient keyword extraction enhances search accuracy.', Similarity Score - 0.7521412968635559\n", "Rank 2: Document - 'Machine learning algorithms can optimize keyword extraction methods.', Similarity Score - 0.7448165416717529\n", "Rank 3: Document - 'Understanding document structure aids in keyword extraction.', Similarity Score - 0.631611704826355\n", "Rank 4: Document - 'Document analysis involves extracting keywords.', Similarity Score - 0.5675692558288574\n", "Rank 5: Document - 'Semantic similarity improves document retrieval performance.', Similarity Score - 0.5503519177436829\n", "Rank 6: Document - 'Keywords are important for keyword-based search.', Similarity Score - 0.45802268385887146\n", "Rank 7: Document - 'Keyword-based search relies on sparse embeddings.', Similarity Score - 0.44123274087905884\n", "Rank 8: Document - 'This is a list which containing sample documents.', Similarity Score - 0.16948138177394867\n"]}]}, {"cell_type": "code", "source": ["print(\"Top 4 Documents:\")\n", "for rank, (document, similarity) in enumerate(ranked_documents[:4], start=1):\n", "    print(f\"Rank {rank}: Document - '{document}', Similarity Score - {similarity}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "nIRnIQbbF6u4", "outputId": "2cbfeb6e-ded3-4187-eaf7-3e20d4a6a870"}, "execution_count": 36, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Top 4 Documents:\n", "Rank 1: Document - 'Efficient keyword extraction enhances search accuracy.', Similarity Score - 0.7521412968635559\n", "Rank 2: Document - 'Machine learning algorithms can optimize keyword extraction methods.', Similarity Score - 0.7448165416717529\n", "Rank 3: Document - 'Understanding document structure aids in keyword extraction.', Similarity Score - 0.631611704826355\n", "Rank 4: Document - 'Document analysis involves extracting keywords.', Similarity Score - 0.5675692558288574\n"]}]}, {"cell_type": "code", "source": ["query"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "_OrCV0bDGWeN", "outputId": "7850bfa2-1446-4f82-fe3f-7f9b66fcac73"}, "execution_count": 39, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Natural language processing techniques enhance keyword extraction efficiency.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 39}]}, {"cell_type": "code", "source": ["!pip install rank_bm25"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RdUhRaPuGBdG", "outputId": "07310c7d-e6f7-4448-f486-fc74e977c0b8"}, "execution_count": 37, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting rank_bm25\n", "  Downloading rank_bm25-0.2.2-py3-none-any.whl (8.6 kB)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from rank_bm25) (1.25.2)\n", "Installing collected packages: rank_bm25\n", "Successfully installed rank_bm25-0.2.2\n"]}]}, {"cell_type": "code", "source": ["from rank_bm25 import BM25Okapi"], "metadata": {"id": "V2xHQLECGOPh"}, "execution_count": 38, "outputs": []}, {"cell_type": "code", "source": ["top_4_documents = [doc[0] for doc in ranked_documents[:4]]"], "metadata": {"id": "IOWKXh97GTt9"}, "execution_count": 40, "outputs": []}, {"cell_type": "code", "source": ["top_4_documents"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "HL6C8FBkGkvR", "outputId": "b6008275-ca9e-4f57-af0d-8022926eb976"}, "execution_count": 41, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['Efficient keyword extraction enhances search accuracy.',\n", " 'Machine learning algorithms can optimize keyword extraction methods.',\n", " 'Understanding document structure aids in keyword extraction.',\n", " 'Document analysis involves extracting keywords.']"]}, "metadata": {}, "execution_count": 41}]}, {"cell_type": "code", "source": ["tokenized_top_4_documents = [doc.split() for doc in top_4_documents]"], "metadata": {"id": "JRxkOMP8GlmO"}, "execution_count": 42, "outputs": []}, {"cell_type": "code", "source": ["tokenized_top_4_documents"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "JXfRdUURGqak", "outputId": "284d7b52-f5c8-4b54-e65b-93b51ccac61d"}, "execution_count": 43, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[['Efficient', 'keyword', 'extraction', 'enhances', 'search', 'accuracy.'],\n", " ['Machine',\n", "  'learning',\n", "  'algorithms',\n", "  'can',\n", "  'optimize',\n", "  'keyword',\n", "  'extraction',\n", "  'methods.'],\n", " ['Understanding',\n", "  'document',\n", "  'structure',\n", "  'aids',\n", "  'in',\n", "  'keyword',\n", "  'extraction.'],\n", " ['Document', 'analysis', 'involves', 'extracting', 'keywords.']]"]}, "metadata": {}, "execution_count": 43}]}, {"cell_type": "code", "source": ["tokenized_query = query.split()"], "metadata": {"id": "qI6FBUxVGrPG"}, "execution_count": 44, "outputs": []}, {"cell_type": "code", "source": ["tokenized_query"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pmLnmTKwHV3Q", "outputId": "4e581b53-63c5-4cd7-bd5f-beba513e71a6"}, "execution_count": 45, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['Natural',\n", " 'language',\n", " 'processing',\n", " 'techniques',\n", " 'enhance',\n", " 'keyword',\n", " 'extraction',\n", " 'efficiency.']"]}, "metadata": {}, "execution_count": 45}]}, {"cell_type": "code", "source": ["bm25=BM25Okapi(tokenized_top_4_documents)"], "metadata": {"id": "tFnVRMXgHXGf"}, "execution_count": 46, "outputs": []}, {"cell_type": "code", "source": ["bm25"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "M1JsrrgQHft_", "outputId": "9ce9731f-1a5f-4a6d-9a5a-c2de87d74441"}, "execution_count": 47, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<rank_bm25.BM25<PERSON><PERSON>pi at 0x7edb701d8070>"]}, "metadata": {}, "execution_count": 47}]}, {"cell_type": "code", "source": ["bm25_scores = bm25.get_scores(tokenized_query)"], "metadata": {"id": "_qSArmibHhIm"}, "execution_count": 48, "outputs": []}, {"cell_type": "code", "source": ["bm25_scores"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "uCwf4pd1HsKe", "outputId": "701d823b-66ad-47ee-bc36-721482a5a30d"}, "execution_count": 49, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([0.1907998 , 0.16686672, 0.17803252, 0.        ])"]}, "metadata": {}, "execution_count": 49}]}, {"cell_type": "code", "source": ["sorted_indices2 = np.argsort(bm25_scores)[::-1]"], "metadata": {"id": "NZ9O9jCqHuEV"}, "execution_count": 62, "outputs": []}, {"cell_type": "code", "source": ["sorted_indices2"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "r4E-x3nyIBoH", "outputId": "54cf1b6d-0b7c-4530-9d6a-c064af50b876"}, "execution_count": 63, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([0, 2, 1, 3])"]}, "metadata": {}, "execution_count": 63}]}, {"cell_type": "code", "source": ["top_4_documents"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "b7CaBP5QICd2", "outputId": "46e955c8-59ed-4754-9670-703431fb2939"}, "execution_count": 79, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['Efficient keyword extraction enhances search accuracy.',\n", " 'Machine learning algorithms can optimize keyword extraction methods.',\n", " 'Understanding document structure aids in keyword extraction.',\n", " 'Document analysis involves extracting keywords.']"]}, "metadata": {}, "execution_count": 79}]}, {"cell_type": "code", "source": ["query"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "uRatS013IM0m", "outputId": "121e1576-5234-41d0-f283-5e3ebc013549"}, "execution_count": 56, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Natural language processing techniques enhance keyword extraction efficiency.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 56}]}, {"cell_type": "code", "source": ["reranked_documents = [(top_4_documents[i], bm25_scores[i]) for i in sorted_indices2]"], "metadata": {"id": "IDrlrwEZQwgz"}, "execution_count": 80, "outputs": []}, {"cell_type": "code", "source": ["reranked_documents"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mdlTwxcSQ7UD", "outputId": "65dc55c0-7add-48c4-8edc-13df8fd6e683"}, "execution_count": 81, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[('Efficient keyword extraction enhances search accuracy.',\n", "  0.19079979534096053),\n", " ('Understanding document structure aids in keyword extraction.',\n", "  0.1780325227902643),\n", " ('Machine learning algorithms can optimize keyword extraction methods.',\n", "  0.1668667199671815),\n", " ('Document analysis involves extracting keywords.', 0.0)]"]}, "metadata": {}, "execution_count": 81}]}, {"cell_type": "code", "source": ["print(\"Rerank of top 4 Documents:\")\n", "for rank, (document, similarity) in enumerate(reranked_documents, start=1):\n", "    print(f\"Rank {rank}: Document - '{document}', Similarity Score - {similarity}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Tp0KrhpHQYIC", "outputId": "d8a30b20-6d03-434e-cda1-cd194d652be0"}, "execution_count": 82, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Rerank of top 4 Documents:\n", "Rank 1: Document - 'Efficient keyword extraction enhances search accuracy.', Similarity Score - 0.19079979534096053\n", "Rank 2: Document - 'Understanding document structure aids in keyword extraction.', Similarity Score - 0.1780325227902643\n", "Rank 3: Document - 'Machine learning algorithms can optimize keyword extraction methods.', Similarity Score - 0.1668667199671815\n", "Rank 4: Document - 'Document analysis involves extracting keywords.', Similarity Score - 0.0\n"]}]}, {"cell_type": "code", "source": ["ranked_documents[:4]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "EStAQfpCRS42", "outputId": "95fb587a-c7d1-4306-8aec-1c1c5117ed59"}, "execution_count": 84, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[('Efficient keyword extraction enhances search accuracy.', 0.7521413),\n", " ('Machine learning algorithms can optimize keyword extraction methods.',\n", "  0.74481654),\n", " ('Understanding document structure aids in keyword extraction.', 0.6316117),\n", " ('Document analysis involves extracting keywords.', 0.56756926)]"]}, "metadata": {}, "execution_count": 84}]}, {"cell_type": "markdown", "source": ["# Cross-Encoder"], "metadata": {"id": "G4J4__8URiHJ"}}, {"cell_type": "code", "source": ["from sentence_transformers import CrossEncoder"], "metadata": {"id": "1-5TWTMuLLP3"}, "execution_count": 85, "outputs": []}, {"cell_type": "code", "source": ["cross_encoder = CrossEncoder('cross-encoder/ms-marco-MiniLM-L-6-v2')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 232, "referenced_widgets": ["333ba957c0164cc7b1367fb4e77c165f", "60dffc29c59743719ccbe57607b306be", "1c1b5ab6594246699420d15f40dcbf9a", "9d17552a55fc44be878dd307775ac321", "9e0dd06ae62747ffa2afd3603eece633", "cbedaac8d74e4abca2d169ac86c1a637", "255b200fe7844af5ae00e2db870baeca", "8dd323012ba14f6eb099213e75490155", "9234b774c856496a93449f43840cdf22", "9945335709b245ca8ec2442cc91e3f17", "f1ffd96a7aab475283cc4eeec7f51e79", "35a8f13d044b46839c6929f4b3c051b5", "47f9be3cc02742e8a4a0631e32ebef7c", "334ee20108d542f0adc8f90a03639ce2", "e90378ec97604ff7a9d210d9ab813770", "51a10f9e1520468abe3aa8ab11b96fe4", "8ab3c4817e1646e8997d3a7dbe2d8de0", "c4d4e21690a8406db75c681fe5a982c3", "d8a6c3f4dd5843808e9f892568528b2f", "46cd6012a4764a61bc4d4afb901adfbf", "e35dfa10dc3343498f7d99b99f8f9bbe", "06f393835dcd46bd826335e62c32de9d", "8e3c909019364932843a339d20f5b361", "18dac171c82043688a6d2f181ff675db", "49768866bcd04cd1a1d9a87bd52d8ece", "01db3797f9bf4f538502c97de09c87d0", "d31a76180c5049768d150c88cdb56a6d", "876314d86ebf4879a3f831a982d6c9a0", "5907a798063a4e5cb2c943a23eb82d70", "acce923405544520ac3173e6d98e1c1c", "754f2e87a56e410d961c8bb803258d22", "f9c9e5dd77294f47b227fceea135c663", "1145cfda26014c00a372cad81fd7292f", "5e0aa6c336094cdcbff419ace9866327", "bf81c8f43ac8436eaf7e4011c51a05be", "38cef872a51f4ef499e0fd885144c593", "b95e39b0d7304f6a8e8f3c1f539b5cfe", "7a87c8bf911f403d92563ac4b0c8e708", "0002e0e41c2246c8828d54ff07773cd7", "ff569f3b6df641d48f6657e699c32c5a", "e4f33c9295cf402e803f9f0ffa676894", "1b167f862fa44b199524107af690eaea", "be453dbcf0ab4bda8eb5b4586ea260df", "f993ac5a08e948cebb08ce7b03cb1553", "41feb9480c9d4766870aae759b13eb83", "b331725039514bdd855459d96399b6b4", "186c4b647c444c3a8883ad7356057d82", "2eee9553552c424c9c2af6a203122659", "00f97ae5e93b4b9c8a2875ad7261d920", "bb61044a9b8d4a0b8046323b1362d5c0", "f82665fb752e424fa74862157349de6f", "893ed7f5802543a1a91ad502cb4604c4", "7ed3ad5926fa416689ab21d83d3c4130", "1d89db0e3d1e44acbf306d20b8bf38fb", "09987e3201424e1f9958604ea336e601"]}, "id": "i0mlZrepRlY5", "outputId": "1e56d95b-710a-4b33-ffee-4e4e3326e790"}, "execution_count": 86, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/huggingface_hub/file_download.py:1132: FutureWarning: `resume_download` is deprecated and will be removed in version 1.0.0. Downloads always resume when possible. If you want to force a new download, use `force_download=True`.\n", "  warnings.warn(\n"]}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/794 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "333ba957c0164cc7b1367fb4e77c165f"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["pytorch_model.bin:   0%|          | 0.00/90.9M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "35a8f13d044b46839c6929f4b3c051b5"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json:   0%|          | 0.00/316 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "8e3c909019364932843a339d20f5b361"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["vocab.txt:   0%|          | 0.00/232k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "5e0aa6c336094cdcbff419ace9866327"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["special_tokens_map.json:   0%|          | 0.00/112 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "41feb9480c9d4766870aae759b13eb83"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["top_4_documents"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3qe0M86ERoHJ", "outputId": "8d042ab6-4236-4adb-f0a8-4f8d3a7c65df"}, "execution_count": 95, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['Efficient keyword extraction enhances search accuracy.',\n", " 'Machine learning algorithms can optimize keyword extraction methods.',\n", " 'Understanding document structure aids in keyword extraction.',\n", " 'Document analysis involves extracting keywords.']"]}, "metadata": {}, "execution_count": 95}]}, {"cell_type": "code", "source": ["query"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "aEpEpJGHRrQD", "outputId": "4466a61f-3662-4d03-9068-40d5b7e8f58f"}, "execution_count": 96, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Natural language processing techniques enhance keyword extraction efficiency.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 96}]}, {"cell_type": "code", "source": ["pairs = []\n", "for doc in top_4_documents:\n", "    pairs.append([query, doc])"], "metadata": {"id": "pl0C674yRtFQ"}, "execution_count": 97, "outputs": []}, {"cell_type": "code", "source": ["pairs"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XTFyfLz5XXO8", "outputId": "b47d87a7-b7b7-47f5-b535-34ee99a37f10"}, "execution_count": 127, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[['Natural language processing techniques enhance keyword extraction efficiency.',\n", "  'Efficient keyword extraction enhances search accuracy.'],\n", " ['Natural language processing techniques enhance keyword extraction efficiency.',\n", "  'Machine learning algorithms can optimize keyword extraction methods.'],\n", " ['Natural language processing techniques enhance keyword extraction efficiency.',\n", "  'Understanding document structure aids in keyword extraction.'],\n", " ['Natural language processing techniques enhance keyword extraction efficiency.',\n", "  'Document analysis involves extracting keywords.']]"]}, "metadata": {}, "execution_count": 127}]}, {"cell_type": "code", "source": ["scores = cross_encoder.predict(pairs)\n", "scores"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9Mo5_OHVRu0x", "outputId": "1156480a-fecf-4491-ab6f-2ab02d7fdef6"}, "execution_count": 128, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([ 3.1378722,  0.842167 , -2.919299 , -2.8781896], dtype=float32)"]}, "metadata": {}, "execution_count": 128}]}, {"cell_type": "code", "source": ["scored_docs = zip(scores, top_4_documents)"], "metadata": {"id": "3RstDfogRwZi"}, "execution_count": 129, "outputs": []}, {"cell_type": "code", "source": ["scored_docs"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "M2feALlgXqnq", "outputId": "cf780c2c-41a9-419a-e922-aab20209a2b7"}, "execution_count": 130, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<zip at 0x7edb2e862200>"]}, "metadata": {}, "execution_count": 130}]}, {"cell_type": "code", "source": ["reranked_document_cross_encoder = sorted(scored_docs, reverse=True)"], "metadata": {"id": "mPmPy-XwRy6n"}, "execution_count": 131, "outputs": []}, {"cell_type": "code", "source": ["reranked_document_cross_encoder"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "eXb2dOvwR0YR", "outputId": "dc214e72-abaf-4f51-f9e7-e4ba0aad906a"}, "execution_count": 132, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[(3.1378722, 'Efficient keyword extraction enhances search accuracy.'),\n", " (0.842167,\n", "  'Machine learning algorithms can optimize keyword extraction methods.'),\n", " (-2.8781896, 'Document analysis involves extracting keywords.'),\n", " (-2.919299, 'Understanding document structure aids in keyword extraction.')]"]}, "metadata": {}, "execution_count": 132}]}, {"cell_type": "markdown", "source": ["# BM_25"], "metadata": {"id": "ZCb89yk6X600"}}, {"cell_type": "code", "source": ["reranked_documents"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jkxrfvbSR2cz", "outputId": "598a94dc-9cae-4d70-fac1-4553ccb64e66"}, "execution_count": 110, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[('Efficient keyword extraction enhances search accuracy.',\n", "  0.19079979534096053),\n", " ('Understanding document structure aids in keyword extraction.',\n", "  0.1780325227902643),\n", " ('Machine learning algorithms can optimize keyword extraction methods.',\n", "  0.1668667199671815),\n", " ('Document analysis involves extracting keywords.', 0.0)]"]}, "metadata": {}, "execution_count": 110}]}, {"cell_type": "code", "source": ["!pip install cohere"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "L9nMXBERSroy", "outputId": "a7620b5f-c385-43a3-f6de-0efb5457dbf3"}, "execution_count": 111, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting cohere\n", "  Downloading cohere-5.5.5-py3-none-any.whl (169 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m169.8/169.8 kB\u001b[0m \u001b[31m1.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting boto3<2.0.0,>=1.34.0 (from cohere)\n", "  Downloading boto3-1.34.121-py3-none-any.whl (139 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m139.3/139.3 kB\u001b[0m \u001b[31m7.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting fastavro<2.0.0,>=1.9.4 (from cohere)\n", "  Downloading fastavro-1.9.4-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.1 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.1/3.1 MB\u001b[0m \u001b[31m21.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting httpx>=0.21.2 (from cohere)\n", "  Downloading httpx-0.27.0-py3-none-any.whl (75 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m9.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting httpx-sse<0.5.0,>=0.4.0 (from cohere)\n", "  Downloading httpx_sse-0.4.0-py3-none-any.whl (7.8 kB)\n", "Requirement already satisfied: pydantic>=1.9.2 in /usr/local/lib/python3.10/dist-packages (from cohere) (2.7.3)\n", "Requirement already satisfied: requests<3.0.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from cohere) (2.31.0)\n", "Collecting tokenizers<0.16,>=0.15 (from cohere)\n", "  Downloading tokenizers-0.15.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.6 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.6/3.6 MB\u001b[0m \u001b[31m57.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting types-requests<3.0.0,>=2.0.0 (from cohere)\n", "  Downloading types_requests-2.32.0.********-py3-none-any.whl (15 kB)\n", "Requirement already satisfied: typing_extensions>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from cohere) (4.12.1)\n", "Collecting botocore<1.35.0,>=1.34.121 (from boto3<2.0.0,>=1.34.0->cohere)\n", "  Downloading botocore-1.34.121-py3-none-any.whl (12.3 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m12.3/12.3 MB\u001b[0m \u001b[31m42.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting jmespath<2.0.0,>=0.7.1 (from boto3<2.0.0,>=1.34.0->cohere)\n", "  Downloading jmespath-1.0.1-py3-none-any.whl (20 kB)\n", "Collecting s3transfer<0.11.0,>=0.10.0 (from boto3<2.0.0,>=1.34.0->cohere)\n", "  Downloading s3transfer-0.10.1-py3-none-any.whl (82 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m82.2/82.2 kB\u001b[0m \u001b[31m12.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx>=0.21.2->cohere) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx>=0.21.2->cohere) (2024.6.2)\n", "Collecting httpcore==1.* (from httpx>=0.21.2->cohere)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m10.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx>=0.21.2->cohere) (3.7)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx>=0.21.2->cohere) (1.3.1)\n", "Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx>=0.21.2->cohere)\n", "  Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m8.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.9.2->cohere) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.18.4 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.9.2->cohere) (2.18.4)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.0.0->cohere) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.0.0->cohere) (2.0.7)\n", "Requirement already satisfied: huggingface_hub<1.0,>=0.16.4 in /usr/local/lib/python3.10/dist-packages (from tokenizers<0.16,>=0.15->cohere) (0.23.2)\n", "Requirement already satisfied: python-dateutil<3.0.0,>=2.1 in /usr/local/lib/python3.10/dist-packages (from botocore<1.35.0,>=1.34.121->boto3<2.0.0,>=1.34.0->cohere) (2.8.2)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface_hub<1.0,>=0.16.4->tokenizers<0.16,>=0.15->cohere) (3.14.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub<1.0,>=0.16.4->tokenizers<0.16,>=0.15->cohere) (2023.6.0)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub<1.0,>=0.16.4->tokenizers<0.16,>=0.15->cohere) (24.0)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub<1.0,>=0.16.4->tokenizers<0.16,>=0.15->cohere) (6.0.1)\n", "Requirement already satisfied: tqdm>=4.42.1 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub<1.0,>=0.16.4->tokenizers<0.16,>=0.15->cohere) (4.66.4)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx>=0.21.2->cohere) (1.2.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil<3.0.0,>=2.1->botocore<1.35.0,>=1.34.121->boto3<2.0.0,>=1.34.0->cohere) (1.16.0)\n", "Installing collected packages: types-requests, jmespath, httpx-sse, h11, fastavro, httpcore, botocore, tokenizers, s3transfer, httpx, boto3, cohere\n", "  Attempting uninstall: tokenizers\n", "    Found existing installation: tokenizers 0.19.1\n", "    Uninstalling tokenizers-0.19.1:\n", "      Successfully uninstalled tokenizers-0.19.1\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "transformers 4.41.2 requires tokenizers<0.20,>=0.19, but you have tokenizers 0.15.2 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed boto3-1.34.121 botocore-1.34.121 cohere-5.5.5 fastavro-1.9.4 h11-0.14.0 httpcore-1.0.5 httpx-0.27.0 httpx-sse-0.4.0 jmespath-1.0.1 s3transfer-0.10.1 tokenizers-0.15.2 types-requests-2.32.0.********\n"]}]}, {"cell_type": "code", "source": ["import cohere"], "metadata": {"id": "_a4J2-TfS2vC"}, "execution_count": 112, "outputs": []}, {"cell_type": "code", "source": ["co = cohere.Client(\"nbDqU1hTVxWmXGbLYI6OnYhp4Cx40MZ5hOmO5oKX\")"], "metadata": {"id": "SdLeyOkES5OP"}, "execution_count": 113, "outputs": []}, {"cell_type": "code", "source": ["top_4_documents"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "oG-b7zwjTJu6", "outputId": "383724b7-6087-4623-a061-9590f515975f"}, "execution_count": 116, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['Efficient keyword extraction enhances search accuracy.',\n", " 'Machine learning algorithms can optimize keyword extraction methods.',\n", " 'Understanding document structure aids in keyword extraction.',\n", " 'Document analysis involves extracting keywords.']"]}, "metadata": {}, "execution_count": 116}]}, {"cell_type": "code", "source": ["query"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "yb8ykLpRTMBk", "outputId": "7b160b1a-9256-406f-ddee-6e4db54de349"}, "execution_count": 117, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Natural language processing techniques enhance keyword extraction efficiency.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 117}]}, {"cell_type": "code", "source": ["response = co.rerank(\n", "    model=\"rerank-english-v3.0\",\n", "    query=\"Natural language processing techniques enhance keyword extraction efficiency.\",\n", "    documents=top_4_documents,\n", "    return_documents=True\n", ")"], "metadata": {"id": "FYPqN4zZS6wC"}, "execution_count": 118, "outputs": []}, {"cell_type": "code", "source": ["print(response)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "i_PU-k1HTXbR", "outputId": "3657e386-83be-4fa0-a2ac-e7800c11aa31"}, "execution_count": 119, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["id='41dcfeb5-fada-41a0-8119-7860e3cc80fc' results=[RerankResponseResultsItem(document=RerankResponseResultsItemDocument(text='Efficient keyword extraction enhances search accuracy.'), index=0, relevance_score=0.99411184), RerankResponseResultsItem(document=RerankResponseResultsItemDocument(text='Machine learning algorithms can optimize keyword extraction methods.'), index=1, relevance_score=0.9129032), RerankResponseResultsItem(document=RerankResponseResultsItemDocument(text='Understanding document structure aids in keyword extraction.'), index=2, relevance_score=0.32885265), RerankResponseResultsItem(document=RerankResponseResultsItemDocument(text='Document analysis involves extracting keywords.'), index=3, relevance_score=0.02865267)] meta=ApiMeta(api_version=ApiMetaApiVersion(version='1', is_deprecated=None, is_experimental=None), billed_units=ApiMetaBilledUnits(input_tokens=None, output_tokens=None, search_units=1, classifications=None), tokens=None, warnings=None)\n"]}]}, {"cell_type": "code", "source": ["response.results[0].document.text"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "j6rK9-qJTaLZ", "outputId": "698a9024-284e-4314-c00b-13c7c5a8bfb2"}, "execution_count": 120, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Efficient keyword extraction enhances search accuracy.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 120}]}, {"cell_type": "code", "source": ["response.results[0].relevance_score"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "JhWpXlwsTcAr", "outputId": "56c8afcf-a423-480c-917d-5b1056335b1c"}, "execution_count": 121, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0.99411184"]}, "metadata": {}, "execution_count": 121}]}, {"cell_type": "code", "source": ["for i in range(4):\n", "  print(f'text: {response.results[i].document.text} score: {response.results[i].relevance_score}')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XK91v711TdaV", "outputId": "26a032c6-07c9-47a9-d0b1-221e5edf0c2a"}, "execution_count": 122, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["text: Efficient keyword extraction enhances search accuracy. score: 0.99411184\n", "text: Machine learning algorithms can optimize keyword extraction methods. score: 0.9129032\n", "text: Understanding document structure aids in keyword extraction. score: 0.32885265\n", "text: Document analysis involves extracting keywords. score: 0.02865267\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "vHkJZ_ODTe5a"}, "execution_count": null, "outputs": []}]}