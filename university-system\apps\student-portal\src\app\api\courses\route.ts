import { NextRequest, NextResponse } from 'next/server';
import { createServerClient, getCurrentUser } from '@university/database/src/client';
import { ApiResponse, PaginatedResponse } from '@university/types';

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient();
    const { searchParams } = new URL(request.url);
    
    // Get pagination parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Get current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    // Get user profile to verify student role
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError || profile?.role !== 'student') {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Access denied'
      }, { status: 403 });
    }

    // Get enrolled courses for the student
    const { data: enrollments, error: enrollmentError } = await supabase
      .from('enrollments')
      .select(`
        id,
        status,
        enrollment_date,
        final_grade,
        courses (
          id,
          code,
          name,
          description,
          credits,
          semester,
          year,
          schedule,
          location,
          users!courses_instructor_id_fkey (
            first_name,
            last_name,
            email
          ),
          departments (
            name,
            code
          )
        )
      `)
      .eq('student_id', user.id)
      .eq('status', 'enrolled')
      .range(offset, offset + limit - 1);

    if (enrollmentError) {
      console.error('Error fetching courses:', enrollmentError);
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Failed to fetch courses'
      }, { status: 500 });
    }

    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('enrollments')
      .select('*', { count: 'exact', head: true })
      .eq('student_id', user.id)
      .eq('status', 'enrolled');

    if (countError) {
      console.error('Error counting courses:', countError);
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Failed to count courses'
      }, { status: 500 });
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json<PaginatedResponse>({
      success: true,
      data: enrollments,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages
      }
    });

  } catch (error) {
    console.error('Courses API error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
