{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 220px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/aaa.png\" width=\"220\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Autonomous Traders</h2>\n", "            <span style=\"color:#ff7800;\">An equity trading simulation to illustrate autonomous agents powered by tools and resources from MCP servers.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["An equity trading simulation, with 4 Traders and a Researcher, powered by a 6 MCP servers with 44 tools & 2 resources:\n", "\n", "1. Our home-made Accounts MCP server (written by our engineering team!)\n", "2. Fetch (get webpage via a local headless browser)\n", "3. <PERSON>\n", "4. Brave Search\n", "5. Financial data from Polygon\n", "6. The return of our Push Notification tool ❤️\n", "\n", "And a resource to read information about the trader's account, and their investment strategy.\n", "\n", "This lab looks at a new python module, `traders.py` that will manage a single trader on our trading floor.\n", "\n", "We will experiment and explore in the lab, and then migrate to a python module when we're ready."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/stop.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">One more time --</h2>\n", "            <span style=\"color:#ff7800;\">Please do not use this for actual trading decisions!!\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv\n", "from dotenv import load_dotenv\n", "from agents import Agent, Runner, trace, Tool\n", "from agents.mcp import MCPServerStdio\n", "from IPython.display import Markdown, display\n", "from datetime import datetime\n", "from accounts_client import read_accounts_resource, read_strategy_resource\n", "from accounts import Account\n", "\n", "load_dotenv(override=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Let's start by gathering the MCP servers for our trader and researcher"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from mcp_params import trader_mcp_server_params, researcher_mcp_server_params\n", "\n", "trader_mcp_servers = [MCPServerStdio(params) for params in trader_mcp_server_params]\n", "researcher_mcp_servers = [MCPServerStdio(params) for params in researcher_mcp_server_params(\"ed\")]\n", "mcp_servers = trader_mcp_servers + researcher_mcp_servers"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Now let's make a Researcher Agent to do market research\n", "\n", "And turn it into a tool"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["async def get_researcher(mcp_servers) -> Agent:\n", "    instructions = f\"\"\"You are a financial researcher. You are able to search the web for interesting financial news,\n", "look for possible trading opportunities, and help with research.\n", "Based on the request, you carry out necessary research and respond with your findings.\n", "Take time to make multiple searches to get a comprehensive overview, and then summarize your findings.\n", "If there isn't a specific request, then just respond with investment opportunities based on searching latest news.\n", "The current datetime is {datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")}\n", "\"\"\"\n", "    researcher = Agent(\n", "        name=\"Researcher\",\n", "        instructions=instructions,\n", "        model=\"gpt-4o-mini\",\n", "        mcp_servers=mcp_servers,\n", "    )\n", "    return researcher"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["async def get_researcher_tool(mcp_servers) -> Tool:\n", "    researcher = await get_researcher(mcp_servers)\n", "    return researcher.as_tool(\n", "            tool_name=\"Researcher\",\n", "            tool_description=\"This tool researches online for news and opportunities, \\\n", "                either based on your specific request to look into a certain stock, \\\n", "                or generally for notable financial news and opportunities. \\\n", "                Describe what kind of research you're looking for.\"\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["research_question = \"What's the latest news on Amazon?\"\n", "\n", "for server in researcher_mcp_servers:\n", "    await server.connect()\n", "researcher = await get_researcher(researcher_mcp_servers)\n", "with trace(\"Researcher\"):\n", "    result = await Runner.run(researcher, research_question)\n", "display(Markdown(result.final_output))\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Look at the trace\n", "\n", "https://platform.openai.com/traces"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ed_initial_strategy = \"You are a day trader that aggressively buys and sells shares based on news and market conditions.\"\n", "Account.get(\"Ed\").reset(ed_initial_strategy)\n", "\n", "display(Markdown(await read_accounts_resource(\"Ed\")))\n", "display(Markdown(await read_strategy_resource(\"Ed\")))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### And now - to create our Trader Agent"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["agent_name = \"<PERSON>\"\n", "\n", "# Using MCP Servers to read resources\n", "account_details = await read_accounts_resource(agent_name)\n", "strategy = await read_strategy_resource(agent_name)\n", "\n", "instructions = f\"\"\"\n", "You are a trader that manages a portfolio of shares. Your name is {agent_name} and your account is under your name, {agent_name}.\n", "You have access to tools that allow you to search the internet for company news, check stock prices, and buy and sell shares.\n", "Your investment strategy for your portfolio is:\n", "{strategy}\n", "Your current holdings and balance is:\n", "{account_details}\n", "You have the tools to perform a websearch for relevant news and information.\n", "You have tools to check stock prices.\n", "You have tools to buy and sell shares.\n", "You have tools to save memory of companies, research and thinking so far.\n", "Please make use of these tools to manage your portfolio. Carry out trades as you see fit; do not wait for instructions or ask for confirmation.\n", "\"\"\"\n", "\n", "prompt = \"\"\"\n", "Use your tools to make decisions about your portfolio.\n", "Investigate the news and the market, make your decision, make the trades, and respond with a summary of your actions.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["display(Markdown(instructions))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### And to run our Trader"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for server in mcp_servers:\n", "    await server.connect()\n", "\n", "researcher_tool = await get_researcher_tool(researcher_mcp_servers)\n", "trader = Agent(\n", "    name=agent_name,\n", "    instructions=instructions,\n", "    tools=[researcher_tool],\n", "    mcp_servers=trader_mcp_servers,\n", "    model=\"gpt-4o-mini\",\n", ")\n", "with trace(agent_name):\n", "    result = await Runner.run(trader, prompt)\n", "display(Markdown(result.final_output))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Now it's time to review the Python module made from this:\n", "\n", "`mcp_servers.py` is where the MCP servers are specified. You'll notice I've brought in some familiar friends: memory and push notifications!\n", "\n", "`templates.py` is where the instructions and messages are set up (i.e. the System prompts and User prompts)\n", "\n", "`traders.py` brings it all together.\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}