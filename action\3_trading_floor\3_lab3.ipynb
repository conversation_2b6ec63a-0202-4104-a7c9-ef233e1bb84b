{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Lab 3\n", "\n", "## Let's try out 3 MCP Servers\n", "\n", "We will add new powers to our agents:  \n", "1. Memory\n", "2. Internet search\n", "3. Live market data\n", "\n", "This is where you can really appreciate the benefits of MCP: it makes it so easy to equip our agents with tools that others have developed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "from agents import Agent, Runner, trace\n", "from agents.mcp import MCPServerStdio\n", "import os\n", "from IPython.display import Markdown, display\n", "from datetime import datetime\n", "load_dotenv(override=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## First new MCP Server: a persistent knowledge-graph based memory\n", "\n", "Here's a really interesting one: a knowledge-graph based memory.\n", "\n", "It's a persistent memory store of entities, observations about them, and relationships between them.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["params = {\n", "  \"command\": \"npx\",\n", "  \"args\": [\"-y\", \"mcp-memory-libsql\"],\n", "  \"env\": {\n", "\t\t\t\t\"LIBSQL_URL\": \"file:./memory/ed.db\"\n", "\t\t\t}\n", "}\n", "\n", "\n", "async with MCPServerStdio(params=params) as server:\n", "    mcp_tools = await server.list_tools()\n", "\n", "mcp_tools"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["instructions = \"You use your entity tools as a persistent memory to store and recall information about your conversations.\"\n", "request = \"My name's <PERSON>. I'm running a workshop live about AI Agents right now, \\\n", "and I'm co-presenting with the legendary presenter, <PERSON>, from the SuperDataScience podcast.\"\n", "model = \"gpt-4o-mini\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async with MCPServerStdio(params=params, client_session_timeout_seconds=60) as mcp_server:\n", "    agent = Agent(name=\"agent\", instructions=instructions, model=model, mcp_servers=[mcp_server])\n", "    with trace(\"conversation\"):\n", "        result = await Runner.run(agent, request)\n", "    display(Markdown(result.final_output))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["question = \"My name's <PERSON>. What do you know about me?\"\n", "\n", "async with MCPServerStdio(params=params, client_session_timeout_seconds=60) as mcp_server:\n", "    agent = Agent(name=\"agent\", instructions=instructions, model=model, mcp_servers=[mcp_server])\n", "    with trace(\"conversation\"):\n", "        result = await Runner.run(agent, question)\n", "    display(Markdown(result.final_output))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Check the trace:\n", "\n", "https://platform.openai.com/traces"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Second new MCP Server: Brave Search\n", "\n", "\n", "\n", "https://brave.com/search/api/\n", "\n", "Set up your account, and put your key in the .env under `BRAVE_API_KEY`"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["env = {\"BRAVE_API_KEY\": os.getenv(\"BRAVE_API_KEY\")}\n", "params = {\"command\": \"npx\", \"args\": [\"-y\", \"@modelcontextprotocol/server-brave-search\"], \"env\": env}\n", "\n", "async with MCPServerStdio(params=params, client_session_timeout_seconds=60) as server:\n", "    mcp_tools = await server.list_tools()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mcp_tools"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["instructions = \"You are able to search the web for information and briefly summarize the takeaways.\"\n", "request = f\"Please research the latest news on Amazon stock price and briefly summarize its outlook. \\\n", "For context, the current date is {datetime.now().strftime('%Y-%m-%d')}\"\n", "model = \"gpt-4o-mini\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async with MCPServerStdio(params=params, client_session_timeout_seconds=60) as mcp_server:\n", "    agent = Agent(name=\"agent\", instructions=instructions, model=model, mcp_servers=[mcp_server])\n", "    with trace(\"conversation\"):\n", "        result = await Runner.run(agent, request)\n", "    display(Markdown(result.final_output))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Third MCP Server: Polygon.io MCP Server"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Introducing polygon.io\n", "\n", "Polygon.io is a hugely popular financial data provider. It has a free plan and a paid plan. And it also has an MCP Server!\n", "\n", "First, read up on polygon.io on their excellent website, including looking at their pricing:\n", "\n", "https://polygon.io\n", "\n", "1. Please sign up for polygon.io (top right)  \n", "2. Once signed in, please select \"<PERSON>\" in the left hand navigation\n", "3. Press the blue \"New Key\" button\n", "4. <PERSON><PERSON> the key name\n", "5. Edit your .env file and add the row:\n", "\n", "`POLYGON_API_KEY=xxxx`\n", "\n", "This section covers the paid plan, but I've set up a free alternative too. Just use the `.env` file to control which one is used:\n", "\n", "If you do decide to have a paid plan, please add this to your .env file to indicate:\n", "\n", "`POLYGON_PLAN=paid`\n", "\n", "And if you decide to go all the way for the realtime API, then please do:\n", "\n", "`POLYGON_PLAN=realtime`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["polygon_api_key = os.getenv(\"POLYGON_API_KEY\")\n", "\n", "params = {\"command\": \"uvx\",\n", "          \"args\": [\"--from\", \"git+https://github.com/polygon-io/mcp_polygon@master\", \"mcp_polygon\"],\n", "          \"env\": {\"POLYGON_API_KEY\": polygon_api_key}\n", "          }\n", "async with MCPServerStdio(params=params, client_session_timeout_seconds=60) as server:\n", "    mcp_tools = await server.list_tools()\n", "mcp_tools\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Wow that's a lot of tools!\n", "\n", "Let's try them out - hopefully the sheer number of tools doesn't overwhelm gpt-4o-mini!\n", "\n", "With the $29 monthly plan, we don't have access to some of the APIs, so I've needed to specify which APIs can be called.\n", "\n", "If you've splashed out on a bigger plan, feel free to remove my extra constraint.."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["instructions = \"You answer questions about the stock market.\"\n", "request = \"What's the share price of Apple? Use your get_snapshot_ticker tool to get the latest price.\"\n", "model = \"gpt-4o-mini\"\n", "\n", "async with MCPServerStdio(params=params, client_session_timeout_seconds=60) as mcp_server:\n", "    agent = Agent(name=\"agent\", instructions=instructions, model=model, mcp_servers=[mcp_server])\n", "    with trace(\"conversation\"):\n", "        result = await Runner.run(agent, request)\n", "    display(Markdown(result.final_output))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## In total, how many MCP servers and tools have we looked at?"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from mcp_params import trader_mcp_server_params, researcher_mcp_server_params\n", "\n", "all_params = trader_mcp_server_params + researcher_mcp_server_params(\"ed\")\n", "\n", "count = 0\n", "for each_params in all_params:\n", "    async with MCPServerStdio(params=each_params, client_session_timeout_seconds=60) as server:\n", "        mcp_tools = await server.list_tools()\n", "        count += len(mcp_tools)\n", "print(f\"We have {len(all_params)} MCP servers, and {count} tools\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## OK - Let's build an application that uses all of these tools!!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}