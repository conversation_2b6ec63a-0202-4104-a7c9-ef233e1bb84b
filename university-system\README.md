# University Record Management System

A comprehensive university record management system built with Next.js, featuring separate portals for students and administrative staff.

## Architecture Overview

This system consists of:
- **Student Portal**: Student-facing application for course enrollment, grade viewing, and profile management
- **Admin Portal**: Administrative interface for staff to manage students, courses, grades, and reports
- **Shared Backend**: Next.js API routes providing unified backend services
- **Database**: Supabase PostgreSQL database with file storage

## Project Structure

```
university-system/
├── apps/
│   ├── student-portal/     # Student-facing Next.js app
│   ├── admin-portal/       # Admin/staff Next.js app
│   └── shared/             # Shared components and utilities
├── packages/
│   ├── database/           # Database schemas and migrations
│   ├── ui/                 # Shared UI components
│   └── types/              # TypeScript type definitions
├── docs/                   # Documentation
└── scripts/                # Build and deployment scripts
```

## Core Features

### Student Portal
- User authentication and profile management
- Course catalog browsing and enrollment
- Grade and transcript viewing
- Schedule management
- Assignment submissions
- Communication with faculty

### Admin Portal
- Student information management
- Course and curriculum management
- Grade entry and management
- Faculty and staff management
- Reporting and analytics
- System administration

## Technology Stack

- **Frontend**: Next.js 14 with App Router
- **Backend**: Next.js API Routes
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth with role-based access
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/ui
- **State Management**: Zustand
- **Form Handling**: React Hook Form with Zod validation
- **Testing**: Jest, React Testing Library, Playwright

## Database Schema

### Core Entities
- Users (students, faculty, staff, admins)
- Departments
- Courses
- Enrollments
- Grades
- Schedules
- Assignments
- Announcements

## Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables
4. Run database migrations
5. Start development servers

## Development

- Student Portal: `npm run dev:student`
- Admin Portal: `npm run dev:admin`
- Both portals: `npm run dev`

## Deployment

The system is designed for deployment on Vercel with Supabase as the backend service.

## Contributing

Please read our contributing guidelines and code of conduct before submitting pull requests.

## License

This project is licensed under the MIT License.
