{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU", "widgets": {"application/vnd.jupyter.widget-state+json": {"e84b2a9a1740434fb6b8861efb3312af": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4dbdf69df7ee48f0b7b6f62085a4336e", "IPY_MODEL_fca9e2fe67b4497b823b5134fea5a736", "IPY_MODEL_26a1c028d889456e803598e1531fefaa"], "layout": "IPY_MODEL_4a903db9cd3c490fbddbdfee0db4cc47"}}, "4dbdf69df7ee48f0b7b6f62085a4336e": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_616d99ddbe18408db18a85c37625014d", "placeholder": "​", "style": "IPY_MODEL_3329c6debbcc456a99f82b43eb1d9dce", "value": "modules.json: 100%"}}, "fca9e2fe67b4497b823b5134fea5a736": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fe97ceb98d1e4c22ac386549564ba2f6", "max": 349, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_cbf51dc4ebfc4962b7f23f277d7e20d4", "value": 349}}, "26a1c028d889456e803598e1531fefaa": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2633fe257e3240efb020d512e0647381", "placeholder": "​", "style": "IPY_MODEL_6a07e94c3b35489cbb06f6c78f0a3059", "value": " 349/349 [00:00&lt;00:00, 14.3kB/s]"}}, "4a903db9cd3c490fbddbdfee0db4cc47": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "616d99ddbe18408db18a85c37625014d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3329c6debbcc456a99f82b43eb1d9dce": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fe97ceb98d1e4c22ac386549564ba2f6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cbf51dc4ebfc4962b7f23f277d7e20d4": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2633fe257e3240efb020d512e0647381": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6a07e94c3b35489cbb06f6c78f0a3059": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d49fde6298f142ec844387f4a302f320": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ce8873428ece4d0d9215927f1300e1d9", "IPY_MODEL_cf5d5061460a4a7ea0608e6ddc2175a4", "IPY_MODEL_7889413ec63f49e08d84fbf2c44725d8"], "layout": "IPY_MODEL_0e429fce0f6c48f799e6c5dda3d650da"}}, "ce8873428ece4d0d9215927f1300e1d9": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_45ef437c64814da993620e391796cdd9", "placeholder": "​", "style": "IPY_MODEL_d3a0df6679c54a51ae76171bfb97d5fe", "value": "config_sentence_transformers.json: 100%"}}, "cf5d5061460a4a7ea0608e6ddc2175a4": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3d2707ff05824a5fbbd95bb637db838d", "max": 116, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5a0080ac33dc4714a78c11a41310277c", "value": 116}}, "7889413ec63f49e08d84fbf2c44725d8": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_381d72f3ad754726b2bb4fe38fdfc14d", "placeholder": "​", "style": "IPY_MODEL_b9cd0e64b9d64fd796706033e7c76c20", "value": " 116/116 [00:00&lt;00:00, 6.38kB/s]"}}, "0e429fce0f6c48f799e6c5dda3d650da": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "45ef437c64814da993620e391796cdd9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d3a0df6679c54a51ae76171bfb97d5fe": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3d2707ff05824a5fbbd95bb637db838d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5a0080ac33dc4714a78c11a41310277c": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "381d72f3ad754726b2bb4fe38fdfc14d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b9cd0e64b9d64fd796706033e7c76c20": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0bc5f0065f7d427894e04e804f870d1f": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0790ed27ffc64a30b233ffd99c887832", "IPY_MODEL_a1fff71f64c24670b2b3de5096a604e9", "IPY_MODEL_43fdafe04ec94cfa90949e13bada9af8"], "layout": "IPY_MODEL_a3f9dfc42a9545e7b5473a3e4d46cdc1"}}, "0790ed27ffc64a30b233ffd99c887832": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bbed2678a37e4d1da8217a027f3a5276", "placeholder": "​", "style": "IPY_MODEL_8271c13521a14558ad1e56c56ace3a37", "value": "README.md: 100%"}}, "a1fff71f64c24670b2b3de5096a604e9": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b4b124405a274a6db38806cb4cff8de9", "max": 10621, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_907d9d117fa649aa8fdc6c73252286f1", "value": 10621}}, "43fdafe04ec94cfa90949e13bada9af8": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7d0011068c424329add6961d52760115", "placeholder": "​", "style": "IPY_MODEL_eb198933e6824c32b855bd106e1ec49f", "value": " 10.6k/10.6k [00:00&lt;00:00, 489kB/s]"}}, "a3f9dfc42a9545e7b5473a3e4d46cdc1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bbed2678a37e4d1da8217a027f3a5276": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8271c13521a14558ad1e56c56ace3a37": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b4b124405a274a6db38806cb4cff8de9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "907d9d117fa649aa8fdc6c73252286f1": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7d0011068c424329add6961d52760115": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "eb198933e6824c32b855bd106e1ec49f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5643a8a739f949ecbb5a31a7ef7d7be2": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_701d231d3aab4940937c87eda8e061ff", "IPY_MODEL_0e177c2d9b83416bb6100ac385a04929", "IPY_MODEL_d7ebe401f50a438d9f55c8eec19c017b"], "layout": "IPY_MODEL_e32511c27cc04f049d8221d8291c74ca"}}, "701d231d3aab4940937c87eda8e061ff": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_26b1c7172626484e9e275cd17ad75038", "placeholder": "​", "style": "IPY_MODEL_8c517421a26d447081172c7b2c338068", "value": "sentence_bert_config.json: 100%"}}, "0e177c2d9b83416bb6100ac385a04929": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_739aad34e5e04f9e997f7a481c1c740d", "max": 53, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_6f92013ea1924f739d0c6552ba0ac362", "value": 53}}, "d7ebe401f50a438d9f55c8eec19c017b": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_804631e6b9cf480bb3a06036e6349beb", "placeholder": "​", "style": "IPY_MODEL_d9a945d711ff4afc8cdab8155fab2f3f", "value": " 53.0/53.0 [00:00&lt;00:00, 3.33kB/s]"}}, "e32511c27cc04f049d8221d8291c74ca": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "26b1c7172626484e9e275cd17ad75038": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8c517421a26d447081172c7b2c338068": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "739aad34e5e04f9e997f7a481c1c740d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6f92013ea1924f739d0c6552ba0ac362": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "804631e6b9cf480bb3a06036e6349beb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d9a945d711ff4afc8cdab8155fab2f3f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d2fc565185c443a8a6e9401f700c4178": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_8cb855c39f254c15bafe1cecf9e0379a", "IPY_MODEL_3f1981d29c804593969405b5bef1895c", "IPY_MODEL_7bccbc4904ed4bf79427274c9098bf2f"], "layout": "IPY_MODEL_e37147192a1644ad8ab5dcbcfd8c4cb5"}}, "8cb855c39f254c15bafe1cecf9e0379a": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1922cc6d1cee4e2cb3d9c20df7fe8c96", "placeholder": "​", "style": "IPY_MODEL_2ee34c5f27c345e682681540f1f1aa10", "value": "config.json: 100%"}}, "3f1981d29c804593969405b5bef1895c": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fe609465091b45e2be9dd56b084b9ceb", "max": 571, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_46a967e072ad4a0d97b4fa3ae0cc9fdc", "value": 571}}, "7bccbc4904ed4bf79427274c9098bf2f": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e014290748294aba9929c98e9dc2a2f9", "placeholder": "​", "style": "IPY_MODEL_a77907217a6340a3a8fccad5976de4f1", "value": " 571/571 [00:00&lt;00:00, 25.9kB/s]"}}, "e37147192a1644ad8ab5dcbcfd8c4cb5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1922cc6d1cee4e2cb3d9c20df7fe8c96": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2ee34c5f27c345e682681540f1f1aa10": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fe609465091b45e2be9dd56b084b9ceb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "46a967e072ad4a0d97b4fa3ae0cc9fdc": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e014290748294aba9929c98e9dc2a2f9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a77907217a6340a3a8fccad5976de4f1": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0a04b7d000234b7dbd6da536740e0ccd": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6bfc56f6c2e54095b0ef0d3b6bd5778d", "IPY_MODEL_6cf453f3a54446808ce2cd0b6e472031", "IPY_MODEL_b0c1a600ea594da093b6609e96dcedb5"], "layout": "IPY_MODEL_8902e2d51915467fa85b73b277650ad4"}}, "6bfc56f6c2e54095b0ef0d3b6bd5778d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_46a022889a0c4f4898720027dccc537e", "placeholder": "​", "style": "IPY_MODEL_89c04c6a67584f40b49632989a8afe45", "value": "model.safetensors: 100%"}}, "6cf453f3a54446808ce2cd0b6e472031": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_07bfb5ef68004a229299d621d8743811", "max": 437971872, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_cb72f7924dad4bcf88acf339cdc317eb", "value": 437971872}}, "b0c1a600ea594da093b6609e96dcedb5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fbe9a4b0a015460a8edd80e8ed17f9d1", "placeholder": "​", "style": "IPY_MODEL_f266974305af4d269c1657a9fbf4e026", "value": " 438M/438M [00:02&lt;00:00, 170MB/s]"}}, "8902e2d51915467fa85b73b277650ad4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "46a022889a0c4f4898720027dccc537e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "89c04c6a67584f40b49632989a8afe45": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "07bfb5ef68004a229299d621d8743811": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cb72f7924dad4bcf88acf339cdc317eb": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "fbe9a4b0a015460a8edd80e8ed17f9d1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f266974305af4d269c1657a9fbf4e026": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ee79b8d34e9147bb8a98a3c3595cba89": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ef9f53eddff2406898d22f4f7e11d0b7", "IPY_MODEL_944e43e1d8184fb3839f2a36e2249e34", "IPY_MODEL_49ec7c8b25f24ba9823d4df8452ea873"], "layout": "IPY_MODEL_b1f26085277f4958b6204a9477d45ba1"}}, "ef9f53eddff2406898d22f4f7e11d0b7": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d810e614b09644468a77e45865a61eaa", "placeholder": "​", "style": "IPY_MODEL_9db9bf5dfcbb4c7b8e505220fc8ded76", "value": "tokenizer_config.json: 100%"}}, "944e43e1d8184fb3839f2a36e2249e34": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a6fc52b6bc924ec69adfa035bde09617", "max": 363, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_623465c9c926493595d5cc090c956f82", "value": 363}}, "49ec7c8b25f24ba9823d4df8452ea873": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d592b7b6baaa478986fd01b27f2e94e1", "placeholder": "​", "style": "IPY_MODEL_190e44d1036b4eecb7dbfbffaf0c1ec2", "value": " 363/363 [00:00&lt;00:00, 18.2kB/s]"}}, "b1f26085277f4958b6204a9477d45ba1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d810e614b09644468a77e45865a61eaa": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9db9bf5dfcbb4c7b8e505220fc8ded76": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a6fc52b6bc924ec69adfa035bde09617": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "623465c9c926493595d5cc090c956f82": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d592b7b6baaa478986fd01b27f2e94e1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "190e44d1036b4eecb7dbfbffaf0c1ec2": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d506e084a29d40f39d64cf0822c801d7": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_198ab2f45ed341a19f817c3020785906", "IPY_MODEL_dd36a78b619844da9753fcfdb552c479", "IPY_MODEL_31aea98379fd407caf515cb2900adbbd"], "layout": "IPY_MODEL_95221e2d2e064c93a4bee68e76b70df3"}}, "198ab2f45ed341a19f817c3020785906": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1218d786f4644e659527780a57013a5a", "placeholder": "​", "style": "IPY_MODEL_865c84e6337149bc95ceffea04c54a5f", "value": "vocab.txt: 100%"}}, "dd36a78b619844da9753fcfdb552c479": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_87c2c4b80fe542acb461ebc8f5f72e0f", "max": 231536, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_89dfeeb9b40240968210e5b51d3d63ff", "value": 231536}}, "31aea98379fd407caf515cb2900adbbd": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e6cd47c0f8024ea7b868daca3e71e795", "placeholder": "​", "style": "IPY_MODEL_553cce0be8694d6f9b703a60084e61d9", "value": " 232k/232k [00:00&lt;00:00, 5.29MB/s]"}}, "95221e2d2e064c93a4bee68e76b70df3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1218d786f4644e659527780a57013a5a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "865c84e6337149bc95ceffea04c54a5f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "87c2c4b80fe542acb461ebc8f5f72e0f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "89dfeeb9b40240968210e5b51d3d63ff": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e6cd47c0f8024ea7b868daca3e71e795": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "553cce0be8694d6f9b703a60084e61d9": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5b6e93ec87b34053985cb04e88f616e2": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d4cd5aaf4f504e7d9e1e64c09647af27", "IPY_MODEL_404092a77b6040cdba69c354d812ab83", "IPY_MODEL_d208f427da784f8ea66cf557b0983ea3"], "layout": "IPY_MODEL_d079490dfeb64dada549e0284d32fe23"}}, "d4cd5aaf4f504e7d9e1e64c09647af27": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_382e20255f6a4421bcdb31453ff80336", "placeholder": "​", "style": "IPY_MODEL_dba3379c1cd1418b9897f80ad82b0c3f", "value": "tokenizer.json: 100%"}}, "404092a77b6040cdba69c354d812ab83": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c9251c439ddb4ef58f1d916a75df282d", "max": 466021, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_281400a864c44df288cafae32c39986a", "value": 466021}}, "d208f427da784f8ea66cf557b0983ea3": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b763b331a780410489c5c5be443eea1a", "placeholder": "​", "style": "IPY_MODEL_62f1dc6f8f1f461495e9ed551694179e", "value": " 466k/466k [00:00&lt;00:00, 10.7MB/s]"}}, "d079490dfeb64dada549e0284d32fe23": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "382e20255f6a4421bcdb31453ff80336": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dba3379c1cd1418b9897f80ad82b0c3f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c9251c439ddb4ef58f1d916a75df282d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "281400a864c44df288cafae32c39986a": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b763b331a780410489c5c5be443eea1a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "62f1dc6f8f1f461495e9ed551694179e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "53e061c920a946f18f9a7ccb75780a45": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9ab93b923e8042ed87610a67654169d5", "IPY_MODEL_cf32c7a599f6453296962d6ea0d4dfb6", "IPY_MODEL_a16de14019eb47d98a7e4a6e6d377dad"], "layout": "IPY_MODEL_b719bfd43eb9421a8b2b89102ccb9b39"}}, "9ab93b923e8042ed87610a67654169d5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3bb282017aaf41798b6938cd4bbf13c5", "placeholder": "​", "style": "IPY_MODEL_ce594d2f0dc44470b017997a98742589", "value": "special_tokens_map.json: 100%"}}, "cf32c7a599f6453296962d6ea0d4dfb6": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bc3bfa5ee2194ed9ae640662006d11a0", "max": 239, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_31861bb5717045d09a500f088de105f9", "value": 239}}, "a16de14019eb47d98a7e4a6e6d377dad": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_52eea7d6153846f495ff104d1e4a7967", "placeholder": "​", "style": "IPY_MODEL_f094f5dab603420790b1b016fccb0d49", "value": " 239/239 [00:00&lt;00:00, 10.0kB/s]"}}, "b719bfd43eb9421a8b2b89102ccb9b39": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3bb282017aaf41798b6938cd4bbf13c5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ce594d2f0dc44470b017997a98742589": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "bc3bfa5ee2194ed9ae640662006d11a0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "31861bb5717045d09a500f088de105f9": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "52eea7d6153846f495ff104d1e4a7967": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f094f5dab603420790b1b016fccb0d49": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "926b7aa7a8c843c1a7677db4ac1e4d21": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0bfe4459cc0d45f78576e657e408fbeb", "IPY_MODEL_74eba064d1f347ba830d0aa3e29a38c0", "IPY_MODEL_8889a72dde804679863d6204ad275e5e"], "layout": "IPY_MODEL_f4e04d7a1e144831bbdb358ca2b526fd"}}, "0bfe4459cc0d45f78576e657e408fbeb": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c43664b349aa4ff9a9e607cc348a30b5", "placeholder": "​", "style": "IPY_MODEL_6560f016bbb04bf9b4844ea1c7445c9c", "value": "1_Pooling/config.json: 100%"}}, "74eba064d1f347ba830d0aa3e29a38c0": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d4a9fc50fed04255974951c405ed0917", "max": 190, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ccaae9ce3b8e4f479598d0a3de81ca8f", "value": 190}}, "8889a72dde804679863d6204ad275e5e": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e04d055c328342f6b0cf9ee679400125", "placeholder": "​", "style": "IPY_MODEL_fb4f5bf291cc4d01b5422953452b9bc6", "value": " 190/190 [00:00&lt;00:00, 8.74kB/s]"}}, "f4e04d7a1e144831bbdb358ca2b526fd": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c43664b349aa4ff9a9e607cc348a30b5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6560f016bbb04bf9b4844ea1c7445c9c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d4a9fc50fed04255974951c405ed0917": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ccaae9ce3b8e4f479598d0a3de81ca8f": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e04d055c328342f6b0cf9ee679400125": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fb4f5bf291cc4d01b5422953452b9bc6": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "y7z6_whzm23H", "outputId": "383384d8-de22-424b-8a23-a2c2e57009a1"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting weaviate-client\n", "  Downloading weaviate_client-4.5.5-py3-none-any.whl (306 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m306.8/306.8 kB\u001b[0m \u001b[31m4.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain\n", "  Downloading langchain-0.1.14-py3-none-any.whl (812 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m812.8/812.8 kB\u001b[0m \u001b[31m9.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting tiktoken\n", "  Downloading tiktoken-0.6.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.8/1.8 MB\u001b[0m \u001b[31m12.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting pypdf\n", "  Downloading pypdf-4.2.0-py3-none-any.whl (290 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m290.4/290.4 kB\u001b[0m \u001b[31m12.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting rapidocr-onnxruntime\n", "  Downloading rapidocr_onnxruntime-1.3.16-py3-none-any.whl (14.9 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m14.9/14.9 MB\u001b[0m \u001b[31m32.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: requests<3.0.0,>=2.30.0 in /usr/local/lib/python3.10/dist-packages (from weaviate-client) (2.31.0)\n", "Collecting httpx==0.27.0 (from weaviate-client)\n", "  Downloading httpx-0.27.0-py3-none-any.whl (75 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m8.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting validators==0.22.0 (from weaviate-client)\n", "  Downloading validators-0.22.0-py3-none-any.whl (26 kB)\n", "Collecting authlib<2.0.0,>=1.2.1 (from weaviate-client)\n", "  Downloading Authlib-1.3.0-py2.py3-none-any.whl (223 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m223.7/223.7 kB\u001b[0m \u001b[31m25.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pydantic<3.0.0,>=2.5.0 in /usr/local/lib/python3.10/dist-packages (from weaviate-client) (2.6.4)\n", "Requirement already satisfied: grpcio<2.0.0,>=1.57.0 in /usr/local/lib/python3.10/dist-packages (from weaviate-client) (1.62.1)\n", "Collecting grpcio-tools<2.0.0,>=1.57.0 (from weaviate-client)\n", "  Downloading grpcio_tools-1.62.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.8/2.8 MB\u001b[0m \u001b[31m48.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting grpcio-health-checking<2.0.0,>=1.57.0 (from weaviate-client)\n", "  Downloading grpcio_health_checking-1.62.1-py3-none-any.whl (18 kB)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx==0.27.0->weaviate-client) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx==0.27.0->weaviate-client) (2024.2.2)\n", "Collecting httpcore==1.* (from httpx==0.27.0->weaviate-client)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m11.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx==0.27.0->weaviate-client) (3.6)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx==0.27.0->weaviate-client) (1.3.1)\n", "Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx==0.27.0->weaviate-client)\n", "  Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m6.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.0.29)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.9.3)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (4.0.3)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain)\n", "  Downloading dataclasses_json-0.6.4-py3-none-any.whl (28 kB)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Collecting langchain-community<0.1,>=0.0.30 (from langchain)\n", "  Downloading langchain_community-0.0.31-py3-none-any.whl (1.9 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.9/1.9 MB\u001b[0m \u001b[31m57.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-core<0.2.0,>=0.1.37 (from langchain)\n", "  Downloading langchain_core-0.1.40-py3-none-any.whl (276 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m276.8/276.8 kB\u001b[0m \u001b[31m34.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-text-splitters<0.1,>=0.0.1 (from langchain)\n", "  Downloading langchain_text_splitters-0.0.1-py3-none-any.whl (21 kB)\n", "Collecting langsmith<0.2.0,>=0.1.17 (from langchain)\n", "  Downloading langsmith-0.1.41-py3-none-any.whl (91 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m91.7/91.7 kB\u001b[0m \u001b[31m13.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.25.2)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (8.2.3)\n", "Requirement already satisfied: regex>=2022.1.18 in /usr/local/lib/python3.10/dist-packages (from tiktoken) (2023.12.25)\n", "Requirement already satisfied: typing_extensions>=4.0 in /usr/local/lib/python3.10/dist-packages (from pypdf) (4.10.0)\n", "Collecting pyclipper>=1.2.0 (from rapidocr-onnxruntime)\n", "  Downloading pyclipper-1.3.0.post5-cp310-cp310-manylinux_2_12_x86_64.manylinux2010_x86_64.whl (908 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m908.3/908.3 kB\u001b[0m \u001b[31m66.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting onnxruntime>=1.7.0 (from rapidocr-onnxruntime)\n", "  Downloading onnxruntime-1.17.1-cp310-cp310-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (6.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.8/6.8 MB\u001b[0m \u001b[31m68.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: opencv-python>=******** in /usr/local/lib/python3.10/dist-packages (from rapidocr-onnxruntime) (********)\n", "Requirement already satisfied: six>=1.15.0 in /usr/local/lib/python3.10/dist-packages (from rapidocr-onnxruntime) (1.16.0)\n", "Requirement already satisfied: S<PERSON><PERSON>y>=1.7.1 in /usr/local/lib/python3.10/dist-packages (from rapidocr-onnxruntime) (2.0.3)\n", "Requirement already satisfied: Pillow in /usr/local/lib/python3.10/dist-packages (from rapidocr-onnxruntime) (9.4.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.9.4)\n", "Requirement already satisfied: cryptography in /usr/local/lib/python3.10/dist-packages (from authlib<2.0.0,>=1.2.1->weaviate-client) (42.0.5)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain)\n", "  Downloading marshmallow-3.21.1-py3-none-any.whl (49 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.4/49.4 kB\u001b[0m \u001b[31m7.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Collecting protobuf>=4.21.6 (from grpcio-health-checking<2.0.0,>=1.57.0->weaviate-client)\n", "  Downloading protobuf-5.26.1-cp37-abi3-manylinux2014_x86_64.whl (302 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m302.8/302.8 kB\u001b[0m \u001b[31m38.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Downloading protobuf-4.25.3-cp37-abi3-manylinux2014_x86_64.whl (294 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m294.6/294.6 kB\u001b[0m \u001b[31m34.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: setuptools in /usr/local/lib/python3.10/dist-packages (from grpcio-tools<2.0.0,>=1.57.0->weaviate-client) (67.7.2)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain)\n", "  Downloading jsonpointer-2.4-py2.py3-none-any.whl (7.8 kB)\n", "Collecting packaging<24.0,>=23.2 (from langchain-core<0.2.0,>=0.1.37->langchain)\n", "  Downloading packaging-23.2-py3-none-any.whl (53 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m53.0/53.0 kB\u001b[0m \u001b[31m7.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hColl<PERSON>ting or<PERSON>son<4.0.0,>=3.9.14 (from langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading orjson-3.10.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (144 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m144.8/144.8 kB\u001b[0m \u001b[31m17.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting coloredlogs (from onnxruntime>=1.7.0->rapidocr-onnxruntime)\n", "  Downloading coloredlogs-15.0.1-py2.py3-none-any.whl (46 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.0/46.0 kB\u001b[0m \u001b[31m6.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: flatbuffers in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.7.0->rapidocr-onnxruntime) (24.3.25)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.7.0->rapidocr-onnxruntime) (1.12)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0.0,>=2.5.0->weaviate-client) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.16.3 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0.0,>=2.5.0->weaviate-client) (2.16.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.30.0->weaviate-client) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.30.0->weaviate-client) (2.0.7)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.0.3)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx==0.27.0->weaviate-client) (1.2.0)\n", "Collecting humanfriendly>=9.1 (from coloredlogs->onnxruntime>=1.7.0->rapidocr-onnxruntime)\n", "  Downloading humanfriendly-10.0-py2.py3-none-any.whl (86 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.8/86.8 kB\u001b[0m \u001b[31m13.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: cffi>=1.12 in /usr/local/lib/python3.10/dist-packages (from cryptography->authlib<2.0.0,>=1.2.1->weaviate-client) (1.16.0)\n", "Requirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->onnxruntime>=1.7.0->rapidocr-onnxruntime) (1.3.0)\n", "Requirement already satisfied: pycparser in /usr/local/lib/python3.10/dist-packages (from cffi>=1.12->cryptography->authlib<2.0.0,>=1.2.1->weaviate-client) (2.22)\n", "Installing collected packages: pyclipper, validators, pypdf, protobuf, packaging, orjson, mypy-extensions, jsonpointer, humanfriendly, h11, typing-inspect, tiktoken, marshmallow, jsonpatch, httpcore, grpcio-tools, grpcio-health-checking, coloredlogs, onnxruntime, langsmith, httpx, dataclasses-json, authlib, weaviate-client, rapidocr-onnxruntime, langchain-core, langchain-text-splitters, langchain-community, langchain\n", "  Attempting uninstall: protobuf\n", "    Found existing installation: protobuf 3.20.3\n", "    Uninstalling protobuf-3.20.3:\n", "      Successfully uninstalled protobuf-3.20.3\n", "  Attempting uninstall: packaging\n", "    Found existing installation: packaging 24.0\n", "    Uninstalling packaging-24.0:\n", "      Successfully uninstalled packaging-24.0\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "tensorflow-metadata 1.14.0 requires protobuf<4.21,>=3.20.3, but you have protobuf 4.25.3 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed authlib-1.3.0 coloredlogs-15.0.1 dataclasses-json-0.6.4 grpcio-health-checking-1.62.1 grpcio-tools-1.62.1 h11-0.14.0 httpcore-1.0.5 httpx-0.27.0 humanfriendly-10.0 jsonpatch-1.33 jsonpointer-2.4 langchain-0.1.14 langchain-community-0.0.31 langchain-core-0.1.40 langchain-text-splitters-0.0.1 langsmith-0.1.41 marshmallow-3.21.1 mypy-extensions-1.0.0 onnxruntime-1.17.1 orjson-3.10.0 packaging-23.2 protobuf-4.25.3 pyclipper-1.3.0.post5 pypdf-4.2.0 rapidocr-onnxruntime-1.3.16 tiktoken-0.6.0 typing-inspect-0.9.0 validators-0.22.0 weaviate-client-4.5.5\n"]}], "source": ["!pip install weaviate-client langchain tiktoken pypdf rapidocr-onnxruntime"]}, {"cell_type": "code", "source": ["WEAVIATE_CLUSTER=\"https://mylangchainproject-z88ava1x.weaviate.network\"\n", "WEAVIATE_API_KEY=\"k4FFtLx0ad4pLbBm2bi11mVHdTlHVowNo99k\""], "metadata": {"id": "TA6SWl0KnV_E"}, "execution_count": 12, "outputs": []}, {"cell_type": "code", "source": ["from langchain.vectorstores import Weaviate\n", "import weaviate\n", "\n", "WEAVIATE_URL = WEAVIATE_CLUSTER\n", "WEAVIATE_API_KEY = WEAVIATE_API_KEY\n", "\n", "client = weaviate.Client(\n", "    url=WEAVIATE_URL, auth_client_secret=weaviate.AuthApiKey(WEAVIATE_API_KEY)\n", ")"], "metadata": {"id": "NjSE_cFAnvuA"}, "execution_count": 13, "outputs": []}, {"cell_type": "code", "source": ["# fixing unicode error in google colab\n", "import locale\n", "locale.getpreferredencoding = lambda: \"UTF-8\""], "metadata": {"id": "UiPYOR0EocFe"}, "execution_count": 2, "outputs": []}, {"cell_type": "code", "source": ["!pip install sentence-transformers"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aYyME3XbqnBL", "outputId": "23723780-f312-47bc-ac5a-f7c957d1a16e"}, "execution_count": 7, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting sentence-transformers\n", "  Downloading sentence_transformers-2.6.1-py3-none-any.whl (163 kB)\n", "\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/163.3 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[91m━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[91m╸\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m92.2/163.3 kB\u001b[0m \u001b[31m2.7 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m163.3/163.3 kB\u001b[0m \u001b[31m3.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: transformers<5.0.0,>=4.32.0 in /usr/local/lib/python3.10/dist-packages (from sentence-transformers) (4.38.2)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from sentence-transformers) (4.66.2)\n", "Requirement already satisfied: torch>=1.11.0 in /usr/local/lib/python3.10/dist-packages (from sentence-transformers) (2.2.1+cu121)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from sentence-transformers) (1.25.2)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.10/dist-packages (from sentence-transformers) (1.2.2)\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.10/dist-packages (from sentence-transformers) (1.11.4)\n", "Requirement already satisfied: huggingface-hub>=0.15.1 in /usr/local/lib/python3.10/dist-packages (from sentence-transformers) (0.20.3)\n", "Requirement already satisfied: Pillow in /usr/local/lib/python3.10/dist-packages (from sentence-transformers) (9.4.0)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence-transformers) (3.13.3)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence-transformers) (2023.6.0)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence-transformers) (2.31.0)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence-transformers) (6.0.1)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence-transformers) (4.10.0)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence-transformers) (23.2)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers) (1.12)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers) (3.2.1)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers) (3.1.3)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.1.105 (from torch>=1.11.0->sentence-transformers)\n", "  Downloading nvidia_cuda_nvrtc_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (23.7 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m23.7/23.7 MB\u001b[0m \u001b[31m33.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting nvidia-cuda-runtime-cu12==12.1.105 (from torch>=1.11.0->sentence-transformers)\n", "  Downloading nvidia_cuda_runtime_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (823 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m823.6/823.6 kB\u001b[0m \u001b[31m51.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting nvidia-cuda-cupti-cu12==12.1.105 (from torch>=1.11.0->sentence-transformers)\n", "  Downloading nvidia_cuda_cupti_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (14.1 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m14.1/14.1 MB\u001b[0m \u001b[31m51.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting nvidia-cudnn-cu12==8.9.2.26 (from torch>=1.11.0->sentence-transformers)\n", "  Downloading nvidia_cudnn_cu12-8.9.2.26-py3-none-manylinux1_x86_64.whl (731.7 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m731.7/731.7 MB\u001b[0m \u001b[31m748.4 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting nvidia-cublas-cu12==12.1.3.1 (from torch>=1.11.0->sentence-transformers)\n", "  Downloading nvidia_cublas_cu12-12.1.3.1-py3-none-manylinux1_x86_64.whl (410.6 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m410.6/410.6 MB\u001b[0m \u001b[31m1.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting nvidia-cufft-cu12==11.0.2.54 (from torch>=1.11.0->sentence-transformers)\n", "  Downloading nvidia_cufft_cu12-11.0.2.54-py3-none-manylinux1_x86_64.whl (121.6 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m121.6/121.6 MB\u001b[0m \u001b[31m8.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting nvidia-curand-cu12==10.3.2.106 (from torch>=1.11.0->sentence-transformers)\n", "  Downloading nvidia_curand_cu12-10.3.2.106-py3-none-manylinux1_x86_64.whl (56.5 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.5/56.5 MB\u001b[0m \u001b[31m11.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting nvidia-cusolver-cu12==11.4.5.107 (from torch>=1.11.0->sentence-transformers)\n", "  Downloading nvidia_cusolver_cu12-11.4.5.107-py3-none-manylinux1_x86_64.whl (124.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m124.2/124.2 MB\u001b[0m \u001b[31m7.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting nvidia-cusparse-cu12==12.1.0.106 (from torch>=1.11.0->sentence-transformers)\n", "  Downloading nvidia_cusparse_cu12-12.1.0.106-py3-none-manylinux1_x86_64.whl (196.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m196.0/196.0 MB\u001b[0m \u001b[31m2.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting nvidia-nccl-cu12==2.19.3 (from torch>=1.11.0->sentence-transformers)\n", "  Downloading nvidia_nccl_cu12-2.19.3-py3-none-manylinux1_x86_64.whl (166.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m166.0/166.0 MB\u001b[0m \u001b[31m2.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting nvidia-nvtx-cu12==12.1.105 (from torch>=1.11.0->sentence-transformers)\n", "  Downloading nvidia_nvtx_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (99 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m99.1/99.1 kB\u001b[0m \u001b[31m14.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: triton==2.2.0 in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers) (2.2.0)\n", "Collecting nvidia-nvjitlink-cu12 (from nvidia-cusolver-cu12==11.4.5.107->torch>=1.11.0->sentence-transformers)\n", "  Downloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m64.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.32.0->sentence-transformers) (2023.12.25)\n", "Requirement already satisfied: tokenizers<0.19,>=0.14 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.32.0->sentence-transformers) (0.15.2)\n", "Requirement already satisfied: safetensors>=0.4.1 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.32.0->sentence-transformers) (0.4.2)\n", "Requirement already satisfied: joblib>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from scikit-learn->sentence-transformers) (1.3.2)\n", "Requirement already satisfied: threadpoolctl>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from scikit-learn->sentence-transformers) (3.4.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch>=1.11.0->sentence-transformers) (2.1.5)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.15.1->sentence-transformers) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.15.1->sentence-transformers) (3.6)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.15.1->sentence-transformers) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.15.1->sentence-transformers) (2024.2.2)\n", "Requirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->torch>=1.11.0->sentence-transformers) (1.3.0)\n", "Installing collected packages: nvidia-nvtx-cu12, nvidia-nvjitlink-cu12, nvidia-nccl-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, nvidia-cusparse-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12, sentence-transformers\n", "Successfully installed nvidia-cublas-cu12-12.1.3.1 nvidia-cuda-cupti-cu12-12.1.105 nvidia-cuda-nvrtc-cu12-12.1.105 nvidia-cuda-runtime-cu12-12.1.105 nvidia-cudnn-cu12-8.9.2.26 nvidia-cufft-cu12-11.0.2.54 nvidia-curand-cu12-10.3.2.106 nvidia-cusolver-cu12-11.4.5.107 nvidia-cusparse-cu12-12.1.0.106 nvidia-nccl-cu12-2.19.3 nvidia-nvjitlink-cu12-12.4.127 nvidia-nvtx-cu12-12.1.105 sentence-transformers-2.6.1\n"]}]}, {"cell_type": "code", "source": ["# specify embedding model (using huggingface sentence transformer)\n", "from langchain.embeddings import HuggingFaceEmbeddings\n", "embedding_model_name = \"sentence-transformers/all-mpnet-base-v2\"\n", "#model_kwargs = {\"device\": \"cuda\"}\n", "embeddings = HuggingFaceEmbeddings(\n", "  model_name=embedding_model_name,\n", "  #model_kwargs=model_kwargs\n", ")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 493, "referenced_widgets": ["e84b2a9a1740434fb6b8861efb3312af", "4dbdf69df7ee48f0b7b6f62085a4336e", "fca9e2fe67b4497b823b5134fea5a736", "26a1c028d889456e803598e1531fefaa", "4a903db9cd3c490fbddbdfee0db4cc47", "616d99ddbe18408db18a85c37625014d", "3329c6debbcc456a99f82b43eb1d9dce", "fe97ceb98d1e4c22ac386549564ba2f6", "cbf51dc4ebfc4962b7f23f277d7e20d4", "2633fe257e3240efb020d512e0647381", "6a07e94c3b35489cbb06f6c78f0a3059", "d49fde6298f142ec844387f4a302f320", "ce8873428ece4d0d9215927f1300e1d9", "cf5d5061460a4a7ea0608e6ddc2175a4", "7889413ec63f49e08d84fbf2c44725d8", "0e429fce0f6c48f799e6c5dda3d650da", "45ef437c64814da993620e391796cdd9", "d3a0df6679c54a51ae76171bfb97d5fe", "3d2707ff05824a5fbbd95bb637db838d", "5a0080ac33dc4714a78c11a41310277c", "381d72f3ad754726b2bb4fe38fdfc14d", "b9cd0e64b9d64fd796706033e7c76c20", "0bc5f0065f7d427894e04e804f870d1f", "0790ed27ffc64a30b233ffd99c887832", "a1fff71f64c24670b2b3de5096a604e9", "43fdafe04ec94cfa90949e13bada9af8", "a3f9dfc42a9545e7b5473a3e4d46cdc1", "bbed2678a37e4d1da8217a027f3a5276", "8271c13521a14558ad1e56c56ace3a37", "b4b124405a274a6db38806cb4cff8de9", "907d9d117fa649aa8fdc6c73252286f1", "7d0011068c424329add6961d52760115", "eb198933e6824c32b855bd106e1ec49f", "5643a8a739f949ecbb5a31a7ef7d7be2", "701d231d3aab4940937c87eda8e061ff", "0e177c2d9b83416bb6100ac385a04929", "d7ebe401f50a438d9f55c8eec19c017b", "e32511c27cc04f049d8221d8291c74ca", "26b1c7172626484e9e275cd17ad75038", "8c517421a26d447081172c7b2c338068", "739aad34e5e04f9e997f7a481c1c740d", "6f92013ea1924f739d0c6552ba0ac362", "804631e6b9cf480bb3a06036e6349beb", "d9a945d711ff4afc8cdab8155fab2f3f", "d2fc565185c443a8a6e9401f700c4178", "8cb855c39f254c15bafe1cecf9e0379a", "3f1981d29c804593969405b5bef1895c", "7bccbc4904ed4bf79427274c9098bf2f", "e37147192a1644ad8ab5dcbcfd8c4cb5", "1922cc6d1cee4e2cb3d9c20df7fe8c96", "2ee34c5f27c345e682681540f1f1aa10", "fe609465091b45e2be9dd56b084b9ceb", "46a967e072ad4a0d97b4fa3ae0cc9fdc", "e014290748294aba9929c98e9dc2a2f9", "a77907217a6340a3a8fccad5976de4f1", "0a04b7d000234b7dbd6da536740e0ccd", "6bfc56f6c2e54095b0ef0d3b6bd5778d", "6cf453f3a54446808ce2cd0b6e472031", "b0c1a600ea594da093b6609e96dcedb5", "8902e2d51915467fa85b73b277650ad4", "46a022889a0c4f4898720027dccc537e", "89c04c6a67584f40b49632989a8afe45", "07bfb5ef68004a229299d621d8743811", "cb72f7924dad4bcf88acf339cdc317eb", "fbe9a4b0a015460a8edd80e8ed17f9d1", "f266974305af4d269c1657a9fbf4e026", "ee79b8d34e9147bb8a98a3c3595cba89", "ef9f53eddff2406898d22f4f7e11d0b7", "944e43e1d8184fb3839f2a36e2249e34", "49ec7c8b25f24ba9823d4df8452ea873", "b1f26085277f4958b6204a9477d45ba1", "d810e614b09644468a77e45865a61eaa", "9db9bf5dfcbb4c7b8e505220fc8ded76", "a6fc52b6bc924ec69adfa035bde09617", "623465c9c926493595d5cc090c956f82", "d592b7b6baaa478986fd01b27f2e94e1", "190e44d1036b4eecb7dbfbffaf0c1ec2", "d506e084a29d40f39d64cf0822c801d7", "198ab2f45ed341a19f817c3020785906", "dd36a78b619844da9753fcfdb552c479", "31aea98379fd407caf515cb2900adbbd", "95221e2d2e064c93a4bee68e76b70df3", "1218d786f4644e659527780a57013a5a", "865c84e6337149bc95ceffea04c54a5f", "87c2c4b80fe542acb461ebc8f5f72e0f", "89dfeeb9b40240968210e5b51d3d63ff", "e6cd47c0f8024ea7b868daca3e71e795", "553cce0be8694d6f9b703a60084e61d9", "5b6e93ec87b34053985cb04e88f616e2", "d4cd5aaf4f504e7d9e1e64c09647af27", "404092a77b6040cdba69c354d812ab83", "d208f427da784f8ea66cf557b0983ea3", "d079490dfeb64dada549e0284d32fe23", "382e20255f6a4421bcdb31453ff80336", "dba3379c1cd1418b9897f80ad82b0c3f", "c9251c439ddb4ef58f1d916a75df282d", "281400a864c44df288cafae32c39986a", "b763b331a780410489c5c5be443eea1a", "62f1dc6f8f1f461495e9ed551694179e", "53e061c920a946f18f9a7ccb75780a45", "9ab93b923e8042ed87610a67654169d5", "cf32c7a599f6453296962d6ea0d4dfb6", "a16de14019eb47d98a7e4a6e6d377dad", "b719bfd43eb9421a8b2b89102ccb9b39", "3bb282017aaf41798b6938cd4bbf13c5", "ce594d2f0dc44470b017997a98742589", "bc3bfa5ee2194ed9ae640662006d11a0", "31861bb5717045d09a500f088de105f9", "52eea7d6153846f495ff104d1e4a7967", "f094f5dab603420790b1b016fccb0d49", "926b7aa7a8c843c1a7677db4ac1e4d21", "0bfe4459cc0d45f78576e657e408fbeb", "74eba064d1f347ba830d0aa3e29a38c0", "8889a72dde804679863d6204ad275e5e", "f4e04d7a1e144831bbdb358ca2b526fd", "c43664b349aa4ff9a9e607cc348a30b5", "6560f016bbb04bf9b4844ea1c7445c9c", "d4a9fc50fed04255974951c405ed0917", "ccaae9ce3b8e4f479598d0a3de81ca8f", "e04d055c328342f6b0cf9ee679400125", "fb4f5bf291cc4d01b5422953452b9bc6"]}, "id": "oLyzB1XMojKG", "outputId": "021d83d8-b77a-4863-ceaa-5c88df1d0b7a"}, "execution_count": 8, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/huggingface_hub/utils/_token.py:88: UserWarning: \n", "The secret `HF_TOKEN` does not exist in your Colab secrets.\n", "To authenticate with the Hugging Face Hub, create a token in your settings tab (https://huggingface.co/settings/tokens), set it as secret in your Google Colab and restart your session.\n", "You will be able to reuse this secret in all of your notebooks.\n", "Please note that authentication is recommended but still optional to access public models or datasets.\n", "  warnings.warn(\n"]}, {"output_type": "display_data", "data": {"text/plain": ["modules.json:   0%|          | 0.00/349 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "e84b2a9a1740434fb6b8861efb3312af"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config_sentence_transformers.json:   0%|          | 0.00/116 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "d49fde6298f142ec844387f4a302f320"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["README.md:   0%|          | 0.00/10.6k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "0bc5f0065f7d427894e04e804f870d1f"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["sentence_bert_config.json:   0%|          | 0.00/53.0 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "5643a8a739f949ecbb5a31a7ef7d7be2"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/571 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "d2fc565185c443a8a6e9401f700c4178"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors:   0%|          | 0.00/438M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "0a04b7d000234b7dbd6da536740e0ccd"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json:   0%|          | 0.00/363 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "ee79b8d34e9147bb8a98a3c3595cba89"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["vocab.txt:   0%|          | 0.00/232k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "d506e084a29d40f39d64cf0822c801d7"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.json:   0%|          | 0.00/466k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "5b6e93ec87b34053985cb04e88f616e2"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["special_tokens_map.json:   0%|          | 0.00/239 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "53e061c920a946f18f9a7ccb75780a45"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["1_Pooling/config.json:   0%|          | 0.00/190 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "926b7aa7a8c843c1a7677db4ac1e4d21"}}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["# you can load multiple types of pdf using the langchain just check with the document\n", "\n", "https://python.langchain.com/docs/modules/data_connection/document_loaders/pdf/"], "metadata": {"id": "WHwqV_H6pFqW"}}, {"cell_type": "code", "source": ["from langchain.document_loaders import PyPDFLoader\n", "loader = PyPDFLoader(\"/content/RAG.pdf\", extract_images=True)\n", "pages = loader.load()"], "metadata": {"id": "0-YiQTCmo74-"}, "execution_count": 3, "outputs": []}, {"cell_type": "code", "source": ["pages"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "18DG7rJtqEsV", "outputId": "30dd8c4b-eba7-4653-df4e-f58d1c5ad11a"}, "execution_count": 4, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='Retrieval-Augmented Generation for\\nKnowledge-Intensive NLP Tasks\\n<PERSON><PERSON><PERSON>†‡, <PERSON> Perez⋆,\\<PERSON><PERSON><PERSON><PERSON><PERSON>†, <PERSON><PERSON><PERSON>†, <PERSON>†, <PERSON><PERSON>†, <PERSON>†,\\<PERSON><PERSON><PERSON>†, <PERSON><PERSON><PERSON><PERSON>†, <PERSON>†‡, <PERSON>†‡, <PERSON><PERSON><PERSON>†\\n†Facebook AI Research;‡University College London;⋆New York University;\\<EMAIL>\\nAbstract\\nLarge pre-trained language models have been shown to store factual knowledge\\nin their parameters, and achieve state-of-the-art results when ﬁne-tuned on down-\\nstream NLP tasks. However, their ability to access and precisely manipulate knowl-\\nedge is still limited, and hence on knowledge-intensive tasks, their performance\\nlags behind task-speciﬁc architectures. Additionally, providing provenance for their\\ndecisions and updating their world knowledge remain open research problems. Pre-\\ntrained models with a differentiable access mechanism to explicit non-parametric\\nmemory have so far been only investigated for extractive downstream tasks. We\\nexplore a general-purpose ﬁne-tuning recipe for retrieval-augmented generation\\n(RAG) — models which combine pre-trained parametric and non-parametric mem-\\nory for language generation. We introduce RAG models where the parametric\\nmemory is a pre-trained seq2seq model and the non-parametric memory is a dense\\nvector index of Wikipedia, accessed with a pre-trained neural retriever. We com-\\npare two RAG formulations, one which conditions on the same retrieved passages\\nacross the whole generated sequence, and another which can use different passages\\nper token. We ﬁne-tune and evaluate our models on a wide range of knowledge-\\nintensive NLP tasks and set the state of the art on three open domain QA tasks,\\noutperforming parametric seq2seq models and task-speciﬁc retrieve-and-extract\\narchitectures. For language generation tasks, we ﬁnd that RAG models generate\\nmore speciﬁc, diverse and factual language than a state-of-the-art parametric-only\\nseq2seq baseline.\\n1 Introduction\\nPre-trained neural language models have been shown to learn a substantial amount of in-depth knowl-\\nedge from data [ 47]. They can do so without any access to an external memory, as a parameterized\\nimplicit knowledge base [ 51,52]. While this development is exciting, such models do have down-\\nsides: They cannot easily expand or revise their memory, can’t straightforwardly provide insight into\\ntheir predictions, and may produce “hallucinations” [ 38]. Hybrid models that combine parametric\\nmemory with non-parametric (i.e., retrieval-based) memories [ 20,26,48] can address some of these\\nissues because knowledge can be directly revised and expanded, and accessed knowledge can be\\ninspected and interpreted. REALM [ 20] and ORQA [ 31], two recently introduced models that\\ncombine masked language models [ 8] with a differentiable retriever, have shown promising results,arXiv:2005.11401v4  [cs.CL]  12 Apr 2021', metadata={'source': '/content/RAG.pdf', 'page': 0}),\n", " Document(page_content='The\\tDivine\\nComedy\\t(x) qQuery\\nEncoder\\nq(x)\\nMIPS p θGenerator \\xa0pθ\\n(Parametric)\\nMargin-\\nalize\\nThis\\t14th\\tcentury\\twork\\nis\\tdivided\\tinto\\t3\\nsections:\\t\"Inferno\",\\n\"Purgatorio\"\\t&\\n\"Paradiso\"\\t\\t\\t\\t\\t\\t\\t\\t\\t (y)End-to-End Backprop through q and\\xa0 p θ\\nBarack\\tObama\\twas\\nborn\\tin\\tHawaii. (x)\\nFact V eriﬁcation: Fact Querysupports \\t(y)\\nQuestion GenerationFact V eriﬁcation:\\nLabel GenerationDocument\\nIndexDefine\\t\"middle\\tear\" (x)\\nQuestion Answering:\\nQuestion QueryThe\\tmiddle\\tear\\tincludes\\nthe\\ttympanic\\tcavity\\tand\\nthe\\tthree\\tossicles.\\t\\t (y)\\nQuestion Answering:\\nAnswer GenerationRetriever pη\\n(Non-Parametric)\\nz 4\\nz3\\nz2\\nz 1d(z)\\nJeopardy Question\\nGeneration:\\nAnswer QueryFigure 1: Overview of our approach. We combine a pre-trained retriever ( Query Encoder +Document\\nIndex ) with a pre-trained seq2seq model ( Generator ) and ﬁne-tune end-to-end. For query x, we use\\nMaximum Inner Product Search (MIPS) to ﬁnd the top-K documents zi. For ﬁnal prediction y, we\\ntreatzas a latent variable and marginalize over seq2seq predictions given different documents.\\nbut have only explored open-domain extractive question answering. Here, we bring hybrid parametric\\nand non-parametric memory to the “workhorse of NLP,” i.e. sequence-to-sequence (seq2seq) models.\\nWe endow pre-trained, parametric-memory generation models with a non-parametric memory through\\na general-purpose ﬁne-tuning approach which we refer to as retrieval-augmented generation (RAG).\\nWe build RAG models where the parametric memory is a pre-trained seq2seq transformer, and the\\nnon-parametric memory is a dense vector index of Wikipedia, accessed with a pre-trained neural\\nretriever. We combine these components in a probabilistic model trained end-to-end (Fig. 1). The\\nretriever (Dense Passage Retriever [ 26], henceforth DPR) provides latent documents conditioned on\\nthe input, and the seq2seq model (BART [ 32]) then conditions on these latent documents together with\\nthe input to generate the output. We marginalize the latent documents with a top-K approximation,\\neither on a per-output basis (assuming the same document is responsible for all tokens) or a per-token\\nbasis (where different documents are responsible for different tokens). Like T5 [ 51] or BART, RAG\\ncan be ﬁne-tuned on any seq2seq task, whereby both the generator and retriever are jointly learned.\\nThere has been extensive previous work proposing architectures to enrich systems with non-parametric\\nmemory which are trained from scratch for speciﬁc tasks, e.g. memory networks [ 64,55], stack-\\naugmented networks [ 25] and memory layers [ 30]. In contrast, we explore a setting where both\\nparametric and non-parametric memory components are pre-trained and pre-loaded with extensive\\nknowledge. Crucially, by using pre-trained access mechanisms, the ability to access knowledge is\\npresent without additional training.\\nOur results highlight the beneﬁts of combining parametric and non-parametric memory with genera-\\ntion for knowledge-intensive tasks —tasks that humans could not reasonably be expected to perform\\nwithout access to an external knowledge source. Our RAG models achieve state-of-the-art results\\non open Natural Questions [ 29], WebQuestions [ 3] and CuratedTrec [ 2] and strongly outperform\\nrecent approaches that use specialised pre-training objectives on TriviaQA [ 24]. Despite these being\\nextractive tasks, we ﬁnd that unconstrained generation outperforms previous extractive approaches.\\nFor knowledge-intensive generation, we experiment with MS-MARCO [ 1] and Jeopardy question\\ngeneration, and we ﬁnd that our models generate responses that are more factual, speciﬁc, and\\ndiverse than a BART baseline. For FEVER [ 56] fact veriﬁcation, we achieve results within 4.3% of\\nstate-of-the-art pipeline models which use strong retrieval supervision. Finally, we demonstrate that\\nthe non-parametric memory can be replaced to update the models’ knowledge as the world changes.1\\n2 Methods\\nWe explore RAG models, which use the input sequence xto retrieve text documents zand use them\\nas additional context when generating the target sequence y. As shown in Figure 1, our models\\nleverage two components: (i) a retriever pη(z|x)with parameters ηthat returns (top-K truncated)\\ndistributions over text passages given a query xand (ii) a generator pθ(yi|x,z,y 1:i−1)parametrized\\n1Code to run experiments with RAG has been open-sourced as part of the HuggingFace Transform-\\ners Library [ 66] and can be found at https://github.com/huggingface/transformers/blob/master/\\nexamples/rag/ . An interactive demo of RAG models can be found at https://huggingface.co/rag/\\n2', metadata={'source': '/content/RAG.pdf', 'page': 1}),\n", " Document(page_content='byθthat generates a current token based on a context of the previous i−1tokensy1:i−1, the original\\ninputxand a retrieved passage z.\\nTo train the retriever and generator end-to-end, we treat the retrieved document as a latent variable.\\nWe propose two models that marginalize over the latent documents in different ways to produce a\\ndistribution over generated text. In one approach, RAG-Sequence , the model uses the same document\\nto predict each target token. The second approach, RAG-Token , can predict each target token based\\non a different document. In the following, we formally introduce both models and then describe the\\npηandpθcomponents, as well as the training and decoding procedure.\\n2.1 Models\\nRAG-Sequence Model The RAG-Sequence model uses the same retrieved document to generate\\nthe complete sequence . Technically, it treats the retrieved document as a single latent variable that\\nis marginalized to get the seq2seq probability p(y|x)via a top-K approximation. Concretely, the\\ntop K documents are retrieved using the retriever, and the generator produces the output sequence\\nprobability for each document, which are then marginalized,\\npRAG-Sequence (y|x)≈∑\\nz∈top-k(p(·|x))pη(z|x)pθ(y|x,z) =∑\\nz∈top-k(p(·|x))pη(z|x)N∏\\nipθ(yi|x,z,y 1:i−1)\\nRAG-Token Model In the RAG-Token model we can draw a different latent document for each\\ntarget token and marginalize accordingly. This allows the generator to choose content from several\\ndocuments when producing an answer. Concretely, the top K documents are retrieved using the\\nretriever, and then the generator produces a distribution for the next output token for each document,\\nbefore marginalizing, and repeating the process with the following output token, Formally, we deﬁne:\\npRAG-Token (y|x)≈N∏\\ni∑\\nz∈top-k(p(·|x))pη(z|x)pθ(yi|x,z,y 1:i−1)\\nFinally, we note that RAG can be used for sequence classiﬁcation tasks by considering the target class\\nas a target sequence of length one, in which case RAG-Sequence and RAG-Token are equivalent.\\n2.2 Retriever: DPR\\nThe retrieval component pη(z|x)is based on DPR [26]. DPR follows a bi-encoder architecture:\\npη(z|x)∝exp(\\nd(z)⊤q(x))\\nd(z) =BERTd(z),q(x) =BERTq(x)\\nwhere d(z)is a dense representation of a document produced by a BERT BASE document encoder [8],\\nandq(x)a query representation produced by a query encoder , also based on BERT BASE. Calculating\\ntop-k (pη(·|x)), the list ofkdocumentszwith highest prior probability pη(z|x), is a Maximum Inner\\nProduct Search (MIPS) problem, which can be approximately solved in sub-linear time [ 23]. We use\\na pre-trained bi-encoder from DPR to initialize our retriever and to build the document index. This\\nretriever was trained to retrieve documents which contain answers to TriviaQA [ 24] questions and\\nNatural Questions [29]. We refer to the document index as the non-parametric memory .\\n2.3 Generator: BART\\nThe generator component pθ(yi|x,z,y 1:i−1)could be modelled using any encoder-decoder. We use\\nBART-large [ 32], a pre-trained seq2seq transformer [ 58] with 400M parameters. To combine the input\\nxwith the retrieved content zwhen generating from BART, we simply concatenate them. BART was\\npre-trained using a denoising objective and a variety of different noising functions. It has obtained\\nstate-of-the-art results on a diverse set of generation tasks and outperforms comparably-sized T5\\nmodels [32]. We refer to the BART generator parameters θas the parametric memory henceforth.\\n2.4 Training\\nWe jointly train the retriever and generator components without any direct supervision on what\\ndocument should be retrieved. Given a ﬁne-tuning training corpus of input/output pairs (xj,yj), we\\n3', metadata={'source': '/content/RAG.pdf', 'page': 2}),\n", " Document(page_content='minimize the negative marginal log-likelihood of each target,∑\\nj−logp(yj|xj)using stochastic\\ngradient descent with <PERSON> [ 28]. Updating the document encoder BERTdduring training is costly as\\nit requires the document index to be periodically updated as REALM does during pre-training [ 20].\\nWe do not ﬁnd this step necessary for strong performance, and keep the document encoder (and\\nindex) ﬁxed, only ﬁne-tuning the query encoder BERT qand the BART generator.\\n2.5 Decoding\\nAt test time, RAG-Sequence and RAG-Token require different ways to approximate arg maxyp(y|x).\\nRAG-Token The RAG-Token model can be seen as a standard, autoregressive seq2seq genera-\\ntor with transition probability: p′\\nθ(yi|x,y 1:i−1) =∑\\nz∈top-k(p(·|x))pη(zi|x)pθ(yi|x,zi,y1:i−1)To\\ndecode, we can plug p′\\nθ(yi|x,y 1:i−1)into a standard beam decoder.\\nRAG-Sequence For RAG-Sequence, the likelihood p(y|x)does not break into a conventional per-\\ntoken likelihood, hence we cannot solve it with a single beam search. Instead, we run beam search for\\neach document z, scoring each hypothesis using pθ(yi|x,z,y 1:i−1). This yields a set of hypotheses\\nY, some of which may not have appeared in the beams of all documents. To estimate the probability\\nof an hypothesis ywe run an additional forward pass for each document zfor whichydoes not\\nappear in the beam, multiply generator probability with pη(z|x)and then sum the probabilities across\\nbeams for the marginals. We refer to this decoding procedure as “Thorough Decoding.” For longer\\noutput sequences,|Y|can become large, requiring many forward passes. For more efﬁcient decoding,\\nwe can make a further approximation that pθ(y|x,zi)≈0whereywas not generated during beam\\nsearch from x,zi. This avoids the need to run additional forward passes once the candidate set Yhas\\nbeen generated. We refer to this decoding procedure as “Fast Decoding.”\\n3 Experiments\\nWe experiment with RAG in a wide range of knowledge-intensive tasks. For all experiments, we use\\na single Wikipedia dump for our non-parametric knowledge source. Following Lee et al. [31] and\\nKarpukhin et al. [26], we use the December 2018 dump. Each Wikipedia article is split into disjoint\\n100-word chunks, to make a total of 21M documents. We use the document encoder to compute an\\nembedding for each document, and build a single MIPS index using FAISS [ 23] with a Hierarchical\\nNavigable Small World approximation for fast retrieval [ 37]. During training, we retrieve the top\\nkdocuments for each query. We consider k∈{5,10}for training and set kfor test time using dev\\ndata. We now discuss experimental details for each task.\\n3.1 Open-domain Question Answering\\nOpen-domain question answering (QA) is an important real-world application and common testbed\\nfor knowledge-intensive tasks [ 20]. We treat questions and answers as input-output text pairs (x,y)\\nand train RAG by directly minimizing the negative log-likelihood of answers. We compare RAG to\\nthe popular extractive QA paradigm [ 5,7,31,26], where answers are extracted spans from retrieved\\ndocuments, relying primarily on non-parametric knowledge. We also compare to “Closed-Book\\nQA” approaches [ 52], which, like RAG, generate answers, but which do not exploit retrieval, instead\\nrelying purely on parametric knowledge. We consider four popular open-domain QA datasets: Natural\\nQuestions (NQ) [ 29], TriviaQA (TQA) [ 24]. WebQuestions (WQ) [ 3] and CuratedTrec (CT) [ 2]. As\\nCT and WQ are small, we follow DPR [ 26] by initializing CT and WQ models with our NQ RAG\\nmodel. We use the same train/dev/test splits as prior work [ 31,26] and report Exact Match (EM)\\nscores. For TQA, to compare with T5 [52], we also evaluate on the TQA Wiki test set.\\n3.2 Abstractive Question Answering\\nRAG models can go beyond simple extractive QA and answer questions with free-form, abstractive\\ntext generation. To test RAG’s natural language generation (NLG) in a knowledge-intensive setting,\\nwe use the MSMARCO NLG task v2.1 [ 43]. The task consists of questions, ten gold passages\\nretrieved from a search engine for each question, and a full sentence answer annotated from the\\nretrieved passages. We do not use the supplied passages, only the questions and answers, to treat\\n4', metadata={'source': '/content/RAG.pdf', 'page': 3}),\n", " Document(page_content='MSMARCO as an open-domain abstractive QA task. MSMARCO has some questions that cannot be\\nanswered in a way that matches the reference answer without access to the gold passages, such as\\n“What is the weather in V olcano, CA?” so performance will be lower without using gold passages.\\nWe also note that some MSMARCO questions cannot be answered using Wikipedia alone. Here,\\nRAG can rely on parametric knowledge to generate reasonable responses.\\n3.3 Jeopardy Question Generation\\nTo evaluate RAG’s generation abilities in a non-QA setting, we study open-domain question gen-\\neration. Rather than use questions from standard open-domain QA tasks, which typically consist\\nof short, simple questions, we propose the more demanding task of generating Jeopardy questions.\\nJeopardy is an unusual format that consists of trying to guess an entity from a fact about that entity.\\nFor example, “The World Cup” is the answer to the question “In 1986 Mexico scored as the ﬁrst\\ncountry to host this international sports competition twice.” As Jeopardy questions are precise,\\nfactual statements, generating Jeopardy questions conditioned on their answer entities constitutes a\\nchallenging knowledge-intensive generation task.\\nWe use the splits from SearchQA [ 10], with 100K train, 14K dev, and 27K test examples. As\\nthis is a new task, we train a BART model for comparison. Following [ 67], we evaluate using the\\nSQuAD-tuned Q-BLEU-1 metric [ 42]. Q-BLEU is a variant of BLEU with a higher weight for\\nmatching entities and has higher correlation with human judgment for question generation than\\nstandard metrics. We also perform two human evaluations, one to assess generation factuality, and\\none for speciﬁcity. We deﬁne factuality as whether a statement can be corroborated by trusted external\\nsources, and speciﬁcity as high mutual dependence between the input and output [ 33]. We follow\\nbest practice and use pairwise comparative evaluation [ 34]. Evaluators are shown an answer and two\\ngenerated questions, one from BART and one from RAG. They are then asked to pick one of four\\noptions—quuestion A is better, question B is better, both are good, or neither is good.\\n3.4 Fact Veriﬁcation\\nFEVER [ 56] requires classifying whether a natural language claim is supported or refuted by\\nWikipedia, or whether there is not enough information to decide. The task requires retrieving\\nevidence from Wikipedia relating to the claim and then reasoning over this evidence to classify\\nwhether the claim is true, false, or unveriﬁable from Wikipedia alone. FEVER is a retrieval problem\\ncoupled with an challenging entailment reasoning task. It also provides an appropriate testbed for\\nexploring the RAG models’ ability to handle classiﬁcation rather than generation. We map FEVER\\nclass labels (supports, refutes, or not enough info) to single output tokens and directly train with\\nclaim-class pairs. Crucially, unlike most other approaches to FEVER, we do not use supervision on\\nretrieved evidence. In many real-world applications, retrieval supervision signals aren’t available, and\\nmodels that do not require such supervision will be applicable to a wider range of tasks. We explore\\ntwo variants: the standard 3-way classiﬁcation task (supports/refutes/not enough info) and the 2-way\\n(supports/refutes) task studied in Thorne and Vlachos [57]. In both cases we report label accuracy.\\n4 Results\\n4.1 Open-domain Question Answering\\nTable 1 shows results for RAG along with state-of-the-art models. On all four open-domain QA\\ntasks, RAG sets a new state of the art (only on the T5-comparable split for TQA). RAG combines\\nthe generation ﬂexibility of the “closed-book” (parametric only) approaches and the performance of\\n\"open-book\" retrieval-based approaches. Unlike REALM and T5+SSM, RAG enjoys strong results\\nwithout expensive, specialized “salient span masking” pre-training [ 20]. It is worth noting that RAG’s\\nretriever is initialized using DPR’s retriever, which uses retrieval supervision on Natural Questions\\nand TriviaQA. RAG compares favourably to the DPR QA system, which uses a BERT-based “cross-\\nencoder” to re-rank documents, along with an extractive reader. RAG demonstrates that neither a\\nre-ranker nor extractive reader is necessary for state-of-the-art performance.\\nThere are several advantages to generating answers even when it is possible to extract them. Docu-\\nments with clues about the answer but do not contain the answer verbatim can still contribute towards\\na correct answer being generated, which is not possible with standard extractive approaches, leading\\n5', metadata={'source': '/content/RAG.pdf', 'page': 4}),\n", " Document(page_content='Table 1: Open-Domain QA Test Scores. For TQA,\\nleft column uses the standard test set for Open-\\nDomain QA, right column uses the TQA-Wiki\\ntest set. See Appendix D for further details.\\nModel NQ TQA WQ CT\\nClosed\\nBookT5-11B [52] 34.5 - /50.1 37.4 -\\nT5-11B+SSM[52] 36.6 - /60.5 44.7 -\\nOpen\\nBookREALM [20] 40.4 - / - 40.7 46.8\\nDPR [26] 41.5 57.9/ - 41.1 50.6\\nRAG-Token 44.1 55.2/66.1 45.5 50.0\\nRAG-Seq. 44.5 56.8/ 68.0 45.2 52.2Table 2: Generation and classiﬁcation Test Scores.\\nMS-MARCO SotA is [ 4], FEVER-3 is [ 68] and\\nFEVER-2 is [ 57] *Uses gold context/evidence.\\nBest model without gold access underlined.\\nModel Jeopardy MSMARCO FVR3 FVR2\\nB-1 QB-1 R-L B-1 Label Acc.\\nSotA - - 49.8*49.9*76.8 92.2 *\\nBART 15.1 19.7 38.2 41.6 64.0 81.1\\nRAG-Tok. 17.3 22.2 40.1 41.572.5 89.5RAG-Seq. 14.7 21.4 40.8 44.2\\nto more effective marginalization over documents. Furthermore, RAG can generate correct answers\\neven when the correct answer is not in any retrieved document, achieving 11.8% accuracy in such\\ncases for NQ, where an extractive model would score 0%.\\n4.2 Abstractive Question Answering\\nAs shown in Table 2, RAG-Sequence outperforms BART on Open MS-MARCO NLG by 2.6 Bleu\\npoints and 2.6 Rouge-L points. RAG approaches state-of-the-art model performance, which is\\nimpressive given that (i) those models access gold passages with speciﬁc information required to\\ngenerate the reference answer , (ii) many questions are unanswerable without the gold passages, and\\n(iii) not all questions are answerable from Wikipedia alone. Table 3 shows some generated answers\\nfrom our models. Qualitatively, we ﬁnd that RAG models hallucinate less and generate factually\\ncorrect text more often than BART. Later, we also show that RAG generations are more diverse than\\nBART generations (see §4.5).\\n4.3 Jeopardy Question Generation\\nTable 2 shows that RAG-Token performs better than RAG-Sequence on Jeopardy question generation,\\nwith both models outperforming BART on Q-BLEU-1. 4 shows human evaluation results, over 452\\npairs of generations from BART and RAG-Token. Evaluators indicated that BART was more factual\\nthan RAG in only 7.1% of cases, while RAG was more factual in 42.7% of cases, and both RAG and\\nBART were factual in a further 17% of cases, clearly demonstrating the effectiveness of RAG on\\nthe task over a state-of-the-art generation model. Evaluators also ﬁnd RAG generations to be more\\nspeciﬁc by a large margin. Table 3 shows typical generations from each model.\\nJeopardy questions often contain two separate pieces of information, and RAG-Token may perform\\nbest because it can generate responses that combine content from several documents. Figure 2 shows\\nan example. When generating “Sun”, the posterior is high for document 2 which mentions “The\\nSun Also Rises”. Similarly, document 1 dominates the posterior when “A Farewell to Arms” is\\ngenerated. Intriguingly, after the ﬁrst token of each book is generated, the document posterior ﬂattens.\\nThis observation suggests that the generator can complete the titles without depending on speciﬁc\\ndocuments. In other words, the model’s parametric knowledge is sufﬁcient to complete the titles. We\\nﬁnd evidence for this hypothesis by feeding the BART-only baseline with the partial decoding \"The\\nSun. BART completes the generation \"The SunAlso Rises\" isanovel bythis author of\"The Sun\\nAlso Rises\" indicating the title \"The Sun Also Rises\" is stored in BART’s parameters. Similarly,\\nBART will complete the partial decoding \"The SunAlso Rises\" isanovel bythis author of\"A\\nwith \"The SunAlso Rises\" isanovel bythis author of\"AFarewell toArms\" . This example shows\\nhow parametric and non-parametric memories work together —the non-parametric component helps\\nto guide the generation, drawing out speciﬁc knowledge stored in the parametric memory.\\n4.4 Fact Veriﬁcation\\nTable 2 shows our results on FEVER. For 3-way classiﬁcation, RAG scores are within 4.3% of\\nstate-of-the-art models, which are complex pipeline systems with domain-speciﬁc architectures and\\nsubstantial engineering, trained using intermediate retrieval supervision, which RAG does not require.\\n6', metadata={'source': '/content/RAG.pdf', 'page': 5}),\n", " Document(page_content='Document 1 : his works are considered classics of American\\nliterature ... His wartime experiences formed the basis for his novel\\n”A Farewell to Arms” (1929) ...\\nDocument 2 : ... artists of the 1920s ”Lost Generation” expatriate\\ncommunity. His debut novel, ”The Sun Also Rises” , was published\\nin 1926.\\nBOS”\\nTheSunAlsoRises”isa\\nnovelbythis\\nauthorof”A\\nFarewellto\\nArms”Doc 1\\nDoc 2\\nDoc 3\\nDoc 4\\nDoc 5Figure 2: RAG-Token document posterior p(zi|x,yi,y−i)for each generated token for input “Hem-\\ningway\" for Jeopardy generation with 5 retrieved documents. The posterior for document 1 is high\\nwhen generating “A Farewell to Arms\" and for document 2 when generating “The Sun Also Rises\".\\nTable 3: Examples from generation tasks. RAG models generate more speciﬁc and factually accurate\\nresponses. ‘?’ indicates factually incorrect responses, * indicates partially correct responses.\\nTask Input Model Generation\\nMS-\\nMARCOdeﬁne middle\\nearBART?The middle ear is the part of the ear between the middle ear and the nose.\\nRAG-T The middle ear is the portion of the ear internal to the eardrum.\\nRAG-S The middle ear includes the tympanic cavity and the three ossicles.\\nwhat currency\\nneeded in\\nscotlandBART The currency needed in Scotland is Pound sterling.\\nRAG-T Pound is the currency needed in Scotland.\\nRAG-S The currency needed in Scotland is the pound sterling.\\nJeopardy\\nQuestion\\nGener\\n-ationWashingtonBART?This state has the largest number of counties in the U.S.\\nRAG-T It’s the only U.S. state named for a U.S. president\\nRAG-S It’s the state where you’ll ﬁnd Mount Rainier National Park\\nThe Divine\\nComedyBART*This epic poem by Dante is divided into 3 parts: the Inferno, the Purgatorio & the Purgatorio\\nRAG-T Dante’s \"Inferno\" is the ﬁrst part of this epic poem\\nRAG-S This 14th century work is divided into 3 sections: \"Inferno\", \"Purgatorio\" & \"Paradiso\"\\nFor 2-way classiﬁcation, we compare against Thorne and Vlachos [57], who train RoBERTa [ 35]\\nto classify the claim as true or false given the gold evidence sentence. RAG achieves an accuracy\\nwithin 2.7% of this model, despite being supplied with only the claim and retrieving its own evidence.\\nWe also analyze whether documents retrieved by RAG correspond to documents annotated as gold\\nevidence in FEVER. We calculate the overlap in article titles between the top kdocuments retrieved\\nby RAG and gold evidence annotations. We ﬁnd that the top retrieved document is from a gold article\\nin 71% of cases, and a gold article is present in the top 10 retrieved articles in 90% of cases.\\n4.5 Additional Results\\nGeneration Diversity Section 4.3 shows that RAG models are more factual and speciﬁc than\\nBART for Jeopardy question generation. Following recent work on diversity-promoting decoding\\n[33,59,39], we also investigate generation diversity by calculating the ratio of distinct ngrams to\\ntotal ngrams generated by different models. Table 5 shows that RAG-Sequence’s generations are\\nmore diverse than RAG-Token’s, and both are signiﬁcantly more diverse than BART without needing\\nany diversity-promoting decoding.\\nRetrieval Ablations A key feature of RAG is learning to retrieve relevant information for the task.\\nTo assess the effectiveness of the retrieval mechanism, we run ablations where we freeze the retriever\\nduring training. As shown in Table 6, learned retrieval improves results for all tasks.\\nWe compare RAG’s dense retriever to a word overlap-based BM25 retriever [ 53]. Here, we replace\\nRAG’s retriever with a ﬁxed BM25 system, and use BM25 retrieval scores as logits when calculating\\np(z|x). Table 6 shows the results. For FEVER, BM25 performs best, perhaps since FEVER claims are\\nheavily entity-centric and thus well-suited for word overlap-based retrieval. Differentiable retrieval\\nimproves results on all other tasks, especially for Open-Domain QA, where it is crucial.\\nIndex hot-swapping An advantage of non-parametric memory models like RAG is that knowledge\\ncan be easily updated at test time. Parametric-only models like T5 or BART need further training to\\nupdate their behavior as the world changes. To demonstrate, we build an index using the DrQA [ 5]\\nWikipedia dump from December 2016 and compare outputs from RAG using this index to the newer\\nindex from our main results (December 2018). We prepare a list of 82 world leaders who had changed\\n7', metadata={'source': '/content/RAG.pdf', 'page': 6}),\n", " Document(page_content='Table 4: Human assessments for the Jeopardy\\nQuestion Generation Task.\\nFactuality Speciﬁcity\\nBART better 7.1% 16.8%\\nRAG better 42.7% 37.4%\\nBoth good 11.7% 11.8%\\nBoth poor 17.7% 6.9%\\nNo majority 20.8% 20.1%Table 5: Ratio of distinct to total tri-grams for\\ngeneration tasks.\\nMSMARCO Jeopardy QGen\\nGold 89.6% 90.0%\\nBART 70.7% 32.4%\\nRAG-Token 77.8% 46.8%\\nRAG-Seq. 83.5% 53.8%\\nTable 6: Ablations on the dev set. As FEVER is a classiﬁcation task, both RAG models are equivalent.\\nModel NQ TQA WQ CT Jeopardy-QGen MSMarco FVR-3 FVR-2\\nExact Match B-1 QB-1 R-L B-1 Label Accuracy\\nRAG-Token-BM25 29.7 41.5 32.1 33.1 17.5 22.3 55.5 48.475.1 91.6RAG-Sequence-BM25 31.8 44.1 36.6 33.8 11.1 19.5 56.5 46.9\\nRAG-Token-Frozen 37.8 50.1 37.1 51.1 16.7 21.7 55.9 49.472.9 89.4RAG-Se<PERSON>-<PERSON>ozen 41.2 52.1 41.8 52.6 11.8 19.6 56.7 47.3\\nRAG-Token 43.5 54.8 46.5 51.9 17.9 22.6 56.2 49.474.5 90.6RAG-Sequence 44.0 55.8 44.9 53.4 15.3 21.5 57.2 47.5\\nbetween these dates and use a template “Who is {position}?” (e.g. “Who is the President of Peru?”)\\nto query our NQ RAG model with each index. RAG answers 70% correctly using the 2016 index for\\n2016 world leaders and 68% using the 2018 index for 2018 world leaders. Accuracy with mismatched\\nindices is low (12% with the 2018 index and 2016 leaders, 4% with the 2016 index and 2018 leaders).\\nThis shows we can update RAG’s world knowledge by simply replacing its non-parametric memory.\\nEffect of Retrieving more documents Models are trained with either 5 or 10 retrieved latent\\ndocuments, and we do not observe signiﬁcant differences in performance between them. We have the\\nﬂexibility to adjust the number of retrieved documents at test time, which can affect performance and\\nruntime. Figure 3 (left) shows that retrieving more documents at test time monotonically improves\\nOpen-domain QA results for RAG-Sequence, but performance peaks for RAG-Token at 10 retrieved\\ndocuments. Figure 3 (right) shows that retrieving more documents leads to higher Rouge-L for\\nRAG-Token at the expense of Bleu-1, but the effect is less pronounced for RAG-Sequence.\\n10 20 30 40 50\\nKR e t r i e v e dD o c s394041424344NQ Exact MatchRAG-Tok\\nRAG-Seq\\n10 20 30 40 50\\nKR e t r i e v e dD o c s4050607080NQ Answer Recall @ KRAG-Tok\\nRAG-Seq\\nFixed DPR\\nBM25\\n10 20 30 40 50\\nKR e t r i e v e dD o c s4850525456Bleu-1 / Rouge-L scoreRAG-Tok R-L\\nRAG-Tok B-1\\nRAG-Seq R-L\\nRAG-Seq B-1\\nFigure 3: Left: NQ performance as more documents are retrieved. Center: Retrieval recall perfor-\\nmance in NQ. Right: MS-MARCO Bleu-1 and Rouge-L as more documents are retrieved.\\n5 Related Work\\nSingle-Task Retrieval Prior work has shown that retrieval improves performance across a variety of\\nNLP tasks when considered in isolation. Such tasks include open-domain question answering [ 5,29],\\nfact checking [ 56], fact completion [ 48], long-form question answering [ 12], Wikipedia article\\ngeneration [ 36], dialogue [ 41,65,9,13], translation [ 17], and language modeling [ 19,27]. Our\\nwork uniﬁes previous successes in incorporating retrieval into individual tasks, showing that a single\\nretrieval-based architecture is capable of achieving strong performance across several tasks.\\n8', metadata={'source': '/content/RAG.pdf', 'page': 7}),\n", " Document(page_content='General-Purpose Architectures for NLP Prior work on general-purpose architectures for NLP\\ntasks has shown great success without the use of retrieval. A single, pre-trained language model\\nhas been shown to achieve strong performance on various classiﬁcation tasks in the GLUE bench-\\nmarks [ 60,61] after ﬁne-tuning [ 49,8]. GPT-2 [ 50] later showed that a single, left-to-right, pre-trained\\nlanguage model could achieve strong performance across both discriminative and generative tasks.\\nFor further improvement, BART [ 32] and T5 [ 51,52] propose a single, pre-trained encoder-decoder\\nmodel that leverages bi-directional attention to achieve stronger performance on discriminative\\nand generative tasks. Our work aims to expand the space of possible tasks with a single, uniﬁed\\narchitecture, by learning a retrieval module to augment pre-trained, generative language models.\\nLearned Retrieval There is signiﬁcant work on learning to retrieve documents in information\\nretrieval, more recently with pre-trained, neural language models [ 44,26] similar to ours. Some\\nwork optimizes the retrieval module to aid in a speciﬁc, downstream task such as question answering,\\nusing search [ 46], reinforcement learning [ 6,63,62], or a latent variable approach [ 31,20] as in our\\nwork. These successes leverage different retrieval-based architectures and optimization techniques to\\nachieve strong performance on a single task, while we show that a single retrieval-based architecture\\ncan be ﬁne-tuned for strong performance on a variety of tasks.\\nMemory-based Architectures Our document index can be seen as a large external memory for\\nneural networks to attend to, analogous to memory networks [ 64,55]. Concurrent work [ 14] learns\\nto retrieve a trained embedding for each entity in the input, rather than to retrieve raw text as in our\\nwork. Other work improves the ability of dialog models to generate factual text by attending over\\nfact embeddings [ 15,13]. A key feature of our memory is that it is comprised of raw text rather\\ndistributed representations, which makes the memory both (i) human-readable, lending a form of\\ninterpretability to our model, and (ii) human-writable, enabling us to dynamically update the model’s\\nmemory by editing the document index. This approach has also been used in knowledge-intensive\\ndialog, where generators have been conditioned on retrieved text directly, albeit obtained via TF-IDF\\nrather than end-to-end learnt retrieval [9].\\nRetrieve-and-Edit approaches Our method shares some similarities with retrieve-and-edit style\\napproaches, where a similar training input-output pair is retrieved for a given input, and then edited\\nto provide a ﬁnal output. These approaches have proved successful in a number of domains including\\nMachine Translation [ 18,22] and Semantic Parsing [ 21]. Our approach does have several differences,\\nincluding less of emphasis on lightly editing a retrieved item, but on aggregating content from several\\npieces of retrieved content, as well as learning latent retrieval, and retrieving evidence documents\\nrather than related training pairs. This said, RAG techniques may work well in these settings, and\\ncould represent promising future work.\\n6 Discussion\\nIn this work, we presented hybrid generation models with access to parametric and non-parametric\\nmemory. We showed that our RAG models obtain state of the art results on open-domain QA. We\\nfound that people prefer RAG’s generation over purely parametric BART, ﬁnding RAG more factual\\nand speciﬁc. We conducted an thorough investigation of the learned retrieval component, validating\\nits effectiveness, and we illustrated how the retrieval index can be hot-swapped to update the model\\nwithout requiring any retraining. In future work, it may be fruitful to investigate if the two components\\ncan be jointly pre-trained from scratch, either with a denoising objective similar to BART or some\\nanother objective. Our work opens up new research directions on how parametric and non-parametric\\nmemories interact and how to most effectively combine them, showing promise in being applied to a\\nwide variety of NLP tasks.\\n9', metadata={'source': '/content/RAG.pdf', 'page': 8}),\n", " Document(page_content='Broader Impact\\nThis work offers several positive societal beneﬁts over previous work: the fact that it is more\\nstrongly grounded in real factual knowledge (in this case Wikipedia) makes it “hallucinate” less\\nwith generations that are more factual, and offers more control and interpretability. RAG could be\\nemployed in a wide variety of scenarios with direct beneﬁt to society, for example by endowing it\\nwith a medical index and asking it open-domain questions on that topic, or by helping people be more\\neffective at their jobs.\\nWith these advantages also come potential downsides: Wikipedia, or any potential external knowledge\\nsource, will probably never be entirely factual and completely devoid of bias. Since RAG can be\\nemployed as a language model, similar concerns as for GPT-2 [ 50] are valid here, although arguably\\nto a lesser extent, including that it might be used to generate abuse, faked or misleading content in\\nthe news or on social media; to impersonate others; or to automate the production of spam/phishing\\ncontent [ 54]. Advanced language models may also lead to the automation of various jobs in the\\ncoming decades [ 16]. In order to mitigate these risks, AI systems could be employed to ﬁght against\\nmisleading content and automated spam/phishing.\\nAcknowledgments\\nThe authors would like to thank the reviewers for their thoughtful and constructive feedback on this\\npaper, as well as HuggingFace for their help in open-sourcing code to run RAG models. The authors\\nwould also like to thank Kyunghyun Cho and Sewon Min for productive discussions and advice. EP\\nthanks supports from the NSF Graduate Research Fellowship. PL is supported by the FAIR PhD\\nprogram.\\nReferences\\n[1]Payal Bajaj, Daniel Campos, Nick Craswell, Li Deng, Jianfeng Gao, Xiaodong Liu, Rangan\\nMajumder, Andrew McNamara, Bhaskar Mitra, Tri Nguyen, Mir Rosenberg, Xia Song, Alina\\nStoica, Saurabh Tiwary, and Tong Wang. MS MARCO: A Human Generated MAchine\\nReading COmprehension Dataset. arXiv:1611.09268 [cs] , November 2016. URL http:\\n//arxiv.org/abs/1611.09268 . arXiv: 1611.09268.\\n[2]Petr Baudiš and Jan Šediv `y. Modeling of the question answering task in the yodaqa system. In\\nInternational Conference of the Cross-Language Evaluation Forum for European Languages ,\\npages 222–228. Springer, 2015. URL https://link.springer.com/chapter/10.1007%\\n2F978-3-319-24027-5_20 .\\n[3]Jonathan Berant, Andrew Chou, Roy Frostig, and Percy Liang. Semantic Parsing on Freebase\\nfrom Question-Answer Pairs. In Proceedings of the 2013 Conference on Empirical Methods\\nin Natural Language Processing , pages 1533–1544, Seattle, Washington, USA, October 2013.\\nAssociation for Computational Linguistics. URL http://www.aclweb.org/anthology/\\nD13-1160 .\\n[4]Bin Bi, Chenliang Li, Chen Wu, Ming Yan, and Wei Wang. Palm: Pre-training an autoencod-\\ning&autoregressive language model for context-conditioned generation. ArXiv , abs/2004.07159,\\n2020. URL https://arxiv.org/abs/2004.07159 .\\n[5]Danqi Chen, Adam Fisch, Jason Weston, and Antoine Bordes. Reading Wikipedia to Answer\\nOpen-Domain Questions. In Proceedings of the 55th Annual Meeting of the Association for\\nComputational Linguistics (Volume 1: Long Papers) , pages 1870–1879, Vancouver, Canada,\\nJuly 2017. Association for Computational Linguistics. doi: 10.18653/v1/P17-1171. URL\\nhttps://www.aclweb.org/anthology/P17-1171 .\\n[6]Eunsol Choi, Daniel Hewlett, Jakob Uszkoreit, Illia Polosukhin, Alexandre Lacoste, and\\nJonathan Berant. Coarse-to-ﬁne question answering for long documents. In Proceedings of the\\n55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers) ,\\npages 209–220, Vancouver, Canada, July 2017. Association for Computational Linguistics. doi:\\n10.18653/v1/P17-1020. URL https://www.aclweb.org/anthology/P17-1020 .\\n10', metadata={'source': '/content/RAG.pdf', 'page': 9}),\n", " Document(page_content='[7]<PERSON> and <PERSON>. Simple and Effective Multi-Paragraph Reading Compre-\\nhension. arXiv:1710.10723 [cs] , October 2017. URL http://arxiv.org/abs/1710.10723 .\\narXiv: 1710.10723.\\n[8]<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. BERT: Pre-training of\\nDeep Bidirectional Transformers for Language Understanding. In Proceedings of the 2019 Con-\\nference of the North American Chapter of the Association for Computational Linguistics: Human\\nLanguage Technologies, Volume 1 (Long and Short Papers) , pages 4171–4186, Minneapolis,\\nMinnesota, June 2019. Association for Computational Linguistics. doi: 10.18653/v1/N19-1423.\\nURL https://www.aclweb.org/anthology/N19-1423 .\\n[9]<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Wiz-\\nard of wikipedia: Knowledge-powered conversational agents. In International Conference on\\nLearning Representations , 2019. URL https://openreview.net/forum?id=r1l73iRqKm .\\n[10] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> <PERSON>, <PERSON>, and <PERSON>\\nC<PERSON>. SearchQA: A New Q&A Dataset Augmented with Context from a Search Engine.\\narXiv:1704.05179 [cs] , April 2017. URL http://arxiv.org/abs/1704.05179 . arXiv:\\n1704.05179.\\n[11] Angela Fan, Mike <PERSON>, and <PERSON>n Dauphin. Hierarchical neural story generation. In Proceed-\\nings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 1:\\nLong Papers) , pages 889–898, Melbourne, Australia, July 2018. Association for Computational\\nLinguistics. doi: 10.18653/v1/P18-1082. URL https://www.aclweb.org/anthology/\\nP18-1082 .\\n[12] Angela Fan, Yacine Jernite, Ethan Perez, David Grangier, Jason Weston, and Michael Auli. ELI5:\\nLong form question answering. In Proceedings of the 57th Annual Meeting of the Association\\nfor Computational Linguistics , pages 3558–3567, Florence, Italy, July 2019. Association for\\nComputational Linguistics. doi: 10.18653/v1/P19-1346. URL https://www.aclweb.org/\\nanthology/P19-1346 .\\n[13] Angela Fan, Claire Gardent, Chloe Braud, and Antoine Bordes. Augmenting transformers\\nwith KNN-based composite memory, 2020. URL https://openreview.net/forum?id=\\nH1gx1CNKPH .\\n[14] Thibault Févry, Livio Baldini Soares, Nicholas FitzGerald, Eunsol Choi, and Tom Kwiatkowski.\\nEntities as experts: Sparse memory access with entity supervision. ArXiv , abs/2004.07202,\\n2020. URL https://arxiv.org/abs/2004.07202 .\\n[15] Marjan Ghazvininejad, Chris Brockett, Ming-Wei Chang, Bill Dolan, Jianfeng Gao, Wen\\ntau Yih, and Michel Galley. A knowledge-grounded neural conversation model. In AAAI\\nConference on Artiﬁcial Intelligence , 2018. URL https://www.aaai.org/ocs/index.php/\\nAAAI/AAAI18/paper/view/16710 .\\n[16] Katja Grace, John Salvatier, Allan Dafoe, Baobao Zhang, and Owain Evans. When will AI\\nexceed human performance? evidence from AI experts. CoRR , abs/1705.08807, 2017. URL\\nhttp://arxiv.org/abs/1705.08807 .\\n[17] Jiatao Gu, Yong Wang, Kyunghyun Cho, and Victor O.K. Li. Search engine guided neural\\nmachine translation. In AAAI Conference on Artiﬁcial Intelligence , 2018. URL https:\\n//www.aaai.org/ocs/index.php/AAAI/AAAI18/paper/view/17282 .\\n[18] Jiatao Gu, Yong Wang, Kyunghyun Cho, and Victor O.K. Li. Search engine guided neural\\nmachine translation. In 32nd AAAI Conference on Artiﬁcial Intelligence, AAAI 2018 , 32nd\\nAAAI Conference on Artiﬁcial Intelligence, AAAI 2018, pages 5133–5140. AAAI press, 2018.\\n32nd AAAI Conference on Artiﬁcial Intelligence, AAAI 2018 ; Conference date: 02-02-2018\\nThrough 07-02-2018.\\n[19] Kelvin Guu, Tatsunori B. Hashimoto, Yonatan Oren, and Percy Liang. Generating sentences by\\nediting prototypes. Transactions of the Association for Computational Linguistics , 6:437–450,\\n2018. doi: 10.1162/tacl_a_00030. URL https://www.aclweb.org/anthology/Q18-1031 .\\n11', metadata={'source': '/content/RAG.pdf', 'page': 10}),\n", " Document(page_content='[20] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. REALM:\\nRetrieval-augmented language model pre-training. ArXiv , abs/2002.08909, 2020. URL https:\\n//arxiv.org/abs/2002.08909 .\\n[21] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A\\nretrieve-and-edit framework for predicting structured outputs. In S. <PERSON>,\\nH. <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, ed-\\nitors, Advances in Neural Information Processing Systems 31 , pages 10052–\\n10062. Curran Associates, Inc., 2018. URL http://papers.nips.cc/paper/\\n8209-a-retrieve-and-edit-framework-for-predicting-structured-outputs.\\npdf.\\n[22] <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Simple and effective retrieve-\\nedit-rerank text generation. In Proceedings of the 58th Annual Meeting of the Association for\\nComputational Linguistics , pages 2532–2538, Online, July 2020. Association for Computa-\\ntional Linguistics. doi: 10.18653/v1/2020.acl-main.228. URL https://www.aclweb.org/\\nanthology/2020.acl-main.228 .\\n[23] Jeff Johnson, Matthijs Douze, and Hervé Jégou. Billion-scale similarity search with gpus. arXiv\\npreprint arXiv:1702.08734 , 2017. URL https://arxiv.org/abs/1702.08734 .\\n[24] Mandar Joshi, Eunsol Choi, Daniel Weld, and Luke Zettlemoyer. TriviaQA: A Large Scale\\nDistantly Supervised Challenge Dataset for Reading Comprehension. In Proceedings of the\\n55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers) ,\\npages 1601–1611, Vancouver, Canada, July 2017. Association for Computational Linguistics.\\ndoi: 10.18653/v1/P17-1147. URL https://www.aclweb.org/anthology/P17-1147 .\\n[25] Armand Joulin and Tomas Mikolov. Inferring algorithmic patterns with stack-\\naugmented recurrent nets. In Proceedings of the 28th International Conference on\\nNeural Information Processing Systems - Volume 1 , NIPS’15, page 190–198, Cam-\\nbridge, MA, USA, 2015. MIT Press. URL https://papers.nips.cc/paper/\\n5857-inferring-algorithmic-patterns-with-stack-augmented-recurrent-nets .\\n[26] Vladimir Karpukhin, Barlas Oguz, Sewon Min, Ledell Wu, Sergey Edunov, Danqi Chen, and\\nWen-tau Yih. Dense passage retrieval for open-domain question answering. arXiv preprint\\narXiv:2004.04906 , 2020. URL https://arxiv.org/abs/2004.04906 .\\n[27] Urvashi Khandelwal, Omer Levy, Dan Jurafsky, Luke Zettlemoyer, and Mike Lewis. Generaliza-\\ntion through memorization: Nearest neighbor language models. In International Conference on\\nLearning Representations , 2020. URL https://openreview.net/forum?id=HklBjCEKvH .\\n[28] Diederik P. Kingma and Jimmy Ba. Adam: A method for stochastic optimization. In Yoshua\\nBengio and Yann LeCun, editors, 3rd International Conference on Learning Representations,\\nICLR 2015, San Diego, CA, USA, May 7-9, 2015, Conference Track Proceedings , 2015. URL\\nhttp://arxiv.org/abs/1412.6980 .\\n[29] Tom Kwiatkowski, Jennimaria Palomaki, Olivia Redﬁeld, Michael Collins, Ankur Parikh,\\nChris Alberti, Danielle Epstein, Illia Polosukhin, Matthew Kelcey, Jacob Devlin, Ken-\\nton Lee, Kristina N. Toutanova, Llion Jones, Ming-Wei Chang, Andrew Dai, Jakob\\nUszkoreit, Quoc Le, and Slav Petrov. Natural Questions: a Benchmark for Ques-\\ntion Answering Research. Transactions of the Association of Computational Lin-\\nguistics , 2019. URL https://tomkwiat.users.x20web.corp.google.com/papers/\\nnatural-questions/main-1455-kwiatkowski.pdf .\\n[30] Guillaume Lample, Alexandre Sablayrolles, Marc’ Aurelio Ranzato, Ludovic Denoyer, and\\nHerve Jegou. Large memory layers with product keys. In H. Wallach, H. Larochelle,\\nA. Beygelzimer, F. d’ Alché-Buc, E. Fox, and R. Garnett, editors, Advances in Neural In-\\nformation Processing Systems 32 , pages 8548–8559. Curran Associates, Inc., 2019. URL http:\\n//papers.nips.cc/paper/9061-large-memory-layers-with-product-keys.pdf .\\n[31] Kenton Lee, Ming-Wei Chang, and Kristina Toutanova. Latent retrieval for weakly supervised\\nopen domain question answering. In Proceedings of the 57th Annual Meeting of the Association\\n12', metadata={'source': '/content/RAG.pdf', 'page': 11}),\n", " Document(page_content='for Computational Linguistics , pages 6086–6096, Florence, Italy, July 2019. Association for\\nComputational Linguistics. doi: 10.18653/v1/P19-1612. URL https://www.aclweb.org/\\nanthology/P19-1612 .\\n[32] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,\\<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. BART: Denoising sequence-to-sequence\\npre-training for natural language generation, translation, and comprehension. arXiv preprint\\narXiv:1910.13461 , 2019. URL https://arxiv.org/abs/1910.13461 .\\n[33] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A diversity-promoting\\nobjective function for neural conversation models. In Proceedings of the 2016 Conference of the\\nNorth American Chapter of the Association for Computational Linguistics: Human Language\\nTechnologies , pages 110–119, San Diego, California, June 2016. Association for Computational\\nLinguistics. doi: 10.18653/v1/N16-1014. URL https://www.aclweb.org/anthology/\\nN16-1014 .\\n[34] <PERSON>, <PERSON>, and <PERSON>. Acute-eval: Improved dialogue evaluation\\nwith optimized questions and multi-turn comparisons. ArXiv , abs/1909.03087, 2019. URL\\nhttps://arxiv.org/abs/1909.03087 .\\n[35] Hairong Liu, Mingbo Ma, Liang Huang, Hao Xiong, and Zhongjun He. Robust neural machine\\ntranslation with joint textual and phonetic embedding. In Proceedings of the 57th Annual\\nMeeting of the Association for Computational Linguistics , pages 3044–3049, Florence, Italy,\\nJuly 2019. Association for Computational Linguistics. doi: 10.18653/v1/P19-1291. URL\\nhttps://www.aclweb.org/anthology/P19-1291 .\\n[36] Peter J. Liu*, Mohammad Saleh*, Etienne Pot, Ben Goodrich, Ryan Sepassi, Lukasz Kaiser,\\nand Noam Shazeer. Generating wikipedia by summarizing long sequences. In International\\nConference on Learning Representations , 2018. URL https://openreview.net/forum?\\nid=Hyg0vbWC- .\\n[37] Yury A. Malkov and D. A. Yashunin. Efﬁcient and robust approximate nearest neighbor search\\nusing hierarchical navigable small world graphs. IEEE Transactions on Pattern Analysis and\\nMachine Intelligence , 42:824–836, 2016. URL https://arxiv.org/abs/1603.09320 .\\n[38] Gary Marcus. The next decade in ai: four steps towards robust artiﬁcial intelligence. arXiv\\npreprint arXiv:2002.06177 , 2020. URL https://arxiv.org/abs/2002.06177 .\\n[39] Luca Massarelli, Fabio Petroni, Aleksandra Piktus, Myle Ott, Tim Rocktäschel, Vassilis\\nPlachouras, Fabrizio Silvestri, and Sebastian Riedel. How decoding strategies affect the\\nveriﬁability of generated text. arXiv preprint arXiv:1911.03587 , 2019. URL https:\\n//arxiv.org/abs/1911.03587 .\\n[40] Paulius Micikevicius, Sharan Narang, Jonah Alben, Gregory Diamos, Erich Elsen, David Garcia,\\nBoris Ginsburg, Michael Houston, Oleksii Kuchaiev, Ganesh Venkatesh, and Hao Wu. Mixed\\nprecision training. In ICLR , 2018. URL https://openreview.net/forum?id=r1gs9JgRZ .\\n[41] Nikita Moghe, Siddhartha Arora, Suman Banerjee, and Mitesh M. Khapra. Towards exploit-\\ning background knowledge for building conversation systems. In Proceedings of the 2018\\nConference on Empirical Methods in Natural Language Processing , pages 2322–2332, Brus-\\nsels, Belgium, October-November 2018. Association for Computational Linguistics. doi:\\n10.18653/v1/D18-1255. URL https://www.aclweb.org/anthology/D18-1255 .\\n[42] Preksha Nema and Mitesh M. Khapra. Towards a better metric for evaluating question generation\\nsystems. In Proceedings of the 2018 Conference on Empirical Methods in Natural Language\\nProcessing , pages 3950–3959, Brussels, Belgium, October-November 2018. Association for\\nComputational Linguistics. doi: 10.18653/v1/D18-1429. URL https://www.aclweb.org/\\nanthology/D18-1429 .\\n[43] Tri Nguyen, Mir Rosenberg, Xia Song, Jianfeng Gao, Saurabh Tiwary, Rangan Majumder,\\nand Li Deng. MS MARCO: A human generated machine reading comprehension dataset. In\\nTarek Richard Besold, Antoine Bordes, Artur S. d’Avila Garcez, and Greg Wayne, editors,\\nProceedings of the Workshop on Cognitive Computation: Integrating neural and symbolic\\n13', metadata={'source': '/content/RAG.pdf', 'page': 12}),\n", " Document(page_content='approaches 2016 co-located with the 30th Annual Conference on Neural Information Processing\\nSystems (NIPS 2016), Barcelona, Spain, December 9, 2016 , volume 1773 of CEUR Workshop\\nProceedings . CEUR-WS.org, 2016. URL http://ceur-ws.org/Vol-1773/CoCoNIPS_\\n2016_paper9.pdf .\\n[44] <PERSON> and <PERSON><PERSON><PERSON><PERSON>. <PERSON> re-ranking with BERT. arXiv preprint\\narXiv:1901.04085 , 2019. URL https://arxiv.org/abs/1901.04085 .\\n[45] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,\\nand <PERSON>. fairseq: A fast, extensible toolkit for sequence modeling. In Proceedings\\nof the 2019 Conference of the North American Chapter of the Association for Computational\\nLinguistics (Demonstrations) , pages 48–53, Minneapolis, Minnesota, June 2019. Association\\nfor Computational Linguistics. doi: 10.18653/v1/N19-4009. URL https://www.aclweb.\\norg/anthology/N19-4009 .\\n[46] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>\\<PERSON><PERSON><PERSON>. Finding generalizable evidence by learning to convince <PERSON>&a models. In Proceedings\\nof the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th\\nInternational Joint Conference on Natural Language Processing (EMNLP-IJCNLP) , pages\\n2402–2411, Hong Kong, China, November 2019. Association for Computational Linguistics.\\ndoi: 10.18653/v1/D19-1244. URL https://www.aclweb.org/anthology/D19-1244 .\\n[47] Fabio Petroni, Tim Rocktäschel, Sebastian Riedel, Patrick Lewis, Anton Bakhtin, Yuxiang Wu,\\nand Alexander Miller. Language models as knowledge bases? In Proceedings of the 2019\\nConference on Empirical Methods in Natural Language Processing and the 9th International\\nJoint Conference on Natural Language Processing (EMNLP-IJCNLP) , pages 2463–2473, Hong\\nKong, China, November 2019. Association for Computational Linguistics. doi: 10.18653/v1/\\nD19-1250. URL https://www.aclweb.org/anthology/D19-1250 .\\n[48] Fabio Petroni, Patrick Lewis, Aleksandra Piktus, Tim Rocktäschel, Yuxiang Wu, Alexander H.\\nMiller, and Sebastian Riedel. How context affects language models’ factual predictions. In\\nAutomated Knowledge Base Construction , 2020. URL https://openreview.net/forum?\\nid=025X0zPfn .\\n[49] Alec Radford, Karthik Narasimhan, Tim Salimans, and Ilya Sutskever. Im-\\nproving Language Understanding by Generative Pre-Training, 2018. URL\\nhttps://s3-us-west-2.amazonaws.com/openai-assets/research-covers/\\nlanguage-unsupervised/language_understanding_paper.pdf .\\n[50] Alec Radford, Jeff Wu, Rewon Child, David Luan, Dario Amodei, and Ilya\\nSutskever. Language models are unsupervised multitask learners, 2019. URL\\nhttps://d4mucfpksywv.cloudfront.net/better-language-models/language_\\nmodels_are_unsupervised_multitask_learners.pdf .\\n[51] Colin Raffel, Noam Shazeer, Adam Roberts, Katherine Lee, Sharan Narang, Michael Matena,\\nYanqi Zhou, Wei Li, and Peter J. Liu. Exploring the limits of transfer learning with a uniﬁed\\ntext-to-text transformer. arXiv e-prints , 2019. URL https://arxiv.org/abs/1910.10683 .\\n[52] Adam Roberts, Colin Raffel, and Noam Shazeer. How much knowledge can you pack into\\nthe parameters of a language model? arXiv e-prints , 2020. URL https://arxiv.org/abs/\\n2002.08910 .\\n[53] Stephen Robertson and Hugo Zaragoza. The probabilistic relevance framework: Bm25 and\\nbeyond. Found. Trends Inf. Retr. , 3(4):333–389, April 2009. ISSN 1554-0669. doi: 10.1561/\\n1500000019. URL https://doi.org/10.1561/1500000019 .\\n[54] Irene Solaiman, Miles Brundage, Jack Clark, Amanda Askell, Ariel Herbert-V oss, Jeff Wu, Alec\\nRadford, and Jian-Bing Wang. Release strategies and the social impacts of language models.\\nArXiv , abs/1908.09203, 2019.\\n[55] Sainbayar Sukhbaatar, Arthur Szlam, Jason Weston, and Rob Fergus. End-to-end memory net-\\nworks. In C. Cortes, N. D. Lawrence, D. D. Lee, M. Sugiyama, and R. Garnett, editors, Advances\\nin Neural Information Processing Systems 28 , pages 2440–2448. Curran Associates, Inc., 2015.\\nURL http://papers.nips.cc/paper/5846-end-to-end-memory-networks.pdf .\\n14', metadata={'source': '/content/RAG.pdf', 'page': 13}),\n", " Document(page_content='[56] <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. FEVER: a\\nlarge-scale dataset for fact extraction and VERiﬁcation. In Proceedings of the 2018 Conference\\nof the North American Chapter of the Association for Computational Linguistics: Human\\nLanguage Technologies, Volume 1 (Long Papers) , pages 809–819, New Orleans, Louisiana,\\nJune 2018. Association for Computational Linguistics. doi: 10.18653/v1/N18-1074. URL\\nhttps://www.aclweb.org/anthology/N18-1074 .\\n[57] <PERSON> and <PERSON>. Avoiding catastrophic forgetting in mitigating model\\nbiases in sentence-pair classiﬁcation with elastic weight consolidation. ArXiv , abs/2004.14366,\\n2020. URL https://arxiv.org/abs/2004.14366 .\\n[58] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>,\\nŁ uk<PERSON><PERSON>, and <PERSON><PERSON>. Attention is all you need. In <PERSON><PERSON>, U. V . <PERSON>,\\n<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural\\nInformation Processing Systems 30 , pages 5998–6008. Curran Associates, Inc., 2017. URL\\nhttp://papers.nips.cc/paper/7181-attention-is-all-you-need.pdf .\\n[59] Ashwin Vijayakumar, Michael Cogswell, Ramprasaath Selvaraju, Qing Sun, Stefan <PERSON>, <PERSON>\\nCrandall, and Dhruv Batra. Diverse beam search for improved description of complex scenes.\\nAAAI Conference on Artiﬁcial Intelligence , 2018. URL https://www.aaai.org/ocs/index.\\nphp/AAAI/AAAI18/paper/view/17329 .\\n[60] Alex Wang, Amanpreet Singh, Julian Michael, Felix Hill, Omer Levy, and Samuel Bowman.\\nGLUE: A multi-task benchmark and analysis platform for natural language understanding.\\nInProceedings of the 2018 EMNLP Workshop BlackboxNLP: Analyzing and Interpreting\\nNeural Networks for NLP , pages 353–355, Brussels, Belgium, November 2018. Association for\\nComputational Linguistics. doi: 10.18653/v1/W18-5446. URL https://www.aclweb.org/\\nanthology/W18-5446 .\\n[61] Alex Wang, Yada Pruksachatkun, Nikita Nangia, Amanpreet Singh, Julian Michael, Felix\\nHill, Omer Levy, and Samuel Bowman. SuperGLUE: A Stickier Benchmark for General-\\nPurpose Language Understanding Systems. In H. Wallach, H. Larochelle, A. Beygelzimer,\\nF. d\\\\textquotesingle Alché-Buc, E. Fox, and R. Garnett, editors, Advances in Neural Information\\nProcessing Systems 32 , pages 3261–3275. Curran Associates, Inc., 2019. URL https://\\narxiv.org/abs/1905.00537 .\\n[62] Shuohang Wang, Mo Yu, Xiaoxiao Guo, Zhiguo Wang, Tim Klinger, Wei Zhang, Shiyu Chang,\\nGerry Tesauro, Bowen Zhou, and Jing Jiang. R3: Reinforced ranker-reader for open-domain\\nquestion answering. In Sheila A. McIlraith and Kilian Q. Weinberger, editors, Proceedings of\\nthe Thirty-Second AAAI Conference on Artiﬁcial Intelligence, (AAAI-18), the 30th innovative\\nApplications of Artiﬁcial Intelligence (IAAI-18), and the 8th AAAI Symposium on Educational\\nAdvances in Artiﬁcial Intelligence (EAAI-18), New Orleans, Louisiana, USA, February 2-7,\\n2018 , pages 5981–5988. AAAI Press, 2018. URL https://www.aaai.org/ocs/index.\\nphp/AAAI/AAAI18/paper/view/16712 .\\n[63] Shuohang Wang, Mo Yu, Jing Jiang, Wei Zhang, Xiaoxiao Guo, Shiyu Chang, Zhiguo Wang,\\nTim Klinger, Gerald Tesauro, and Murray Campbell. Evidence aggregation for answer re-\\nranking in open-domain question answering. In ICLR , 2018. URL https://openreview.\\nnet/forum?id=rJl3yM-Ab .\\n[64] Jason Weston, Sumit Chopra, and Antoine Bordes. Memory networks. In Yoshua Bengio\\nand Yann LeCun, editors, 3rd International Conference on Learning Representations, ICLR\\n2015, San Diego, CA, USA, May 7-9, 2015, Conference Track Proceedings , 2015. URL\\nhttp://arxiv.org/abs/1410.3916 .\\n[65] Jason Weston, Emily Dinan, and Alexander Miller. Retrieve and reﬁne: Improved sequence\\ngeneration models for dialogue. In Proceedings of the 2018 EMNLP Workshop SCAI: The 2nd\\nInternational Workshop on Search-Oriented Conversational AI , pages 87–92, Brussels, Belgium,\\nOctober 2018. Association for Computational Linguistics. doi: 10.18653/v1/W18-5713. URL\\nhttps://www.aclweb.org/anthology/W18-5713 .\\n15', metadata={'source': '/content/RAG.pdf', 'page': 14}),\n", " Document(page_content='[66] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>,\\<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>face’s transformers:\\nState-of-the-art natural language processing. ArXiv , abs/1910.03771, 2019.\\n[67] <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Addressing semantic drift in question generation for semi-\\nsupervised question answering. In Proceedings of the 2019 Conference on Empirical Meth-\\nods in Natural Language Processing and the 9th International Joint Conference on Natural\\nLanguage Processing (EMNLP-IJCNLP) , pages 2495–2509, Hong Kong, China, Novem-\\nber 2019. Association for Computational Linguistics. doi: 10.18653/v1/D19-1253. URL\\nhttps://www.aclweb.org/anthology/D19-1253 .\\n[68] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and\\<PERSON><PERSON><PERSON>. Reasoning over semantic-level graph for fact checking. ArXiv , abs/1909.03745, 2019.\\nURL https://arxiv.org/abs/1909.03745 .\\n16', metadata={'source': '/content/RAG.pdf', 'page': 15}),\n", " Document(page_content='Appendices for Retrieval-Augmented Generation for\\nKnowledge-Intensive NLP Tasks\\nA Implementation Details\\nFor Open-domain QA we report test numbers using 15 retrieved documents for RAG-Token models.\\nFor RAG-Sequence models, we report test results using 50 retrieved documents, and we use the\\nThorough Decoding approach since answers are generally short. We use greedy decoding for QA as\\nwe did not ﬁnd beam search improved results. For Open-MSMarco and Jeopardy question generation,\\nwe report test numbers using ten retrieved documents for both RAG-Token and RAG-Sequence,\\nand we also train a BART-large model as a baseline. We use a beam size of four, and use the Fast\\nDecoding approach for RAG-Sequence models, as Thorough Decoding did not improve performance.\\nB Human Evaluation\\nFigure 4: Annotation interface for human evaluation of factuality. A pop-out for detailed instructions\\nand a worked example appear when clicking \"view tool guide\".\\nFigure 4 shows the user interface for human evaluation. To avoid any biases for screen position,\\nwhich model corresponded to sentence A and sentence B was randomly selected for each example.\\nAnnotators were encouraged to research the topic using the internet, and were given detailed instruc-\\ntions and worked examples in a full instructions tab. We included some gold sentences in order to\\nassess the accuracy of the annotators. Two annotators did not perform well on these examples and\\ntheir annotations were removed from the results.\\nC Training setup Details\\nWe train all RAG models and BART baselines using Fairseq [ 45].2We train with mixed precision\\nﬂoating point arithmetic [ 40], distributing training across 8, 32GB NVIDIA V100 GPUs, though\\ntraining and inference can be run on one GPU. We ﬁnd that doing Maximum Inner Product Search\\nwith FAISS is sufﬁciently fast on CPU, so we store document index vectors on CPU, requiring ∼100\\nGB of CPU memory for all of Wikipedia. After submission, We have ported our code to HuggingFace\\nTransformers [ 66]3, which achieves equivalent performance to the previous version but is a cleaner\\nand easier to use implementation. This version is also open-sourced. We also compress the document\\nindex using FAISS’s compression tools, reducing the CPU memory requirement to 36GB. Scripts to\\nrun experiments with RAG can be found at https://github.com/huggingface/transformers/\\nblob/master/examples/rag/README.md and an interactive demo of a RAG model can be found\\nathttps://huggingface.co/rag/\\n2https://github.com/pytorch/fairseq\\n3https://github.com/huggingface/transformers\\n17View full instructions\\nWhich sentence is more factually true?\\nView tool guide\\nSelect an option\\nSubject\\n: Hemingway\\nNote: Some questions are\\nSentence A is more\\ntrue\\ncontrol questions. We require\\nSentence A: \"The Sun Also Rises\" is a novel by this author of \"A\\ngood accuracy on our control\\nFarewell to Arms\"\\nSentence B is more\\nquestions to accept\\ntrue\\nresponses.\\nSentence B : This author of \"The Sun Also Rises\"was born in\\nBoth sentences are\\nHavana, Cuba, the son of Spanish immigrants\\ntrue\\nIndicatewhichoneof the\\nfollowing sentences is more\\nBoth sentences are\\ncompletely untrue\\nfactually true with respect to\\nthe subject. Using the\\ninternet to check whether\\nthe sentences are true is\\nencouraged.', metadata={'source': '/content/RAG.pdf', 'page': 16}),\n", " Document(page_content='D Further Details on Open-Domain QA\\nFor open-domain QA, multiple answer annotations are often available for a given question. These\\nanswer annotations are exploited by extractive models during training as typically all the answer\\nannotations are used to ﬁnd matches within documents when preparing training data. For RAG, we\\nalso make use of multiple annotation examples for Natural Questions and WebQuestions by training\\nthe model with each (q,a)pair separately, leading to a small increase in accuracy. For TriviaQA,\\nthere are often many valid answers to a given question, some of which are not suitable training targets,\\nsuch as emoji or spelling variants. For TriviaQA, we ﬁlter out answer candidates if they do not occur\\nin top 1000 documents for the query.\\nCuratedTrec preprocessing The answers for CuratedTrec are given in the form of regular expres-\\nsions, which has been suggested as a reason why it is unsuitable for answer-generation models [20].\\nTo overcome this, we use a pre-processing step where we ﬁrst retrieve the top 1000 documents for\\neach query, and use the answer that most frequently matches the regex pattern as the supervision\\ntarget. If no matches are found, we resort to a simple heuristic: generate all possible permutations for\\neach regex, replacing non-deterministic symbols in the regex nested tree structure with a whitespace.\\nTriviaQA Evaluation setups The open-domain QA community customarily uses public develop-\\nment datasets as test datasets, as test data for QA datasets is often restricted and dedicated to reading\\ncompehension purposes. We report our results using the datasets splits used in DPR [ 26], which are\\nconsistent with common practice in Open-domain QA. For TriviaQA, this test dataset is the public\\nTriviaQA Web Development split. Roberts et al. [52] used the TriviaQA ofﬁcial Wikipedia test set\\ninstead. Févry et al. [14] follow this convention in order to compare with Roberts et al. [52] (See\\nappendix of [ 14]). We report results on both test sets to enable fair comparison to both approaches.\\nWe ﬁnd that our performance is much higher using the ofﬁcial Wiki test set, rather than the more\\nconventional open-domain test set, which we attribute to the ofﬁcial Wiki test set questions being\\nsimpler to answer from Wikipedia.\\nE Further Details on FEVER\\nFor FEVER classiﬁcation, we follow the practice from [ 32], and ﬁrst re-generate the claim, and\\nthen classify using the representation of the ﬁnal hidden state, before ﬁnally marginalizing across\\ndocuments to obtain the class probabilities. The FEVER task traditionally has two sub-tasks. The\\nﬁrst is to classify the claim as either \"Supported\", \"Refuted\" or \"Not Enough Info\", which is the task\\nwe explore in the main paper. FEVER’s other sub-task involves extracting sentences from Wikipedia\\nas evidence supporting the classiﬁcation prediction. As FEVER uses a different Wikipedia dump to\\nus, directly tackling this task is not straightforward. We hope to address this in future work.\\nF Null Document Probabilities\\nWe experimented with adding \"Null document\" mechanism to RAG, similar to REALM [ 20] in order\\nto model cases where no useful information could be retrieved for a given input. Here, if kdocuments\\nwere retrieved, we would additionally \"retrieve\" an empty document and predict a logit for the null\\ndocument, before marginalizing over k+ 1predictions. We explored modelling this null document\\nlogit by learning (i) a document embedding for the null document, (ii) a static learnt bias term, or\\n(iii) a neural network to predict the logit. We did not ﬁnd that these improved performance, so in\\nthe interests of simplicity, we omit them. For Open MS-MARCO, where useful retrieved documents\\ncannot always be retrieved, we observe that the model learns to always retrieve a particular set of\\ndocuments for questions that are less likely to beneﬁt from retrieval, suggesting that null document\\nmechanisms may not be necessary for RAG.\\nG Parameters\\nOur RAG models contain the trainable parameters for the BERT-base query and document encoder of\\nDPR, with 110M parameters each (although we do not train the document encoder ourselves) and\\n406M trainable parameters from BART-large, 406M parameters, making a total of 626M trainable\\n18', metadata={'source': '/content/RAG.pdf', 'page': 17}),\n", " Document(page_content='Table 7: Number of instances in the datasets used. *A hidden subset of this data is used for evaluation\\nTask Train Development Test\\nNatural Questions 79169 8758 3611\\nTriviaQA 78786 8838 11314\\nWebQuestions 3418 362 2033\\nCuratedTrec 635 134 635\\nJeopardy Question Generation 97392 13714 26849\\nMS-MARCO 153726 12468 101093*\\nFEVER-3-way 145450 10000 10000\\nFEVER-2-way 96966 6666 6666\\nparameters. The best performing \"closed-book\" (parametric only) open-domain QA model is T5-11B\\nwith 11 Billion trainable parameters. The T5 model with the closest number of parameters to our\\nmodels is T5-large (770M parameters), which achieves a score of 28.9 EM on Natural Questions [ 52],\\nsubstantially below the 44.5 that RAG-Sequence achieves, indicating that hybrid parametric/non-\\nparametric models require far fewer trainable parameters for strong open-domain QA performance.\\nThe non-parametric memory index does not consist of trainable parameters, but does consists of 21M\\n728 dimensional vectors, consisting of 15.3B values. These can be easily be stored at 8-bit ﬂoating\\npoint precision to manage memory and disk footprints.\\nH Retrieval Collapse\\nIn preliminary experiments, we observed that for some tasks such as story generation [ 11], the\\nretrieval component would “collapse” and learn to retrieve the same documents regardless of the\\ninput. In these cases, once retrieval had collapsed, the generator would learn to ignore the documents,\\nand the RAG model would perform equivalently to BART. The collapse could be due to a less-explicit\\nrequirement for factual knowledge in some tasks, or the longer target sequences, which could result\\nin less informative gradients for the retriever. Perez et al. [46] also found spurious retrieval results\\nwhen optimizing a retrieval component in order to improve performance on downstream tasks.\\nI Number of instances per dataset\\nThe number of training, development and test datapoints in each of our datasets is shown in Table 7.\\n19', metadata={'source': '/content/RAG.pdf', 'page': 18})]"]}, "metadata": {}, "execution_count": 4}]}, {"cell_type": "code", "source": ["# Split text into chunks\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=20)\n", "docs = text_splitter.split_documents(pages)"], "metadata": {"id": "o3ynVE9IqLry"}, "execution_count": 9, "outputs": []}, {"cell_type": "code", "source": ["docs"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "eW1VQ-TrscRo", "outputId": "72f6ce94-1f60-440f-cb08-139a05ec6f51"}, "execution_count": 10, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='Retrieval-Augmented Generation for\\nKnowledge-Intensive NLP Tasks\\n<PERSON><PERSON><PERSON>†‡, <PERSON>,\\<PERSON><PERSON><PERSON><PERSON><PERSON>†, <PERSON><PERSON><PERSON>†, <PERSON>†, <PERSON><PERSON>†, <PERSON>†,\\<PERSON><PERSON><PERSON>†, <PERSON><PERSON><PERSON><PERSON>†, <PERSON>†‡, <PERSON>†‡, <PERSON><PERSON><PERSON>†\\n†Facebook AI Research;‡University College London;⋆New York University;\\<EMAIL>\\nAbstract\\nLarge pre-trained language models have been shown to store factual knowledge\\nin their parameters, and achieve state-of-the-art results when ﬁne-tuned on down-\\nstream NLP tasks. However, their ability to access and precisely manipulate knowl-\\nedge is still limited, and hence on knowledge-intensive tasks, their performance\\nlags behind task-speciﬁc architectures. Additionally, providing provenance for their\\ndecisions and updating their world knowledge remain open research problems. Pre-\\ntrained models with a differentiable access mechanism to explicit non-parametric', metadata={'source': '/content/RAG.pdf', 'page': 0}),\n", " Document(page_content='memory have so far been only investigated for extractive downstream tasks. We\\nexplore a general-purpose ﬁne-tuning recipe for retrieval-augmented generation\\n(RAG) — models which combine pre-trained parametric and non-parametric mem-\\nory for language generation. We introduce RAG models where the parametric\\nmemory is a pre-trained seq2seq model and the non-parametric memory is a dense\\nvector index of Wikipedia, accessed with a pre-trained neural retriever. We com-\\npare two RAG formulations, one which conditions on the same retrieved passages\\nacross the whole generated sequence, and another which can use different passages\\nper token. We ﬁne-tune and evaluate our models on a wide range of knowledge-\\nintensive NLP tasks and set the state of the art on three open domain QA tasks,\\noutperforming parametric seq2seq models and task-speciﬁc retrieve-and-extract\\narchitectures. For language generation tasks, we ﬁnd that RAG models generate', metadata={'source': '/content/RAG.pdf', 'page': 0}),\n", " Document(page_content='more speciﬁc, diverse and factual language than a state-of-the-art parametric-only\\nseq2seq baseline.\\n1 Introduction\\nPre-trained neural language models have been shown to learn a substantial amount of in-depth knowl-\\nedge from data [ 47]. They can do so without any access to an external memory, as a parameterized\\nimplicit knowledge base [ 51,52]. While this development is exciting, such models do have down-\\nsides: They cannot easily expand or revise their memory, can’t straightforwardly provide insight into\\ntheir predictions, and may produce “hallucinations” [ 38]. Hybrid models that combine parametric\\nmemory with non-parametric (i.e., retrieval-based) memories [ 20,26,48] can address some of these\\nissues because knowledge can be directly revised and expanded, and accessed knowledge can be\\ninspected and interpreted. REALM [ 20] and ORQA [ 31], two recently introduced models that', metadata={'source': '/content/RAG.pdf', 'page': 0}),\n", " Document(page_content='combine masked language models [ 8] with a differentiable retriever, have shown promising results,arXiv:2005.11401v4  [cs.CL]  12 Apr 2021', metadata={'source': '/content/RAG.pdf', 'page': 0}),\n", " Document(page_content='The\\tDivine\\nComedy\\t(x) qQuery\\nEncoder\\nq(x)\\nMIPS p θGenerator \\xa0pθ\\n(Parametric)\\nMargin-\\nalize\\nThis\\t14th\\tcentury\\twork\\nis\\tdivided\\tinto\\t3\\nsections:\\t\"Inferno\",\\n\"Purgatorio\"\\t&\\n\"Paradiso\"\\t\\t\\t\\t\\t\\t\\t\\t\\t (y)End-to-End Backprop through q and\\xa0 p θ\\nBarack\\tObama\\twas\\nborn\\tin\\tHawaii. (x)\\nFact V eriﬁcation: Fact Querysupports \\t(y)\\nQuestion GenerationFact V eriﬁcation:\\nLabel GenerationDocument\\nIndexDefine\\t\"middle\\tear\" (x)\\nQuestion Answering:\\nQuestion QueryThe\\tmiddle\\tear\\tincludes\\nthe\\ttympanic\\tcavity\\tand\\nthe\\tthree\\tossicles.\\t\\t (y)\\nQuestion Answering:\\nAnswer GenerationRetriever pη\\n(Non-Parametric)\\nz 4\\nz3\\nz2\\nz 1d(z)\\nJeopardy Question\\nGeneration:\\nAnswer QueryFigure 1: Overview of our approach. We combine a pre-trained retriever ( Query Encoder +Document\\nIndex ) with a pre-trained seq2seq model ( Generator ) and ﬁne-tune end-to-end. For query x, we use\\nMaximum Inner Product Search (MIPS) to ﬁnd the top-K documents zi. For ﬁnal prediction y, we', metadata={'source': '/content/RAG.pdf', 'page': 1}),\n", " Document(page_content='treatzas a latent variable and marginalize over seq2seq predictions given different documents.\\nbut have only explored open-domain extractive question answering. Here, we bring hybrid parametric\\nand non-parametric memory to the “workhorse of NLP,” i.e. sequence-to-sequence (seq2seq) models.\\nWe endow pre-trained, parametric-memory generation models with a non-parametric memory through\\na general-purpose ﬁne-tuning approach which we refer to as retrieval-augmented generation (RAG).\\nWe build RAG models where the parametric memory is a pre-trained seq2seq transformer, and the\\nnon-parametric memory is a dense vector index of Wikipedia, accessed with a pre-trained neural\\nretriever. We combine these components in a probabilistic model trained end-to-end (Fig. 1). The\\nretriever (Dense Passage Retriever [ 26], henceforth DPR) provides latent documents conditioned on\\nthe input, and the seq2seq model (BART [ 32]) then conditions on these latent documents together with', metadata={'source': '/content/RAG.pdf', 'page': 1}),\n", " Document(page_content='the input to generate the output. We marginalize the latent documents with a top-K approximation,\\neither on a per-output basis (assuming the same document is responsible for all tokens) or a per-token\\nbasis (where different documents are responsible for different tokens). Like T5 [ 51] or BART, RAG\\ncan be ﬁne-tuned on any seq2seq task, whereby both the generator and retriever are jointly learned.\\nThere has been extensive previous work proposing architectures to enrich systems with non-parametric\\nmemory which are trained from scratch for speciﬁc tasks, e.g. memory networks [ 64,55], stack-\\naugmented networks [ 25] and memory layers [ 30]. In contrast, we explore a setting where both\\nparametric and non-parametric memory components are pre-trained and pre-loaded with extensive\\nknowledge. Crucially, by using pre-trained access mechanisms, the ability to access knowledge is\\npresent without additional training.', metadata={'source': '/content/RAG.pdf', 'page': 1}),\n", " Document(page_content='Our results highlight the beneﬁts of combining parametric and non-parametric memory with genera-\\ntion for knowledge-intensive tasks —tasks that humans could not reasonably be expected to perform\\nwithout access to an external knowledge source. Our RAG models achieve state-of-the-art results\\non open Natural Questions [ 29], WebQuestions [ 3] and CuratedTrec [ 2] and strongly outperform\\nrecent approaches that use specialised pre-training objectives on TriviaQA [ 24]. Despite these being\\nextractive tasks, we ﬁnd that unconstrained generation outperforms previous extractive approaches.\\nFor knowledge-intensive generation, we experiment with MS-MARCO [ 1] and Je<PERSON><PERSON>y question\\ngeneration, and we ﬁnd that our models generate responses that are more factual, speciﬁc, and\\ndiverse than a BART baseline. For FEVER [ 56] fact veriﬁcation, we achieve results within 4.3% of\\nstate-of-the-art pipeline models which use strong retrieval supervision. Finally, we demonstrate that', metadata={'source': '/content/RAG.pdf', 'page': 1}),\n", " Document(page_content='the non-parametric memory can be replaced to update the models’ knowledge as the world changes.1\\n2 Methods\\nWe explore RAG models, which use the input sequence xto retrieve text documents zand use them\\nas additional context when generating the target sequence y. As shown in Figure 1, our models\\nleverage two components: (i) a retriever pη(z|x)with parameters ηthat returns (top-K truncated)\\ndistributions over text passages given a query xand (ii) a generator pθ(yi|x,z,y 1:i−1)parametrized\\n1Code to run experiments with RAG has been open-sourced as part of the HuggingFace Transform-\\ners Library [ 66] and can be found at https://github.com/huggingface/transformers/blob/master/\\nexamples/rag/ . An interactive demo of RAG models can be found at https://huggingface.co/rag/\\n2', metadata={'source': '/content/RAG.pdf', 'page': 1}),\n", " Document(page_content='byθthat generates a current token based on a context of the previous i−1tokensy1:i−1, the original\\ninputxand a retrieved passage z.\\nTo train the retriever and generator end-to-end, we treat the retrieved document as a latent variable.\\nWe propose two models that marginalize over the latent documents in different ways to produce a\\ndistribution over generated text. In one approach, RAG-Sequence , the model uses the same document\\nto predict each target token. The second approach, RAG-Token , can predict each target token based\\non a different document. In the following, we formally introduce both models and then describe the\\npηandpθcomponents, as well as the training and decoding procedure.\\n2.1 Models\\nRAG-Sequence Model The RAG-Sequence model uses the same retrieved document to generate\\nthe complete sequence . Technically, it treats the retrieved document as a single latent variable that\\nis marginalized to get the seq2seq probability p(y|x)via a top-K approximation. Concretely, the', metadata={'source': '/content/RAG.pdf', 'page': 2}),\n", " Document(page_content='top K documents are retrieved using the retriever, and the generator produces the output sequence\\nprobability for each document, which are then marginalized,\\npRAG-Sequence (y|x)≈∑\\nz∈top-k(p(·|x))pη(z|x)pθ(y|x,z) =∑\\nz∈top-k(p(·|x))pη(z|x)N∏\\nipθ(yi|x,z,y 1:i−1)\\nRAG-Token Model In the RAG-Token model we can draw a different latent document for each\\ntarget token and marginalize accordingly. This allows the generator to choose content from several\\ndocuments when producing an answer. Concretely, the top K documents are retrieved using the\\nretriever, and then the generator produces a distribution for the next output token for each document,\\nbefore marginalizing, and repeating the process with the following output token, Formally, we deﬁne:\\npRAG-Token (y|x)≈N∏\\ni∑\\nz∈top-k(p(·|x))pη(z|x)pθ(yi|x,z,y 1:i−1)\\nFinally, we note that RAG can be used for sequence classiﬁcation tasks by considering the target class', metadata={'source': '/content/RAG.pdf', 'page': 2}),\n", " Document(page_content='as a target sequence of length one, in which case RAG-Sequence and RAG-Token are equivalent.\\n2.2 Retriever: DPR\\nThe retrieval component pη(z|x)is based on DPR [26]. DPR follows a bi-encoder architecture:\\npη(z|x)∝exp(\\nd(z)⊤q(x))\\nd(z) =BERTd(z),q(x) =BERTq(x)\\nwhere d(z)is a dense representation of a document produced by a BERT BASE document encoder [8],\\nandq(x)a query representation produced by a query encoder , also based on BERT BASE. Calculating\\ntop-k (pη(·|x)), the list ofkdocumentszwith highest prior probability pη(z|x), is a Maximum Inner\\nProduct Search (MIPS) problem, which can be approximately solved in sub-linear time [ 23]. We use\\na pre-trained bi-encoder from DPR to initialize our retriever and to build the document index. This\\nretriever was trained to retrieve documents which contain answers to TriviaQA [ 24] questions and\\nNatural Questions [29]. We refer to the document index as the non-parametric memory .\\n2.3 Generator: BART', metadata={'source': '/content/RAG.pdf', 'page': 2}),\n", " Document(page_content='2.3 Generator: BART\\nThe generator component pθ(yi|x,z,y 1:i−1)could be modelled using any encoder-decoder. We use\\nBART-large [ 32], a pre-trained seq2seq transformer [ 58] with 400M parameters. To combine the input\\nxwith the retrieved content zwhen generating from BART, we simply concatenate them. BART was\\npre-trained using a denoising objective and a variety of different noising functions. It has obtained\\nstate-of-the-art results on a diverse set of generation tasks and outperforms comparably-sized T5\\nmodels [32]. We refer to the BART generator parameters θas the parametric memory henceforth.\\n2.4 Training\\nWe jointly train the retriever and generator components without any direct supervision on what\\ndocument should be retrieved. Given a ﬁne-tuning training corpus of input/output pairs (xj,yj), we\\n3', metadata={'source': '/content/RAG.pdf', 'page': 2}),\n", " Document(page_content='minimize the negative marginal log-likelihood of each target,∑\\nj−logp(yj|xj)using stochastic\\ngradient descent with Adam [ 28]. Updating the document encoder BERTdduring training is costly as\\nit requires the document index to be periodically updated as REALM does during pre-training [ 20].\\nWe do not ﬁnd this step necessary for strong performance, and keep the document encoder (and\\nindex) ﬁxed, only ﬁne-tuning the query encoder BERT qand the BART generator.\\n2.5 Decoding\\nAt test time, RAG-Sequence and RAG-Token require different ways to approximate arg maxyp(y|x).\\nRAG-Token The RAG-Token model can be seen as a standard, autoregressive seq2seq genera-\\ntor with transition probability: p′\\nθ(yi|x,y 1:i−1) =∑\\nz∈top-k(p(·|x))pη(zi|x)pθ(yi|x,zi,y1:i−1)To\\ndecode, we can plug p′\\nθ(yi|x,y 1:i−1)into a standard beam decoder.\\nRAG-Sequence For RAG-Sequence, the likelihood p(y|x)does not break into a conventional per-', metadata={'source': '/content/RAG.pdf', 'page': 3}),\n", " Document(page_content='token likelihood, hence we cannot solve it with a single beam search. Instead, we run beam search for\\neach document z, scoring each hypothesis using pθ(yi|x,z,y 1:i−1). This yields a set of hypotheses\\nY, some of which may not have appeared in the beams of all documents. To estimate the probability\\nof an hypothesis ywe run an additional forward pass for each document zfor whichydoes not\\nappear in the beam, multiply generator probability with pη(z|x)and then sum the probabilities across\\nbeams for the marginals. We refer to this decoding procedure as “Thorough Decoding.” For longer\\noutput sequences,|Y|can become large, requiring many forward passes. For more efﬁcient decoding,\\nwe can make a further approximation that pθ(y|x,zi)≈0whereywas not generated during beam\\nsearch from x,zi. This avoids the need to run additional forward passes once the candidate set Yhas\\nbeen generated. We refer to this decoding procedure as “Fast Decoding.”\\n3 Experiments', metadata={'source': '/content/RAG.pdf', 'page': 3}),\n", " Document(page_content='3 Experiments\\nWe experiment with RAG in a wide range of knowledge-intensive tasks. For all experiments, we use\\na single Wikipedia dump for our non-parametric knowledge source. Following <PERSON> et al. [31] and\\<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al. [26], we use the December 2018 dump. Each Wikipedia article is split into disjoint\\n100-word chunks, to make a total of 21M documents. We use the document encoder to compute an\\nembedding for each document, and build a single MIPS index using FAISS [ 23] with a Hierarchical\\nNavigable Small World approximation for fast retrieval [ 37]. During training, we retrieve the top\\nkdocuments for each query. We consider k∈{5,10}for training and set kfor test time using dev\\ndata. We now discuss experimental details for each task.\\n3.1 Open-domain Question Answering\\nOpen-domain question answering (QA) is an important real-world application and common testbed\\nfor knowledge-intensive tasks [ 20]. We treat questions and answers as input-output text pairs (x,y)', metadata={'source': '/content/RAG.pdf', 'page': 3}),\n", " Document(page_content='and train RAG by directly minimizing the negative log-likelihood of answers. We compare RAG to\\nthe popular extractive QA paradigm [ 5,7,31,26], where answers are extracted spans from retrieved\\ndocuments, relying primarily on non-parametric knowledge. We also compare to “Closed-Book\\nQA” approaches [ 52], which, like RAG, generate answers, but which do not exploit retrieval, instead\\nrelying purely on parametric knowledge. We consider four popular open-domain QA datasets: Natural\\nQuestions (NQ) [ 29], TriviaQA (TQA) [ 24]. WebQuestions (WQ) [ 3] and CuratedTrec (CT) [ 2]. As\\nCT and WQ are small, we follow DPR [ 26] by initializing CT and WQ models with our NQ RAG\\nmodel. We use the same train/dev/test splits as prior work [ 31,26] and report Exact Match (EM)\\nscores. For TQA, to compare with T5 [52], we also evaluate on the TQA Wiki test set.\\n3.2 Abstractive Question Answering\\nRAG models can go beyond simple extractive QA and answer questions with free-form, abstractive', metadata={'source': '/content/RAG.pdf', 'page': 3}),\n", " Document(page_content='text generation. To test RAG’s natural language generation (NLG) in a knowledge-intensive setting,\\nwe use the MSMARCO NLG task v2.1 [ 43]. The task consists of questions, ten gold passages\\nretrieved from a search engine for each question, and a full sentence answer annotated from the\\nretrieved passages. We do not use the supplied passages, only the questions and answers, to treat\\n4', metadata={'source': '/content/RAG.pdf', 'page': 3}),\n", " Document(page_content='MSMARCO as an open-domain abstractive QA task. MSMARCO has some questions that cannot be\\nanswered in a way that matches the reference answer without access to the gold passages, such as\\n“What is the weather in V olcano, CA?” so performance will be lower without using gold passages.\\nWe also note that some MSMARCO questions cannot be answered using Wikipedia alone. Here,\\nRAG can rely on parametric knowledge to generate reasonable responses.\\n3.3 Jeopardy Question Generation\\nTo evaluate RAG’s generation abilities in a non-QA setting, we study open-domain question gen-\\neration. Rather than use questions from standard open-domain QA tasks, which typically consist\\nof short, simple questions, we propose the more demanding task of generating Jeopardy questions.\\nJeopardy is an unusual format that consists of trying to guess an entity from a fact about that entity.\\nFor example, “The World Cup” is the answer to the question “In 1986 Mexico scored as the ﬁrst', metadata={'source': '/content/RAG.pdf', 'page': 4}),\n", " Document(page_content='country to host this international sports competition twice.” As Jeopardy questions are precise,\\nfactual statements, generating Jeopardy questions conditioned on their answer entities constitutes a\\nchallenging knowledge-intensive generation task.\\nWe use the splits from SearchQA [ 10], with 100K train, 14K dev, and 27K test examples. As\\nthis is a new task, we train a BART model for comparison. Following [ 67], we evaluate using the\\nSQuAD-tuned Q-BLEU-1 metric [ 42]. Q-BLEU is a variant of BLEU with a higher weight for\\nmatching entities and has higher correlation with human judgment for question generation than\\nstandard metrics. We also perform two human evaluations, one to assess generation factuality, and\\none for speciﬁcity. We deﬁne factuality as whether a statement can be corroborated by trusted external\\nsources, and speciﬁcity as high mutual dependence between the input and output [ 33]. We follow', metadata={'source': '/content/RAG.pdf', 'page': 4}),\n", " Document(page_content='best practice and use pairwise comparative evaluation [ 34]. Eva<PERSON><PERSON> are shown an answer and two\\ngenerated questions, one from BART and one from RAG. They are then asked to pick one of four\\noptions—quuestion A is better, question B is better, both are good, or neither is good.\\n3.4 Fact Veriﬁcation\\nFEVER [ 56] requires classifying whether a natural language claim is supported or refuted by\\nWikipedia, or whether there is not enough information to decide. The task requires retrieving\\nevidence from Wikipedia relating to the claim and then reasoning over this evidence to classify\\nwhether the claim is true, false, or unveriﬁable from Wikipedia alone. FEVER is a retrieval problem\\ncoupled with an challenging entailment reasoning task. It also provides an appropriate testbed for\\nexploring the RAG models’ ability to handle classiﬁcation rather than generation. We map FEVER\\nclass labels (supports, refutes, or not enough info) to single output tokens and directly train with', metadata={'source': '/content/RAG.pdf', 'page': 4}),\n", " Document(page_content='claim-class pairs. Crucially, unlike most other approaches to FEVER, we do not use supervision on\\nretrieved evidence. In many real-world applications, retrieval supervision signals aren’t available, and\\nmodels that do not require such supervision will be applicable to a wider range of tasks. We explore\\ntwo variants: the standard 3-way classiﬁcation task (supports/refutes/not enough info) and the 2-way\\n(supports/refutes) task studied in Thorne and Vlachos [57]. In both cases we report label accuracy.\\n4 Results\\n4.1 Open-domain Question Answering\\nTable 1 shows results for RAG along with state-of-the-art models. On all four open-domain QA\\ntasks, RAG sets a new state of the art (only on the T5-comparable split for TQA). RAG combines\\nthe generation ﬂexibility of the “closed-book” (parametric only) approaches and the performance of\\n\"open-book\" retrieval-based approaches. Unlike REALM and T5+SSM, RAG enjoys strong results', metadata={'source': '/content/RAG.pdf', 'page': 4}),\n", " Document(page_content='without expensive, specialized “salient span masking” pre-training [ 20]. It is worth noting that RAG’s\\nretriever is initialized using DPR’s retriever, which uses retrieval supervision on Natural Questions\\nand TriviaQA. RAG compares favourably to the DPR QA system, which uses a BERT-based “cross-\\nencoder” to re-rank documents, along with an extractive reader. RAG demonstrates that neither a\\nre-ranker nor extractive reader is necessary for state-of-the-art performance.\\nThere are several advantages to generating answers even when it is possible to extract them. Docu-\\nments with clues about the answer but do not contain the answer verbatim can still contribute towards\\na correct answer being generated, which is not possible with standard extractive approaches, leading\\n5', metadata={'source': '/content/RAG.pdf', 'page': 4}),\n", " Document(page_content='Table 1: Open-Domain QA Test Scores. For TQA,\\nleft column uses the standard test set for Open-\\nDomain QA, right column uses the TQA-Wiki\\ntest set. See Appendix D for further details.\\nModel NQ TQA WQ CT\\nClosed\\nBookT5-11B [52] 34.5 - /50.1 37.4 -\\nT5-11B+SSM[52] 36.6 - /60.5 44.7 -\\nOpen\\nBookREALM [20] 40.4 - / - 40.7 46.8\\nDPR [26] 41.5 57.9/ - 41.1 50.6\\nRAG-Token 44.1 55.2/66.1 45.5 50.0\\nRAG-Seq. 44.5 56.8/ 68.0 45.2 52.2Table 2: Generation and classiﬁcation Test Scores.\\nMS-MARCO SotA is [ 4], FEVER-3 is [ 68] and\\nFEVER-2 is [ 57] *Uses gold context/evidence.\\nBest model without gold access underlined.\\nModel Jeopardy MSMARCO FVR3 FVR2\\nB-1 QB-1 R-L B-1 Label Acc.\\nSotA - - 49.8*49.9*76.8 92.2 *\\nBART 15.1 19.7 38.2 41.6 64.0 81.1\\nRAG-Tok. 17.3 22.2 40.1 41.572.5 89.5RAG-Seq. 14.7 21.4 40.8 44.2\\nto more effective marginalization over documents. Furthermore, RAG can generate correct answers\\neven when the correct answer is not in any retrieved document, achieving 11.8% accuracy in such', metadata={'source': '/content/RAG.pdf', 'page': 5}),\n", " Document(page_content='cases for NQ, where an extractive model would score 0%.\\n4.2 Abstractive Question Answering\\nAs shown in Table 2, RAG-Sequence outperforms BART on Open MS-MARCO NLG by 2.6 Bleu\\npoints and 2.6 Rouge-L points. RAG approaches state-of-the-art model performance, which is\\nimpressive given that (i) those models access gold passages with speciﬁc information required to\\ngenerate the reference answer , (ii) many questions are unanswerable without the gold passages, and\\n(iii) not all questions are answerable from Wikipedia alone. Table 3 shows some generated answers\\nfrom our models. Qualitatively, we ﬁnd that RAG models hallucinate less and generate factually\\ncorrect text more often than BART. Later, we also show that RAG generations are more diverse than\\nBART generations (see §4.5).\\n4.3 Jeopardy Question Generation\\nTable 2 shows that RAG-Token performs better than RAG-Sequence on Jeopardy question generation,', metadata={'source': '/content/RAG.pdf', 'page': 5}),\n", " Document(page_content='with both models outperforming BART on Q-BLEU-1. 4 shows human evaluation results, over 452\\npairs of generations from BART and RAG-Token. Evaluators indicated that BART was more factual\\nthan RAG in only 7.1% of cases, while RAG was more factual in 42.7% of cases, and both RAG and\\nBART were factual in a further 17% of cases, clearly demonstrating the effectiveness of RAG on\\nthe task over a state-of-the-art generation model. Evaluators also ﬁnd RAG generations to be more\\nspeciﬁc by a large margin. Table 3 shows typical generations from each model.\\nJeopardy questions often contain two separate pieces of information, and RAG-Token may perform\\nbest because it can generate responses that combine content from several documents. Figure 2 shows\\nan example. When generating “Sun”, the posterior is high for document 2 which mentions “The\\nSun Also Rises”. Similarly, document 1 dominates the posterior when “A Farewell to Arms” is', metadata={'source': '/content/RAG.pdf', 'page': 5}),\n", " Document(page_content='generated. Intriguingly, after the ﬁrst token of each book is generated, the document posterior ﬂattens.\\nThis observation suggests that the generator can complete the titles without depending on speciﬁc\\ndocuments. In other words, the model’s parametric knowledge is sufﬁcient to complete the titles. We\\nﬁnd evidence for this hypothesis by feeding the BART-only baseline with the partial decoding \"The\\nSun. BART completes the generation \"The SunAlso Rises\" isanovel bythis author of\"The Sun\\nAlso Rises\" indicating the title \"The Sun Also Rises\" is stored in BART’s parameters. Similarly,\\nBART will complete the partial decoding \"The SunAlso Rises\" isanovel bythis author of\"A\\nwith \"The SunAlso Rises\" isanovel bythis author of\"AFarewell toArms\" . This example shows\\nhow parametric and non-parametric memories work together —the non-parametric component helps\\nto guide the generation, drawing out speciﬁc knowledge stored in the parametric memory.\\n4.4 Fact Veriﬁcation', metadata={'source': '/content/RAG.pdf', 'page': 5}),\n", " Document(page_content='Table 2 shows our results on FEVER. For 3-way classiﬁcation, RAG scores are within 4.3% of\\nstate-of-the-art models, which are complex pipeline systems with domain-speciﬁc architectures and\\nsubstantial engineering, trained using intermediate retrieval supervision, which RAG does not require.\\n6', metadata={'source': '/content/RAG.pdf', 'page': 5}),\n", " Document(page_content='Document 1 : his works are considered classics of American\\nliterature ... His wartime experiences formed the basis for his novel\\n”A Farewell to Arms” (1929) ...\\nDocument 2 : ... artists of the 1920s ”Lost Generation” expatriate\\ncommunity. His debut novel, ”The Sun Also Rises” , was published\\nin 1926.\\nBOS”\\nTheSunAlsoRises”isa\\nnovelbythis\\nauthorof”A\\nFarewellto\\nArms”Doc 1\\nDoc 2\\nDoc 3\\nDoc 4\\nDoc 5Figure 2: RAG-Token document posterior p(zi|x,yi,y−i)for each generated token for input “Hem-\\ningway\" for Jeopardy generation with 5 retrieved documents. The posterior for document 1 is high\\nwhen generating “A Farewell to Arms\" and for document 2 when generating “The Sun Also Rises\".\\nTable 3: Examples from generation tasks. RAG models generate more speciﬁc and factually accurate\\nresponses. ‘?’ indicates factually incorrect responses, * indicates partially correct responses.\\nTask Input Model Generation\\nMS-\\nMARCOdeﬁne middle', metadata={'source': '/content/RAG.pdf', 'page': 6}),\n", " Document(page_content='MARCOdeﬁne middle\\nearBART?The middle ear is the part of the ear between the middle ear and the nose.\\nRAG-T The middle ear is the portion of the ear internal to the eardrum.\\nRAG-S The middle ear includes the tympanic cavity and the three ossicles.\\nwhat currency\\nneeded in\\nscotlandBART The currency needed in Scotland is Pound sterling.\\nRAG-T Pound is the currency needed in Scotland.\\nRAG-S The currency needed in Scotland is the pound sterling.\\nJeopardy\\nQuestion\\nGener\\n-ationWashingtonBART?This state has the largest number of counties in the U.S.\\nRAG-T It’s the only U.S. state named for a U.S. president\\nRAG-S It’s the state where you’ll ﬁnd Mount Rainier National Park\\nThe Divine\\nComedyBART*This epic poem by <PERSON> is divided into 3 parts: the Inferno, the Purgatorio & the Purgatorio\\nRAG-T Dante’s \"Inferno\" is the ﬁrst part of this epic poem\\nRAG-S This 14th century work is divided into 3 sections: \"Inferno\", \"Purgatorio\" & \"Paradiso\"', metadata={'source': '/content/RAG.pdf', 'page': 6}),\n", " Document(page_content='For 2-way classiﬁcation, we compare against <PERSON> and <PERSON><PERSON><PERSON><PERSON> [57], who train RoBERTa [ 35]\\nto classify the claim as true or false given the gold evidence sentence. RAG achieves an accuracy\\nwithin 2.7% of this model, despite being supplied with only the claim and retrieving its own evidence.\\nWe also analyze whether documents retrieved by RAG correspond to documents annotated as gold\\nevidence in FEVER. We calculate the overlap in article titles between the top kdocuments retrieved\\nby RAG and gold evidence annotations. We ﬁnd that the top retrieved document is from a gold article\\nin 71% of cases, and a gold article is present in the top 10 retrieved articles in 90% of cases.\\n4.5 Additional Results\\nGeneration Diversity Section 4.3 shows that RAG models are more factual and speciﬁc than\\nBART for Jeopardy question generation. Following recent work on diversity-promoting decoding\\n[33,59,39], we also investigate generation diversity by calculating the ratio of distinct ngrams to', metadata={'source': '/content/RAG.pdf', 'page': 6}),\n", " Document(page_content='total ngrams generated by different models. Table 5 shows that RAG-Sequence’s generations are\\nmore diverse than RAG-Token’s, and both are signiﬁcantly more diverse than BART without needing\\nany diversity-promoting decoding.\\nRetrieval Ablations A key feature of RAG is learning to retrieve relevant information for the task.\\nTo assess the effectiveness of the retrieval mechanism, we run ablations where we freeze the retriever\\nduring training. As shown in Table 6, learned retrieval improves results for all tasks.\\nWe compare RAG’s dense retriever to a word overlap-based BM25 retriever [ 53]. Here, we replace\\nRAG’s retriever with a ﬁxed BM25 system, and use BM25 retrieval scores as logits when calculating\\np(z|x). Table 6 shows the results. For FEVER, BM25 performs best, perhaps since FEVER claims are\\nheavily entity-centric and thus well-suited for word overlap-based retrieval. Differentiable retrieval\\nimproves results on all other tasks, especially for Open-Domain QA, where it is crucial.', metadata={'source': '/content/RAG.pdf', 'page': 6}),\n", " Document(page_content='Index hot-swapping An advantage of non-parametric memory models like RAG is that knowledge\\ncan be easily updated at test time. Parametric-only models like T5 or BART need further training to\\nupdate their behavior as the world changes. To demonstrate, we build an index using the DrQA [ 5]\\nWikipedia dump from December 2016 and compare outputs from RAG using this index to the newer\\nindex from our main results (December 2018). We prepare a list of 82 world leaders who had changed\\n7', metadata={'source': '/content/RAG.pdf', 'page': 6}),\n", " Document(page_content='Table 4: Human assessments for the Jeopardy\\nQuestion Generation Task.\\nFactuality Speciﬁcity\\nBART better 7.1% 16.8%\\nRAG better 42.7% 37.4%\\nBoth good 11.7% 11.8%\\nBoth poor 17.7% 6.9%\\nNo majority 20.8% 20.1%Table 5: Ratio of distinct to total tri-grams for\\ngeneration tasks.\\nMSMARCO Jeopardy QGen\\nGold 89.6% 90.0%\\nBART 70.7% 32.4%\\nRAG-Token 77.8% 46.8%\\nRAG-Seq. 83.5% 53.8%\\nTable 6: Ablations on the dev set. As FEVER is a classiﬁcation task, both RAG models are equivalent.\\nModel NQ TQA WQ CT Jeopardy-QGen MSMarco FVR-3 FVR-2\\nExact Match B-1 QB-1 R-L B-1 Label Accuracy\\nRAG-Token-BM25 29.7 41.5 32.1 33.1 17.5 22.3 55.5 48.475.1 91.6RAG-Sequence-BM25 31.8 44.1 36.6 33.8 11.1 19.5 56.5 46.9\\nRAG-Token-Frozen 37.8 50.1 37.1 51.1 16.7 21.7 55.9 49.472.9 89.4RAG-Sequence-<PERSON>ozen 41.2 52.1 41.8 52.6 11.8 19.6 56.7 47.3\\nRAG-Token 43.5 54.8 46.5 51.9 17.9 22.6 56.2 49.474.5 90.6RAG-Sequence 44.0 55.8 44.9 53.4 15.3 21.5 57.2 47.5', metadata={'source': '/content/RAG.pdf', 'page': 7}),\n", " Document(page_content='between these dates and use a template “Who is {position}?” (e.g. “Who is the President of Peru?”)\\nto query our NQ RAG model with each index. RAG answers 70% correctly using the 2016 index for\\n2016 world leaders and 68% using the 2018 index for 2018 world leaders. Accuracy with mismatched\\nindices is low (12% with the 2018 index and 2016 leaders, 4% with the 2016 index and 2018 leaders).\\nThis shows we can update RAG’s world knowledge by simply replacing its non-parametric memory.\\nEffect of Retrieving more documents Models are trained with either 5 or 10 retrieved latent\\ndocuments, and we do not observe signiﬁcant differences in performance between them. We have the\\nﬂexibility to adjust the number of retrieved documents at test time, which can affect performance and\\nruntime. Figure 3 (left) shows that retrieving more documents at test time monotonically improves\\nOpen-domain QA results for RAG-Sequence, but performance peaks for RAG-Token at 10 retrieved', metadata={'source': '/content/RAG.pdf', 'page': 7}),\n", " Document(page_content='documents. Figure 3 (right) shows that retrieving more documents leads to higher Rouge-L for\\nRAG-Token at the expense of Bleu-1, but the effect is less pronounced for RAG-Sequence.\\n10 20 30 40 50\\nKR e t r i e v e dD o c s394041424344NQ Exact MatchRAG-Tok\\nRAG-Seq\\n10 20 30 40 50\\nKR e t r i e v e dD o c s4050607080NQ Answer Recall @ KRAG-Tok\\nRAG-Seq\\nFixed DPR\\nBM25\\n10 20 30 40 50\\nKR e t r i e v e dD o c s4850525456Bleu-1 / Rouge-L scoreRAG-Tok R-L\\nRAG-Tok B-1\\nRAG-Seq R-L\\nRAG-Seq B-1\\nFigure 3: Left: NQ performance as more documents are retrieved. Center: Retrieval recall perfor-\\nmance in NQ. Right: MS-MARCO Bleu-1 and Rouge-L as more documents are retrieved.\\n5 Related Work\\nSingle-Task Retrieval Prior work has shown that retrieval improves performance across a variety of\\nNLP tasks when considered in isolation. Such tasks include open-domain question answering [ 5,29],\\nfact checking [ 56], fact completion [ 48], long-form question answering [ 12], Wikipedia article', metadata={'source': '/content/RAG.pdf', 'page': 7}),\n", " Document(page_content='generation [ 36], dialogue [ 41,65,9,13], translation [ 17], and language modeling [ 19,27]. Our\\nwork uniﬁes previous successes in incorporating retrieval into individual tasks, showing that a single\\nretrieval-based architecture is capable of achieving strong performance across several tasks.\\n8', metadata={'source': '/content/RAG.pdf', 'page': 7}),\n", " Document(page_content='General-Purpose Architectures for NLP Prior work on general-purpose architectures for NLP\\ntasks has shown great success without the use of retrieval. A single, pre-trained language model\\nhas been shown to achieve strong performance on various classiﬁcation tasks in the GLUE bench-\\nmarks [ 60,61] after ﬁne-tuning [ 49,8]. GPT-2 [ 50] later showed that a single, left-to-right, pre-trained\\nlanguage model could achieve strong performance across both discriminative and generative tasks.\\nFor further improvement, BART [ 32] and T5 [ 51,52] propose a single, pre-trained encoder-decoder\\nmodel that leverages bi-directional attention to achieve stronger performance on discriminative\\nand generative tasks. Our work aims to expand the space of possible tasks with a single, uniﬁed\\narchitecture, by learning a retrieval module to augment pre-trained, generative language models.\\nLearned Retrieval There is signiﬁcant work on learning to retrieve documents in information', metadata={'source': '/content/RAG.pdf', 'page': 8}),\n", " Document(page_content='retrieval, more recently with pre-trained, neural language models [ 44,26] similar to ours. Some\\nwork optimizes the retrieval module to aid in a speciﬁc, downstream task such as question answering,\\nusing search [ 46], reinforcement learning [ 6,63,62], or a latent variable approach [ 31,20] as in our\\nwork. These successes leverage different retrieval-based architectures and optimization techniques to\\nachieve strong performance on a single task, while we show that a single retrieval-based architecture\\ncan be ﬁne-tuned for strong performance on a variety of tasks.\\nMemory-based Architectures Our document index can be seen as a large external memory for\\nneural networks to attend to, analogous to memory networks [ 64,55]. Concurrent work [ 14] learns\\nto retrieve a trained embedding for each entity in the input, rather than to retrieve raw text as in our\\nwork. Other work improves the ability of dialog models to generate factual text by attending over', metadata={'source': '/content/RAG.pdf', 'page': 8}),\n", " Document(page_content='fact embeddings [ 15,13]. A key feature of our memory is that it is comprised of raw text rather\\ndistributed representations, which makes the memory both (i) human-readable, lending a form of\\ninterpretability to our model, and (ii) human-writable, enabling us to dynamically update the model’s\\nmemory by editing the document index. This approach has also been used in knowledge-intensive\\ndialog, where generators have been conditioned on retrieved text directly, albeit obtained via TF-IDF\\nrather than end-to-end learnt retrieval [9].\\nRetrieve-and-Edit approaches Our method shares some similarities with retrieve-and-edit style\\napproaches, where a similar training input-output pair is retrieved for a given input, and then edited\\nto provide a ﬁnal output. These approaches have proved successful in a number of domains including\\nMachine Translation [ 18,22] and Semantic Parsing [ 21]. Our approach does have several differences,', metadata={'source': '/content/RAG.pdf', 'page': 8}),\n", " Document(page_content='including less of emphasis on lightly editing a retrieved item, but on aggregating content from several\\npieces of retrieved content, as well as learning latent retrieval, and retrieving evidence documents\\nrather than related training pairs. This said, RAG techniques may work well in these settings, and\\ncould represent promising future work.\\n6 Discussion\\nIn this work, we presented hybrid generation models with access to parametric and non-parametric\\nmemory. We showed that our RAG models obtain state of the art results on open-domain QA. We\\nfound that people prefer RAG’s generation over purely parametric BART, ﬁnding RAG more factual\\nand speciﬁc. We conducted an thorough investigation of the learned retrieval component, validating\\nits effectiveness, and we illustrated how the retrieval index can be hot-swapped to update the model\\nwithout requiring any retraining. In future work, it may be fruitful to investigate if the two components', metadata={'source': '/content/RAG.pdf', 'page': 8}),\n", " Document(page_content='can be jointly pre-trained from scratch, either with a denoising objective similar to BART or some\\nanother objective. Our work opens up new research directions on how parametric and non-parametric\\nmemories interact and how to most effectively combine them, showing promise in being applied to a\\nwide variety of NLP tasks.\\n9', metadata={'source': '/content/RAG.pdf', 'page': 8}),\n", " Document(page_content='Broader Impact\\nThis work offers several positive societal beneﬁts over previous work: the fact that it is more\\nstrongly grounded in real factual knowledge (in this case Wikipedia) makes it “hallucinate” less\\nwith generations that are more factual, and offers more control and interpretability. RAG could be\\nemployed in a wide variety of scenarios with direct beneﬁt to society, for example by endowing it\\nwith a medical index and asking it open-domain questions on that topic, or by helping people be more\\neffective at their jobs.\\nWith these advantages also come potential downsides: Wikipedia, or any potential external knowledge\\nsource, will probably never be entirely factual and completely devoid of bias. Since RAG can be\\nemployed as a language model, similar concerns as for GPT-2 [ 50] are valid here, although arguably\\nto a lesser extent, including that it might be used to generate abuse, faked or misleading content in', metadata={'source': '/content/RAG.pdf', 'page': 9}),\n", " Document(page_content='the news or on social media; to impersonate others; or to automate the production of spam/phishing\\ncontent [ 54]. Advanced language models may also lead to the automation of various jobs in the\\ncoming decades [ 16]. In order to mitigate these risks, AI systems could be employed to ﬁght against\\nmisleading content and automated spam/phishing.\\nAcknowledgments\\nThe authors would like to thank the reviewers for their thoughtful and constructive feedback on this\\npaper, as well as HuggingFace for their help in open-sourcing code to run RAG models. The authors\\nwould also like to thank <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> for productive discussions and advice. EP\\nthanks supports from the NSF Graduate Research Fellowship. PL is supported by the FAIR PhD\\nprogram.\\nReferences\\n[1]<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>\\nMaj<PERSON>der, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>', metadata={'source': '/content/RAG.pdf', 'page': 9}),\n", " Document(page_content=<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. MS MARCO: A Human Generated MAchine\\nReading COmprehension Dataset. arXiv:1611.09268 [cs] , November 2016. URL http:\\n//arxiv.org/abs/1611.09268 . arXiv: 1611.09268.\\n[2]<PERSON><PERSON> and <PERSON> `y. Modeling of the question answering task in the yodaqa system. In\\nInternational Conference of the Cross-Language Evaluation Forum for European Languages ,\\npages 222–228. Springer, 2015. URL https://link.springer.com/chapter/10.1007%\\n2F978-3-319-24027-5_20 .\\n[3]<PERSON>, <PERSON>, <PERSON>, and <PERSON>. <PERSON><PERSON><PERSON> Parsing on Freebase\\nfrom Question-Answer Pairs. In Proceedings of the 2013 Conference on Empirical Methods\\nin Natural Language Processing , pages 1533–1544, Seattle, Washington, USA, October 2013.\\nAssociation for Computational Linguistics. URL http://www.aclweb.org/anthology/\\nD13-1160 .\\n[4]<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Palm: Pre-training an autoencod-', metadata={'source': '/content/RAG.pdf', 'page': 9}),\n", " Document(page_content='ing&autoregressive language model for context-conditioned generation. ArXiv , abs/2004.07159,\\n2020. URL https://arxiv.org/abs/2004.07159 .\\n[5]<PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Reading Wikipedia to Answer\\nOpen-Domain Questions. In Proceedings of the 55th Annual Meeting of the Association for\\nComputational Linguistics (Volume 1: Long Papers) , pages 1870–1879, Vancouver, Canada,\\nJuly 2017. Association for Computational Linguistics. doi: 10.18653/v1/P17-1171. URL\\nhttps://www.aclweb.org/anthology/P17-1171 .\\n[6]<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and\\n<PERSON><PERSON><PERSON>. Coarse-to-ﬁne question answering for long documents. In Proceedings of the\\n55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers) ,\\npages 209–220, Vancouver, Canada, July 2017. Association for Computational Linguistics. doi:\\n10.18653/v1/P17-1020. URL https://www.aclweb.org/anthology/P17-1020 .\\n10', metadata={'source': '/content/RAG.pdf', 'page': 9}),\n", " Document(page_content='[7]<PERSON> and <PERSON>. Simple and Effective Multi-Paragraph Reading Compre-\\nhension. arXiv:1710.10723 [cs] , October 2017. URL http://arxiv.org/abs/1710.10723 .\\narXiv: 1710.10723.\\n[8]<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. BERT: Pre-training of\\nDeep Bidirectional Transformers for Language Understanding. In Proceedings of the 2019 Con-\\nference of the North American Chapter of the Association for Computational Linguistics: Human\\nLanguage Technologies, Volume 1 (Long and Short Papers) , pages 4171–4186, Minneapolis,\\nMinnesota, June 2019. Association for Computational Linguistics. doi: 10.18653/v1/N19-1423.\\nURL https://www.aclweb.org/anthology/N19-1423 .\\n[9]<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Wiz-\\nard of wikipedia: Knowledge-powered conversational agents. In International Conference on\\nLearning Representations , 2019. URL https://openreview.net/forum?id=r1l73iRqKm .', metadata={'source': '/content/RAG.pdf', 'page': 10}),\n", " Document(page_content='[10] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>\\nCho. SearchQA: A New Q&A Dataset Augmented with Context from a Search Engine.\\narXiv:1704.05179 [cs] , April 2017. URL http://arxiv.org/abs/1704.05179 . arXiv:\\n1704.05179.\\n[11] <PERSON>, <PERSON>, and <PERSON><PERSON>. Hierarchical neural story generation. In Proceed-\\nings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 1:\\nLong Papers) , pages 889–898, Melbourne, Australia, July 2018. Association for Computational\\nLinguistics. doi: 10.18653/v1/P18-1082. URL https://www.aclweb.org/anthology/\\nP18-1082 .\\n[12] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. ELI5:\\nLong form question answering. In Proceedings of the 57th Annual Meeting of the Association\\nfor Computational Linguistics , pages 3558–3567, Florence, Italy, July 2019. Association for\\nComputational Linguistics. doi: 10.18653/v1/P19-1346. URL https://www.aclweb.org/', metadata={'source': '/content/RAG.pdf', 'page': 10}),\n", " Document(page_content='anthology/P19-1346 .\\n[13] <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Augmenting transformers\\nwith KNN-based composite memory, 2020. URL https://openreview.net/forum?id=\\nH1gx1CNKPH .\\n[14] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.\\nEntities as experts: Sparse memory access with entity supervision. ArXiv , abs/2004.07202,\\n2020. URL https://arxiv.org/abs/2004.07202 .\\n[15] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON>. A knowledge-grounded neural conversation model. In AAAI\\nConference on Artiﬁcial Intelligence , 2018. URL https://www.aaai.org/ocs/index.php/\\nAAAI/AAAI18/paper/view/16710 .\\n[16] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. When will AI\\nexceed human performance? evidence from AI experts. CoRR , abs/1705.08807, 2017. URL\\nhttp://arxiv.org/abs/1705.08807 .', metadata={'source': '/content/RAG.pdf', 'page': 10}),\n", " Document(page_content='[17] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Search engine guided neural\\nmachine translation. In AAAI Conference on Artiﬁcial Intelligence , 2018. URL https:\\n//www.aaai.org/ocs/index.php/AAAI/AAAI18/paper/view/17282 .\\n[18] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Search engine guided neural\\nmachine translation. In 32nd AAAI Conference on Artiﬁcial Intelligence, AAAI 2018 , 32nd\\nAAAI Conference on Artiﬁcial Intelligence, AAAI 2018, pages 5133–5140. AAAI press, 2018.\\n32nd AAAI Conference on Artiﬁcial Intelligence, AAAI 2018 ; Conference date: 02-02-2018\\nThrough 07-02-2018.\\n[19] <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Generating sentences by\\nediting prototypes. Transactions of the Association for Computational Linguistics , 6:437–450,\\n2018. doi: 10.1162/tacl_a_00030. URL https://www.aclweb.org/anthology/Q18-1031 .\\n11', metadata={'source': '/content/RAG.pdf', 'page': 10}),\n", " Document(page_content='[20] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. REALM:\\nRetrieval-augmented language model pre-training. ArXiv , abs/2002.08909, 2020. URL https:\\n//arxiv.org/abs/2002.08909 .\\n[21] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A\\nretrieve-and-edit framework for predicting structured outputs. In S. <PERSON>,\\nH<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, ed-\\nitors, Advances in Neural Information Processing Systems 31 , pages 10052–\\n10062. Curran Associates, Inc., 2018. URL http://papers.nips.cc/paper/\\n8209-a-retrieve-and-edit-framework-for-predicting-structured-outputs.\\npdf.\\n[22] <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Simple and effective retrieve-\\nedit-rerank text generation. In Proceedings of the 58th Annual Meeting of the Association for\\nComputational Linguistics , pages 2532–2538, Online, July 2020. Association for Computa-', metadata={'source': '/content/RAG.pdf', 'page': 11}),\n", " Document(page_content='tional Linguistics. doi: 10.18653/v1/2020.acl-main.228. URL https://www.aclweb.org/\\nanthology/2020.acl-main.228 .\\n[23] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Billion-scale similarity search with gpus. arXiv\\npreprint arXiv:1702.08734 , 2017. URL https://arxiv.org/abs/1702.08734 .\\n[24] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. TriviaQA: A Large Scale\\nDistantly Supervised Challenge Dataset for Reading Comprehension. In Proceedings of the\\n55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers) ,\\npages 1601–1611, Vancouver, Canada, July 2017. Association for Computational Linguistics.\\ndoi: 10.18653/v1/P17-1147. URL https://www.aclweb.org/anthology/P17-1147 .\\n[25] <PERSON> and <PERSON>. Inferring algorithmic patterns with stack-\\naugmented recurrent nets. In Proceedings of the 28th International Conference on\\nNeural Information Processing Systems - Volume 1 , NIPS’15, page 190–198, Cam-', metadata={'source': '/content/RAG.pdf', 'page': 11}),\n", " Document(page_content='bridge, MA, USA, 2015. MIT Press. URL https://papers.nips.cc/paper/\\n5857-inferring-algorithmic-patterns-with-stack-augmented-recurrent-nets .\\n[26] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and\\n<PERSON><PERSON>-<PERSON>u <PERSON>. Dense passage retrieval for open-domain question answering. arXiv preprint\\narXiv:2004.04906 , 2020. URL https://arxiv.org/abs/2004.04906 .\\n[27] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Generaliza-\\ntion through memorization: Nearest neighbor language models. In International Conference on\\nLearning Representations , 2020. URL https://openreview.net/forum?id=HklBjCEKvH .\\n[28] <PERSON><PERSON><PERSON> and <PERSON>: A method for stochastic optimization. In Yoshua\\nBeng<PERSON> and <PERSON><PERSON>, editors, 3rd International Conference on Learning Representations,\\nICLR 2015, San Diego, CA, USA, May 7-9, 2015, Conference Track Proceedings , 2015. URL\\nhttp://arxiv.org/abs/1412.6980 .', metadata={'source': '/content/RAG.pdf', 'page': 11}),\n", " Document(page_content='[29] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,\\<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Natural Questions: a Benchmark for Ques-\\ntion Answering Research. Transactions of the Association of Computational Lin-\\nguistics , 2019. URL https://tomkwiat.users.x20web.corp.google.com/papers/\\nnatural-questions/main-1455-kwiatkowski.pdf .\\n[30] <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and\\nHerve Jegou. Large memory layers with product keys. In <PERSON><PERSON>, <PERSON>,\\nA. Beygel<PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural In-\\nformation Processing Systems 32 , pages 8548–8559. Curran Associates, Inc., 2019. URL http:\\n//papers.nips.cc/paper/9061-large-memory-layers-with-product-keys.pdf .', metadata={'source': '/content/RAG.pdf', 'page': 11}),\n", " Document(page_content='[31] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Latent retrieval for weakly supervised\\nopen domain question answering. In Proceedings of the 57th Annual Meeting of the Association\\n12', metadata={'source': '/content/RAG.pdf', 'page': 11}),\n", " Document(page_content='for Computational Linguistics , pages 6086–6096, Florence, Italy, July 2019. Association for\\nComputational Linguistics. doi: 10.18653/v1/P19-1612. URL https://www.aclweb.org/\\nanthology/P19-1612 .\\n[32] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. BART: Denoising sequence-to-sequence\\npre-training for natural language generation, translation, and comprehension. arXiv preprint\\narXiv:1910.13461 , 2019. URL https://arxiv.org/abs/1910.13461 .\\n[33] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A diversity-promoting\\nobjective function for neural conversation models. In Proceedings of the 2016 Conference of the\\nNorth American Chapter of the Association for Computational Linguistics: Human Language\\nTechnologies , pages 110–119, San Diego, California, June 2016. Association for Computational\\nLinguistics. doi: 10.18653/v1/N16-1014. URL https://www.aclweb.org/anthology/\\nN16-1014 .', metadata={'source': '/content/RAG.pdf', 'page': 12}),\n", " Document(page_content='N16-1014 .\\n[34] <PERSON>, <PERSON>, and <PERSON>. Acute-eval: Improved dialogue evaluation\\nwith optimized questions and multi-turn comparisons. ArXiv , abs/1909.03087, 2019. URL\\nhttps://arxiv.org/abs/1909.03087 .\\n[35] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Robust neural machine\\ntranslation with joint textual and phonetic embedding. In Proceedings of the 57th Annual\\nMeeting of the Association for Computational Linguistics , pages 3044–3049, Florence, Italy,\\nJuly 2019. Association for Computational Linguistics. doi: 10.18653/v1/P19-1291. URL\\nhttps://www.aclweb.org/anthology/P19-1291 .\\n[36] <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>,\\nand <PERSON>. Generating wikipedia by summarizing long sequences. In International\\nConference on Learning Representations , 2018. URL https://openreview.net/forum?\\nid=Hyg0vbWC- .', metadata={'source': '/content/RAG.pdf', 'page': 12}),\n", " Document(page_content='id=Hyg0vbWC- .\\n[37] <PERSON><PERSON> and <PERSON><PERSON> <PERSON><PERSON>. Efﬁcient and robust approximate nearest neighbor search\\nusing hierarchical navigable small world graphs. IEEE Transactions on Pattern Analysis and\\nMachine Intelligence , 42:824–836, 2016. URL https://arxiv.org/abs/1603.09320 .\\n[38] <PERSON>. The next decade in ai: four steps towards robust artiﬁcial intelligence. arXiv\\npreprint arXiv:2002.06177 , 2020. URL https://arxiv.org/abs/2002.06177 .\\n[39] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. How decoding strategies affect the\\nveriﬁability of generated text. arXiv preprint arXiv:1911.03587 , 2019. URL https:\\n//arxiv.org/abs/1911.03587 .\\n[40] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,\\<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>', metadata={'source': '/content/RAG.pdf', 'page': 12}),\n", " Document(page_content='precision training. In ICLR , 2018. URL https://openreview.net/forum?id=r1gs9JgRZ .\\n[41] <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Towards exploit-\\ning background knowledge for building conversation systems. In Proceedings of the 2018\\nConference on Empirical Methods in Natural Language Processing , pages 2322–2332, Brus-\\nsels, Belgium, October-November 2018. Association for Computational Linguistics. doi:\\n10.18653/v1/D18-1255. URL https://www.aclweb.org/anthology/D18-1255 .\\n[42] <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Towards a better metric for evaluating question generation\\nsystems. In Proceedings of the 2018 Conference on Empirical Methods in Natural Language\\nProcessing , pages 3950–3959, Brussels, Belgium, October-November 2018. Association for\\nComputational Linguistics. doi: 10.18653/v1/D18-1429. URL https://www.aclweb.org/\\nanthology/D18-1429 .\\n[43] <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,', metadata={'source': '/content/RAG.pdf', 'page': 12}),\n", " Document(page_content='and <PERSON> Deng. MS MARCO: A human generated machine reading comprehension dataset. In\\nTare<PERSON>, <PERSON>, <PERSON>ur <PERSON><PERSON><PERSON>, and <PERSON>, editors,\\nProceedings of the Workshop on Cognitive Computation: Integrating neural and symbolic\\n13', metadata={'source': '/content/RAG.pdf', 'page': 12}),\n", " Document(page_content='approaches 2016 co-located with the 30th Annual Conference on Neural Information Processing\\nSystems (NIPS 2016), Barcelona, Spain, December 9, 2016 , volume 1773 of CEUR Workshop\\nProceedings . CEUR-WS.org, 2016. URL http://ceur-ws.org/Vol-1773/CoCoNIPS_\\n2016_paper9.pdf .\\n[44] <PERSON> and <PERSON><PERSON><PERSON><PERSON>. <PERSON> re-ranking with BERT. arXiv preprint\\narXiv:1901.04085 , 2019. URL https://arxiv.org/abs/1901.04085 .\\n[45] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,\\nand <PERSON>. fairseq: A fast, extensible toolkit for sequence modeling. In Proceedings\\nof the 2019 Conference of the North American Chapter of the Association for Computational\\nLinguistics (Demonstrations) , pages 48–53, Minneapolis, Minnesota, June 2019. Association\\nfor Computational Linguistics. doi: 10.18653/v1/N19-4009. URL https://www.aclweb.\\norg/anthology/N19-4009 .\\n[46] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>', metadata={'source': '/content/RAG.pdf', 'page': 13}),\n", " Document(page_content='Cho. Finding generalizable evidence by learning to convince q&a models. In Proceedings\\nof the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th\\nInternational Joint Conference on Natural Language Processing (EMNLP-IJCNLP) , pages\\n2402–2411, Hong Kong, China, November 2019. Association for Computational Linguistics.\\ndoi: 10.18653/v1/D19-1244. URL https://www.aclweb.org/anthology/D19-1244 .\\n[47] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,\\nand <PERSON>. Language models as knowledge bases? In Proceedings of the 2019\\nConference on Empirical Methods in Natural Language Processing and the 9th International\\nJoint Conference on Natural Language Processing (EMNLP-IJCNLP) , pages 2463–2473, Hong\\nKong, China, November 2019. Association for Computational Linguistics. doi: 10.18653/v1/\\nD19-1250. URL https://www.aclweb.org/anthology/D19-1250 .', metadata={'source': '/content/RAG.pdf', 'page': 13}),\n", " Document(page_content='[48] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. How context affects language models’ factual predictions. In\\nAutomated Knowledge Base Construction , 2020. URL https://openreview.net/forum?\\nid=025X0zPfn .\\n[49] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Im-\\nproving Language Understanding by Generative Pre-Training, 2018. URL\\nhttps://s3-us-west-2.amazonaws.com/openai-assets/research-covers/\\nlanguage-unsupervised/language_understanding_paper.pdf .\\n[50] <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>\\nSutskever. Language models are unsupervised multitask learners, 2019. URL\\nhttps://d4mucfpksywv.cloudfront.net/better-language-models/language_\\nmodels_are_unsupervised_multitask_learners.pdf .\\n[51] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>,', metadata={'source': '/content/RAG.pdf', 'page': 13}),\n", " Document(page_content=<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Exploring the limits of transfer learning with a uniﬁed\\ntext-to-text transformer. arXiv e-prints , 2019. URL https://arxiv.org/abs/1910.10683 .\\n[52] <PERSON>, <PERSON>, and <PERSON><PERSON>. How much knowledge can you pack into\\nthe parameters of a language model? arXiv e-prints , 2020. URL https://arxiv.org/abs/\\n2002.08910 .\\n[53] <PERSON> and <PERSON>. The probabilistic relevance framework: Bm25 and\\nbeyond. Found. Trends Inf. Retr. , 3(4):333–389, April 2009. ISSN 1554-0669. doi: 10.1561/\\n1500000019. URL https://doi.org/10.1561/1500000019 .\\n[54] <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Release strategies and the social impacts of language models.\\nArXiv , abs/1908.09203, 2019.\\n[55] <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. End-to-end memory net-', metadata={'source': '/content/RAG.pdf', 'page': 13}),\n", " Document(page_content='works. In <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances\\nin Neural Information Processing Systems 28 , pages 2440–2448. Curran Associates, Inc., 2015.\\nURL http://papers.nips.cc/paper/5846-end-to-end-memory-networks.pdf .\\n14', metadata={'source': '/content/RAG.pdf', 'page': 13}),\n", " Document(page_content='[56] <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. FEVER: a\\nlarge-scale dataset for fact extraction and VERiﬁcation. In Proceedings of the 2018 Conference\\nof the North American Chapter of the Association for Computational Linguistics: Human\\nLanguage Technologies, Volume 1 (Long Papers) , pages 809–819, New Orleans, Louisiana,\\nJune 2018. Association for Computational Linguistics. doi: 10.18653/v1/N18-1074. URL\\nhttps://www.aclweb.org/anthology/N18-1074 .\\n[57] <PERSON> and <PERSON>. Avoiding catastrophic forgetting in mitigating model\\nbiases in sentence-pair classiﬁcation with elastic weight consolidation. ArXiv , abs/2004.14366,\\n2020. URL https://arxiv.org/abs/2004.14366 .\\n[58] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>,\\nŁ uk<PERSON><PERSON>, and <PERSON><PERSON>. Attention is all you need. In <PERSON><PERSON>, U. V . <PERSON>,', metadata={'source': '/content/RAG.pdf', 'page': 14}),\n", " Document(page_content='<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural\\nInformation Processing Systems 30 , pages 5998–6008. Curran Associates, Inc., 2017. URL\\nhttp://papers.nips.cc/paper/7181-attention-is-all-you-need.pdf .\\n[59] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Diverse beam search for improved description of complex scenes.\\nAAAI Conference on Artiﬁcial Intelligence , 2018. URL https://www.aaai.org/ocs/index.\\nphp/AAAI/AAAI18/paper/view/17329 .\\n[60] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>.\\nGLUE: A multi-task benchmark and analysis platform for natural language understanding.\\nInProceedings of the 2018 EMNLP Workshop BlackboxNLP: Analyzing and Interpreting\\nNeural Networks for NLP , pages 353–355, Brussels, Belgium, November 2018. Association for\\nComputational Linguistics. doi: 10.18653/v1/W18-5446. URL https://www.aclweb.org/', metadata={'source': '/content/RAG.pdf', 'page': 14}),\n", " Document(page_content='anthology/W18-5446 .\\n[61] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. SuperGLUE: A Stickier Benchmark for General-\\nPurpose Language Understanding Systems. In <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>,\\nF. d\\\\textquotesingle <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural Information\\nProcessing Systems 32 , pages 3261–3275. Curran Associates, Inc., 2019. URL https://\\narxiv.org/abs/1905.00537 .\\n[62] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>,\\<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. R3: Reinforced ranker-reader for open-domain\\nquestion answering. In <PERSON> and <PERSON><PERSON>, editors, Proceedings of\\nthe Thirty-Second AAAI Conference on Artiﬁcial Intelligence, (AAAI-18), the 30th innovative\\nApplications of Artiﬁcial Intelligence (IAAI-18), and the 8th AAAI Symposium on Educational', metadata={'source': '/content/RAG.pdf', 'page': 14}),\n", " Document(page_content='Advances in Artiﬁcial Intelligence (EAAI-18), New Orleans, Louisiana, USA, February 2-7,\\n2018 , pages 5981–5988. AAAI Press, 2018. URL https://www.aaai.org/ocs/index.\\nphp/AAAI/AAAI18/paper/view/16712 .\\n[63] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,\\<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Evidence aggregation for answer re-\\nranking in open-domain question answering. In ICLR , 2018. URL https://openreview.\\nnet/forum?id=rJl3yM-Ab .\\n[64] <PERSON>, <PERSON><PERSON>, and <PERSON>. Memory networks. In Yo<PERSON><PERSON>\\nand <PERSON>, editors, 3rd International Conference on Learning Representations, ICLR\\n2015, San Diego, CA, USA, May 7-9, 2015, Conference Track Proceedings , 2015. URL\\nhttp://arxiv.org/abs/1410.3916 .\\n[65] <PERSON>, <PERSON>, and <PERSON>. Retrieve and reﬁne: Improved sequence\\ngeneration models for dialogue. In Proceedings of the 2018 EMNLP Workshop SCAI: The 2nd', metadata={'source': '/content/RAG.pdf', 'page': 14}),\n", " Document(page_content='International Workshop on Search-Oriented Conversational AI , pages 87–92, Brussels, Belgium,\\nOctober 2018. Association for Computational Linguistics. doi: 10.18653/v1/W18-5713. URL\\nhttps://www.aclweb.org/anthology/W18-5713 .\\n15', metadata={'source': '/content/RAG.pdf', 'page': 14}),\n", " Document(page_content='[66] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>,\\<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>face’s transformers:\\nState-of-the-art natural language processing. ArXiv , abs/1910.03771, 2019.\\n[67] <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Addressing semantic drift in question generation for semi-\\nsupervised question answering. In Proceedings of the 2019 Conference on Empirical Meth-\\nods in Natural Language Processing and the 9th International Joint Conference on Natural\\nLanguage Processing (EMNLP-IJCNLP) , pages 2495–2509, Hong Kong, China, Novem-\\nber 2019. Association for Computational Linguistics. doi: 10.18653/v1/D19-1253. URL\\nhttps://www.aclweb.org/anthology/D19-1253 .', metadata={'source': '/content/RAG.pdf', 'page': 15}),\n", " Document(page_content='[68] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and\\n<PERSON><PERSON>. Reasoning over semantic-level graph for fact checking. ArXiv , abs/1909.03745, 2019.\\nURL https://arxiv.org/abs/1909.03745 .\\n16', metadata={'source': '/content/RAG.pdf', 'page': 15}),\n", " Document(page_content='Appendices for Retrieval-Augmented Generation for\\nKnowledge-Intensive NLP Tasks\\nA Implementation Details\\nFor Open-domain QA we report test numbers using 15 retrieved documents for RAG-Token models.\\nFor RAG-Sequence models, we report test results using 50 retrieved documents, and we use the\\nThorough Decoding approach since answers are generally short. We use greedy decoding for QA as\\nwe did not ﬁnd beam search improved results. For Open-MSMarco and Jeopardy question generation,\\nwe report test numbers using ten retrieved documents for both RAG-Token and RAG-Sequence,\\nand we also train a BART-large model as a baseline. We use a beam size of four, and use the Fast\\nDecoding approach for RAG-Sequence models, as Thorough Decoding did not improve performance.\\nB Human Evaluation\\nFigure 4: Annotation interface for human evaluation of factuality. A pop-out for detailed instructions\\nand a worked example appear when clicking \"view tool guide\".', metadata={'source': '/content/RAG.pdf', 'page': 16}),\n", " Document(page_content='Figure 4 shows the user interface for human evaluation. To avoid any biases for screen position,\\nwhich model corresponded to sentence A and sentence B was randomly selected for each example.\\nAnnotators were encouraged to research the topic using the internet, and were given detailed instruc-\\ntions and worked examples in a full instructions tab. We included some gold sentences in order to\\nassess the accuracy of the annotators. Two annotators did not perform well on these examples and\\ntheir annotations were removed from the results.\\nC Training setup Details\\nWe train all RAG models and BART baselines using Fairseq [ 45].2We train with mixed precision\\nﬂoating point arithmetic [ 40], distributing training across 8, 32GB NVIDIA V100 GPUs, though\\ntraining and inference can be run on one GPU. We ﬁnd that doing Maximum Inner Product Search\\nwith FAISS is sufﬁciently fast on CPU, so we store document index vectors on CPU, requiring ∼100', metadata={'source': '/content/RAG.pdf', 'page': 16}),\n", " Document(page_content='GB of CPU memory for all of Wikipedia. After submission, We have ported our code to HuggingFace\\nTransformers [ 66]3, which achieves equivalent performance to the previous version but is a cleaner\\nand easier to use implementation. This version is also open-sourced. We also compress the document\\nindex using FAISS’s compression tools, reducing the CPU memory requirement to 36GB. Scripts to\\nrun experiments with RAG can be found at https://github.com/huggingface/transformers/\\nblob/master/examples/rag/README.md and an interactive demo of a RAG model can be found\\nathttps://huggingface.co/rag/\\n2https://github.com/pytorch/fairseq\\n3https://github.com/huggingface/transformers\\n17View full instructions\\nWhich sentence is more factually true?\\nView tool guide\\nSelect an option\\nSubject\\n: Hemingway\\nNote: Some questions are\\nSentence A is more\\ntrue\\ncontrol questions. We require\\nSentence A: \"The Sun Also Rises\" is a novel by this author of \"A\\ngood accuracy on our control\\nFarewell to Arms\"', metadata={'source': '/content/RAG.pdf', 'page': 16}),\n", " Document(page_content='<PERSON><PERSON>ell to Arms\"\\nSentence B is more\\nquestions to accept\\ntrue\\nresponses.\\nSentence B : This author of \"The Sun Also Rises\"was born in\\nBoth sentences are\\nHavana, Cuba, the son of Spanish immigrants\\ntrue\\nIndicatewhichoneof the\\nfollowing sentences is more\\nBoth sentences are\\ncompletely untrue\\nfactually true with respect to\\nthe subject. Using the\\ninternet to check whether\\nthe sentences are true is\\nencouraged.', metadata={'source': '/content/RAG.pdf', 'page': 16}),\n", " Document(page_content='D Further Details on Open-Domain QA\\nFor open-domain QA, multiple answer annotations are often available for a given question. These\\nanswer annotations are exploited by extractive models during training as typically all the answer\\nannotations are used to ﬁnd matches within documents when preparing training data. For RAG, we\\nalso make use of multiple annotation examples for Natural Questions and WebQuestions by training\\nthe model with each (q,a)pair separately, leading to a small increase in accuracy. For TriviaQA,\\nthere are often many valid answers to a given question, some of which are not suitable training targets,\\nsuch as emoji or spelling variants. For TriviaQA, we ﬁlter out answer candidates if they do not occur\\nin top 1000 documents for the query.\\nCuratedTrec preprocessing The answers for CuratedTrec are given in the form of regular expres-\\nsions, which has been suggested as a reason why it is unsuitable for answer-generation models [20].', metadata={'source': '/content/RAG.pdf', 'page': 17}),\n", " Document(page_content='To overcome this, we use a pre-processing step where we ﬁrst retrieve the top 1000 documents for\\neach query, and use the answer that most frequently matches the regex pattern as the supervision\\ntarget. If no matches are found, we resort to a simple heuristic: generate all possible permutations for\\neach regex, replacing non-deterministic symbols in the regex nested tree structure with a whitespace.\\nTriviaQA Evaluation setups The open-domain QA community customarily uses public develop-\\nment datasets as test datasets, as test data for QA datasets is often restricted and dedicated to reading\\ncompehension purposes. We report our results using the datasets splits used in DPR [ 26], which are\\nconsistent with common practice in Open-domain QA. For TriviaQA, this test dataset is the public\\nTriviaQA Web Development split. <PERSON> et al. [52] used the TriviaQA ofﬁcial Wikipedia test set\\ninstead. F<PERSON><PERSON><PERSON> et al. [14] follow this convention in order to compare with <PERSON> et al. [52] (See', metadata={'source': '/content/RAG.pdf', 'page': 17}),\n", " Document(page_content='appendix of [ 14]). We report results on both test sets to enable fair comparison to both approaches.\\nWe ﬁnd that our performance is much higher using the ofﬁcial Wiki test set, rather than the more\\nconventional open-domain test set, which we attribute to the ofﬁcial Wiki test set questions being\\nsimpler to answer from Wikipedia.\\nE Further Details on FEVER\\nFor FEVER classiﬁcation, we follow the practice from [ 32], and ﬁrst re-generate the claim, and\\nthen classify using the representation of the ﬁnal hidden state, before ﬁnally marginalizing across\\ndocuments to obtain the class probabilities. The FEVER task traditionally has two sub-tasks. The\\nﬁrst is to classify the claim as either \"Supported\", \"Refuted\" or \"Not Enough Info\", which is the task\\nwe explore in the main paper. FEVER’s other sub-task involves extracting sentences from Wikipedia\\nas evidence supporting the classiﬁcation prediction. As FEVER uses a different Wikipedia dump to', metadata={'source': '/content/RAG.pdf', 'page': 17}),\n", " Document(page_content='us, directly tackling this task is not straightforward. We hope to address this in future work.\\nF Null Document Probabilities\\nWe experimented with adding \"Null document\" mechanism to RAG, similar to REALM [ 20] in order\\nto model cases where no useful information could be retrieved for a given input. Here, if kdocuments\\nwere retrieved, we would additionally \"retrieve\" an empty document and predict a logit for the null\\ndocument, before marginalizing over k+ 1predictions. We explored modelling this null document\\nlogit by learning (i) a document embedding for the null document, (ii) a static learnt bias term, or\\n(iii) a neural network to predict the logit. We did not ﬁnd that these improved performance, so in\\nthe interests of simplicity, we omit them. For Open MS-MARCO, where useful retrieved documents\\ncannot always be retrieved, we observe that the model learns to always retrieve a particular set of', metadata={'source': '/content/RAG.pdf', 'page': 17}),\n", " Document(page_content='documents for questions that are less likely to beneﬁt from retrieval, suggesting that null document\\nmechanisms may not be necessary for RAG.\\nG Parameters\\nOur RAG models contain the trainable parameters for the BERT-base query and document encoder of\\nDPR, with 110M parameters each (although we do not train the document encoder ourselves) and\\n406M trainable parameters from BART-large, 406M parameters, making a total of 626M trainable\\n18', metadata={'source': '/content/RAG.pdf', 'page': 17}),\n", " Document(page_content='Table 7: Number of instances in the datasets used. *A hidden subset of this data is used for evaluation\\nTask Train Development Test\\nNatural Questions 79169 8758 3611\\nTriviaQA 78786 8838 11314\\nWebQuestions 3418 362 2033\\nCuratedTrec 635 134 635\\nJeopardy Question Generation 97392 13714 26849\\nMS-MARCO 153726 12468 101093*\\nFEVER-3-way 145450 10000 10000\\nFEVER-2-way 96966 6666 6666\\nparameters. The best performing \"closed-book\" (parametric only) open-domain QA model is T5-11B\\nwith 11 Billion trainable parameters. The T5 model with the closest number of parameters to our\\nmodels is T5-large (770M parameters), which achieves a score of 28.9 EM on Natural Questions [ 52],\\nsubstantially below the 44.5 that RAG-Sequence achieves, indicating that hybrid parametric/non-\\nparametric models require far fewer trainable parameters for strong open-domain QA performance.\\nThe non-parametric memory index does not consist of trainable parameters, but does consists of 21M', metadata={'source': '/content/RAG.pdf', 'page': 18}),\n", " Document(page_content='728 dimensional vectors, consisting of 15.3B values. These can be easily be stored at 8-bit ﬂoating\\npoint precision to manage memory and disk footprints.\\nH Retrieval Collapse\\nIn preliminary experiments, we observed that for some tasks such as story generation [ 11], the\\nretrieval component would “collapse” and learn to retrieve the same documents regardless of the\\ninput. In these cases, once retrieval had collapsed, the generator would learn to ignore the documents,\\nand the RAG model would perform equivalently to BART. The collapse could be due to a less-explicit\\nrequirement for factual knowledge in some tasks, or the longer target sequences, which could result\\nin less informative gradients for the retriever. <PERSON> et al. [46] also found spurious retrieval results\\nwhen optimizing a retrieval component in order to improve performance on downstream tasks.\\nI Number of instances per dataset', metadata={'source': '/content/RAG.pdf', 'page': 18}),\n", " Document(page_content='The number of training, development and test datapoints in each of our datasets is shown in Table 7.\\n19', metadata={'source': '/content/RAG.pdf', 'page': 18})]"]}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "code", "source": ["vector_db = Weaviate.from_documents(\n", "    docs, embeddings, client=client, by_text=False\n", ")"], "metadata": {"id": "zluFUYQ3sdxX"}, "execution_count": 14, "outputs": []}, {"cell_type": "code", "source": ["print(vector_db.similarity_search(\"what is rag?\", k=3)[0].page_content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Y8qLjlXYtoFk", "outputId": "692c1514-012b-4383-86a5-1deb43deb3a6"}, "execution_count": 19, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["documents for questions that are less likely to beneﬁt from retrieval, suggesting that null document\n", "mechanisms may not be necessary for RAG.\n", "G Parameters\n", "Our RAG models contain the trainable parameters for the BERT-base query and document encoder of\n", "DPR, with 110M parameters each (although we do not train the document encoder ourselves) and\n", "406M trainable parameters from BART-large, 406M parameters, making a total of 626M trainable\n", "18\n"]}]}, {"cell_type": "code", "source": ["print(vector_db.similarity_search(\"what is rag?\", k=3)[1].page_content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vVj7MAw-uE9B", "outputId": "01f81a6b-7026-4e69-a8b3-18ec57339249"}, "execution_count": 20, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["cases for NQ, where an extractive model would score 0%.\n", "4.2 Abstractive Question Answering\n", "As shown in Table 2, RAG-Sequence outperforms BART on Open MS-MARCO NLG by 2.6 Bleu\n", "points and 2.6 Rouge-L points. RAG approaches state-of-the-art model performance, which is\n", "impressive given that (i) those models access gold passages with speciﬁc information required to\n", "generate the reference answer , (ii) many questions are unanswerable without the gold passages, and\n", "(iii) not all questions are answerable from Wikipedia alone. Table 3 shows some generated answers\n", "from our models. Qualitatively, we ﬁnd that RAG models hallucinate less and generate factually\n", "correct text more often than BART. Later, we also show that RAG generations are more diverse than\n", "BART generations (see §4.5).\n", "4.3 Jeopardy Question Generation\n", "Table 2 shows that RAG-Token performs better than RAG-Sequence on Jeopardy question generation,\n"]}]}, {"cell_type": "code", "source": ["print(vector_db.similarity_search(\"what is rag?\", k=3)[2].page_content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6oudP8LruHJj", "outputId": "11bb882b-4a28-470f-a552-a80084319830"}, "execution_count": 21, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Broader Impact\n", "This work offers several positive societal beneﬁts over previous work: the fact that it is more\n", "strongly grounded in real factual knowledge (in this case Wikipedia) makes it “hallucinate” less\n", "with generations that are more factual, and offers more control and interpretability. RAG could be\n", "employed in a wide variety of scenarios with direct beneﬁt to society, for example by endowing it\n", "with a medical index and asking it open-domain questions on that topic, or by helping people be more\n", "effective at their jobs.\n", "With these advantages also come potential downsides: Wikipedia, or any potential external knowledge\n", "source, will probably never be entirely factual and completely devoid of bias. Since RAG can be\n", "employed as a language model, similar concerns as for GPT-2 [ 50] are valid here, although arguably\n", "to a lesser extent, including that it might be used to generate abuse, faked or misleading content in\n"]}]}, {"cell_type": "code", "source": ["print(\n", "    vector_db.similarity_search(\n", "        \"what is attention?\", k=3)\n", "    )"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dsvgQsH2tgvT", "outputId": "38d1fa3c-eebf-490e-c7e2-e9c6d1d521ad"}, "execution_count": 16, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[Document(page_content='<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural\\nInformation Processing Systems 30 , pages 5998–6008. Curran Associates, Inc., 2017. URL\\nhttp://papers.nips.cc/paper/7181-attention-is-all-you-need.pdf .\\n[59] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Diverse beam search for improved description of complex scenes.\\nAAAI Conference on Artiﬁcial Intelligence , 2018. URL https://www.aaai.org/ocs/index.\\nphp/AAAI/AAAI18/paper/view/17329 .\\n[60] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>.\\nGLUE: A multi-task benchmark and analysis platform for natural language understanding.\\nInProceedings of the 2018 EMNLP Workshop BlackboxNLP: Analyzing and Interpreting\\nNeural Networks for NLP , pages 353–355, Brussels, Belgium, November 2018. Association for\\nComputational Linguistics. doi: 10.18653/v1/W18-5446. URL https://www.aclweb.org/', metadata={'page': 14, 'source': '/content/RAG.pdf'}), Document(page_content='[56] <PERSON>, Andreas Vlachos, Christos Christodoulopoulos, and Arpit Mittal. FEVER: a\\nlarge-scale dataset for fact extraction and VERiﬁcation. In Proceedings of the 2018 Conference\\nof the North American Chapter of the Association for Computational Linguistics: Human\\nLanguage Technologies, Volume 1 (Long Papers) , pages 809–819, New Orleans, Louisiana,\\nJune 2018. Association for Computational Linguistics. doi: 10.18653/v1/N18-1074. URL\\nhttps://www.aclweb.org/anthology/N18-1074 .\\n[57] James H. Thorne and Andreas Vlachos. Avoiding catastrophic forgetting in mitigating model\\nbiases in sentence-pair classiﬁcation with elastic weight consolidation. ArXiv , abs/2004.14366,\\n2020. URL https://arxiv.org/abs/2004.14366 .\\n[58] Ashish Vaswani, Noam Shazeer, Niki Parmar, Jakob Uszkoreit, Llion Jones, Aidan N Gomez,\\nŁ ukasz Kaiser, and Illia Polosukhin. Attention is all you need. In I. Guyon, U. V . Luxburg,', metadata={'page': 14, 'source': '/content/RAG.pdf'}), Document(page_content='General-Purpose Architectures for NLP Prior work on general-purpose architectures for NLP\\ntasks has shown great success without the use of retrieval. A single, pre-trained language model\\nhas been shown to achieve strong performance on various classiﬁcation tasks in the GLUE bench-\\nmarks [ 60,61] after ﬁne-tuning [ 49,8]. GPT-2 [ 50] later showed that a single, left-to-right, pre-trained\\nlanguage model could achieve strong performance across both discriminative and generative tasks.\\nFor further improvement, BART [ 32] and T5 [ 51,52] propose a single, pre-trained encoder-decoder\\nmodel that leverages bi-directional attention to achieve stronger performance on discriminative\\nand generative tasks. Our work aims to expand the space of possible tasks with a single, uniﬁed\\narchitecture, by learning a retrieval module to augment pre-trained, generative language models.\\nLearned Retrieval There is signiﬁcant work on learning to retrieve documents in information', metadata={'page': 8, 'source': '/content/RAG.pdf'})]\n"]}]}, {"cell_type": "code", "source": ["from langchain.prompts import ChatPromptTemplate\n", "\n", "template=\"\"\"You are an assistant for question-answering tasks.\n", "Use the following pieces of retrieved context to answer the question.\n", "If you don't know the answer, just say that you don't know.\n", "Use ten sentences maximum and keep the answer concise.\n", "Question: {question}\n", "Context: {context}\n", "Answer:\n", "\"\"\""], "metadata": {"id": "r-iUy_kqs3sz"}, "execution_count": 22, "outputs": []}, {"cell_type": "code", "source": ["prompt=ChatPromptTemplate.from_template(template)"], "metadata": {"id": "IPQkyjuXuTJH"}, "execution_count": 23, "outputs": []}, {"cell_type": "code", "source": ["prompt"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tuwzW99<PERSON><PERSON>", "outputId": "b8cda52e-6d01-474b-a808-37ffa380262f"}, "execution_count": 24, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["ChatPromptTemplate(input_variables=['context', 'question'], messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['context', 'question'], template=\"You are an assistant for question-answering tasks.\\nUse the following pieces of retrieved context to answer the question.\\nIf you don't know the answer, just say that you don't know.\\nUse ten sentences maximum and keep the answer concise.\\nQuestion: {question}\\nContext: {context}\\nAnswer:\\n\"))])"]}, "metadata": {}, "execution_count": 24}]}, {"cell_type": "code", "source": ["from langchain import HuggingFaceHub"], "metadata": {"id": "AlFnn-9GueFz"}, "execution_count": 25, "outputs": []}, {"cell_type": "code", "source": ["from google.colab import userdata\n", "huggingfacehub_api_token=userdata.get('HuGGINGFACE_TOKEN')"], "metadata": {"id": "fhJ2KAlivPFH"}, "execution_count": 26, "outputs": []}, {"cell_type": "code", "source": ["model = HuggingFaceHub(\n", "    huggingfacehub_api_token=huggingfacehub_api_token,\n", "    repo_id=\"mistralai/Mistral-7B-Instruct-v0.1\",\n", "    model_kwargs={\"temperature\":1, \"max_length\":180}\n", ")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "inS4srvcvAy8", "outputId": "4347ebb3-72c9-407d-e528-aa2fcac781ec"}, "execution_count": 27, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/langchain_core/_api/deprecation.py:117: LangChainDeprecationWarning: The class `langchain_community.llms.huggingface_hub.HuggingFaceHub` was deprecated in langchain-community 0.0.21 and will be removed in 0.2.0. Use HuggingFaceEndpoint instead.\n", "  warn_deprecated(\n"]}]}, {"cell_type": "code", "source": ["from langchain.schema.runnable import RunnablePassthrough\n", "from langchain.schema.output_parser import StrOutputParser"], "metadata": {"id": "0OOsmfqkve6T"}, "execution_count": 28, "outputs": []}, {"cell_type": "code", "source": ["output_parser=StrOutputParser()"], "metadata": {"id": "qHwkkEyfvtlr"}, "execution_count": 29, "outputs": []}, {"cell_type": "code", "source": ["retriever=vector_db.as_retriever()"], "metadata": {"id": "I18V6AE4v359"}, "execution_count": 30, "outputs": []}, {"cell_type": "code", "source": ["rag_chain = (\n", "    {\"context\": retriever,  \"question\": RunnablePassthrough()}\n", "    | prompt\n", "    | model\n", "    | output_parser\n", ")"], "metadata": {"id": "XgIZfslPvlDu"}, "execution_count": 31, "outputs": []}, {"cell_type": "code", "source": ["print(rag_chain.invoke(\"what is rag system?\"))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1oVtr-_5vmAG", "outputId": "a4b5c919-f850-4a9a-a760-3b92468068e1"}, "execution_count": 35, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Human: You are an assistant for question-answering tasks.\n", "Use the following pieces of retrieved context to answer the question.\n", "If you don't know the answer, just say that you don't know.\n", "Use ten sentences maximum and keep the answer concise.\n", "Question: what is rag system?\n", "Context: [Document(page_content='Table 2 shows our results on FEVER. For 3-way classiﬁcation, RAG scores are within 4.3% of\\nstate-of-the-art models, which are complex pipeline systems with domain-speciﬁc architectures and\\nsubstantial engineering, trained using intermediate retrieval supervision, which RAG does not require.\\n6', metadata={'page': 5, 'source': '/content/RAG.pdf'}), Document(page_content='documents for questions that are less likely to beneﬁt from retrieval, suggesting that null document\\nmechanisms may not be necessary for RAG.\\nG Parameters\\nOur RAG models contain the trainable parameters for the BERT-base query and document encoder of\\nDPR, with 110M parameters each (although we do not train the document encoder ourselves) and\\n406M trainable parameters from BART-large, 406M parameters, making a total of 626M trainable\\n18', metadata={'page': 17, 'source': '/content/RAG.pdf'}), Document(page_content='cases for NQ, where an extractive model would score 0%.\\n4.2 Abstractive Question Answering\\nAs shown in Table 2, RAG-Sequence outperforms BART on Open MS-MARCO NLG by 2.6 Bleu\\npoints and 2.6 Rouge-L points. RAG approaches state-of-the-art model performance, which is\\nimpressive given that (i) those models access gold passages with speciﬁc information required to\\ngenerate the reference answer , (ii) many questions are unanswerable without the gold passages, and\\n(iii) not all questions are answerable from Wikipedia alone. Table 3 shows some generated answers\\nfrom our models. Qualitatively, we ﬁnd that RAG models hallucinate less and generate factually\\ncorrect text more often than BART. Later, we also show that RAG generations are more diverse than\\nBART generations (see §4.5).\\n4.3 Jeopardy Question Generation\\nTable 2 shows that RAG-Token performs better than RAG-Sequence on Jeopardy question generation,', metadata={'page': 5, 'source': '/content/RAG.pdf'}), Document(page_content='Broader Impact\\nThis work offers several positive societal beneﬁts over previous work: the fact that it is more\\nstrongly grounded in real factual knowledge (in this case Wikipedia) makes it “hallucinate” less\\nwith generations that are more factual, and offers more control and interpretability. RAG could be\\nemployed in a wide variety of scenarios with direct beneﬁt to society, for example by endowing it\\nwith a medical index and asking it open-domain questions on that topic, or by helping people be more\\neffective at their jobs.\\nWith these advantages also come potential downsides: Wikipedia, or any potential external knowledge\\nsource, will probably never be entirely factual and completely devoid of bias. Since RAG can be\\nemployed as a language model, similar concerns as for GPT-2 [ 50] are valid here, although arguably\\nto a lesser extent, including that it might be used to generate abuse, faked or misleading content in', metadata={'page': 9, 'source': '/content/RAG.pdf'})]\n", "Answer:\n", "RAG (Retrieval-Augmented Generation) is a system that uses a combination of retrieval and generation to answer questions. It is a language model that can generate text based on a query and a set of documents. RAG is trained on a large corpus of text and can generate text that is more factual and less likely to hallucinate than other language models. It is also more diverse than other language models. RAG can be employed in a wide variety of scenarios\n"]}]}, {"cell_type": "code", "source": ["print(rag_chain.invoke(\"How does the RAG model differ from traditional language generation models?\"))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XQbbKG2FvpNB", "outputId": "acf66b66-d104-430a-d8a2-7a3a0c4aa28c"}, "execution_count": 37, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Human: You are an assistant for question-answering tasks.\n", "Use the following pieces of retrieved context to answer the question.\n", "If you don't know the answer, just say that you don't know.\n", "Use ten sentences maximum and keep the answer concise.\n", "Question: How does the RAG model differ from traditional language generation models?\n", "Context: [Document(page_content='cases for NQ, where an extractive model would score 0%.\\n4.2 Abstractive Question Answering\\nAs shown in Table 2, RAG-Sequence outperforms BART on Open MS-MARCO NLG by 2.6 Bleu\\npoints and 2.6 Rouge-L points. RAG approaches state-of-the-art model performance, which is\\nimpressive given that (i) those models access gold passages with speciﬁc information required to\\ngenerate the reference answer , (ii) many questions are unanswerable without the gold passages, and\\n(iii) not all questions are answerable from Wikipedia alone. Table 3 shows some generated answers\\nfrom our models. Qualitatively, we ﬁnd that RAG models hallucinate less and generate factually\\ncorrect text more often than BART. Later, we also show that RAG generations are more diverse than\\nBART generations (see §4.5).\\n4.3 Jeopardy Question Generation\\nTable 2 shows that RAG-Token performs better than RAG-Sequence on Jeopardy question generation,', metadata={'page': 5, 'source': '/content/RAG.pdf'}), Document(page_content='text generation. To test RAG’s natural language generation (NLG) in a knowledge-intensive setting,\\nwe use the MSMARCO NLG task v2.1 [ 43]. The task consists of questions, ten gold passages\\nretrieved from a search engine for each question, and a full sentence answer annotated from the\\nretrieved passages. We do not use the supplied passages, only the questions and answers, to treat\\n4', metadata={'page': 3, 'source': '/content/RAG.pdf'}), Document(page_content='top K documents are retrieved using the retriever, and the generator produces the output sequence\\nprobability for each document, which are then marginalized,\\npRAG-Sequence (y|x)≈∑\\nz∈top-k(p(·|x))pη(z|x)pθ(y|x,z) =∑\\nz∈top-k(p(·|x))pη(z|x)N∏\\nipθ(yi|x,z,y 1:i−1)\\nRAG-Token Model In the RAG-Token model we can draw a different latent document for each\\ntarget token and marginalize accordingly. This allows the generator to choose content from several\\ndocuments when producing an answer. Concretely, the top K documents are retrieved using the\\nretriever, and then the generator produces a distribution for the next output token for each document,\\nbefore marginalizing, and repeating the process with the following output token, Formally, we deﬁne:\\npRAG-Token (y|x)≈N∏\\ni∑\\nz∈top-k(p(·|x))pη(z|x)pθ(yi|x,z,y 1:i−1)\\nFinally, we note that RAG can be used for sequence classiﬁcation tasks by considering the target class', metadata={'page': 2, 'source': '/content/RAG.pdf'}), Document(page_content='total ngrams generated by different models. Table 5 shows that RAG-Sequence’s generations are\\nmore diverse than RAG-Token’s, and both are signiﬁcantly more diverse than BART without needing\\nany diversity-promoting decoding.\\nRetrieval Ablations A key feature of RAG is learning to retrieve relevant information for the task.\\nTo assess the effectiveness of the retrieval mechanism, we run ablations where we freeze the retriever\\nduring training. As shown in Table 6, learned retrieval improves results for all tasks.\\nWe compare RAG’s dense retriever to a word overlap-based BM25 retriever [ 53]. Here, we replace\\nRAG’s retriever with a ﬁxed BM25 system, and use BM25 retrieval scores as logits when calculating\\np(z|x). Table 6 shows the results. For FEVER, BM25 performs best, perhaps since FEVER claims are\\nheavily entity-centric and thus well-suited for word overlap-based retrieval. Differentiable retrieval\\nimproves results on all other tasks, especially for Open-Domain QA, where it is crucial.', metadata={'page': 6, 'source': '/content/RAG.pdf'})]\n", "Answer:\n", "The RAG model differs from traditional language generation models in that it uses a retrieval mechanism to access relevant information for the task. This allows it to generate factually correct text more often and hallucinate less than traditional models. Additionally, the RAG model can choose content from several documents when producing an answer, making its generations more diverse than traditional models.\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "gHjbemJtwmpq"}, "execution_count": null, "outputs": []}]}