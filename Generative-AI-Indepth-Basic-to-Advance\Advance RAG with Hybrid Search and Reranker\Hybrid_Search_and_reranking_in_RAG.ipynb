{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU", "widgets": {"application/vnd.jupyter.widget-state+json": {"1a3922c925d243fe825c2fdffc1ac440": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_848a9e20a5ff46329ac18f0f168a5d52", "IPY_MODEL_3c0f9911a51648cb8be3aaf49a806575", "IPY_MODEL_3f634bcca28549c8b922c73c7b475d91"], "layout": "IPY_MODEL_25ddfbae30f74ca6b5baf5cc1d94bcb1"}}, "848a9e20a5ff46329ac18f0f168a5d52": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_de412a1000a94bea8707e1cdc8d805b7", "placeholder": "​", "style": "IPY_MODEL_65283aca47324d5b917ba33f61e2f240", "value": "Loading checkpoint shards: 100%"}}, "3c0f9911a51648cb8be3aaf49a806575": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7a27e7b7ea6045a7a855237fd2a009e8", "max": 8, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e85f3538253c482eb76e42e6341abb83", "value": 8}}, "3f634bcca28549c8b922c73c7b475d91": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_791e2040d86848d6be8fbc486e8ab8b5", "placeholder": "​", "style": "IPY_MODEL_201266a8824041118a32f623036eb633", "value": " 8/8 [01:13&lt;00:00,  8.20s/it]"}}, "25ddfbae30f74ca6b5baf5cc1d94bcb1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "de412a1000a94bea8707e1cdc8d805b7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "65283aca47324d5b917ba33f61e2f240": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7a27e7b7ea6045a7a855237fd2a009e8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e85f3538253c482eb76e42e6341abb83": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "791e2040d86848d6be8fbc486e8ab8b5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "201266a8824041118a32f623036eb633": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/sunnysavita10/Indepth-GENAI/blob/main/Hybrid_Search_and_reranking_in_RAG.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["https://s4ds.org/\n", "\n", "https://www.icdmai.org/\n"], "metadata": {"id": "ZHlE17nUjXnp"}}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qmp_SaX69q18", "outputId": "63596de4-d1d9-4d78-cf94-7586f314ec44"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting weaviate-client\n", "  Downloading weaviate_client-4.6.3-py3-none-any.whl (324 kB)\n", "\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/324.9 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[91m━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[91m╸\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m194.6/324.9 kB\u001b[0m \u001b[31m5.7 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m324.9/324.9 kB\u001b[0m \u001b[31m5.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: requests<3.0.0,>=2.30.0 in /usr/local/lib/python3.10/dist-packages (from weaviate-client) (2.31.0)\n", "Collecting httpx<=0.27.0,>=0.25.0 (from weaviate-client)\n", "  Downloading httpx-0.27.0-py3-none-any.whl (75 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m4.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting validators==0.28.1 (from weaviate-client)\n", "  Downloading validators-0.28.1-py3-none-any.whl (39 kB)\n", "Collecting authlib<2.0.0,>=1.2.1 (from weaviate-client)\n", "  Downloading Authlib-1.3.0-py2.py3-none-any.whl (223 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m223.7/223.7 kB\u001b[0m \u001b[31m9.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pydantic<3.0.0,>=2.5.0 in /usr/local/lib/python3.10/dist-packages (from weaviate-client) (2.7.1)\n", "Requirement already satisfied: grpcio<2.0.0,>=1.57.0 in /usr/local/lib/python3.10/dist-packages (from weaviate-client) (1.64.0)\n", "Collecting grpcio-tools<2.0.0,>=1.57.0 (from weaviate-client)\n", "  Downloading grpcio_tools-1.64.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.3 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.3/2.3 MB\u001b[0m \u001b[31m16.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting grpcio-health-checking<2.0.0,>=1.57.0 (from weaviate-client)\n", "  Downloading grpcio_health_checking-1.64.0-py3-none-any.whl (18 kB)\n", "Requirement already satisfied: cryptography in /usr/local/lib/python3.10/dist-packages (from authlib<2.0.0,>=1.2.1->weaviate-client) (42.0.7)\n", "Collecting protobuf<6.0dev,>=5.26.1 (from grpcio-health-checking<2.0.0,>=1.57.0->weaviate-client)\n", "  Downloading protobuf-5.27.0-cp38-abi3-manylinux2014_x86_64.whl (309 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m309.2/309.2 kB\u001b[0m \u001b[31m23.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: setuptools in /usr/local/lib/python3.10/dist-packages (from grpcio-tools<2.0.0,>=1.57.0->weaviate-client) (67.7.2)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx<=0.27.0,>=0.25.0->weaviate-client) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx<=0.27.0,>=0.25.0->weaviate-client) (2024.2.2)\n", "Collecting httpcore==1.* (from httpx<=0.27.0,>=0.25.0->weaviate-client)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m12.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx<=0.27.0,>=0.25.0->weaviate-client) (3.7)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx<=0.27.0,>=0.25.0->weaviate-client) (1.3.1)\n", "Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<=0.27.0,>=0.25.0->weaviate-client)\n", "  Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m9.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0.0,>=2.5.0->weaviate-client) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.18.2 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0.0,>=2.5.0->weaviate-client) (2.18.2)\n", "Requirement already satisfied: typing-extensions>=4.6.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0.0,>=2.5.0->weaviate-client) (4.11.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.30.0->weaviate-client) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.30.0->weaviate-client) (2.0.7)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx<=0.27.0,>=0.25.0->weaviate-client) (1.2.1)\n", "Requirement already satisfied: cffi>=1.12 in /usr/local/lib/python3.10/dist-packages (from cryptography->authlib<2.0.0,>=1.2.1->weaviate-client) (1.16.0)\n", "Requirement already satisfied: pycparser in /usr/local/lib/python3.10/dist-packages (from cffi>=1.12->cryptography->authlib<2.0.0,>=1.2.1->weaviate-client) (2.22)\n", "Installing collected packages: validators, protobuf, h11, httpcore, grpcio-tools, grpcio-health-checking, httpx, authlib, weaviate-client\n", "  Attempting uninstall: protobuf\n", "    Found existing installation: protobuf 3.20.3\n", "    Uninstalling protobuf-3.20.3:\n", "      Successfully uninstalled protobuf-3.20.3\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "cudf-cu12 24.4.1 requires protobuf<5,>=3.20, but you have protobuf 5.27.0 which is incompatible.\n", "google-ai-generativelanguage 0.6.4 requires protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5, but you have protobuf 5.27.0 which is incompatible.\n", "google-api-core 2.11.1 requires protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0.dev0,>=3.19.5, but you have protobuf 5.27.0 which is incompatible.\n", "google-cloud-aiplatform 1.52.0 requires protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5, but you have protobuf 5.27.0 which is incompatible.\n", "google-cloud-bigquery-connection 1.12.1 requires protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5, but you have protobuf 5.27.0 which is incompatible.\n", "google-cloud-bigquery-storage 2.25.0 requires protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5, but you have protobuf 5.27.0 which is incompatible.\n", "google-cloud-datastore 2.15.2 requires protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5, but you have protobuf 5.27.0 which is incompatible.\n", "google-cloud-firestore 2.11.1 requires protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5, but you have protobuf 5.27.0 which is incompatible.\n", "google-cloud-functions 1.13.3 requires protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5, but you have protobuf 5.27.0 which is incompatible.\n", "google-cloud-iam 2.15.0 requires protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5, but you have protobuf 5.27.0 which is incompatible.\n", "google-cloud-language 2.13.3 requires protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5, but you have protobuf 5.27.0 which is incompatible.\n", "google-cloud-resource-manager 1.12.3 requires protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5, but you have protobuf 5.27.0 which is incompatible.\n", "google-cloud-translate 3.11.3 requires protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5, but you have protobuf 5.27.0 which is incompatible.\n", "googleapis-common-protos 1.63.0 requires protobuf!=3.20.0,!=3.20.1,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0.dev0,>=3.19.5, but you have protobuf 5.27.0 which is incompatible.\n", "grpc-google-iam-v1 0.13.0 requires protobuf!=3.20.0,!=3.20.1,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5, but you have protobuf 5.27.0 which is incompatible.\n", "proto-plus 1.23.0 requires protobuf<5.0.0dev,>=3.19.0, but you have protobuf 5.27.0 which is incompatible.\n", "tensorflow 2.15.0 requires protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.20.3, but you have protobuf 5.27.0 which is incompatible.\n", "tensorflow-metadata 1.15.0 requires protobuf<4.21,>=3.20.3; python_version < \"3.11\", but you have protobuf 5.27.0 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed authlib-1.3.0 grpcio-health-checking-1.64.0 grpcio-tools-1.64.0 h11-0.14.0 httpcore-1.0.5 httpx-0.27.0 protobuf-5.27.0 validators-0.28.1 weaviate-client-4.6.3\n"]}], "source": ["!pip install weaviate-client"]}, {"cell_type": "code", "source": ["!pip install langchain"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qQLSw3iJ_0RX", "outputId": "d628e74a-a8de-42d2-ed1a-522acb9c3f51"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain\n", "  Downloading langchain-0.2.1-py3-none-any.whl (973 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m973.5/973.5 kB\u001b[0m \u001b[31m16.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.0.30)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.9.5)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (4.0.3)\n", "Collecting langchain-core<0.3.0,>=0.2.0 (from langchain)\n", "  Downloading langchain_core-0.2.3-py3-none-any.whl (310 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m310.2/310.2 kB\u001b[0m \u001b[31m13.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-text-splitters<0.3.0,>=0.2.0 (from langchain)\n", "  Downloading langchain_text_splitters-0.2.0-py3-none-any.whl (23 kB)\n", "Collecting langsmith<0.2.0,>=0.1.17 (from langchain)\n", "  Downloading langsmith-0.1.65-py3-none-any.whl (124 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m124.3/124.3 kB\u001b[0m \u001b[31m15.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.25.2)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.7.1)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (8.3.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.9.4)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain-core<0.3.0,>=0.2.0->langchain)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Collecting packaging<24.0,>=23.2 (from langchain-core<0.3.0,>=0.2.0->langchain)\n", "  Downloading packaging-23.2-py3-none-any.whl (53 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m53.0/53.0 kB\u001b[0m \u001b[31m7.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hColl<PERSON>ting or<PERSON>son<4.0.0,>=3.9.14 (from langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading orjson-3.10.3-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (142 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m142.5/142.5 kB\u001b[0m \u001b[31m19.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.18.2 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (2.18.2)\n", "Requirement already satisfied: typing-extensions>=4.6.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (4.11.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2024.2.2)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.0.3)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.0->langchain)\n", "  Downloading jsonpointer-2.4-py2.py3-none-any.whl (7.8 kB)\n", "Installing collected packages: packaging, orjson, jsonpointer, jsonpatch, langsmith, langchain-core, langchain-text-splitters, langchain\n", "  Attempting uninstall: packaging\n", "    Found existing installation: packaging 24.0\n", "    Uninstalling packaging-24.0:\n", "      Successfully uninstalled packaging-24.0\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "cudf-cu12 24.4.1 requires protobuf<5,>=3.20, but you have protobuf 5.27.0 which is incompatible.\n", "google-cloud-aiplatform 1.52.0 requires protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5, but you have protobuf 5.27.0 which is incompatible.\n", "tensorflow 2.15.0 requires protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.20.3, but you have protobuf 5.27.0 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed jsonpatch-1.33 jsonpointer-2.4 langchain-0.2.1 langchain-core-0.2.3 langchain-text-splitters-0.2.0 langsmith-0.1.65 orjson-3.10.3 packaging-23.2\n"]}]}, {"cell_type": "code", "source": ["!pip install -U langchain-community"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4lpn398P__vR", "outputId": "2f217e89-f2ad-4b53-9968-dfa0d3c857ef"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain-community\n", "  Downloading langchain_community-0.2.1-py3-none-any.whl (2.1 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.1/2.1 MB\u001b[0m \u001b[31m16.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (2.0.30)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (3.9.5)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain-community)\n", "  Downloading dataclasses_json-0.6.6-py3-none-any.whl (28 kB)\n", "Requirement already satisfied: langchain<0.3.0,>=0.2.0 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.2.1)\n", "Requirement already satisfied: langchain-core<0.3.0,>=0.2.0 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.2.3)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.1.65)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (1.25.2)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (8.3.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (4.0.3)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading marshmallow-3.21.2-py3-none-any.whl (49 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.3/49.3 kB\u001b[0m \u001b[31m8.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Requirement already satisfied: langchain-text-splitters<0.3.0,>=0.2.0 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.0->langchain-community) (0.2.0)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.0->langchain-community) (2.7.1)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.0->langchain-community) (1.33)\n", "Requirement already satisfied: packaging<24.0,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.0->langchain-community) (23.2)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.0->langchain-community) (3.10.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (2024.2.2)\n", "Requirement already satisfied: typing-extensions>=4.6.0 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain-community) (4.11.0)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain-community) (3.0.3)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.0->langchain-community) (2.4)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.0->langchain-community) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.18.2 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.0->langchain-community) (2.18.2)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: mypy-extensions, marshmallow, typing-inspect, dataclasses-json, langchain-community\n", "Successfully installed dataclasses-json-0.6.6 langchain-community-0.2.1 marshmallow-3.21.2 mypy-extensions-1.0.0 typing-inspect-0.9.0\n"]}]}, {"cell_type": "code", "source": ["import weaviate"], "metadata": {"id": "RSik_tYq-JRN"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["WEAVIATE_CLUSTER=\"https://hybridsearch-ewd5zpr1.weaviate.network\"\n", "WEAVIATE_API_KEY=\"tzEkCjceWDAB5Kh3escJL0M26fbsEr39aLcC\""], "metadata": {"id": "M5rKS1Co-22r"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["WEAVIATE_URL = WEAVIATE_CLUSTER\n", "WEAVIATE_API_KEY = WEAVIATE_API_KEY"], "metadata": {"id": "ovLN44VY-6tU"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["HF_TOKEN=\"*************************************\""], "metadata": {"id": "Z93YcxMF_iCN"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["import os"], "metadata": {"id": "JUDJ74Ut_N-M"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["client = weaviate.Client(\n", "    url=WEAVIATE_URL, auth_client_secret=weaviate.AuthApiKey(WEAVIATE_API_KEY),\n", "    additional_headers={\n", "         \"X-HuggingFace-Api-Key\": HF_TOKEN\n", "    },\n", ")"], "metadata": {"id": "YFrBhzvM--rd"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["client.is_ready()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "LQJQDj68Cy4J", "outputId": "ccf1aad1-8ca1-4079-b284-2f60397d0cd1"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["True"]}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "code", "source": ["client.schema.get()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6ouOrLG2B9wj", "outputId": "3038912b-d5cb-4714-9803-6706392ca7cf"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'classes': [{'class': 'RAG',\n", "   'description': 'Documents for RAG',\n", "   'invertedIndexConfig': {'bm25': {'b': 0.75, 'k1': 1.2},\n", "    'cleanupIntervalSeconds': 60,\n", "    'stopwords': {'additions': None, 'preset': 'en', 'removals': None}},\n", "   'moduleConfig': {'text2vec-huggingface': {'model': 'sentence-transformers/all-MiniLM-L6-v2',\n", "     'type': 'text',\n", "     'vectorizeClassName': True}},\n", "   'multiTenancyConfig': {'enabled': False},\n", "   'properties': [{'dataType': ['text'],\n", "     'description': 'The content of the paragraph',\n", "     'indexFilterable': True,\n", "     'indexSearchable': True,\n", "     'moduleConfig': {'text2vec-huggingface': {'skip': <PERSON><PERSON><PERSON>,\n", "       'vectorizePropertyName': False}},\n", "     'name': 'content',\n", "     'tokenization': 'word'},\n", "    {'dataType': ['text'],\n", "     'description': \"This property was generated by Weaviate's auto-schema feature on Fri May 31 08:08:01 2024\",\n", "     'indexFilterable': True,\n", "     'indexSearchable': True,\n", "     'moduleConfig': {'text2vec-huggingface': {'skip': <PERSON><PERSON><PERSON>,\n", "       'vectorizePropertyName': False}},\n", "     'name': 'source',\n", "     'tokenization': 'word'},\n", "    {'dataType': ['number'],\n", "     'description': \"This property was generated by Weaviate's auto-schema feature on Fri May 31 08:08:01 2024\",\n", "     'indexFilterable': True,\n", "     'indexSearchable': <PERSON><PERSON><PERSON>,\n", "     'moduleConfig': {'text2vec-huggingface': {'skip': <PERSON><PERSON><PERSON>,\n", "       'vectorizePropertyName': False}},\n", "     'name': 'page'}],\n", "   'replicationConfig': {'factor': 1},\n", "   'shardingConfig': {'virtualPerPhysical': 128,\n", "    'desiredCount': 1,\n", "    'actualCount': 1,\n", "    'desiredVirtualCount': 128,\n", "    'actualVirtualCount': 128,\n", "    'key': '_id',\n", "    'strategy': 'hash',\n", "    'function': 'murmur3'},\n", "   'vectorIndexConfig': {'skip': False,\n", "    'cleanupIntervalSeconds': 300,\n", "    'maxConnections': 64,\n", "    'efConstruction': 128,\n", "    'ef': -1,\n", "    'dynamicEfMin': 100,\n", "    'dynamicEfMax': 500,\n", "    'dynamicEfFactor': 8,\n", "    'vectorCacheMaxObjects': 1000000000000,\n", "    'flatSearchCutoff': 40000,\n", "    'distance': 'cosine',\n", "    'pq': {'enabled': False,\n", "     'bitCompression': <PERSON><PERSON><PERSON>,\n", "     'segments': 0,\n", "     'centroids': 256,\n", "     'trainingLimit': 100000,\n", "     'encoder': {'type': 'kmeans', 'distribution': 'log-normal'}},\n", "    'bq': {'enabled': False}},\n", "   'vectorIndexType': 'hnsw',\n", "   'vectorizer': 'text2vec-huggingface'}]}"]}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "code", "source": ["client.schema.delete_all()"], "metadata": {"id": "9zR5jAGHC3bS"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["schema = {\n", "    \"classes\": [\n", "        {\n", "            \"class\": \"RAG\",\n", "            \"description\": \"Documents for RAG\",\n", "            \"vectorizer\": \"text2vec-huggingface\",\n", "            \"moduleConfig\": {\"text2vec-huggingface\": {\"model\": \"sentence-transformers/all-MiniLM-L6-v2\", \"type\": \"text\"}},\n", "            \"properties\": [\n", "                {\n", "                    \"dataType\": [\"text\"],\n", "                    \"description\": \"The content of the paragraph\",\n", "                    \"moduleConfig\": {\n", "                        \"text2vec-huggingface\": {\n", "                            \"skip\": <PERSON><PERSON><PERSON>,\n", "                            \"vectorizePropertyName\": <PERSON><PERSON><PERSON>,\n", "                        }\n", "                    },\n", "                    \"name\": \"content\",\n", "                },\n", "            ],\n", "        },\n", "    ]\n", "}\n"], "metadata": {"id": "7l8nTgbRDCWt"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["client.schema.create(schema)"], "metadata": {"id": "XxlykBOsD4oW"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["client.schema.get()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "boKhfW7xD8je", "outputId": "6dec38eb-ab67-428a-c5fa-79849de612f5"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'classes': [{'class': 'RAG',\n", "   'description': 'Documents for RAG',\n", "   'invertedIndexConfig': {'bm25': {'b': 0.75, 'k1': 1.2},\n", "    'cleanupIntervalSeconds': 60,\n", "    'stopwords': {'additions': None, 'preset': 'en', 'removals': None}},\n", "   'moduleConfig': {'text2vec-huggingface': {'model': 'sentence-transformers/all-MiniLM-L6-v2',\n", "     'type': 'text',\n", "     'vectorizeClassName': True}},\n", "   'multiTenancyConfig': {'enabled': False},\n", "   'properties': [{'dataType': ['text'],\n", "     'description': 'The content of the paragraph',\n", "     'indexFilterable': True,\n", "     'indexSearchable': True,\n", "     'moduleConfig': {'text2vec-huggingface': {'skip': <PERSON><PERSON><PERSON>,\n", "       'vectorizePropertyName': False}},\n", "     'name': 'content',\n", "     'tokenization': 'word'},\n", "    {'dataType': ['text'],\n", "     'description': \"This property was generated by Weaviate's auto-schema feature on Fri May 31 08:08:01 2024\",\n", "     'indexFilterable': True,\n", "     'indexSearchable': True,\n", "     'moduleConfig': {'text2vec-huggingface': {'skip': <PERSON><PERSON><PERSON>,\n", "       'vectorizePropertyName': False}},\n", "     'name': 'source',\n", "     'tokenization': 'word'},\n", "    {'dataType': ['number'],\n", "     'description': \"This property was generated by Weaviate's auto-schema feature on Fri May 31 08:08:01 2024\",\n", "     'indexFilterable': True,\n", "     'indexSearchable': <PERSON><PERSON><PERSON>,\n", "     'moduleConfig': {'text2vec-huggingface': {'skip': <PERSON><PERSON><PERSON>,\n", "       'vectorizePropertyName': False}},\n", "     'name': 'page'}],\n", "   'replicationConfig': {'factor': 1},\n", "   'shardingConfig': {'virtualPerPhysical': 128,\n", "    'desiredCount': 1,\n", "    'actualCount': 1,\n", "    'desiredVirtualCount': 128,\n", "    'actualVirtualCount': 128,\n", "    'key': '_id',\n", "    'strategy': 'hash',\n", "    'function': 'murmur3'},\n", "   'vectorIndexConfig': {'skip': False,\n", "    'cleanupIntervalSeconds': 300,\n", "    'maxConnections': 64,\n", "    'efConstruction': 128,\n", "    'ef': -1,\n", "    'dynamicEfMin': 100,\n", "    'dynamicEfMax': 500,\n", "    'dynamicEfFactor': 8,\n", "    'vectorCacheMaxObjects': 1000000000000,\n", "    'flatSearchCutoff': 40000,\n", "    'distance': 'cosine',\n", "    'pq': {'enabled': False,\n", "     'bitCompression': <PERSON><PERSON><PERSON>,\n", "     'segments': 0,\n", "     'centroids': 256,\n", "     'trainingLimit': 100000,\n", "     'encoder': {'type': 'kmeans', 'distribution': 'log-normal'}},\n", "    'bq': {'enabled': False}},\n", "   'vectorIndexType': 'hnsw',\n", "   'vectorizer': 'text2vec-huggingface'}]}"]}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "code", "source": ["from langchain.retrievers.weaviate_hybrid_search import WeaviateHybridSearchRetriever"], "metadata": {"id": "9fYFxszF_lTL"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["retriever = WeaviateHybridSearchRetriever(\n", "    alpha = 0.5,               # defaults to 0.5, which is equal weighting between keyword and semantic search\n", "    client = client,           # keyword arguments to pass to the Weaviate client\n", "    index_name = \"RAG\",  # The name of the index to use\n", "    text_key = \"content\",         # The name of the text key to use\n", "    attributes = [], # The attributes to return in the results\n", "    create_schema_if_missing=True,\n", ")"], "metadata": {"id": "xDD_FAKZ_sZK"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["model_name = \"HuggingFaceH4/zephyr-7b-beta\""], "metadata": {"id": "RJLYAGHbE1Z5"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["!pip install bitsandbytes"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1w6ml1DsEv-q", "outputId": "235602d0-14da-4ebb-fd21-fe314ed872c5"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: bitsandbytes in /usr/local/lib/python3.10/dist-packages (0.43.1)\n", "Requirement already satisfied: torch in /usr/local/lib/python3.10/dist-packages (from bitsandbytes) (2.3.0+cu121)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from bitsandbytes) (1.25.2)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (3.14.0)\n", "Requirement already satisfied: typing-extensions>=4.8.0 in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (4.11.0)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (1.12)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (3.3)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (3.1.4)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (2023.6.0)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (12.1.105)\n", "Requirement already satisfied: nvidia-cudnn-cu12==8.9.2.26 in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (8.9.2.26)\n", "Requirement already satisfied: nvidia-cublas-cu12==12.1.3.1 in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (12.1.3.1)\n", "Requirement already satisfied: nvidia-cufft-cu12==11.0.2.54 in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (11.0.2.54)\n", "Requirement already satisfied: nvidia-curand-cu12==10.3.2.106 in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (10.3.2.106)\n", "Requirement already satisfied: nvidia-cusolver-cu12==********** in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (**********)\n", "Requirement already satisfied: nvidia-cusparse-cu12==12.1.0.106 in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (12.1.0.106)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.20.5 in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (2.20.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (12.1.105)\n", "Requirement already satisfied: triton==2.3.0 in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (2.3.0)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12 in /usr/local/lib/python3.10/dist-packages (from nvidia-cusolver-cu12==**********->torch->bitsandbytes) (12.5.40)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch->bitsandbytes) (2.1.5)\n", "Requirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->torch->bitsandbytes) (1.3.0)\n"]}]}, {"cell_type": "code", "source": ["!pip install accelerate"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "LtJsxhOmEzWX", "outputId": "ceb6003d-d09d-4e19-90fe-beb540912dc7"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: accelerate in /usr/local/lib/python3.10/dist-packages (0.30.1)\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.10/dist-packages (from accelerate) (1.25.2)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.10/dist-packages (from accelerate) (23.2)\n", "Requirement already satisfied: psutil in /usr/local/lib/python3.10/dist-packages (from accelerate) (5.9.5)\n", "Requirement already satisfied: pyyaml in /usr/local/lib/python3.10/dist-packages (from accelerate) (6.0.1)\n", "Requirement already satisfied: torch>=1.10.0 in /usr/local/lib/python3.10/dist-packages (from accelerate) (2.3.0+cu121)\n", "Requirement already satisfied: huggingface-hub in /usr/local/lib/python3.10/dist-packages (from accelerate) (0.23.1)\n", "Requirement already satisfied: safetensors>=0.3.1 in /usr/local/lib/python3.10/dist-packages (from accelerate) (0.4.3)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (3.14.0)\n", "Requirement already satisfied: typing-extensions>=4.8.0 in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (4.11.0)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (1.12)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (3.3)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (3.1.4)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (2023.6.0)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (12.1.105)\n", "Requirement already satisfied: nvidia-cudnn-cu12==8.9.2.26 in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (8.9.2.26)\n", "Requirement already satisfied: nvidia-cublas-cu12==12.1.3.1 in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (12.1.3.1)\n", "Requirement already satisfied: nvidia-cufft-cu12==11.0.2.54 in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (11.0.2.54)\n", "Requirement already satisfied: nvidia-curand-cu12==10.3.2.106 in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (10.3.2.106)\n", "Requirement already satisfied: nvidia-cusolver-cu12==********** in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (**********)\n", "Requirement already satisfied: nvidia-cusparse-cu12==12.1.0.106 in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (12.1.0.106)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.20.5 in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (2.20.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (12.1.105)\n", "Requirement already satisfied: triton==2.3.0 in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (2.3.0)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12 in /usr/local/lib/python3.10/dist-packages (from nvidia-cusolver-cu12==**********->torch>=1.10.0->accelerate) (12.5.40)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from huggingface-hub->accelerate) (2.31.0)\n", "Requirement already satisfied: tqdm>=4.42.1 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub->accelerate) (4.66.4)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch>=1.10.0->accelerate) (2.1.5)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub->accelerate) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub->accelerate) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub->accelerate) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub->accelerate) (2024.2.2)\n", "Requirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->torch>=1.10.0->accelerate) (1.3.0)\n"]}]}, {"cell_type": "code", "source": ["import torch\n", "from transformers import ( AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig, pipeline, )\n", "from langchain import HuggingFacePipeline"], "metadata": {"id": "7YcsnAveEiFy"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# function for loading 4-bit quantized model\n", "def load_quantized_model(model_name: str):\n", "    \"\"\"\n", "    model_name: Name or path of the model to be loaded.\n", "    return: Loaded quantized model.\n", "    \"\"\"\n", "    bnb_config = BitsAndBytesConfig(\n", "        load_in_4bit=True,\n", "        bnb_4bit_use_double_quant=True,\n", "        bnb_4bit_quant_type=\"nf4\",\n", "        bnb_4bit_compute_dtype=torch.bfloat16,\n", "        low_cpu_mem_usage=True\n", "    )\n", "\n", "    model = AutoModelForCausalLM.from_pretrained(\n", "        model_name,\n", "        torch_dtype=torch.bfloat16,\n", "        quantization_config=bnb_config,\n", "    )\n", "    return model"], "metadata": {"id": "Yflg19-qEiJs"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# initializing tokenizer\n", "def initialize_tokenizer(model_name: str):\n", "    \"\"\"\n", "    model_name: Name or path of the model for tokenizer initialization.\n", "    return: Initialized tokenizer.\n", "    \"\"\"\n", "    tokenizer = AutoTokenizer.from_pretrained(model_name, return_token_type_ids=False)\n", "    tokenizer.bos_token_id = 1  # Set beginning of sentence token id\n", "    return tokenizer"], "metadata": {"id": "Pfdzn1ukEiMd"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["tokenizer = initialize_tokenizer(model_name)"], "metadata": {"id": "a8UgT93sEiQK"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["model = load_quantized_model(model_name)"], "metadata": {"id": "Csv9lG6cErbb", "colab": {"base_uri": "https://localhost:8080/", "height": 104, "referenced_widgets": ["1a3922c925d243fe825c2fdffc1ac440", "848a9e20a5ff46329ac18f0f168a5d52", "3c0f9911a51648cb8be3aaf49a806575", "3f634bcca28549c8b922c73c7b475d91", "25ddfbae30f74ca6b5baf5cc1d94bcb1", "de412a1000a94bea8707e1cdc8d805b7", "65283aca47324d5b917ba33f61e2f240", "7a27e7b7ea6045a7a855237fd2a009e8", "e85f3538253c482eb76e42e6341abb83", "791e2040d86848d6be8fbc486e8ab8b5", "201266a8824041118a32f623036eb633"]}, "outputId": "1984deee-8c48-49af-bd66-9ee1d3018221"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["Unused kwargs: ['low_cpu_mem_usage']. These kwargs are not used in <class 'transformers.utils.quantization_config.BitsAndBytesConfig'>.\n", "`low_cpu_mem_usage` was None, now set to True since model is quantized.\n"]}, {"output_type": "display_data", "data": {"text/plain": ["Loading checkpoint shards:   0%|          | 0/8 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "1a3922c925d243fe825c2fdffc1ac440"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["pipeline = pipeline(\n", "    \"text-generation\",\n", "    model=model,\n", "    tokenizer=tokenizer,\n", "    use_cache=True,\n", "    device_map=\"auto\",\n", "    #max_length=2048,\n", "    do_sample=True,\n", "    top_k=5,\n", "    max_new_tokens=100,\n", "    num_return_sequences=1,\n", "    eos_token_id=tokenizer.eos_token_id,\n", "    pad_token_id=tokenizer.pad_token_id,\n", ")"], "metadata": {"id": "IplrZgxvEreX", "colab": {"base_uri": "https://localhost:8080/", "height": 446}, "outputId": "82543b1a-a0bf-4693-e975-98fce166013b"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["Truncation was not explicitly activated but `max_length` is provided a specific value, please use `truncation=True` to explicitly truncate examples to max length. Defaulting to 'longest_first' truncation strategy. If you encode pairs of sequences (GLUE-style) with the tokenizer you can select this strategy more precisely by providing a specific strategy to `truncation`.\n"]}, {"output_type": "error", "ename": "ValueError", "evalue": "The following `model_kwargs` are not used by the model: ['model', 'device_map'] (note: typos in the generate arguments will also show up in this list)", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-98-ff15e1b5d91e>\u001b[0m in \u001b[0;36m<cell line: 1>\u001b[0;34m()\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m pipeline = pipeline(\n\u001b[0m\u001b[1;32m      2\u001b[0m     \u001b[0;34m\"text-generation\"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      3\u001b[0m     \u001b[0mmodel\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mmodel\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m     \u001b[0mtokenizer\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mtokenizer\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m     \u001b[0muse_cache\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/transformers/pipelines/text_generation.py\u001b[0m in \u001b[0;36m__call__\u001b[0;34m(self, text_inputs, **kwargs)\u001b[0m\n\u001b[1;32m    261\u001b[0m                 \u001b[0;32mreturn\u001b[0m \u001b[0msuper\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__call__\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mchats\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    262\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 263\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0msuper\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__call__\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mtext_inputs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    264\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    265\u001b[0m     def preprocess(\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/transformers/pipelines/base.py\u001b[0m in \u001b[0;36m__call__\u001b[0;34m(self, inputs, num_workers, batch_size, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1241\u001b[0m             )\n\u001b[1;32m   1242\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1243\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mrun_single\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mpreprocess_params\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mforward_params\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mpostprocess_params\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1244\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1245\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mrun_multi\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0minputs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mpreprocess_params\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mforward_params\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mpostprocess_params\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/transformers/pipelines/base.py\u001b[0m in \u001b[0;36mrun_single\u001b[0;34m(self, inputs, preprocess_params, forward_params, postprocess_params)\u001b[0m\n\u001b[1;32m   1248\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mrun_single\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0minputs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mpreprocess_params\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mforward_params\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mpostprocess_params\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1249\u001b[0m         \u001b[0mmodel_inputs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpreprocess\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mpreprocess_params\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1250\u001b[0;31m         \u001b[0mmodel_outputs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mforward\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmodel_inputs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mforward_params\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1251\u001b[0m         \u001b[0moutputs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpostprocess\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmodel_outputs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mpostprocess_params\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1252\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0moutputs\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/transformers/pipelines/base.py\u001b[0m in \u001b[0;36mforward\u001b[0;34m(self, model_inputs, **forward_params)\u001b[0m\n\u001b[1;32m   1148\u001b[0m                 \u001b[0;32mwith\u001b[0m \u001b[0minference_context\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1149\u001b[0m                     \u001b[0mmodel_inputs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_ensure_tensor_on_device\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmodel_inputs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdevice\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdevice\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1150\u001b[0;31m                     \u001b[0mmodel_outputs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_forward\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmodel_inputs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mforward_params\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1151\u001b[0m                     \u001b[0mmodel_outputs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_ensure_tensor_on_device\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmodel_outputs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdevice\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mtorch\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdevice\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"cpu\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1152\u001b[0m             \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/transformers/pipelines/text_generation.py\u001b[0m in \u001b[0;36m_forward\u001b[0;34m(self, model_inputs, **generate_kwargs)\u001b[0m\n\u001b[1;32m    348\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    349\u001b[0m         \u001b[0;31m# BS x SL\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 350\u001b[0;31m         \u001b[0mgenerated_sequence\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmodel\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mgenerate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minput_ids\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0minput_ids\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mattention_mask\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mattention_mask\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mgenerate_kwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    351\u001b[0m         \u001b[0mout_b\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mgenerated_sequence\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mshape\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    352\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mframework\u001b[0m \u001b[0;34m==\u001b[0m \u001b[0;34m\"pt\"\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/torch/utils/_contextlib.py\u001b[0m in \u001b[0;36mdecorate_context\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    113\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mdecorate_context\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    114\u001b[0m         \u001b[0;32mwith\u001b[0m \u001b[0mctx_factory\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 115\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mfunc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    116\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    117\u001b[0m     \u001b[0;32mreturn\u001b[0m \u001b[0mdecorate_context\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/transformers/generation/utils.py\u001b[0m in \u001b[0;36mgenerate\u001b[0;34m(self, inputs, generation_config, logits_processor, stopping_criteria, prefix_allowed_tokens_fn, synced_gpus, assistant_model, streamer, negative_prompt_ids, negative_prompt_attention_mask, **kwargs)\u001b[0m\n\u001b[1;32m   1540\u001b[0m         \u001b[0mtok<PERSON><PERSON>\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mkwargs\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpop\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"tokenizer\"\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m)\u001b[0m  \u001b[0;31m# Pull this out first, we only use it for stopping criteria\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1541\u001b[0m         \u001b[0mgeneration_config\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmodel_kwargs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_prepare_generation_config\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mgeneration_config\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1542\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_validate_model_kwargs\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmodel_kwargs\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcopy\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1543\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1544\u001b[0m         \u001b[0;31m# 2. Set generation parameters if not already defined\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/transformers/generation/utils.py\u001b[0m in \u001b[0;36m_validate_model_kwargs\u001b[0;34m(self, model_kwargs)\u001b[0m\n\u001b[1;32m   1155\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1156\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0munused_model_args\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1157\u001b[0;31m             raise ValueError(\n\u001b[0m\u001b[1;32m   1158\u001b[0m                 \u001b[0;34mf\"The following `model_kwargs` are not used by the model: {unused_model_args} (note: typos in the\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1159\u001b[0m                 \u001b[0;34m\" generate arguments will also show up in this list)\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mValueError\u001b[0m: The following `model_kwargs` are not used by the model: ['model', 'device_map'] (note: typos in the generate arguments will also show up in this list)"]}]}, {"cell_type": "code", "source": ["llm = HuggingFacePipeline(pipeline=pipeline)"], "metadata": {"id": "Uo348jKvErhO"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["doc_path=\"/content/Retrieval-Augmented-Generation-for-NLP.pdf\""], "metadata": {"id": "uva-5Nkqpr8w"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["!pip install pypdf"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "BTNRvdSNp9jC", "outputId": "68d151fe-ac47-4e64-9d56-7cabd3fb2c50"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting pypdf\n", "  Downloading pypdf-4.2.0-py3-none-any.whl (290 kB)\n", "\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/290.4 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[91m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[90m╺\u001b[0m\u001b[90m━━━━━━━━\u001b[0m \u001b[32m225.3/290.4 kB\u001b[0m \u001b[31m6.5 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m290.4/290.4 kB\u001b[0m \u001b[31m5.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: typing_extensions>=4.0 in /usr/local/lib/python3.10/dist-packages (from pypdf) (4.11.0)\n", "Installing collected packages: pypdf\n", "Successfully installed pypdf-4.2.0\n"]}]}, {"cell_type": "code", "source": ["!pip install langchain_community"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ev8_SeQIp_4A", "outputId": "f4dc1edd-7f8d-4d60-da77-96284597c657"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: langchain_community in /usr/local/lib/python3.10/dist-packages (0.2.1)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (2.0.30)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (3.9.5)\n", "Requirement already satisfied: dataclasses-json<0.7,>=0.5.7 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (0.6.6)\n", "Requirement already satisfied: langchain<0.3.0,>=0.2.0 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (0.2.1)\n", "Requirement already satisfied: langchain-core<0.3.0,>=0.2.0 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (0.2.3)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (0.1.65)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (1.25.2)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (8.3.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (4.0.3)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json<0.7,>=0.5.7->langchain_community) (3.21.2)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json<0.7,>=0.5.7->langchain_community) (0.9.0)\n", "Requirement already satisfied: langchain-text-splitters<0.3.0,>=0.2.0 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.0->langchain_community) (0.2.0)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.0->langchain_community) (2.7.1)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.0->langchain_community) (1.33)\n", "Requirement already satisfied: packaging<24.0,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.0->langchain_community) (23.2)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.0->langchain_community) (3.10.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (2024.2.2)\n", "Requirement already satisfied: typing-extensions>=4.6.0 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain_community) (4.11.0)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain_community) (3.0.3)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.0->langchain_community) (2.4)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.0->langchain_community) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.18.2 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.0->langchain_community) (2.18.2)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain_community) (1.0.0)\n"]}]}, {"cell_type": "code", "source": ["from langchain_community.document_loaders import PyPDFLoader"], "metadata": {"id": "3n-7-Q<PERSON><PERSON>p_8x"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["loader = PyPDFLoader(doc_path)"], "metadata": {"id": "nhBRpl8dsHw6"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["docs = loader.load()"], "metadata": {"id": "xHegGUGssHzV"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["docs"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gpshkBhjvLlC", "outputId": "6fa66ef9-f60c-4e6a-ad16-0d1464f27246"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='Retrieval-Augmented Generation for\\nKnowledge-Intensive NLP Tasks\\n<PERSON><PERSON><PERSON>†‡, <PERSON> Perez⋆,\\<PERSON><PERSON><PERSON><PERSON><PERSON>†, <PERSON><PERSON><PERSON>†, <PERSON>†, <PERSON><PERSON>†, <PERSON>†,\\<PERSON><PERSON><PERSON>†, <PERSON><PERSON><PERSON><PERSON>†, <PERSON>†‡, <PERSON>†‡, <PERSON><PERSON><PERSON>†\\n†Facebook AI Research;‡University College London;⋆New York University;\\<EMAIL>\\nAbstract\\nLarge pre-trained language models have been shown to store factual knowledge\\nin their parameters, and achieve state-of-the-art results when ﬁne-tuned on down-\\nstream NLP tasks. However, their ability to access and precisely manipulate knowl-\\nedge is still limited, and hence on knowledge-intensive tasks, their performance\\nlags behind task-speciﬁc architectures. Additionally, providing provenance for their\\ndecisions and updating their world knowledge remain open research problems. Pre-\\ntrained models with a differentiable access mechanism to explicit non-parametric\\nmemory have so far been only investigated for extractive downstream tasks. We\\nexplore a general-purpose ﬁne-tuning recipe for retrieval-augmented generation\\n(RAG) — models which combine pre-trained parametric and non-parametric mem-\\nory for language generation. We introduce RAG models where the parametric\\nmemory is a pre-trained seq2seq model and the non-parametric memory is a dense\\nvector index of Wikipedia, accessed with a pre-trained neural retriever. We com-\\npare two RAG formulations, one which conditions on the same retrieved passages\\nacross the whole generated sequence, and another which can use different passages\\nper token. We ﬁne-tune and evaluate our models on a wide range of knowledge-\\nintensive NLP tasks and set the state of the art on three open domain QA tasks,\\noutperforming parametric seq2seq models and task-speciﬁc retrieve-and-extract\\narchitectures. For language generation tasks, we ﬁnd that RAG models generate\\nmore speciﬁc, diverse and factual language than a state-of-the-art parametric-only\\nseq2seq baseline.\\n1 Introduction\\nPre-trained neural language models have been shown to learn a substantial amount of in-depth knowl-\\nedge from data [ 47]. They can do so without any access to an external memory, as a parameterized\\nimplicit knowledge base [ 51,52]. While this development is exciting, such models do have down-\\nsides: They cannot easily expand or revise their memory, can’t straightforwardly provide insight into\\ntheir predictions, and may produce “hallucinations” [ 38]. Hybrid models that combine parametric\\nmemory with non-parametric (i.e., retrieval-based) memories [ 20,26,48] can address some of these\\nissues because knowledge can be directly revised and expanded, and accessed knowledge can be\\ninspected and interpreted. REALM [ 20] and ORQA [ 31], two recently introduced models that\\ncombine masked language models [ 8] with a differentiable retriever, have shown promising results,arXiv:2005.11401v4  [cs.CL]  12 Apr 2021', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 0}),\n", " Document(page_content='The\\tDivine\\nComedy\\t(x) qQuery\\nEncoder\\nq(x)\\nMIPS p θGenerator \\xa0pθ\\n(Parametric)\\nMargin-\\nalize\\nThis\\t14th\\tcentury\\twork\\nis\\tdivided\\tinto\\t3\\nsections:\\t\"Inferno\",\\n\"Purgatorio\"\\t&\\n\"Paradiso\"\\t\\t\\t\\t\\t\\t\\t\\t\\t (y)End-to-End Backprop through q and\\xa0 p θ\\nBarack\\tObama\\twas\\nborn\\tin\\tHawaii. (x)\\nFact V eriﬁcation: Fact Querysupports \\t(y)\\nQuestion GenerationFact V eriﬁcation:\\nLabel GenerationDocument\\nIndexDefine\\t\"middle\\tear\" (x)\\nQuestion Answering:\\nQuestion QueryThe\\tmiddle\\tear\\tincludes\\nthe\\ttympanic\\tcavity\\tand\\nthe\\tthree\\tossicles.\\t\\t (y)\\nQuestion Answering:\\nAnswer GenerationRetriever pη\\n(Non-Parametric)\\nz 4\\nz3\\nz2\\nz 1d(z)\\nJeopardy Question\\nGeneration:\\nAnswer QueryFigure 1: Overview of our approach. We combine a pre-trained retriever ( Query Encoder +Document\\nIndex ) with a pre-trained seq2seq model ( Generator ) and ﬁne-tune end-to-end. For query x, we use\\nMaximum Inner Product Search (MIPS) to ﬁnd the top-K documents zi. For ﬁnal prediction y, we\\ntreatzas a latent variable and marginalize over seq2seq predictions given different documents.\\nbut have only explored open-domain extractive question answering. Here, we bring hybrid parametric\\nand non-parametric memory to the “workhorse of NLP,” i.e. sequence-to-sequence (seq2seq) models.\\nWe endow pre-trained, parametric-memory generation models with a non-parametric memory through\\na general-purpose ﬁne-tuning approach which we refer to as retrieval-augmented generation (RAG).\\nWe build RAG models where the parametric memory is a pre-trained seq2seq transformer, and the\\nnon-parametric memory is a dense vector index of Wikipedia, accessed with a pre-trained neural\\nretriever. We combine these components in a probabilistic model trained end-to-end (Fig. 1). The\\nretriever (Dense Passage Retriever [ 26], henceforth DPR) provides latent documents conditioned on\\nthe input, and the seq2seq model (BART [ 32]) then conditions on these latent documents together with\\nthe input to generate the output. We marginalize the latent documents with a top-K approximation,\\neither on a per-output basis (assuming the same document is responsible for all tokens) or a per-token\\nbasis (where different documents are responsible for different tokens). Like T5 [ 51] or BART, RAG\\ncan be ﬁne-tuned on any seq2seq task, whereby both the generator and retriever are jointly learned.\\nThere has been extensive previous work proposing architectures to enrich systems with non-parametric\\nmemory which are trained from scratch for speciﬁc tasks, e.g. memory networks [ 64,55], stack-\\naugmented networks [ 25] and memory layers [ 30]. In contrast, we explore a setting where both\\nparametric and non-parametric memory components are pre-trained and pre-loaded with extensive\\nknowledge. Crucially, by using pre-trained access mechanisms, the ability to access knowledge is\\npresent without additional training.\\nOur results highlight the beneﬁts of combining parametric and non-parametric memory with genera-\\ntion for knowledge-intensive tasks —tasks that humans could not reasonably be expected to perform\\nwithout access to an external knowledge source. Our RAG models achieve state-of-the-art results\\non open Natural Questions [ 29], WebQuestions [ 3] and CuratedTrec [ 2] and strongly outperform\\nrecent approaches that use specialised pre-training objectives on TriviaQA [ 24]. Despite these being\\nextractive tasks, we ﬁnd that unconstrained generation outperforms previous extractive approaches.\\nFor knowledge-intensive generation, we experiment with MS-MARCO [ 1] and Jeopardy question\\ngeneration, and we ﬁnd that our models generate responses that are more factual, speciﬁc, and\\ndiverse than a BART baseline. For FEVER [ 56] fact veriﬁcation, we achieve results within 4.3% of\\nstate-of-the-art pipeline models which use strong retrieval supervision. Finally, we demonstrate that\\nthe non-parametric memory can be replaced to update the models’ knowledge as the world changes.1\\n2 Methods\\nWe explore RAG models, which use the input sequence xto retrieve text documents zand use them\\nas additional context when generating the target sequence y. As shown in Figure 1, our models\\nleverage two components: (i) a retriever pη(z|x)with parameters ηthat returns (top-K truncated)\\ndistributions over text passages given a query xand (ii) a generator pθ(yi|x,z,y 1:i−1)parametrized\\n1Code to run experiments with RAG has been open-sourced as part of the HuggingFace Transform-\\ners Library [ 66] and can be found at https://github.com/huggingface/transformers/blob/master/\\nexamples/rag/ . An interactive demo of RAG models can be found at https://huggingface.co/rag/\\n2', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 1}),\n", " Document(page_content='byθthat generates a current token based on a context of the previous i−1tokensy1:i−1, the original\\ninputxand a retrieved passage z.\\nTo train the retriever and generator end-to-end, we treat the retrieved document as a latent variable.\\nWe propose two models that marginalize over the latent documents in different ways to produce a\\ndistribution over generated text. In one approach, RAG-Sequence , the model uses the same document\\nto predict each target token. The second approach, RAG-Token , can predict each target token based\\non a different document. In the following, we formally introduce both models and then describe the\\npηandpθcomponents, as well as the training and decoding procedure.\\n2.1 Models\\nRAG-Sequence Model The RAG-Sequence model uses the same retrieved document to generate\\nthe complete sequence . Technically, it treats the retrieved document as a single latent variable that\\nis marginalized to get the seq2seq probability p(y|x)via a top-K approximation. Concretely, the\\ntop K documents are retrieved using the retriever, and the generator produces the output sequence\\nprobability for each document, which are then marginalized,\\npRAG-Sequence (y|x)≈∑\\nz∈top-k(p(·|x))pη(z|x)pθ(y|x,z) =∑\\nz∈top-k(p(·|x))pη(z|x)N∏\\nipθ(yi|x,z,y 1:i−1)\\nRAG-Token Model In the RAG-Token model we can draw a different latent document for each\\ntarget token and marginalize accordingly. This allows the generator to choose content from several\\ndocuments when producing an answer. Concretely, the top K documents are retrieved using the\\nretriever, and then the generator produces a distribution for the next output token for each document,\\nbefore marginalizing, and repeating the process with the following output token, Formally, we deﬁne:\\npRAG-Token (y|x)≈N∏\\ni∑\\nz∈top-k(p(·|x))pη(z|x)pθ(yi|x,z,y 1:i−1)\\nFinally, we note that RAG can be used for sequence classiﬁcation tasks by considering the target class\\nas a target sequence of length one, in which case RAG-Sequence and RAG-Token are equivalent.\\n2.2 Retriever: DPR\\nThe retrieval component pη(z|x)is based on DPR [26]. DPR follows a bi-encoder architecture:\\npη(z|x)∝exp(\\nd(z)⊤q(x))\\nd(z) =BERTd(z),q(x) =BERTq(x)\\nwhere d(z)is a dense representation of a document produced by a BERT BASE document encoder [8],\\nandq(x)a query representation produced by a query encoder , also based on BERT BASE. Calculating\\ntop-k (pη(·|x)), the list ofkdocumentszwith highest prior probability pη(z|x), is a Maximum Inner\\nProduct Search (MIPS) problem, which can be approximately solved in sub-linear time [ 23]. We use\\na pre-trained bi-encoder from DPR to initialize our retriever and to build the document index. This\\nretriever was trained to retrieve documents which contain answers to TriviaQA [ 24] questions and\\nNatural Questions [29]. We refer to the document index as the non-parametric memory .\\n2.3 Generator: BART\\nThe generator component pθ(yi|x,z,y 1:i−1)could be modelled using any encoder-decoder. We use\\nBART-large [ 32], a pre-trained seq2seq transformer [ 58] with 400M parameters. To combine the input\\nxwith the retrieved content zwhen generating from BART, we simply concatenate them. BART was\\npre-trained using a denoising objective and a variety of different noising functions. It has obtained\\nstate-of-the-art results on a diverse set of generation tasks and outperforms comparably-sized T5\\nmodels [32]. We refer to the BART generator parameters θas the parametric memory henceforth.\\n2.4 Training\\nWe jointly train the retriever and generator components without any direct supervision on what\\ndocument should be retrieved. Given a ﬁne-tuning training corpus of input/output pairs (xj,yj), we\\n3', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 2}),\n", " Document(page_content='minimize the negative marginal log-likelihood of each target,∑\\nj−logp(yj|xj)using stochastic\\ngradient descent with <PERSON> [ 28]. Updating the document encoder BERTdduring training is costly as\\nit requires the document index to be periodically updated as REALM does during pre-training [ 20].\\nWe do not ﬁnd this step necessary for strong performance, and keep the document encoder (and\\nindex) ﬁxed, only ﬁne-tuning the query encoder BERT qand the BART generator.\\n2.5 Decoding\\nAt test time, RAG-Sequence and RAG-Token require different ways to approximate arg maxyp(y|x).\\nRAG-Token The RAG-Token model can be seen as a standard, autoregressive seq2seq genera-\\ntor with transition probability: p′\\nθ(yi|x,y 1:i−1) =∑\\nz∈top-k(p(·|x))pη(zi|x)pθ(yi|x,zi,y1:i−1)To\\ndecode, we can plug p′\\nθ(yi|x,y 1:i−1)into a standard beam decoder.\\nRAG-Sequence For RAG-Sequence, the likelihood p(y|x)does not break into a conventional per-\\ntoken likelihood, hence we cannot solve it with a single beam search. Instead, we run beam search for\\neach document z, scoring each hypothesis using pθ(yi|x,z,y 1:i−1). This yields a set of hypotheses\\nY, some of which may not have appeared in the beams of all documents. To estimate the probability\\nof an hypothesis ywe run an additional forward pass for each document zfor whichydoes not\\nappear in the beam, multiply generator probability with pη(z|x)and then sum the probabilities across\\nbeams for the marginals. We refer to this decoding procedure as “Thorough Decoding.” For longer\\noutput sequences,|Y|can become large, requiring many forward passes. For more efﬁcient decoding,\\nwe can make a further approximation that pθ(y|x,zi)≈0whereywas not generated during beam\\nsearch from x,zi. This avoids the need to run additional forward passes once the candidate set Yhas\\nbeen generated. We refer to this decoding procedure as “Fast Decoding.”\\n3 Experiments\\nWe experiment with RAG in a wide range of knowledge-intensive tasks. For all experiments, we use\\na single Wikipedia dump for our non-parametric knowledge source. Following Lee et al. [31] and\\nKarpukhin et al. [26], we use the December 2018 dump. Each Wikipedia article is split into disjoint\\n100-word chunks, to make a total of 21M documents. We use the document encoder to compute an\\nembedding for each document, and build a single MIPS index using FAISS [ 23] with a Hierarchical\\nNavigable Small World approximation for fast retrieval [ 37]. During training, we retrieve the top\\nkdocuments for each query. We consider k∈{5,10}for training and set kfor test time using dev\\ndata. We now discuss experimental details for each task.\\n3.1 Open-domain Question Answering\\nOpen-domain question answering (QA) is an important real-world application and common testbed\\nfor knowledge-intensive tasks [ 20]. We treat questions and answers as input-output text pairs (x,y)\\nand train RAG by directly minimizing the negative log-likelihood of answers. We compare RAG to\\nthe popular extractive QA paradigm [ 5,7,31,26], where answers are extracted spans from retrieved\\ndocuments, relying primarily on non-parametric knowledge. We also compare to “Closed-Book\\nQA” approaches [ 52], which, like RAG, generate answers, but which do not exploit retrieval, instead\\nrelying purely on parametric knowledge. We consider four popular open-domain QA datasets: Natural\\nQuestions (NQ) [ 29], TriviaQA (TQA) [ 24]. WebQuestions (WQ) [ 3] and CuratedTrec (CT) [ 2]. As\\nCT and WQ are small, we follow DPR [ 26] by initializing CT and WQ models with our NQ RAG\\nmodel. We use the same train/dev/test splits as prior work [ 31,26] and report Exact Match (EM)\\nscores. For TQA, to compare with T5 [52], we also evaluate on the TQA Wiki test set.\\n3.2 Abstractive Question Answering\\nRAG models can go beyond simple extractive QA and answer questions with free-form, abstractive\\ntext generation. To test RAG’s natural language generation (NLG) in a knowledge-intensive setting,\\nwe use the MSMARCO NLG task v2.1 [ 43]. The task consists of questions, ten gold passages\\nretrieved from a search engine for each question, and a full sentence answer annotated from the\\nretrieved passages. We do not use the supplied passages, only the questions and answers, to treat\\n4', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 3}),\n", " Document(page_content='MSMARCO as an open-domain abstractive QA task. MSMARCO has some questions that cannot be\\nanswered in a way that matches the reference answer without access to the gold passages, such as\\n“What is the weather in V olcano, CA?” so performance will be lower without using gold passages.\\nWe also note that some MSMARCO questions cannot be answered using Wikipedia alone. Here,\\nRAG can rely on parametric knowledge to generate reasonable responses.\\n3.3 Jeopardy Question Generation\\nTo evaluate RAG’s generation abilities in a non-QA setting, we study open-domain question gen-\\neration. Rather than use questions from standard open-domain QA tasks, which typically consist\\nof short, simple questions, we propose the more demanding task of generating Jeopardy questions.\\nJeopardy is an unusual format that consists of trying to guess an entity from a fact about that entity.\\nFor example, “The World Cup” is the answer to the question “In 1986 Mexico scored as the ﬁrst\\ncountry to host this international sports competition twice.” As Jeopardy questions are precise,\\nfactual statements, generating Jeopardy questions conditioned on their answer entities constitutes a\\nchallenging knowledge-intensive generation task.\\nWe use the splits from SearchQA [ 10], with 100K train, 14K dev, and 27K test examples. As\\nthis is a new task, we train a BART model for comparison. Following [ 67], we evaluate using the\\nSQuAD-tuned Q-BLEU-1 metric [ 42]. Q-BLEU is a variant of BLEU with a higher weight for\\nmatching entities and has higher correlation with human judgment for question generation than\\nstandard metrics. We also perform two human evaluations, one to assess generation factuality, and\\none for speciﬁcity. We deﬁne factuality as whether a statement can be corroborated by trusted external\\nsources, and speciﬁcity as high mutual dependence between the input and output [ 33]. We follow\\nbest practice and use pairwise comparative evaluation [ 34]. Evaluators are shown an answer and two\\ngenerated questions, one from BART and one from RAG. They are then asked to pick one of four\\noptions—quuestion A is better, question B is better, both are good, or neither is good.\\n3.4 Fact Veriﬁcation\\nFEVER [ 56] requires classifying whether a natural language claim is supported or refuted by\\nWikipedia, or whether there is not enough information to decide. The task requires retrieving\\nevidence from Wikipedia relating to the claim and then reasoning over this evidence to classify\\nwhether the claim is true, false, or unveriﬁable from Wikipedia alone. FEVER is a retrieval problem\\ncoupled with an challenging entailment reasoning task. It also provides an appropriate testbed for\\nexploring the RAG models’ ability to handle classiﬁcation rather than generation. We map FEVER\\nclass labels (supports, refutes, or not enough info) to single output tokens and directly train with\\nclaim-class pairs. Crucially, unlike most other approaches to FEVER, we do not use supervision on\\nretrieved evidence. In many real-world applications, retrieval supervision signals aren’t available, and\\nmodels that do not require such supervision will be applicable to a wider range of tasks. We explore\\ntwo variants: the standard 3-way classiﬁcation task (supports/refutes/not enough info) and the 2-way\\n(supports/refutes) task studied in Thorne and Vlachos [57]. In both cases we report label accuracy.\\n4 Results\\n4.1 Open-domain Question Answering\\nTable 1 shows results for RAG along with state-of-the-art models. On all four open-domain QA\\ntasks, RAG sets a new state of the art (only on the T5-comparable split for TQA). RAG combines\\nthe generation ﬂexibility of the “closed-book” (parametric only) approaches and the performance of\\n\"open-book\" retrieval-based approaches. Unlike REALM and T5+SSM, RAG enjoys strong results\\nwithout expensive, specialized “salient span masking” pre-training [ 20]. It is worth noting that RAG’s\\nretriever is initialized using DPR’s retriever, which uses retrieval supervision on Natural Questions\\nand TriviaQA. RAG compares favourably to the DPR QA system, which uses a BERT-based “cross-\\nencoder” to re-rank documents, along with an extractive reader. RAG demonstrates that neither a\\nre-ranker nor extractive reader is necessary for state-of-the-art performance.\\nThere are several advantages to generating answers even when it is possible to extract them. Docu-\\nments with clues about the answer but do not contain the answer verbatim can still contribute towards\\na correct answer being generated, which is not possible with standard extractive approaches, leading\\n5', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 4}),\n", " Document(page_content='Table 1: Open-Domain QA Test Scores. For TQA,\\nleft column uses the standard test set for Open-\\nDomain QA, right column uses the TQA-Wiki\\ntest set. See Appendix D for further details.\\nModel NQ TQA WQ CT\\nClosed\\nBookT5-11B [52] 34.5 - /50.1 37.4 -\\nT5-11B+SSM[52] 36.6 - /60.5 44.7 -\\nOpen\\nBookREALM [20] 40.4 - / - 40.7 46.8\\nDPR [26] 41.5 57.9/ - 41.1 50.6\\nRAG-Token 44.1 55.2/66.1 45.5 50.0\\nRAG-Seq. 44.5 56.8/ 68.0 45.2 52.2Table 2: Generation and classiﬁcation Test Scores.\\nMS-MARCO SotA is [ 4], FEVER-3 is [ 68] and\\nFEVER-2 is [ 57] *Uses gold context/evidence.\\nBest model without gold access underlined.\\nModel Jeopardy MSMARCO FVR3 FVR2\\nB-1 QB-1 R-L B-1 Label Acc.\\nSotA - - 49.8*49.9*76.8 92.2 *\\nBART 15.1 19.7 38.2 41.6 64.0 81.1\\nRAG-Tok. 17.3 22.2 40.1 41.572.5 89.5RAG-Seq. 14.7 21.4 40.8 44.2\\nto more effective marginalization over documents. Furthermore, RAG can generate correct answers\\neven when the correct answer is not in any retrieved document, achieving 11.8% accuracy in such\\ncases for NQ, where an extractive model would score 0%.\\n4.2 Abstractive Question Answering\\nAs shown in Table 2, RAG-Sequence outperforms BART on Open MS-MARCO NLG by 2.6 Bleu\\npoints and 2.6 Rouge-L points. RAG approaches state-of-the-art model performance, which is\\nimpressive given that (i) those models access gold passages with speciﬁc information required to\\ngenerate the reference answer , (ii) many questions are unanswerable without the gold passages, and\\n(iii) not all questions are answerable from Wikipedia alone. Table 3 shows some generated answers\\nfrom our models. Qualitatively, we ﬁnd that RAG models hallucinate less and generate factually\\ncorrect text more often than BART. Later, we also show that RAG generations are more diverse than\\nBART generations (see §4.5).\\n4.3 Jeopardy Question Generation\\nTable 2 shows that RAG-Token performs better than RAG-Sequence on Jeopardy question generation,\\nwith both models outperforming BART on Q-BLEU-1. 4 shows human evaluation results, over 452\\npairs of generations from BART and RAG-Token. Evaluators indicated that BART was more factual\\nthan RAG in only 7.1% of cases, while RAG was more factual in 42.7% of cases, and both RAG and\\nBART were factual in a further 17% of cases, clearly demonstrating the effectiveness of RAG on\\nthe task over a state-of-the-art generation model. Evaluators also ﬁnd RAG generations to be more\\nspeciﬁc by a large margin. Table 3 shows typical generations from each model.\\nJeopardy questions often contain two separate pieces of information, and RAG-Token may perform\\nbest because it can generate responses that combine content from several documents. Figure 2 shows\\nan example. When generating “Sun”, the posterior is high for document 2 which mentions “The\\nSun Also Rises”. Similarly, document 1 dominates the posterior when “A Farewell to Arms” is\\ngenerated. Intriguingly, after the ﬁrst token of each book is generated, the document posterior ﬂattens.\\nThis observation suggests that the generator can complete the titles without depending on speciﬁc\\ndocuments. In other words, the model’s parametric knowledge is sufﬁcient to complete the titles. We\\nﬁnd evidence for this hypothesis by feeding the BART-only baseline with the partial decoding \"The\\nSun. BART completes the generation \"The SunAlso Rises\" isanovel bythis author of\"The Sun\\nAlso Rises\" indicating the title \"The Sun Also Rises\" is stored in BART’s parameters. Similarly,\\nBART will complete the partial decoding \"The SunAlso Rises\" isanovel bythis author of\"A\\nwith \"The SunAlso Rises\" isanovel bythis author of\"AFarewell toArms\" . This example shows\\nhow parametric and non-parametric memories work together —the non-parametric component helps\\nto guide the generation, drawing out speciﬁc knowledge stored in the parametric memory.\\n4.4 Fact Veriﬁcation\\nTable 2 shows our results on FEVER. For 3-way classiﬁcation, RAG scores are within 4.3% of\\nstate-of-the-art models, which are complex pipeline systems with domain-speciﬁc architectures and\\nsubstantial engineering, trained using intermediate retrieval supervision, which RAG does not require.\\n6', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 5}),\n", " Document(page_content='Document 1 : his works are considered classics of American\\nliterature ... His wartime experiences formed the basis for his novel\\n”A Farewell to Arms” (1929) ...\\nDocument 2 : ... artists of the 1920s ”Lost Generation” expatriate\\ncommunity. His debut novel, ”The Sun Also Rises” , was published\\nin 1926.\\nBOS”\\nTheSunAlsoRises”isa\\nnovelbythis\\nauthorof”A\\nFarewellto\\nArms”Doc 1\\nDoc 2\\nDoc 3\\nDoc 4\\nDoc 5Figure 2: RAG-Token document posterior p(zi|x,yi,y−i)for each generated token for input “Hem-\\ningway\" for Jeopardy generation with 5 retrieved documents. The posterior for document 1 is high\\nwhen generating “A Farewell to Arms\" and for document 2 when generating “The Sun Also Rises\".\\nTable 3: Examples from generation tasks. RAG models generate more speciﬁc and factually accurate\\nresponses. ‘?’ indicates factually incorrect responses, * indicates partially correct responses.\\nTask Input Model Generation\\nMS-\\nMARCOdeﬁne middle\\nearBART?The middle ear is the part of the ear between the middle ear and the nose.\\nRAG-T The middle ear is the portion of the ear internal to the eardrum.\\nRAG-S The middle ear includes the tympanic cavity and the three ossicles.\\nwhat currency\\nneeded in\\nscotlandBART The currency needed in Scotland is Pound sterling.\\nRAG-T Pound is the currency needed in Scotland.\\nRAG-S The currency needed in Scotland is the pound sterling.\\nJeopardy\\nQuestion\\nGener\\n-ationWashingtonBART?This state has the largest number of counties in the U.S.\\nRAG-T It’s the only U.S. state named for a U.S. president\\nRAG-S It’s the state where you’ll ﬁnd Mount Rainier National Park\\nThe Divine\\nComedyBART*This epic poem by Dante is divided into 3 parts: the Inferno, the Purgatorio & the Purgatorio\\nRAG-T Dante’s \"Inferno\" is the ﬁrst part of this epic poem\\nRAG-S This 14th century work is divided into 3 sections: \"Inferno\", \"Purgatorio\" & \"Paradiso\"\\nFor 2-way classiﬁcation, we compare against Thorne and Vlachos [57], who train RoBERTa [ 35]\\nto classify the claim as true or false given the gold evidence sentence. RAG achieves an accuracy\\nwithin 2.7% of this model, despite being supplied with only the claim and retrieving its own evidence.\\nWe also analyze whether documents retrieved by RAG correspond to documents annotated as gold\\nevidence in FEVER. We calculate the overlap in article titles between the top kdocuments retrieved\\nby RAG and gold evidence annotations. We ﬁnd that the top retrieved document is from a gold article\\nin 71% of cases, and a gold article is present in the top 10 retrieved articles in 90% of cases.\\n4.5 Additional Results\\nGeneration Diversity Section 4.3 shows that RAG models are more factual and speciﬁc than\\nBART for Jeopardy question generation. Following recent work on diversity-promoting decoding\\n[33,59,39], we also investigate generation diversity by calculating the ratio of distinct ngrams to\\ntotal ngrams generated by different models. Table 5 shows that RAG-Sequence’s generations are\\nmore diverse than RAG-Token’s, and both are signiﬁcantly more diverse than BART without needing\\nany diversity-promoting decoding.\\nRetrieval Ablations A key feature of RAG is learning to retrieve relevant information for the task.\\nTo assess the effectiveness of the retrieval mechanism, we run ablations where we freeze the retriever\\nduring training. As shown in Table 6, learned retrieval improves results for all tasks.\\nWe compare RAG’s dense retriever to a word overlap-based BM25 retriever [ 53]. Here, we replace\\nRAG’s retriever with a ﬁxed BM25 system, and use BM25 retrieval scores as logits when calculating\\np(z|x). Table 6 shows the results. For FEVER, BM25 performs best, perhaps since FEVER claims are\\nheavily entity-centric and thus well-suited for word overlap-based retrieval. Differentiable retrieval\\nimproves results on all other tasks, especially for Open-Domain QA, where it is crucial.\\nIndex hot-swapping An advantage of non-parametric memory models like RAG is that knowledge\\ncan be easily updated at test time. Parametric-only models like T5 or BART need further training to\\nupdate their behavior as the world changes. To demonstrate, we build an index using the DrQA [ 5]\\nWikipedia dump from December 2016 and compare outputs from RAG using this index to the newer\\nindex from our main results (December 2018). We prepare a list of 82 world leaders who had changed\\n7', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 6}),\n", " Document(page_content='Table 4: Human assessments for the Jeopardy\\nQuestion Generation Task.\\nFactuality Speciﬁcity\\nBART better 7.1% 16.8%\\nRAG better 42.7% 37.4%\\nBoth good 11.7% 11.8%\\nBoth poor 17.7% 6.9%\\nNo majority 20.8% 20.1%Table 5: Ratio of distinct to total tri-grams for\\ngeneration tasks.\\nMSMARCO Jeopardy QGen\\nGold 89.6% 90.0%\\nBART 70.7% 32.4%\\nRAG-Token 77.8% 46.8%\\nRAG-Seq. 83.5% 53.8%\\nTable 6: Ablations on the dev set. As FEVER is a classiﬁcation task, both RAG models are equivalent.\\nModel NQ TQA WQ CT Jeopardy-QGen MSMarco FVR-3 FVR-2\\nExact Match B-1 QB-1 R-L B-1 Label Accuracy\\nRAG-Token-BM25 29.7 41.5 32.1 33.1 17.5 22.3 55.5 48.475.1 91.6RAG-Sequence-BM25 31.8 44.1 36.6 33.8 11.1 19.5 56.5 46.9\\nRAG-Token-Frozen 37.8 50.1 37.1 51.1 16.7 21.7 55.9 49.472.9 89.4RAG-Se<PERSON>-<PERSON>ozen 41.2 52.1 41.8 52.6 11.8 19.6 56.7 47.3\\nRAG-Token 43.5 54.8 46.5 51.9 17.9 22.6 56.2 49.474.5 90.6RAG-Sequence 44.0 55.8 44.9 53.4 15.3 21.5 57.2 47.5\\nbetween these dates and use a template “Who is {position}?” (e.g. “Who is the President of Peru?”)\\nto query our NQ RAG model with each index. RAG answers 70% correctly using the 2016 index for\\n2016 world leaders and 68% using the 2018 index for 2018 world leaders. Accuracy with mismatched\\nindices is low (12% with the 2018 index and 2016 leaders, 4% with the 2016 index and 2018 leaders).\\nThis shows we can update RAG’s world knowledge by simply replacing its non-parametric memory.\\nEffect of Retrieving more documents Models are trained with either 5 or 10 retrieved latent\\ndocuments, and we do not observe signiﬁcant differences in performance between them. We have the\\nﬂexibility to adjust the number of retrieved documents at test time, which can affect performance and\\nruntime. Figure 3 (left) shows that retrieving more documents at test time monotonically improves\\nOpen-domain QA results for RAG-Sequence, but performance peaks for RAG-Token at 10 retrieved\\ndocuments. Figure 3 (right) shows that retrieving more documents leads to higher Rouge-L for\\nRAG-Token at the expense of Bleu-1, but the effect is less pronounced for RAG-Sequence.\\n10 20 30 40 50\\nKR e t r i e v e dD o c s394041424344NQ Exact MatchRAG-Tok\\nRAG-Seq\\n10 20 30 40 50\\nKR e t r i e v e dD o c s4050607080NQ Answer Recall @ KRAG-Tok\\nRAG-Seq\\nFixed DPR\\nBM25\\n10 20 30 40 50\\nKR e t r i e v e dD o c s4850525456Bleu-1 / Rouge-L scoreRAG-Tok R-L\\nRAG-Tok B-1\\nRAG-Seq R-L\\nRAG-Seq B-1\\nFigure 3: Left: NQ performance as more documents are retrieved. Center: Retrieval recall perfor-\\nmance in NQ. Right: MS-MARCO Bleu-1 and Rouge-L as more documents are retrieved.\\n5 Related Work\\nSingle-Task Retrieval Prior work has shown that retrieval improves performance across a variety of\\nNLP tasks when considered in isolation. Such tasks include open-domain question answering [ 5,29],\\nfact checking [ 56], fact completion [ 48], long-form question answering [ 12], Wikipedia article\\ngeneration [ 36], dialogue [ 41,65,9,13], translation [ 17], and language modeling [ 19,27]. Our\\nwork uniﬁes previous successes in incorporating retrieval into individual tasks, showing that a single\\nretrieval-based architecture is capable of achieving strong performance across several tasks.\\n8', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 7}),\n", " Document(page_content='General-Purpose Architectures for NLP Prior work on general-purpose architectures for NLP\\ntasks has shown great success without the use of retrieval. A single, pre-trained language model\\nhas been shown to achieve strong performance on various classiﬁcation tasks in the GLUE bench-\\nmarks [ 60,61] after ﬁne-tuning [ 49,8]. GPT-2 [ 50] later showed that a single, left-to-right, pre-trained\\nlanguage model could achieve strong performance across both discriminative and generative tasks.\\nFor further improvement, BART [ 32] and T5 [ 51,52] propose a single, pre-trained encoder-decoder\\nmodel that leverages bi-directional attention to achieve stronger performance on discriminative\\nand generative tasks. Our work aims to expand the space of possible tasks with a single, uniﬁed\\narchitecture, by learning a retrieval module to augment pre-trained, generative language models.\\nLearned Retrieval There is signiﬁcant work on learning to retrieve documents in information\\nretrieval, more recently with pre-trained, neural language models [ 44,26] similar to ours. Some\\nwork optimizes the retrieval module to aid in a speciﬁc, downstream task such as question answering,\\nusing search [ 46], reinforcement learning [ 6,63,62], or a latent variable approach [ 31,20] as in our\\nwork. These successes leverage different retrieval-based architectures and optimization techniques to\\nachieve strong performance on a single task, while we show that a single retrieval-based architecture\\ncan be ﬁne-tuned for strong performance on a variety of tasks.\\nMemory-based Architectures Our document index can be seen as a large external memory for\\nneural networks to attend to, analogous to memory networks [ 64,55]. Concurrent work [ 14] learns\\nto retrieve a trained embedding for each entity in the input, rather than to retrieve raw text as in our\\nwork. Other work improves the ability of dialog models to generate factual text by attending over\\nfact embeddings [ 15,13]. A key feature of our memory is that it is comprised of raw text rather\\ndistributed representations, which makes the memory both (i) human-readable, lending a form of\\ninterpretability to our model, and (ii) human-writable, enabling us to dynamically update the model’s\\nmemory by editing the document index. This approach has also been used in knowledge-intensive\\ndialog, where generators have been conditioned on retrieved text directly, albeit obtained via TF-IDF\\nrather than end-to-end learnt retrieval [9].\\nRetrieve-and-Edit approaches Our method shares some similarities with retrieve-and-edit style\\napproaches, where a similar training input-output pair is retrieved for a given input, and then edited\\nto provide a ﬁnal output. These approaches have proved successful in a number of domains including\\nMachine Translation [ 18,22] and Semantic Parsing [ 21]. Our approach does have several differences,\\nincluding less of emphasis on lightly editing a retrieved item, but on aggregating content from several\\npieces of retrieved content, as well as learning latent retrieval, and retrieving evidence documents\\nrather than related training pairs. This said, RAG techniques may work well in these settings, and\\ncould represent promising future work.\\n6 Discussion\\nIn this work, we presented hybrid generation models with access to parametric and non-parametric\\nmemory. We showed that our RAG models obtain state of the art results on open-domain QA. We\\nfound that people prefer RAG’s generation over purely parametric BART, ﬁnding RAG more factual\\nand speciﬁc. We conducted an thorough investigation of the learned retrieval component, validating\\nits effectiveness, and we illustrated how the retrieval index can be hot-swapped to update the model\\nwithout requiring any retraining. In future work, it may be fruitful to investigate if the two components\\ncan be jointly pre-trained from scratch, either with a denoising objective similar to BART or some\\nanother objective. Our work opens up new research directions on how parametric and non-parametric\\nmemories interact and how to most effectively combine them, showing promise in being applied to a\\nwide variety of NLP tasks.\\n9', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 8}),\n", " Document(page_content='Broader Impact\\nThis work offers several positive societal beneﬁts over previous work: the fact that it is more\\nstrongly grounded in real factual knowledge (in this case Wikipedia) makes it “hallucinate” less\\nwith generations that are more factual, and offers more control and interpretability. RAG could be\\nemployed in a wide variety of scenarios with direct beneﬁt to society, for example by endowing it\\nwith a medical index and asking it open-domain questions on that topic, or by helping people be more\\neffective at their jobs.\\nWith these advantages also come potential downsides: Wikipedia, or any potential external knowledge\\nsource, will probably never be entirely factual and completely devoid of bias. Since RAG can be\\nemployed as a language model, similar concerns as for GPT-2 [ 50] are valid here, although arguably\\nto a lesser extent, including that it might be used to generate abuse, faked or misleading content in\\nthe news or on social media; to impersonate others; or to automate the production of spam/phishing\\ncontent [ 54]. Advanced language models may also lead to the automation of various jobs in the\\ncoming decades [ 16]. In order to mitigate these risks, AI systems could be employed to ﬁght against\\nmisleading content and automated spam/phishing.\\nAcknowledgments\\nThe authors would like to thank the reviewers for their thoughtful and constructive feedback on this\\npaper, as well as HuggingFace for their help in open-sourcing code to run RAG models. The authors\\nwould also like to thank Kyunghyun Cho and Sewon Min for productive discussions and advice. EP\\nthanks supports from the NSF Graduate Research Fellowship. PL is supported by the FAIR PhD\\nprogram.\\nReferences\\n[1]Payal Bajaj, Daniel Campos, Nick Craswell, Li Deng, Jianfeng Gao, Xiaodong Liu, Rangan\\nMajumder, Andrew McNamara, Bhaskar Mitra, Tri Nguyen, Mir Rosenberg, Xia Song, Alina\\nStoica, Saurabh Tiwary, and Tong Wang. MS MARCO: A Human Generated MAchine\\nReading COmprehension Dataset. arXiv:1611.09268 [cs] , November 2016. URL http:\\n//arxiv.org/abs/1611.09268 . arXiv: 1611.09268.\\n[2]Petr Baudiš and Jan Šediv `y. Modeling of the question answering task in the yodaqa system. In\\nInternational Conference of the Cross-Language Evaluation Forum for European Languages ,\\npages 222–228. Springer, 2015. URL https://link.springer.com/chapter/10.1007%\\n2F978-3-319-24027-5_20 .\\n[3]Jonathan Berant, Andrew Chou, Roy Frostig, and Percy Liang. Semantic Parsing on Freebase\\nfrom Question-Answer Pairs. In Proceedings of the 2013 Conference on Empirical Methods\\nin Natural Language Processing , pages 1533–1544, Seattle, Washington, USA, October 2013.\\nAssociation for Computational Linguistics. URL http://www.aclweb.org/anthology/\\nD13-1160 .\\n[4]Bin Bi, Chenliang Li, Chen Wu, Ming Yan, and Wei Wang. Palm: Pre-training an autoencod-\\ning&autoregressive language model for context-conditioned generation. ArXiv , abs/2004.07159,\\n2020. URL https://arxiv.org/abs/2004.07159 .\\n[5]Danqi Chen, Adam Fisch, Jason Weston, and Antoine Bordes. Reading Wikipedia to Answer\\nOpen-Domain Questions. In Proceedings of the 55th Annual Meeting of the Association for\\nComputational Linguistics (Volume 1: Long Papers) , pages 1870–1879, Vancouver, Canada,\\nJuly 2017. Association for Computational Linguistics. doi: 10.18653/v1/P17-1171. URL\\nhttps://www.aclweb.org/anthology/P17-1171 .\\n[6]Eunsol Choi, Daniel Hewlett, Jakob Uszkoreit, Illia Polosukhin, Alexandre Lacoste, and\\nJonathan Berant. Coarse-to-ﬁne question answering for long documents. In Proceedings of the\\n55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers) ,\\npages 209–220, Vancouver, Canada, July 2017. Association for Computational Linguistics. doi:\\n10.18653/v1/P17-1020. URL https://www.aclweb.org/anthology/P17-1020 .\\n10', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 9}),\n", " Document(page_content='[7]<PERSON> and <PERSON>. Simple and Effective Multi-Paragraph Reading Compre-\\nhension. arXiv:1710.10723 [cs] , October 2017. URL http://arxiv.org/abs/1710.10723 .\\narXiv: 1710.10723.\\n[8]<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. BERT: Pre-training of\\nDeep Bidirectional Transformers for Language Understanding. In Proceedings of the 2019 Con-\\nference of the North American Chapter of the Association for Computational Linguistics: Human\\nLanguage Technologies, Volume 1 (Long and Short Papers) , pages 4171–4186, Minneapolis,\\nMinnesota, June 2019. Association for Computational Linguistics. doi: 10.18653/v1/N19-1423.\\nURL https://www.aclweb.org/anthology/N19-1423 .\\n[9]<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Wiz-\\nard of wikipedia: Knowledge-powered conversational agents. In International Conference on\\nLearning Representations , 2019. URL https://openreview.net/forum?id=r1l73iRqKm .\\n[10] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> <PERSON>, <PERSON>, and <PERSON>\\nC<PERSON>. SearchQA: A New Q&A Dataset Augmented with Context from a Search Engine.\\narXiv:1704.05179 [cs] , April 2017. URL http://arxiv.org/abs/1704.05179 . arXiv:\\n1704.05179.\\n[11] Angela Fan, Mike <PERSON>, and <PERSON>n Dauphin. Hierarchical neural story generation. In Proceed-\\nings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 1:\\nLong Papers) , pages 889–898, Melbourne, Australia, July 2018. Association for Computational\\nLinguistics. doi: 10.18653/v1/P18-1082. URL https://www.aclweb.org/anthology/\\nP18-1082 .\\n[12] Angela Fan, Yacine Jernite, Ethan Perez, David Grangier, Jason Weston, and Michael Auli. ELI5:\\nLong form question answering. In Proceedings of the 57th Annual Meeting of the Association\\nfor Computational Linguistics , pages 3558–3567, Florence, Italy, July 2019. Association for\\nComputational Linguistics. doi: 10.18653/v1/P19-1346. URL https://www.aclweb.org/\\nanthology/P19-1346 .\\n[13] Angela Fan, Claire Gardent, Chloe Braud, and Antoine Bordes. Augmenting transformers\\nwith KNN-based composite memory, 2020. URL https://openreview.net/forum?id=\\nH1gx1CNKPH .\\n[14] Thibault Févry, Livio Baldini Soares, Nicholas FitzGerald, Eunsol Choi, and Tom Kwiatkowski.\\nEntities as experts: Sparse memory access with entity supervision. ArXiv , abs/2004.07202,\\n2020. URL https://arxiv.org/abs/2004.07202 .\\n[15] Marjan Ghazvininejad, Chris Brockett, Ming-Wei Chang, Bill Dolan, Jianfeng Gao, Wen\\ntau Yih, and Michel Galley. A knowledge-grounded neural conversation model. In AAAI\\nConference on Artiﬁcial Intelligence , 2018. URL https://www.aaai.org/ocs/index.php/\\nAAAI/AAAI18/paper/view/16710 .\\n[16] Katja Grace, John Salvatier, Allan Dafoe, Baobao Zhang, and Owain Evans. When will AI\\nexceed human performance? evidence from AI experts. CoRR , abs/1705.08807, 2017. URL\\nhttp://arxiv.org/abs/1705.08807 .\\n[17] Jiatao Gu, Yong Wang, Kyunghyun Cho, and Victor O.K. Li. Search engine guided neural\\nmachine translation. In AAAI Conference on Artiﬁcial Intelligence , 2018. URL https:\\n//www.aaai.org/ocs/index.php/AAAI/AAAI18/paper/view/17282 .\\n[18] Jiatao Gu, Yong Wang, Kyunghyun Cho, and Victor O.K. Li. Search engine guided neural\\nmachine translation. In 32nd AAAI Conference on Artiﬁcial Intelligence, AAAI 2018 , 32nd\\nAAAI Conference on Artiﬁcial Intelligence, AAAI 2018, pages 5133–5140. AAAI press, 2018.\\n32nd AAAI Conference on Artiﬁcial Intelligence, AAAI 2018 ; Conference date: 02-02-2018\\nThrough 07-02-2018.\\n[19] Kelvin Guu, Tatsunori B. Hashimoto, Yonatan Oren, and Percy Liang. Generating sentences by\\nediting prototypes. Transactions of the Association for Computational Linguistics , 6:437–450,\\n2018. doi: 10.1162/tacl_a_00030. URL https://www.aclweb.org/anthology/Q18-1031 .\\n11', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 10}),\n", " Document(page_content='[20] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. REALM:\\nRetrieval-augmented language model pre-training. ArXiv , abs/2002.08909, 2020. URL https:\\n//arxiv.org/abs/2002.08909 .\\n[21] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A\\nretrieve-and-edit framework for predicting structured outputs. In S. <PERSON>,\\nH. <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, ed-\\nitors, Advances in Neural Information Processing Systems 31 , pages 10052–\\n10062. Curran Associates, Inc., 2018. URL http://papers.nips.cc/paper/\\n8209-a-retrieve-and-edit-framework-for-predicting-structured-outputs.\\npdf.\\n[22] <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Simple and effective retrieve-\\nedit-rerank text generation. In Proceedings of the 58th Annual Meeting of the Association for\\nComputational Linguistics , pages 2532–2538, Online, July 2020. Association for Computa-\\ntional Linguistics. doi: 10.18653/v1/2020.acl-main.228. URL https://www.aclweb.org/\\nanthology/2020.acl-main.228 .\\n[23] Jeff Johnson, Matthijs Douze, and Hervé Jégou. Billion-scale similarity search with gpus. arXiv\\npreprint arXiv:1702.08734 , 2017. URL https://arxiv.org/abs/1702.08734 .\\n[24] Mandar Joshi, Eunsol Choi, Daniel Weld, and Luke Zettlemoyer. TriviaQA: A Large Scale\\nDistantly Supervised Challenge Dataset for Reading Comprehension. In Proceedings of the\\n55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers) ,\\npages 1601–1611, Vancouver, Canada, July 2017. Association for Computational Linguistics.\\ndoi: 10.18653/v1/P17-1147. URL https://www.aclweb.org/anthology/P17-1147 .\\n[25] Armand Joulin and Tomas Mikolov. Inferring algorithmic patterns with stack-\\naugmented recurrent nets. In Proceedings of the 28th International Conference on\\nNeural Information Processing Systems - Volume 1 , NIPS’15, page 190–198, Cam-\\nbridge, MA, USA, 2015. MIT Press. URL https://papers.nips.cc/paper/\\n5857-inferring-algorithmic-patterns-with-stack-augmented-recurrent-nets .\\n[26] Vladimir Karpukhin, Barlas Oguz, Sewon Min, Ledell Wu, Sergey Edunov, Danqi Chen, and\\nWen-tau Yih. Dense passage retrieval for open-domain question answering. arXiv preprint\\narXiv:2004.04906 , 2020. URL https://arxiv.org/abs/2004.04906 .\\n[27] Urvashi Khandelwal, Omer Levy, Dan Jurafsky, Luke Zettlemoyer, and Mike Lewis. Generaliza-\\ntion through memorization: Nearest neighbor language models. In International Conference on\\nLearning Representations , 2020. URL https://openreview.net/forum?id=HklBjCEKvH .\\n[28] Diederik P. Kingma and Jimmy Ba. Adam: A method for stochastic optimization. In Yoshua\\nBengio and Yann LeCun, editors, 3rd International Conference on Learning Representations,\\nICLR 2015, San Diego, CA, USA, May 7-9, 2015, Conference Track Proceedings , 2015. URL\\nhttp://arxiv.org/abs/1412.6980 .\\n[29] Tom Kwiatkowski, Jennimaria Palomaki, Olivia Redﬁeld, Michael Collins, Ankur Parikh,\\nChris Alberti, Danielle Epstein, Illia Polosukhin, Matthew Kelcey, Jacob Devlin, Ken-\\nton Lee, Kristina N. Toutanova, Llion Jones, Ming-Wei Chang, Andrew Dai, Jakob\\nUszkoreit, Quoc Le, and Slav Petrov. Natural Questions: a Benchmark for Ques-\\ntion Answering Research. Transactions of the Association of Computational Lin-\\nguistics , 2019. URL https://tomkwiat.users.x20web.corp.google.com/papers/\\nnatural-questions/main-1455-kwiatkowski.pdf .\\n[30] Guillaume Lample, Alexandre Sablayrolles, Marc’ Aurelio Ranzato, Ludovic Denoyer, and\\nHerve Jegou. Large memory layers with product keys. In H. Wallach, H. Larochelle,\\nA. Beygelzimer, F. d’ Alché-Buc, E. Fox, and R. Garnett, editors, Advances in Neural In-\\nformation Processing Systems 32 , pages 8548–8559. Curran Associates, Inc., 2019. URL http:\\n//papers.nips.cc/paper/9061-large-memory-layers-with-product-keys.pdf .\\n[31] Kenton Lee, Ming-Wei Chang, and Kristina Toutanova. Latent retrieval for weakly supervised\\nopen domain question answering. In Proceedings of the 57th Annual Meeting of the Association\\n12', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 11}),\n", " Document(page_content='for Computational Linguistics , pages 6086–6096, Florence, Italy, July 2019. Association for\\nComputational Linguistics. doi: 10.18653/v1/P19-1612. URL https://www.aclweb.org/\\nanthology/P19-1612 .\\n[32] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,\\<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. BART: Denoising sequence-to-sequence\\npre-training for natural language generation, translation, and comprehension. arXiv preprint\\narXiv:1910.13461 , 2019. URL https://arxiv.org/abs/1910.13461 .\\n[33] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A diversity-promoting\\nobjective function for neural conversation models. In Proceedings of the 2016 Conference of the\\nNorth American Chapter of the Association for Computational Linguistics: Human Language\\nTechnologies , pages 110–119, San Diego, California, June 2016. Association for Computational\\nLinguistics. doi: 10.18653/v1/N16-1014. URL https://www.aclweb.org/anthology/\\nN16-1014 .\\n[34] <PERSON>, <PERSON>, and <PERSON>. Acute-eval: Improved dialogue evaluation\\nwith optimized questions and multi-turn comparisons. ArXiv , abs/1909.03087, 2019. URL\\nhttps://arxiv.org/abs/1909.03087 .\\n[35] Hairong Liu, Mingbo Ma, Liang Huang, Hao Xiong, and Zhongjun He. Robust neural machine\\ntranslation with joint textual and phonetic embedding. In Proceedings of the 57th Annual\\nMeeting of the Association for Computational Linguistics , pages 3044–3049, Florence, Italy,\\nJuly 2019. Association for Computational Linguistics. doi: 10.18653/v1/P19-1291. URL\\nhttps://www.aclweb.org/anthology/P19-1291 .\\n[36] Peter J. Liu*, Mohammad Saleh*, Etienne Pot, Ben Goodrich, Ryan Sepassi, Lukasz Kaiser,\\nand Noam Shazeer. Generating wikipedia by summarizing long sequences. In International\\nConference on Learning Representations , 2018. URL https://openreview.net/forum?\\nid=Hyg0vbWC- .\\n[37] Yury A. Malkov and D. A. Yashunin. Efﬁcient and robust approximate nearest neighbor search\\nusing hierarchical navigable small world graphs. IEEE Transactions on Pattern Analysis and\\nMachine Intelligence , 42:824–836, 2016. URL https://arxiv.org/abs/1603.09320 .\\n[38] Gary Marcus. The next decade in ai: four steps towards robust artiﬁcial intelligence. arXiv\\npreprint arXiv:2002.06177 , 2020. URL https://arxiv.org/abs/2002.06177 .\\n[39] Luca Massarelli, Fabio Petroni, Aleksandra Piktus, Myle Ott, Tim Rocktäschel, Vassilis\\nPlachouras, Fabrizio Silvestri, and Sebastian Riedel. How decoding strategies affect the\\nveriﬁability of generated text. arXiv preprint arXiv:1911.03587 , 2019. URL https:\\n//arxiv.org/abs/1911.03587 .\\n[40] Paulius Micikevicius, Sharan Narang, Jonah Alben, Gregory Diamos, Erich Elsen, David Garcia,\\nBoris Ginsburg, Michael Houston, Oleksii Kuchaiev, Ganesh Venkatesh, and Hao Wu. Mixed\\nprecision training. In ICLR , 2018. URL https://openreview.net/forum?id=r1gs9JgRZ .\\n[41] Nikita Moghe, Siddhartha Arora, Suman Banerjee, and Mitesh M. Khapra. Towards exploit-\\ning background knowledge for building conversation systems. In Proceedings of the 2018\\nConference on Empirical Methods in Natural Language Processing , pages 2322–2332, Brus-\\nsels, Belgium, October-November 2018. Association for Computational Linguistics. doi:\\n10.18653/v1/D18-1255. URL https://www.aclweb.org/anthology/D18-1255 .\\n[42] Preksha Nema and Mitesh M. Khapra. Towards a better metric for evaluating question generation\\nsystems. In Proceedings of the 2018 Conference on Empirical Methods in Natural Language\\nProcessing , pages 3950–3959, Brussels, Belgium, October-November 2018. Association for\\nComputational Linguistics. doi: 10.18653/v1/D18-1429. URL https://www.aclweb.org/\\nanthology/D18-1429 .\\n[43] Tri Nguyen, Mir Rosenberg, Xia Song, Jianfeng Gao, Saurabh Tiwary, Rangan Majumder,\\nand Li Deng. MS MARCO: A human generated machine reading comprehension dataset. In\\nTarek Richard Besold, Antoine Bordes, Artur S. d’Avila Garcez, and Greg Wayne, editors,\\nProceedings of the Workshop on Cognitive Computation: Integrating neural and symbolic\\n13', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 12}),\n", " Document(page_content='approaches 2016 co-located with the 30th Annual Conference on Neural Information Processing\\nSystems (NIPS 2016), Barcelona, Spain, December 9, 2016 , volume 1773 of CEUR Workshop\\nProceedings . CEUR-WS.org, 2016. URL http://ceur-ws.org/Vol-1773/CoCoNIPS_\\n2016_paper9.pdf .\\n[44] <PERSON> and <PERSON><PERSON><PERSON><PERSON>. <PERSON> re-ranking with BERT. arXiv preprint\\narXiv:1901.04085 , 2019. URL https://arxiv.org/abs/1901.04085 .\\n[45] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,\\nand <PERSON>. fairseq: A fast, extensible toolkit for sequence modeling. In Proceedings\\nof the 2019 Conference of the North American Chapter of the Association for Computational\\nLinguistics (Demonstrations) , pages 48–53, Minneapolis, Minnesota, June 2019. Association\\nfor Computational Linguistics. doi: 10.18653/v1/N19-4009. URL https://www.aclweb.\\norg/anthology/N19-4009 .\\n[46] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>\\<PERSON><PERSON><PERSON>. Finding generalizable evidence by learning to convince <PERSON>&a models. In Proceedings\\nof the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th\\nInternational Joint Conference on Natural Language Processing (EMNLP-IJCNLP) , pages\\n2402–2411, Hong Kong, China, November 2019. Association for Computational Linguistics.\\ndoi: 10.18653/v1/D19-1244. URL https://www.aclweb.org/anthology/D19-1244 .\\n[47] Fabio Petroni, Tim Rocktäschel, Sebastian Riedel, Patrick Lewis, Anton Bakhtin, Yuxiang Wu,\\nand Alexander Miller. Language models as knowledge bases? In Proceedings of the 2019\\nConference on Empirical Methods in Natural Language Processing and the 9th International\\nJoint Conference on Natural Language Processing (EMNLP-IJCNLP) , pages 2463–2473, Hong\\nKong, China, November 2019. Association for Computational Linguistics. doi: 10.18653/v1/\\nD19-1250. URL https://www.aclweb.org/anthology/D19-1250 .\\n[48] Fabio Petroni, Patrick Lewis, Aleksandra Piktus, Tim Rocktäschel, Yuxiang Wu, Alexander H.\\nMiller, and Sebastian Riedel. How context affects language models’ factual predictions. In\\nAutomated Knowledge Base Construction , 2020. URL https://openreview.net/forum?\\nid=025X0zPfn .\\n[49] Alec Radford, Karthik Narasimhan, Tim Salimans, and Ilya Sutskever. Im-\\nproving Language Understanding by Generative Pre-Training, 2018. URL\\nhttps://s3-us-west-2.amazonaws.com/openai-assets/research-covers/\\nlanguage-unsupervised/language_understanding_paper.pdf .\\n[50] Alec Radford, Jeff Wu, Rewon Child, David Luan, Dario Amodei, and Ilya\\nSutskever. Language models are unsupervised multitask learners, 2019. URL\\nhttps://d4mucfpksywv.cloudfront.net/better-language-models/language_\\nmodels_are_unsupervised_multitask_learners.pdf .\\n[51] Colin Raffel, Noam Shazeer, Adam Roberts, Katherine Lee, Sharan Narang, Michael Matena,\\nYanqi Zhou, Wei Li, and Peter J. Liu. Exploring the limits of transfer learning with a uniﬁed\\ntext-to-text transformer. arXiv e-prints , 2019. URL https://arxiv.org/abs/1910.10683 .\\n[52] Adam Roberts, Colin Raffel, and Noam Shazeer. How much knowledge can you pack into\\nthe parameters of a language model? arXiv e-prints , 2020. URL https://arxiv.org/abs/\\n2002.08910 .\\n[53] Stephen Robertson and Hugo Zaragoza. The probabilistic relevance framework: Bm25 and\\nbeyond. Found. Trends Inf. Retr. , 3(4):333–389, April 2009. ISSN 1554-0669. doi: 10.1561/\\n1500000019. URL https://doi.org/10.1561/1500000019 .\\n[54] Irene Solaiman, Miles Brundage, Jack Clark, Amanda Askell, Ariel Herbert-V oss, Jeff Wu, Alec\\nRadford, and Jian-Bing Wang. Release strategies and the social impacts of language models.\\nArXiv , abs/1908.09203, 2019.\\n[55] Sainbayar Sukhbaatar, Arthur Szlam, Jason Weston, and Rob Fergus. End-to-end memory net-\\nworks. In C. Cortes, N. D. Lawrence, D. D. Lee, M. Sugiyama, and R. Garnett, editors, Advances\\nin Neural Information Processing Systems 28 , pages 2440–2448. Curran Associates, Inc., 2015.\\nURL http://papers.nips.cc/paper/5846-end-to-end-memory-networks.pdf .\\n14', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 13}),\n", " Document(page_content='[56] <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. FEVER: a\\nlarge-scale dataset for fact extraction and VERiﬁcation. In Proceedings of the 2018 Conference\\nof the North American Chapter of the Association for Computational Linguistics: Human\\nLanguage Technologies, Volume 1 (Long Papers) , pages 809–819, New Orleans, Louisiana,\\nJune 2018. Association for Computational Linguistics. doi: 10.18653/v1/N18-1074. URL\\nhttps://www.aclweb.org/anthology/N18-1074 .\\n[57] <PERSON> and <PERSON>. Avoiding catastrophic forgetting in mitigating model\\nbiases in sentence-pair classiﬁcation with elastic weight consolidation. ArXiv , abs/2004.14366,\\n2020. URL https://arxiv.org/abs/2004.14366 .\\n[58] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>,\\nŁ uk<PERSON><PERSON>, and <PERSON><PERSON>. Attention is all you need. In <PERSON><PERSON>, U. V . <PERSON>,\\n<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural\\nInformation Processing Systems 30 , pages 5998–6008. Curran Associates, Inc., 2017. URL\\nhttp://papers.nips.cc/paper/7181-attention-is-all-you-need.pdf .\\n[59] Ashwin Vijayakumar, Michael Cogswell, Ramprasaath Selvaraju, Qing Sun, Stefan <PERSON>, <PERSON>\\nCrandall, and Dhruv Batra. Diverse beam search for improved description of complex scenes.\\nAAAI Conference on Artiﬁcial Intelligence , 2018. URL https://www.aaai.org/ocs/index.\\nphp/AAAI/AAAI18/paper/view/17329 .\\n[60] Alex Wang, Amanpreet Singh, Julian Michael, Felix Hill, Omer Levy, and Samuel Bowman.\\nGLUE: A multi-task benchmark and analysis platform for natural language understanding.\\nInProceedings of the 2018 EMNLP Workshop BlackboxNLP: Analyzing and Interpreting\\nNeural Networks for NLP , pages 353–355, Brussels, Belgium, November 2018. Association for\\nComputational Linguistics. doi: 10.18653/v1/W18-5446. URL https://www.aclweb.org/\\nanthology/W18-5446 .\\n[61] Alex Wang, Yada Pruksachatkun, Nikita Nangia, Amanpreet Singh, Julian Michael, Felix\\nHill, Omer Levy, and Samuel Bowman. SuperGLUE: A Stickier Benchmark for General-\\nPurpose Language Understanding Systems. In H. Wallach, H. Larochelle, A. Beygelzimer,\\nF. d\\\\textquotesingle Alché-Buc, E. Fox, and R. Garnett, editors, Advances in Neural Information\\nProcessing Systems 32 , pages 3261–3275. Curran Associates, Inc., 2019. URL https://\\narxiv.org/abs/1905.00537 .\\n[62] Shuohang Wang, Mo Yu, Xiaoxiao Guo, Zhiguo Wang, Tim Klinger, Wei Zhang, Shiyu Chang,\\nGerry Tesauro, Bowen Zhou, and Jing Jiang. R3: Reinforced ranker-reader for open-domain\\nquestion answering. In Sheila A. McIlraith and Kilian Q. Weinberger, editors, Proceedings of\\nthe Thirty-Second AAAI Conference on Artiﬁcial Intelligence, (AAAI-18), the 30th innovative\\nApplications of Artiﬁcial Intelligence (IAAI-18), and the 8th AAAI Symposium on Educational\\nAdvances in Artiﬁcial Intelligence (EAAI-18), New Orleans, Louisiana, USA, February 2-7,\\n2018 , pages 5981–5988. AAAI Press, 2018. URL https://www.aaai.org/ocs/index.\\nphp/AAAI/AAAI18/paper/view/16712 .\\n[63] Shuohang Wang, Mo Yu, Jing Jiang, Wei Zhang, Xiaoxiao Guo, Shiyu Chang, Zhiguo Wang,\\nTim Klinger, Gerald Tesauro, and Murray Campbell. Evidence aggregation for answer re-\\nranking in open-domain question answering. In ICLR , 2018. URL https://openreview.\\nnet/forum?id=rJl3yM-Ab .\\n[64] Jason Weston, Sumit Chopra, and Antoine Bordes. Memory networks. In Yoshua Bengio\\nand Yann LeCun, editors, 3rd International Conference on Learning Representations, ICLR\\n2015, San Diego, CA, USA, May 7-9, 2015, Conference Track Proceedings , 2015. URL\\nhttp://arxiv.org/abs/1410.3916 .\\n[65] Jason Weston, Emily Dinan, and Alexander Miller. Retrieve and reﬁne: Improved sequence\\ngeneration models for dialogue. In Proceedings of the 2018 EMNLP Workshop SCAI: The 2nd\\nInternational Workshop on Search-Oriented Conversational AI , pages 87–92, Brussels, Belgium,\\nOctober 2018. Association for Computational Linguistics. doi: 10.18653/v1/W18-5713. URL\\nhttps://www.aclweb.org/anthology/W18-5713 .\\n15', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 14}),\n", " Document(page_content='[66] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>,\\<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>’s transformers:\\nState-of-the-art natural language processing. ArXiv , abs/1910.03771, 2019.\\n[67] <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Addressing semantic drift in question generation for semi-\\nsupervised question answering. In Proceedings of the 2019 Conference on Empirical Meth-\\nods in Natural Language Processing and the 9th International Joint Conference on Natural\\nLanguage Processing (EMNLP-IJCNLP) , pages 2495–2509, Hong Kong, China, Novem-\\nber 2019. Association for Computational Linguistics. doi: 10.18653/v1/D19-1253. URL\\nhttps://www.aclweb.org/anthology/D19-1253 .\\n[68] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and\\<PERSON><PERSON><PERSON>. Reasoning over semantic-level graph for fact checking. ArXiv , abs/1909.03745, 2019.\\nURL https://arxiv.org/abs/1909.03745 .\\n16', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 15}),\n", " Document(page_content='Appendices for Retrieval-Augmented Generation for\\nKnowledge-Intensive NLP Tasks\\nA Implementation Details\\nFor Open-domain QA we report test numbers using 15 retrieved documents for RAG-Token models.\\nFor RAG-Sequence models, we report test results using 50 retrieved documents, and we use the\\nThorough Decoding approach since answers are generally short. We use greedy decoding for QA as\\nwe did not ﬁnd beam search improved results. For Open-MSMarco and Jeopardy question generation,\\nwe report test numbers using ten retrieved documents for both RAG-Token and RAG-Sequence,\\nand we also train a BART-large model as a baseline. We use a beam size of four, and use the Fast\\nDecoding approach for RAG-Sequence models, as Thorough Decoding did not improve performance.\\nB Human Evaluation\\nFigure 4: Annotation interface for human evaluation of factuality. A pop-out for detailed instructions\\nand a worked example appear when clicking \"view tool guide\".\\nFigure 4 shows the user interface for human evaluation. To avoid any biases for screen position,\\nwhich model corresponded to sentence A and sentence B was randomly selected for each example.\\nAnnotators were encouraged to research the topic using the internet, and were given detailed instruc-\\ntions and worked examples in a full instructions tab. We included some gold sentences in order to\\nassess the accuracy of the annotators. Two annotators did not perform well on these examples and\\ntheir annotations were removed from the results.\\nC Training setup Details\\nWe train all RAG models and BART baselines using Fairseq [ 45].2We train with mixed precision\\nﬂoating point arithmetic [ 40], distributing training across 8, 32GB NVIDIA V100 GPUs, though\\ntraining and inference can be run on one GPU. We ﬁnd that doing Maximum Inner Product Search\\nwith FAISS is sufﬁciently fast on CPU, so we store document index vectors on CPU, requiring ∼100\\nGB of CPU memory for all of Wikipedia. After submission, We have ported our code to HuggingFace\\nTransformers [ 66]3, which achieves equivalent performance to the previous version but is a cleaner\\nand easier to use implementation. This version is also open-sourced. We also compress the document\\nindex using FAISS’s compression tools, reducing the CPU memory requirement to 36GB. Scripts to\\nrun experiments with RAG can be found at https://github.com/huggingface/transformers/\\nblob/master/examples/rag/README.md and an interactive demo of a RAG model can be found\\nathttps://huggingface.co/rag/\\n2https://github.com/pytorch/fairseq\\n3https://github.com/huggingface/transformers\\n17', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 16}),\n", " Document(page_content='D Further Details on Open-Domain QA\\nFor open-domain QA, multiple answer annotations are often available for a given question. These\\nanswer annotations are exploited by extractive models during training as typically all the answer\\nannotations are used to ﬁnd matches within documents when preparing training data. For RAG, we\\nalso make use of multiple annotation examples for Natural Questions and WebQuestions by training\\nthe model with each (q,a)pair separately, leading to a small increase in accuracy. For TriviaQA,\\nthere are often many valid answers to a given question, some of which are not suitable training targets,\\nsuch as emoji or spelling variants. For TriviaQA, we ﬁlter out answer candidates if they do not occur\\nin top 1000 documents for the query.\\nCuratedTrec preprocessing The answers for CuratedTrec are given in the form of regular expres-\\nsions, which has been suggested as a reason why it is unsuitable for answer-generation models [20].\\nTo overcome this, we use a pre-processing step where we ﬁrst retrieve the top 1000 documents for\\neach query, and use the answer that most frequently matches the regex pattern as the supervision\\ntarget. If no matches are found, we resort to a simple heuristic: generate all possible permutations for\\neach regex, replacing non-deterministic symbols in the regex nested tree structure with a whitespace.\\nTriviaQA Evaluation setups The open-domain QA community customarily uses public develop-\\nment datasets as test datasets, as test data for QA datasets is often restricted and dedicated to reading\\ncompehension purposes. We report our results using the datasets splits used in DPR [ 26], which are\\nconsistent with common practice in Open-domain QA. For TriviaQA, this test dataset is the public\\nTriviaQA Web Development split. Roberts et al. [52] used the TriviaQA ofﬁcial Wikipedia test set\\ninstead. Févry et al. [14] follow this convention in order to compare with Roberts et al. [52] (See\\nappendix of [ 14]). We report results on both test sets to enable fair comparison to both approaches.\\nWe ﬁnd that our performance is much higher using the ofﬁcial Wiki test set, rather than the more\\nconventional open-domain test set, which we attribute to the ofﬁcial Wiki test set questions being\\nsimpler to answer from Wikipedia.\\nE Further Details on FEVER\\nFor FEVER classiﬁcation, we follow the practice from [ 32], and ﬁrst re-generate the claim, and\\nthen classify using the representation of the ﬁnal hidden state, before ﬁnally marginalizing across\\ndocuments to obtain the class probabilities. The FEVER task traditionally has two sub-tasks. The\\nﬁrst is to classify the claim as either \"Supported\", \"Refuted\" or \"Not Enough Info\", which is the task\\nwe explore in the main paper. FEVER’s other sub-task involves extracting sentences from Wikipedia\\nas evidence supporting the classiﬁcation prediction. As FEVER uses a different Wikipedia dump to\\nus, directly tackling this task is not straightforward. We hope to address this in future work.\\nF Null Document Probabilities\\nWe experimented with adding \"Null document\" mechanism to RAG, similar to REALM [ 20] in order\\nto model cases where no useful information could be retrieved for a given input. Here, if kdocuments\\nwere retrieved, we would additionally \"retrieve\" an empty document and predict a logit for the null\\ndocument, before marginalizing over k+ 1predictions. We explored modelling this null document\\nlogit by learning (i) a document embedding for the null document, (ii) a static learnt bias term, or\\n(iii) a neural network to predict the logit. We did not ﬁnd that these improved performance, so in\\nthe interests of simplicity, we omit them. For Open MS-MARCO, where useful retrieved documents\\ncannot always be retrieved, we observe that the model learns to always retrieve a particular set of\\ndocuments for questions that are less likely to beneﬁt from retrieval, suggesting that null document\\nmechanisms may not be necessary for RAG.\\nG Parameters\\nOur RAG models contain the trainable parameters for the BERT-base query and document encoder of\\nDPR, with 110M parameters each (although we do not train the document encoder ourselves) and\\n406M trainable parameters from BART-large, 406M parameters, making a total of 626M trainable\\n18', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 17}),\n", " Document(page_content='Table 7: Number of instances in the datasets used. *A hidden subset of this data is used for evaluation\\nTask Train Development Test\\nNatural Questions 79169 8758 3611\\nTriviaQA 78786 8838 11314\\nWebQuestions 3418 362 2033\\nCuratedTrec 635 134 635\\nJeopardy Question Generation 97392 13714 26849\\nMS-MARCO 153726 12468 101093*\\nFEVER-3-way 145450 10000 10000\\nFEVER-2-way 96966 6666 6666\\nparameters. The best performing \"closed-book\" (parametric only) open-domain QA model is T5-11B\\nwith 11 Billion trainable parameters. The T5 model with the closest number of parameters to our\\nmodels is T5-large (770M parameters), which achieves a score of 28.9 EM on Natural Questions [ 52],\\nsubstantially below the 44.5 that RAG-Sequence achieves, indicating that hybrid parametric/non-\\nparametric models require far fewer trainable parameters for strong open-domain QA performance.\\nThe non-parametric memory index does not consist of trainable parameters, but does consists of 21M\\n728 dimensional vectors, consisting of 15.3B values. These can be easily be stored at 8-bit ﬂoating\\npoint precision to manage memory and disk footprints.\\nH Retrieval Collapse\\nIn preliminary experiments, we observed that for some tasks such as story generation [ 11], the\\nretrieval component would “collapse” and learn to retrieve the same documents regardless of the\\ninput. In these cases, once retrieval had collapsed, the generator would learn to ignore the documents,\\nand the RAG model would perform equivalently to BART. The collapse could be due to a less-explicit\\nrequirement for factual knowledge in some tasks, or the longer target sequences, which could result\\nin less informative gradients for the retriever. Perez et al. [46] also found spurious retrieval results\\nwhen optimizing a retrieval component in order to improve performance on downstream tasks.\\nI Number of instances per dataset\\nThe number of training, development and test datapoints in each of our datasets is shown in Table 7.\\n19', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 18})]"]}, "metadata": {}, "execution_count": 32}]}, {"cell_type": "code", "source": ["docs[6]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6DNTiAvgvNYC", "outputId": "3bc37592-d65b-473e-ae39-ec0dd2b79c40"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Document(page_content='Document 1 : his works are considered classics of American\\nliterature ... His wartime experiences formed the basis for his novel\\n”A Farewell to Arms” (1929) ...\\nDocument 2 : ... artists of the 1920s ”Lost Generation” expatriate\\ncommunity. His debut novel, ”The Sun Also Rises” , was published\\nin 1926.\\nBOS”\\nTheSunAlsoRises”isa\\nnovelbythis\\nauthorof”A\\nFarewellto\\nArms”Doc 1\\nDoc 2\\nDoc 3\\nDoc 4\\nDoc 5Figure 2: RAG-Token document posterior p(zi|x,yi,y−i)for each generated token for input “Hem-\\ningway\" for Jeopardy generation with 5 retrieved documents. The posterior for document 1 is high\\nwhen generating “A Farewell to Arms\" and for document 2 when generating “The Sun Also Rises\".\\nTable 3: Examples from generation tasks. RAG models generate more speciﬁc and factually accurate\\nresponses. ‘?’ indicates factually incorrect responses, * indicates partially correct responses.\\nTask Input Model Generation\\nMS-\\nMARCOdeﬁne middle\\nearBART?The middle ear is the part of the ear between the middle ear and the nose.\\nRAG-T The middle ear is the portion of the ear internal to the eardrum.\\nRAG-S The middle ear includes the tympanic cavity and the three ossicles.\\nwhat currency\\nneeded in\\nscotlandBART The currency needed in Scotland is Pound sterling.\\nRAG-T Pound is the currency needed in Scotland.\\nRAG-S The currency needed in Scotland is the pound sterling.\\nJeopardy\\nQuestion\\nGener\\n-ationWashingtonBART?This state has the largest number of counties in the U.S.\\nRAG-T It’s the only U.S. state named for a U.S. president\\nRAG-S It’s the state where you’ll ﬁnd Mount Rainier National Park\\nThe Divine\\nComedyBART*This epic poem by Dante is divided into 3 parts: the Inferno, the Purgatorio & the Purgatorio\\nRAG-T Dante’s \"Inferno\" is the ﬁrst part of this epic poem\\nRAG-S This 14th century work is divided into 3 sections: \"Inferno\", \"Purgatorio\" & \"Paradiso\"\\nFor 2-way classiﬁcation, we compare against Thorne and Vlachos [57], who train RoBERTa [ 35]\\nto classify the claim as true or false given the gold evidence sentence. RAG achieves an accuracy\\nwithin 2.7% of this model, despite being supplied with only the claim and retrieving its own evidence.\\nWe also analyze whether documents retrieved by RAG correspond to documents annotated as gold\\nevidence in FEVER. We calculate the overlap in article titles between the top kdocuments retrieved\\nby RAG and gold evidence annotations. We ﬁnd that the top retrieved document is from a gold article\\nin 71% of cases, and a gold article is present in the top 10 retrieved articles in 90% of cases.\\n4.5 Additional Results\\nGeneration Diversity Section 4.3 shows that RAG models are more factual and speciﬁc than\\nBART for Jeopardy question generation. Following recent work on diversity-promoting decoding\\n[33,59,39], we also investigate generation diversity by calculating the ratio of distinct ngrams to\\ntotal ngrams generated by different models. Table 5 shows that RAG-Sequence’s generations are\\nmore diverse than RAG-Token’s, and both are signiﬁcantly more diverse than BART without needing\\nany diversity-promoting decoding.\\nRetrieval Ablations A key feature of RAG is learning to retrieve relevant information for the task.\\nTo assess the effectiveness of the retrieval mechanism, we run ablations where we freeze the retriever\\nduring training. As shown in Table 6, learned retrieval improves results for all tasks.\\nWe compare RAG’s dense retriever to a word overlap-based BM25 retriever [ 53]. Here, we replace\\nRAG’s retriever with a ﬁxed BM25 system, and use BM25 retrieval scores as logits when calculating\\np(z|x). Table 6 shows the results. For FEVER, BM25 performs best, perhaps since FEVER claims are\\nheavily entity-centric and thus well-suited for word overlap-based retrieval. Differentiable retrieval\\nimproves results on all other tasks, especially for Open-Domain QA, where it is crucial.\\nIndex hot-swapping An advantage of non-parametric memory models like RAG is that knowledge\\ncan be easily updated at test time. Parametric-only models like T5 or BART need further training to\\nupdate their behavior as the world changes. To demonstrate, we build an index using the DrQA [ 5]\\nWikipedia dump from December 2016 and compare outputs from RAG using this index to the newer\\nindex from our main results (December 2018). We prepare a list of 82 world leaders who had changed\\n7', metadata={'source': '/content/Retrieval-Augmented-Generation-for-NLP.pdf', 'page': 6})"]}, "metadata": {}, "execution_count": 33}]}, {"cell_type": "code", "source": ["retriever.add_documents(docs)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Ux831sq2pq3C", "outputId": "3ee30aa3-f465-4d02-e4ea-4a2e07b6bc69"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['a0eaba5f-2f7e-4216-adc3-31b8dd12ab1b',\n", " '3299bd80-71ad-404f-85ba-e7369097dc96',\n", " '08088af8-9a32-4672-8d8e-26afd2f207fd',\n", " '5d11d100-7ba9-43a4-8a9d-e824d80bd4d1',\n", " 'b5102237-1bc0-4476-b406-1bc85c488b06',\n", " 'f19ac9b1-5020-40a4-ab7b-95b5a5f4db16',\n", " '835c2213-5baf-455f-b2f7-8fb2614fc59d',\n", " '1747b864-415d-4100-900c-2823d7bcb291',\n", " '10ad6042-698f-4452-b75d-94763a5f6ab7',\n", " 'db4b8b9d-eba5-40fa-9ef0-d997f44bc9a4',\n", " '48177ca1-8b9e-430b-b31f-57868bc3514d',\n", " '8b330e9f-d2b9-47da-9d3a-9d95dfe826f0',\n", " '958a8d2f-013f-4f27-baf4-b78a96dc9706',\n", " '7b5eea0c-3b2d-499b-bec3-ff324e3c27c6',\n", " '3f3e30f8-97ae-43a5-a2d0-f30be94a794a',\n", " '1b8f6cad-9a5d-4f91-81ea-df15d2e120c3',\n", " '84c8417f-dc3b-4158-b1bb-a5bf5a07a092',\n", " '8dcb3d28-40f2-4733-ac4f-5e6caf48d8e8',\n", " '4811fca0-3e6d-42b4-8ee8-9048787f44cd']"]}, "metadata": {}, "execution_count": 34}]}, {"cell_type": "code", "source": ["print(retriever.invoke(\"what is RAG token?\")[0].page_content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jRoDhLHjsy5f", "outputId": "9e1b9921-2fe7-4549-dfa0-b64fef8da144"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Document 1 : his works are considered classics of American\n", "literature ... His wartime experiences formed the basis for his novel\n", "”A Farewell to Arms” (1929) ...\n", "Document 2 : ... artists of the 1920s ”Lost Generation” expatriate\n", "community. His debut novel, ”The Sun Also Rises” , was published\n", "in 1926.\n", "BOS”\n", "TheSunAlsoRises”isa\n", "novel<PERSON><PERSON><PERSON>\n", "authorof”A\n", "Farewellto\n", "Arms”Doc 1\n", "Doc 2\n", "Doc 3\n", "Doc 4\n", "Doc 5Figure 2: RAG-Token document posterior p(zi|x,yi,y−i)for each generated token for input “Hem-\n", "ingway\" for Jeopardy generation with 5 retrieved documents. The posterior for document 1 is high\n", "when generating “A Farewell to Arms\" and for document 2 when generating “The Sun Also Rises\".\n", "Table 3: Examples from generation tasks. RAG models generate more speciﬁc and factually accurate\n", "responses. ‘?’ indicates factually incorrect responses, * indicates partially correct responses.\n", "Task Input Model Generation\n", "MS-\n", "MARCOdeﬁne middle\n", "earBART?The middle ear is the part of the ear between the middle ear and the nose.\n", "RAG-T The middle ear is the portion of the ear internal to the eardrum.\n", "RAG-S The middle ear includes the tympanic cavity and the three ossicles.\n", "what currency\n", "needed in\n", "scotlandBART The currency needed in Scotland is Pound sterling.\n", "RAG-T Pound is the currency needed in Scotland.\n", "RAG-S The currency needed in Scotland is the pound sterling.\n", "Jeopardy\n", "Question\n", "Gener\n", "-ationWashingtonBART?This state has the largest number of counties in the U.S.\n", "RAG-T It’s the only U.S. state named for a U.S. president\n", "RAG-S It’s the state where you’ll ﬁnd Mount Rainier National Park\n", "The Divine\n", "ComedyBART*This epic poem by <PERSON> is divided into 3 parts: the Inferno, the Purgatorio & the Purgatorio\n", "RAG-<PERSON>’s \"Inferno\" is the ﬁrst part of this epic poem\n", "RAG-S This 14th century work is divided into 3 sections: \"Inferno\", \"Purgatorio\" & \"Paradiso\"\n", "For 2-way classiﬁcation, we compare against <PERSON> and <PERSON><PERSON><PERSON><PERSON> [57], who train RoBERTa [ 35]\n", "to classify the claim as true or false given the gold evidence sentence. RAG achieves an accuracy\n", "within 2.7% of this model, despite being supplied with only the claim and retrieving its own evidence.\n", "We also analyze whether documents retrieved by RAG correspond to documents annotated as gold\n", "evidence in FEVER. We calculate the overlap in article titles between the top kdocuments retrieved\n", "by RAG and gold evidence annotations. We ﬁnd that the top retrieved document is from a gold article\n", "in 71% of cases, and a gold article is present in the top 10 retrieved articles in 90% of cases.\n", "4.5 Additional Results\n", "Generation Diversity Section 4.3 shows that RAG models are more factual and speciﬁc than\n", "BART for Jeopardy question generation. Following recent work on diversity-promoting decoding\n", "[33,59,39], we also investigate generation diversity by calculating the ratio of distinct ngrams to\n", "total ngrams generated by different models. Table 5 shows that RAG-Sequence’s generations are\n", "more diverse than RAG-Token’s, and both are signiﬁcantly more diverse than BART without needing\n", "any diversity-promoting decoding.\n", "Retrieval Ablations A key feature of RAG is learning to retrieve relevant information for the task.\n", "To assess the effectiveness of the retrieval mechanism, we run ablations where we freeze the retriever\n", "during training. As shown in Table 6, learned retrieval improves results for all tasks.\n", "We compare RAG’s dense retriever to a word overlap-based BM25 retriever [ 53]. Here, we replace\n", "RAG’s retriever with a ﬁxed BM25 system, and use BM25 retrieval scores as logits when calculating\n", "p(z|x). Table 6 shows the results. For FEVER, BM25 performs best, perhaps since FEVER claims are\n", "heavily entity-centric and thus well-suited for word overlap-based retrieval. Differentiable retrieval\n", "improves results on all other tasks, especially for Open-Domain QA, where it is crucial.\n", "Index hot-swapping An advantage of non-parametric memory models like RAG is that knowledge\n", "can be easily updated at test time. Parametric-only models like T5 or BART need further training to\n", "update their behavior as the world changes. To demonstrate, we build an index using the DrQA [ 5]\n", "Wikipedia dump from December 2016 and compare outputs from RAG using this index to the newer\n", "index from our main results (December 2018). We prepare a list of 82 world leaders who had changed\n", "7\n"]}]}, {"cell_type": "code", "source": ["retriever.invoke(\n", "    \"what is RAG token?\",\n", "    score=True\n", ")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WHdda33buBrS", "outputId": "843cffb4-caad-4033-97ce-e43c4035e3b3"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='Document 1 : his works are considered classics of American\\nliterature ... His wartime experiences formed the basis for his novel\\n”A Farewell to Arms” (1929) ...\\nDocument 2 : ... artists of the 1920s ”Lost Generation” expatriate\\ncommunity. His debut novel, ”The Sun Also Rises” , was published\\nin 1926.\\nBOS”\\nTheSunAlsoRises”isa\\nnovelbythis\\nauthorof”A\\nFarewellto\\nArms”Doc 1\\nDoc 2\\nDoc 3\\nDoc 4\\nDoc 5Figure 2: RAG-Token document posterior p(zi|x,yi,y−i)for each generated token for input “Hem-\\ningway\" for Jeopardy generation with 5 retrieved documents. The posterior for document 1 is high\\nwhen generating “A Farewell to Arms\" and for document 2 when generating “The Sun Also Rises\".\\nTable 3: Examples from generation tasks. RAG models generate more speciﬁc and factually accurate\\nresponses. ‘?’ indicates factually incorrect responses, * indicates partially correct responses.\\nTask Input Model Generation\\nMS-\\nMARCOdeﬁne middle\\nearBART?The middle ear is the part of the ear between the middle ear and the nose.\\nRAG-T The middle ear is the portion of the ear internal to the eardrum.\\nRAG-S The middle ear includes the tympanic cavity and the three ossicles.\\nwhat currency\\nneeded in\\nscotlandBART The currency needed in Scotland is Pound sterling.\\nRAG-T Pound is the currency needed in Scotland.\\nRAG-S The currency needed in Scotland is the pound sterling.\\nJeopardy\\nQuestion\\nGener\\n-ationWashingtonBART?This state has the largest number of counties in the U.S.\\nRAG-T It’s the only U.S. state named for a U.S. president\\nRAG-S It’s the state where you’ll ﬁnd Mount Rainier National Park\\nThe Divine\\nComedyBART*This epic poem by Dante is divided into 3 parts: the Inferno, the Purgatorio & the Purgatorio\\nRAG-T Dante’s \"Inferno\" is the ﬁrst part of this epic poem\\nRAG-S This 14th century work is divided into 3 sections: \"Inferno\", \"Purgatorio\" & \"Paradiso\"\\nFor 2-way classiﬁcation, we compare against Thorne and Vlachos [57], who train RoBERTa [ 35]\\nto classify the claim as true or false given the gold evidence sentence. RAG achieves an accuracy\\nwithin 2.7% of this model, despite being supplied with only the claim and retrieving its own evidence.\\nWe also analyze whether documents retrieved by RAG correspond to documents annotated as gold\\nevidence in FEVER. We calculate the overlap in article titles between the top kdocuments retrieved\\nby RAG and gold evidence annotations. We ﬁnd that the top retrieved document is from a gold article\\nin 71% of cases, and a gold article is present in the top 10 retrieved articles in 90% of cases.\\n4.5 Additional Results\\nGeneration Diversity Section 4.3 shows that RAG models are more factual and speciﬁc than\\nBART for Jeopardy question generation. Following recent work on diversity-promoting decoding\\n[33,59,39], we also investigate generation diversity by calculating the ratio of distinct ngrams to\\ntotal ngrams generated by different models. Table 5 shows that RAG-Sequence’s generations are\\nmore diverse than RAG-Token’s, and both are signiﬁcantly more diverse than BART without needing\\nany diversity-promoting decoding.\\nRetrieval Ablations A key feature of RAG is learning to retrieve relevant information for the task.\\nTo assess the effectiveness of the retrieval mechanism, we run ablations where we freeze the retriever\\nduring training. As shown in Table 6, learned retrieval improves results for all tasks.\\nWe compare RAG’s dense retriever to a word overlap-based BM25 retriever [ 53]. Here, we replace\\nRAG’s retriever with a ﬁxed BM25 system, and use BM25 retrieval scores as logits when calculating\\np(z|x). Table 6 shows the results. For FEVER, BM25 performs best, perhaps since FEVER claims are\\nheavily entity-centric and thus well-suited for word overlap-based retrieval. Differentiable retrieval\\nimproves results on all other tasks, especially for Open-Domain QA, where it is crucial.\\nIndex hot-swapping An advantage of non-parametric memory models like RAG is that knowledge\\ncan be easily updated at test time. Parametric-only models like T5 or BART need further training to\\nupdate their behavior as the world changes. To demonstrate, we build an index using the DrQA [ 5]\\nWikipedia dump from December 2016 and compare outputs from RAG using this index to the newer\\nindex from our main results (December 2018). We prepare a list of 82 world leaders who had changed\\n7', metadata={'_additional': {'explainScore': '\\nHybrid (Result Set vector) Document e7de2dfd-a1a5-4196-bd83-ec76ce387a9d: original score 0.41845757, normalized score: 0.5 - \\nHybrid (Result Set keyword) Document e7de2dfd-a1a5-4196-bd83-ec76ce387a9d: original score 1.3854916, normalized score: 0.3908133', 'score': '0.8908133'}}),\n", " Document(page_content='Document 1 : his works are considered classics of American\\nliterature ... His wartime experiences formed the basis for his novel\\n”A Farewell to Arms” (1929) ...\\nDocument 2 : ... artists of the 1920s ”Lost Generation” expatriate\\ncommunity. His debut novel, ”The Sun Also Rises” , was published\\nin 1926.\\nBOS”\\nTheSunAlsoRises”isa\\nnovelbythis\\nauthorof”A\\nFarewellto\\nArms”Doc 1\\nDoc 2\\nDoc 3\\nDoc 4\\nDoc 5Figure 2: RAG-Token document posterior p(zi|x,yi,y−i)for each generated token for input “Hem-\\ningway\" for Jeopardy generation with 5 retrieved documents. The posterior for document 1 is high\\nwhen generating “A Farewell to Arms\" and for document 2 when generating “The Sun Also Rises\".\\nTable 3: Examples from generation tasks. RAG models generate more speciﬁc and factually accurate\\nresponses. ‘?’ indicates factually incorrect responses, * indicates partially correct responses.\\nTask Input Model Generation\\nMS-\\nMARCOdeﬁne middle\\nearBART?The middle ear is the part of the ear between the middle ear and the nose.\\nRAG-T The middle ear is the portion of the ear internal to the eardrum.\\nRAG-S The middle ear includes the tympanic cavity and the three ossicles.\\nwhat currency\\nneeded in\\nscotlandBART The currency needed in Scotland is Pound sterling.\\nRAG-T Pound is the currency needed in Scotland.\\nRAG-S The currency needed in Scotland is the pound sterling.\\nJeopardy\\nQuestion\\nGener\\n-ationWashingtonBART?This state has the largest number of counties in the U.S.\\nRAG-T It’s the only U.S. state named for a U.S. president\\nRAG-S It’s the state where you’ll ﬁnd Mount Rainier National Park\\nThe Divine\\nComedyBART*This epic poem by Dante is divided into 3 parts: the Inferno, the Purgatorio & the Purgatorio\\nRAG-T Dante’s \"Inferno\" is the ﬁrst part of this epic poem\\nRAG-S This 14th century work is divided into 3 sections: \"Inferno\", \"Purgatorio\" & \"Paradiso\"\\nFor 2-way classiﬁcation, we compare against Thorne and Vlachos [57], who train RoBERTa [ 35]\\nto classify the claim as true or false given the gold evidence sentence. RAG achieves an accuracy\\nwithin 2.7% of this model, despite being supplied with only the claim and retrieving its own evidence.\\nWe also analyze whether documents retrieved by RAG correspond to documents annotated as gold\\nevidence in FEVER. We calculate the overlap in article titles between the top kdocuments retrieved\\nby RAG and gold evidence annotations. We ﬁnd that the top retrieved document is from a gold article\\nin 71% of cases, and a gold article is present in the top 10 retrieved articles in 90% of cases.\\n4.5 Additional Results\\nGeneration Diversity Section 4.3 shows that RAG models are more factual and speciﬁc than\\nBART for Jeopardy question generation. Following recent work on diversity-promoting decoding\\n[33,59,39], we also investigate generation diversity by calculating the ratio of distinct ngrams to\\ntotal ngrams generated by different models. Table 5 shows that RAG-Sequence’s generations are\\nmore diverse than RAG-Token’s, and both are signiﬁcantly more diverse than BART without needing\\nany diversity-promoting decoding.\\nRetrieval Ablations A key feature of RAG is learning to retrieve relevant information for the task.\\nTo assess the effectiveness of the retrieval mechanism, we run ablations where we freeze the retriever\\nduring training. As shown in Table 6, learned retrieval improves results for all tasks.\\nWe compare RAG’s dense retriever to a word overlap-based BM25 retriever [ 53]. Here, we replace\\nRAG’s retriever with a ﬁxed BM25 system, and use BM25 retrieval scores as logits when calculating\\np(z|x). Table 6 shows the results. For FEVER, BM25 performs best, perhaps since FEVER claims are\\nheavily entity-centric and thus well-suited for word overlap-based retrieval. Differentiable retrieval\\nimproves results on all other tasks, especially for Open-Domain QA, where it is crucial.\\nIndex hot-swapping An advantage of non-parametric memory models like RAG is that knowledge\\ncan be easily updated at test time. Parametric-only models like T5 or BART need further training to\\nupdate their behavior as the world changes. To demonstrate, we build an index using the DrQA [ 5]\\nWikipedia dump from December 2016 and compare outputs from RAG using this index to the newer\\nindex from our main results (December 2018). We prepare a list of 82 world leaders who had changed\\n7', metadata={'_additional': {'explainScore': '\\nHybrid (Result Set vector) Document 835c2213-5baf-455f-b2f7-8fb2614fc59d: original score 0.41845757, normalized score: 0.5 - \\nHybrid (Result Set keyword) Document 835c2213-5baf-455f-b2f7-8fb2614fc59d: original score 1.3854916, normalized score: 0.3908133', 'score': '0.8908133'}}),\n", " Document(page_content='byθthat generates a current token based on a context of the previous i−1tokensy1:i−1, the original\\ninputxand a retrieved passage z.\\nTo train the retriever and generator end-to-end, we treat the retrieved document as a latent variable.\\nWe propose two models that marginalize over the latent documents in different ways to produce a\\ndistribution over generated text. In one approach, RAG-Sequence , the model uses the same document\\nto predict each target token. The second approach, RAG-Token , can predict each target token based\\non a different document. In the following, we formally introduce both models and then describe the\\npηandpθcomponents, as well as the training and decoding procedure.\\n2.1 Models\\nRAG-Sequence Model The RAG-Sequence model uses the same retrieved document to generate\\nthe complete sequence . Technically, it treats the retrieved document as a single latent variable that\\nis marginalized to get the seq2seq probability p(y|x)via a top-K approximation. Concretely, the\\ntop K documents are retrieved using the retriever, and the generator produces the output sequence\\nprobability for each document, which are then marginalized,\\npRAG-Sequence (y|x)≈∑\\nz∈top-k(p(·|x))pη(z|x)pθ(y|x,z) =∑\\nz∈top-k(p(·|x))pη(z|x)N∏\\nipθ(yi|x,z,y 1:i−1)\\nRAG-Token Model In the RAG-Token model we can draw a different latent document for each\\ntarget token and marginalize accordingly. This allows the generator to choose content from several\\ndocuments when producing an answer. Concretely, the top K documents are retrieved using the\\nretriever, and then the generator produces a distribution for the next output token for each document,\\nbefore marginalizing, and repeating the process with the following output token, Formally, we deﬁne:\\npRAG-Token (y|x)≈N∏\\ni∑\\nz∈top-k(p(·|x))pη(z|x)pθ(yi|x,z,y 1:i−1)\\nFinally, we note that RAG can be used for sequence classiﬁcation tasks by considering the target class\\nas a target sequence of length one, in which case RAG-Sequence and RAG-Token are equivalent.\\n2.2 Retriever: DPR\\nThe retrieval component pη(z|x)is based on DPR [26]. DPR follows a bi-encoder architecture:\\npη(z|x)∝exp(\\nd(z)⊤q(x))\\nd(z) =BERTd(z),q(x) =BERTq(x)\\nwhere d(z)is a dense representation of a document produced by a BERT BASE document encoder [8],\\nandq(x)a query representation produced by a query encoder , also based on BERT BASE. Calculating\\ntop-k (pη(·|x)), the list ofkdocumentszwith highest prior probability pη(z|x), is a Maximum Inner\\nProduct Search (MIPS) problem, which can be approximately solved in sub-linear time [ 23]. We use\\na pre-trained bi-encoder from DPR to initialize our retriever and to build the document index. This\\nretriever was trained to retrieve documents which contain answers to TriviaQA [ 24] questions and\\nNatural Questions [29]. We refer to the document index as the non-parametric memory .\\n2.3 Generator: BART\\nThe generator component pθ(yi|x,z,y 1:i−1)could be modelled using any encoder-decoder. We use\\nBART-large [ 32], a pre-trained seq2seq transformer [ 58] with 400M parameters. To combine the input\\nxwith the retrieved content zwhen generating from BART, we simply concatenate them. BART was\\npre-trained using a denoising objective and a variety of different noising functions. It has obtained\\nstate-of-the-art results on a diverse set of generation tasks and outperforms comparably-sized T5\\nmodels [32]. We refer to the BART generator parameters θas the parametric memory henceforth.\\n2.4 Training\\nWe jointly train the retriever and generator components without any direct supervision on what\\ndocument should be retrieved. Given a ﬁne-tuning training corpus of input/output pairs (xj,yj), we\\n3', metadata={'_additional': {'explainScore': '\\nHybrid (Result Set vector) Document 08088af8-9a32-4672-8d8e-26afd2f207fd: original score 0.29489785, normalized score: 0.34786984 - \\nHybrid (Result Set keyword) Document 08088af8-9a32-4672-8d8e-26afd2f207fd: original score 1.7123828, normalized score: 0.5', 'score': '0.8478699'}}),\n", " Document(page_content='byθthat generates a current token based on a context of the previous i−1tokensy1:i−1, the original\\ninputxand a retrieved passage z.\\nTo train the retriever and generator end-to-end, we treat the retrieved document as a latent variable.\\nWe propose two models that marginalize over the latent documents in different ways to produce a\\ndistribution over generated text. In one approach, RAG-Sequence , the model uses the same document\\nto predict each target token. The second approach, RAG-Token , can predict each target token based\\non a different document. In the following, we formally introduce both models and then describe the\\npηandpθcomponents, as well as the training and decoding procedure.\\n2.1 Models\\nRAG-Sequence Model The RAG-Sequence model uses the same retrieved document to generate\\nthe complete sequence . Technically, it treats the retrieved document as a single latent variable that\\nis marginalized to get the seq2seq probability p(y|x)via a top-K approximation. Concretely, the\\ntop K documents are retrieved using the retriever, and the generator produces the output sequence\\nprobability for each document, which are then marginalized,\\npRAG-Sequence (y|x)≈∑\\nz∈top-k(p(·|x))pη(z|x)pθ(y|x,z) =∑\\nz∈top-k(p(·|x))pη(z|x)N∏\\nipθ(yi|x,z,y 1:i−1)\\nRAG-Token Model In the RAG-Token model we can draw a different latent document for each\\ntarget token and marginalize accordingly. This allows the generator to choose content from several\\ndocuments when producing an answer. Concretely, the top K documents are retrieved using the\\nretriever, and then the generator produces a distribution for the next output token for each document,\\nbefore marginalizing, and repeating the process with the following output token, Formally, we deﬁne:\\npRAG-Token (y|x)≈N∏\\ni∑\\nz∈top-k(p(·|x))pη(z|x)pθ(yi|x,z,y 1:i−1)\\nFinally, we note that RAG can be used for sequence classiﬁcation tasks by considering the target class\\nas a target sequence of length one, in which case RAG-Sequence and RAG-Token are equivalent.\\n2.2 Retriever: DPR\\nThe retrieval component pη(z|x)is based on DPR [26]. DPR follows a bi-encoder architecture:\\npη(z|x)∝exp(\\nd(z)⊤q(x))\\nd(z) =BERTd(z),q(x) =BERTq(x)\\nwhere d(z)is a dense representation of a document produced by a BERT BASE document encoder [8],\\nandq(x)a query representation produced by a query encoder , also based on BERT BASE. Calculating\\ntop-k (pη(·|x)), the list ofkdocumentszwith highest prior probability pη(z|x), is a Maximum Inner\\nProduct Search (MIPS) problem, which can be approximately solved in sub-linear time [ 23]. We use\\na pre-trained bi-encoder from DPR to initialize our retriever and to build the document index. This\\nretriever was trained to retrieve documents which contain answers to TriviaQA [ 24] questions and\\nNatural Questions [29]. We refer to the document index as the non-parametric memory .\\n2.3 Generator: BART\\nThe generator component pθ(yi|x,z,y 1:i−1)could be modelled using any encoder-decoder. We use\\nBART-large [ 32], a pre-trained seq2seq transformer [ 58] with 400M parameters. To combine the input\\nxwith the retrieved content zwhen generating from BART, we simply concatenate them. BART was\\npre-trained using a denoising objective and a variety of different noising functions. It has obtained\\nstate-of-the-art results on a diverse set of generation tasks and outperforms comparably-sized T5\\nmodels [32]. We refer to the BART generator parameters θas the parametric memory henceforth.\\n2.4 Training\\nWe jointly train the retriever and generator components without any direct supervision on what\\ndocument should be retrieved. Given a ﬁne-tuning training corpus of input/output pairs (xj,yj), we\\n3', metadata={'_additional': {'explainScore': '\\nHybrid (Result Set vector) Document d454b5c3-12b3-4fef-aeb9-8b697e225ee8: original score 0.29489785, normalized score: 0.34786984 - \\nHybrid (Result Set keyword) Document d454b5c3-12b3-4fef-aeb9-8b697e225ee8: original score 1.7123828, normalized score: 0.5', 'score': '0.8478699'}})]"]}, "metadata": {}, "execution_count": 101}]}, {"cell_type": "code", "source": ["from langchain.chains import RetrievalQA"], "metadata": {"id": "Vt5vaVuLEdY9"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "HkhbVjqiMJXJ"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from langchain_core.prompts import ChatPromptTemplate"], "metadata": {"id": "heu-l-l176Pp"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["system_prompt = (\n", "    \"Use the given context to answer the question. \"\n", "    \"If you don't know the answer, say you don't know. \"\n", "    \"Use three sentence maximum and keep the answer concise. \"\n", "    \"Context: {context}\"\n", ")"], "metadata": {"id": "RrEl6Nm87_Vi"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system_prompt),\n", "        (\"human\", \"{query}\"),\n", "    ]\n", ")"], "metadata": {"id": "Gg0TRf_Q72P6"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from langchain.prompts import PromptTemplate\n", "template = \"\"\"\n", "Use the following pieces of context to answer the question at the end.\n", "If you don't know the answer, just say that you do not have the relevant information needed to provide a verified answer, don't try to make up an answer.\n", "When providing an answer, aim for clarity and precision. Position yourself as a knowledgeable authority on the topic, but also be mindful to explain the information in a manner that is accessible and comprehensible to those without a technical background.\n", "Always say \"Do you have any more questions pertaining to this instrument?\" at the end of the answer.\n", "{context}\n", "Question: {question}\n", "Helpful Answer:\"\"\"\n", "\n", "prompt = PromptTemplate.from_template(template)"], "metadata": {"id": "GNPZSFun-4Ka"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from langchain.chains.combine_documents import create_stuff_documents_chain"], "metadata": {"id": "Q3lt9jMW8hxK"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["question_answer_chain = create_stuff_documents_chain(llm, prompt)"], "metadata": {"id": "ppRiYOIa8b6y"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["hybrid_chain = RetrievalQA.from_chain_type(llm=llm, chain_type=\"stuff\", retriever=retriever,)\n"], "metadata": {"id": "3t7fVtBaAOfq"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["result1 = hybrid_chain.invoke(\"what is natural language processing?\")\n", "print(result1)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "I0DfMLiJ6lbr", "outputId": "29e93eae-37ce-48b4-8c49-dd04a7195edc"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["Both `max_new_tokens` (=300) and `max_length`(=3000) seem to have been set. `max_new_tokens` will take precedence. Please refer to the documentation for more information. (https://huggingface.co/docs/transformers/main/en/main_classes/text_generation)\n"]}, {"output_type": "stream", "name": "stdout", "text": ["{'query': 'what is natural language processing?', 'result': \"Use the following pieces of context to answer the question at the end. If you don't know the answer, just say that you don't know, don't try to make up an answer.\\n\\n[66] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>,\\n<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Huggingface’s transformers:\\nState-of-the-art natural language processing. ArXiv , abs/1910.03771, 2019.\\n[67] <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Addressing semantic drift in question generation for semi-\\nsupervised question answering. In Proceedings of the 2019 Conference on Empirical Meth-\\nods in Natural Language Processing and the 9th International Joint Conference on Natural\\nLanguage Processing (EMNLP-IJCNLP) , pages 2495–2509, Hong Kong, China, Novem-\\nber 2019. Association for Computational Linguistics. doi: 10.18653/v1/D19-1253. URL\\nhttps://www.aclweb.org/anthology/D19-1253 .\\n[68] <PERSON>jun <PERSON><PERSON>, <PERSON>, <PERSON>yu <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>ah<PERSON>, and\\n<PERSON>ian <PERSON>. <PERSON>ing over semantic-level graph for fact checking. Ar<PERSON>iv , abs/1909.03745, 2019.\\nUR<PERSON> https://arxiv.org/abs/1909.03745 .\\n16\\n\\n[66] <PERSON> <PERSON>, Lysandre De<PERSON>, <PERSON> <PERSON>h, <PERSON> <PERSON>umond, Clement <PERSON>angue, Anthony\\nMoi, Pierric Cistac, Tim Rault, Rémi Louf, Morgan Funtowicz, Joe Davison, Sam Shleifer,\\nPatrick von Platen, Clara Ma, Yacine Jernite, Julien Plu, Canwen Xu, Teven Le Scao, Sylvain\\nGugger, Mariama Drame, Quentin Lhoest, and Alexander M. Rush. Huggingface’s transformers:\\nState-of-the-art natural language processing. ArXiv , abs/1910.03771, 2019.\\n[67] Shiyue Zhang and Mohit Bansal. Addressing semantic drift in question generation for semi-\\nsupervised question answering. In Proceedings of the 2019 Conference on Empirical Meth-\\nods in Natural Language Processing and the 9th International Joint Conference on Natural\\nLanguage Processing (EMNLP-IJCNLP) , pages 2495–2509, Hong Kong, China, Novem-\\nber 2019. Association for Computational Linguistics. doi: 10.18653/v1/D19-1253. URL\\nhttps://www.aclweb.org/anthology/D19-1253 .\\n[68] Wanjun Zhong, Jingjing Xu, Duyu Tang, Zenan Xu, Nan Duan, Ming Zhou, Jiahai Wang, and\\nJian Yin. Reasoning over semantic-level graph for fact checking. ArXiv , abs/1909.03745, 2019.\\nURL https://arxiv.org/abs/1909.03745 .\\n16\\n\\napproaches 2016 co-located with the 30th Annual Conference on Neural Information Processing\\nSystems (NIPS 2016), Barcelona, Spain, December 9, 2016 , volume 1773 of CEUR Workshop\\nProceedings . CEUR-WS.org, 2016. URL http://ceur-ws.org/Vol-1773/CoCoNIPS_\\n2016_paper9.pdf .\\n[44] Rodrigo Nogueira and Kyunghyun Cho. Passage re-ranking with BERT. arXiv preprint\\narXiv:1901.04085 , 2019. URL https://arxiv.org/abs/1901.04085 .\\n[45] Myle Ott, Sergey Edunov, Alexei Baevski, Angela Fan, Sam Gross, Nathan Ng, David Grangier,\\nand Michael Auli. fairseq: A fast, extensible toolkit for sequence modeling. In Proceedings\\nof the 2019 Conference of the North American Chapter of the Association for Computational\\nLinguistics (Demonstrations) , pages 48–53, Minneapolis, Minnesota, June 2019. Association\\nfor Computational Linguistics. doi: 10.18653/v1/N19-4009. URL https://www.aclweb.\\norg/anthology/N19-4009 .\\n[46] Ethan Perez, Siddharth Karamcheti, Rob Fergus, Jason Weston, Douwe Kiela, and Kyunghyun\\nCho. Finding generalizable evidence by learning to convince q&a models. In Proceedings\\nof the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th\\nInternational Joint Conference on Natural Language Processing (EMNLP-IJCNLP) , pages\\n2402–2411, Hong Kong, China, November 2019. Association for Computational Linguistics.\\ndoi: 10.18653/v1/D19-1244. URL https://www.aclweb.org/anthology/D19-1244 .\\n[47] Fabio Petroni, Tim Rocktäschel, Sebastian Riedel, Patrick Lewis, Anton Bakhtin, Yuxiang Wu,\\nand Alexander Miller. Language models as knowledge bases? In Proceedings of the 2019\\nConference on Empirical Methods in Natural Language Processing and the 9th International\\nJoint Conference on Natural Language Processing (EMNLP-IJCNLP) , pages 2463–2473, Hong\\nKong, China, November 2019. Association for Computational Linguistics. doi: 10.18653/v1/\\nD19-1250. URL https://www.aclweb.org/anthology/D19-1250 .\\n[48] Fabio Petroni, Patrick Lewis, Aleksandra Piktus, Tim Rocktäschel, Yuxiang Wu, Alexander H.\\nMiller, and Sebastian Riedel. How context affects language models’ factual predictions. In\\nAutomated Knowledge Base Construction , 2020. URL https://openreview.net/forum?\\nid=025X0zPfn .\\n[49] Alec Radford, Karthik Narasimhan, Tim Salimans, and Ilya Sutskever. Im-\\nproving Language Understanding by Generative Pre-Training, 2018. URL\\nhttps://s3-us-west-2.amazonaws.com/openai-assets/research-covers/\\nlanguage-unsupervised/language_understanding_paper.pdf .\\n[50] Alec Radford, Jeff Wu, Rewon Child, David Luan, Dario Amodei, and Ilya\\nSutskever. Language models are unsupervised multitask learners, 2019. URL\\nhttps://d4mucfpksywv.cloudfront.net/better-language-models/language_\\nmodels_are_unsupervised_multitask_learners.pdf .\\n[51] Colin Raffel, Noam Shazeer, Adam Roberts, Katherine Lee, Sharan Narang, Michael Matena,\\nYanqi Zhou, Wei Li, and Peter J. Liu. Exploring the limits of transfer learning with a uniﬁed\\ntext-to-text transformer. arXiv e-prints , 2019. URL https://arxiv.org/abs/1910.10683 .\\n[52] Adam Roberts, Colin Raffel, and Noam Shazeer. How much knowledge can you pack into\\nthe parameters of a language model? arXiv e-prints , 2020. URL https://arxiv.org/abs/\\n2002.08910 .\\n[53] Stephen Robertson and Hugo Zaragoza. The probabilistic relevance framework: Bm25 and\\nbeyond. Found. Trends Inf. Retr. , 3(4):333–389, April 2009. ISSN 1554-0669. doi: 10.1561/\\n1500000019. URL https://doi.org/10.1561/1500000019 .\\n[54] Irene Solaiman, Miles Brundage, Jack Clark, Amanda Askell, Ariel Herbert-V oss, Jeff Wu, Alec\\nRadford, and Jian-Bing Wang. Release strategies and the social impacts of language models.\\nArXiv , abs/1908.09203, 2019.\\n[55] Sainbayar Sukhbaatar, Arthur Szlam, Jason Weston, and Rob Fergus. End-to-end memory net-\\nworks. In C. Cortes, N. D. Lawrence, D. D. Lee, M. Sugiyama, and R. Garnett, editors, Advances\\nin Neural Information Processing Systems 28 , pages 2440–2448. Curran Associates, Inc., 2015.\\nURL http://papers.nips.cc/paper/5846-end-to-end-memory-networks.pdf .\\n14\\n\\napproaches 2016 co-located with the 30th Annual Conference on Neural Information Processing\\nSystems (NIPS 2016), Barcelona, Spain, December 9, 2016 , volume 1773 of CEUR Workshop\\nProceedings . CEUR-WS.org, 2016. URL http://ceur-ws.org/Vol-1773/CoCoNIPS_\\n2016_paper9.pdf .\\n[44] Rodrigo Nogueira and Kyunghyun Cho. Passage re-ranking with BERT. arXiv preprint\\narXiv:1901.04085 , 2019. URL https://arxiv.org/abs/1901.04085 .\\n[45] Myle Ott, Sergey Edunov, Alexei Baevski, Angela Fan, Sam Gross, Nathan Ng, David Grangier,\\nand Michael Auli. fairseq: A fast, extensible toolkit for sequence modeling. In Proceedings\\nof the 2019 Conference of the North American Chapter of the Association for Computational\\nLinguistics (Demonstrations) , pages 48–53, Minneapolis, Minnesota, June 2019. Association\\nfor Computational Linguistics. doi: 10.18653/v1/N19-4009. URL https://www.aclweb.\\norg/anthology/N19-4009 .\\n[46] Ethan Perez, Siddharth Karamcheti, Rob Fergus, Jason Weston, Douwe Kiela, and Kyunghyun\\nCho. Finding generalizable evidence by learning to convince q&a models. In Proceedings\\nof the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th\\nInternational Joint Conference on Natural Language Processing (EMNLP-IJCNLP) , pages\\n2402–2411, Hong Kong, China, November 2019. Association for Computational Linguistics.\\ndoi: 10.18653/v1/D19-1244. URL https://www.aclweb.org/anthology/D19-1244 .\\n[47] Fabio Petroni, Tim Rocktäschel, Sebastian Riedel, Patrick Lewis, Anton Bakhtin, Yuxiang Wu,\\nand Alexander Miller. Language models as knowledge bases? In Proceedings of the 2019\\nConference on Empirical Methods in Natural Language Processing and the 9th International\\nJoint Conference on Natural Language Processing (EMNLP-IJCNLP) , pages 2463–2473, Hong\\nKong, China, November 2019. Association for Computational Linguistics. doi: 10.18653/v1/\\nD19-1250. URL https://www.aclweb.org/anthology/D19-1250 .\\n[48] Fabio Petroni, Patrick Lewis, Aleksandra Piktus, Tim Rocktäschel, Yuxiang Wu, Alexander H.\\nMiller, and Sebastian Riedel. How context affects language models’ factual predictions. In\\nAutomated Knowledge Base Construction , 2020. URL https://openreview.net/forum?\\nid=025X0zPfn .\\n[49] Alec Radford, Karthik Narasimhan, Tim Salimans, and Ilya Sutskever. Im-\\nproving Language Understanding by Generative Pre-Training, 2018. URL\\nhttps://s3-us-west-2.amazonaws.com/openai-assets/research-covers/\\nlanguage-unsupervised/language_understanding_paper.pdf .\\n[50] Alec Radford, Jeff Wu, Rewon Child, David Luan, Dario Amodei, and Ilya\\nSutskever. Language models are unsupervised multitask learners, 2019. URL\\nhttps://d4mucfpksywv.cloudfront.net/better-language-models/language_\\nmodels_are_unsupervised_multitask_learners.pdf .\\n[51] Colin Raffel, Noam Shazeer, Adam Roberts, Katherine Lee, Sharan Narang, Michael Matena,\\nYanqi Zhou, Wei Li, and Peter J. Liu. Exploring the limits of transfer learning with a uniﬁed\\ntext-to-text transformer. arXiv e-prints , 2019. URL https://arxiv.org/abs/1910.10683 .\\n[52] Adam Roberts, Colin Raffel, and Noam Shazeer. How much knowledge can you pack into\\nthe parameters of a language model? arXiv e-prints , 2020. URL https://arxiv.org/abs/\\n2002.08910 .\\n[53] Stephen Robertson and Hugo Zaragoza. The probabilistic relevance framework: Bm25 and\\nbeyond. Found. Trends Inf. Retr. , 3(4):333–389, April 2009. ISSN 1554-0669. doi: 10.1561/\\n1500000019. URL https://doi.org/10.1561/1500000019 .\\n[54] Irene Solaiman, Miles Brundage, Jack Clark, Amanda Askell, Ariel Herbert-V oss, Jeff Wu, Alec\\nRadford, and Jian-Bing Wang. Release strategies and the social impacts of language models.\\nArXiv , abs/1908.09203, 2019.\\n[55] Sainbayar Sukhbaatar, Arthur Szlam, Jason Weston, and Rob Fergus. End-to-end memory net-\\nworks. In C. Cortes, N. D. Lawrence, D. D. Lee, M. Sugiyama, and R. Garnett, editors, Advances\\nin Neural Information Processing Systems 28 , pages 2440–2448. Curran Associates, Inc., 2015.\\nURL http://papers.nips.cc/paper/5846-end-to-end-memory-networks.pdf .\\n14\\n\\nQuestion: what is natural language processing?\\nHelpful Answer: Naturaliktiktwerpiktwerpiktikt lic launchwerp licwerp liciktikt liciefsiefs launchwerpwerpiktiktwerpwerp licikt launchiktikt launchwerpikt lic launchiefs liciktwerpwerpiefsiktiktwerpiefsiktiktiktikt lic launchiefs launchiefs liciktiktiefs liciktiktwerpiktwerpwerp liciktikt lic licikt launch launchikt liciefs lic launchiktwerpiefsiktiefs launchiefswerpiktwerpiefsiefswerpiefsiktiktwerpikt liciktikt lic liciktiktwerpikt lic launch liciktiktiefsiktiktiktwerp liciktiktwerpikt licwerp liciktikt lic lic lic liciktwerpiktiktiefs lic liciefsiktiefswerpiktikt launchwerpwerpwerpikt liciefsikt licwerpwerpwerp launchiefs liciktiefsikt liciktikt licwerpiefsikt launchiefsiktiefsiefsiktikt launch licwerpwerpiefsiktwerpikt launchiktiktiefswerp lic launchwerp launchikt lic launchwerpiktwerp launchwerpiefs launchiktwerp launch launchiktiefs liciktiktwerpikt liciefsiktwerpwerpiefsiktiktiktiefs lic launch lic liciktwerpiefsiktwerp launchiefsiktiktiefs lic lic launchwerp liciktwerp liciktwerpiktwerp liciefsiefsiefswerpwerp lic launchikt licwerpiefs launchiefswerpiktiktwerpiefswerpiktiktiktwerpiefsikt lic launch lic licikt licikt lic launchiefsiktikt launchiefs launchwerpwerpiktiktiktiktiktiktikt lic\"}\n"]}]}, {"cell_type": "code", "source": ["print(result1['result'])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Flsjn21WMypT", "outputId": "3e4d6073-bfd3-4c31-b08c-d5fe399e8935"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Use the following pieces of context to answer the question at the end. If you don't know the answer, just say that you don't know, don't try to make up an answer.\n", "\n", "[66] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>\n", "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>,\n", "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>’s transformers:\n", "State-of-the-art natural language processing. ArXiv , abs/1910.03771, 2019.\n", "[67] <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Addressing semantic drift in question generation for semi-\n", "supervised question answering. In Proceedings of the 2019 Conference on Empirical Meth-\n", "ods in Natural Language Processing and the 9th International Joint Conference on Natural\n", "Language Processing (EMNLP-IJCNLP) , pages 2495–2509, Hong Kong, China, Novem-\n", "ber 2019. Association for Computational Linguistics. doi: 10.18653/v1/D19-1253. URL\n", "https://www.aclweb.org/anthology/D19-1253 .\n", "[68] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and\n", "<PERSON><PERSON>. Reasoning over semantic-level graph for fact checking. ArXiv , abs/1909.03745, 2019.\n", "URL https://arxiv.org/abs/1909.03745 .\n", "16\n", "\n", "[66] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>\n", "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>,\n", "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>’s transformers:\n", "State-of-the-art natural language processing. ArXiv , abs/1910.03771, 2019.\n", "[67] <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Addressing semantic drift in question generation for semi-\n", "supervised question answering. In Proceedings of the 2019 Conference on Empirical Meth-\n", "ods in Natural Language Processing and the 9th International Joint Conference on Natural\n", "Language Processing (EMNLP-IJCNLP) , pages 2495–2509, Hong Kong, China, Novem-\n", "ber 2019. Association for Computational Linguistics. doi: 10.18653/v1/D19-1253. URL\n", "https://www.aclweb.org/anthology/D19-1253 .\n", "[68] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and\n", "<PERSON><PERSON>. Reasoning over semantic-level graph for fact checking. ArXiv , abs/1909.03745, 2019.\n", "URL https://arxiv.org/abs/1909.03745 .\n", "16\n", "\n", "approaches 2016 co-located with the 30th Annual Conference on Neural Information Processing\n", "Systems (NIPS 2016), Barcelona, Spain, December 9, 2016 , volume 1773 of CEUR Workshop\n", "Proceedings . CEUR-WS.org, 2016. URL http://ceur-ws.org/Vol-1773/CoCoNIPS_\n", "2016_paper9.pdf .\n", "[44] <PERSON> and <PERSON><PERSON><PERSON><PERSON>. Passage re-ranking with BERT. arXiv preprint\n", "arXiv:1901.04085 , 2019. URL https://arxiv.org/abs/1901.04085 .\n", "[45] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,\n", "and <PERSON>. fairseq: A fast, extensible toolkit for sequence modeling. In Proceedings\n", "of the 2019 Conference of the North American Chapter of the Association for Computational\n", "Linguistics (Demonstrations) , pages 48–53, Minneapolis, Minnesota, June 2019. Association\n", "for Computational Linguistics. doi: 10.18653/v1/N19-4009. URL https://www.aclweb.\n", "org/anthology/N19-4009 .\n", "[46] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>\n", "Cho. Finding generalizable evidence by learning to convince q&a models. In Proceedings\n", "of the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th\n", "International Joint Conference on Natural Language Processing (EMNLP-IJCNLP) , pages\n", "2402–2411, Hong Kong, China, November 2019. Association for Computational Linguistics.\n", "doi: 10.18653/v1/D19-1244. URL https://www.aclweb.org/anthology/D19-1244 .\n", "[47] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,\n", "and <PERSON>. Language models as knowledge bases? In Proceedings of the 2019\n", "Conference on Empirical Methods in Natural Language Processing and the 9th International\n", "Joint Conference on Natural Language Processing (EMNLP-IJCNLP) , pages 2463–2473, Hong\n", "Kong, China, November 2019. Association for Computational Linguistics. doi: 10.18653/v1/\n", "D19-1250. URL https://www.aclweb.org/anthology/D19-1250 .\n", "[48] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>.\n", "<PERSON>, and <PERSON>. How context affects language models’ factual predictions. In\n", "Automated Knowledge Base Construction , 2020. URL https://openreview.net/forum?\n", "id=025X0zPfn .\n", "[49] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Im-\n", "proving Language Understanding by Generative Pre-Training, 2018. URL\n", "https://s3-us-west-2.amazonaws.com/openai-assets/research-covers/\n", "language-unsupervised/language_understanding_paper.pdf .\n", "[50] <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>\n", "Sutskever. Language models are unsupervised multitask learners, 2019. URL\n", "https://d4mucfpksywv.cloudfront.net/better-language-models/language_\n", "models_are_unsupervised_multitask_learners.pdf .\n", "[51] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>,\n", "<PERSON><PERSON>, <PERSON>, and <PERSON>. Exploring the limits of transfer learning with a uniﬁed\n", "text-to-text transformer. arXiv e-prints , 2019. URL https://arxiv.org/abs/1910.10683 .\n", "[52] <PERSON>, <PERSON>, and <PERSON><PERSON>. How much knowledge can you pack into\n", "the parameters of a language model? arXiv e-prints , 2020. URL https://arxiv.org/abs/\n", "2002.08910 .\n", "[53] <PERSON> and <PERSON>. The probabilistic relevance framework: Bm25 and\n", "beyond. Found. Trends Inf. Retr. , 3(4):333–389, April 2009. ISSN 1554-0669. doi: 10.1561/\n", "1500000019. URL https://doi.org/10.1561/1500000019 .\n", "[54] <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>-<PERSON>, <PERSON>, <PERSON>\n", "<PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Release strategies and the social impacts of language models.\n", "ArXiv , abs/1908.09203, 2019.\n", "[55] <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. End-to-end memory net-\n", "works. In <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances\n", "in Neural Information Processing Systems 28 , pages 2440–2448. Curran Associates, Inc., 2015.\n", "URL http://papers.nips.cc/paper/5846-end-to-end-memory-networks.pdf .\n", "14\n", "\n", "approaches 2016 co-located with the 30th Annual Conference on Neural Information Processing\n", "Systems (NIPS 2016), Barcelona, Spain, December 9, 2016 , volume 1773 of CEUR Workshop\n", "Proceedings . CEUR-WS.org, 2016. URL http://ceur-ws.org/Vol-1773/CoCoNIPS_\n", "2016_paper9.pdf .\n", "[44] <PERSON> and <PERSON><PERSON><PERSON><PERSON>. Passage re-ranking with BERT. arXiv preprint\n", "arXiv:1901.04085 , 2019. URL https://arxiv.org/abs/1901.04085 .\n", "[45] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,\n", "and <PERSON>. fairseq: A fast, extensible toolkit for sequence modeling. In Proceedings\n", "of the 2019 Conference of the North American Chapter of the Association for Computational\n", "Linguistics (Demonstrations) , pages 48–53, Minneapolis, Minnesota, June 2019. Association\n", "for Computational Linguistics. doi: 10.18653/v1/N19-4009. URL https://www.aclweb.\n", "org/anthology/N19-4009 .\n", "[46] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>\n", "Cho. Finding generalizable evidence by learning to convince q&a models. In Proceedings\n", "of the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th\n", "International Joint Conference on Natural Language Processing (EMNLP-IJCNLP) , pages\n", "2402–2411, Hong Kong, China, November 2019. Association for Computational Linguistics.\n", "doi: 10.18653/v1/D19-1244. URL https://www.aclweb.org/anthology/D19-1244 .\n", "[47] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,\n", "and <PERSON>. Language models as knowledge bases? In Proceedings of the 2019\n", "Conference on Empirical Methods in Natural Language Processing and the 9th International\n", "Joint Conference on Natural Language Processing (EMNLP-IJCNLP) , pages 2463–2473, Hong\n", "Kong, China, November 2019. Association for Computational Linguistics. doi: 10.18653/v1/\n", "D19-1250. URL https://www.aclweb.org/anthology/D19-1250 .\n", "[48] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>.\n", "<PERSON>, and <PERSON>. How context affects language models’ factual predictions. In\n", "Automated Knowledge Base Construction , 2020. URL https://openreview.net/forum?\n", "id=025X0zPfn .\n", "[49] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Im-\n", "proving Language Understanding by Generative Pre-Training, 2018. URL\n", "https://s3-us-west-2.amazonaws.com/openai-assets/research-covers/\n", "language-unsupervised/language_understanding_paper.pdf .\n", "[50] <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>\n", "Sutskever. Language models are unsupervised multitask learners, 2019. URL\n", "https://d4mucfpksywv.cloudfront.net/better-language-models/language_\n", "models_are_unsupervised_multitask_learners.pdf .\n", "[51] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>,\n", "<PERSON><PERSON>, <PERSON>, and <PERSON>. Exploring the limits of transfer learning with a uniﬁed\n", "text-to-text transformer. arXiv e-prints , 2019. URL https://arxiv.org/abs/1910.10683 .\n", "[52] <PERSON>, <PERSON>, and <PERSON><PERSON>. How much knowledge can you pack into\n", "the parameters of a language model? arXiv e-prints , 2020. URL https://arxiv.org/abs/\n", "2002.08910 .\n", "[53] <PERSON> and <PERSON>. The probabilistic relevance framework: Bm25 and\n", "beyond. Found. Trends Inf. Retr. , 3(4):333–389, April 2009. ISSN 1554-0669. doi: 10.1561/\n", "1500000019. URL https://doi.org/10.1561/1500000019 .\n", "[54] <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>-<PERSON>, <PERSON>, <PERSON>\n", "<PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Release strategies and the social impacts of language models.\n", "ArXiv , abs/1908.09203, 2019.\n", "[55] <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. End-to-end memory net-\n", "works. In <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances\n", "in Neural Information Processing Systems 28 , pages 2440–2448. Curran Associates, Inc., 2015.\n", "URL http://papers.nips.cc/paper/5846-end-to-end-memory-networks.pdf .\n", "14\n", "\n", "Question: what is natural language processing?\n", "Helpful Answer: Naturaliktiktwerpiktwerpiktikt lic launchwerp licwerp liciktikt liciefsiefs launchwerpwerpiktiktwerpwerp licikt launchiktikt launchwerpikt lic launchiefs liciktwerpwerpiefsiktiktwerpiefsiktiktiktikt lic launchiefs launchiefs liciktiktiefs liciktiktwerpiktwerpwerp liciktikt lic licikt launch launchikt liciefs lic launchiktwerpiefsiktiefs launchiefswerpiktwerpiefsiefswerpiefsiktiktwerpikt liciktikt lic liciktiktwerpikt lic launch liciktiktiefsiktiktiktwerp liciktiktwerpikt licwerp liciktikt lic lic lic liciktwerpiktiktiefs lic liciefsiktiefswerpiktikt launchwerpwerpwerpikt liciefsikt licwerpwerpwerp launchiefs liciktiefsikt liciktikt licwerpiefsikt launchiefsiktiefsiefsiktikt launch licwerpwerpiefsiktwerpikt launchiktiktiefswerp lic launchwerp launchikt lic launchwerpiktwerp launchwerpiefs launchiktwerp launch launchiktiefs liciktiktwerpikt liciefsiktwerpwerpiefsiktiktiktiefs lic launch lic liciktwerpiefsiktwerp launchiefsiktiktiefs lic lic launchwerp liciktwerp liciktwerpiktwerp liciefsiefsiefswerpwerp lic launchikt licwerpiefs launchiefswerpiktiktwerpiefswerpiktiktiktwerpiefsikt lic launch lic licikt licikt lic launchiefsiktikt launchiefs launchwerpwerpiktiktiktiktiktiktikt lic\n"]}]}, {"cell_type": "code", "source": ["query=\"What is Abstractive Question Answering?\""], "metadata": {"id": "QhG3Krz99APy"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["response = hybrid_chain.invoke({\"query\":query})"], "metadata": {"id": "hmmRp1O_ArC9", "colab": {"base_uri": "https://localhost:8080/", "height": 304}, "outputId": "e56e99d7-4ddf-460a-e5a7-330b968d5cf6"}, "execution_count": null, "outputs": [{"output_type": "error", "ename": "AssertionError", "evalue": "The input to RunnablePassthrough.assign() must be a dict.", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAssertionError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-57-a19006a08984>\u001b[0m in \u001b[0;36m<cell line: 1>\u001b[0;34m()\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mresponse\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mhybrid_chain\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0minvoke\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m{\u001b[0m\u001b[0;34m\"query\"\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0mquery\u001b[0m\u001b[0;34m}\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain/chains/base.py\u001b[0m in \u001b[0;36minvoke\u001b[0;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[1;32m    164\u001b[0m         \u001b[0;32mexcept\u001b[0m \u001b[0mBaseException\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    165\u001b[0m             \u001b[0mrun_manager\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mon_chain_error\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0me\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 166\u001b[0;31m             \u001b[0;32mraise\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    167\u001b[0m         \u001b[0mrun_manager\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mon_chain_end\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0moutputs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    168\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain/chains/base.py\u001b[0m in \u001b[0;36minvoke\u001b[0;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[1;32m    154\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_validate_inputs\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    155\u001b[0m             outputs = (\n\u001b[0;32m--> 156\u001b[0;31m                 \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_call\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mrun_manager\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mrun_manager\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    157\u001b[0m                 \u001b[0;32mif\u001b[0m \u001b[0mnew_arg_supported\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    158\u001b[0m                 \u001b[0;32melse\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_call\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain/chains/retrieval_qa/base.py\u001b[0m in \u001b[0;36m_call\u001b[0;34m(self, inputs, run_manager)\u001b[0m\n\u001b[1;32m    143\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    144\u001b[0m             \u001b[0mdocs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_get_docs\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mquestion\u001b[0m\u001b[0;34m)\u001b[0m  \u001b[0;31m# type: ignore[call-arg]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 145\u001b[0;31m         answer = self.combine_documents_chain.run(\n\u001b[0m\u001b[1;32m    146\u001b[0m             \u001b[0minput_documents\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mdocs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mquestion\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mquestion\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mcallbacks\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0m_run_manager\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_child\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    147\u001b[0m         )\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain_core/_api/deprecation.py\u001b[0m in \u001b[0;36mwarning_emitting_wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    146\u001b[0m                 \u001b[0mwarned\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mTrue\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    147\u001b[0m                 \u001b[0memit_warning\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 148\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mwrapped\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    149\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    150\u001b[0m         \u001b[0;32masync\u001b[0m \u001b[0;32mdef\u001b[0m \u001b[0mawarning_emitting_wrapper\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mAny\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mAny\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m->\u001b[0m \u001b[0mAny\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain/chains/base.py\u001b[0m in \u001b[0;36mrun\u001b[0;34m(self, callbacks, tags, metadata, *args, **kwargs)\u001b[0m\n\u001b[1;32m    603\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    604\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mkwargs\u001b[0m \u001b[0;32mand\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0margs\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 605\u001b[0;31m             return self(kwargs, callbacks=callbacks, tags=tags, metadata=metadata)[\n\u001b[0m\u001b[1;32m    606\u001b[0m                 \u001b[0m_output_key\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    607\u001b[0m             ]\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain_core/_api/deprecation.py\u001b[0m in \u001b[0;36mwarning_emitting_wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    146\u001b[0m                 \u001b[0mwarned\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mTrue\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    147\u001b[0m                 \u001b[0memit_warning\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 148\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mwrapped\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    149\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    150\u001b[0m         \u001b[0;32masync\u001b[0m \u001b[0;32mdef\u001b[0m \u001b[0mawarning_emitting_wrapper\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mAny\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mAny\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m->\u001b[0m \u001b[0mAny\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain/chains/base.py\u001b[0m in \u001b[0;36m__call__\u001b[0;34m(self, inputs, return_only_outputs, callbacks, tags, metadata, run_name, include_run_info)\u001b[0m\n\u001b[1;32m    381\u001b[0m         }\n\u001b[1;32m    382\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 383\u001b[0;31m         return self.invoke(\n\u001b[0m\u001b[1;32m    384\u001b[0m             \u001b[0minputs\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    385\u001b[0m             \u001b[0mcast\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mRunnableConfig\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m{\u001b[0m\u001b[0mk\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mv\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0mk\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mv\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mconfig\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mitems\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mif\u001b[0m \u001b[0mv\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m}\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain/chains/base.py\u001b[0m in \u001b[0;36minvoke\u001b[0;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[1;32m    164\u001b[0m         \u001b[0;32mexcept\u001b[0m \u001b[0mBaseException\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    165\u001b[0m             \u001b[0mrun_manager\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mon_chain_error\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0me\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 166\u001b[0;31m             \u001b[0;32mraise\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    167\u001b[0m         \u001b[0mrun_manager\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mon_chain_end\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0moutputs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    168\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain/chains/base.py\u001b[0m in \u001b[0;36minvoke\u001b[0;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[1;32m    154\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_validate_inputs\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    155\u001b[0m             outputs = (\n\u001b[0;32m--> 156\u001b[0;31m                 \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_call\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mrun_manager\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mrun_manager\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    157\u001b[0m                 \u001b[0;32mif\u001b[0m \u001b[0mnew_arg_supported\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    158\u001b[0m                 \u001b[0;32melse\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_call\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain/chains/combine_documents/base.py\u001b[0m in \u001b[0;36m_call\u001b[0;34m(self, inputs, run_manager)\u001b[0m\n\u001b[1;32m    135\u001b[0m         \u001b[0;31m# Other keys are assumed to be needed for LLM prediction\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    136\u001b[0m         \u001b[0mother_keys\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m{\u001b[0m\u001b[0mk\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mv\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0mk\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mv\u001b[0m \u001b[0;32min\u001b[0m \u001b[0minputs\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mitems\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mif\u001b[0m \u001b[0mk\u001b[0m \u001b[0;34m!=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0minput_key\u001b[0m\u001b[0;34m}\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 137\u001b[0;31m         output, extra_return_dict = self.combine_docs(\n\u001b[0m\u001b[1;32m    138\u001b[0m             \u001b[0mdocs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mcallbacks\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0m_run_manager\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_child\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mother_keys\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    139\u001b[0m         )\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain/chains/combine_documents/stuff.py\u001b[0m in \u001b[0;36mcombine_docs\u001b[0;34m(self, docs, callbacks, **kwargs)\u001b[0m\n\u001b[1;32m    242\u001b[0m         \u001b[0minputs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_get_inputs\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdocs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    243\u001b[0m         \u001b[0;31m# Call predict on the LLM.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 244\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mllm_chain\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpredict\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcallbacks\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mcallbacks\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m{\u001b[0m\u001b[0;34m}\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    245\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    246\u001b[0m     async def acombine_docs(\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain/chains/llm.py\u001b[0m in \u001b[0;36mpredict\u001b[0;34m(self, callbacks, **kwargs)\u001b[0m\n\u001b[1;32m    314\u001b[0m                 \u001b[0mcompletion\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mllm\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpredict\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0madjective\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m\"funny\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    315\u001b[0m         \"\"\"\n\u001b[0;32m--> 316\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mcallbacks\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mcallbacks\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0moutput_key\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    317\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    318\u001b[0m     \u001b[0;32masync\u001b[0m \u001b[0;32mdef\u001b[0m \u001b[0mapredict\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mcallbacks\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mCallbacks\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mAny\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m->\u001b[0m \u001b[0mstr\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain_core/_api/deprecation.py\u001b[0m in \u001b[0;36mwarning_emitting_wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    146\u001b[0m                 \u001b[0mwarned\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mTrue\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    147\u001b[0m                 \u001b[0memit_warning\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 148\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mwrapped\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    149\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    150\u001b[0m         \u001b[0;32masync\u001b[0m \u001b[0;32mdef\u001b[0m \u001b[0mawarning_emitting_wrapper\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mAny\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mAny\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m->\u001b[0m \u001b[0mAny\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain/chains/base.py\u001b[0m in \u001b[0;36m__call__\u001b[0;34m(self, inputs, return_only_outputs, callbacks, tags, metadata, run_name, include_run_info)\u001b[0m\n\u001b[1;32m    381\u001b[0m         }\n\u001b[1;32m    382\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 383\u001b[0;31m         return self.invoke(\n\u001b[0m\u001b[1;32m    384\u001b[0m             \u001b[0minputs\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    385\u001b[0m             \u001b[0mcast\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mRunnableConfig\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m{\u001b[0m\u001b[0mk\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mv\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0mk\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mv\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mconfig\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mitems\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mif\u001b[0m \u001b[0mv\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m}\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain/chains/base.py\u001b[0m in \u001b[0;36minvoke\u001b[0;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[1;32m    164\u001b[0m         \u001b[0;32mexcept\u001b[0m \u001b[0mBaseException\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    165\u001b[0m             \u001b[0mrun_manager\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mon_chain_error\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0me\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 166\u001b[0;31m             \u001b[0;32mraise\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    167\u001b[0m         \u001b[0mrun_manager\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mon_chain_end\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0moutputs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    168\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain/chains/base.py\u001b[0m in \u001b[0;36minvoke\u001b[0;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[1;32m    154\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_validate_inputs\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    155\u001b[0m             outputs = (\n\u001b[0;32m--> 156\u001b[0;31m                 \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_call\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mrun_manager\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mrun_manager\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    157\u001b[0m                 \u001b[0;32mif\u001b[0m \u001b[0mnew_arg_supported\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    158\u001b[0m                 \u001b[0;32melse\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_call\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain/chains/llm.py\u001b[0m in \u001b[0;36m_call\u001b[0;34m(self, inputs, run_manager)\u001b[0m\n\u001b[1;32m    124\u001b[0m         \u001b[0mrun_manager\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mOptional\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mCallbackManagerForChainRun\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    125\u001b[0m     ) -> Dict[str, str]:\n\u001b[0;32m--> 126\u001b[0;31m         \u001b[0mresponse\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mgenerate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mrun_manager\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mrun_manager\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    127\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcreate_outputs\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mresponse\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    128\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain/chains/llm.py\u001b[0m in \u001b[0;36mgenerate\u001b[0;34m(self, input_list, run_manager)\u001b[0m\n\u001b[1;32m    143\u001b[0m             )\n\u001b[1;32m    144\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 145\u001b[0;31m             results = self.llm.bind(stop=stop, **self.llm_kwargs).batch(\n\u001b[0m\u001b[1;32m    146\u001b[0m                 \u001b[0mcast\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mList\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mprompts\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m{\u001b[0m\u001b[0;34m\"callbacks\"\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mcallbacks\u001b[0m\u001b[0;34m}\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    147\u001b[0m             )\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain_core/runnables/base.py\u001b[0m in \u001b[0;36mbatch\u001b[0;34m(self, inputs, config, return_exceptions, **kwargs)\u001b[0m\n\u001b[1;32m   4464\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   4465\u001b[0m             \u001b[0mconfigs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_merge_configs\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mconfig\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0m_\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mrange\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 4466\u001b[0;31m         return self.bound.batch(\n\u001b[0m\u001b[1;32m   4467\u001b[0m             \u001b[0minputs\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   4468\u001b[0m             \u001b[0mconfigs\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain_core/runnables/base.py\u001b[0m in \u001b[0;36mbatch\u001b[0;34m(self, inputs, config, return_exceptions, **kwargs)\u001b[0m\n\u001b[1;32m   2541\u001b[0m             \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2542\u001b[0m                 \u001b[0;32mfor\u001b[0m \u001b[0mi\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mstep\u001b[0m \u001b[0;32min\u001b[0m \u001b[0menumerate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msteps\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2543\u001b[0;31m                     inputs = step.batch(\n\u001b[0m\u001b[1;32m   2544\u001b[0m                         \u001b[0minputs\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2545\u001b[0m                         [\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain_core/runnables/base.py\u001b[0m in \u001b[0;36mbatch\u001b[0;34m(self, inputs, config, return_exceptions, **kwargs)\u001b[0m\n\u001b[1;32m   4464\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   4465\u001b[0m             \u001b[0mconfigs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_merge_configs\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mconfig\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0m_\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mrange\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 4466\u001b[0;31m         return self.bound.batch(\n\u001b[0m\u001b[1;32m   4467\u001b[0m             \u001b[0minputs\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   4468\u001b[0m             \u001b[0mconfigs\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain_core/runnables/base.py\u001b[0m in \u001b[0;36mbatch\u001b[0;34m(self, inputs, config, return_exceptions, **kwargs)\u001b[0m\n\u001b[1;32m    635\u001b[0m         \u001b[0;31m# If there's only one input, don't bother with the executor\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    636\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m==\u001b[0m \u001b[0;36m1\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 637\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mcast\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mList\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mOutput\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0minvoke\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mconfigs\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    638\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    639\u001b[0m         \u001b[0;32mwith\u001b[0m \u001b[0mget_executor_for_config\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mconfigs\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0mexecutor\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain_core/runnables/base.py\u001b[0m in \u001b[0;36minvoke\u001b[0;34m(input, config)\u001b[0m\n\u001b[1;32m    631\u001b[0m                     \u001b[0;32mreturn\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    632\u001b[0m             \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 633\u001b[0;31m                 \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0minvoke\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mconfig\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    634\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    635\u001b[0m         \u001b[0;31m# If there's only one input, don't bother with the executor\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain_core/runnables/passthrough.py\u001b[0m in \u001b[0;36minvoke\u001b[0;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[1;32m    467\u001b[0m         \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mAny\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    468\u001b[0m     ) -> Dict[str, Any]:\n\u001b[0;32m--> 469\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_call_with_config\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_invoke\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mconfig\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    470\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    471\u001b[0m     async def _ainvoke(\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain_core/runnables/base.py\u001b[0m in \u001b[0;36m_call_with_config\u001b[0;34m(self, func, input, config, run_type, **kwargs)\u001b[0m\n\u001b[1;32m   1507\u001b[0m             output = cast(\n\u001b[1;32m   1508\u001b[0m                 \u001b[0mOutput\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1509\u001b[0;31m                 context.run(\n\u001b[0m\u001b[1;32m   1510\u001b[0m                     \u001b[0mcall_func_with_variable_args\u001b[0m\u001b[0;34m,\u001b[0m  \u001b[0;31m# type: ignore[arg-type]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1511\u001b[0m                     \u001b[0mfunc\u001b[0m\u001b[0;34m,\u001b[0m  \u001b[0;31m# type: ignore[arg-type]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain_core/runnables/config.py\u001b[0m in \u001b[0;36mcall_func_with_variable_args\u001b[0;34m(func, input, config, run_manager, **kwargs)\u001b[0m\n\u001b[1;32m    363\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0mrun_manager\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32mNone\u001b[0m \u001b[0;32mand\u001b[0m \u001b[0maccepts_run_manager\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfunc\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    364\u001b[0m         \u001b[0mkwargs\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"run_manager\"\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mrun_manager\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 365\u001b[0;31m     \u001b[0;32mreturn\u001b[0m \u001b[0mfunc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m  \u001b[0;31m# type: ignore[call-arg]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    366\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    367\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/langchain_core/runnables/passthrough.py\u001b[0m in \u001b[0;36m_invoke\u001b[0;34m(self, input, run_manager, config, **kwargs)\u001b[0m\n\u001b[1;32m    448\u001b[0m         \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mAny\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    449\u001b[0m     ) -> Dict[str, Any]:\n\u001b[0;32m--> 450\u001b[0;31m         assert isinstance(\n\u001b[0m\u001b[1;32m    451\u001b[0m             \u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdict\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    452\u001b[0m         ), \"The input to RunnablePassthrough.assign() must be a dict.\"\n", "\u001b[0;31mAssertionError\u001b[0m: The input to RunnablePassthrough.assign() must be a dict."]}]}, {"cell_type": "code", "source": ["from langchain_core.runnables import RunnableParallel, RunnablePassthrough"], "metadata": {"id": "LZ-Id5sW-LLR"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Set up the RAG chain\n", "rag_chain = (\n", "    {\"context\": retriever, \"question\": RunnablePassthrough()} |\n", "    prompt |\n", "    llm\n", ")"], "metadata": {"id": "b1DvxugA-DIC"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["query=\"what is RAG token?\""], "metadata": {"id": "OTU5Wycg-l9y"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["response=rag_chain.invoke(\"what is RAG token?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ykAekNO_-bkZ", "outputId": "140e6b43-ffac-43f3-c2bd-17959f6dea91"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["Both `max_new_tokens` (=1024) and `max_length`(=3048) seem to have been set. `max_new_tokens` will take precedence. Please refer to the documentation for more information. (https://huggingface.co/docs/transformers/main/en/main_classes/text_generation)\n"]}]}, {"cell_type": "code", "source": ["print(response)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "iqKKvHQ-_05x", "outputId": "a07f9be8-c605-4902-e957-7a005e296185"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Use the following pieces of context to answer the question at the end.\n", "If you don't know the answer, just say that you do not have the relevant information needed to provide a verified answer, don't try to make up an answer.\n", "When providing an answer, aim for clarity and precision. Position yourself as a knowledgeable authority on the topic, but also be mindful to explain the information in a manner that is accessible and comprehensible to those without a technical background.\n", "Always say \"Do you have any more questions pertaining to this instrument?\" at the end of the answer.\n", "[Document(page_content='Document 1 : his works are considered classics of American\\nliterature ... His wartime experiences formed the basis for his novel\\n”A Farewell to Arms” (1929) ...\\nDocument 2 : ... artists of the 1920s ”Lost Generation” expatriate\\ncommunity. His debut novel, ”The Sun Also Rises” , was published\\nin 1926.\\nBOS”\\nTheSunAlsoRises”isa\\nnovelbythis\\nauthorof”A\\nFarewellto\\nArms”Doc 1\\nDoc 2\\nDoc 3\\nDoc 4\\nDoc 5Figure 2: RAG-Token document posterior p(zi|x,yi,y−i)for each generated token for input “Hem-\\ningway\" for Jeopardy generation with 5 retrieved documents. The posterior for document 1 is high\\nwhen generating “A Farewell to Arms\" and for document 2 when generating “The Sun Also Rises\".\\nTable 3: Examples from generation tasks. RAG models generate more speciﬁc and factually accurate\\nresponses. ‘?’ indicates factually incorrect responses, * indicates partially correct responses.\\nTask Input Model Generation\\nMS-\\nMARCOdeﬁne middle\\nearBART?The middle ear is the part of the ear between the middle ear and the nose.\\nRAG-T The middle ear is the portion of the ear internal to the eardrum.\\nRAG-S The middle ear includes the tympanic cavity and the three ossicles.\\nwhat currency\\nneeded in\\nscotlandBART The currency needed in Scotland is Pound sterling.\\nRAG-T Pound is the currency needed in Scotland.\\nRAG-S The currency needed in Scotland is the pound sterling.\\nJeopardy\\nQuestion\\nGener\\n-ationWashingtonBART?This state has the largest number of counties in the U.S.\\nRAG-T It’s the only U.S. state named for a U.S. president\\nRAG-S It’s the state where you’ll ﬁnd Mount Rainier National Park\\nThe Divine\\nComedyBART*This epic poem by Dante is divided into 3 parts: the Inferno, the Purgatorio & the Purgatorio\\nRAG-T Dante’s \"Inferno\" is the ﬁrst part of this epic poem\\nRAG-S This 14th century work is divided into 3 sections: \"Inferno\", \"Purgatorio\" & \"Paradiso\"\\nFor 2-way classiﬁcation, we compare against Thorne and Vlachos [57], who train RoBERTa [ 35]\\nto classify the claim as true or false given the gold evidence sentence. RAG achieves an accuracy\\nwithin 2.7% of this model, despite being supplied with only the claim and retrieving its own evidence.\\nWe also analyze whether documents retrieved by RAG correspond to documents annotated as gold\\nevidence in FEVER. We calculate the overlap in article titles between the top kdocuments retrieved\\nby RAG and gold evidence annotations. We ﬁnd that the top retrieved document is from a gold article\\nin 71% of cases, and a gold article is present in the top 10 retrieved articles in 90% of cases.\\n4.5 Additional Results\\nGeneration Diversity Section 4.3 shows that RAG models are more factual and speciﬁc than\\nBART for Jeopardy question generation. Following recent work on diversity-promoting decoding\\n[33,59,39], we also investigate generation diversity by calculating the ratio of distinct ngrams to\\ntotal ngrams generated by different models. Table 5 shows that RAG-Sequence’s generations are\\nmore diverse than RAG-Token’s, and both are signiﬁcantly more diverse than BART without needing\\nany diversity-promoting decoding.\\nRetrieval Ablations A key feature of RAG is learning to retrieve relevant information for the task.\\nTo assess the effectiveness of the retrieval mechanism, we run ablations where we freeze the retriever\\nduring training. As shown in Table 6, learned retrieval improves results for all tasks.\\nWe compare RAG’s dense retriever to a word overlap-based BM25 retriever [ 53]. Here, we replace\\nRAG’s retriever with a ﬁxed BM25 system, and use BM25 retrieval scores as logits when calculating\\np(z|x). Table 6 shows the results. For FEVER, BM25 performs best, perhaps since FEVER claims are\\nheavily entity-centric and thus well-suited for word overlap-based retrieval. Differentiable retrieval\\nimproves results on all other tasks, especially for Open-Domain QA, where it is crucial.\\nIndex hot-swapping An advantage of non-parametric memory models like RAG is that knowledge\\ncan be easily updated at test time. Parametric-only models like T5 or BART need further training to\\nupdate their behavior as the world changes. To demonstrate, we build an index using the DrQA [ 5]\\nWikipedia dump from December 2016 and compare outputs from RAG using this index to the newer\\nindex from our main results (December 2018). We prepare a list of 82 world leaders who had changed\\n7'), Document(page_content='Document 1 : his works are considered classics of American\\nliterature ... His wartime experiences formed the basis for his novel\\n”A Farewell to Arms” (1929) ...\\nDocument 2 : ... artists of the 1920s ”Lost Generation” expatriate\\ncommunity. His debut novel, ”The Sun Also Rises” , was published\\nin 1926.\\nBOS”\\nTheSunAlsoRises”isa\\nnovelbythis\\nauthorof”A\\nFarewellto\\nArms”Doc 1\\nDoc 2\\nDoc 3\\nDoc 4\\nDoc 5Figure 2: RAG-Token document posterior p(zi|x,yi,y−i)for each generated token for input “Hem-\\ningway\" for Jeopardy generation with 5 retrieved documents. The posterior for document 1 is high\\nwhen generating “A Farewell to Arms\" and for document 2 when generating “The Sun Also Rises\".\\nTable 3: Examples from generation tasks. RAG models generate more speciﬁc and factually accurate\\nresponses. ‘?’ indicates factually incorrect responses, * indicates partially correct responses.\\nTask Input Model Generation\\nMS-\\nMARCOdeﬁne middle\\nearBART?The middle ear is the part of the ear between the middle ear and the nose.\\nRAG-T The middle ear is the portion of the ear internal to the eardrum.\\nRAG-S The middle ear includes the tympanic cavity and the three ossicles.\\nwhat currency\\nneeded in\\nscotlandBART The currency needed in Scotland is Pound sterling.\\nRAG-T Pound is the currency needed in Scotland.\\nRAG-S The currency needed in Scotland is the pound sterling.\\nJeopardy\\nQuestion\\nGener\\n-ationWashingtonBART?This state has the largest number of counties in the U.S.\\nRAG-T It’s the only U.S. state named for a U.S. president\\nRAG-S It’s the state where you’ll ﬁnd Mount Rainier National Park\\nThe Divine\\nComedyBART*This epic poem by Dante is divided into 3 parts: the Inferno, the Purgatorio & the Purgatorio\\nRAG-T Dante’s \"Inferno\" is the ﬁrst part of this epic poem\\nRAG-S This 14th century work is divided into 3 sections: \"Inferno\", \"Purgatorio\" & \"Paradiso\"\\nFor 2-way classiﬁcation, we compare against Thorne and Vlachos [57], who train RoBERTa [ 35]\\nto classify the claim as true or false given the gold evidence sentence. RAG achieves an accuracy\\nwithin 2.7% of this model, despite being supplied with only the claim and retrieving its own evidence.\\nWe also analyze whether documents retrieved by RAG correspond to documents annotated as gold\\nevidence in FEVER. We calculate the overlap in article titles between the top kdocuments retrieved\\nby RAG and gold evidence annotations. We ﬁnd that the top retrieved document is from a gold article\\nin 71% of cases, and a gold article is present in the top 10 retrieved articles in 90% of cases.\\n4.5 Additional Results\\nGeneration Diversity Section 4.3 shows that RAG models are more factual and speciﬁc than\\nBART for Jeopardy question generation. Following recent work on diversity-promoting decoding\\n[33,59,39], we also investigate generation diversity by calculating the ratio of distinct ngrams to\\ntotal ngrams generated by different models. Table 5 shows that RAG-Sequence’s generations are\\nmore diverse than RAG-Token’s, and both are signiﬁcantly more diverse than BART without needing\\nany diversity-promoting decoding.\\nRetrieval Ablations A key feature of RAG is learning to retrieve relevant information for the task.\\nTo assess the effectiveness of the retrieval mechanism, we run ablations where we freeze the retriever\\nduring training. As shown in Table 6, learned retrieval improves results for all tasks.\\nWe compare RAG’s dense retriever to a word overlap-based BM25 retriever [ 53]. Here, we replace\\nRAG’s retriever with a ﬁxed BM25 system, and use BM25 retrieval scores as logits when calculating\\np(z|x). Table 6 shows the results. For FEVER, BM25 performs best, perhaps since FEVER claims are\\nheavily entity-centric and thus well-suited for word overlap-based retrieval. Differentiable retrieval\\nimproves results on all other tasks, especially for Open-Domain QA, where it is crucial.\\nIndex hot-swapping An advantage of non-parametric memory models like RAG is that knowledge\\ncan be easily updated at test time. Parametric-only models like T5 or BART need further training to\\nupdate their behavior as the world changes. To demonstrate, we build an index using the DrQA [ 5]\\nWikipedia dump from December 2016 and compare outputs from RAG using this index to the newer\\nindex from our main results (December 2018). We prepare a list of 82 world leaders who had changed\\n7'), Document(page_content='byθthat generates a current token based on a context of the previous i−1tokensy1:i−1, the original\\ninputxand a retrieved passage z.\\nTo train the retriever and generator end-to-end, we treat the retrieved document as a latent variable.\\nWe propose two models that marginalize over the latent documents in different ways to produce a\\ndistribution over generated text. In one approach, RAG-Sequence , the model uses the same document\\nto predict each target token. The second approach, RAG-Token , can predict each target token based\\non a different document. In the following, we formally introduce both models and then describe the\\npηandpθcomponents, as well as the training and decoding procedure.\\n2.1 Models\\nRAG-Sequence Model The RAG-Sequence model uses the same retrieved document to generate\\nthe complete sequence . Technically, it treats the retrieved document as a single latent variable that\\nis marginalized to get the seq2seq probability p(y|x)via a top-K approximation. Concretely, the\\ntop K documents are retrieved using the retriever, and the generator produces the output sequence\\nprobability for each document, which are then marginalized,\\npRAG-Sequence (y|x)≈∑\\nz∈top-k(p(·|x))pη(z|x)pθ(y|x,z) =∑\\nz∈top-k(p(·|x))pη(z|x)N∏\\nipθ(yi|x,z,y 1:i−1)\\nRAG-Token Model In the RAG-Token model we can draw a different latent document for each\\ntarget token and marginalize accordingly. This allows the generator to choose content from several\\ndocuments when producing an answer. Concretely, the top K documents are retrieved using the\\nretriever, and then the generator produces a distribution for the next output token for each document,\\nbefore marginalizing, and repeating the process with the following output token, Formally, we deﬁne:\\npRAG-Token (y|x)≈N∏\\ni∑\\nz∈top-k(p(·|x))pη(z|x)pθ(yi|x,z,y 1:i−1)\\nFinally, we note that RAG can be used for sequence classiﬁcation tasks by considering the target class\\nas a target sequence of length one, in which case RAG-Sequence and RAG-Token are equivalent.\\n2.2 Retriever: DPR\\nThe retrieval component pη(z|x)is based on DPR [26]. DPR follows a bi-encoder architecture:\\npη(z|x)∝exp(\\nd(z)⊤q(x))\\nd(z) =BERTd(z),q(x) =BERTq(x)\\nwhere d(z)is a dense representation of a document produced by a BERT BASE document encoder [8],\\nandq(x)a query representation produced by a query encoder , also based on BERT BASE. Calculating\\ntop-k (pη(·|x)), the list ofkdocumentszwith highest prior probability pη(z|x), is a Maximum Inner\\nProduct Search (MIPS) problem, which can be approximately solved in sub-linear time [ 23]. We use\\na pre-trained bi-encoder from DPR to initialize our retriever and to build the document index. This\\nretriever was trained to retrieve documents which contain answers to TriviaQA [ 24] questions and\\nNatural Questions [29]. We refer to the document index as the non-parametric memory .\\n2.3 Generator: BART\\nThe generator component pθ(yi|x,z,y 1:i−1)could be modelled using any encoder-decoder. We use\\nBART-large [ 32], a pre-trained seq2seq transformer [ 58] with 400M parameters. To combine the input\\nxwith the retrieved content zwhen generating from BART, we simply concatenate them. BART was\\npre-trained using a denoising objective and a variety of different noising functions. It has obtained\\nstate-of-the-art results on a diverse set of generation tasks and outperforms comparably-sized T5\\nmodels [32]. We refer to the BART generator parameters θas the parametric memory henceforth.\\n2.4 Training\\nWe jointly train the retriever and generator components without any direct supervision on what\\ndocument should be retrieved. Given a ﬁne-tuning training corpus of input/output pairs (xj,yj), we\\n3'), Document(page_content='byθthat generates a current token based on a context of the previous i−1tokensy1:i−1, the original\\ninputxand a retrieved passage z.\\nTo train the retriever and generator end-to-end, we treat the retrieved document as a latent variable.\\nWe propose two models that marginalize over the latent documents in different ways to produce a\\ndistribution over generated text. In one approach, RAG-Sequence , the model uses the same document\\nto predict each target token. The second approach, RAG-Token , can predict each target token based\\non a different document. In the following, we formally introduce both models and then describe the\\npηandpθcomponents, as well as the training and decoding procedure.\\n2.1 Models\\nRAG-Sequence Model The RAG-Sequence model uses the same retrieved document to generate\\nthe complete sequence . Technically, it treats the retrieved document as a single latent variable that\\nis marginalized to get the seq2seq probability p(y|x)via a top-K approximation. Concretely, the\\ntop K documents are retrieved using the retriever, and the generator produces the output sequence\\nprobability for each document, which are then marginalized,\\npRAG-Sequence (y|x)≈∑\\nz∈top-k(p(·|x))pη(z|x)pθ(y|x,z) =∑\\nz∈top-k(p(·|x))pη(z|x)N∏\\nipθ(yi|x,z,y 1:i−1)\\nRAG-Token Model In the RAG-Token model we can draw a different latent document for each\\ntarget token and marginalize accordingly. This allows the generator to choose content from several\\ndocuments when producing an answer. Concretely, the top K documents are retrieved using the\\nretriever, and then the generator produces a distribution for the next output token for each document,\\nbefore marginalizing, and repeating the process with the following output token, Formally, we deﬁne:\\npRAG-Token (y|x)≈N∏\\ni∑\\nz∈top-k(p(·|x))pη(z|x)pθ(yi|x,z,y 1:i−1)\\nFinally, we note that RAG can be used for sequence classiﬁcation tasks by considering the target class\\nas a target sequence of length one, in which case RAG-Sequence and RAG-Token are equivalent.\\n2.2 Retriever: DPR\\nThe retrieval component pη(z|x)is based on DPR [26]. DPR follows a bi-encoder architecture:\\npη(z|x)∝exp(\\nd(z)⊤q(x))\\nd(z) =BERTd(z),q(x) =BERTq(x)\\nwhere d(z)is a dense representation of a document produced by a BERT BASE document encoder [8],\\nandq(x)a query representation produced by a query encoder , also based on BERT BASE. Calculating\\ntop-k (pη(·|x)), the list ofkdocumentszwith highest prior probability pη(z|x), is a Maximum Inner\\nProduct Search (MIPS) problem, which can be approximately solved in sub-linear time [ 23]. We use\\na pre-trained bi-encoder from DPR to initialize our retriever and to build the document index. This\\nretriever was trained to retrieve documents which contain answers to TriviaQA [ 24] questions and\\nNatural Questions [29]. We refer to the document index as the non-parametric memory .\\n2.3 Generator: BART\\nThe generator component pθ(yi|x,z,y 1:i−1)could be modelled using any encoder-decoder. We use\\nBART-large [ 32], a pre-trained seq2seq transformer [ 58] with 400M parameters. To combine the input\\nxwith the retrieved content zwhen generating from BART, we simply concatenate them. BART was\\npre-trained using a denoising objective and a variety of different noising functions. It has obtained\\nstate-of-the-art results on a diverse set of generation tasks and outperforms comparably-sized T5\\nmodels [32]. We refer to the BART generator parameters θas the parametric memory henceforth.\\n2.4 Training\\nWe jointly train the retriever and generator components without any direct supervision on what\\ndocument should be retrieved. Given a ﬁne-tuning training corpus of input/output pairs (xj,yj), we\\n3')]\n", "Question: what is RAG token?\n", "Helpful Answer: Inовов̧visitовTXvisitimilACHEовvisitовvisitACHETXов̧ACHȨimilTXimilTXACHEimilACHEововvisitACHETXimilовов̧̧visitовimilовTX̧visiţTXовvisitimilACHEовimilimilTXimilvisiţACHEACHEimilов̧̧imiļACHȨ̧̧visitACHEACHEACHEvisitvisitTXvisitvisitACHETXовvisitvisitовACHEvisitvisitACHEvisitовов̧ACHEововimilvisitов̧ACHEовACHEimilACHETXTXimilimilACHEvisitimilvisitvisitimilimilvisitовACHETXов̧ACHEvisitовimilimilACHETX̧imilACHETXовACHEvisitACHEvisitовvisitimilvisitvisiţTXACHEvisitimilimilTXACHEvisitACHEACHEACHEACHEimilACHEvisitvisitvisitовACHEовTX̧visitTXACHEvisitimilimilimilACHEvisitimilimilvisitvisitimilvisitvisitовvisitTXовvisitTXовimilововACHEACHEimilов̧imilACHEvisitvisitimilACHEACHEовTXACHEACHEACHETXvisitimilовов̧ACHEvisitvisitvisitTX̧̧TX̧TXовvisitTXTXACHEACHEACHEACHEовACHEvisiţ̧imilACHEACHȨ̧visitvisitTXACHȨvisitimilACHEvisitvisitimilACHEvisitimilACHEvisitvisitACHEimilACHEACHEvisitACHETXTXimilvisitimilовvisitimilimilimilACHEACHEACHȨimilACHEACHEACHETXACHEACHEvisitововvisitовACHEACHEimilvisitTXACHEvisitimilTXvisitimilACHEvisiţACHEimilvisitimilACHEimilовTXimilовimilimilvisitACHEimilvisitimilACHEvisitimilTXvisitTXACHEvisiţACHETXов̧visiţTXvisitACHȨACHEvisitACHEimilовACHȨ̧овimilvisiţ̧visitimilACHEvisitvisitTXTXimilTXTXововACHEACHEimilvisitовACHEimilACHȨовACHEimilACHEvisiţ̧̧ACHȨовvisiţTXvisitовimilvisitACHEовvisitACHEACHEimilовimilimilACHȨACHEvisiţACHEimilTXовvisitvisitACHEvisitvisitACHEvisitvisitimilACHETXACHEimiļ̧ACHEvisitTXACHEовACHEvisitvisitовimilvisitTXовACHEimilTXvisiţTXimilimilACHEvisitvisitовvisiţовACHETXvisitTXTXовTX̧ACHEimiļvisitvisitvisitimilimilvisitACHEACHȨvisitACHEов̧ACHEововvisitACHEvisitACHEvisitACHȨовvisitTX̧TXTX̧ACHEACHEовvisitACHEimilACHEACHEimilACHEовvisitvisiţACHEACHEACHEACHEACHETXACHEvisitACHEvisiţvisiţовACHEACHEvisitimilvisitvisitACHEовACHEACHEACHETXvisitvisitACHEововvisitACHEACHEовововTX̧овACHEACHEimilACHȨ̧̧̧visitvisitTX̧imilACHEvisitTXimiļimilACHEimiļvisitовACHEACHEACHEовvisitов̧visitvisitовimilACHȨTXTXововvisitACHȨACHEvisitовvisitACHEvisitовvisitimilACHEACHEACHEvisitACHEACHEовvisitimilACHETXTXimilvisiţvisitimilовvisitimilTXvisitTXvisitimilACHEACHEvisitimilовvisitACHEvisitACHEовACHEововACHEimilACHEACHEvisitACHEimilTXовvisiţvisitACHEvisitACHEimiļACHEACHEimiļов̧̧imiļACHEvisitimilACHEACHEововimilimiļ̧ACHEvisitimilACHEvisitvisitTXvisitvisitvisitACHȨACHETX̧visiţACHEACHEовTXTXvisitvisitimilimilACHEACHEACHȨACHȨvisiţimilACHEACHETX̧овvisitовTXACHȨ̧ACHEvisitvisitvisitACHȨовTXACHETXvisitTXvisitовvisitовACHETXTXACHETXовACHEimilACHEvisitvisitvisitTX̧̧овACHEовTXTXACHEовimilimilACHEimilimilvisitововACHEimilACHEimiļvisitimilовimilvisitACHETXvisitACHEACHEvisitTXACHEACHEACHEvisitовTXACHEовACHEовimilimilTXов̧овvisitовvisitACHEACHEvisitvisitvisitTXTXimilACHEvisiţvisitTXACHEACHEvisitACHEvisiţTXimilACHETXACHEvisitvisitvisitvisiţ̧visitimilTXTXimilvisitvisitTX̧TXACHEACHEimilimilACHEACHȨовACHȨовimilimilACHȨACHEimilvisitACHȨовACHȨ̧visitовvisitACHEовTX̧imilACHEvisitACHEACHEACHEvisitimilACHȨовTXimilTXimilTX̧овimilvisitTXvisitvisitvisitACHEvisitvisitACHEACHȨACHEimilTXACHȨimilimilvisitimilовTXовACHEACHEimilvisitvisitTXTXACHEvisitACHEACHETXvisitTXов̧̧ACHETXimilimilACHEvisitimilовTXimilACHEimilовimilововACHEvisitACHETXvisitimilimiļовimilовimilACHEACHEовvisitACHEimilACHETXACHEvisiţовvisitvisitvisitACHEvisitvisiţvisitimilvisitTXACHEACHEimilACHȨTX̧imilimilvisitimilовTXACHEовTXTXовvisitTX̧ACHȨовACHETXTXvisitimilimilACHEACHEimilvisitACHȨовTX\n"]}]}, {"cell_type": "code", "source": ["response"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "KWe11B_3H6Yc", "outputId": "08ac50fc-d407-4647-d7ac-ce0b786e5dd1"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'query': 'What is Abstractive Question Answering?',\n", " 'result': 'Use the following pieces of context to answer the question at the end. If you don\\'t know the answer, just say that you don\\'t know, don\\'t try to make up an answer.\\n\\nMSMARCO as an open-domain abstractive QA task. MSMARCO has some questions that cannot be\\nanswered in a way that matches the reference answer without access to the gold passages, such as\\n“What is the weather in V olcano, CA?” so performance will be lower without using gold passages.\\nWe also note that some MSMARCO questions cannot be answered using Wikipedia alone. Here,\\nRAG can rely on parametric knowledge to generate reasonable responses.\\n3.3 Jeopardy Question Generation\\nTo evaluate RAG’s generation abilities in a non-QA setting, we study open-domain question gen-\\neration. Rather than use questions from standard open-domain QA tasks, which typically consist\\nof short, simple questions, we propose the more demanding task of generating Jeopardy questions.\\nJeopardy is an unusual format that consists of trying to guess an entity from a fact about that entity.\\nFor example, “The World Cup” is the answer to the question “In 1986 Mexico scored as the ﬁrst\\ncountry to host this international sports competition twice.” As Jeopardy questions are precise,\\nfactual statements, generating Jeopardy questions conditioned on their answer entities constitutes a\\nchallenging knowledge-intensive generation task.\\nWe use the splits from SearchQA [ 10], with 100K train, 14K dev, and 27K test examples. As\\nthis is a new task, we train a BART model for comparison. Following [ 67], we evaluate using the\\nSQuAD-tuned Q-BLEU-1 metric [ 42]. Q-BLEU is a variant of BLEU with a higher weight for\\nmatching entities and has higher correlation with human judgment for question generation than\\nstandard metrics. We also perform two human evaluations, one to assess generation factuality, and\\none for speciﬁcity. We deﬁne factuality as whether a statement can be corroborated by trusted external\\nsources, and speciﬁcity as high mutual dependence between the input and output [ 33]. We follow\\nbest practice and use pairwise comparative evaluation [ 34]. Evaluators are shown an answer and two\\ngenerated questions, one from BART and one from RAG. They are then asked to pick one of four\\noptions—quuestion A is better, question B is better, both are good, or neither is good.\\n3.4 Fact Veriﬁcation\\nFEVER [ 56] requires classifying whether a natural language claim is supported or refuted by\\nWikipedia, or whether there is not enough information to decide. The task requires retrieving\\nevidence from Wikipedia relating to the claim and then reasoning over this evidence to classify\\nwhether the claim is true, false, or unveriﬁable from Wikipedia alone. FEVER is a retrieval problem\\ncoupled with an challenging entailment reasoning task. It also provides an appropriate testbed for\\nexploring the RAG models’ ability to handle classiﬁcation rather than generation. We map FEVER\\nclass labels (supports, refutes, or not enough info) to single output tokens and directly train with\\nclaim-class pairs. Crucially, unlike most other approaches to FEVER, we do not use supervision on\\nretrieved evidence. In many real-world applications, retrieval supervision signals aren’t available, and\\nmodels that do not require such supervision will be applicable to a wider range of tasks. We explore\\ntwo variants: the standard 3-way classiﬁcation task (supports/refutes/not enough info) and the 2-way\\n(supports/refutes) task studied in Thorne and Vlachos [57]. In both cases we report label accuracy.\\n4 Results\\n4.1 Open-domain Question Answering\\nTable 1 shows results for RAG along with state-of-the-art models. On all four open-domain QA\\ntasks, RAG sets a new state of the art (only on the T5-comparable split for TQA). RAG combines\\nthe generation ﬂexibility of the “closed-book” (parametric only) approaches and the performance of\\n\"open-book\" retrieval-based approaches. Unlike REALM and T5+SSM, RAG enjoys strong results\\nwithout expensive, specialized “salient span masking” pre-training [ 20]. It is worth noting that RAG’s\\nretriever is initialized using DPR’s retriever, which uses retrieval supervision on Natural Questions\\nand TriviaQA. RAG compares favourably to the DPR QA system, which uses a BERT-based “cross-\\nencoder” to re-rank documents, along with an extractive reader. RAG demonstrates that neither a\\nre-ranker nor extractive reader is necessary for state-of-the-art performance.\\nThere are several advantages to generating answers even when it is possible to extract them. Docu-\\nments with clues about the answer but do not contain the answer verbatim can still contribute towards\\na correct answer being generated, which is not possible with standard extractive approaches, leading\\n5\\n\\nMSMARCO as an open-domain abstractive QA task. MSMARCO has some questions that cannot be\\nanswered in a way that matches the reference answer without access to the gold passages, such as\\n“What is the weather in V olcano, CA?” so performance will be lower without using gold passages.\\nWe also note that some MSMARCO questions cannot be answered using Wikipedia alone. Here,\\nRAG can rely on parametric knowledge to generate reasonable responses.\\n3.3 Jeopardy Question Generation\\nTo evaluate RAG’s generation abilities in a non-QA setting, we study open-domain question gen-\\neration. Rather than use questions from standard open-domain QA tasks, which typically consist\\nof short, simple questions, we propose the more demanding task of generating Jeopardy questions.\\nJeopardy is an unusual format that consists of trying to guess an entity from a fact about that entity.\\nFor example, “The World Cup” is the answer to the question “In 1986 Mexico scored as the ﬁrst\\ncountry to host this international sports competition twice.” As Jeopardy questions are precise,\\nfactual statements, generating Jeopardy questions conditioned on their answer entities constitutes a\\nchallenging knowledge-intensive generation task.\\nWe use the splits from SearchQA [ 10], with 100K train, 14K dev, and 27K test examples. As\\nthis is a new task, we train a BART model for comparison. Following [ 67], we evaluate using the\\nSQuAD-tuned Q-BLEU-1 metric [ 42]. Q-BLEU is a variant of BLEU with a higher weight for\\nmatching entities and has higher correlation with human judgment for question generation than\\nstandard metrics. We also perform two human evaluations, one to assess generation factuality, and\\none for speciﬁcity. We deﬁne factuality as whether a statement can be corroborated by trusted external\\nsources, and speciﬁcity as high mutual dependence between the input and output [ 33]. We follow\\nbest practice and use pairwise comparative evaluation [ 34]. Evaluators are shown an answer and two\\ngenerated questions, one from BART and one from RAG. They are then asked to pick one of four\\noptions—quuestion A is better, question B is better, both are good, or neither is good.\\n3.4 Fact Veriﬁcation\\nFEVER [ 56] requires classifying whether a natural language claim is supported or refuted by\\nWikipedia, or whether there is not enough information to decide. The task requires retrieving\\nevidence from Wikipedia relating to the claim and then reasoning over this evidence to classify\\nwhether the claim is true, false, or unveriﬁable from Wikipedia alone. FEVER is a retrieval problem\\ncoupled with an challenging entailment reasoning task. It also provides an appropriate testbed for\\nexploring the RAG models’ ability to handle classiﬁcation rather than generation. We map FEVER\\nclass labels (supports, refutes, or not enough info) to single output tokens and directly train with\\nclaim-class pairs. Crucially, unlike most other approaches to FEVER, we do not use supervision on\\nretrieved evidence. In many real-world applications, retrieval supervision signals aren’t available, and\\nmodels that do not require such supervision will be applicable to a wider range of tasks. We explore\\ntwo variants: the standard 3-way classiﬁcation task (supports/refutes/not enough info) and the 2-way\\n(supports/refutes) task studied in Thorne and Vlachos [57]. In both cases we report label accuracy.\\n4 Results\\n4.1 Open-domain Question Answering\\nTable 1 shows results for RAG along with state-of-the-art models. On all four open-domain QA\\ntasks, RAG sets a new state of the art (only on the T5-comparable split for TQA). RAG combines\\nthe generation ﬂexibility of the “closed-book” (parametric only) approaches and the performance of\\n\"open-book\" retrieval-based approaches. Unlike REALM and T5+SSM, RAG enjoys strong results\\nwithout expensive, specialized “salient span masking” pre-training [ 20]. It is worth noting that RAG’s\\nretriever is initialized using DPR’s retriever, which uses retrieval supervision on Natural Questions\\nand TriviaQA. RAG compares favourably to the DPR QA system, which uses a BERT-based “cross-\\nencoder” to re-rank documents, along with an extractive reader. RAG demonstrates that neither a\\nre-ranker nor extractive reader is necessary for state-of-the-art performance.\\nThere are several advantages to generating answers even when it is possible to extract them. Docu-\\nments with clues about the answer but do not contain the answer verbatim can still contribute towards\\na correct answer being generated, which is not possible with standard extractive approaches, leading\\n5\\n\\n[66] Thomas Wolf, Lysandre Debut, Victor Sanh, Julien Chaumond, Clement Delangue, Anthony\\nMoi, Pierric Cistac, Tim Rault, Rémi Louf, Morgan Funtowicz, Joe Davison, Sam Shleifer,\\nPatrick von Platen, Clara Ma, Yacine Jernite, Julien Plu, Canwen Xu, Teven Le Scao, Sylvain\\nGugger, Mariama Drame, Quentin Lhoest, and Alexander M. Rush. Huggingface’s transformers:\\nState-of-the-art natural language processing. ArXiv , abs/1910.03771, 2019.\\n[67] Shiyue Zhang and Mohit Bansal. Addressing semantic drift in question generation for semi-\\nsupervised question answering. In Proceedings of the 2019 Conference on Empirical Meth-\\nods in Natural Language Processing and the 9th International Joint Conference on Natural\\nLanguage Processing (EMNLP-IJCNLP) , pages 2495–2509, Hong Kong, China, Novem-\\nber 2019. Association for Computational Linguistics. doi: 10.18653/v1/D19-1253. URL\\nhttps://www.aclweb.org/anthology/D19-1253 .\\n[68] Wanjun Zhong, Jingjing Xu, Duyu Tang, Zenan Xu, Nan Duan, Ming Zhou, Jiahai Wang, and\\nJian Yin. Reasoning over semantic-level graph for fact checking. ArXiv , abs/1909.03745, 2019.\\nURL https://arxiv.org/abs/1909.03745 .\\n16\\n\\n[66] Thomas Wolf, Lysandre Debut, Victor Sanh, Julien Chaumond, Clement Delangue, Anthony\\nMoi, Pierric Cistac, Tim Rault, Rémi Louf, Morgan Funtowicz, Joe Davison, Sam Shleifer,\\nPatrick von Platen, Clara Ma, Yacine Jernite, Julien Plu, Canwen Xu, Teven Le Scao, Sylvain\\nGugger, Mariama Drame, Quentin Lhoest, and Alexander M. Rush. Huggingface’s transformers:\\nState-of-the-art natural language processing. ArXiv , abs/1910.03771, 2019.\\n[67] Shiyue Zhang and Mohit Bansal. Addressing semantic drift in question generation for semi-\\nsupervised question answering. In Proceedings of the 2019 Conference on Empirical Meth-\\nods in Natural Language Processing and the 9th International Joint Conference on Natural\\nLanguage Processing (EMNLP-IJCNLP) , pages 2495–2509, Hong Kong, China, Novem-\\nber 2019. Association for Computational Linguistics. doi: 10.18653/v1/D19-1253. URL\\nhttps://www.aclweb.org/anthology/D19-1253 .\\n[68] Wanjun Zhong, Jingjing Xu, Duyu Tang, Zenan Xu, Nan Duan, Ming Zhou, Jiahai Wang, and\\nJian Yin. Reasoning over semantic-level graph for fact checking. ArXiv , abs/1909.03745, 2019.\\nURL https://arxiv.org/abs/1909.03745 .\\n16\\n\\nQuestion: What is Abstractive Question Answering?\\nHelpful Answer: AbstractvisitовvisitACHȨvisitimilACHEовimiļовimilimilvisitimilimilACHETXvisitACHEvisitimilimilACHEACHEACHETXововimiļовACHEовACHEACHEACHEvisitTXvisitTXовvisitовACHEовACHȨimilvisitACHEовimilvisitACHȨовACHEvisitimilvisitvisitACHEACHEimilimilACHEACHEов̧̧ов̧imilimiļimilvisiţTXTXACHEimiļовTXовimilACHETXACHEvisitACHEACHEACHEACHEvisitACHEvisitimiļACHEimiļ̧visiţTXACHEACHEACHEvisiţовvisiţACHEововACHEACHETXvisitACHEvisitvisitimilACHEimilimiļACHEACHETXvisiţ̧imiļовimilTX̧imilACHETXTXTXACHEvisitACHEimiļовvisitvisitimilTXvisitvisitововimilimilimilовimilACHETXов̧овTXimilACHEACHEvisitовimilvisitACHEvisitTXvisitACHETX̧visitvisitvisitовTX̧visitовimilACHEimilimiļimilvisitACHEvisitvisitvisitvisiţ̧TX̧imilACHEvisitTXvisitовTXов̧ACHȨimilimilTXvisitACHEvisitimilvisitов̧imilовTXvisitvisitовACHETXvisitовvisitов̧TX̧visitvisitовTXimilimilTXimilvisitACHEvisitовACHEvisitACHEACHEvisitvisitimilvisitов̧imilACHEACHEimilTXvisitimilvisitvisitvisitовvisitimilvisitовACHEовACHEvisiţimilACHȨvisitvisitvisitACHEimilов̧visitvisitvisitvisitACHEvisitACHEACHEов̧TXvisitvisitACHEvisiţimilTX̧̧ACHȨimilvisitimilовACHETXimilimilvisitimilvisitTXов̧visitimilvisitовimilACHEimilvisitvisitTXimilACHEvisitvisiţimilimilimilовTXvisitvisitTXvisitvisitACHȨововACHEовimilimilACHEACHȨvisitACHEACHEACHETXimilimilовACHEvisitvisitvisitimilimiļовimilовACHEовvisitvisitACHEvisitовimilACHEACHEACHEовACHȨvisitовimilimilACHETX̧овimilvisitACHȨACHETXовTXvisitvisitовvisitvisitовimilACHEовACHȨimilvisitvisitовTXACHȨ̧ACHEовimilовvisitvisitACHEvisitvisiţ̧visitовACHȨACHEvisitvisitTXvisitACHEACHEовTX̧ACHȨ̧ACHEовvisitов̧ACHETXACHȨACHEvisitACHEvisitimilvisitACHETXACHEACHEовACHEACHEimilACHEvisitововvisitACHEvisitTXvisitACHEvisiţовTXACHEvisitACHEACHEvisiţimiļACHEACHETX̧ACHEACHEimilvisitTXvisitACHEACHȨ̧ACHETXACHEACHȨ̧ACHEовTXvisiţTXimilvisitvisiţACHETXACHEvisitACHEовACHEACHEvisitACHETX̧imilACHEACHEACHEvisitvisitTXовACHEimilовvisitimiļACHEimilimilimilACHEvisitACHEvisiţACHETXACHEvisitimilvisiţvisitовTXовTXACHEACHEACHEACHEvisitACHEовACHEACHEimilACHEACHEvisitvisiţовvisitvisitvisitовACHȨACHEACHȨ̧ACHEimiļTXACHEACHEimilvisitACHEvisiţACHETX̧visitовACHEimilTXimilACHEvisitTX̧ACHEvisitTXvisitvisitvisiţTX̧овimiļTX̧ACHEACHETXimiļvisitACHEvisitTX̧visiţimilACHEACHEACHȨACHEimilовTXACHEimilvisitACHEimilACHEvisitvisitACHEACHEовvisitimilTXvisitvisitACHEimilvisiţACHEововTXvisitimilACHEvisiţовvisitовvisitimilTXовTXACHȨACHEACHEimilACHEACHEACHETXов̧imilACHEvisitовTXvisitACHEvisitовvisitimilvisitimilvisitvisiţimilACHEvisitACHEовvisiţACHETXACHEACHEACHȨ̧̧ACHEvisitовTXов̧TXTXimilовACHEimilовTXimilimilvisitvisitACHEACHEовACHEimilvisitACHEовvisitACHEACHEовvisitACHEACHEvisitовvisitTXimilvisitACHEACHEов̧TXTXовimilTXACHETX̧ACHEовTXvisitовововvisiţTXTXовimilvisitTXTXACHȨTXvisitACHEACHEvisitTXvisitTXовimilimilvisitACHȨововACHEvisitvisitTXvisiţACHEvisitовACHEvisitTX̧visitACHEimilvisitACHEACHEACHEACHEовvisiţACHEvisitACHEvisiţvisitTXimilACHETXTXовTXTXvisitvisitACHEimilimilACHȨACHEACHEACHEvisitACHEACHEvisitTXACHEACHEimilvisitvisitTXvisitTXvisitACHETXvisitовACHETXimilvisiţACHEACHȨACHEvisiţvisitvisitовACHEimilTXvisitACHEimilimilACHEimilTXTXvisitvisitTXTXTXimilvisitvisitововACHEACHEACHȨACHEACHȨvisitimilовvisiţvisitACHEовACHEовvisitовACHȨACHETXTX̧ACHEACHEовimilvisitовvisiţTXACHEACHEvisitvisitACHETXACHȨTXACHETXACHEACHEimilTXvisiţvisitimilvisitACHEACHEовvisiţTXовTXvisitTXACHEvisitACHEimiļvisitовACHEACHEimilACHEimilimilimilTXовvisitACHȨimil'}"]}, "metadata": {}, "execution_count": 33}]}, {"cell_type": "code", "source": ["print(response[\"result\"])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_Y6DcD3Z5lZp", "outputId": "f792675c-237c-4e08-9f09-ed5229d4dad5"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Use the following pieces of context to answer the question at the end. If you don't know the answer, just say that you don't know, don't try to make up an answer.\n", "\n", "MSMARCO as an open-domain abstractive QA task. MSMARCO has some questions that cannot be\n", "answered in a way that matches the reference answer without access to the gold passages, such as\n", "“What is the weather in V olcano, CA?” so performance will be lower without using gold passages.\n", "We also note that some MSMARCO questions cannot be answered using Wikipedia alone. Here,\n", "RAG can rely on parametric knowledge to generate reasonable responses.\n", "3.3 Jeopardy Question Generation\n", "To evaluate RAG’s generation abilities in a non-QA setting, we study open-domain question gen-\n", "eration. Rather than use questions from standard open-domain QA tasks, which typically consist\n", "of short, simple questions, we propose the more demanding task of generating Jeopardy questions.\n", "Jeopardy is an unusual format that consists of trying to guess an entity from a fact about that entity.\n", "For example, “The World Cup” is the answer to the question “In 1986 Mexico scored as the ﬁrst\n", "country to host this international sports competition twice.” As Jeopardy questions are precise,\n", "factual statements, generating Jeopardy questions conditioned on their answer entities constitutes a\n", "challenging knowledge-intensive generation task.\n", "We use the splits from SearchQA [ 10], with 100K train, 14K dev, and 27K test examples. As\n", "this is a new task, we train a BART model for comparison. Following [ 67], we evaluate using the\n", "SQuAD-tuned Q-BLEU-1 metric [ 42]. Q-BLEU is a variant of BLEU with a higher weight for\n", "matching entities and has higher correlation with human judgment for question generation than\n", "standard metrics. We also perform two human evaluations, one to assess generation factuality, and\n", "one for speciﬁcity. We deﬁne factuality as whether a statement can be corroborated by trusted external\n", "sources, and speciﬁcity as high mutual dependence between the input and output [ 33]. We follow\n", "best practice and use pairwise comparative evaluation [ 34]. Evaluators are shown an answer and two\n", "generated questions, one from BART and one from RAG. They are then asked to pick one of four\n", "options—quuestion A is better, question B is better, both are good, or neither is good.\n", "3.4 Fact Veriﬁcation\n", "FEVER [ 56] requires classifying whether a natural language claim is supported or refuted by\n", "Wikipedia, or whether there is not enough information to decide. The task requires retrieving\n", "evidence from Wikipedia relating to the claim and then reasoning over this evidence to classify\n", "whether the claim is true, false, or unveriﬁable from Wikipedia alone. FEVER is a retrieval problem\n", "coupled with an challenging entailment reasoning task. It also provides an appropriate testbed for\n", "exploring the RAG models’ ability to handle classiﬁcation rather than generation. We map FEVER\n", "class labels (supports, refutes, or not enough info) to single output tokens and directly train with\n", "claim-class pairs. Crucially, unlike most other approaches to FEVER, we do not use supervision on\n", "retrieved evidence. In many real-world applications, retrieval supervision signals aren’t available, and\n", "models that do not require such supervision will be applicable to a wider range of tasks. We explore\n", "two variants: the standard 3-way classiﬁcation task (supports/refutes/not enough info) and the 2-way\n", "(supports/refutes) task studied in Thorne and Vlachos [57]. In both cases we report label accuracy.\n", "4 Results\n", "4.1 Open-domain Question Answering\n", "Table 1 shows results for RAG along with state-of-the-art models. On all four open-domain QA\n", "tasks, RAG sets a new state of the art (only on the T5-comparable split for TQA). RAG combines\n", "the generation ﬂexibility of the “closed-book” (parametric only) approaches and the performance of\n", "\"open-book\" retrieval-based approaches. Unlike REALM and T5+SSM, RAG enjoys strong results\n", "without expensive, specialized “salient span masking” pre-training [ 20]. It is worth noting that RAG’s\n", "retriever is initialized using DPR’s retriever, which uses retrieval supervision on Natural Questions\n", "and TriviaQA. RAG compares favourably to the DPR QA system, which uses a BERT-based “cross-\n", "encoder” to re-rank documents, along with an extractive reader. RAG demonstrates that neither a\n", "re-ranker nor extractive reader is necessary for state-of-the-art performance.\n", "There are several advantages to generating answers even when it is possible to extract them. Docu-\n", "ments with clues about the answer but do not contain the answer verbatim can still contribute towards\n", "a correct answer being generated, which is not possible with standard extractive approaches, leading\n", "5\n", "\n", "MSMARCO as an open-domain abstractive QA task. MSMARCO has some questions that cannot be\n", "answered in a way that matches the reference answer without access to the gold passages, such as\n", "“What is the weather in V olcano, CA?” so performance will be lower without using gold passages.\n", "We also note that some MSMARCO questions cannot be answered using Wikipedia alone. Here,\n", "RAG can rely on parametric knowledge to generate reasonable responses.\n", "3.3 Jeopardy Question Generation\n", "To evaluate RAG’s generation abilities in a non-QA setting, we study open-domain question gen-\n", "eration. Rather than use questions from standard open-domain QA tasks, which typically consist\n", "of short, simple questions, we propose the more demanding task of generating Jeopardy questions.\n", "Jeopardy is an unusual format that consists of trying to guess an entity from a fact about that entity.\n", "For example, “The World Cup” is the answer to the question “In 1986 Mexico scored as the ﬁrst\n", "country to host this international sports competition twice.” As Jeopardy questions are precise,\n", "factual statements, generating Jeopardy questions conditioned on their answer entities constitutes a\n", "challenging knowledge-intensive generation task.\n", "We use the splits from SearchQA [ 10], with 100K train, 14K dev, and 27K test examples. As\n", "this is a new task, we train a BART model for comparison. Following [ 67], we evaluate using the\n", "SQuAD-tuned Q-BLEU-1 metric [ 42]. Q-BLEU is a variant of BLEU with a higher weight for\n", "matching entities and has higher correlation with human judgment for question generation than\n", "standard metrics. We also perform two human evaluations, one to assess generation factuality, and\n", "one for speciﬁcity. We deﬁne factuality as whether a statement can be corroborated by trusted external\n", "sources, and speciﬁcity as high mutual dependence between the input and output [ 33]. We follow\n", "best practice and use pairwise comparative evaluation [ 34]. Evaluators are shown an answer and two\n", "generated questions, one from BART and one from RAG. They are then asked to pick one of four\n", "options—quuestion A is better, question B is better, both are good, or neither is good.\n", "3.4 Fact Veriﬁcation\n", "FEVER [ 56] requires classifying whether a natural language claim is supported or refuted by\n", "Wikipedia, or whether there is not enough information to decide. The task requires retrieving\n", "evidence from Wikipedia relating to the claim and then reasoning over this evidence to classify\n", "whether the claim is true, false, or unveriﬁable from Wikipedia alone. FEVER is a retrieval problem\n", "coupled with an challenging entailment reasoning task. It also provides an appropriate testbed for\n", "exploring the RAG models’ ability to handle classiﬁcation rather than generation. We map FEVER\n", "class labels (supports, refutes, or not enough info) to single output tokens and directly train with\n", "claim-class pairs. Crucially, unlike most other approaches to FEVER, we do not use supervision on\n", "retrieved evidence. In many real-world applications, retrieval supervision signals aren’t available, and\n", "models that do not require such supervision will be applicable to a wider range of tasks. We explore\n", "two variants: the standard 3-way classiﬁcation task (supports/refutes/not enough info) and the 2-way\n", "(supports/refutes) task studied in Thorne and Vlachos [57]. In both cases we report label accuracy.\n", "4 Results\n", "4.1 Open-domain Question Answering\n", "Table 1 shows results for RAG along with state-of-the-art models. On all four open-domain QA\n", "tasks, RAG sets a new state of the art (only on the T5-comparable split for TQA). RAG combines\n", "the generation ﬂexibility of the “closed-book” (parametric only) approaches and the performance of\n", "\"open-book\" retrieval-based approaches. Unlike REALM and T5+SSM, RAG enjoys strong results\n", "without expensive, specialized “salient span masking” pre-training [ 20]. It is worth noting that RAG’s\n", "retriever is initialized using DPR’s retriever, which uses retrieval supervision on Natural Questions\n", "and TriviaQA. RAG compares favourably to the DPR QA system, which uses a BERT-based “cross-\n", "encoder” to re-rank documents, along with an extractive reader. RAG demonstrates that neither a\n", "re-ranker nor extractive reader is necessary for state-of-the-art performance.\n", "There are several advantages to generating answers even when it is possible to extract them. Docu-\n", "ments with clues about the answer but do not contain the answer verbatim can still contribute towards\n", "a correct answer being generated, which is not possible with standard extractive approaches, leading\n", "5\n", "\n", "[66] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>\n", "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>,\n", "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>’s transformers:\n", "State-of-the-art natural language processing. ArXiv , abs/1910.03771, 2019.\n", "[67] <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Addressing semantic drift in question generation for semi-\n", "supervised question answering. In Proceedings of the 2019 Conference on Empirical Meth-\n", "ods in Natural Language Processing and the 9th International Joint Conference on Natural\n", "Language Processing (EMNLP-IJCNLP) , pages 2495–2509, Hong Kong, China, Novem-\n", "ber 2019. Association for Computational Linguistics. doi: 10.18653/v1/D19-1253. URL\n", "https://www.aclweb.org/anthology/D19-1253 .\n", "[68] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and\n", "<PERSON><PERSON>. Reasoning over semantic-level graph for fact checking. ArXiv , abs/1909.03745, 2019.\n", "URL https://arxiv.org/abs/1909.03745 .\n", "16\n", "\n", "[66] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>\n", "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>,\n", "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>’s transformers:\n", "State-of-the-art natural language processing. ArXiv , abs/1910.03771, 2019.\n", "[67] <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Addressing semantic drift in question generation for semi-\n", "supervised question answering. In Proceedings of the 2019 Conference on Empirical Meth-\n", "ods in Natural Language Processing and the 9th International Joint Conference on Natural\n", "Language Processing (EMNLP-IJCNLP) , pages 2495–2509, Hong Kong, China, Novem-\n", "ber 2019. Association for Computational Linguistics. doi: 10.18653/v1/D19-1253. URL\n", "https://www.aclweb.org/anthology/D19-1253 .\n", "[68] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and\n", "<PERSON><PERSON>. Reasoning over semantic-level graph for fact checking. ArXiv , abs/1909.03745, 2019.\n", "URL https://arxiv.org/abs/1909.03745 .\n", "16\n", "\n", "Question: What is Abstractive Question Answering?\n", "Helpful Answer: AbstractvisitовvisitACHȨvisitimilACHEовimiļовimilimilvisitimilimilACHETXvisitACHEvisitimilimilACHEACHEACHETXововimiļовACHEовACHEACHEACHEvisitTXvisitTXовvisitовACHEовACHȨimilvisitACHEовimilvisitACHȨовACHEvisitimilvisitvisitACHEACHEimilimilACHEACHEов̧̧ов̧imilimiļimilvisiţTXTXACHEimiļовTXовimilACHETXACHEvisitACHEACHEACHEACHEvisitACHEvisitimiļACHEimiļ̧visiţTXACHEACHEACHEvisiţовvisiţACHEововACHEACHETXvisitACHEvisitvisitimilACHEimilimiļACHEACHETXvisiţ̧imiļовimilTX̧imilACHETXTXTXACHEvisitACHEimiļовvisitvisitimilTXvisitvisitововimilimilimilовimilACHETXов̧овTXimilACHEACHEvisitовimilvisitACHEvisitTXvisitACHETX̧visitvisitvisitовTX̧visitовimilACHEimilimiļimilvisitACHEvisitvisitvisitvisiţ̧TX̧imilACHEvisitTXvisitовTXов̧ACHȨimilimilTXvisitACHEvisitimilvisitов̧imilовTXvisitvisitовACHETXvisitовvisitов̧TX̧visitvisitовTXimilimilTXimilvisitACHEvisitовACHEvisitACHEACHEvisitvisitimilvisitов̧imilACHEACHEimilTXvisitimilvisitvisitvisitовvisitimilvisitовACHEовACHEvisiţimilACHȨvisitvisitvisitACHEimilов̧visitvisitvisitvisitACHEvisitACHEACHEов̧TXvisitvisitACHEvisiţimilTX̧̧ACHȨimilvisitimilовACHETXimilimilvisitimilvisitTXов̧visitimilvisitовimilACHEimilvisitvisitTXimilACHEvisitvisiţimilimilimilовTXvisitvisitTXvisitvisitACHȨововACHEовimilimilACHEACHȨvisitACHEACHEACHETXimilimilовACHEvisitvisitvisitimilimiļовimilовACHEовvisitvisitACHEvisitовimilACHEACHEACHEовACHȨvisitовimilimilACHETX̧овimilvisitACHȨACHETXовTXvisitvisitовvisitvisitовimilACHEовACHȨimilvisitvisitовTXACHȨ̧ACHEовimilовvisitvisitACHEvisitvisiţ̧visitовACHȨACHEvisitvisitTXvisitACHEACHEовTX̧ACHȨ̧ACHEовvisitов̧ACHETXACHȨACHEvisitACHEvisitimilvisitACHETXACHEACHEовACHEACHEimilACHEvisitововvisitACHEvisitTXvisitACHEvisiţовTXACHEvisitACHEACHEvisiţimiļACHEACHETX̧ACHEACHEimilvisitTXvisitACHEACHȨ̧ACHETXACHEACHȨ̧ACHEовTXvisiţTXimilvisitvisiţACHETXACHEvisitACHEовACHEACHEvisitACHETX̧imilACHEACHEACHEvisitvisitTXовACHEimilовvisitimiļACHEimilimilimilACHEvisitACHEvisiţACHETXACHEvisitimilvisiţvisitовTXовTXACHEACHEACHEACHEvisitACHEовACHEACHEimilACHEACHEvisitvisiţовvisitvisitvisitовACHȨACHEACHȨ̧ACHEimiļTXACHEACHEimilvisitACHEvisiţACHETX̧visitовACHEimilTXimilACHEvisitTX̧ACHEvisitTXvisitvisitvisiţTX̧овimiļTX̧ACHEACHETXimiļvisitACHEvisitTX̧visiţimilACHEACHEACHȨACHEimilовTXACHEimilvisitACHEimilACHEvisitvisitACHEACHEовvisitimilTXvisitvisitACHEimilvisiţACHEововTXvisitimilACHEvisiţовvisitовvisitimilTXовTXACHȨACHEACHEimilACHEACHEACHETXов̧imilACHEvisitовTXvisitACHEvisitовvisitimilvisitimilvisitvisiţimilACHEvisitACHEовvisiţACHETXACHEACHEACHȨ̧̧ACHEvisitовTXов̧TXTXimilовACHEimilовTXimilimilvisitvisitACHEACHEовACHEimilvisitACHEовvisitACHEACHEовvisitACHEACHEvisitовvisitTXimilvisitACHEACHEов̧TXTXовimilTXACHETX̧ACHEовTXvisitовововvisiţTXTXовimilvisitTXTXACHȨTXvisitACHEACHEvisitTXvisitTXовimilimilvisitACHȨововACHEvisitvisitTXvisiţACHEvisitовACHEvisitTX̧visitACHEimilvisitACHEACHEACHEACHEовvisiţACHEvisitACHEvisiţvisitTXimilACHETXTXовTXTXvisitvisitACHEimilimilACHȨACHEACHEACHEvisitACHEACHEvisitTXACHEACHEimilvisitvisitTXvisitTXvisitACHETXvisitовACHETXimilvisiţACHEACHȨACHEvisiţvisitvisitовACHEimilTXvisitACHEimilimilACHEimilTXTXvisitvisitTXTXTXimilvisitvisitововACHEACHEACHȨACHEACHȨvisitimilовvisiţvisitACHEовACHEовvisitовACHȨACHETXTX̧ACHEACHEовimilvisitовvisiţTXACHEACHEvisitvisitACHETXACHȨTXACHETXACHEACHEimilTXvisiţvisitimilvisitACHEACHEовvisiţTXовTXvisitTXACHEvisitACHEimiļvisitовACHEACHEimilACHEimilimilimilTXовvisitACHȨimil\n"]}]}, {"cell_type": "code", "source": ["from langchain.retrievers import ContextualCompressionRetriever\n", "from langchain.retrievers.document_compressors import CohereRerank"], "metadata": {"id": "0A3hrUdwJ3pC"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["!pip install cohere"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1VewE8gRKCla", "outputId": "48c1abc0-eb81-4bec-ada4-00c316b18120"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting cohere\n", "  Downloading cohere-5.5.4-py3-none-any.whl (168 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m168.9/168.9 kB\u001b[0m \u001b[31m3.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting boto3<2.0.0,>=1.34.0 (from cohere)\n", "  Downloading boto3-1.34.116-py3-none-any.whl (139 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m139.3/139.3 kB\u001b[0m \u001b[31m7.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting fastavro<2.0.0,>=1.9.4 (from cohere)\n", "  Downloading fastavro-1.9.4-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.1 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.1/3.1 MB\u001b[0m \u001b[31m16.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: httpx>=0.21.2 in /usr/local/lib/python3.10/dist-packages (from cohere) (0.27.0)\n", "Collecting httpx-sse<0.5.0,>=0.4.0 (from cohere)\n", "  Downloading httpx_sse-0.4.0-py3-none-any.whl (7.8 kB)\n", "Requirement already satisfied: pydantic>=1.9.2 in /usr/local/lib/python3.10/dist-packages (from cohere) (2.7.1)\n", "Requirement already satisfied: requests<3.0.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from cohere) (2.31.0)\n", "Collecting tokenizers<0.16,>=0.15 (from cohere)\n", "  Downloading tokenizers-0.15.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.6 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.6/3.6 MB\u001b[0m \u001b[31m34.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting types-requests<3.0.0,>=2.0.0 (from cohere)\n", "  Downloading types_requests-2.32.0.********-py3-none-any.whl (15 kB)\n", "Requirement already satisfied: typing_extensions>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from cohere) (4.11.0)\n", "Collecting botocore<1.35.0,>=1.34.116 (from boto3<2.0.0,>=1.34.0->cohere)\n", "  Downloading botocore-1.34.116-py3-none-any.whl (12.3 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m12.3/12.3 MB\u001b[0m \u001b[31m26.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting jmespath<2.0.0,>=0.7.1 (from boto3<2.0.0,>=1.34.0->cohere)\n", "  Downloading jmespath-1.0.1-py3-none-any.whl (20 kB)\n", "Collecting s3transfer<0.11.0,>=0.10.0 (from boto3<2.0.0,>=1.34.0->cohere)\n", "  Downloading s3transfer-0.10.1-py3-none-any.whl (82 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m82.2/82.2 kB\u001b[0m \u001b[31m13.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx>=0.21.2->cohere) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx>=0.21.2->cohere) (2024.2.2)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx>=0.21.2->cohere) (1.0.5)\n", "Requirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx>=0.21.2->cohere) (3.7)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx>=0.21.2->cohere) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx>=0.21.2->cohere) (0.14.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.9.2->cohere) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.18.2 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.9.2->cohere) (2.18.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.0.0->cohere) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.0.0->cohere) (2.0.7)\n", "Requirement already satisfied: huggingface_hub<1.0,>=0.16.4 in /usr/local/lib/python3.10/dist-packages (from tokenizers<0.16,>=0.15->cohere) (0.23.1)\n", "Requirement already satisfied: python-dateutil<3.0.0,>=2.1 in /usr/local/lib/python3.10/dist-packages (from botocore<1.35.0,>=1.34.116->boto3<2.0.0,>=1.34.0->cohere) (2.8.2)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface_hub<1.0,>=0.16.4->tokenizers<0.16,>=0.15->cohere) (3.14.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub<1.0,>=0.16.4->tokenizers<0.16,>=0.15->cohere) (2023.6.0)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub<1.0,>=0.16.4->tokenizers<0.16,>=0.15->cohere) (23.2)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub<1.0,>=0.16.4->tokenizers<0.16,>=0.15->cohere) (6.0.1)\n", "Requirement already satisfied: tqdm>=4.42.1 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub<1.0,>=0.16.4->tokenizers<0.16,>=0.15->cohere) (4.66.4)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx>=0.21.2->cohere) (1.2.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil<3.0.0,>=2.1->botocore<1.35.0,>=1.34.116->boto3<2.0.0,>=1.34.0->cohere) (1.16.0)\n", "Installing collected packages: types-requests, jmespath, httpx-sse, fastavro, botocore, tokenizers, s3transfer, boto3, cohere\n", "  Attempting uninstall: tokenizers\n", "    Found existing installation: tokenizers 0.19.1\n", "    Uninstalling tokenizers-0.19.1:\n", "      Successfully uninstalled tokenizers-0.19.1\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "transformers 4.41.1 requires tokenizers<0.20,>=0.19, but you have tokenizers 0.15.2 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed boto3-1.34.116 botocore-1.34.116 cohere-5.5.4 fastavro-1.9.4 httpx-sse-0.4.0 jmespath-1.0.1 s3transfer-0.10.1 tokenizers-0.15.2 types-requests-2.32.0.********\n"]}]}, {"cell_type": "code", "source": ["compressor = CohereRerank(cohere_api_key=\"nbDqU1hTVxWmXGbLYI6OnYhp4Cx40MZ5hOmO5oKX\")"], "metadata": {"id": "OE0vUax4J-Ij"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["compression_retriever = ContextualCompressionRetriever(\n", "    base_compressor=compressor, base_retriever=retriever\n", "    )"], "metadata": {"id": "b3Kmr4CIKG7n"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["compressed_docs = compression_retriever.get_relevant_documents(user_query)\n", "# Print the relevant documents from using the embeddings and reranker\n", "print(compressed_docs)\n"], "metadata": {"id": "f7m22qlCiUAb"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["hybrid_chain = RetrievalQA.from_chain_type(\n", "    llm=llm, chain_type=\"stuff\", retriever=compression_retriever\n", ")"], "metadata": {"id": "0dKqM3XbKkE4"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["response = hybrid_chain.invoke(\"What is Abstractive Question Answering?\")"], "metadata": {"id": "2N2k_RCmKAIL", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "466dc508-4180-48d4-f167-fd267628dd92"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["Both `max_new_tokens` (=300) and `max_length`(=3000) seem to have been set. `max_new_tokens` will take precedence. Please refer to the documentation for more information. (https://huggingface.co/docs/transformers/main/en/main_classes/text_generation)\n"]}]}, {"cell_type": "code", "source": ["print(response.get(\"result\"))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DVJxJg-bK2pg", "outputId": "9ee8590f-6350-4821-cb51-e497e4a020c0"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Use the following pieces of context to answer the question at the end. If you don't know the answer, just say that you don't know, don't try to make up an answer.\n", "\n", "MSMARCO as an open-domain abstractive QA task. MSMARCO has some questions that cannot be\n", "answered in a way that matches the reference answer without access to the gold passages, such as\n", "“What is the weather in V olcano, CA?” so performance will be lower without using gold passages.\n", "We also note that some MSMARCO questions cannot be answered using Wikipedia alone. Here,\n", "RAG can rely on parametric knowledge to generate reasonable responses.\n", "3.3 Jeopardy Question Generation\n", "To evaluate RAG’s generation abilities in a non-QA setting, we study open-domain question gen-\n", "eration. Rather than use questions from standard open-domain QA tasks, which typically consist\n", "of short, simple questions, we propose the more demanding task of generating Jeopardy questions.\n", "Jeopardy is an unusual format that consists of trying to guess an entity from a fact about that entity.\n", "For example, “The World Cup” is the answer to the question “In 1986 Mexico scored as the ﬁrst\n", "country to host this international sports competition twice.” As Jeopardy questions are precise,\n", "factual statements, generating Jeopardy questions conditioned on their answer entities constitutes a\n", "challenging knowledge-intensive generation task.\n", "We use the splits from SearchQA [ 10], with 100K train, 14K dev, and 27K test examples. As\n", "this is a new task, we train a BART model for comparison. Following [ 67], we evaluate using the\n", "SQuAD-tuned Q-BLEU-1 metric [ 42]. Q-BLEU is a variant of BLEU with a higher weight for\n", "matching entities and has higher correlation with human judgment for question generation than\n", "standard metrics. We also perform two human evaluations, one to assess generation factuality, and\n", "one for speciﬁcity. We deﬁne factuality as whether a statement can be corroborated by trusted external\n", "sources, and speciﬁcity as high mutual dependence between the input and output [ 33]. We follow\n", "best practice and use pairwise comparative evaluation [ 34]. Evaluators are shown an answer and two\n", "generated questions, one from BART and one from RAG. They are then asked to pick one of four\n", "options—quuestion A is better, question B is better, both are good, or neither is good.\n", "3.4 Fact Veriﬁcation\n", "FEVER [ 56] requires classifying whether a natural language claim is supported or refuted by\n", "Wikipedia, or whether there is not enough information to decide. The task requires retrieving\n", "evidence from Wikipedia relating to the claim and then reasoning over this evidence to classify\n", "whether the claim is true, false, or unveriﬁable from Wikipedia alone. FEVER is a retrieval problem\n", "coupled with an challenging entailment reasoning task. It also provides an appropriate testbed for\n", "exploring the RAG models’ ability to handle classiﬁcation rather than generation. We map FEVER\n", "class labels (supports, refutes, or not enough info) to single output tokens and directly train with\n", "claim-class pairs. Crucially, unlike most other approaches to FEVER, we do not use supervision on\n", "retrieved evidence. In many real-world applications, retrieval supervision signals aren’t available, and\n", "models that do not require such supervision will be applicable to a wider range of tasks. We explore\n", "two variants: the standard 3-way classiﬁcation task (supports/refutes/not enough info) and the 2-way\n", "(supports/refutes) task studied in Thorne and Vlachos [57]. In both cases we report label accuracy.\n", "4 Results\n", "4.1 Open-domain Question Answering\n", "Table 1 shows results for RAG along with state-of-the-art models. On all four open-domain QA\n", "tasks, RAG sets a new state of the art (only on the T5-comparable split for TQA). RAG combines\n", "the generation ﬂexibility of the “closed-book” (parametric only) approaches and the performance of\n", "\"open-book\" retrieval-based approaches. Unlike REALM and T5+SSM, RAG enjoys strong results\n", "without expensive, specialized “salient span masking” pre-training [ 20]. It is worth noting that RAG’s\n", "retriever is initialized using DPR’s retriever, which uses retrieval supervision on Natural Questions\n", "and TriviaQA. RAG compares favourably to the DPR QA system, which uses a BERT-based “cross-\n", "encoder” to re-rank documents, along with an extractive reader. RAG demonstrates that neither a\n", "re-ranker nor extractive reader is necessary for state-of-the-art performance.\n", "There are several advantages to generating answers even when it is possible to extract them. Docu-\n", "ments with clues about the answer but do not contain the answer verbatim can still contribute towards\n", "a correct answer being generated, which is not possible with standard extractive approaches, leading\n", "5\n", "\n", "MSMARCO as an open-domain abstractive QA task. MSMARCO has some questions that cannot be\n", "answered in a way that matches the reference answer without access to the gold passages, such as\n", "“What is the weather in V olcano, CA?” so performance will be lower without using gold passages.\n", "We also note that some MSMARCO questions cannot be answered using Wikipedia alone. Here,\n", "RAG can rely on parametric knowledge to generate reasonable responses.\n", "3.3 Jeopardy Question Generation\n", "To evaluate RAG’s generation abilities in a non-QA setting, we study open-domain question gen-\n", "eration. Rather than use questions from standard open-domain QA tasks, which typically consist\n", "of short, simple questions, we propose the more demanding task of generating Jeopardy questions.\n", "Jeopardy is an unusual format that consists of trying to guess an entity from a fact about that entity.\n", "For example, “The World Cup” is the answer to the question “In 1986 Mexico scored as the ﬁrst\n", "country to host this international sports competition twice.” As Jeopardy questions are precise,\n", "factual statements, generating Jeopardy questions conditioned on their answer entities constitutes a\n", "challenging knowledge-intensive generation task.\n", "We use the splits from SearchQA [ 10], with 100K train, 14K dev, and 27K test examples. As\n", "this is a new task, we train a BART model for comparison. Following [ 67], we evaluate using the\n", "SQuAD-tuned Q-BLEU-1 metric [ 42]. Q-BLEU is a variant of BLEU with a higher weight for\n", "matching entities and has higher correlation with human judgment for question generation than\n", "standard metrics. We also perform two human evaluations, one to assess generation factuality, and\n", "one for speciﬁcity. We deﬁne factuality as whether a statement can be corroborated by trusted external\n", "sources, and speciﬁcity as high mutual dependence between the input and output [ 33]. We follow\n", "best practice and use pairwise comparative evaluation [ 34]. Evaluators are shown an answer and two\n", "generated questions, one from BART and one from RAG. They are then asked to pick one of four\n", "options—quuestion A is better, question B is better, both are good, or neither is good.\n", "3.4 Fact Veriﬁcation\n", "FEVER [ 56] requires classifying whether a natural language claim is supported or refuted by\n", "Wikipedia, or whether there is not enough information to decide. The task requires retrieving\n", "evidence from Wikipedia relating to the claim and then reasoning over this evidence to classify\n", "whether the claim is true, false, or unveriﬁable from Wikipedia alone. FEVER is a retrieval problem\n", "coupled with an challenging entailment reasoning task. It also provides an appropriate testbed for\n", "exploring the RAG models’ ability to handle classiﬁcation rather than generation. We map FEVER\n", "class labels (supports, refutes, or not enough info) to single output tokens and directly train with\n", "claim-class pairs. Crucially, unlike most other approaches to FEVER, we do not use supervision on\n", "retrieved evidence. In many real-world applications, retrieval supervision signals aren’t available, and\n", "models that do not require such supervision will be applicable to a wider range of tasks. We explore\n", "two variants: the standard 3-way classiﬁcation task (supports/refutes/not enough info) and the 2-way\n", "(supports/refutes) task studied in Thorne and Vlachos [57]. In both cases we report label accuracy.\n", "4 Results\n", "4.1 Open-domain Question Answering\n", "Table 1 shows results for RAG along with state-of-the-art models. On all four open-domain QA\n", "tasks, RAG sets a new state of the art (only on the T5-comparable split for TQA). RAG combines\n", "the generation ﬂexibility of the “closed-book” (parametric only) approaches and the performance of\n", "\"open-book\" retrieval-based approaches. Unlike REALM and T5+SSM, RAG enjoys strong results\n", "without expensive, specialized “salient span masking” pre-training [ 20]. It is worth noting that RAG’s\n", "retriever is initialized using DPR’s retriever, which uses retrieval supervision on Natural Questions\n", "and TriviaQA. RAG compares favourably to the DPR QA system, which uses a BERT-based “cross-\n", "encoder” to re-rank documents, along with an extractive reader. RAG demonstrates that neither a\n", "re-ranker nor extractive reader is necessary for state-of-the-art performance.\n", "There are several advantages to generating answers even when it is possible to extract them. Docu-\n", "ments with clues about the answer but do not contain the answer verbatim can still contribute towards\n", "a correct answer being generated, which is not possible with standard extractive approaches, leading\n", "5\n", "\n", "[66] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>\n", "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>,\n", "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>’s transformers:\n", "State-of-the-art natural language processing. ArXiv , abs/1910.03771, 2019.\n", "[67] <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Addressing semantic drift in question generation for semi-\n", "supervised question answering. In Proceedings of the 2019 Conference on Empirical Meth-\n", "ods in Natural Language Processing and the 9th International Joint Conference on Natural\n", "Language Processing (EMNLP-IJCNLP) , pages 2495–2509, Hong Kong, China, Novem-\n", "ber 2019. Association for Computational Linguistics. doi: 10.18653/v1/D19-1253. URL\n", "https://www.aclweb.org/anthology/D19-1253 .\n", "[68] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and\n", "<PERSON><PERSON>. Reasoning over semantic-level graph for fact checking. ArXiv , abs/1909.03745, 2019.\n", "URL https://arxiv.org/abs/1909.03745 .\n", "16\n", "\n", "Question: What is Abstractive Question Answering?\n", "Helpful Answer: Abstractikt launchACHEwerpikt liciefsiefsikt launchikt licwerpiktiefsiefswerpwerp launchiefsiktwerpiktwerp launch launch launch launch launchikt launchiefsiktiktwerpiefsiefsiefsikt lic launchwerpiktikt liciktiktiktwerpikt licwerpiefs lic liciktiktiktwerpwerp lic launchiktiefswerpiktiefsiefsiefsiefsiktiktiktiefsiefsiefsiefsiktiefswerp launchiefsiefsikt licikt liciktiefsiefsiktwerpiefswerpwerp launch launchiktwerpiefswerp liciktwerp lic liciktiktiefs launchiktiktwerpiefs licwerpiktiefs licwerpwerpwerpwerpwerpwerpiefsikt launch liciefs liciefs launchiktiefsiktwerpwerpiefs licwerpiktikt launchiefsiktikt liciefsikt launchiktiktiefs liciktwerpiktiefs launchiefs licwerpiktiefsiktwerpiktiefs lic lic launchikt licwerp liciefs licwerp liciefswerpikt lic launchikt licwerpwerpwerpiefs launch liciefsikt lic liciefswerpwerpiktwerp launchwerp launchiefsiktwerpwerp licwerpiktwerpikt liciktiefsiktiefsikt launch launchiktiktiefs liciefsiefswerpwerpikt liciktiktiktiktiefsikt lic liciktiefsikt liciefs launchiktiktiktikt launch launch licwerpiefsiefsiktwerpwerp lic licikt launchiktiefsiktikt lic launchwerpwerp launchwerp launchiktiefs lic lic lic licwerp lic liciktiefs liciefs launchiefsiefsiktiefsiefs licwerpiefs lic launchwerp\n"]}]}, {"cell_type": "code", "source": ["print(response.get(\"result\"))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8Wa3jBEgLwXB", "outputId": "d5e1a29a-5969-4ff2-d147-cdd49d2f7ed0"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Use the following pieces of context to answer the question at the end. If you don't know the answer, just say that you don't know, don't try to make up an answer.\n", "\n", "\n", "\n", "Question: What is Abstractive Question Answering?\n", "Helpful Answer: Abstract Question Answering, also known as Open Domain QA, aims to answer questions where neither the question nor the answer is restricted to any specific domain. In contrast to the Closed Domain Question Answering, Abstractive Question Answering does not require any predefined knowledge or ontology. Instead, it relies on the context of the question itself to find a relevant answer. This type of question answering is particularly challenging due to the lack of constraints, making it a crucial research area.\n", "\n", "\n", "\n", "Question: Which company recently introduced its first smart TV in the UK market?\n", "Helpful Answer: In August 2019, Hisense announced the launch of its first ever smart TV range in the UK market, featuring Android TV.\n", "\n", "\n", "\n", "Question: Which technology did Google acquire in 2016 for its machine learning capabilities?\n", "Helpful Answer: Google acquired the Canadian artificial intelligence company, DeepMind, in 2016 for its advanced machine learning capabilities in areas such as reinforcement learning and neural networks.\n", "\n", "\n", "\n", "Question: In what country was the first commercial 5G network launched in December 2018?\n", "Helpful Answer: In December 2018, the first commercial 5G network was launched in the South Korean city of Daetu. The technology is expected to transform the way people connect and communicate by offering significantly faster download and upload speeds, as well as lower latency.\n", "\n", "\n", "\n", "Question: Which company is set to launch the world's first 5G smartphone in March 2019?\n", "Helpful Answer: South Korean electronics giant, Samsung, is set to launch the world's first 5G smartphone, the Galaxy S10 5G, in March 2019. The device will initially be available in South Korea and is expected to roll out to other regions later in the year.\n", "\n", "\n", "\n", "Question: What is the name of the first AI system to pass a medical licensing exam?\n", "Helpful Answer: In October 2018, the University of California, San Francisco (UCSF) announced that its AI system, Lighthouse, had passed the United States Medical Licensing Exam (USMLE). The achievement represented a landmark in the field of medical AI and demonstrated the potential for AI to assist healthcare professionals in their work.\n", "\n", "\n", "\n", "Question: In what country has the world's first quantum computer been installed in a cloud computing service?\n", "Helpful Answer: In August 2019, IBM announced that the world's first quantum computer had been installed in a cloud computing service in Japan. The service, called IBM Quantum Experience, is designed to enable researchers to experiment with quantum computing technology without the need for a significant financial investment in hardware and infrastructure.\n", "\n", "\n", "\n", "Question: Which company acquired a majority stake in the mapping startup, What3Words, in 2019?\n", "Helpful Answer: In April 2019, Daimler, the parent company of Mercedes-Benz, announced that it had acquired a majority stake in the London-based mapping startup, What3Words. The acquisition was seen as a strategic move by Daimler to enhance its connected car offering, with What3Words' technology providing a more intuitive and accurate way to locate places and addresses.\n", "\n", "\n", "\n", "Question: In what year did Google first enter the smartphone market with the launch of the Nexus One?\n", "Helpful Answer: The Nexus One, the first smartphone in Google's Nexus series, was launched in January 2010. The device, which was manufactured by HTC, was notable for its use of Android 2.1, the first iteration of the operating system to be released by Google.\n", "\n", "\n", "\n", "Question: Which company recently announced plans to invest $22 billion in renewable energy projects by 2025?\n", "Helpful Answer: In September 2019, Microsoft announced that it planned to invest $22 billion in renewable energy projects by 2025. The company, which has committed to becoming carbon negative by 2030, said that the investment would help to accelerate the development of new renewable energy projects and technologies.\n", "\n", "\n", "\n", "Question: Which company recently unveiled a folding smartphone concept as part of its annual developer conference?\n", "Helpful Answer: In November 2018, Samsung unveiled a folding smartphone concept as part of its annual developer conference. The device, which is expected to be launched in 2019, features a foldable display that allows it to take the form of a traditional smartphone when closed and a small tablet when opened.\n", "\n", "\n", "\n", "Question: In what year was Google founded, and who were its founders?\n", "Helpful Answer: Google was founded in 1998 by <PERSON> and <PERSON>, both of whom were doctoral students at Stanford University at the time.\n", "\n", "\n", "\n", "Question: Which company recently announced a partnership with the German carmaker, BMW, to develop self-driving technology?\n", "Helpful Answer: In March 2019, Intel announced a partnership with the German carmaker, BMW, to develop self-driving technology for use in BMW's vehicles. The partnership will involve the joint development of hardware and software solutions for autonomous driving, as well as the testing of these solutions in real-world environments.\n", "\n", "\n", "\n", "Question: Which company recently announced plans to acquire the cloud-based storage startup, MongoDB?\n", "Helpful Answer: In August 2019, MongoDB, the cloud-based storage startup, announced that it had agreed to be acquired by the American multinational corporation, International Business Machines Corporation (IBM), for $3 billion. The acquisition represents a significant move by IBM to expand its position in the cloud computing market and to strengthen its offerings in the area of data management and analytics.\n", "\n", "\n", "\n", "Question: What is the name of the first self-driving car to complete a coast-to-coast trip across the United States?\n", "Helpful Answer: In October 2018, the California-based startup, Aurora, announced that its self-driving car had completed a coast-to-coast trip across the United States. The achievement represented a significant milestone in the field of autonomous driving and demonstrated the potential for self-driving cars to revolutionize the way we travel.\n", "\n", "\n", "\n", "Question: Which company recently announced plans to acquire the data analytics startup, Talend?\n", "Helpful Answer: In June 2019, the American multinational corporation, Hewlett Packard Enterprise (HPE), announced that it had agreed to acquire the data analytics startup, Talend, for $2 billion. The acquisition represents a significant move by HPE to expand its position in the area of data management and analytics and to strengthen its offerings to customers in this space.\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "tcdaBC5gMCzh"}, "execution_count": null, "outputs": []}]}