import gensim
from gensim.models import Word2Vec,KeyedVectors

from google.colab import drive
drive.mount('/content/drive')

model = KeyedVectors.load_word2vec_format('/content/drive/MyDrive/GoogleNews/GoogleNews-vectors-negative300.bin.gz',binary=True,limit=500000)

model['man']

model['man'].shape

model['glass']

model.most_similar('man')

model.most_similar('cricket')

model.most_similar('facebook')

model.most_similar('woman')

model.similarity('man','woman')

model.similarity('Python','man')

model.similarity('Java','man')

model.doesnt_match(['JAVA', 'Python', 'Elephant'])

vec = model['king'] - model['man'] + model['woman']
model.most_similar([vec])

model['king'].shape

# king, queen, woman, girl, boy, man, water

model['king'][0:50]

import seaborn as sns
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

# king, queen, woman, girl, boy, man, water
words = ['queen', 'woman', 'girl', 'boy', 'man', 'king', 'water']
embeddings = np.array([
    model['queen'][0:10],
    model['woman'][0:10],
    model['girl'][0:10],
    model['boy'][0:10],
    model['man'][0:10],
    model['king'][0:10],
    model['water'][0:10],
                       ])

df = pd.DataFrame(embeddings, index=words)

# Create a hashmap
plt.figure(figsize=(18,16))
heatmap = sns.heatmap(df, cmap='crest')
plt.show()

