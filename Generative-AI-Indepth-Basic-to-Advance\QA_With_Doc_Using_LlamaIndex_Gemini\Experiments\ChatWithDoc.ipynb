{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv\n", "load_dotenv()\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["GOOGLE_API_KEY=os.getenv(\"GOOGLE_API_KEY\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["'AIzaSyDnACKG8IVHV0NwTP3tiZJEI937ck6HH7w'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["GOOGLE_API_KEY"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\chatwithdocllama\\venv\\lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from llama_index.core import SimpleDirectoryReader\n", "from llama_index.core import VectorStoreIndex\n", "from llama_index.llms.gemini import Gemini\n", "from IPython.display import Markdown, display\n", "from llama_index.core import ServiceContext\n", "from llama_index.core import StorageContext, load_index_from_storage\n", "import google.generativeai as genai\n", "from llama_index.embeddings.gemini import GeminiEmbedding\n", "#from llama_index.core.settings import Settings\n", "genai.configure(api_key=GOOGLE_API_KEY)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model(name='models/chat-bison-001',\n", "      base_model_id='',\n", "      version='001',\n", "      display_name='PaLM 2 Chat (Legacy)',\n", "      description='A legacy text-only model optimized for chat conversations',\n", "      input_token_limit=4096,\n", "      output_token_limit=1024,\n", "      supported_generation_methods=['generateMessage', 'countMessageTokens'],\n", "      temperature=0.25,\n", "      top_p=0.95,\n", "      top_k=40)\n", "Model(name='models/text-bison-001',\n", "      base_model_id='',\n", "      version='001',\n", "      display_name='PaLM 2 (Legacy)',\n", "      description='A legacy model that understands text and generates text as an output',\n", "      input_token_limit=8196,\n", "      output_token_limit=1024,\n", "      supported_generation_methods=['generateText', 'countTextTokens', 'createTunedTextModel'],\n", "      temperature=0.7,\n", "      top_p=0.95,\n", "      top_k=40)\n", "Model(name='models/embedding-gecko-001',\n", "      base_model_id='',\n", "      version='001',\n", "      display_name='Embedding Gecko',\n", "      description='Obtain a distributed representation of a text.',\n", "      input_token_limit=1024,\n", "      output_token_limit=1,\n", "      supported_generation_methods=['embedText', 'countTextTokens'],\n", "      temperature=None,\n", "      top_p=None,\n", "      top_k=None)\n", "Model(name='models/gemini-pro',\n", "      base_model_id='',\n", "      version='001',\n", "      display_name='Gemini 1.0 Pro',\n", "      description='The best model for scaling across a wide range of tasks',\n", "      input_token_limit=30720,\n", "      output_token_limit=2048,\n", "      supported_generation_methods=['generateContent', 'countTokens'],\n", "      temperature=0.9,\n", "      top_p=1.0,\n", "      top_k=1)\n", "Model(name='models/gemini-pro-vision',\n", "      base_model_id='',\n", "      version='001',\n", "      display_name='Gemini 1.0 Pro Vision',\n", "      description='The best image understanding model to handle a broad range of applications',\n", "      input_token_limit=12288,\n", "      output_token_limit=4096,\n", "      supported_generation_methods=['generateContent', 'countTokens'],\n", "      temperature=0.4,\n", "      top_p=1.0,\n", "      top_k=32)\n", "Model(name='models/embedding-001',\n", "      base_model_id='',\n", "      version='001',\n", "      display_name='Embedding 001',\n", "      description='Obtain a distributed representation of a text.',\n", "      input_token_limit=2048,\n", "      output_token_limit=1,\n", "      supported_generation_methods=['embedContent', 'countTextTokens'],\n", "      temperature=None,\n", "      top_p=None,\n", "      top_k=None)\n", "Model(name='models/aqa',\n", "      base_model_id='',\n", "      version='001',\n", "      display_name='Model that performs Attributed Question Answering.',\n", "      description=('Model trained to return answers to questions that are grounded in provided '\n", "                   'sources, along with estimating answerable probability.'),\n", "      input_token_limit=7168,\n", "      output_token_limit=1024,\n", "      supported_generation_methods=['generateAnswer'],\n", "      temperature=0.2,\n", "      top_p=1.0,\n", "      top_k=40)\n"]}], "source": ["for models in genai.list_models():\n", "  print(models)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["models/gemini-pro\n", "models/gemini-pro-vision\n"]}], "source": ["for models in genai.list_models():\n", "  if 'generateContent' in models.supported_generation_methods:\n", "    print(models.name)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["documents = SimpleDirectoryReader(\"../Data\").load_data()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(id_='e853545c-0ca1-4b7e-9681-02919ad26522', embedding=None, metadata={'file_path': '..\\\\Data\\\\MLDOC.txt', 'file_name': 'MLDOC.txt', 'file_type': 'text/plain', 'file_size': 22273, 'creation_date': '2024-02-15', 'last_modified_date': '2024-02-15', 'last_accessed_date': '2024-02-15'}, excluded_embed_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, text='What is machine learning?\\nMachine learning is a branch of artificial intelligence (AI) and computer science which\\nfocuses on the use of data and algorithms to imitate the way that humans learn,\\ngradually improving its accuracy.\\nIBM has a rich history with machine learning. One of its own, <PERSON>, is credited\\nfor coining the term, “machine learning” with his research (link resides outside ibm.com)\\naround the game of checkers. <PERSON> <PERSON>ey, the self-proclaimed checkers master,\\nplayed the game on an IBM 7094 computer in 1962, and he lost to the computer.\\nCompared to what can be done today, this feat seems trivial, but it’s considered a major\\nmilestone in the field of artificial intelligence.\\nOver the last couple of decades, the technological advances in storage and processing\\npower have enabled some innovative products based on machine learning, such as\\nNetflix’s recommendation engine and self-driving cars.\\nMachine learning is an important component of the growing field of data science.\\nThrough the use of statistical methods, algorithms are trained to make classifications or\\npredictions, and to uncover key insights in data mining projects. These insights\\nsubsequently drive decision making within applications and businesses, ideally\\nimpacting key growth metrics. As big data continues to expand and grow, the market\\ndemand for new data scientists will increase. They will be required to help identify the\\nmost relevant business questions and the data to answer them.\\nMachine learning algorithms are typically created using frameworks such as Python that\\naccelerate solution development by using platforms like TensorFlow or PyTorch.\\nNow available: watsonx.ai\\nThe all-new enterprise studio that brings together traditional machine learning along\\nwith new generative AI capabilities powered by foundation models.\\nTry watsonx.ai\\nBegin your journey to AI\\nLearn how to scale AI\\nExplore the AI Academy\\nMachine Learning vs. Deep Learning vs. Neural Networks\\nSince deep learning and machine learning tend to be used interchangeably, it’s worth\\nnoting the nuances between the two. Machine learning, deep learning, and neural\\nnetworks are all sub-fields of artificial intelligence. However, neural networks is actually\\na sub-field of machine learning, and deep learning is a sub-field of neural networks.\\nThe way in which deep learning and machine learning differ is in how each algorithm\\nlearns. \"Deep\" machine learning can use labeled datasets, also known as supervised\\nlearning, to inform its algorithm, but it doesn’t necessarily require a labeled dataset. The\\ndeep learning process can ingest unstructured data in its raw form (e.g., text or images),\\nand it can automatically determine the set of features which distinguish different\\ncategories of data from one another. This eliminates some of the human intervention\\nrequired and enables the use of large amounts of data. You can think of deep learning\\nas \"scalable machine learning\" as Lex Fridman notes in this MIT lecture (link resides\\noutside ibm.com).\\nClassical, or \"non-deep,\" machine learning is more dependent on human intervention to\\nlearn. Human experts determine the set of features to understand the differences\\nbetween data inputs, usually requiring more structured data to learn.\\nNeural networks, or artificial neural networks (ANNs), are comprised of node layers,\\ncontaining an input layer, one or more hidden layers, and an output layer. Each node, or\\nartificial neuron, connects to another and has an associated weight and threshold. If the\\noutput of any individual node is above the specified threshold value, that node is\\nactivated, sending data to the next layer of the network. Otherwise, no data is passed\\nalong to the next layer of the network by that node. The “deep” in deep learning is just\\nreferring to the number of layers in a neural network. A neural network that consists of\\nmore than three layers—which would be inclusive of the input and the output—can be\\nconsidered a deep learning algorithm or a deep neural network. A neural network that\\nonly has three layers is just a basic neural network.\\nDeep learning and neural networks are credited with accelerating progress in areas\\nsuch as computer vision, natural language processing, and speech recognition.\\nSee the blog post “AI vs. Machine Learning vs. Deep Learning vs. Neural Networks:\\nWhat’s the Difference?” for a closer look at how the different concepts relate.\\nRelated content\\nExplore the watsonx.ai interactive demo\\nDownload “Machine learning for Dummies”\\n- This link downloads a pdf\\nExplore Gen AI for developers\\nHow does machine learning work?\\nUC Berkeley (link resides outside ibm.com) breaks out the learning system of a\\nmachine learning algorithm into three main parts.\\nA Decision Process: In general, machine learning algorithms are used to make a\\nprediction or classification. Based on some input data, which can be labeled or\\nunlabeled, your algorithm will produce an estimate about a pattern in the data.\\nAn Error Function: An error function evaluates the prediction of the model. If\\nthere are known examples, an error function can make a comparison to assess\\nthe accuracy of the model.\\nA Model Optimization Process: If the model can fit better to the data points in the\\ntraining set, then weights are adjusted to reduce the discrepancy between the\\nknown example and the model estimate. The algorithm will repeat this iterative\\n“evaluate and optimize” process, updating weights autonomously until a\\nthreshold of accuracy has been met.\\nMachine learning methods\\nMachine learning models fall into three primary categories.\\nSupervised machine learning\\nSupervised learning, also known as supervised machine learning, is defined by its use\\nof labeled datasets to train algorithms to classify data or predict outcomes accurately.\\nAs input data is fed into the model, the model adjusts its weights until it has been fitted\\nappropriately. This occurs as part of the cross validation process to ensure that the\\nmodel avoids overfitting or underfitting. Supervised learning helps organizations solve a\\nvariety of real-world problems at scale, such as classifying spam in a separate folder\\nfrom your inbox. Some methods used in supervised learning include neural networks,\\nnaïve bayes, linear regression, logistic regression, random forest, and support vector\\nmachine (SVM).\\nUnsupervised machine learning\\nUnsupervised learning, also known as unsupervised machine learning, uses machine\\nlearning algorithms to analyze and cluster unlabeled datasets (subsets called clusters).\\nThese algorithms discover hidden patterns or data groupings without the need for\\nhuman intervention. This method’s ability to discover similarities and differences in\\ninformation make it ideal for exploratory data analysis, cross-selling strategies,\\ncustomer segmentation, and image and pattern recognition. It’s also used to reduce the\\nnumber of features in a model through the process of dimensionality reduction. Principal\\ncomponent analysis (PCA) and singular value decomposition (SVD) are two common\\napproaches for this. Other algorithms used in unsupervised learning include neural\\nnetworks, k-means clustering, and probabilistic clustering methods.\\nSemi-supervised learning\\nSemi-supervised learning offers a happy medium between supervised and\\nunsupervised learning. During training, it uses a smaller labeled data set to guide\\nclassification and feature extraction from a larger, unlabeled data set. Semi-supervised\\nlearning can solve the problem of not having enough labeled data for a supervised\\nlearning algorithm. It also helps if it’s too costly to label enough data.\\nFor a deep dive into the differences between these approaches, check out \"Supervised\\nvs. Unsupervised Learning: What\\'s the Difference?\"\\nReinforcement machine learning\\nReinforcement machine learning is a machine learning model that is similar to\\nsupervised learning, but the algorithm isn’t trained using sample data. This model learns\\nas it goes by using trial and error. A sequence of successful outcomes will be reinforced\\nto develop the best recommendation or policy for a given problem.\\nThe IBM Watson® system that won the Jeopardy! challenge in 2011 is a good example.\\nThe system used reinforcement learning to learn when to attempt an answer (or\\nquestion, as it were), which square to select on the board, and how much to\\nwager—especially on daily doubles.\\nLearn more about reinforcement learning\\nCommon machine learning algorithms\\nA number of machine learning algorithms are commonly used. These include:\\nNeural networks: Neural networks simulate the way the human brain works, with\\na huge number of linked processing nodes. Neural networks are good at\\nrecognizing patterns and play an important role in applications including natural\\nlanguage translation, image recognition, speech recognition, and image creation.\\nLinear regression: This algorithm is used to predict numerical values, based on a\\nlinear relationship between different values. For example, the technique could be\\nused to predict house prices based on historical data for the area.\\nLogistic regression: This supervised learning algorithm makes predictions for\\ncategorical response variables, such as “yes/no” answers to questions. It can be\\nused for applications such as classifying spam and quality control on a\\nproduction line.\\nClustering: Using unsupervised learning, clustering algorithms can identify\\npatterns in data so that it can be grouped. Computers can help data scientists by\\nidentifying differences between data items that humans have overlooked.\\nDecision trees: Decision trees can be used for both predicting numerical values\\n(regression) and classifying data into categories. Decision trees use a branching\\nsequence of linked decisions that can be represented with a tree diagram. One of\\nthe advantages of decision trees is that they are easy to validate and audit,\\nunlike the black box of the neural network.\\nRandom forests: In a random forest, the machine learning algorithm predicts a\\nvalue or category by combining the results from a number of decision trees.\\nAdvantages and disadvantages of machine learning algorithms\\nDepending on your budget, need for speed and precision required, each algorithm\\ntype—supervised, unsupervised, semi-supervised, or reinforcement—has its own\\nadvantages and disadvantages. For example, decision tree algorithms are used for both\\npredicting numerical values (regression problems) and classifying data into categories.\\nDecision trees use a branching sequence of linked decisions that may be represented\\nwith a tree diagram. A prime advantage of decision trees is that they are easier to\\nvalidate and audit than a neural network. The bad news is that they can be more\\nunstable than other decision predictors.\\nOverall, there are many advantages to machine learning that businesses can leverage\\nfor new efficiencies. These include machine learning identifying patterns and trends in\\nmassive volumes of data that humans might not spot at all. And this analysis requires\\nlittle human intervention: just feed in the dataset of interest and let the machine learning\\nsystem assemble and refine its own algorithms—which will continually improve with\\nmore data input over time. Customers and users can enjoy a more personalized\\nexperience as the model learns more with every experience with that person.\\nOn the downside, machine learning requires large training datasets that are accurate\\nand unbiased. GIGO is the operative factor: garbage in / garbage out. Gathering\\nsufficient data and having a system robust enough to run it might also be a drain on\\nresources. Machine learning can also be prone to error, depending on the input. With\\ntoo small a sample, the system could produce a perfectly logical algorithm that is\\ncompletely wrong or misleading. To avoid wasting budget or displeasing customers,\\norganizations should act on the answers only when there is high confidence in the\\noutput.\\nReal-world machine learning use cases\\nHere are just a few examples of machine learning you might encounter every day:\\nSpeech recognition: It is also known as automatic speech recognition (ASR), computer\\nspeech recognition, or speech-to-text, and it is a capability which uses natural language\\nprocessing (NLP) to translate human speech into a written format. Many mobile devices\\nincorporate speech recognition into their systems to conduct voice search—e.g. Siri—or\\nimprove accessibility for texting.\\nCustomer service: Online chatbots are replacing human agents along the customer\\njourney, changing the way we think about customer engagement across websites and\\nsocial media platforms. Chatbots answer frequently asked questions (FAQs) about\\ntopics such as shipping, or provide personalized advice, cross-selling products or\\nsuggesting sizes for users. Examples include virtual agents on e-commerce sites;\\nmessaging bots, using Slack and Facebook Messenger; and tasks usually done by\\nvirtual assistants and voice assistants.\\nComputer vision: This AI technology enables computers to derive meaningful\\ninformation from digital images, videos, and other visual inputs, and then take the\\nappropriate action. Powered by convolutional neural networks, computer vision has\\napplications in photo tagging on social media, radiology imaging in healthcare, and\\nself-driving cars in the automotive industry.\\nRecommendation engines: Using past consumption behavior data, AI algorithms can\\nhelp to discover data trends that can be used to develop more effective cross-selling\\nstrategies. Recommendation engines are used by online retailers to make relevant\\nproduct recommendations to customers during the checkout process.\\nRobotic process automation (RPA): Also known as software robotics, RPA uses\\nintelligent automation technologies to perform repetitive manual tasks.\\nAutomated stock trading: Designed to optimize stock portfolios, AI-driven\\nhigh-frequency trading platforms make thousands or even millions of trades per day\\nwithout human intervention.\\nFraud detection: Banks and other financial institutions can use machine learning to spot\\nsuspicious transactions. Supervised learning can train a model using information about\\nknown fraudulent transactions. Anomaly detection can identify transactions that look\\natypical and deserve further investigation.\\nChallenges of machine learning\\nAs machine learning technology has developed, it has certainly made our lives easier.\\nHowever, implementing machine learning in businesses has also raised a number of\\nethical concerns about AI technologies. Some of these include:\\nTechnological singularity\\nWhile this topic garners a lot of public attention, many researchers are not concerned\\nwith the idea of AI surpassing human intelligence in the near future. Technological\\nsingularity is also referred to as strong AI or superintelligence. Philosopher Nick\\nBostrum defines superintelligence as “any intellect that vastly outperforms the best\\nhuman brains in practically every field, including scientific creativity, general wisdom,\\nand social skills.” Despite the fact that superintelligence is not imminent in society, the\\nidea of it raises some interesting questions as we consider the use of autonomous\\nsystems, like self-driving cars. It’s unrealistic to think that a driverless car would never\\nhave an accident, but who is responsible and liable under those circumstances? Should\\nwe still develop autonomous vehicles, or do we limit this technology to\\nsemi-autonomous vehicles which help people drive safely? The jury is still out on this,\\nbut these are the types of ethical debates that are occurring as new, innovative AI\\ntechnology develops.\\nAI impact on jobs\\nWhile a lot of public perception of artificial intelligence centers around job losses, this\\nconcern should probably be reframed. With every disruptive, new technology, we see\\nthat the market demand for specific job roles shifts. For example, when we look at the\\nautomotive industry, many manufacturers, like GM, are shifting to focus on electric\\nvehicle production to align with green initiatives. The energy industry isn’t going away,\\nbut the source of energy is shifting from a fuel economy to an electric one.\\nIn a similar way, artificial intelligence will shift the demand for jobs to other areas. There\\nwill need to be individuals to help manage AI systems. There will still need to be people\\nto address more complex problems within the industries that are most likely to be\\naffected by job demand shifts, such as customer service. The biggest challenge with\\nartificial intelligence and its effect on the job market will be helping people to transition\\nto new roles that are in demand.\\nPrivacy\\nPrivacy tends to be discussed in the context of data privacy, data protection, and data\\nsecurity. These concerns have allowed policymakers to make more strides in recent\\nyears. For example, in 2016, GDPR legislation was created to protect the personal data\\nof people in the European Union and European Economic Area, giving individuals more\\ncontrol of their data. In the United States, individual states are developing policies, such\\nas the California Consumer Privacy Act (CCPA), which was introduced in 2018 and\\nrequires businesses to inform consumers about the collection of their data. Legislation\\nsuch as this has forced companies to rethink how they store and use personally\\nidentifiable information (PII). As a result, investments in security have become an\\nincreasing priority for businesses as they seek to eliminate any vulnerabilities and\\nopportunities for surveillance, hacking, and cyberattacks.\\nBias and discrimination\\nInstances of bias and discrimination across a number of machine learning systems have\\nraised many ethical questions regarding the use of artificial intelligence. How can we\\nsafeguard against bias and discrimination when the training data itself may be\\ngenerated by biased human processes? While companies typically have good\\nintentions for their automation efforts, Reuters (link resides outside ibm.com) highlights\\nsome of the unforeseen consequences of incorporating AI into hiring practices. In their\\neffort to automate and simplify a process, Amazon unintentionally discriminated against\\njob candidates by gender for technical roles, and the company ultimately had to scrap\\nthe project. Harvard Business Review (link resides outside ibm.com) has raised other\\npointed questions about the use of AI in hiring practices, such as what data you should\\nbe able to use when evaluating a candidate for a role.\\nBias and discrimination aren’t limited to the human resources function either; they can\\nbe found in a number of applications from facial recognition software to social media\\nalgorithms.\\nAs businesses become more aware of the risks with AI, they’ve also become more\\nactive in this discussion around AI ethics and values. For example, IBM has sunset its\\ngeneral purpose facial recognition and analysis products. IBM CEO Arvind Krishna\\nwrote: “IBM firmly opposes and will not condone uses of any technology, including facial\\nrecognition technology offered by other vendors, for mass surveillance, racial profiling,\\nviolations of basic human rights and freedoms, or any purpose which is not consistent\\nwith our values and Principles of Trust and Transparency.”\\nAccountability\\nSince there isn’t significant legislation to regulate AI practices, there is no real\\nenforcement mechanism to ensure that ethical AI is practiced. The current incentives for\\ncompanies to be ethical are the negative repercussions of an unethical AI system on the\\nbottom line. To fill the gap, ethical frameworks have emerged as part of a collaboration\\nbetween ethicists and researchers to govern the construction and distribution of AI\\nmodels within society. However, at the moment, these only serve to guide. Some\\nresearch (link resides outside ibm.com) shows that the combination of distributed\\nresponsibility and a lack of foresight into potential consequences aren’t conducive to\\npreventing harm to society.\\nRead more about IBM\\'s position on AI Ethics\\nHow to choose the right AI platform for machine learning\\nSelecting a platform can be a challenging process, as the wrong system can drive up\\ncosts, or limit the use of other valuable tools or technologies. When reviewing multiple\\nvendors to select an AI platform, there is often a tendency to think that more features =\\na better system. Maybe so, but reviewers should start by thinking through what the AI\\nplatform will be doing for their organization. What machine learning capabilities need to\\nbe delivered and what features are important to accomplish them? One missing feature\\nmight doom the usefulness of an entire system. Here are some features to consider.\\nMLOps capabilities. Does the system have:\\na unified interface for ease of management?\\nautomated machine learning tools for faster model creation with low-code\\nand no-code functionality?\\ndecision optimization to streamline the selection and deployment of\\noptimization models?\\nvisual modeling to combine visual data science with open-source libraries\\nand notebook-based interfaces on a unified data and AI studio?\\nautomated development for beginners to get started quickly and more\\nadvanced data scientists to experiment?\\nsynthetic data generator as an alternative or supplement to real-world data\\nwhen real-world data is not readily available?\\nGenerative AI capabilities. Does the system have:\\na content generator that can generate text, images and other content\\nbased on the data it was trained on?\\nautomated classification to read and classify written input, such as\\nevaluating and sorting customer complaints or reviewing customer\\nfeedback sentiment?\\na summary generator that can transform dense text into a high-quality\\nsummary, capture key points from financial reports, and generate meeting\\ntranscriptions?\\na data extraction capability to sort through complex details and quickly pull\\nthe necessary information from large documents?', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n')]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["documents\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["What is machine learning?\n", "Machine learning is a branch of artificial intelligence (AI) and computer science which\n", "focuses on the use of data and algorithms to imitate the way that humans learn,\n", "gradually improving its accuracy.\n", "IBM has a rich history with machine learning. One of its own, <PERSON>, is credited\n", "for coining the term, “machine learning” with his research (link resides outside ibm.com)\n", "around the game of checkers. <PERSON>, the self-proclaimed checkers master,\n", "played the game on an IBM 7094 computer in 1962, and he lost to the computer.\n", "Compared to what can be done today, this feat seems trivial, but it’s considered a major\n", "milestone in the field of artificial intelligence.\n", "Over the last couple of decades, the technological advances in storage and processing\n", "power have enabled some innovative products based on machine learning, such as\n", "Netflix’s recommendation engine and self-driving cars.\n", "Machine learning is an important component of the growing field of data science.\n", "Through the use of statistical methods, algorithms are trained to make classifications or\n", "predictions, and to uncover key insights in data mining projects. These insights\n", "subsequently drive decision making within applications and businesses, ideally\n", "impacting key growth metrics. As big data continues to expand and grow, the market\n", "demand for new data scientists will increase. They will be required to help identify the\n", "most relevant business questions and the data to answer them.\n", "Machine learning algorithms are typically created using frameworks such as Python that\n", "accelerate solution development by using platforms like TensorFlow or PyTorch.\n", "Now available: watsonx.ai\n", "The all-new enterprise studio that brings together traditional machine learning along\n", "with new generative AI capabilities powered by foundation models.\n", "Try watsonx.ai\n", "Begin your journey to AI\n", "Learn how to scale AI\n", "Explore the AI Academy\n", "Machine Learning vs. Deep Learning vs. Neural Networks\n", "Since deep learning and machine learning tend to be used interchangeably, it’s worth\n", "noting the nuances between the two. Machine learning, deep learning, and neural\n", "networks are all sub-fields of artificial intelligence. However, neural networks is actually\n", "a sub-field of machine learning, and deep learning is a sub-field of neural networks.\n", "The way in which deep learning and machine learning differ is in how each algorithm\n", "learns. \"Deep\" machine learning can use labeled datasets, also known as supervised\n", "learning, to inform its algorithm, but it doesn’t necessarily require a labeled dataset. The\n", "deep learning process can ingest unstructured data in its raw form (e.g., text or images),\n", "and it can automatically determine the set of features which distinguish different\n", "categories of data from one another. This eliminates some of the human intervention\n", "required and enables the use of large amounts of data. You can think of deep learning\n", "as \"scalable machine learning\" as <PERSON> notes in this MIT lecture (link resides\n", "outside ibm.com).\n", "Classical, or \"non-deep,\" machine learning is more dependent on human intervention to\n", "learn. Human experts determine the set of features to understand the differences\n", "between data inputs, usually requiring more structured data to learn.\n", "Neural networks, or artificial neural networks (ANNs), are comprised of node layers,\n", "containing an input layer, one or more hidden layers, and an output layer. Each node, or\n", "artificial neuron, connects to another and has an associated weight and threshold. If the\n", "output of any individual node is above the specified threshold value, that node is\n", "activated, sending data to the next layer of the network. Otherwise, no data is passed\n", "along to the next layer of the network by that node. The “deep” in deep learning is just\n", "referring to the number of layers in a neural network. A neural network that consists of\n", "more than three layers—which would be inclusive of the input and the output—can be\n", "considered a deep learning algorithm or a deep neural network. A neural network that\n", "only has three layers is just a basic neural network.\n", "Deep learning and neural networks are credited with accelerating progress in areas\n", "such as computer vision, natural language processing, and speech recognition.\n", "See the blog post “AI vs. Machine Learning vs. Deep Learning vs. Neural Networks:\n", "What’s the Difference?” for a closer look at how the different concepts relate.\n", "Related content\n", "Explore the watsonx.ai interactive demo\n", "Download “Machine learning for Dummies”\n", "- This link downloads a pdf\n", "Explore Gen AI for developers\n", "How does machine learning work?\n", "UC Berkeley (link resides outside ibm.com) breaks out the learning system of a\n", "machine learning algorithm into three main parts.\n", "A Decision Process: In general, machine learning algorithms are used to make a\n", "prediction or classification. Based on some input data, which can be labeled or\n", "unlabeled, your algorithm will produce an estimate about a pattern in the data.\n", "An Error Function: An error function evaluates the prediction of the model. If\n", "there are known examples, an error function can make a comparison to assess\n", "the accuracy of the model.\n", "A Model Optimization Process: If the model can fit better to the data points in the\n", "training set, then weights are adjusted to reduce the discrepancy between the\n", "known example and the model estimate. The algorithm will repeat this iterative\n", "“evaluate and optimize” process, updating weights autonomously until a\n", "threshold of accuracy has been met.\n", "Machine learning methods\n", "Machine learning models fall into three primary categories.\n", "Supervised machine learning\n", "Supervised learning, also known as supervised machine learning, is defined by its use\n", "of labeled datasets to train algorithms to classify data or predict outcomes accurately.\n", "As input data is fed into the model, the model adjusts its weights until it has been fitted\n", "appropriately. This occurs as part of the cross validation process to ensure that the\n", "model avoids overfitting or underfitting. Supervised learning helps organizations solve a\n", "variety of real-world problems at scale, such as classifying spam in a separate folder\n", "from your inbox. Some methods used in supervised learning include neural networks,\n", "naïve bayes, linear regression, logistic regression, random forest, and support vector\n", "machine (SVM).\n", "Unsupervised machine learning\n", "Unsupervised learning, also known as unsupervised machine learning, uses machine\n", "learning algorithms to analyze and cluster unlabeled datasets (subsets called clusters).\n", "These algorithms discover hidden patterns or data groupings without the need for\n", "human intervention. This method’s ability to discover similarities and differences in\n", "information make it ideal for exploratory data analysis, cross-selling strategies,\n", "customer segmentation, and image and pattern recognition. It’s also used to reduce the\n", "number of features in a model through the process of dimensionality reduction. Principal\n", "component analysis (PCA) and singular value decomposition (SVD) are two common\n", "approaches for this. Other algorithms used in unsupervised learning include neural\n", "networks, k-means clustering, and probabilistic clustering methods.\n", "Semi-supervised learning\n", "Semi-supervised learning offers a happy medium between supervised and\n", "unsupervised learning. During training, it uses a smaller labeled data set to guide\n", "classification and feature extraction from a larger, unlabeled data set. Semi-supervised\n", "learning can solve the problem of not having enough labeled data for a supervised\n", "learning algorithm. It also helps if it’s too costly to label enough data.\n", "For a deep dive into the differences between these approaches, check out \"Supervised\n", "vs. Unsupervised Learning: What's the Difference?\"\n", "Reinforcement machine learning\n", "Reinforcement machine learning is a machine learning model that is similar to\n", "supervised learning, but the algorithm isn’t trained using sample data. This model learns\n", "as it goes by using trial and error. A sequence of successful outcomes will be reinforced\n", "to develop the best recommendation or policy for a given problem.\n", "The IBM Watson® system that won the Jeopardy! challenge in 2011 is a good example.\n", "The system used reinforcement learning to learn when to attempt an answer (or\n", "question, as it were), which square to select on the board, and how much to\n", "wager—especially on daily doubles.\n", "Learn more about reinforcement learning\n", "Common machine learning algorithms\n", "A number of machine learning algorithms are commonly used. These include:\n", "Neural networks: Neural networks simulate the way the human brain works, with\n", "a huge number of linked processing nodes. Neural networks are good at\n", "recognizing patterns and play an important role in applications including natural\n", "language translation, image recognition, speech recognition, and image creation.\n", "Linear regression: This algorithm is used to predict numerical values, based on a\n", "linear relationship between different values. For example, the technique could be\n", "used to predict house prices based on historical data for the area.\n", "Logistic regression: This supervised learning algorithm makes predictions for\n", "categorical response variables, such as “yes/no” answers to questions. It can be\n", "used for applications such as classifying spam and quality control on a\n", "production line.\n", "Clustering: Using unsupervised learning, clustering algorithms can identify\n", "patterns in data so that it can be grouped. Computers can help data scientists by\n", "identifying differences between data items that humans have overlooked.\n", "Decision trees: Decision trees can be used for both predicting numerical values\n", "(regression) and classifying data into categories. Decision trees use a branching\n", "sequence of linked decisions that can be represented with a tree diagram. One of\n", "the advantages of decision trees is that they are easy to validate and audit,\n", "unlike the black box of the neural network.\n", "Random forests: In a random forest, the machine learning algorithm predicts a\n", "value or category by combining the results from a number of decision trees.\n", "Advantages and disadvantages of machine learning algorithms\n", "Depending on your budget, need for speed and precision required, each algorithm\n", "type—supervised, unsupervised, semi-supervised, or reinforcement—has its own\n", "advantages and disadvantages. For example, decision tree algorithms are used for both\n", "predicting numerical values (regression problems) and classifying data into categories.\n", "Decision trees use a branching sequence of linked decisions that may be represented\n", "with a tree diagram. A prime advantage of decision trees is that they are easier to\n", "validate and audit than a neural network. The bad news is that they can be more\n", "unstable than other decision predictors.\n", "Overall, there are many advantages to machine learning that businesses can leverage\n", "for new efficiencies. These include machine learning identifying patterns and trends in\n", "massive volumes of data that humans might not spot at all. And this analysis requires\n", "little human intervention: just feed in the dataset of interest and let the machine learning\n", "system assemble and refine its own algorithms—which will continually improve with\n", "more data input over time. Customers and users can enjoy a more personalized\n", "experience as the model learns more with every experience with that person.\n", "On the downside, machine learning requires large training datasets that are accurate\n", "and unbiased. GIGO is the operative factor: garbage in / garbage out. Gathering\n", "sufficient data and having a system robust enough to run it might also be a drain on\n", "resources. Machine learning can also be prone to error, depending on the input. With\n", "too small a sample, the system could produce a perfectly logical algorithm that is\n", "completely wrong or misleading. To avoid wasting budget or displeasing customers,\n", "organizations should act on the answers only when there is high confidence in the\n", "output.\n", "Real-world machine learning use cases\n", "Here are just a few examples of machine learning you might encounter every day:\n", "Speech recognition: It is also known as automatic speech recognition (ASR), computer\n", "speech recognition, or speech-to-text, and it is a capability which uses natural language\n", "processing (NLP) to translate human speech into a written format. Many mobile devices\n", "incorporate speech recognition into their systems to conduct voice search—e.g. <PERSON><PERSON>—or\n", "improve accessibility for texting.\n", "Customer service: Online chatbots are replacing human agents along the customer\n", "journey, changing the way we think about customer engagement across websites and\n", "social media platforms. Chatbots answer frequently asked questions (FAQs) about\n", "topics such as shipping, or provide personalized advice, cross-selling products or\n", "suggesting sizes for users. Examples include virtual agents on e-commerce sites;\n", "messaging bots, using Slack and Facebook Messenger; and tasks usually done by\n", "virtual assistants and voice assistants.\n", "Computer vision: This AI technology enables computers to derive meaningful\n", "information from digital images, videos, and other visual inputs, and then take the\n", "appropriate action. Powered by convolutional neural networks, computer vision has\n", "applications in photo tagging on social media, radiology imaging in healthcare, and\n", "self-driving cars in the automotive industry.\n", "Recommendation engines: Using past consumption behavior data, AI algorithms can\n", "help to discover data trends that can be used to develop more effective cross-selling\n", "strategies. Recommendation engines are used by online retailers to make relevant\n", "product recommendations to customers during the checkout process.\n", "Robotic process automation (RPA): Also known as software robotics, RPA uses\n", "intelligent automation technologies to perform repetitive manual tasks.\n", "Automated stock trading: Designed to optimize stock portfolios, AI-driven\n", "high-frequency trading platforms make thousands or even millions of trades per day\n", "without human intervention.\n", "Fraud detection: Banks and other financial institutions can use machine learning to spot\n", "suspicious transactions. Supervised learning can train a model using information about\n", "known fraudulent transactions. Anomaly detection can identify transactions that look\n", "atypical and deserve further investigation.\n", "Challenges of machine learning\n", "As machine learning technology has developed, it has certainly made our lives easier.\n", "However, implementing machine learning in businesses has also raised a number of\n", "ethical concerns about AI technologies. Some of these include:\n", "Technological singularity\n", "While this topic garners a lot of public attention, many researchers are not concerned\n", "with the idea of AI surpassing human intelligence in the near future. Technological\n", "singularity is also referred to as strong AI or superintelligence. Philosopher <PERSON>\n", "Bostrum defines superintelligence as “any intellect that vastly outperforms the best\n", "human brains in practically every field, including scientific creativity, general wisdom,\n", "and social skills.” Despite the fact that superintelligence is not imminent in society, the\n", "idea of it raises some interesting questions as we consider the use of autonomous\n", "systems, like self-driving cars. It’s unrealistic to think that a driverless car would never\n", "have an accident, but who is responsible and liable under those circumstances? Should\n", "we still develop autonomous vehicles, or do we limit this technology to\n", "semi-autonomous vehicles which help people drive safely? The jury is still out on this,\n", "but these are the types of ethical debates that are occurring as new, innovative AI\n", "technology develops.\n", "AI impact on jobs\n", "While a lot of public perception of artificial intelligence centers around job losses, this\n", "concern should probably be reframed. With every disruptive, new technology, we see\n", "that the market demand for specific job roles shifts. For example, when we look at the\n", "automotive industry, many manufacturers, like GM, are shifting to focus on electric\n", "vehicle production to align with green initiatives. The energy industry isn’t going away,\n", "but the source of energy is shifting from a fuel economy to an electric one.\n", "In a similar way, artificial intelligence will shift the demand for jobs to other areas. There\n", "will need to be individuals to help manage AI systems. There will still need to be people\n", "to address more complex problems within the industries that are most likely to be\n", "affected by job demand shifts, such as customer service. The biggest challenge with\n", "artificial intelligence and its effect on the job market will be helping people to transition\n", "to new roles that are in demand.\n", "Privacy\n", "Privacy tends to be discussed in the context of data privacy, data protection, and data\n", "security. These concerns have allowed policymakers to make more strides in recent\n", "years. For example, in 2016, GDPR legislation was created to protect the personal data\n", "of people in the European Union and European Economic Area, giving individuals more\n", "control of their data. In the United States, individual states are developing policies, such\n", "as the California Consumer Privacy Act (CCPA), which was introduced in 2018 and\n", "requires businesses to inform consumers about the collection of their data. Legislation\n", "such as this has forced companies to rethink how they store and use personally\n", "identifiable information (PII). As a result, investments in security have become an\n", "increasing priority for businesses as they seek to eliminate any vulnerabilities and\n", "opportunities for surveillance, hacking, and cyberattacks.\n", "Bias and discrimination\n", "Instances of bias and discrimination across a number of machine learning systems have\n", "raised many ethical questions regarding the use of artificial intelligence. How can we\n", "safeguard against bias and discrimination when the training data itself may be\n", "generated by biased human processes? While companies typically have good\n", "intentions for their automation efforts, Reuters (link resides outside ibm.com) highlights\n", "some of the unforeseen consequences of incorporating AI into hiring practices. In their\n", "effort to automate and simplify a process, Amazon unintentionally discriminated against\n", "job candidates by gender for technical roles, and the company ultimately had to scrap\n", "the project. Harvard Business Review (link resides outside ibm.com) has raised other\n", "pointed questions about the use of AI in hiring practices, such as what data you should\n", "be able to use when evaluating a candidate for a role.\n", "Bias and discrimination aren’t limited to the human resources function either; they can\n", "be found in a number of applications from facial recognition software to social media\n", "algorithms.\n", "As businesses become more aware of the risks with AI, they’ve also become more\n", "active in this discussion around AI ethics and values. For example, IBM has sunset its\n", "general purpose facial recognition and analysis products. IBM CEO <PERSON><PERSON><PERSON>\n", "wrote: “IBM firmly opposes and will not condone uses of any technology, including facial\n", "recognition technology offered by other vendors, for mass surveillance, racial profiling,\n", "violations of basic human rights and freedoms, or any purpose which is not consistent\n", "with our values and Principles of Trust and Transparency.”\n", "Accountability\n", "Since there isn’t significant legislation to regulate AI practices, there is no real\n", "enforcement mechanism to ensure that ethical AI is practiced. The current incentives for\n", "companies to be ethical are the negative repercussions of an unethical AI system on the\n", "bottom line. To fill the gap, ethical frameworks have emerged as part of a collaboration\n", "between ethicists and researchers to govern the construction and distribution of AI\n", "models within society. However, at the moment, these only serve to guide. Some\n", "research (link resides outside ibm.com) shows that the combination of distributed\n", "responsibility and a lack of foresight into potential consequences aren’t conducive to\n", "preventing harm to society.\n", "Read more about IBM's position on AI Ethics\n", "How to choose the right AI platform for machine learning\n", "Selecting a platform can be a challenging process, as the wrong system can drive up\n", "costs, or limit the use of other valuable tools or technologies. When reviewing multiple\n", "vendors to select an AI platform, there is often a tendency to think that more features =\n", "a better system. Maybe so, but reviewers should start by thinking through what the AI\n", "platform will be doing for their organization. What machine learning capabilities need to\n", "be delivered and what features are important to accomplish them? One missing feature\n", "might doom the usefulness of an entire system. Here are some features to consider.\n", "MLOps capabilities. Does the system have:\n", "a unified interface for ease of management?\n", "automated machine learning tools for faster model creation with low-code\n", "and no-code functionality?\n", "decision optimization to streamline the selection and deployment of\n", "optimization models?\n", "visual modeling to combine visual data science with open-source libraries\n", "and notebook-based interfaces on a unified data and AI studio?\n", "automated development for beginners to get started quickly and more\n", "advanced data scientists to experiment?\n", "synthetic data generator as an alternative or supplement to real-world data\n", "when real-world data is not readily available?\n", "Generative AI capabilities. Does the system have:\n", "a content generator that can generate text, images and other content\n", "based on the data it was trained on?\n", "automated classification to read and classify written input, such as\n", "evaluating and sorting customer complaints or reviewing customer\n", "feedback sentiment?\n", "a summary generator that can transform dense text into a high-quality\n", "summary, capture key points from financial reports, and generate meeting\n", "transcriptions?\n", "a data extraction capability to sort through complex details and quickly pull\n", "the necessary information from large documents?\n"]}], "source": ["print(documents[0].text)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["model=Gemini(models='gemini-pro',api_key=GOOGLE_API_KEY)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# grab embeddings from gemini embeddings model\n", "gemini_embed_model = GeminiEmbedding(model_name=\"models/embedding-001\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27624\\4136029042.py:2: DeprecationWarning: Call to deprecated class method from_defaults. (ServiceContext is deprecated, please use `llama_index.settings.Settings` instead.) -- Deprecated since version 0.10.0.\n", "  service_context = ServiceContext.from_defaults(llm=model,embed_model=gemini_embed_model, chunk_size=800, chunk_overlap=20)\n"]}], "source": ["# Configure Service Context\n", "service_context = ServiceContext.from_defaults(llm=model,embed_model=gemini_embed_model, chunk_size=800, chunk_overlap=20)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["index = VectorStoreIndex.from_documents(documents,service_context=service_context)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["index.storage_context.persist()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["query_engine = index.as_query_engine()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["\n", "response = query_engine.query(\"what is machine learning?\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Machine learning is a branch of artificial intelligence (AI) and computer science which focuses on the use of data and algorithms to imitate the way that humans learn, gradually improving its accuracy.'"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["response.response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "response = query_engine.query(\"what is attention mechnism\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response.response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}