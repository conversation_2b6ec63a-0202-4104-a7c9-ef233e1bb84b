{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Retriever And Chain With Langchain"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(page_content='Provided proper attribution is provided, Google hereby grants permission to\\nreproduce the tables and figures in this paper solely for use in journalistic or\\nscholarly works.\\nAttention Is All You Need\\nAshish Vaswani∗\\nGoogle Brain\\<EMAIL> Shazeer∗\\nGoogle Brain\\<EMAIL> Parmar∗\\nGoogle Research\\<EMAIL> <PERSON>z<PERSON>eit∗\\nGoogle Research\\<EMAIL>\\nLlion Jones∗\\nGoogle Research\\<EMAIL> N. Gomez∗ †\\nUniversity of Toronto\\<EMAIL>.eduŁ<PERSON>sz Kaiser∗\\nGoogle Brain\\<EMAIL>\\nIllia Polosukhin∗ ‡\\<EMAIL>\\nAbstract\\nThe dominant sequence transduction models are based on complex recurrent or\\nconvolutional neural networks that include an encoder and a decoder. The best\\nperforming models also connect the encoder and decoder through an attention\\nmechanism. We propose a new simple network architecture, the Transformer,\\nbased solely on attention mechanisms, dispensing with recurrence and convolutions\\nentirely. Experiments on two machine translation tasks show these models to\\nbe superior in quality while being more parallelizable and requiring significantly\\nless time to train. Our model achieves 28.4 BLEU on the WMT 2014 English-\\nto-German translation task, improving over the existing best results, including\\nensembles, by over 2 BLEU. On the WMT 2014 English-to-French translation task,\\nour model establishes a new single-model state-of-the-art BLEU score of 41.8 after\\ntraining for 3.5 days on eight GPUs, a small fraction of the training costs of the\\nbest models from the literature. We show that the Transformer generalizes well to\\nother tasks by applying it successfully to English constituency parsing both with\\nlarge and limited training data.\\n∗Equal contribution. Listing order is random. Jakob proposed replacing RNNs with self-attention and started\\nthe effort to evaluate this idea. Ashish, with Illia, designed and implemented the first Transformer models and\\nhas been crucially involved in every aspect of this work. Noam proposed scaled dot-product attention, multi-head\\nattention and the parameter-free position representation and became the other person involved in nearly every\\ndetail. Niki designed, implemented, tuned and evaluated countless model variants in our original codebase and\\ntensor2tensor. Llion also experimented with novel model variants, was responsible for our initial codebase, and\\nefficient inference and visualizations. Lukasz and Aidan spent countless long days designing various parts of and\\nimplementing tensor2tensor, replacing our earlier codebase, greatly improving results and massively accelerating\\nour research.\\n†Work performed while at Google Brain.\\n‡Work performed while at Google Research.\\n31st Conference on Neural Information Processing Systems (NIPS 2017), Long Beach, CA, USA.arXiv:1706.03762v7  [cs.CL]  2 Aug 2023', metadata={'source': 'attention.pdf', 'page': 0}),\n", " Document(page_content='1 Introduction\\nRecurrent neural networks, long short-term memory [ 13] and gated recurrent [ 7] neural networks\\nin particular, have been firmly established as state of the art approaches in sequence modeling and\\ntransduction problems such as language modeling and machine translation [ 35,2,5]. Numerous\\nefforts have since continued to push the boundaries of recurrent language models and encoder-decoder\\narchitectures [38, 24, 15].\\nRecurrent models typically factor computation along the symbol positions of the input and output\\nsequences. Aligning the positions to steps in computation time, they generate a sequence of hidden\\nstates ht, as a function of the previous hidden state ht−1and the input for position t. This inherently\\nsequential nature precludes parallelization within training examples, which becomes critical at longer\\nsequence lengths, as memory constraints limit batching across examples. Recent work has achieved\\nsignificant improvements in computational efficiency through factorization tricks [ 21] and conditional\\ncomputation [ 32], while also improving model performance in case of the latter. The fundamental\\nconstraint of sequential computation, however, remains.\\nAttention mechanisms have become an integral part of compelling sequence modeling and transduc-\\ntion models in various tasks, allowing modeling of dependencies without regard to their distance in\\nthe input or output sequences [ 2,19]. In all but a few cases [ 27], however, such attention mechanisms\\nare used in conjunction with a recurrent network.\\nIn this work we propose the Transformer, a model architecture eschewing recurrence and instead\\nrelying entirely on an attention mechanism to draw global dependencies between input and output.\\nThe Transformer allows for significantly more parallelization and can reach a new state of the art in\\ntranslation quality after being trained for as little as twelve hours on eight P100 GPUs.\\n2 Background\\nThe goal of reducing sequential computation also forms the foundation of the Extended Neural GPU\\n[16], ByteNet [ 18] and ConvS2S [ 9], all of which use convolutional neural networks as basic building\\nblock, computing hidden representations in parallel for all input and output positions. In these models,\\nthe number of operations required to relate signals from two arbitrary input or output positions grows\\nin the distance between positions, linearly for ConvS2S and logarithmically for ByteNet. This makes\\nit more difficult to learn dependencies between distant positions [ 12]. In the Transformer this is\\nreduced to a constant number of operations, albeit at the cost of reduced effective resolution due\\nto averaging attention-weighted positions, an effect we counteract with Multi-Head Attention as\\ndescribed in section 3.2.\\nSelf-attention, sometimes called intra-attention is an attention mechanism relating different positions\\nof a single sequence in order to compute a representation of the sequence. Self-attention has been\\nused successfully in a variety of tasks including reading comprehension, abstractive summarization,\\ntextual entailment and learning task-independent sentence representations [4, 27, 28, 22].\\nEnd-to-end memory networks are based on a recurrent attention mechanism instead of sequence-\\naligned recurrence and have been shown to perform well on simple-language question answering and\\nlanguage modeling tasks [34].\\nTo the best of our knowledge, however, the Transformer is the first transduction model relying\\nentirely on self-attention to compute representations of its input and output without using sequence-\\naligned RNNs or convolution. In the following sections, we will describe the Transformer, motivate\\nself-attention and discuss its advantages over models such as [17, 18] and [9].\\n3 Model Architecture\\nMost competitive neural sequence transduction models have an encoder-decoder structure [ 5,2,35].\\nHere, the encoder maps an input sequence of symbol representations (x1, ..., x n)to a sequence\\nof continuous representations z= (z1, ..., z n). Given z, the decoder then generates an output\\nsequence (y1, ..., y m)of symbols one element at a time. At each step the model is auto-regressive\\n[10], consuming the previously generated symbols as additional input when generating the next.\\n2', metadata={'source': 'attention.pdf', 'page': 1}),\n", " Document(page_content='Figure 1: The Transformer - model architecture.\\nThe Transformer follows this overall architecture using stacked self-attention and point-wise, fully\\nconnected layers for both the encoder and decoder, shown in the left and right halves of Figure 1,\\nrespectively.\\n3.1 Encoder and Decoder Stacks\\nEncoder: The encoder is composed of a stack of N= 6 identical layers. Each layer has two\\nsub-layers. The first is a multi-head self-attention mechanism, and the second is a simple, position-\\nwise fully connected feed-forward network. We employ a residual connection [ 11] around each of\\nthe two sub-layers, followed by layer normalization [ 1]. That is, the output of each sub-layer is\\nLayerNorm( x+ Sublayer( x)), where Sublayer( x)is the function implemented by the sub-layer\\nitself. To facilitate these residual connections, all sub-layers in the model, as well as the embedding\\nlayers, produce outputs of dimension dmodel = 512 .\\nDecoder: The decoder is also composed of a stack of N= 6identical layers. In addition to the two\\nsub-layers in each encoder layer, the decoder inserts a third sub-layer, which performs multi-head\\nattention over the output of the encoder stack. Similar to the encoder, we employ residual connections\\naround each of the sub-layers, followed by layer normalization. We also modify the self-attention\\nsub-layer in the decoder stack to prevent positions from attending to subsequent positions. This\\nmasking, combined with fact that the output embeddings are offset by one position, ensures that the\\npredictions for position ican depend only on the known outputs at positions less than i.\\n3.2 Attention\\nAn attention function can be described as mapping a query and a set of key-value pairs to an output,\\nwhere the query, keys, values, and output are all vectors. The output is computed as a weighted sum\\n3', metadata={'source': 'attention.pdf', 'page': 2}),\n", " Document(page_content='Scaled Dot-Product Attention\\n Multi-Head Attention\\nFigure 2: (left) Scaled Dot-Product Attention. (right) Multi-Head Attention consists of several\\nattention layers running in parallel.\\nof the values, where the weight assigned to each value is computed by a compatibility function of the\\nquery with the corresponding key.\\n3.2.1 Scaled Dot-Product Attention\\nWe call our particular attention \"Scaled Dot-Product Attention\" (Figure 2). The input consists of\\nqueries and keys of dimension dk, and values of dimension dv. We compute the dot products of the\\nquery with all keys, divide each by√dk, and apply a softmax function to obtain the weights on the\\nvalues.\\nIn practice, we compute the attention function on a set of queries simultaneously, packed together\\ninto a matrix Q. The keys and values are also packed together into matrices KandV. We compute\\nthe matrix of outputs as:\\nAttention( Q, K, V ) = softmax(QKT\\n√dk)V (1)\\nThe two most commonly used attention functions are additive attention [ 2], and dot-product (multi-\\nplicative) attention. Dot-product attention is identical to our algorithm, except for the scaling factor\\nof1√dk. Additive attention computes the compatibility function using a feed-forward network with\\na single hidden layer. While the two are similar in theoretical complexity, dot-product attention is\\nmuch faster and more space-efficient in practice, since it can be implemented using highly optimized\\nmatrix multiplication code.\\nWhile for small values of dkthe two mechanisms perform similarly, additive attention outperforms\\ndot product attention without scaling for larger values of dk[3]. We suspect that for large values of\\ndk, the dot products grow large in magnitude, pushing the softmax function into regions where it has\\nextremely small gradients4. To counteract this effect, we scale the dot products by1√dk.\\n3.2.2 Multi-Head Attention\\nInstead of performing a single attention function with dmodel-dimensional keys, values and queries,\\nwe found it beneficial to linearly project the queries, keys and values htimes with different, learned\\nlinear projections to dk,dkanddvdimensions, respectively. On each of these projected versions of\\nqueries, keys and values we then perform the attention function in parallel, yielding dv-dimensional\\n4To illustrate why the dot products get large, assume that the components of qandkare independent random\\nvariables with mean 0and variance 1. Then their dot product, q·k=Pdk\\ni=1qiki, has mean 0and variance dk.\\n4', metadata={'source': 'attention.pdf', 'page': 3}),\n", " Document(page_content='output values. These are concatenated and once again projected, resulting in the final values, as\\ndepicted in Figure 2.\\nMulti-head attention allows the model to jointly attend to information from different representation\\nsubspaces at different positions. With a single attention head, averaging inhibits this.\\nMultiHead( Q, K, V ) = Concat(head 1, ...,head h)WO\\nwhere head i= Attention( QWQ\\ni, KWK\\ni, V WV\\ni)\\nWhere the projections are parameter matrices WQ\\ni∈Rdmodel×dk,WK\\ni∈Rdmodel×dk,WV\\ni∈Rdmodel×dv\\nandWO∈Rhdv×dmodel.\\nIn this work we employ h= 8 parallel attention layers, or heads. For each of these we use\\ndk=dv=dmodel/h= 64 . Due to the reduced dimension of each head, the total computational cost\\nis similar to that of single-head attention with full dimensionality.\\n3.2.3 Applications of Attention in our Model\\nThe Transformer uses multi-head attention in three different ways:\\n•In \"encoder-decoder attention\" layers, the queries come from the previous decoder layer,\\nand the memory keys and values come from the output of the encoder. This allows every\\nposition in the decoder to attend over all positions in the input sequence. This mimics the\\ntypical encoder-decoder attention mechanisms in sequence-to-sequence models such as\\n[38, 2, 9].\\n•The encoder contains self-attention layers. In a self-attention layer all of the keys, values\\nand queries come from the same place, in this case, the output of the previous layer in the\\nencoder. Each position in the encoder can attend to all positions in the previous layer of the\\nencoder.\\n•Similarly, self-attention layers in the decoder allow each position in the decoder to attend to\\nall positions in the decoder up to and including that position. We need to prevent leftward\\ninformation flow in the decoder to preserve the auto-regressive property. We implement this\\ninside of scaled dot-product attention by masking out (setting to −∞) all values in the input\\nof the softmax which correspond to illegal connections. See Figure 2.\\n3.3 Position-wise Feed-Forward Networks\\nIn addition to attention sub-layers, each of the layers in our encoder and decoder contains a fully\\nconnected feed-forward network, which is applied to each position separately and identically. This\\nconsists of two linear transformations with a ReLU activation in between.\\nFFN( x) = max(0 , xW 1+b1)W2+b2 (2)\\nWhile the linear transformations are the same across different positions, they use different parameters\\nfrom layer to layer. Another way of describing this is as two convolutions with kernel size 1.\\nThe dimensionality of input and output is dmodel = 512 , and the inner-layer has dimensionality\\ndff= 2048 .\\n3.4 Embeddings and Softmax\\nSimilarly to other sequence transduction models, we use learned embeddings to convert the input\\ntokens and output tokens to vectors of dimension dmodel. We also use the usual learned linear transfor-\\nmation and softmax function to convert the decoder output to predicted next-token probabilities. In\\nour model, we share the same weight matrix between the two embedding layers and the pre-softmax\\nlinear transformation, similar to [ 30]. In the embedding layers, we multiply those weights by√dmodel.\\n5', metadata={'source': 'attention.pdf', 'page': 4}),\n", " Document(page_content='Table 1: Maximum path lengths, per-layer complexity and minimum number of sequential operations\\nfor different layer types. nis the sequence length, dis the representation dimension, kis the kernel\\nsize of convolutions and rthe size of the neighborhood in restricted self-attention.\\nLayer Type Complexity per Layer Sequential Maximum Path Length\\nOperations\\nSelf-Attention O(n2·d) O(1) O(1)\\nRecurrent O(n·d2) O(n) O(n)\\nConvolutional O(k·n·d2) O(1) O(logk(n))\\nSelf-Attention (restricted) O(r·n·d) O(1) O(n/r)\\n3.5 Positional Encoding\\nSince our model contains no recurrence and no convolution, in order for the model to make use of the\\norder of the sequence, we must inject some information about the relative or absolute position of the\\ntokens in the sequence. To this end, we add \"positional encodings\" to the input embeddings at the\\nbottoms of the encoder and decoder stacks. The positional encodings have the same dimension dmodel\\nas the embeddings, so that the two can be summed. There are many choices of positional encodings,\\nlearned and fixed [9].\\nIn this work, we use sine and cosine functions of different frequencies:\\nPE(pos,2i)=sin(pos/100002i/d model)\\nPE(pos,2i+1)=cos(pos/100002i/d model)\\nwhere posis the position and iis the dimension. That is, each dimension of the positional encoding\\ncorresponds to a sinusoid. The wavelengths form a geometric progression from 2πto10000 ·2π. We\\nchose this function because we hypothesized it would allow the model to easily learn to attend by\\nrelative positions, since for any fixed offset k,PEpos+kcan be represented as a linear function of\\nPEpos.\\nWe also experimented with using learned positional embeddings [ 9] instead, and found that the two\\nversions produced nearly identical results (see Table 3 row (E)). We chose the sinusoidal version\\nbecause it may allow the model to extrapolate to sequence lengths longer than the ones encountered\\nduring training.\\n4 Why Self-Attention\\nIn this section we compare various aspects of self-attention layers to the recurrent and convolu-\\ntional layers commonly used for mapping one variable-length sequence of symbol representations\\n(x1, ..., x n)to another sequence of equal length (z1, ..., z n), with xi, zi∈Rd, such as a hidden\\nlayer in a typical sequence transduction encoder or decoder. Motivating our use of self-attention we\\nconsider three desiderata.\\nOne is the total computational complexity per layer. Another is the amount of computation that can\\nbe parallelized, as measured by the minimum number of sequential operations required.\\nThe third is the path length between long-range dependencies in the network. Learning long-range\\ndependencies is a key challenge in many sequence transduction tasks. One key factor affecting the\\nability to learn such dependencies is the length of the paths forward and backward signals have to\\ntraverse in the network. The shorter these paths between any combination of positions in the input\\nand output sequences, the easier it is to learn long-range dependencies [ 12]. Hence we also compare\\nthe maximum path length between any two input and output positions in networks composed of the\\ndifferent layer types.\\nAs noted in Table 1, a self-attention layer connects all positions with a constant number of sequentially\\nexecuted operations, whereas a recurrent layer requires O(n)sequential operations. In terms of\\ncomputational complexity, self-attention layers are faster than recurrent layers when the sequence\\n6', metadata={'source': 'attention.pdf', 'page': 5}),\n", " Document(page_content='length nis smaller than the representation dimensionality d, which is most often the case with\\nsentence representations used by state-of-the-art models in machine translations, such as word-piece\\n[38] and byte-pair [ 31] representations. To improve computational performance for tasks involving\\nvery long sequences, self-attention could be restricted to considering only a neighborhood of size rin\\nthe input sequence centered around the respective output position. This would increase the maximum\\npath length to O(n/r). We plan to investigate this approach further in future work.\\nA single convolutional layer with kernel width k < n does not connect all pairs of input and output\\npositions. Doing so requires a stack of O(n/k)convolutional layers in the case of contiguous kernels,\\norO(logk(n))in the case of dilated convolutions [ 18], increasing the length of the longest paths\\nbetween any two positions in the network. Convolutional layers are generally more expensive than\\nrecurrent layers, by a factor of k. Separable convolutions [ 6], however, decrease the complexity\\nconsiderably, to O(k·n·d+n·d2). Even with k=n, however, the complexity of a separable\\nconvolution is equal to the combination of a self-attention layer and a point-wise feed-forward layer,\\nthe approach we take in our model.\\nAs side benefit, self-attention could yield more interpretable models. We inspect attention distributions\\nfrom our models and present and discuss examples in the appendix. Not only do individual attention\\nheads clearly learn to perform different tasks, many appear to exhibit behavior related to the syntactic\\nand semantic structure of the sentences.\\n5 Training\\nThis section describes the training regime for our models.\\n5.1 Training Data and Batching\\nWe trained on the standard WMT 2014 English-German dataset consisting of about 4.5 million\\nsentence pairs. Sentences were encoded using byte-pair encoding [ 3], which has a shared source-\\ntarget vocabulary of about 37000 tokens. For English-French, we used the significantly larger WMT\\n2014 English-French dataset consisting of 36M sentences and split tokens into a 32000 word-piece\\nvocabulary [ 38]. Sentence pairs were batched together by approximate sequence length. Each training\\nbatch contained a set of sentence pairs containing approximately 25000 source tokens and 25000\\ntarget tokens.\\n5.2 Hardware and Schedule\\nWe trained our models on one machine with 8 NVIDIA P100 GPUs. For our base models using\\nthe hyperparameters described throughout the paper, each training step took about 0.4 seconds. We\\ntrained the base models for a total of 100,000 steps or 12 hours. For our big models,(described on the\\nbottom line of table 3), step time was 1.0 seconds. The big models were trained for 300,000 steps\\n(3.5 days).\\n5.3 Optimizer\\nWe used the Adam optimizer [ 20] with β1= 0.9,β2= 0.98andϵ= 10−9. We varied the learning\\nrate over the course of training, according to the formula:\\nlrate =d−0.5\\nmodel·min(step_num−0.5, step _num·warmup _steps−1.5) (3)\\nThis corresponds to increasing the learning rate linearly for the first warmup _steps training steps,\\nand decreasing it thereafter proportionally to the inverse square root of the step number. We used\\nwarmup _steps = 4000 .\\n5.4 Regularization\\nWe employ three types of regularization during training:\\n7', metadata={'source': 'attention.pdf', 'page': 6}),\n", " Document(page_content='Table 2: The Transformer achieves better BLEU scores than previous state-of-the-art models on the\\nEnglish-to-German and English-to-French newstest2014 tests at a fraction of the training cost.\\nModelBLEU Training Cost (FLOPs)\\nEN-DE EN-FR EN-DE EN-FR\\nByteNet [18] 23.75\\nDeep-Att + PosUnk [39] 39.2 1.0·1020\\nGNMT + RL [38] 24.6 39.92 2.3·10191.4·1020\\nConvS2S [9] 25.16 40.46 9.6·10181.5·1020\\nMoE [32] 26.03 40.56 2.0·10191.2·1020\\nDeep-Att + PosUnk Ensemble [39] 40.4 8.0·1020\\nGNMT + RL Ensemble [38] 26.30 41.16 1.8·10201.1·1021\\nConvS2S Ensemble [9] 26.36 41.29 7.7·10191.2·1021\\nTransformer (base model) 27.3 38.1 3.3·1018\\nTransformer (big) 28.4 41.8 2.3·1019\\nResidual Dropout We apply dropout [ 33] to the output of each sub-layer, before it is added to the\\nsub-layer input and normalized. In addition, we apply dropout to the sums of the embeddings and the\\npositional encodings in both the encoder and decoder stacks. For the base model, we use a rate of\\nPdrop= 0.1.\\nLabel Smoothing During training, we employed label smoothing of value ϵls= 0.1[36]. This\\nhurts perplexity, as the model learns to be more unsure, but improves accuracy and BLEU score.\\n6 Results\\n6.1 Machine Translation\\nOn the WMT 2014 English-to-German translation task, the big transformer model (Transformer (big)\\nin Table 2) outperforms the best previously reported models (including ensembles) by more than 2.0\\nBLEU, establishing a new state-of-the-art BLEU score of 28.4. The configuration of this model is\\nlisted in the bottom line of Table 3. Training took 3.5days on 8P100 GPUs. Even our base model\\nsurpasses all previously published models and ensembles, at a fraction of the training cost of any of\\nthe competitive models.\\nOn the WMT 2014 English-to-French translation task, our big model achieves a BLEU score of 41.0,\\noutperforming all of the previously published single models, at less than 1/4the training cost of the\\nprevious state-of-the-art model. The Transformer (big) model trained for English-to-French used\\ndropout rate Pdrop= 0.1, instead of 0.3.\\nFor the base models, we used a single model obtained by averaging the last 5 checkpoints, which\\nwere written at 10-minute intervals. For the big models, we averaged the last 20 checkpoints. We\\nused beam search with a beam size of 4and length penalty α= 0.6[38]. These hyperparameters\\nwere chosen after experimentation on the development set. We set the maximum output length during\\ninference to input length + 50, but terminate early when possible [38].\\nTable 2 summarizes our results and compares our translation quality and training costs to other model\\narchitectures from the literature. We estimate the number of floating point operations used to train a\\nmodel by multiplying the training time, the number of GPUs used, and an estimate of the sustained\\nsingle-precision floating-point capacity of each GPU5.\\n6.2 Model Variations\\nTo evaluate the importance of different components of the Transformer, we varied our base model\\nin different ways, measuring the change in performance on English-to-German translation on the\\n5We used values of 2.8, 3.7, 6.0 and 9.5 TFLOPS for K80, K40, M40 and P100, respectively.\\n8', metadata={'source': 'attention.pdf', 'page': 7}),\n", " Document(page_content='Table 3: Variations on the Transformer architecture. Unlisted values are identical to those of the base\\nmodel. All metrics are on the English-to-German translation development set, newstest2013. Listed\\nperplexities are per-wordpiece, according to our byte-pair encoding, and should not be compared to\\nper-word perplexities.\\nN d model dff h d k dvPdrop ϵlstrain PPL BLEU params\\nsteps (dev) (dev) ×106\\nbase 6 512 2048 8 64 64 0.1 0.1 100K 4.92 25.8 65\\n(A)1 512 512 5.29 24.9\\n4 128 128 5.00 25.5\\n16 32 32 4.91 25.8\\n32 16 16 5.01 25.4\\n(B)16 5.16 25.1 58\\n32 5.01 25.4 60\\n(C)2 6.11 23.7 36\\n4 5.19 25.3 50\\n8 4.88 25.5 80\\n256 32 32 5.75 24.5 28\\n1024 128 128 4.66 26.0 168\\n1024 5.12 25.4 53\\n4096 4.75 26.2 90\\n(D)0.0 5.77 24.6\\n0.2 4.95 25.5\\n0.0 4.67 25.3\\n0.2 5.47 25.7\\n(E) positional embedding instead of sinusoids 4.92 25.7\\nbig 6 1024 4096 16 0.3 300K 4.33 26.4 213\\ndevelopment set, newstest2013. We used beam search as described in the previous section, but no\\ncheckpoint averaging. We present these results in Table 3.\\nIn Table 3 rows (A), we vary the number of attention heads and the attention key and value dimensions,\\nkeeping the amount of computation constant, as described in Section 3.2.2. While single-head\\nattention is 0.9 BLEU worse than the best setting, quality also drops off with too many heads.\\nIn Table 3 rows (B), we observe that reducing the attention key size dkhurts model quality. This\\nsuggests that determining compatibility is not easy and that a more sophisticated compatibility\\nfunction than dot product may be beneficial. We further observe in rows (C) and (D) that, as expected,\\nbigger models are better, and dropout is very helpful in avoiding over-fitting. In row (E) we replace our\\nsinusoidal positional encoding with learned positional embeddings [ 9], and observe nearly identical\\nresults to the base model.\\n6.3 English Constituency Parsing\\nTo evaluate if the Transformer can generalize to other tasks we performed experiments on English\\nconstituency parsing. This task presents specific challenges: the output is subject to strong structural\\nconstraints and is significantly longer than the input. Furthermore, RNN sequence-to-sequence\\nmodels have not been able to attain state-of-the-art results in small-data regimes [37].\\nWe trained a 4-layer transformer with dmodel = 1024 on the Wall Street Journal (WSJ) portion of the\\nPenn Treebank [ 25], about 40K training sentences. We also trained it in a semi-supervised setting,\\nusing the larger high-confidence and BerkleyParser corpora from with approximately 17M sentences\\n[37]. We used a vocabulary of 16K tokens for the WSJ only setting and a vocabulary of 32K tokens\\nfor the semi-supervised setting.\\nWe performed only a small number of experiments to select the dropout, both attention and residual\\n(section 5.4), learning rates and beam size on the Section 22 development set, all other parameters\\nremained unchanged from the English-to-German base translation model. During inference, we\\n9', metadata={'source': 'attention.pdf', 'page': 8}),\n", " Document(page_content='Table 4: The Transformer generalizes well to English constituency parsing (Results are on Section 23\\nof WSJ)\\nParser Training WSJ 23 F1\\nVinyals & Kaiser el al. (2014) [37] WSJ only, discriminative 88.3\\<PERSON><PERSON><PERSON><PERSON> et al. (2006) [29] WSJ only, discriminative 90.4\\<PERSON><PERSON><PERSON> et al. (2013) [40] WSJ only, discriminative 90.4\\n<PERSON><PERSON> et al. (2016) [8] WSJ only, discriminative 91.7\\nTransformer (4 layers) WSJ only, discriminative 91.3\\n<PERSON><PERSON> et al. (2013) [40] semi-supervised 91.3\\nH<PERSON> & <PERSON> (2009) [14] semi-supervised 91.3\\<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2006) [26] semi-supervised 92.1\\nVinyals & Kaiser el al. (2014) [37] semi-supervised 92.1\\nTransformer (4 layers) semi-supervised 92.7\\nLuong et al. (2015) [23] multi-task 93.0\\nD<PERSON> et al. (2016) [8] generative 93.3\\nincreased the maximum output length to input length + 300. We used a beam size of 21andα= 0.3\\nfor both WSJ only and the semi-supervised setting.\\nOur results in Table 4 show that despite the lack of task-specific tuning our model performs sur-\\nprisingly well, yielding better results than all previously reported models with the exception of the\\nRecurrent Neural Network Grammar [8].\\nIn contrast to RNN sequence-to-sequence models [ 37], the Transformer outperforms the Berkeley-\\nParser [29] even when training only on the WSJ training set of 40K sentences.\\n7 Conclusion\\nIn this work, we presented the Transformer, the first sequence transduction model based entirely on\\nattention, replacing the recurrent layers most commonly used in encoder-decoder architectures with\\nmulti-headed self-attention.\\nFor translation tasks, the Transformer can be trained significantly faster than architectures based\\non recurrent or convolutional layers. On both WMT 2014 English-to-German and WMT 2014\\nEnglish-to-French translation tasks, we achieve a new state of the art. In the former task our best\\nmodel outperforms even all previously reported ensembles.\\nWe are excited about the future of attention-based models and plan to apply them to other tasks. We\\nplan to extend the Transformer to problems involving input and output modalities other than text and\\nto investigate local, restricted attention mechanisms to efficiently handle large inputs and outputs\\nsuch as images, audio and video. Making generation less sequential is another research goals of ours.\\nThe code we used to train and evaluate our models is available at https://github.com/\\ntensorflow/tensor2tensor .\\nAcknowledgements We are grateful to Nal Kalchbrenner and Stephan Gouws for their fruitful\\ncomments, corrections and inspiration.\\nReferences\\n[1]Jimmy Lei Ba, Jamie Ryan Kiros, and Geoffrey E Hinton. Layer normalization. arXiv preprint\\narXiv:1607.06450 , 2016.\\n[2]Dzmitry Bahdanau, Kyunghyun Cho, and Yoshua Bengio. Neural machine translation by jointly\\nlearning to align and translate. CoRR , abs/1409.0473, 2014.\\n[3]Denny Britz, Anna Goldie, Minh-Thang Luong, and Quoc V . Le. Massive exploration of neural\\nmachine translation architectures. CoRR , abs/1703.03906, 2017.\\n[4]Jianpeng Cheng, Li Dong, and Mirella Lapata. Long short-term memory-networks for machine\\nreading. arXiv preprint arXiv:1601.06733 , 2016.\\n10', metadata={'source': 'attention.pdf', 'page': 9}),\n", " Document(page_content='[5]<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>,\\nand <PERSON><PERSON><PERSON>. Learning phrase representations using rnn encoder-decoder for statistical\\nmachine translation. CoRR , abs/1406.1078, 2014.\\n[6]<PERSON><PERSON>. Xception: Deep learning with depthwise separable convolutions. arXiv\\npreprint arXiv:1610.02357 , 2016.\\n[7]<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Empirical evaluation\\nof gated recurrent neural networks on sequence modeling. CoRR , abs/1412.3555, 2014.\\n[8]<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Recurrent neural\\nnetwork grammars. In Proc. of NAACL , 2016.\\n[9]<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Convolu-\\ntional sequence to sequence learning. arXiv preprint arXiv:1705.03122v2 , 2017.\\n[10] <PERSON>. Generating sequences with recurrent neural networks. arXiv preprint\\narXiv:1308.0850 , 2013.\\n[11] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Deep residual learning for im-\\nage recognition. In Proceedings of the IEEE Conference on Computer Vision and Pattern\\nRec<PERSON> , pages 770–778, 2016.\\n[12] Sepp Hochreiter, Yoshua Bengio, Paolo Frasconi, and Jürgen Schmidhuber. Gradient flow in\\nrecurrent nets: the difficulty of learning long-term dependencies, 2001.\\n[13] Sepp Hochreiter and Jürgen Schmidhuber. Long short-term memory. Neural computation ,\\n9(8):1735–1780, 1997.\\n[14] Zhongqiang Huang and Mary Harper. Self-training PCFG grammars with latent annotations\\nacross languages. In Proceedings of the 2009 Conference on Empirical Methods in Natural\\nLanguage Processing , pages 832–841. ACL, August 2009.\\n[15] Rafal Jozefowicz, Oriol Vinyals, Mike Schuster, Noam Shazeer, and Yonghui Wu. Exploring\\nthe limits of language modeling. arXiv preprint arXiv:1602.02410 , 2016.\\n[16] Łukasz Kaiser and Samy Bengio. Can active memory replace attention? In Advances in Neural\\nInformation Processing Systems, (NIPS) , 2016.\\n[17] Łukasz Kaiser and Ilya Sutskever. Neural GPUs learn algorithms. In International Conference\\non Learning Representations (ICLR) , 2016.\\n[18] Nal Kalchbrenner, Lasse Espeholt, Karen Simonyan, Aaron van den Oord, Alex Graves, and Ko-\\nray Kavukcuoglu. Neural machine translation in linear time. arXiv preprint arXiv:1610.10099v2 ,\\n2017.\\n[19] Yoon Kim, Carl Denton, Luong Hoang, and Alexander M. Rush. Structured attention networks.\\nInInternational Conference on Learning Representations , 2017.\\n[20] Diederik Kingma and Jimmy Ba. Adam: A method for stochastic optimization. In ICLR , 2015.\\n[21] Oleksii Kuchaiev and Boris Ginsburg. Factorization tricks for LSTM networks. arXiv preprint\\narXiv:1703.10722 , 2017.\\n[22] Zhouhan Lin, Minwei Feng, Cicero Nogueira dos Santos, Mo Yu, Bing Xiang, Bowen\\nZhou, and Yoshua Bengio. A structured self-attentive sentence embedding. arXiv preprint\\narXiv:1703.03130 , 2017.\\n[23] Minh-Thang Luong, Quoc V . Le, Ilya Sutskever, Oriol Vinyals, and Lukasz Kaiser. Multi-task\\nsequence to sequence learning. arXiv preprint arXiv:1511.06114 , 2015.\\n[24] Minh-Thang Luong, Hieu Pham, and Christopher D Manning. Effective approaches to attention-\\nbased neural machine translation. arXiv preprint arXiv:1508.04025 , 2015.\\n11', metadata={'source': 'attention.pdf', 'page': 10}),\n", " Document(page_content='[25] <PERSON>, <PERSON>, and <PERSON>. Building a large annotated\\ncorpus of english: The penn treebank. Computational linguistics , 19(2):313–330, 1993.\\n[26] <PERSON>, <PERSON>, and <PERSON>. Effective self-training for parsing. In\\nProceedings of the Human Language Technology Conference of the NAACL, Main Conference ,\\npages 152–159. ACL, June 2006.\\n[27] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A decomposable attention\\nmodel. In Empirical Methods in Natural Language Processing , 2016.\\n[28] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A deep reinforced model for abstractive\\nsummarization. arXiv preprint arXiv:1705.04304 , 2017.\\n[29] <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Learning accurate, compact,\\nand interpretable tree annotation. In Proceedings of the 21st International Conference on\\nComputational Linguistics and 44th Annual Meeting of the ACL , pages 433–440. ACL, July\\n2006.\\n[30] <PERSON><PERSON> Press and <PERSON><PERSON>. Using the output embedding to improve language models. arXiv\\npreprint arXiv:1608.05859 , 2016.\\n[31] <PERSON>, <PERSON>dow, and <PERSON>. Neural machine translation of rare words\\nwith subword units. arXiv preprint arXiv:1508.07909 , 2015.\\n[32] Noam Shazeer, Azalia Mirhoseini, Krzysztof Maziarz, Andy Davis, Quoc Le, <PERSON>,\\nand Jeff Dean. Outrageously large neural networks: The sparsely-gated mixture-of-experts\\nlayer. arXiv preprint arXiv:1701.06538 , 2017.\\n[33] Nitish Srivastava, Geoffrey E Hinton, Alex Krizhevsky, Ilya Sutskever, and Ruslan Salakhutdi-\\nnov. Dropout: a simple way to prevent neural networks from overfitting. Journal of Machine\\nLearning Research , 15(1):1929–1958, 2014.\\n[34] Sainbayar Sukhbaatar, Arthur Szlam, Jason Weston, and Rob Fergus. End-to-end memory\\nnetworks. In C. Cortes, N. D. Lawrence, D. D. Lee, M. Sugiyama, and R. Garnett, editors,\\nAdvances in Neural Information Processing Systems 28 , pages 2440–2448. Curran Associates,\\nInc., 2015.\\n[35] Ilya Sutskever, Oriol Vinyals, and Quoc VV Le. Sequence to sequence learning with neural\\nnetworks. In Advances in Neural Information Processing Systems , pages 3104–3112, 2014.\\n[36] Christian Szegedy, Vincent Vanhoucke, Sergey Ioffe, Jonathon Shlens, and Zbigniew Wojna.\\nRethinking the inception architecture for computer vision. CoRR , abs/1512.00567, 2015.\\n[37] Vinyals & Kaiser, Koo, Petrov, Sutskever, and Hinton. Grammar as a foreign language. In\\nAdvances in Neural Information Processing Systems , 2015.\\n[38] Yonghui Wu, Mike Schuster, Zhifeng Chen, Quoc V Le, Mohammad Norouzi, Wolfgang\\nMacherey, Maxim Krikun, Yuan Cao, Qin Gao, Klaus Macherey, et al. Google’s neural machine\\ntranslation system: Bridging the gap between human and machine translation. arXiv preprint\\narXiv:1609.08144 , 2016.\\n[39] Jie Zhou, Ying Cao, Xuguang Wang, Peng Li, and Wei Xu. Deep recurrent models with\\nfast-forward connections for neural machine translation. CoRR , abs/1606.04199, 2016.\\n[40] Muhua Zhu, Yue Zhang, Wenliang Chen, Min Zhang, and Jingbo Zhu. Fast and accurate\\nshift-reduce constituent parsing. In Proceedings of the 51st Annual Meeting of the ACL (Volume\\n1: Long Papers) , pages 434–443. ACL, August 2013.\\n12', metadata={'source': 'attention.pdf', 'page': 11}),\n", " Document(page_content='Attention Visualizations\\nInput-Input Layer5\\nIt\\nis\\nin\\nthis\\nspirit\\nthat\\na\\nmajority\\nof\\nAmerican\\ngovernments\\nhave\\npassed\\nnew\\nlaws\\nsince\\n2009\\nmaking\\nthe\\nregistration\\nor\\nvoting\\nprocess\\nmore\\ndifficult\\n.\\n<EOS>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\nIt\\nis\\nin\\nthis\\nspirit\\nthat\\na\\nmajority\\nof\\nAmerican\\ngovernments\\nhave\\npassed\\nnew\\nlaws\\nsince\\n2009\\nmaking\\nthe\\nregistration\\nor\\nvoting\\nprocess\\nmore\\ndifficult\\n.\\n<EOS>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\nFigure 3: An example of the attention mechanism following long-distance dependencies in the\\nencoder self-attention in layer 5 of 6. Many of the attention heads attend to a distant dependency of\\nthe verb ‘making’, completing the phrase ‘making...more difficult’. Attentions here shown only for\\nthe word ‘making’. Different colors represent different heads. Best viewed in color.\\n13', metadata={'source': 'attention.pdf', 'page': 12}),\n", " Document(page_content='Input-Input Layer5\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nInput-Input Layer5\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>Figure 4: Two attention heads, also in layer 5 of 6, apparently involved in anaphora resolution. Top:\\nFull attentions for head 5. Bottom: Isolated attentions from just the word ‘its’ for attention heads 5\\nand 6. Note that the attentions are very sharp for this word.\\n14', metadata={'source': 'attention.pdf', 'page': 13}),\n", " Document(page_content='Input-Input Layer5\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nInput-Input Layer5\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>Figure 5: Many of the attention heads exhibit behaviour that seems related to the structure of the\\nsentence. We give two such examples above, from two different heads from the encoder self-attention\\nat layer 5 of 6. The heads clearly learned to perform different tasks.\\n15', metadata={'source': 'attention.pdf', 'page': 14})]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_community.document_loaders import PyPDFLoader\n", "loader = PyPDFLoader(\"attention.pdf\")\n", "docs = loader.load()\n", "docs"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(page_content='Provided proper attribution is provided, Google hereby grants permission to\\nreproduce the tables and figures in this paper solely for use in journalistic or\\nscholarly works.\\nAttention Is All You Need\\nAshish Vaswani∗\\nGoogle Brain\\<EMAIL> Shazeer∗\\nGoogle Brain\\<EMAIL> Parmar∗\\nGoogle Research\\<EMAIL> Uszkoreit∗\\nGoogle Research\\<EMAIL>\\nLlion Jones∗\\nGoogle Research\\<EMAIL> N. Gomez∗ †\\nUniversity of Toronto\\<EMAIL><PERSON><PERSON>sz Kaiser∗\\nGoogle Brain\\<EMAIL>\\nIllia Polosukhin∗ ‡\\<EMAIL>\\nAbstract\\nThe dominant sequence transduction models are based on complex recurrent or\\nconvolutional neural networks that include an encoder and a decoder. The best\\nperforming models also connect the encoder and decoder through an attention\\nmechanism. We propose a new simple network architecture, the Transformer,\\nbased solely on attention mechanisms, dispensing with recurrence and convolutions', metadata={'source': 'attention.pdf', 'page': 0}),\n", " Document(page_content='entirely. Experiments on two machine translation tasks show these models to\\nbe superior in quality while being more parallelizable and requiring significantly\\nless time to train. Our model achieves 28.4 BLEU on the WMT 2014 English-\\nto-German translation task, improving over the existing best results, including\\nensembles, by over 2 BLEU. On the WMT 2014 English-to-French translation task,\\nour model establishes a new single-model state-of-the-art BLEU score of 41.8 after\\ntraining for 3.5 days on eight GPUs, a small fraction of the training costs of the\\nbest models from the literature. We show that the Transformer generalizes well to\\nother tasks by applying it successfully to English constituency parsing both with\\nlarge and limited training data.\\n∗Equal contribution. Listing order is random. <PERSON> proposed replacing RNNs with self-attention and started\\nthe effort to evaluate this idea. <PERSON><PERSON>, with <PERSON><PERSON>, designed and implemented the first Transformer models and', metadata={'source': 'attention.pdf', 'page': 0}),\n", " Document(page_content='has been crucially involved in every aspect of this work. <PERSON><PERSON> proposed scaled dot-product attention, multi-head\\nattention and the parameter-free position representation and became the other person involved in nearly every\\ndetail. <PERSON><PERSON> designed, implemented, tuned and evaluated countless model variants in our original codebase and\\ntensor2tensor. <PERSON><PERSON> also experimented with novel model variants, was responsible for our initial codebase, and\\nefficient inference and visualizations. <PERSON><PERSON><PERSON> and <PERSON> spent countless long days designing various parts of and\\nimplementing tensor2tensor, replacing our earlier codebase, greatly improving results and massively accelerating\\nour research.\\n†Work performed while at Google Brain.\\n‡Work performed while at Google Research.\\n31st Conference on Neural Information Processing Systems (NIPS 2017), Long Beach, CA, USA.arXiv:1706.03762v7  [cs.CL]  2 Aug 2023', metadata={'source': 'attention.pdf', 'page': 0}),\n", " Document(page_content='1 Introduction\\nRecurrent neural networks, long short-term memory [ 13] and gated recurrent [ 7] neural networks\\nin particular, have been firmly established as state of the art approaches in sequence modeling and\\ntransduction problems such as language modeling and machine translation [ 35,2,5]. Numerous\\nefforts have since continued to push the boundaries of recurrent language models and encoder-decoder\\narchitectures [38, 24, 15].\\nRecurrent models typically factor computation along the symbol positions of the input and output\\nsequences. Aligning the positions to steps in computation time, they generate a sequence of hidden\\nstates ht, as a function of the previous hidden state ht−1and the input for position t. This inherently\\nsequential nature precludes parallelization within training examples, which becomes critical at longer\\nsequence lengths, as memory constraints limit batching across examples. Recent work has achieved', metadata={'source': 'attention.pdf', 'page': 1}),\n", " Document(page_content='significant improvements in computational efficiency through factorization tricks [ 21] and conditional\\ncomputation [ 32], while also improving model performance in case of the latter. The fundamental\\nconstraint of sequential computation, however, remains.\\nAttention mechanisms have become an integral part of compelling sequence modeling and transduc-\\ntion models in various tasks, allowing modeling of dependencies without regard to their distance in\\nthe input or output sequences [ 2,19]. In all but a few cases [ 27], however, such attention mechanisms\\nare used in conjunction with a recurrent network.\\nIn this work we propose the Transformer, a model architecture eschewing recurrence and instead\\nrelying entirely on an attention mechanism to draw global dependencies between input and output.\\nThe Transformer allows for significantly more parallelization and can reach a new state of the art in\\ntranslation quality after being trained for as little as twelve hours on eight P100 GPUs.', metadata={'source': 'attention.pdf', 'page': 1})]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=20)\n", "text_splitter.split_documents(docs)[:5]"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(page_content='Provided proper attribution is provided, Google hereby grants permission to\\nreproduce the tables and figures in this paper solely for use in journalistic or\\nscholarly works.\\nAttention Is All You Need\\nAshish Vaswani∗\\nGoogle Brain\\<EMAIL> Shazeer∗\\nGoogle Brain\\<EMAIL> Parmar∗\\nGoogle Research\\<EMAIL> Uszkoreit∗\\nGoogle Research\\<EMAIL>\\nLlion Jones∗\\nGoogle Research\\<EMAIL> N. Gomez∗ †\\nUniversity of Toronto\\<EMAIL><PERSON><PERSON>sz Kaiser∗\\nGoogle Brain\\<EMAIL>\\nIllia Polosukhin∗ ‡\\<EMAIL>\\nAbstract\\nThe dominant sequence transduction models are based on complex recurrent or\\nconvolutional neural networks that include an encoder and a decoder. The best\\nperforming models also connect the encoder and decoder through an attention\\nmechanism. We propose a new simple network architecture, the Transformer,\\nbased solely on attention mechanisms, dispensing with recurrence and convolutions', metadata={'source': 'attention.pdf', 'page': 0}),\n", " Document(page_content='entirely. Experiments on two machine translation tasks show these models to\\nbe superior in quality while being more parallelizable and requiring significantly\\nless time to train. Our model achieves 28.4 BLEU on the WMT 2014 English-\\nto-German translation task, improving over the existing best results, including\\nensembles, by over 2 BLEU. On the WMT 2014 English-to-French translation task,\\nour model establishes a new single-model state-of-the-art BLEU score of 41.8 after\\ntraining for 3.5 days on eight GPUs, a small fraction of the training costs of the\\nbest models from the literature. We show that the Transformer generalizes well to\\nother tasks by applying it successfully to English constituency parsing both with\\nlarge and limited training data.\\n∗Equal contribution. Listing order is random. <PERSON> proposed replacing RNNs with self-attention and started\\nthe effort to evaluate this idea. <PERSON><PERSON>, with <PERSON><PERSON>, designed and implemented the first Transformer models and', metadata={'source': 'attention.pdf', 'page': 0}),\n", " Document(page_content='has been crucially involved in every aspect of this work. <PERSON><PERSON> proposed scaled dot-product attention, multi-head\\nattention and the parameter-free position representation and became the other person involved in nearly every\\ndetail. <PERSON><PERSON> designed, implemented, tuned and evaluated countless model variants in our original codebase and\\ntensor2tensor. <PERSON><PERSON> also experimented with novel model variants, was responsible for our initial codebase, and\\nefficient inference and visualizations. <PERSON><PERSON><PERSON> and <PERSON> spent countless long days designing various parts of and\\nimplementing tensor2tensor, replacing our earlier codebase, greatly improving results and massively accelerating\\nour research.\\n†Work performed while at Google Brain.\\n‡Work performed while at Google Research.\\n31st Conference on Neural Information Processing Systems (NIPS 2017), Long Beach, CA, USA.arXiv:1706.03762v7  [cs.CL]  2 Aug 2023', metadata={'source': 'attention.pdf', 'page': 0}),\n", " Document(page_content='1 Introduction\\nRecurrent neural networks, long short-term memory [ 13] and gated recurrent [ 7] neural networks\\nin particular, have been firmly established as state of the art approaches in sequence modeling and\\ntransduction problems such as language modeling and machine translation [ 35,2,5]. Numerous\\nefforts have since continued to push the boundaries of recurrent language models and encoder-decoder\\narchitectures [38, 24, 15].\\nRecurrent models typically factor computation along the symbol positions of the input and output\\nsequences. Aligning the positions to steps in computation time, they generate a sequence of hidden\\nstates ht, as a function of the previous hidden state ht−1and the input for position t. This inherently\\nsequential nature precludes parallelization within training examples, which becomes critical at longer\\nsequence lengths, as memory constraints limit batching across examples. Recent work has achieved', metadata={'source': 'attention.pdf', 'page': 1}),\n", " Document(page_content='significant improvements in computational efficiency through factorization tricks [ 21] and conditional\\ncomputation [ 32], while also improving model performance in case of the latter. The fundamental\\nconstraint of sequential computation, however, remains.\\nAttention mechanisms have become an integral part of compelling sequence modeling and transduc-\\ntion models in various tasks, allowing modeling of dependencies without regard to their distance in\\nthe input or output sequences [ 2,19]. In all but a few cases [ 27], however, such attention mechanisms\\nare used in conjunction with a recurrent network.\\nIn this work we propose the Transformer, a model architecture eschewing recurrence and instead\\nrelying entirely on an attention mechanism to draw global dependencies between input and output.\\nThe Transformer allows for significantly more parallelization and can reach a new state of the art in\\ntranslation quality after being trained for as little as twelve hours on eight P100 GPUs.', metadata={'source': 'attention.pdf', 'page': 1}),\n", " Document(page_content='2 Background\\nThe goal of reducing sequential computation also forms the foundation of the Extended Neural GPU\\n[16], ByteNet [ 18] and ConvS2S [ 9], all of which use convolutional neural networks as basic building\\nblock, computing hidden representations in parallel for all input and output positions. In these models,\\nthe number of operations required to relate signals from two arbitrary input or output positions grows\\nin the distance between positions, linearly for ConvS2S and logarithmically for ByteNet. This makes\\nit more difficult to learn dependencies between distant positions [ 12]. In the Transformer this is\\nreduced to a constant number of operations, albeit at the cost of reduced effective resolution due\\nto averaging attention-weighted positions, an effect we counteract with Multi-Head Attention as\\ndescribed in section 3.2.\\nSelf-attention, sometimes called intra-attention is an attention mechanism relating different positions', metadata={'source': 'attention.pdf', 'page': 1}),\n", " Document(page_content='of a single sequence in order to compute a representation of the sequence. Self-attention has been\\nused successfully in a variety of tasks including reading comprehension, abstractive summarization,\\ntextual entailment and learning task-independent sentence representations [4, 27, 28, 22].\\nEnd-to-end memory networks are based on a recurrent attention mechanism instead of sequence-\\naligned recurrence and have been shown to perform well on simple-language question answering and\\nlanguage modeling tasks [34].\\nTo the best of our knowledge, however, the Transformer is the first transduction model relying\\nentirely on self-attention to compute representations of its input and output without using sequence-\\naligned RNNs or convolution. In the following sections, we will describe the Transformer, motivate\\nself-attention and discuss its advantages over models such as [17, 18] and [9].\\n3 Model Architecture', metadata={'source': 'attention.pdf', 'page': 1}),\n", " Document(page_content='Most competitive neural sequence transduction models have an encoder-decoder structure [ 5,2,35].\\nHere, the encoder maps an input sequence of symbol representations (x1, ..., x n)to a sequence\\nof continuous representations z= (z1, ..., z n). Given z, the decoder then generates an output\\nsequence (y1, ..., y m)of symbols one element at a time. At each step the model is auto-regressive\\n[10], consuming the previously generated symbols as additional input when generating the next.\\n2', metadata={'source': 'attention.pdf', 'page': 1}),\n", " Document(page_content='Figure 1: The Transformer - model architecture.\\nThe Transformer follows this overall architecture using stacked self-attention and point-wise, fully\\nconnected layers for both the encoder and decoder, shown in the left and right halves of Figure 1,\\nrespectively.\\n3.1 Encoder and Decoder Stacks\\nEncoder: The encoder is composed of a stack of N= 6 identical layers. Each layer has two\\nsub-layers. The first is a multi-head self-attention mechanism, and the second is a simple, position-\\nwise fully connected feed-forward network. We employ a residual connection [ 11] around each of\\nthe two sub-layers, followed by layer normalization [ 1]. That is, the output of each sub-layer is\\nLayerNorm( x+ Sublayer( x)), where Sublayer( x)is the function implemented by the sub-layer\\nitself. To facilitate these residual connections, all sub-layers in the model, as well as the embedding\\nlayers, produce outputs of dimension dmodel = 512 .', metadata={'source': 'attention.pdf', 'page': 2}),\n", " Document(page_content='Decoder: The decoder is also composed of a stack of N= 6identical layers. In addition to the two\\nsub-layers in each encoder layer, the decoder inserts a third sub-layer, which performs multi-head\\nattention over the output of the encoder stack. Similar to the encoder, we employ residual connections\\naround each of the sub-layers, followed by layer normalization. We also modify the self-attention\\nsub-layer in the decoder stack to prevent positions from attending to subsequent positions. This\\nmasking, combined with fact that the output embeddings are offset by one position, ensures that the\\npredictions for position ican depend only on the known outputs at positions less than i.\\n3.2 Attention\\nAn attention function can be described as mapping a query and a set of key-value pairs to an output,\\nwhere the query, keys, values, and output are all vectors. The output is computed as a weighted sum\\n3', metadata={'source': 'attention.pdf', 'page': 2}),\n", " Document(page_content='Scaled Dot-Product Attention\\n Multi-Head Attention\\nFigure 2: (left) Scaled Dot-Product Attention. (right) Multi-Head Attention consists of several\\nattention layers running in parallel.\\nof the values, where the weight assigned to each value is computed by a compatibility function of the\\nquery with the corresponding key.\\n3.2.1 Scaled Dot-Product Attention\\nWe call our particular attention \"Scaled Dot-Product Attention\" (Figure 2). The input consists of\\nqueries and keys of dimension dk, and values of dimension dv. We compute the dot products of the\\nquery with all keys, divide each by√dk, and apply a softmax function to obtain the weights on the\\nvalues.\\nIn practice, we compute the attention function on a set of queries simultaneously, packed together\\ninto a matrix Q. The keys and values are also packed together into matrices KandV. We compute\\nthe matrix of outputs as:\\nAttention( Q, K, V ) = softmax(QKT\\n√dk)V (1)', metadata={'source': 'attention.pdf', 'page': 3}),\n", " Document(page_content='√dk)V (1)\\nThe two most commonly used attention functions are additive attention [ 2], and dot-product (multi-\\nplicative) attention. Dot-product attention is identical to our algorithm, except for the scaling factor\\nof1√dk. Additive attention computes the compatibility function using a feed-forward network with\\na single hidden layer. While the two are similar in theoretical complexity, dot-product attention is\\nmuch faster and more space-efficient in practice, since it can be implemented using highly optimized\\nmatrix multiplication code.\\nWhile for small values of dkthe two mechanisms perform similarly, additive attention outperforms\\ndot product attention without scaling for larger values of dk[3]. We suspect that for large values of\\ndk, the dot products grow large in magnitude, pushing the softmax function into regions where it has\\nextremely small gradients4. To counteract this effect, we scale the dot products by1√dk.\\n3.2.2 Multi-Head Attention', metadata={'source': 'attention.pdf', 'page': 3}),\n", " Document(page_content='Instead of performing a single attention function with dmodel-dimensional keys, values and queries,\\nwe found it beneficial to linearly project the queries, keys and values htimes with different, learned\\nlinear projections to dk,dkanddvdimensions, respectively. On each of these projected versions of\\nqueries, keys and values we then perform the attention function in parallel, yielding dv-dimensional\\n4To illustrate why the dot products get large, assume that the components of qandkare independent random\\nvariables with mean 0and variance 1. Then their dot product, q·k=Pdk\\ni=1qiki, has mean 0and variance dk.\\n4', metadata={'source': 'attention.pdf', 'page': 3}),\n", " Document(page_content='output values. These are concatenated and once again projected, resulting in the final values, as\\ndepicted in Figure 2.\\nMulti-head attention allows the model to jointly attend to information from different representation\\nsubspaces at different positions. With a single attention head, averaging inhibits this.\\nMultiHead( Q, K, V ) = Concat(head 1, ...,head h)WO\\nwhere head i= Attention( QWQ\\ni, KWK\\ni, V WV\\ni)\\nWhere the projections are parameter matrices WQ\\ni∈Rdmodel×dk,WK\\ni∈Rdmodel×dk,WV\\ni∈Rdmodel×dv\\nandWO∈Rhdv×dmodel.\\nIn this work we employ h= 8 parallel attention layers, or heads. For each of these we use\\ndk=dv=dmodel/h= 64 . Due to the reduced dimension of each head, the total computational cost\\nis similar to that of single-head attention with full dimensionality.\\n3.2.3 Applications of Attention in our Model\\nThe Transformer uses multi-head attention in three different ways:\\n•In \"encoder-decoder attention\" layers, the queries come from the previous decoder layer,', metadata={'source': 'attention.pdf', 'page': 4}),\n", " Document(page_content='and the memory keys and values come from the output of the encoder. This allows every\\nposition in the decoder to attend over all positions in the input sequence. This mimics the\\ntypical encoder-decoder attention mechanisms in sequence-to-sequence models such as\\n[38, 2, 9].\\n•The encoder contains self-attention layers. In a self-attention layer all of the keys, values\\nand queries come from the same place, in this case, the output of the previous layer in the\\nencoder. Each position in the encoder can attend to all positions in the previous layer of the\\nencoder.\\n•Similarly, self-attention layers in the decoder allow each position in the decoder to attend to\\nall positions in the decoder up to and including that position. We need to prevent leftward\\ninformation flow in the decoder to preserve the auto-regressive property. We implement this\\ninside of scaled dot-product attention by masking out (setting to −∞) all values in the input', metadata={'source': 'attention.pdf', 'page': 4}),\n", " Document(page_content='of the softmax which correspond to illegal connections. See Figure 2.\\n3.3 Position-wise Feed-Forward Networks\\nIn addition to attention sub-layers, each of the layers in our encoder and decoder contains a fully\\nconnected feed-forward network, which is applied to each position separately and identically. This\\nconsists of two linear transformations with a ReLU activation in between.\\nFFN( x) = max(0 , xW 1+b1)W2+b2 (2)\\nWhile the linear transformations are the same across different positions, they use different parameters\\nfrom layer to layer. Another way of describing this is as two convolutions with kernel size 1.\\nThe dimensionality of input and output is dmodel = 512 , and the inner-layer has dimensionality\\ndff= 2048 .\\n3.4 Embeddings and Softmax\\nSimilarly to other sequence transduction models, we use learned embeddings to convert the input\\ntokens and output tokens to vectors of dimension dmodel. We also use the usual learned linear transfor-', metadata={'source': 'attention.pdf', 'page': 4}),\n", " Document(page_content='mation and softmax function to convert the decoder output to predicted next-token probabilities. In\\nour model, we share the same weight matrix between the two embedding layers and the pre-softmax\\nlinear transformation, similar to [ 30]. In the embedding layers, we multiply those weights by√dmodel.\\n5', metadata={'source': 'attention.pdf', 'page': 4}),\n", " Document(page_content='Table 1: Maximum path lengths, per-layer complexity and minimum number of sequential operations\\nfor different layer types. nis the sequence length, dis the representation dimension, kis the kernel\\nsize of convolutions and rthe size of the neighborhood in restricted self-attention.\\nLayer Type Complexity per Layer Sequential Maximum Path Length\\nOperations\\nSelf-Attention O(n2·d) O(1) O(1)\\nRecurrent O(n·d2) O(n) O(n)\\nConvolutional O(k·n·d2) O(1) O(logk(n))\\nSelf-Attention (restricted) O(r·n·d) O(1) O(n/r)\\n3.5 Positional Encoding\\nSince our model contains no recurrence and no convolution, in order for the model to make use of the\\norder of the sequence, we must inject some information about the relative or absolute position of the\\ntokens in the sequence. To this end, we add \"positional encodings\" to the input embeddings at the\\nbottoms of the encoder and decoder stacks. The positional encodings have the same dimension dmodel', metadata={'source': 'attention.pdf', 'page': 5}),\n", " Document(page_content='as the embeddings, so that the two can be summed. There are many choices of positional encodings,\\nlearned and fixed [9].\\nIn this work, we use sine and cosine functions of different frequencies:\\nPE(pos,2i)=sin(pos/100002i/d model)\\nPE(pos,2i+1)=cos(pos/100002i/d model)\\nwhere posis the position and iis the dimension. That is, each dimension of the positional encoding\\ncorresponds to a sinusoid. The wavelengths form a geometric progression from 2πto10000 ·2π. We\\nchose this function because we hypothesized it would allow the model to easily learn to attend by\\nrelative positions, since for any fixed offset k,PEpos+kcan be represented as a linear function of\\nPEpos.\\nWe also experimented with using learned positional embeddings [ 9] instead, and found that the two\\nversions produced nearly identical results (see Table 3 row (E)). We chose the sinusoidal version\\nbecause it may allow the model to extrapolate to sequence lengths longer than the ones encountered\\nduring training.\\n4 Why Self-Attention', metadata={'source': 'attention.pdf', 'page': 5}),\n", " Document(page_content='In this section we compare various aspects of self-attention layers to the recurrent and convolu-\\ntional layers commonly used for mapping one variable-length sequence of symbol representations\\n(x1, ..., x n)to another sequence of equal length (z1, ..., z n), with xi, zi∈Rd, such as a hidden\\nlayer in a typical sequence transduction encoder or decoder. Motivating our use of self-attention we\\nconsider three desiderata.\\nOne is the total computational complexity per layer. Another is the amount of computation that can\\nbe parallelized, as measured by the minimum number of sequential operations required.\\nThe third is the path length between long-range dependencies in the network. Learning long-range\\ndependencies is a key challenge in many sequence transduction tasks. One key factor affecting the\\nability to learn such dependencies is the length of the paths forward and backward signals have to\\ntraverse in the network. The shorter these paths between any combination of positions in the input', metadata={'source': 'attention.pdf', 'page': 5}),\n", " Document(page_content='and output sequences, the easier it is to learn long-range dependencies [ 12]. Hence we also compare\\nthe maximum path length between any two input and output positions in networks composed of the\\ndifferent layer types.\\nAs noted in Table 1, a self-attention layer connects all positions with a constant number of sequentially\\nexecuted operations, whereas a recurrent layer requires O(n)sequential operations. In terms of\\ncomputational complexity, self-attention layers are faster than recurrent layers when the sequence\\n6', metadata={'source': 'attention.pdf', 'page': 5}),\n", " Document(page_content='length nis smaller than the representation dimensionality d, which is most often the case with\\nsentence representations used by state-of-the-art models in machine translations, such as word-piece\\n[38] and byte-pair [ 31] representations. To improve computational performance for tasks involving\\nvery long sequences, self-attention could be restricted to considering only a neighborhood of size rin\\nthe input sequence centered around the respective output position. This would increase the maximum\\npath length to O(n/r). We plan to investigate this approach further in future work.\\nA single convolutional layer with kernel width k < n does not connect all pairs of input and output\\npositions. Doing so requires a stack of O(n/k)convolutional layers in the case of contiguous kernels,\\norO(logk(n))in the case of dilated convolutions [ 18], increasing the length of the longest paths\\nbetween any two positions in the network. Convolutional layers are generally more expensive than', metadata={'source': 'attention.pdf', 'page': 6}),\n", " Document(page_content='recurrent layers, by a factor of k. Separable convolutions [ 6], however, decrease the complexity\\nconsiderably, to O(k·n·d+n·d2). Even with k=n, however, the complexity of a separable\\nconvolution is equal to the combination of a self-attention layer and a point-wise feed-forward layer,\\nthe approach we take in our model.\\nAs side benefit, self-attention could yield more interpretable models. We inspect attention distributions\\nfrom our models and present and discuss examples in the appendix. Not only do individual attention\\nheads clearly learn to perform different tasks, many appear to exhibit behavior related to the syntactic\\nand semantic structure of the sentences.\\n5 Training\\nThis section describes the training regime for our models.\\n5.1 Training Data and Batching\\nWe trained on the standard WMT 2014 English-German dataset consisting of about 4.5 million\\nsentence pairs. Sentences were encoded using byte-pair encoding [ 3], which has a shared source-', metadata={'source': 'attention.pdf', 'page': 6}),\n", " Document(page_content='target vocabulary of about 37000 tokens. For English-French, we used the significantly larger WMT\\n2014 English-French dataset consisting of 36M sentences and split tokens into a 32000 word-piece\\nvocabulary [ 38]. Sentence pairs were batched together by approximate sequence length. Each training\\nbatch contained a set of sentence pairs containing approximately 25000 source tokens and 25000\\ntarget tokens.\\n5.2 Hardware and Schedule\\nWe trained our models on one machine with 8 NVIDIA P100 GPUs. For our base models using\\nthe hyperparameters described throughout the paper, each training step took about 0.4 seconds. We\\ntrained the base models for a total of 100,000 steps or 12 hours. For our big models,(described on the\\nbottom line of table 3), step time was 1.0 seconds. The big models were trained for 300,000 steps\\n(3.5 days).\\n5.3 Optimizer\\nWe used the Adam optimizer [ 20] with β1= 0.9,β2= 0.98andϵ= 10−9. We varied the learning\\nrate over the course of training, according to the formula:', metadata={'source': 'attention.pdf', 'page': 6}),\n", " Document(page_content='lrate =d−0.5\\nmodel·min(step_num−0.5, step _num·warmup _steps−1.5) (3)\\nThis corresponds to increasing the learning rate linearly for the first warmup _steps training steps,\\nand decreasing it thereafter proportionally to the inverse square root of the step number. We used\\nwarmup _steps = 4000 .\\n5.4 Regularization\\nWe employ three types of regularization during training:\\n7', metadata={'source': 'attention.pdf', 'page': 6}),\n", " Document(page_content='Table 2: The Transformer achieves better BLEU scores than previous state-of-the-art models on the\\nEnglish-to-German and English-to-French newstest2014 tests at a fraction of the training cost.\\nModelBLEU Training Cost (FLOPs)\\nEN-DE EN-FR EN-DE EN-FR\\nByteNet [18] 23.75\\nDeep-Att + PosUnk [39] 39.2 1.0·1020\\nGNMT + RL [38] 24.6 39.92 2.3·10191.4·1020\\nConvS2S [9] 25.16 40.46 9.6·10181.5·1020\\nMoE [32] 26.03 40.56 2.0·10191.2·1020\\nDeep-Att + PosUnk Ensemble [39] 40.4 8.0·1020\\nGNMT + RL Ensemble [38] 26.30 41.16 1.8·10201.1·1021\\nConvS2S Ensemble [9] 26.36 41.29 7.7·10191.2·1021\\nTransformer (base model) 27.3 38.1 3.3·1018\\nTransformer (big) 28.4 41.8 2.3·1019\\nResidual Dropout We apply dropout [ 33] to the output of each sub-layer, before it is added to the\\nsub-layer input and normalized. In addition, we apply dropout to the sums of the embeddings and the\\npositional encodings in both the encoder and decoder stacks. For the base model, we use a rate of\\nPdrop= 0.1.', metadata={'source': 'attention.pdf', 'page': 7}),\n", " Document(page_content='Pdrop= 0.1.\\n<PERSON><PERSON><PERSON> Smoothing During training, we employed label smoothing of value ϵls= 0.1[36]. This\\nhurts perplexity, as the model learns to be more unsure, but improves accuracy and BLEU score.\\n6 Results\\n6.1 Machine Translation\\nOn the WMT 2014 English-to-German translation task, the big transformer model (Transformer (big)\\nin Table 2) outperforms the best previously reported models (including ensembles) by more than 2.0\\nBLEU, establishing a new state-of-the-art BLEU score of 28.4. The configuration of this model is\\nlisted in the bottom line of Table 3. Training took 3.5days on 8P100 GPUs. Even our base model\\nsurpasses all previously published models and ensembles, at a fraction of the training cost of any of\\nthe competitive models.\\nOn the WMT 2014 English-to-French translation task, our big model achieves a BLEU score of 41.0,\\noutperforming all of the previously published single models, at less than 1/4the training cost of the', metadata={'source': 'attention.pdf', 'page': 7}),\n", " Document(page_content='previous state-of-the-art model. The Transformer (big) model trained for English-to-French used\\ndropout rate Pdrop= 0.1, instead of 0.3.\\nFor the base models, we used a single model obtained by averaging the last 5 checkpoints, which\\nwere written at 10-minute intervals. For the big models, we averaged the last 20 checkpoints. We\\nused beam search with a beam size of 4and length penalty α= 0.6[38]. These hyperparameters\\nwere chosen after experimentation on the development set. We set the maximum output length during\\ninference to input length + 50, but terminate early when possible [38].\\nTable 2 summarizes our results and compares our translation quality and training costs to other model\\narchitectures from the literature. We estimate the number of floating point operations used to train a\\nmodel by multiplying the training time, the number of GPUs used, and an estimate of the sustained\\nsingle-precision floating-point capacity of each GPU5.\\n6.2 Model Variations', metadata={'source': 'attention.pdf', 'page': 7}),\n", " Document(page_content='To evaluate the importance of different components of the Transformer, we varied our base model\\nin different ways, measuring the change in performance on English-to-German translation on the\\n5We used values of 2.8, 3.7, 6.0 and 9.5 TFLOPS for K80, K40, M40 and P100, respectively.\\n8', metadata={'source': 'attention.pdf', 'page': 7}),\n", " Document(page_content='Table 3: Variations on the Transformer architecture. Unlisted values are identical to those of the base\\nmodel. All metrics are on the English-to-German translation development set, newstest2013. Listed\\nperplexities are per-wordpiece, according to our byte-pair encoding, and should not be compared to\\nper-word perplexities.\\nN d model dff h d k dvPdrop ϵlstrain PPL BLEU params\\nsteps (dev) (dev) ×106\\nbase 6 512 2048 8 64 64 0.1 0.1 100K 4.92 25.8 65\\n(A)1 512 512 5.29 24.9\\n4 128 128 5.00 25.5\\n16 32 32 4.91 25.8\\n32 16 16 5.01 25.4\\n(B)16 5.16 25.1 58\\n32 5.01 25.4 60\\n(C)2 6.11 23.7 36\\n4 5.19 25.3 50\\n8 4.88 25.5 80\\n256 32 32 5.75 24.5 28\\n1024 128 128 4.66 26.0 168\\n1024 5.12 25.4 53\\n4096 4.75 26.2 90\\n(D)0.0 5.77 24.6\\n0.2 4.95 25.5\\n0.0 4.67 25.3\\n0.2 5.47 25.7\\n(E) positional embedding instead of sinusoids 4.92 25.7\\nbig 6 1024 4096 16 0.3 300K 4.33 26.4 213\\ndevelopment set, newstest2013. We used beam search as described in the previous section, but no', metadata={'source': 'attention.pdf', 'page': 8}),\n", " Document(page_content='checkpoint averaging. We present these results in Table 3.\\nIn Table 3 rows (A), we vary the number of attention heads and the attention key and value dimensions,\\nkeeping the amount of computation constant, as described in Section 3.2.2. While single-head\\nattention is 0.9 BLEU worse than the best setting, quality also drops off with too many heads.\\nIn Table 3 rows (B), we observe that reducing the attention key size dkhurts model quality. This\\nsuggests that determining compatibility is not easy and that a more sophisticated compatibility\\nfunction than dot product may be beneficial. We further observe in rows (C) and (D) that, as expected,\\nbigger models are better, and dropout is very helpful in avoiding over-fitting. In row (E) we replace our\\nsinusoidal positional encoding with learned positional embeddings [ 9], and observe nearly identical\\nresults to the base model.\\n6.3 English Constituency Parsing', metadata={'source': 'attention.pdf', 'page': 8}),\n", " Document(page_content='To evaluate if the Transformer can generalize to other tasks we performed experiments on English\\nconstituency parsing. This task presents specific challenges: the output is subject to strong structural\\nconstraints and is significantly longer than the input. Furthermore, RNN sequence-to-sequence\\nmodels have not been able to attain state-of-the-art results in small-data regimes [37].\\nWe trained a 4-layer transformer with dmodel = 1024 on the Wall Street Journal (WSJ) portion of the\\nPenn Treebank [ 25], about 40K training sentences. We also trained it in a semi-supervised setting,\\nusing the larger high-confidence and BerkleyParser corpora from with approximately 17M sentences\\n[37]. We used a vocabulary of 16K tokens for the WSJ only setting and a vocabulary of 32K tokens\\nfor the semi-supervised setting.\\nWe performed only a small number of experiments to select the dropout, both attention and residual', metadata={'source': 'attention.pdf', 'page': 8}),\n", " Document(page_content='(section 5.4), learning rates and beam size on the Section 22 development set, all other parameters\\nremained unchanged from the English-to-German base translation model. During inference, we\\n9', metadata={'source': 'attention.pdf', 'page': 8}),\n", " Document(page_content='Table 4: The Transformer generalizes well to English constituency parsing (Results are on Section 23\\nof WSJ)\\nParser Training WSJ 23 F1\\nVinyals & Kaiser el al. (2014) [37] WSJ only, discriminative 88.3\\<PERSON><PERSON><PERSON><PERSON> et al. (2006) [29] WSJ only, discriminative 90.4\\<PERSON><PERSON><PERSON> et al. (2013) [40] WSJ only, discriminative 90.4\\n<PERSON><PERSON> et al. (2016) [8] WSJ only, discriminative 91.7\\nTransformer (4 layers) WSJ only, discriminative 91.3\\n<PERSON><PERSON> et al. (2013) [40] semi-supervised 91.3\\<PERSON>H<PERSON> & <PERSON> (2009) [14] semi-supervised 91.3\\<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2006) [26] semi-supervised 92.1\\nVinyals & Kaiser el al. (2014) [37] semi-supervised 92.1\\nTransformer (4 layers) semi-supervised 92.7\\nLuo<PERSON> et al. (2015) [23] multi-task 93.0\\nD<PERSON> et al. (2016) [8] generative 93.3\\nincreased the maximum output length to input length + 300. We used a beam size of 21andα= 0.3\\nfor both WSJ only and the semi-supervised setting.\\nOur results in Table 4 show that despite the lack of task-specific tuning our model performs sur-', metadata={'source': 'attention.pdf', 'page': 9}),\n", " Document(page_content='prisingly well, yielding better results than all previously reported models with the exception of the\\nRecurrent Neural Network Grammar [8].\\nIn contrast to RNN sequence-to-sequence models [ 37], the Transformer outperforms the Berkeley-\\nParser [29] even when training only on the WSJ training set of 40K sentences.\\n7 Conclusion\\nIn this work, we presented the Transformer, the first sequence transduction model based entirely on\\nattention, replacing the recurrent layers most commonly used in encoder-decoder architectures with\\nmulti-headed self-attention.\\nFor translation tasks, the Transformer can be trained significantly faster than architectures based\\non recurrent or convolutional layers. On both WMT 2014 English-to-German and WMT 2014\\nEnglish-to-French translation tasks, we achieve a new state of the art. In the former task our best\\nmodel outperforms even all previously reported ensembles.\\nWe are excited about the future of attention-based models and plan to apply them to other tasks. We', metadata={'source': 'attention.pdf', 'page': 9}),\n", " Document(page_content='plan to extend the Transformer to problems involving input and output modalities other than text and\\nto investigate local, restricted attention mechanisms to efficiently handle large inputs and outputs\\nsuch as images, audio and video. Making generation less sequential is another research goals of ours.\\nThe code we used to train and evaluate our models is available at https://github.com/\\ntensorflow/tensor2tensor .\\nAcknowledgements We are grateful to <PERSON><PERSON> and <PERSON> for their fruitful\\ncomments, corrections and inspiration.\\nReferences\\n[1]<PERSON>, <PERSON>, and <PERSON>. Layer normalization. arXiv preprint\\narXiv:1607.06450 , 2016.\\n[2]<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Neural machine translation by jointly\\nlearning to align and translate. CoRR , abs/1409.0473, 2014.\\n[3]<PERSON>, <PERSON>, <PERSON>, and Quoc V . Le. Massive exploration of neural\\nmachine translation architectures. CoRR , abs/1703.03906, 2017.', metadata={'source': 'attention.pdf', 'page': 9}),\n", " Document(page_content='[4]<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Long short-term memory-networks for machine\\nreading. arXiv preprint arXiv:1601.06733 , 2016.\\n10', metadata={'source': 'attention.pdf', 'page': 9}),\n", " Document(page_content='[5]<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>,\\nand <PERSON><PERSON><PERSON>. Learning phrase representations using rnn encoder-decoder for statistical\\nmachine translation. CoRR , abs/1406.1078, 2014.\\n[6]<PERSON><PERSON>. Xception: Deep learning with depthwise separable convolutions. arXiv\\npreprint arXiv:1610.02357 , 2016.\\n[7]<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Empirical evaluation\\nof gated recurrent neural networks on sequence modeling. CoRR , abs/1412.3555, 2014.\\n[8]<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Recurrent neural\\nnetwork grammars. In Proc. of NAACL , 2016.\\n[9]<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Convolu-\\ntional sequence to sequence learning. arXiv preprint arXiv:1705.03122v2 , 2017.\\n[10] <PERSON>. Generating sequences with recurrent neural networks. arXiv preprint\\narXiv:1308.0850 , 2013.', metadata={'source': 'attention.pdf', 'page': 10}),\n", " Document(page_content='[11] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Deep residual learning for im-\\nage recognition. In Proceedings of the IEEE Conference on Computer Vision and Pattern\\nRecognition , pages 770–778, 2016.\\n[12] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Gradient flow in\\nrecurrent nets: the difficulty of learning long-term dependencies, 2001.\\n[13] <PERSON><PERSON> and <PERSON><PERSON><PERSON>. Long short-term memory. Neural computation ,\\n9(8):1735–1780, 1997.\\n[14] <PERSON><PERSON><PERSON><PERSON> and <PERSON>. Self-training PCFG grammars with latent annotations\\nacross languages. In Proceedings of the 2009 Conference on Empirical Methods in Natural\\nLanguage Processing , pages 832–841. ACL, August 2009.\\n[15] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Exploring\\nthe limits of language modeling. arXiv preprint arXiv:1602.02410 , 2016.\\n[16] <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Can active memory replace attention? In Advances in Neural', metadata={'source': 'attention.pdf', 'page': 10}),\n", " Document(page_content='Information Processing Systems, (NIPS) , 2016.\\n[17] <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Neural GPUs learn algorithms. In International Conference\\non Learning Representations (ICLR) , 2016.\\n[18] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Neural machine translation in linear time. arXiv preprint arXiv:1610.10099v2 ,\\n2017.\\n[19] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Structured attention networks.\\nInInternational Conference on Learning Representations , 2017.\\n[20] <PERSON><PERSON><PERSON> and <PERSON>: A method for stochastic optimization. In ICLR , 2015.\\n[21] <PERSON><PERSON><PERSON> and <PERSON>. Factorization tricks for LSTM networks. arXiv preprint\\narXiv:1703.10722 , 2017.\\n[22] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>\\<PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. A structured self-attentive sentence embedding. arXiv preprint\\narXiv:1703.03130 , 2017.', metadata={'source': 'attention.pdf', 'page': 10}),\n", " Document(page_content='[23] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Multi-task\\nsequence to sequence learning. arXiv preprint arXiv:1511.06114 , 2015.\\n[24] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Effective approaches to attention-\\nbased neural machine translation. arXiv preprint arXiv:1508.04025 , 2015.\\n11', metadata={'source': 'attention.pdf', 'page': 10}),\n", " Document(page_content='[25] <PERSON>, <PERSON>, and <PERSON>. Building a large annotated\\ncorpus of english: The penn treebank. Computational linguistics , 19(2):313–330, 1993.\\n[26] <PERSON>, <PERSON>, and <PERSON>. Effective self-training for parsing. In\\nProceedings of the Human Language Technology Conference of the NAACL, Main Conference ,\\npages 152–159. ACL, June 2006.\\n[27] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A decomposable attention\\nmodel. In Empirical Methods in Natural Language Processing , 2016.\\n[28] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A deep reinforced model for abstractive\\nsummarization. arXiv preprint arXiv:1705.04304 , 2017.\\n[29] <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Learning accurate, compact,\\nand interpretable tree annotation. In Proceedings of the 21st International Conference on\\nComputational Linguistics and 44th Annual Meeting of the ACL , pages 433–440. ACL, July\\n2006.', metadata={'source': 'attention.pdf', 'page': 11}),\n", " Document(page_content='2006.\\n[30] Ofir Press and <PERSON><PERSON>. Using the output embedding to improve language models. arXiv\\npreprint arXiv:1608.05859 , 2016.\\n[31] <PERSON>, <PERSON>, and <PERSON>. Neural machine translation of rare words\\nwith subword units. arXiv preprint arXiv:1508.07909 , 2015.\\n[32] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>,\\nand <PERSON>. Outrageously large neural networks: The sparsely-gated mixture-of-experts\\nlayer. arXiv preprint arXiv:1701.06538 , 2017.\\n[33] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>-\\nnov. Dropout: a simple way to prevent neural networks from overfitting. Journal of Machine\\nLearning Research , 15(1):1929–1958, 2014.\\n[34] <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. End-to-end memory\\nnetworks. In <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors,', metadata={'source': 'attention.pdf', 'page': 11}),\n", " Document(page_content='Advances in Neural Information Processing Systems 28 , pages 2440–2448. <PERSON>,\\nInc., 2015.\\n[35] <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>uo<PERSON>. Sequence to sequence learning with neural\\nnetworks. In Advances in Neural Information Processing Systems , pages 3104–3112, 2014.\\n[36] <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>.\\nRethinking the inception architecture for computer vision. CoRR , abs/1512.00567, 2015.\\n[37] <PERSON><PERSON><PERSON> & Kaiser, <PERSON>, <PERSON>, <PERSON>, and Hinton. Grammar as a foreign language. In\\nAdvances in Neural Information Processing Systems , 2015.\\n[38] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Google’s neural machine\\ntranslation system: Bridging the gap between human and machine translation. arXiv preprint\\narXiv:1609.08144 , 2016.\\n[39] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Deep recurrent models with', metadata={'source': 'attention.pdf', 'page': 11}),\n", " Document(page_content='fast-forward connections for neural machine translation. CoRR , abs/1606.04199, 2016.\\n[40] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Fast and accurate\\nshift-reduce constituent parsing. In Proceedings of the 51st Annual Meeting of the ACL (Volume\\n1: Long Papers) , pages 434–443. ACL, August 2013.\\n12', metadata={'source': 'attention.pdf', 'page': 11}),\n", " Document(page_content='Attention Visualizations\\nInput-Input Layer5\\nIt\\nis\\nin\\nthis\\nspirit\\nthat\\na\\nmajority\\nof\\nAmerican\\ngovernments\\nhave\\npassed\\nnew\\nlaws\\nsince\\n2009\\nmaking\\nthe\\nregistration\\nor\\nvoting\\nprocess\\nmore\\ndifficult\\n.\\n<EOS>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\nIt\\nis\\nin\\nthis\\nspirit\\nthat\\na\\nmajority\\nof\\nAmerican\\ngovernments\\nhave\\npassed\\nnew\\nlaws\\nsince\\n2009\\nmaking\\nthe\\nregistration\\nor\\nvoting\\nprocess\\nmore\\ndifficult\\n.\\n<EOS>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\nFigure 3: An example of the attention mechanism following long-distance dependencies in the\\nencoder self-attention in layer 5 of 6. Many of the attention heads attend to a distant dependency of\\nthe verb ‘making’, completing the phrase ‘making...more difficult’. Attentions here shown only for\\nthe word ‘making’. Different colors represent different heads. Best viewed in color.\\n13', metadata={'source': 'attention.pdf', 'page': 12}),\n", " Document(page_content='Input-Input Layer5\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nInput-Input Layer5\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>Figure 4: Two attention heads, also in layer 5 of 6, apparently involved in anaphora resolution. Top:\\nFull attentions for head 5. Bottom: Isolated attentions from just the word ‘its’ for attention heads 5\\nand 6. Note that the attentions are very sharp for this word.\\n14', metadata={'source': 'attention.pdf', 'page': 13}),\n", " Document(page_content='Input-Input Layer5\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nInput-Input Layer5\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>Figure 5: Many of the attention heads exhibit behaviour that seems related to the structure of the\\nsentence. We give two such examples above, from two different heads from the encoder self-attention\\nat layer 5 of 6. The heads clearly learned to perform different tasks.\\n15', metadata={'source': 'attention.pdf', 'page': 14})]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["documents=text_splitter.split_documents(docs)\n", "documents"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["from langchain_community.embeddings import OpenAIEmbeddings\n", "from langchain_community.embeddings import OllamaEmbeddings\n", "from langchain_community.vectorstores import FAISS\n", "\n", "db=FAISS.from_documents(documents[:30],OpenAIEmbeddings())"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["<langchain_community.vectorstores.faiss.FAISS at 0x230b9c775e0>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["db"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Instead of performing a single attention function with dmodel-dimensional keys, values and queries,\\nwe found it beneficial to linearly project the queries, keys and values htimes with different, learned\\nlinear projections to dk,dkanddvdimensions, respectively. On each of these projected versions of\\nqueries, keys and values we then perform the attention function in parallel, yielding dv-dimensional\\n4To illustrate why the dot products get large, assume that the components of qandkare independent random\\nvariables with mean 0and variance 1. Then their dot product, q·k=Pdk\\ni=1qiki, has mean 0and variance dk.\\n4'"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["query=\"An attention function can be described as mapping a query \"\n", "result=db.similarity_search(query)\n", "result[0].page_content"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["Ollama()"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_community.llms import Ollama\n", "## Load Ollama LAMA2 LLM model\n", "llm=Ollama(model=\"llama2\")\n", "llm"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["## Design ChatPrompt Template\n", "from langchain_core.prompts import ChatPromptTemplate\n", "prompt = ChatPromptTemplate.from_template(\"\"\"\n", "Answer the following question based only on the provided context. \n", "Think step by step before providing a detailed answer. \n", "I will tip you $1000 if the user finds the answer helpful. \n", "<context>\n", "{context}\n", "</context>\n", "Question: {input}\"\"\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["## Chain Introduction\n", "## Create Stuff Docment Chain\n", "\n", "from langchain.chains.combine_documents import create_stuff_documents_chain\n", "\n", "document_chain=create_stuff_documents_chain(llm,prompt)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["VectorStoreRetriever(tags=['FAISS', 'OpenAIEmbeddings'], vectorstore=<langchain_community.vectorstores.faiss.FAISS object at 0x00000230B9C775E0>)"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["\"\"\"\n", "Retrievers: A retriever is an interface that returns documents given\n", " an unstructured query. It is more general than a vector store.\n", " A retriever does not need to be able to store documents, only to \n", " return (or retrieve) them. Vector stores can be used as the backbone\n", " of a retriever, but there are other types of retrievers as well. \n", " https://python.langchain.com/docs/modules/data_connection/retrievers/   \n", "\"\"\"\n", "\n", "retriever=db.as_retriever()\n", "retriever"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Retrieval chain:This chain takes in a user inquiry, which is then\n", "passed to the retriever to fetch relevant documents. Those documents \n", "(and original inputs) are then passed to an LLM to generate a response\n", "https://python.langchain.com/docs/modules/chains/\n", "\"\"\"\n", "from langchain.chains import create_retrieval_chain\n", "retrieval_chain=create_retrieval_chain(retriever,document_chain)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["response=retrieval_chain.invoke({\"input\":\"Scaled Dot-Product Attention\"})"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Scaled Dot-Product Attention is a type of attention mechanism used in the Transformer architecture. It is called \"scaled dot-product attention\" because it computes the compatibility between the query and key vectors by taking the dot product of the two, scaling the result by √dk, and applying a softmax function to obtain the weights on the value vector.\\n\\nThe formula for Scaled Dot-Product Attention is:\\n\\nAttention(Q, K, V) = softmax(QKT√dk)V (1)\\n\\nwhere Q, K, and V are matrices representing the queries, keys, and values, respectively. The scaling factor of √dk is introduced to prevent the dot products from becoming too large and causing numerical instability.\\n\\nThe main advantage of Scaled Dot-Product Attention is its efficiency in terms of computational cost and memory usage. Since the attention function is computed on a set of queries simultaneously, it can be implemented using highly optimized matrix multiplication code, making it much faster and more space-efficient than other attention mechanisms like additive attention.\\n\\nHowever, Scaled Dot-Product Attention has some limitations. For small values of dk, the two mechanisms (scaled dot-product attention and additive attention) perform similarly, but for larger values of dk, additive attention outperforms scaled dot-product attention without scaling [3]. This is because the dot products grow large in magnitude as dk increases, causing the softmax function to have extremely small gradients. To counteract this effect, Scaled Dot-Product Attention scales the dot products by √dk.\\n\\nIn summary, Scaled Dot-Product Attention is a type of attention mechanism used in the Transformer architecture that computes the compatibility between queries and keys using a scaled dot product, followed by a softmax function to obtain the weights on the value vector. It is efficient in terms of computational cost and memory usage but has limitations for large values of dk.'"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["response['answer']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 2}