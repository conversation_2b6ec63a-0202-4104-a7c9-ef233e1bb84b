{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyOSai68G4Hc4t9gpJ5+CkAy", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/sunnysavita10/Generative-AI-Indepth-Basic-to-Advance/blob/main/Conversational_Summary_Memory.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "code", "source": ["!pip install langchain"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AygnEOhZIjbO", "outputId": "a6bce69e-b458-404c-f318-a06160e8fd4c"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain\n", "  Downloading langchain-0.2.16-py3-none-any.whl.metadata (7.1 kB)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (6.0.2)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.0.32)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.10.5)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (4.0.3)\n", "Collecting langchain-core<0.3.0,>=0.2.38 (from langchain)\n", "  Downloading langchain_core-0.2.38-py3-none-any.whl.metadata (6.2 kB)\n", "Collecting langchain-text-splitters<0.3.0,>=0.2.0 (from langchain)\n", "  Downloading langchain_text_splitters-0.2.4-py3-none-any.whl.metadata (2.3 kB)\n", "Collecting langsmith<0.2.0,>=0.1.17 (from langchain)\n", "  Downloading langsmith-0.1.117-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.26.4)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.8.2)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.32.3)\n", "Collecting tenacity!=8.4.0,<9.0.0,>=8.1.0 (from langchain)\n", "  Downloading tenacity-8.5.0-py3-none-any.whl.metadata (1.2 kB)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (2.4.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (24.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.9.4)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain-core<0.3.0,>=0.2.38->langchain)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl.metadata (3.0 kB)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.38->langchain) (24.1)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.38->langchain) (4.12.2)\n", "Collecting httpx<1,>=0.23.0 (from langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading httpx-0.27.2-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting <PERSON><PERSON><PERSON><4.0.0,>=3.9.14 (from langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading orjson-3.10.7-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (50 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.4/50.4 kB\u001b[0m \u001b[31m1.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.20.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (2.20.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2024.8.30)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.0.3)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (3.7.1)\n", "Collecting httpcore==1.* (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl.metadata (20 kB)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (1.3.1)\n", "Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading h11-0.14.0-py3-none-any.whl.metadata (8.2 kB)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.38->langchain)\n", "  Downloading jsonpointer-3.0.0-py2.py3-none-any.whl.metadata (2.3 kB)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (1.2.2)\n", "Downloading langchain-0.2.16-py3-none-any.whl (1.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m16.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langchain_core-0.2.38-py3-none-any.whl (396 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m396.4/396.4 kB\u001b[0m \u001b[31m19.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langchain_text_splitters-0.2.4-py3-none-any.whl (25 kB)\n", "Downloading langsmith-0.1.117-py3-none-any.whl (290 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m290.5/290.5 kB\u001b[0m \u001b[31m15.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading tenacity-8.5.0-py3-none-any.whl (28 kB)\n", "Downloading httpx-0.27.2-py3-none-any.whl (76 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m76.4/76.4 kB\u001b[0m \u001b[31m3.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m4.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Downloading orjson-3.10.7-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (141 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m141.9/141.9 kB\u001b[0m \u001b[31m7.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading jsonpointer-3.0.0-py2.py3-none-any.whl (7.6 kB)\n", "Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m3.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: tenacity, orjson, jsonpointer, h11, jsonpatch, httpcore, httpx, langsmith, langchain-core, langchain-text-splitters, langchain\n", "  Attempting uninstall: tenacity\n", "    Found existing installation: tenacity 9.0.0\n", "    Uninstalling tenacity-9.0.0:\n", "      Successfully uninstalled tenacity-9.0.0\n", "Successfully installed h11-0.14.0 httpcore-1.0.5 httpx-0.27.2 jsonpatch-1.33 jsonpointer-3.0.0 langchain-0.2.16 langchain-core-0.2.38 langchain-text-splitters-0.2.4 langsmith-0.1.117 orjson-3.10.7 tenacity-8.5.0\n"]}]}, {"cell_type": "code", "source": ["!pip install langchain_community"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "PvFZRklaIpbV", "outputId": "b837f7fc-618d-47ef-a9d4-9a4befbc71b1"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain_community\n", "  Downloading langchain_community-0.2.16-py3-none-any.whl.metadata (2.7 kB)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (6.0.2)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (2.0.32)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (3.10.5)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain_community)\n", "  Downloading dataclasses_json-0.6.7-py3-none-any.whl.metadata (25 kB)\n", "Requirement already satisfied: langchain<0.3.0,>=0.2.16 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (0.2.16)\n", "Requirement already satisfied: langchain-core<0.3.0,>=0.2.38 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (0.2.38)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (0.1.117)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (1.26.4)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (2.32.3)\n", "Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (8.5.0)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (2.4.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (24.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (4.0.3)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain_community)\n", "  Downloading marshmallow-3.22.0-py3-none-any.whl.metadata (7.2 kB)\n", "Collecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain_community)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: langchain-text-splitters<0.3.0,>=0.2.0 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.16->langchain_community) (0.2.4)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.16->langchain_community) (2.8.2)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.38->langchain_community) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.38->langchain_community) (24.1)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.38->langchain_community) (4.12.2)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.0->langchain_community) (0.27.2)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.0->langchain_community) (3.10.7)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (2024.8.30)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain_community) (3.0.3)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain_community) (3.7.1)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain_community) (1.0.5)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain_community) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain_community) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.38->langchain_community) (3.0.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.16->langchain_community) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.20.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.16->langchain_community) (2.20.1)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain_community)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl.metadata (1.1 kB)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain_community) (1.2.2)\n", "Downloading langchain_community-0.2.16-py3-none-any.whl (2.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.3/2.3 MB\u001b[0m \u001b[31m25.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading dataclasses_json-0.6.7-py3-none-any.whl (28 kB)\n", "Downloading marshmallow-3.22.0-py3-none-any.whl (49 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.3/49.3 kB\u001b[0m \u001b[31m2.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: mypy-extensions, marshmallow, typing-inspect, dataclasses-json, langchain_community\n", "Successfully installed dataclasses-json-0.6.7 langchain_community-0.2.16 marshmallow-3.22.0 mypy-extensions-1.0.0 typing-inspect-0.9.0\n"]}]}, {"cell_type": "code", "source": ["!pip install langchain-groq"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xPchyz3U8lCl", "outputId": "45596571-05df-475a-9ed8-c75833eabd77"}, "execution_count": 4, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain-groq\n", "  Downloading langchain_groq-0.1.9-py3-none-any.whl.metadata (2.9 kB)\n", "Collecting groq<1,>=0.4.1 (from langchain-groq)\n", "  Downloading groq-0.11.0-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: langchain-core<0.3.0,>=0.2.26 in /usr/local/lib/python3.10/dist-packages (from langchain-groq) (0.2.38)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.10/dist-packages (from groq<1,>=0.4.1->langchain-groq) (3.7.1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from groq<1,>=0.4.1->langchain-groq) (1.7.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.10/dist-packages (from groq<1,>=0.4.1->langchain-groq) (0.27.2)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /usr/local/lib/python3.10/dist-packages (from groq<1,>=0.4.1->langchain-groq) (2.8.2)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from groq<1,>=0.4.1->langchain-groq) (1.3.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.7 in /usr/local/lib/python3.10/dist-packages (from groq<1,>=0.4.1->langchain-groq) (4.12.2)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.26->langchain-groq) (6.0.2)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.26->langchain-groq) (1.33)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.75 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.26->langchain-groq) (0.1.117)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.26->langchain-groq) (24.1)\n", "Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.26->langchain-groq) (8.5.0)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->groq<1,>=0.4.1->langchain-groq) (3.8)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->groq<1,>=0.4.1->langchain-groq) (1.2.2)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->groq<1,>=0.4.1->langchain-groq) (2024.8.30)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->groq<1,>=0.4.1->langchain-groq) (1.0.5)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->groq<1,>=0.4.1->langchain-groq) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.26->langchain-groq) (3.0.0)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.75->langchain-core<0.3.0,>=0.2.26->langchain-groq) (3.10.7)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.75->langchain-core<0.3.0,>=0.2.26->langchain-groq) (2.32.3)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1.9.0->groq<1,>=0.4.1->langchain-groq) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.20.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1.9.0->groq<1,>=0.4.1->langchain-groq) (2.20.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langsmith<0.2.0,>=0.1.75->langchain-core<0.3.0,>=0.2.26->langchain-groq) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langsmith<0.2.0,>=0.1.75->langchain-core<0.3.0,>=0.2.26->langchain-groq) (2.0.7)\n", "Downloading langchain_groq-0.1.9-py3-none-any.whl (14 kB)\n", "Downloading groq-0.11.0-py3-none-any.whl (106 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m106.5/106.5 kB\u001b[0m \u001b[31m5.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: groq, langchain-groq\n", "Successfully installed groq-0.11.0 langchain-groq-0.1.9\n"]}]}, {"cell_type": "code", "source": ["import os\n", "from google.colab import userdata"], "metadata": {"id": "Fg-AKGTs_PQU"}, "execution_count": 47, "outputs": []}, {"cell_type": "code", "source": ["GROQ_API_KEY=userdata.get('GROQ_API_KEY')\n", "os.environ[\"GROQ_API_KEY\"] = GROQ_API_KEY"], "metadata": {"id": "2Cu_pYwIKAUs"}, "execution_count": 48, "outputs": []}, {"cell_type": "code", "source": ["LANGCHAIN_API_KEY=userdata.get('LANGCHAIN_API_KEY')\n", "os.environ[\"LANGCHAIN_API_KEY\"] = LANGCHAIN_API_KEY"], "metadata": {"id": "LW2FMfFP_OQM"}, "execution_count": 49, "outputs": []}, {"cell_type": "code", "source": ["os.environ[\"LANGCHAIN_PROJECT\"]=\"memorylogs\"\n", "os.environ[\"LANGCHAIN_TRACING_V2\"] = \"true\"\n", "os.environ[\"LANGCHAIN_ENDPOINT\"] = \"https://api.smith.langchain.com\""], "metadata": {"id": "7ebHtdBt_NMl"}, "execution_count": 50, "outputs": []}, {"cell_type": "code", "source": ["from langchain_groq import ChatGroq\n", "model=ChatGroq(model_name=\"Gemma2-9b-It\")"], "metadata": {"id": "2qUMkyoc85cF"}, "execution_count": 51, "outputs": []}, {"cell_type": "code", "source": ["model.invoke(\"Hi, what's up?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DrWr856dUYZ0", "outputId": "2d29db2c-7557-43b1-fb1c-6995cef078fd"}, "execution_count": 52, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["AIMessage(content=\"Not much, just hanging out in the digital world, waiting to answer questions and have conversations!\\n\\nWhat about you? What's going on in your world today? 😊\\n\", response_metadata={'token_usage': {'completion_tokens': 38, 'prompt_tokens': 16, 'total_tokens': 54, 'completion_time': 0.069090909, 'prompt_time': 7.395e-05, 'queue_time': 0.014561937, 'total_time': 0.069164859}, 'model_name': 'Gemma2-9b-It', 'system_fingerprint': 'fp_10c08bf97d', 'finish_reason': 'stop', 'logprobs': None}, id='run-7f3ef72f-e804-46ee-bbfd-e4c5a626bf0d-0', usage_metadata={'input_tokens': 16, 'output_tokens': 38, 'total_tokens': 54})"]}, "metadata": {}, "execution_count": 52}]}, {"cell_type": "code", "execution_count": 53, "metadata": {"id": "41gGc9AyIOnz"}, "outputs": [], "source": ["from langchain.memory import ConversationSummaryMemory"]}, {"cell_type": "code", "source": ["from langchain.memory import ChatMessageHistory"], "metadata": {"id": "kVGDaPvPGESO"}, "execution_count": 54, "outputs": []}, {"cell_type": "code", "source": ["memory = ConversationSummaryMemory(llm=model, return_messages=True)"], "metadata": {"id": "VLErHCqoLPCG"}, "execution_count": 55, "outputs": []}, {"cell_type": "code", "source": ["memory.save_context(\n", "    {\"input\": \"<PERSON> and <PERSON><PERSON> are working on a hackathon project.\"},\n", "    {\"output\": \"That's awesome! What's the hackathon project about?\"}\n", ")"], "metadata": {"id": "mbl6oOr7LP21"}, "execution_count": 56, "outputs": []}, {"cell_type": "code", "source": ["memory.load_memory_variables({})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "L8fTked0LUTd", "outputId": "4056e3b8-9738-4668-9cb9-99d7664cdf1a"}, "execution_count": 57, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': [SystemMessage(content=\"Human: <PERSON> and <PERSON><PERSON> are working on a hackathon project. \\nAI: That's awesome! What's the hackathon project about? \\n\\nNew summary: The human mentions that <PERSON> and <PERSON><PERSON> are working on a hackathon project and the AI expresses enthusiasm, asking for more details about it. \\n\")]}"]}, "metadata": {}, "execution_count": 57}]}, {"cell_type": "code", "source": ["summary=memory.load_memory_variables({})"], "metadata": {"id": "geXY_wMSHvLv"}, "execution_count": 58, "outputs": []}, {"cell_type": "code", "source": ["summary"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kVx_ktZwH61P", "outputId": "3e2eb48c-1756-4c2e-c4df-56d7a93bbae1"}, "execution_count": 59, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': [SystemMessage(content=\"Human: <PERSON> and <PERSON><PERSON> are working on a hackathon project. \\nAI: That's awesome! What's the hackathon project about? \\n\\nNew summary: The human mentions that <PERSON> and <PERSON><PERSON> are working on a hackathon project and the AI expresses enthusiasm, asking for more details about it. \\n\")]}"]}, "metadata": {}, "execution_count": 59}]}, {"cell_type": "code", "source": ["print(summary[\"history\"][0].content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "O4JEUOyUHzBz", "outputId": "d0ed406a-c099-4b20-c525-7cea579e640b"}, "execution_count": 60, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Human: <PERSON> and <PERSON><PERSON> are working on a hackathon project. \n", "AI: That's awesome! What's the hackathon project about? \n", "\n", "New summary: The human mentions that <PERSON> and <PERSON><PERSON> are working on a hackathon project and the AI expresses enthusiasm, asking for more details about it. \n", "\n"]}]}, {"cell_type": "code", "source": ["memory.save_context(\n", "    {\"input\": \"It's a machine learning project focused on healthcare.\"},\n", "    {\"output\": \"Sounds exciting! Are they building a prediction model or something else\"}\n", ")\n", "memory.save_context(\n", "    {\"input\": \"Yes, they’re working on a model to predict patient outcomes.\"},\n", "    {\"output\": \"Impressive! How far along are they with the project?\"}\n", ")"], "metadata": {"id": "I_X4a1RMLsbO"}, "execution_count": 61, "outputs": []}, {"cell_type": "code", "source": ["memory.load_memory_variables({})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "nS6bvFa-L49_", "outputId": "b8e01291-910c-4888-9777-df3d3484e302"}, "execution_count": 62, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': [SystemMessage(content='The human mentions that <PERSON> and <PERSON><PERSON> are working on a hackathon project focused on healthcare. The AI is excited and asks if they are building a prediction model or something else. The human confirms that they are working on a model to predict patient outcomes. The AI is impressed and asks how far along they are with the project. \\n')]}"]}, "metadata": {}, "execution_count": 62}]}, {"cell_type": "code", "source": ["summary=memory.load_memory_variables({})"], "metadata": {"id": "HyJX047FInuH"}, "execution_count": 63, "outputs": []}, {"cell_type": "code", "source": ["print(summary[\"history\"][0].content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pv7Y51HkIs7n", "outputId": "df18e64e-235c-49f1-ebdc-281faacfb808"}, "execution_count": 64, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The human mentions that <PERSON> and <PERSON><PERSON> are working on a hackathon project focused on healthcare. The AI is excited and asks if they are building a prediction model or something else. The human confirms that they are working on a model to predict patient outcomes. The AI is impressed and asks how far along they are with the project. \n", "\n"]}]}, {"cell_type": "code", "source": ["memory.load_memory_variables({})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "BxDFvMLXKf51", "outputId": "7c9cf81e-9455-4cec-b285-70245995813e"}, "execution_count": 67, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': [SystemMessage(content='The human mentions that <PERSON> and <PERSON><PERSON> are working on a hackathon project focused on healthcare, specifically a model to predict patient outcomes. The AI is impressed by the project and asks how far along they are with it. \\n')]}"]}, "metadata": {}, "execution_count": 67}]}, {"cell_type": "code", "source": ["print('Progressively summarize the lines of conversation provided, adding onto the previous summary returning a new summary.\\n\\nEXAMPLE\\nCurrent summary:\\nThe human asks what the AI thinks of artificial intelligence. The AI thinks artificial intelligence is a force for good.\\n\\nNew lines of conversation:\\nHuman: Why do you think artificial intelligence is a force for good?\\nAI: Because artificial intelligence will help humans reach their full potential.\\n\\nNew summary:\\nThe human asks what the AI thinks of artificial intelligence. The AI thinks artificial intelligence is a force for good because it will help humans reach their full potential.\\nEND OF EXAMPLE\\n\\nCurrent summary:\\n{summary}\\n\\nNew lines of conversation:\\n{new_lines}\\n\\nNew summary:')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ax-wiHmpJUCB", "outputId": "b406c6e0-23b1-49b7-fc9e-d8e2533ba3d6"}, "execution_count": 89, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Progressively summarize the lines of conversation provided, adding onto the previous summary returning a new summary.\n", "\n", "EXAMPLE\n", "Current summary:\n", "The human asks what the AI thinks of artificial intelligence. The AI thinks artificial intelligence is a force for good.\n", "\n", "New lines of conversation:\n", "Human: Why do you think artificial intelligence is a force for good?\n", "AI: Because artificial intelligence will help humans reach their full potential.\n", "\n", "New summary:\n", "The human asks what the AI thinks of artificial intelligence. The AI thinks artificial intelligence is a force for good because it will help humans reach their full potential.\n", "END OF EXAMPLE\n", "\n", "Current summary:\n", "{summary}\n", "\n", "New lines of conversation:\n", "{new_lines}\n", "\n", "New summary:\n"]}]}, {"cell_type": "code", "source": ["memory.chat_memory"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8YUuMPxGKxVF", "outputId": "e7833d2b-68a0-431f-ed06-f59bf5b3c691"}, "execution_count": 68, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["InMemoryChatMessageHistory(messages=[HumanMessage(content='<PERSON> and <PERSON><PERSON> are working on a hackathon project.'), AIMessage(content=\"That's awesome! What's the hackathon project about?\"), HumanMessage(content=\"It's a machine learning project focused on healthcare.\"), AIMessage(content='Sounds exciting! Are they building a prediction model or something else'), HumanMessage(content='Yes, they’re working on a model to predict patient outcomes.'), AIMessage(content='Impressive! How far along are they with the project?'), HumanMessage(content='Yes, they’re working on a model to predict patient outcomes.'), AIMessage(content='Impressive! How far along are they with the project?')])"]}, "metadata": {}, "execution_count": 68}]}, {"cell_type": "code", "source": ["memory.chat_memory.messages"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "0MKYIhNARIRW", "outputId": "cf64bf27-9e69-4eb5-bd38-41397f7f8ad7"}, "execution_count": 70, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[HumanMessage(content='<PERSON> and <PERSON><PERSON> are working on a hackathon project.'),\n", " AIMessage(content=\"That's awesome! What's the hackathon project about?\"),\n", " HumanMessage(content=\"It's a machine learning project focused on healthcare.\"),\n", " AIMessage(content='Sounds exciting! Are they building a prediction model or something else'),\n", " HumanMessage(content='Yes, they’re working on a model to predict patient outcomes.'),\n", " AIMessage(content='Impressive! How far along are they with the project?'),\n", " HumanMessage(content='Yes, they’re working on a model to predict patient outcomes.'),\n", " AIMessage(content='Impressive! How far along are they with the project?')]"]}, "metadata": {}, "execution_count": 70}]}, {"cell_type": "code", "source": ["messages = memory.chat_memory.messages"], "metadata": {"id": "yuV4T9HmQh7k"}, "execution_count": 71, "outputs": []}, {"cell_type": "code", "source": ["messages"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dUtkymzCWHLa", "outputId": "93ba3130-6163-4e46-d211-7eebe6abdb61"}, "execution_count": 72, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[HumanMessage(content='<PERSON> and <PERSON><PERSON> are working on a hackathon project.'),\n", " AIMessage(content=\"That's awesome! What's the hackathon project about?\"),\n", " HumanMessage(content=\"It's a machine learning project focused on healthcare.\"),\n", " AIMessage(content='Sounds exciting! Are they building a prediction model or something else'),\n", " HumanMessage(content='Yes, they’re working on a model to predict patient outcomes.'),\n", " AIMessage(content='Impressive! How far along are they with the project?'),\n", " HumanMessage(content='Yes, they’re working on a model to predict patient outcomes.'),\n", " AIMessage(content='Impressive! How far along are they with the project?')]"]}, "metadata": {}, "execution_count": 72}]}, {"cell_type": "code", "source": ["previous_summary=\"\""], "metadata": {"id": "cZ7Fn-ZwQkEs"}, "execution_count": 73, "outputs": []}, {"cell_type": "code", "source": ["memory.predict_new_summary(messages, previous_summary)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 54}, "id": "iQnpKenHQpaM", "outputId": "d6a80bee-ec1b-4870-f4d4-3212d74db19e"}, "execution_count": 74, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'New summary: The human tells the AI that <PERSON> and <PERSON><PERSON> are working on a machine learning project for a hackathon focused on healthcare. They are building a model to predict patient outcomes. The AI is impressed and asks how far along they are in the project. \\n'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 74}]}, {"cell_type": "code", "source": ["history = ChatMessageHistory()"], "metadata": {"id": "s5K5tGBEQ3A1"}, "execution_count": 75, "outputs": []}, {"cell_type": "code", "source": ["history.add_user_message(\"Hi\")"], "metadata": {"id": "gK__XEGDSDXt"}, "execution_count": 76, "outputs": []}, {"cell_type": "code", "source": ["history.add_ai_message(\"Hello, how can I assist you today?\")"], "metadata": {"id": "npFZROIOSGsm"}, "execution_count": 77, "outputs": []}, {"cell_type": "code", "source": ["ConversationSummaryMemory.from_messages(\n", "    llm=model,\n", "    chat_memory=history,\n", "    memory_key=\"summary\",\n", "    human_prefix=\"User\",\n", "    ai_prefix=\"AI\"\n", ")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vJhxPTCPSJc1", "outputId": "50764ff3-5daf-484d-cf90-855679bb75e6"}, "execution_count": 78, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["ConversationSummaryMemory(human_prefix='User', llm=ChatGroq(client=<groq.resources.chat.completions.Completions object at 0x7d084365f460>, async_client=<groq.resources.chat.completions.AsyncCompletions object at 0x7d0843e05a80>, model_name='Gemma2-9b-It', groq_api_key=SecretStr('**********')), chat_memory=InMemoryChatMessageHistory(messages=[HumanMessage(content='Hi'), AIMessage(content='Hello, how can I assist you today?')]), buffer='New summary: A user greets the AI with a \"Hi\". The AI responds with a polite \"Hello\" and offers to assist the user. \\n', memory_key='summary')"]}, "metadata": {}, "execution_count": 78}]}, {"cell_type": "code", "source": ["memory = ConversationSummaryMemory.from_messages(\n", "    llm=model,\n", "    chat_memory=history,\n", "    memory_key=\"summary\",\n", "    human_prefix=\"User\",\n", "    ai_prefix=\"AI\"\n", ")"], "metadata": {"id": "1GSJ1hDiSaHM"}, "execution_count": 79, "outputs": []}, {"cell_type": "code", "source": ["memory.buffer"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 54}, "id": "obdS8sIbSnus", "outputId": "5ac9af99-ed5a-40ea-ec0e-5f4e11858eec"}, "execution_count": 80, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'User: Hi\\nAI: Hello, how can I assist you today?\\n\\nNew summary: A user greeted the AI and the AI responded with a greeting and an offer of assistance. \\n'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 80}]}, {"cell_type": "markdown", "source": ["# From here the chaining starting"], "metadata": {"id": "qRfFhS-3LfQu"}}, {"cell_type": "code", "source": ["from langchain.chains import ConversationChain\n", "conversation_with_summary = ConversationChain(\n", "    llm=model,\n", "    memory=ConversationSummaryMemory(llm=model),\n", "    verbose=True\n", ")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_TDHr7zGSpph", "outputId": "c2a47e77-2b1c-430e-cdc5-ee990fe9d076"}, "execution_count": 82, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-82-8b7904464e70>:2: Lang<PERSON>hainDeprecationWarning: The class `ConversationChain` was deprecated in LangChain 0.2.7 and will be removed in 1.0. Use RunnableWithMessageHistory: https://python.langchain.com/v0.2/api_reference/core/runnables/langchain_core.runnables.history.RunnableWithMessageHistory.html instead.\n", "  conversation_with_summary = ConversationChain(\n"]}]}, {"cell_type": "code", "source": ["conversation_with_summary.predict(input=\"Hi, what's up?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 290}, "id": "HMomUKKaYcis", "outputId": "f6af1d1d-01f4-44a5-a008-ee3701fe752b"}, "execution_count": 83, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "\n", "Human: Hi, what's up?\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"Hi there! Not much, just waiting to see what interesting questions you have for me.  How about you? What's going on in your world today? \\n\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 83}]}, {"cell_type": "code", "source": ["conversation_with_summary.predict(input=\"<PERSON> and <PERSON><PERSON> are working on a mlops production ready project.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 362}, "id": "-rhWP-RuYfD8", "outputId": "e9776166-0d7c-426d-a93f-73ba6b014222"}, "execution_count": 84, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "New summary: \n", "\n", "The human greets the AI and asks what it's up to. The AI responds by saying it's waiting for interesting questions and asks the human what's going on in their world. \n", "\n", "Human: <PERSON> and <PERSON><PERSON> are working on a mlops production ready project.\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"That sounds fascinating!  MLOps is such a hot topic right now.  \\n\\nTell me more about their project. What kind of model are they working with? What are some of the challenges they're facing in making it production-ready? I'm always eager to learn about real-world applications of machine learning. \\n\\n\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 84}]}, {"cell_type": "code", "source": ["conversation_with_summary.predict(input=\"It's project focused on healthcare.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 344}, "id": "OCHN6jgpYuvW", "outputId": "857d05d0-fb90-443d-84ec-4d18b8d32f80"}, "execution_count": 85, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "The human greets the AI and asks what it's up to. The AI responds by saying it's waiting for interesting questions and asks the human what's going on in their world. The human tells the AI that <PERSON> and <PERSON><PERSON> are working on a production-ready MLOps project. The AI expresses interest in the project and asks for more details, such as the type of model they are using and the challenges they are facing. \n", "\n", "Human: It's project focused on healthcare.\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"That's fascinating! Healthcare is such a complex and important field.  Tell me more about this project. What kind of health problems are they trying to address with this MLOps project? Are they using it for diagnosis, treatment planning, drug discovery, or something else entirely? \\n\\nI'm eager to learn about the specific challenges they're facing in making it production-ready.  Data privacy and security are always major concerns in healthcare, for example.  Are there any other unique hurdles they're encountering? \\n\\n\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 85}]}, {"cell_type": "code", "source": ["conversation_with_summary.predict(input=\"so can you describe mlops pipeline to me with in six point.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 417}, "id": "APVEcglEYux2", "outputId": "0269a79b-3cb3-4cce-910d-003bb86f943a"}, "execution_count": 86, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "The human greets the AI and asks what it's up to. The AI responds by saying it's waiting for interesting questions and asks the human what's going on in their world. The human tells the AI that <PERSON> and <PERSON><PERSON> are working on a production-ready MLOps project focused on healthcare. The AI expresses interest in the project and asks for more details, such as the type of model they are using and the challenges they are facing. The human clarifies that the project focuses on healthcare. The AI, intrigued by the field's complexity and importance, asks about the specific health problems the project addresses, whether it's for diagnosis, treatment planning, drug discovery, or something else. It also expresses interest in the unique challenges they face, particularly regarding data privacy and security in healthcare.  \n", "\n", "Human: so can you describe mlops pipeline to me with in six point.\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'You got it!  Describing an MLOps pipeline in six points is a great way to get a handle on the key concepts. Here\\'s one way to do it:\\n\\n1. **Data Collection & Preparation:** This is where you gather the raw data needed for your model and clean, transform, and prepare it for training. Think of it like gathering ingredients and chopping them up before you start cooking. \\n2. **Model Development:**  This is where the \"magic\" happens! You choose a suitable machine learning algorithm, train it on your prepared data, and fine-tune its parameters to achieve the best performance.  \\n3. **Model Evaluation:** Before deploying your model, you rigorously test and evaluate its performance on unseen data. This helps you understand how well it will generalize to real-world scenarios.\\n4. **Model Deployment:**  This involves making your trained model accessible for use. It could be deployed as a web service, integrated into an application, or run on edge devices.\\n5. **Monitoring & Maintenance:**  Once deployed, you continuously monitor the model\\'s performance in the real world.  You might also need to retrain or update the model periodically to ensure it stays accurate and relevant.\\n6. **Versioning & Experiment Tracking:**  Throughout the pipeline, it\\'s crucial to track all changes to your code, data, and model parameters. This allows you to roll back to previous versions if needed and understand how different decisions impacted your model\\'s performance. \\n\\n\\n\\nLet me know if you\\'d like me to elaborate on any of these points or if you have other questions about MLOps!\\n'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 86}]}, {"cell_type": "markdown", "source": ["You got it!  Describing an MLOps pipeline in six points is a great way to get a handle on the key concepts. Here's one way to do it:\n", "\n", "1. **Data Collection & Preparation:** This is where you gather the raw data needed for your model and clean, transform, and prepare it for training. Think of it like gathering ingredients and chopping them up before you start cooking.\n", "2. **Model Development:**  This is where the \"magic\" happens! You choose a suitable machine learning algorithm, train it on your prepared data, and fine-tune its parameters to achieve the best performance.  \n", "3. **Model Evaluation:** Before deploying your model, you rigorously test and evaluate its performance on unseen data. This helps you understand how well it will generalize to real-world scenarios.\n", "4. **Model Deployment:**  This involves making your trained model accessible for use. It could be deployed as a web service, integrated into an application, or run on edge devices.\n", "5. **Monitoring & Maintenance:**  Once deployed, you continuously monitor the model's performance in the real world.  You might also need to retrain or update the model periodically to ensure it stays accurate and relevant.\n", "6. **Versioning & Experiment Tracking:**  Throughout the pipeline, it's crucial to track all changes to your code, data, and model parameters. This allows you to roll back to previous versions if needed and understand how different decisions impacted your model's performance.\n", "\n", "\n", "\n", "Let me know if you'd like me to elaborate on any of these points or if you have other questions about <PERSON><PERSON><PERSON><PERSON>!\n"], "metadata": {"id": "ttahJHLcMKtH"}}, {"cell_type": "code", "source": ["conversation_with_summary.predict(input=\"How many total points are there?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 290}, "id": "zG6fTjw5ZEaG", "outputId": "3c07cca6-2eb1-4169-9504-6b0a16ca0cf3"}, "execution_count": 87, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "The human greets the AI and asks what it's up to. The AI responds by saying it's waiting for interesting questions and asks the human what's going on in their world. The human tells the AI that <PERSON> and <PERSON><PERSON> are working on a production-ready MLOps project focused on healthcare. The AI expresses interest in the project and asks for more details, such as the type of model they are using and the challenges they are facing. The human clarifies that the project focuses on healthcare. The AI, intrigued by the field's complexity and importance, asks about the specific health problems the project addresses, whether it's for diagnosis, treatment planning, drug discovery, or something else. It also expresses interest in the unique challenges they face, particularly regarding data privacy and security in healthcare.  The human then asks the AI to describe an MLOps pipeline in six points. The AI obliges, providing a concise explanation of each stage: data collection and preparation, model development, model evaluation, model deployment, monitoring and maintenance, and versioning and experiment tracking. \n", "\n", "Human: How many total points are there?\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'There are **6** total points in an MLOps pipeline.  😊  \\n\\nI listed them all for you! \\n\\n'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 87}]}, {"cell_type": "code", "source": ["conversation_with_summary.predict(input=\"can you give me 5th point with explaination\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 417}, "id": "5fWaLReZZOMm", "outputId": "156b4a3a-3415-431a-b17e-83e641dccd04"}, "execution_count": 88, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "The human greets the AI and asks what it's up to. The AI responds by saying it's waiting for interesting questions and asks the human what's going on in their world. The human tells the AI that <PERSON> and <PERSON><PERSON> are working on a production-ready MLOps project focused on healthcare. The AI expresses interest in the project and asks for more details, such as the type of model they are using and the challenges they are facing. The human clarifies that the project focuses on healthcare. The AI, intrigued by the field's complexity and importance, asks about the specific health problems the project addresses, whether it's for diagnosis, treatment planning, drug discovery, or something else. It also expresses interest in the unique challenges they face, particularly regarding data privacy and security in healthcare.  The human then asks the AI to describe an MLOps pipeline in six points. The AI obliges, providing a concise explanation of each stage: data collection and preparation, model development, model evaluation, model deployment, monitoring and maintenance, and versioning and experiment tracking. The human asks how many points are in the pipeline, and the AI confirms there are six. \n", "\n", "Human: can you give me 5th point with explaination\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"Absolutely!  The fifth point in an MLOps pipeline is **Monitoring and Maintenance**. \\n\\nThis stage is crucial because it ensures that the deployed model continues to perform well in the real world.  \\n\\nHere's a breakdown:\\n\\n* **Performance Tracking:**  We continuously monitor the model's performance metrics, like accuracy, precision, recall, and F1-score.  This helps us detect any degradation in performance over time.\\n* **Data Drift Detection:**  Real-world data can change, and if the input data distribution shifts significantly from what the model was trained on, its performance can suffer.  We use techniques to detect these data drifts.\\n* **Error Analysis:**  We analyze any errors or unexpected outputs from the model to understand the reasons behind them. This can help us identify areas for improvement in the model or the data.\\n* **Model Retraining:**  Based on performance monitoring and data drift detection, we might need to retrain the model with updated data to maintain its effectiveness.\\n* **System Health Checks:**  We also monitor the infrastructure and systems supporting the model deployment to ensure they are running smoothly and securely. \\n\\n\\nThink of monitoring and maintenance like keeping your car in top shape. You need regular checkups, oil changes, and repairs to keep it running efficiently and safely.\\n\\n\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 88}]}, {"cell_type": "markdown", "source": ["Absolutely!  The fifth point in an MLOps pipeline is **Monitoring and Maintenance**.\n", "\n", "This stage is crucial because it ensures that the deployed model continues to perform well in the real world.  \n", "\n", "Here's a breakdown:\n", "\n", "* **Performance Tracking:**  We continuously monitor the model's performance metrics, like accuracy, precision, recall, and F1-score.  This helps us detect any degradation in performance over time.\n", "* **Data Drift Detection:**  Real-world data can change, and if the input data distribution shifts significantly from what the model was trained on, its performance can suffer.  We use techniques to detect these data drifts.\n", "* **Error Analysis:**  We analyze any errors or unexpected outputs from the model to understand the reasons behind them. This can help us identify areas for improvement in the model or the data.\n", "* **Model Retraining:**  Based on performance monitoring and data drift detection, we might need to retrain the model with updated data to maintain its effectiveness.\n", "* **System Health Checks:**  We also monitor the infrastructure and systems supporting the model deployment to ensure they are running smoothly and securely.\n", "\n", "\n", "Think of monitoring and maintenance like keeping your car in top shape. You need regular checkups, oil changes, and repairs to keep it running efficiently and safely.\n", "\n", "\n", "\n"], "metadata": {"id": "FgNQ7TbdMcoA"}}, {"cell_type": "code", "source": ["conversation_with_summary.memory.buffer"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 162}, "id": "UnwvA7MqYxYw", "outputId": "b20df4f5-9db5-47aa-9435-b26940d66cc7"}, "execution_count": 90, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'The human greets the AI and asks what it\\'s up to. The AI responds by saying it\\'s waiting for interesting questions and asks the human what\\'s going on in their world. The human tells the AI that <PERSON> and <PERSON><PERSON> are working on a production-ready MLOps project focused on healthcare. The AI expresses interest in the project and asks for more details, such as the type of model they are using and the challenges they are facing. The human clarifies that the project focuses on healthcare. The AI, intrigued by the field\\'s complexity and importance, asks about the specific health problems the project addresses, whether it\\'s for diagnosis, treatment planning, drug discovery, or something else. It also expresses interest in the unique challenges they face, particularly regarding data privacy and security in healthcare.  The human then asks the AI to describe an MLOps pipeline in six points. The AI obliges, providing a concise explanation of each stage: data collection and preparation, model development, model evaluation, model deployment, monitoring and maintenance, and versioning and experiment tracking. The human asks how many points are in the pipeline, and the AI confirms there are six. Then, the human asks for an explanation of the fifth point, \"Monitoring and Maintenance.\" The AI elaborates on this stage, highlighting its importance in ensuring the deployed model\\'s continued performance in the real world. It describes key aspects like performance tracking, data drift detection, error analysis, model retraining, and system health checks, using the analogy of car maintenance to illustrate its significance. \\n\\n\\n'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 90}]}, {"cell_type": "markdown", "source": ["# Conversation Summary Buffer Memory"], "metadata": {"id": "x0dDwGFZd5UH"}}, {"cell_type": "markdown", "source": ["While summary is good, we know that recent conversation has high correlation to upcoming query and\n", "\n", "A summary of old conversation with a buffer memory of last few conversation would be a good combination. This class exactly does that.\n", "\n", "You can set the token limit which define how much historical conversation to be kept along with the summary. Higher the token size more the exact conversation history kept as-it-is."], "metadata": {"id": "fwRMCMa37OGA"}}, {"cell_type": "code", "source": ["from langchain.memory import ConversationSummaryBufferMemory"], "metadata": {"id": "pJWamX6dYu3_"}, "execution_count": 91, "outputs": []}, {"cell_type": "code", "source": ["memory2 = ConversationSummaryBufferMemory(llm=model,return_messages=True)"], "metadata": {"id": "JrmZ7BIceAPG"}, "execution_count": 92, "outputs": []}, {"cell_type": "code", "source": ["memory2.save_context(\n", "    {\"input\": \"It's a machine learning project focused on healthcare.\"},\n", "    {\"output\": \"Sounds exciting! Are they building a prediction model or something else\"}\n", ")\n", "memory2.save_context(\n", "    {\"input\": \"Yes, they’re working on a model to predict patient outcomes.\"},\n", "    {\"output\": \"Impressive! How far along are they with the project?\"}\n", ")\n"], "metadata": {"id": "J9Dgn3pafik7"}, "execution_count": 93, "outputs": []}, {"cell_type": "code", "source": ["memory2.load_memory_variables({})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SGwrNY39gOqe", "outputId": "884b2989-d181-4b41-a615-d88ba2cf095a"}, "execution_count": 94, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': [HumanMessage(content=\"It's a machine learning project focused on healthcare.\"),\n", "  AIMessage(content='Sounds exciting! Are they building a prediction model or something else'),\n", "  HumanMessage(content='Yes, they’re working on a model to predict patient outcomes.'),\n", "  AIMessage(content='Impressive! How far along are they with the project?')]}"]}, "metadata": {}, "execution_count": 94}]}, {"cell_type": "code", "source": ["memory3 = ConversationSummaryBufferMemory(llm=model,return_messages=True,max_token_limit=50)"], "metadata": {"id": "xOe2ohwSgSDG"}, "execution_count": 95, "outputs": []}, {"cell_type": "code", "source": ["memory3.save_context(\n", "    {\"input\": \"<PERSON> and <PERSON><PERSON> are working on a hackathon project.\"},\n", "    {\"output\": \"That's awesome! What's the hackathon project about?\"}\n", ")\n", "memory3.save_context(\n", "    {\"input\": \"It's a machine learning project focused on healthcare.\"},\n", "    {\"output\": \"Sounds exciting! Are they building a prediction model or something else?\"}\n", ")\n", "memory3.save_context(\n", "    {\"input\": \"Yes, they’re working on a model to predict patient outcomes.\"},\n", "    {\"output\": \"Impressive! Wishing <PERSON> and <PERSON><PERSON> all the best for their project.\"}\n", ")"], "metadata": {"id": "AAuBIP0FgmO3"}, "execution_count": 98, "outputs": []}, {"cell_type": "code", "source": ["#memory3.clear()"], "metadata": {"id": "ChgkyCaxbdAU"}, "execution_count": 97, "outputs": []}, {"cell_type": "code", "source": ["memory3.load_memory_variables({})[\"history\"]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "IG4ihK2cbrql", "outputId": "ccc260ce-9299-4a5b-c439-b8d0e03b3896"}, "execution_count": 101, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[SystemMessage(content=\"A human mentions that <PERSON> and <PERSON><PERSON> are working on a hackathon project. The AI expresses enthusiasm and asks about the project. The human reveals that it's a machine learning project focused on healthcare. \\n\"),\n", " AIMessage(content='Sounds exciting! Are they building a prediction model or something else?'),\n", " HumanMessage(content='Yes, they’re working on a model to predict patient outcomes.'),\n", " AIMessage(content='Impressive! Wishing <PERSON> and <PERSON><PERSON> all the best for their project.')]"]}, "metadata": {}, "execution_count": 101}]}, {"cell_type": "code", "source": ["memory3.load_memory_variables({})[\"history\"][0].content"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 54}, "id": "Zhsc_gU3b3TW", "outputId": "6566bb2d-975e-4039-e6c2-f85f2a28e58a"}, "execution_count": 103, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["\"A human mentions that <PERSON> and <PERSON><PERSON> are working on a hackathon project. The AI expresses enthusiasm and asks about the project. The human reveals that it's a machine learning project focused on healthcare. \\n\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 103}]}, {"cell_type": "markdown", "source": ["AIMessage(content='Sounds exciting! Are they building a prediction model or something else?'),\n", "\n", "HumanMessage(content='Yes, they’re working on a model to predict patient outcomes.'),\n", "\n", "AIMessage(content='Impressive! Wishing <PERSON> and <PERSON><PERSON> all the best for their project.')"], "metadata": {"id": "NqIz-Yg3cEHb"}}, {"cell_type": "code", "source": ["memory4 = ConversationSummaryBufferMemory(llm=model,return_messages=True,max_token_limit=20)"], "metadata": {"id": "I48xWE5e06tL"}, "execution_count": 105, "outputs": []}, {"cell_type": "code", "source": ["memory4.save_context(\n", "    {\"input\": \"<PERSON> and <PERSON><PERSON> are working on a hackathon project.\"},\n", "    {\"output\": \"That's awesome! What's the hackathon project about?\"}\n", ")\n", "memory4.save_context(\n", "    {\"input\": \"It's a machine learning project focused on healthcare.\"},\n", "    {\"output\": \"Sounds exciting! Are they building a prediction model or something else?\"}\n", ")\n", "memory4.save_context(\n", "    {\"input\": \"Yes, they’re working on a model to predict patient outcomes.\"},\n", "    {\"output\": \"Impressive! Wishing <PERSON> and <PERSON><PERSON> all the best for their project.\"}\n", ")"], "metadata": {"id": "cEfsThiN3mlK"}, "execution_count": 106, "outputs": []}, {"cell_type": "code", "source": ["memory4.load_memory_variables({})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6s1X55XKccyJ", "outputId": "64691a41-69a9-4c0a-9af4-8f7161d9ef9e"}, "execution_count": 107, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': [SystemMessage(content='The human tells the AI that <PERSON> and <PERSON><PERSON> are working on a hackathon project. The AI is excited and asks about the project, learning it is a machine learning project focused on healthcare. The AI then asks if they are building a prediction model and learns that they are working on a model to predict patient outcomes. \\n'),\n", "  AIMessage(content='Impressive! Wishing <PERSON> and <PERSON><PERSON> all the best for their project.')]}"]}, "metadata": {}, "execution_count": 107}]}, {"cell_type": "code", "source": ["from langchain.chains import ConversationChain\n", "\n", "conversation_with_summary = ConversationChain(\n", "    llm=model,\n", "    # We set a very low max_token_limit for the purposes of testing.\n", "    memory=ConversationSummaryBufferMemory(llm=model, max_token_limit=40),\n", "    verbose=True,\n", ")\n", "conversation_with_summary.predict(input=\"Hi, what's up?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 290}, "id": "UrE697y6grl8", "outputId": "7c14e3c1-6b20-4772-ae52-dee3fad699d5"}, "execution_count": 108, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "\n", "Human: Hi, what's up?\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"Hi there!  Not much, just waiting to see what interesting questions you have for me. What about you? What's going on in your world today? 😊 \\n\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 108}]}, {"cell_type": "code", "source": ["conversation_with_summary.predict(input=\"Just working on writing some documentation on machine learning!\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 362}, "id": "opRAv9LgiAod", "outputId": "c1af6e8b-6da8-4f17-9ab5-795856499950"}, "execution_count": 109, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "System: New summary: The human greets the AI with a casual \"Hi, what's up?\". \n", "\n", "AI: Hi there!  Not much, just waiting to see what interesting questions you have for me. What about you? What's going on in your world today? 😊 \n", "\n", "Human: Just working on writing some documentation on machine learning!\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"That's fascinating! I can't actually *write* documentation myself, as I don't have hands or interact with the physical world. But I can definitely help you with it! \\n\\nWhat kind of documentation are you working on?  Are you focusing on a particular algorithm or concept? Maybe I can provide some relevant information or examples.  \\n\\n\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 109}]}, {"cell_type": "code", "source": ["conversation_with_summary.predict(input=\"give me some points for writing about the document\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 399}, "id": "x3H2jeVEiHUN", "outputId": "6ba6dafa-1989-447e-a1ff-f48b65fd185d"}, "execution_count": 110, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "System: New summary: The human greets the AI and tells it they are working on documentation for machine learning. The AI expresses interest and offers to help, asking what kind of documentation they are working on and if they are focusing on a specific algorithm or concept. \n", "\n", "\n", "Human: give me some points for writing about the document\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"I'd love to help brainstorm! To give you the most relevant points, tell me:\\n\\n* **What's the target audience for this documentation?** \\n    * Are they beginners just learning about machine learning, or are they experienced developers looking for a deep dive into a specific algorithm? \\n* **What's the main goal of the documentation?** \\n    * Is it to explain the basics of machine learning, provide a tutorial on how to implement a specific algorithm, or something else entirely?\\n* **Do you have a specific algorithm or concept in mind?** \\n    * For example, are you focusing on supervised learning, deep learning, natural language processing, computer vision, or something else? \\n\\nThe more information you give me, the better I can tailor my suggestions to your needs.  😊 \\n\\n\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 110}]}, {"cell_type": "code", "source": ["conversation_with_summary.predict(input=\"can you list out the resources from the previous message\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 471}, "id": "qRUfbtM3iRdN", "outputId": "43449926-483f-4e84-99db-9f78f9ff4169"}, "execution_count": 111, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "System: The human greets the AI and tells it they are working on documentation for machine learning. The AI expresses interest and offers to help, asking what kind of documentation they are working on and if they are focusing on a specific algorithm or concept. The AI then provides a list of questions to help tailor its suggestions for the documentation, including:\n", "\n", "* Who is the target audience? (Beginners or experienced developers)\n", "* What is the main goal of the documentation? (Explain basics, provide a tutorial, etc.)\n", "* Is there a specific algorithm or concept being focused on? (Supervised learning, deep learning, etc.) \n", "\n", "\n", "\n", "\n", "Human: can you list out the resources from the previous message\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"Of course! Here are the resources I mentioned in my previous message:\\n\\n* **Questions to help tailor my suggestions:**\\n\\n    * Who is the target audience? (Beginners or experienced developers)\\n    * What is the main goal of the documentation? (Explain basics, provide a tutorial, etc.)\\n    * Is there a specific algorithm or concept being focused on? (Supervised learning, deep learning, etc.) \\n\\n\\nLet me know if you'd like to discuss any of these in more detail! I'm ready to help however I can.  \\n\\n\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 111}]}, {"cell_type": "code", "source": ["Conversation Knowledge Graph Memory : Uses a knowledge graph to store information and relationships between entities\n", "\n", "VectorStore-Backed Memory: Uses vector embeddings to store and retrieve information based on semantic similarity.\n", "\n", "ConversationTokenBufferMemory: Instead of “k” conversations being remembered in ConversationBufferWindowMemory, in this case we want to remember last set of discussion based on “max token limit”.\n"], "metadata": {"id": "GxbjdvEHidtl"}, "execution_count": null, "outputs": []}]}