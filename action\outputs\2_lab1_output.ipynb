{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## WELCOME TO THE FINALE - PROJECT 3\n", "\n", "And\n", "\n", "# WELCOME TO THE **M**ODEL **C**ONTEXT **P**ROTOCOL!\n", "\n", "And welcome back to OpenAI Agents SDK\n", "\n", "# Please consider this next hour to be a 'teaser'\n", "\n", "We will fly by a lot of functionality; gain intuition, come back later\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/stop.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">To Windows PC people - known issue with MCP servers on PCs</h2>\n", "            <span style=\"color:#ff7800;\">I have unpleasant news. There's a problem running MCP Servers on Windows PCs; Mac and Linux is fine. This is a known issue as of May 4th, 2025. I asked o3 with Deep Research to try to find workarounds; it <a href=\"https://chatgpt.com/share/6817bbc3-3d0c-8012-9b51-631842470628\">confirmed the issue</a> and confirmed the workaround.<br/><br/>\n", "            The workaround is a bit of a bore. It is to take advantage of \"WSL\", the Microsoft approach for running Linux on your PC. You'll need to carry out more setup instructions! But it's quick, and several students have confirmed that this works perfectly for them, then the Week 6 MCP labs work. Plus, WSL is actually a great way to build software on your Windows PC.<br/>\n", "            The WSL Setup instructions are in the Setup folder, <a href=\"../setup/SETUP-WSL.md\">in the file called SETUP-WSL.md here</a>. I do hope this only holds you up briefly - you should be back up and running quickly. Oh the joys of working with bleeding-edge technology!<br/><br/>\n", "            With many thanks to students <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and several others, for helping me work on it and confirming the fix.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# The imports\n", "\n", "from dotenv import load_dotenv\n", "from agents import Agent, Runner, trace\n", "from agents.mcp import MCPServerStdio\n", "import os\n", "from IPython.display import Markdown, display\n", "load_dotenv(override=True)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You may need to install node if you don't have it already.\n", "\n", "https://chatgpt.com/share/68103af2-e2dc-8012-b259-bc135a23273b"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Let's use MCP in OpenAI Agents SDK\n", "\n", "Fetch MCP tools: runs a headless browser controlled by <PERSON><PERSON>\n", "\n", "Filesystem MCP tools: can read and write from a chosen directory on your computer\n", "\n", "Let's put them together to carry out an important task."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Tool(name='fetch', description='Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.', inputSchema={'description': 'Parameters for fetching a URL.', 'properties': {'url': {'description': 'URL to fetch', 'format': 'uri', 'minLength': 1, 'title': 'Url', 'type': 'string'}, 'max_length': {'default': 5000, 'description': 'Maximum number of characters to return.', 'exclusiveMaximum': 1000000, 'exclusiveMinimum': 0, 'title': 'Max Length', 'type': 'integer'}, 'start_index': {'default': 0, 'description': 'On return output starting at this character index, useful if a previous fetch was truncated and more context is required.', 'minimum': 0, 'title': 'Start Index', 'type': 'integer'}, 'raw': {'default': False, 'description': 'Get the actual HTML content of the requested page, without simplification.', 'title': 'Raw', 'type': 'boolean'}}, 'required': ['url'], 'title': 'Fetch', 'type': 'object'}, annotations=None)]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# A python based MCP server\n", "\n", "fetch_params = {\"command\": \"uvx\", \"args\": [\"mcp-server-fetch\"]}\n", "\n", "async with MCPServerStdio(params=fetch_params) as server:\n", "    fetch_tools = await server.list_tools()\n", "\n", "fetch_tools"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Tool(name='read_file', description='Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.', inputSchema={'type': 'object', 'properties': {'path': {'type': 'string'}}, 'required': ['path'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=None),\n", " Tool(name='read_multiple_files', description=\"Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file's content is returned with its path as a reference. Failed reads for individual files won't stop the entire operation. Only works within allowed directories.\", inputSchema={'type': 'object', 'properties': {'paths': {'type': 'array', 'items': {'type': 'string'}}}, 'required': ['paths'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=None),\n", " Tool(name='write_file', description='Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.', inputSchema={'type': 'object', 'properties': {'path': {'type': 'string'}, 'content': {'type': 'string'}}, 'required': ['path', 'content'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=None),\n", " Tool(name='edit_file', description='Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.', inputSchema={'type': 'object', 'properties': {'path': {'type': 'string'}, 'edits': {'type': 'array', 'items': {'type': 'object', 'properties': {'oldText': {'type': 'string', 'description': 'Text to search for - must match exactly'}, 'newText': {'type': 'string', 'description': 'Text to replace with'}}, 'required': ['oldText', 'newText'], 'additionalProperties': False}}, 'dryRun': {'type': 'boolean', 'default': False, 'description': 'Preview changes using git-style diff format'}}, 'required': ['path', 'edits'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=None),\n", " Tool(name='create_directory', description='Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.', inputSchema={'type': 'object', 'properties': {'path': {'type': 'string'}}, 'required': ['path'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=None),\n", " Tool(name='list_directory', description='Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.', inputSchema={'type': 'object', 'properties': {'path': {'type': 'string'}}, 'required': ['path'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=None),\n", " Tool(name='directory_tree', description=\"Get a recursive tree view of files and directories as a JSON structure. Each entry includes 'name', 'type' (file/directory), and 'children' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.\", inputSchema={'type': 'object', 'properties': {'path': {'type': 'string'}}, 'required': ['path'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=None),\n", " Tool(name='move_file', description='Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.', inputSchema={'type': 'object', 'properties': {'source': {'type': 'string'}, 'destination': {'type': 'string'}}, 'required': ['source', 'destination'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=None),\n", " Tool(name='search_files', description=\"Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don't know their exact location. Only searches within allowed directories.\", inputSchema={'type': 'object', 'properties': {'path': {'type': 'string'}, 'pattern': {'type': 'string'}, 'excludePatterns': {'type': 'array', 'items': {'type': 'string'}, 'default': []}}, 'required': ['path', 'pattern'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=None),\n", " Tool(name='get_file_info', description='Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.', inputSchema={'type': 'object', 'properties': {'path': {'type': 'string'}}, 'required': ['path'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=None),\n", " Tool(name='list_allowed_directories', description='Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.', inputSchema={'type': 'object', 'properties': {}, 'required': []}, annotations=None)]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# A JavaScript based MCP server\n", "\n", "sandbox_path = os.path.abspath(os.path.join(os.getcwd(), \"sandbox\"))\n", "files_params = {\"command\": \"npx\", \"args\": [\"-y\", \"@modelcontextprotocol/server-filesystem\", sandbox_path]}\n", "\n", "async with MCPServerStdio(params=files_params) as server:\n", "    file_tools = await server.list_tools()\n", "\n", "file_tools"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/markdown": ["I've created a markdown file for the Banoffee Pie recipe. You can find it at `sandbox/banoffee.md`. Here's a summary of the recipe:\n", "\n", "```markdown\n", "# Banoffee Pie Recipe\n", "\n", "## Ingredients\n", "- **For the pastry:**  \n", "  - <PERSON><PERSON>  \n", "  - Flour  \n", "  - Egg yolk  \n", "  - Sugar  \n", "  - Very cold water  \n", "\n", "- **For the filling:**  \n", "  - Caramel (half for the base, half for layering)  \n", "  - Banana<PERSON> (sliced)  \n", "  - Whipped cream  \n", "  - Dark chocolate (for garnish)  \n", "\n", "## Method\n", "1. **Make the pastry case:**  \n", "   - In a food processor, pulse butter and flour until it resembles fresh breadcrumbs.  \n", "   - Add egg yolk and sugar, then pulse again. Gradually add very cold water until it forms a ball of dough.  \n", "   - Knead gently then wrap in cling film and chill in the fridge for 30 mins.\n", "\n", "2. **Prepare for baking:**  \n", "   - Preheat the oven to 190C/170C/gas 4. Grease a 23cm loose-bottomed tart tin.  \n", "   - Roll out the pastry and line the tin, snipping excess pastry edge.  \n", "   - Line with baking parchment and weigh down with baking beans. Bake for 15 mins, then remove the paper and beans, brush with egg white, and bake for an additional 15-20 mins until golden. Cool completely.\n", "\n", "3. **Assemble the pie:**  \n", "   - Spread half of the caramel on the cooled pastry case and layer sliced bananas on top.  \n", "   - Cover with the remaining caramel and chill in the fridge.\n", "\n", "4. **Finish off:**  \n", "   - Whip the cream until thick and spoon over the tart.  \n", "   - Garnish with finely grated dark chocolate before serving.\n", "```\n", "\n", "Feel free to check the file or modify it as needed!"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["instructions = \"\"\"\n", "You browse the internet to accomplish your instructions.\n", "Be persistent until you have solved your assignment,\n", "trying different options and sites as needed.\n", "\"\"\"\n", "\n", "\n", "async with MCPServerStdio(params=fetch_params) as fetch_mcp_server:\n", "    async with MCPServerStdio(params=files_params) as files_mcp_server:\n", "        agent = Agent(\n", "            name=\"investigator\", \n", "            instructions=instructions, \n", "            model=\"gpt-4o-mini\",\n", "            mcp_servers=[fetch_mcp_server, files_mcp_server]\n", "            )\n", "        with trace(\"investigate\"):\n", "            result = await <PERSON>.run(agent, \"Find a great recipe for Banoffee Pie, then summarize it in markdown to banoffee.md\")\n", "            display(Markdown(result.final_output))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}