{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU", "widgets": {"application/vnd.jupyter.widget-state+json": {"dd5cbd1a967c43cd8520fff6227a41b5": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_8444d16768d8412db16f592c663ac2ee", "IPY_MODEL_9bf68b229b554df1827b74efaffbbb5e", "IPY_MODEL_1293aad1760d4bf68e58cf5d9888df78"], "layout": "IPY_MODEL_b8407a3d1ce845ce9183f378daf35c18"}}, "8444d16768d8412db16f592c663ac2ee": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5d372cc0a3294f21959367ae2408dfd6", "placeholder": "​", "style": "IPY_MODEL_8983e11d5a2d4c6d986d7133877f5298", "value": "config.json: 100%"}}, "9bf68b229b554df1827b74efaffbbb5e": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b418f4a18d594d688b30095d6c307289", "max": 571, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a5fdf629d03f423ab810a33863b756a7", "value": 571}}, "1293aad1760d4bf68e58cf5d9888df78": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_695fbba5151e493ebd8e5637db31f18d", "placeholder": "​", "style": "IPY_MODEL_8aaa86a36a084b979e8de9c19295473d", "value": " 571/571 [00:00&lt;00:00, 37.0kB/s]"}}, "b8407a3d1ce845ce9183f378daf35c18": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5d372cc0a3294f21959367ae2408dfd6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8983e11d5a2d4c6d986d7133877f5298": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b418f4a18d594d688b30095d6c307289": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a5fdf629d03f423ab810a33863b756a7": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "695fbba5151e493ebd8e5637db31f18d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8aaa86a36a084b979e8de9c19295473d": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "492fbd0296944481987615718e8d9d9b": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1e0dd6926dd64f12ad95b17202b9dbaa", "IPY_MODEL_79a2d832f73a4f1f9058de2b5489f07e", "IPY_MODEL_13656119cab642f1979ecb7a6a224dec"], "layout": "IPY_MODEL_28ca702f4f604cc482dae607b828e917"}}, "1e0dd6926dd64f12ad95b17202b9dbaa": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c2c5810a3c14495f91499b2d6b3c4627", "placeholder": "​", "style": "IPY_MODEL_b61a39f8ce064b62a328d7da0823abb7", "value": "model.safetensors.index.json: 100%"}}, "79a2d832f73a4f1f9058de2b5489f07e": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_22cbe78cd06d431688e7c44a3a790b79", "max": 25125, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f2f3587adfbc44e5ab32f6c13c81f631", "value": 25125}}, "13656119cab642f1979ecb7a6a224dec": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_afc409c61ddb4ea3abff15438b1c91c2", "placeholder": "​", "style": "IPY_MODEL_7e96f952ee82402b903a1f2c23c8cf96", "value": " 25.1k/25.1k [00:00&lt;00:00, 1.74MB/s]"}}, "28ca702f4f604cc482dae607b828e917": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c2c5810a3c14495f91499b2d6b3c4627": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b61a39f8ce064b62a328d7da0823abb7": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "22cbe78cd06d431688e7c44a3a790b79": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f2f3587adfbc44e5ab32f6c13c81f631": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "afc409c61ddb4ea3abff15438b1c91c2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7e96f952ee82402b903a1f2c23c8cf96": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cb50e138a59d4bdaab38a2ef6ea02339": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_fe9526f59e774754a2ad4176d3db3c8c", "IPY_MODEL_2fe02108b8ba458285603f50cda06c23", "IPY_MODEL_2e4fdb1949b2498888c4a41c2f0a4d8e"], "layout": "IPY_MODEL_ee2acada158b4e03ad3b1c91667b73c8"}}, "fe9526f59e774754a2ad4176d3db3c8c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1e8dfc158ecf4706af5ccda8c4940b88", "placeholder": "​", "style": "IPY_MODEL_3d25b560105344e0b2e55bea057c1459", "value": "Downloading shards: 100%"}}, "2fe02108b8ba458285603f50cda06c23": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0968757ddc9c476f85311f7826b015ef", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b8404f1636d24c93831331f2ded431d8", "value": 2}}, "2e4fdb1949b2498888c4a41c2f0a4d8e": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c85467854127467ba34e3c2a491d373c", "placeholder": "​", "style": "IPY_MODEL_eafc35172453428bb6f9a7ed738be365", "value": " 2/2 [01:24&lt;00:00, 38.44s/it]"}}, "ee2acada158b4e03ad3b1c91667b73c8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1e8dfc158ecf4706af5ccda8c4940b88": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3d25b560105344e0b2e55bea057c1459": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0968757ddc9c476f85311f7826b015ef": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b8404f1636d24c93831331f2ded431d8": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c85467854127467ba34e3c2a491d373c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "eafc35172453428bb6f9a7ed738be365": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6686977b63404f6db04e5586b6191e04": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b10835c6cfe44fb3b06c6a8a426f95da", "IPY_MODEL_c987da924207433da56cd7180a1e3638", "IPY_MODEL_d87dbe1bc9f9437db5db7b0346c7604d"], "layout": "IPY_MODEL_3db55bbcd3964060be33c17e768baf2a"}}, "b10835c6cfe44fb3b06c6a8a426f95da": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_519ea56649674f678fa1294da9b7e4f5", "placeholder": "​", "style": "IPY_MODEL_9f0b688cfc6647eda521ce087ef81667", "value": "model-00001-of-00002.safetensors: 100%"}}, "c987da924207433da56cd7180a1e3638": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e955c27a42374a8a8d363fa77200cfa9", "max": 9942981696, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_67c028c10ce64914b83cf294375a4071", "value": 9942981696}}, "d87dbe1bc9f9437db5db7b0346c7604d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_26b14d42b2a744c38a3bc94150c8e36a", "placeholder": "​", "style": "IPY_MODEL_c3b84d376faf4e6a8cd2f800be94783a", "value": " 9.94G/9.94G [01:03&lt;00:00, 254MB/s]"}}, "3db55bbcd3964060be33c17e768baf2a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "519ea56649674f678fa1294da9b7e4f5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9f0b688cfc6647eda521ce087ef81667": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e955c27a42374a8a8d363fa77200cfa9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "67c028c10ce64914b83cf294375a4071": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "26b14d42b2a744c38a3bc94150c8e36a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c3b84d376faf4e6a8cd2f800be94783a": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2e645764024f44b197a15eb40f68aa52": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e45f5736462f472088e63e59f518f789", "IPY_MODEL_50224ccf914d435c86f328fd04695022", "IPY_MODEL_614e3411b6084ad69f72fbc047f60869"], "layout": "IPY_MODEL_abc08239af24459b95f7aa9be86da030"}}, "e45f5736462f472088e63e59f518f789": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e6fdd9c2150144098b7178d362b83b10", "placeholder": "​", "style": "IPY_MODEL_977a192392194c3ba2dac845d940a92f", "value": "model-00002-of-00002.safetensors: 100%"}}, "50224ccf914d435c86f328fd04695022": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1138b12d28a34eabbfb73030fc3055ca", "max": 4540516344, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_3a2c956b8eac434b91711f13da391d4f", "value": 4540516344}}, "614e3411b6084ad69f72fbc047f60869": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1720c99486e84def869dcd57197a443b", "placeholder": "​", "style": "IPY_MODEL_19b34ddde5054448b9693b8bba725dc1", "value": " 4.54G/4.54G [00:20&lt;00:00, 231MB/s]"}}, "abc08239af24459b95f7aa9be86da030": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e6fdd9c2150144098b7178d362b83b10": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "977a192392194c3ba2dac845d940a92f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1138b12d28a34eabbfb73030fc3055ca": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3a2c956b8eac434b91711f13da391d4f": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "1720c99486e84def869dcd57197a443b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "19b34ddde5054448b9693b8bba725dc1": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e59a1f02c2c64c82a98ed4ec8126ac17": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0f76a73f9f4f4d73abbb23755d1d817f", "IPY_MODEL_da756ae49de344eab2163133df806531", "IPY_MODEL_583dbec04078405abd29cc56cb3a11a0"], "layout": "IPY_MODEL_226ea5b4838f45e2a24c2efb1849e34c"}}, "0f76a73f9f4f4d73abbb23755d1d817f": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a3ad56dc686b457b91daae39dca24835", "placeholder": "​", "style": "IPY_MODEL_2dc829c8294a4c1491ff5d56364b72f6", "value": "Loading checkpoint shards: 100%"}}, "da756ae49de344eab2163133df806531": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f25f2cef80c64cd0b40fe6d8dc09f5c8", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b8aa3c7b39494e95b7a710a742b5dbf9", "value": 2}}, "583dbec04078405abd29cc56cb3a11a0": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_873b746879ee4a2eaa97e0c54e9a126b", "placeholder": "​", "style": "IPY_MODEL_1797ce9fd78142c8b18a08716f4e7d28", "value": " 2/2 [01:05&lt;00:00, 30.42s/it]"}}, "226ea5b4838f45e2a24c2efb1849e34c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a3ad56dc686b457b91daae39dca24835": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2dc829c8294a4c1491ff5d56364b72f6": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f25f2cef80c64cd0b40fe6d8dc09f5c8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b8aa3c7b39494e95b7a710a742b5dbf9": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "873b746879ee4a2eaa97e0c54e9a126b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1797ce9fd78142c8b18a08716f4e7d28": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6870db13150147aabd935aa70ec350f1": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_fd1cb02b527944eaab2783a99294d172", "IPY_MODEL_217e56acadab4417b3a44eb8145732d5", "IPY_MODEL_0513257d81154388870ffc7eab2317b1"], "layout": "IPY_MODEL_aa09362e2f3140d08c74dac129f170c3"}}, "fd1cb02b527944eaab2783a99294d172": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a3131bf61b7c4c5c80b5ade0d960cd1f", "placeholder": "​", "style": "IPY_MODEL_325e80a4379240688d632ba3cad2e6aa", "value": "generation_config.json: 100%"}}, "217e56acadab4417b3a44eb8145732d5": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_33d52b220a3f4d90a340cffb51cb8333", "max": 116, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4b7e966f1ecd4b4f9e7bb5c55b7e1a7d", "value": 116}}, "0513257d81154388870ffc7eab2317b1": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_29ae8e293137446fbea1384cdc78bfb9", "placeholder": "​", "style": "IPY_MODEL_9f621c6a68394952963c27f049e195ce", "value": " 116/116 [00:00&lt;00:00, 6.66kB/s]"}}, "aa09362e2f3140d08c74dac129f170c3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a3131bf61b7c4c5c80b5ade0d960cd1f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "325e80a4379240688d632ba3cad2e6aa": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "33d52b220a3f4d90a340cffb51cb8333": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4b7e966f1ecd4b4f9e7bb5c55b7e1a7d": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "29ae8e293137446fbea1384cdc78bfb9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9f621c6a68394952963c27f049e195ce": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ac8fb708a6334bb3989c8dcdb9522b49": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_33f55a66a8a84bf5a82662c15413103c", "IPY_MODEL_f67a8d5b90b641e8ac3187c6831de143", "IPY_MODEL_e22e792c0616459a97d62940fd15e669"], "layout": "IPY_MODEL_ee1f169d0091425286f200524a780a12"}}, "33f55a66a8a84bf5a82662c15413103c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_aa01c4758dbf45f7959a2f3490ad2037", "placeholder": "​", "style": "IPY_MODEL_b64410fdc9b049f9b996d54b3b5dec82", "value": "tokenizer_config.json: 100%"}}, "f67a8d5b90b641e8ac3187c6831de143": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_77b8d398a61148f689b576a823f9f1f2", "max": 1467, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4cf031673e284e54bcbc3ccf5b9e3868", "value": 1467}}, "e22e792c0616459a97d62940fd15e669": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1b7f4d24fdd5473fba8e564452139e92", "placeholder": "​", "style": "IPY_MODEL_5c395df37f6d4b049ce4c7c1a6b6eac6", "value": " 1.47k/1.47k [00:00&lt;00:00, 86.5kB/s]"}}, "ee1f169d0091425286f200524a780a12": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "aa01c4758dbf45f7959a2f3490ad2037": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b64410fdc9b049f9b996d54b3b5dec82": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "77b8d398a61148f689b576a823f9f1f2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4cf031673e284e54bcbc3ccf5b9e3868": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "1b7f4d24fdd5473fba8e564452139e92": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5c395df37f6d4b049ce4c7c1a6b6eac6": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "07cf3c551f3b4ca3a63f0b3e7501cd67": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c26643d93235443ea99f59a77f1c7b0a", "IPY_MODEL_e87dcccfb96f47ba9099622c7b2d8a51", "IPY_MODEL_379eb11fc5814f8799b8f45eeba8072c"], "layout": "IPY_MODEL_5eab37bc6617483ca2744c790e13d87d"}}, "c26643d93235443ea99f59a77f1c7b0a": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_aa631f6a79ab4c2c9e2b11cc356a5633", "placeholder": "​", "style": "IPY_MODEL_cf418f17a87744449c86c3b3db57dcad", "value": "tokenizer.model: 100%"}}, "e87dcccfb96f47ba9099622c7b2d8a51": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_880554b7a0404dca997ce1d94da9c97f", "max": 493443, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f580ddc5778649c2be1648cfdefe1b61", "value": 493443}}, "379eb11fc5814f8799b8f45eeba8072c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b31241c7e9d341e7a61483d9aa47c898", "placeholder": "​", "style": "IPY_MODEL_3a78b7b56320489aabb947d86b933f0c", "value": " 493k/493k [00:00&lt;00:00, 33.6MB/s]"}}, "5eab37bc6617483ca2744c790e13d87d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "aa631f6a79ab4c2c9e2b11cc356a5633": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cf418f17a87744449c86c3b3db57dcad": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "880554b7a0404dca997ce1d94da9c97f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f580ddc5778649c2be1648cfdefe1b61": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b31241c7e9d341e7a61483d9aa47c898": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3a78b7b56320489aabb947d86b933f0c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "946da7977691449ca379b06a315334d5": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9b5c5c479edc49579fb61c406aea54bd", "IPY_MODEL_80768bfd36b045fcaaf026d7047e3a44", "IPY_MODEL_184e6a7917b14c6eb8edcf03288940f9"], "layout": "IPY_MODEL_225ad10f0d1048bba62990287fdb8f76"}}, "9b5c5c479edc49579fb61c406aea54bd": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a958a8da218a4a8b9ade2f643d293a78", "placeholder": "​", "style": "IPY_MODEL_45e72108de31456a909f038b2e7487c3", "value": "tokenizer.json: 100%"}}, "80768bfd36b045fcaaf026d7047e3a44": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_098cadbfe744497f91f54ad42c38f3b7", "max": 1795303, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b3642d7f5e8c48e7a1fb39822f1d1c2d", "value": 1795303}}, "184e6a7917b14c6eb8edcf03288940f9": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_588d0237a1774ab494c46ec13b7ab578", "placeholder": "​", "style": "IPY_MODEL_9f734da521f74696a51e6ecf1420823f", "value": " 1.80M/1.80M [00:00&lt;00:00, 3.61MB/s]"}}, "225ad10f0d1048bba62990287fdb8f76": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a958a8da218a4a8b9ade2f643d293a78": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "45e72108de31456a909f038b2e7487c3": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "098cadbfe744497f91f54ad42c38f3b7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b3642d7f5e8c48e7a1fb39822f1d1c2d": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "588d0237a1774ab494c46ec13b7ab578": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9f734da521f74696a51e6ecf1420823f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b56d4ded73cf4630afd9b6cbde49e0d5": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4f6b296e5ae2415592250b60d10a4a24", "IPY_MODEL_f714e4af7d91463a8a228ede9a7f18c1", "IPY_MODEL_2f583b4ef0bd47628b9145ef7f60e387"], "layout": "IPY_MODEL_a4b3af280d9845e291e621eafc51ab8c"}}, "4f6b296e5ae2415592250b60d10a4a24": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ce663a1b3d7c4d11ab52b813cb0a0f3a", "placeholder": "​", "style": "IPY_MODEL_3b14dd425ff645c3a3b2f757e74990ef", "value": "special_tokens_map.json: 100%"}}, "f714e4af7d91463a8a228ede9a7f18c1": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5c32940ce356438da6ec2bd7f42e7d47", "max": 72, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ebb47d7a41e3488cb7d7278497d8ee8c", "value": 72}}, "2f583b4ef0bd47628b9145ef7f60e387": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c9675199f6fb4fd3b28d49827299409c", "placeholder": "​", "style": "IPY_MODEL_b5a4f9ddbf1a4dd9999c208261bde0ad", "value": " 72.0/72.0 [00:00&lt;00:00, 5.29kB/s]"}}, "a4b3af280d9845e291e621eafc51ab8c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ce663a1b3d7c4d11ab52b813cb0a0f3a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3b14dd425ff645c3a3b2f757e74990ef": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5c32940ce356438da6ec2bd7f42e7d47": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ebb47d7a41e3488cb7d7278497d8ee8c": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c9675199f6fb4fd3b28d49827299409c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b5a4f9ddbf1a4dd9999c208261bde0ad": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b374c20dc50e4850857b31dbad215cf1": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9a1c114114f04a4abbc9f163a0804f7a", "IPY_MODEL_fc3ea5e27b974c51a7a5c6971474b5fb", "IPY_MODEL_4f282fc21c1340148e43a3b56f0e8fc0"], "layout": "IPY_MODEL_d2649cc4517f4af9a23277131fa64a5a"}}, "9a1c114114f04a4abbc9f163a0804f7a": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_70238250c0684cdca666a931596c6dde", "placeholder": "​", "style": "IPY_MODEL_f76ebed4482744eea4521c6250a867dd", "value": "modules.json: 100%"}}, "fc3ea5e27b974c51a7a5c6971474b5fb": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6140734d619e46018982df0fe5a7bb86", "max": 349, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f72a1a9f8cd845ea94587b6f6fab5474", "value": 349}}, "4f282fc21c1340148e43a3b56f0e8fc0": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_84c5417bada246d8b26122b4a76db143", "placeholder": "​", "style": "IPY_MODEL_bd7eda890cd94ea9b5b5dfd23508b220", "value": " 349/349 [00:00&lt;00:00, 24.4kB/s]"}}, "d2649cc4517f4af9a23277131fa64a5a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "70238250c0684cdca666a931596c6dde": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f76ebed4482744eea4521c6250a867dd": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6140734d619e46018982df0fe5a7bb86": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f72a1a9f8cd845ea94587b6f6fab5474": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "84c5417bada246d8b26122b4a76db143": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bd7eda890cd94ea9b5b5dfd23508b220": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ee5b6806f1fc491fbbe8bd6a5457f229": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_92db8f39ebbc433896abb720a3324753", "IPY_MODEL_e4450c44e4f84531a80a94a356c0094f", "IPY_MODEL_ebdd0568585c4731865a2ac85ec677ee"], "layout": "IPY_MODEL_7ae45bc5a20b496da43a3d74c4eccee4"}}, "92db8f39ebbc433896abb720a3324753": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e71c56df2aab4e208badc5b0225f3796", "placeholder": "​", "style": "IPY_MODEL_0855dce963744867924d027baad24c4c", "value": "config_sentence_transformers.json: 100%"}}, "e4450c44e4f84531a80a94a356c0094f": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e829a83fe16545f09deccafab0cec555", "max": 116, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a27b123120ce4238ab8f44e04dcb9595", "value": 116}}, "ebdd0568585c4731865a2ac85ec677ee": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_34b54e53fe314dfe8d5db61b927eee79", "placeholder": "​", "style": "IPY_MODEL_3d10cb8d75954abc88868d7cbbf3d4c9", "value": " 116/116 [00:00&lt;00:00, 8.81kB/s]"}}, "7ae45bc5a20b496da43a3d74c4eccee4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e71c56df2aab4e208badc5b0225f3796": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0855dce963744867924d027baad24c4c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e829a83fe16545f09deccafab0cec555": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a27b123120ce4238ab8f44e04dcb9595": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "34b54e53fe314dfe8d5db61b927eee79": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3d10cb8d75954abc88868d7cbbf3d4c9": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6ac493552765456a903f531f421740e2": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d6bf34f7d3764d65bd296a29be4763ec", "IPY_MODEL_c9650e654e16421ba3c565b2d16fde4e", "IPY_MODEL_2fb0a9256f7045829c1685aeb5e88010"], "layout": "IPY_MODEL_1ef32d22d4524c21b990e261e9dc9a41"}}, "d6bf34f7d3764d65bd296a29be4763ec": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d81fd21044d24fb09f4b288a4f005525", "placeholder": "​", "style": "IPY_MODEL_64d9c7dabbaa45fc8164f7d95a0db63d", "value": "README.md: 100%"}}, "c9650e654e16421ba3c565b2d16fde4e": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3b0e122de9a64dfd8849d16864a1e8dc", "max": 10621, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_03a32c88fe8c49c6b178ea0bd17bcca8", "value": 10621}}, "2fb0a9256f7045829c1685aeb5e88010": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d56321d81f574c248134b6a697e52982", "placeholder": "​", "style": "IPY_MODEL_4f59356e38e14dc19cac4292183ca035", "value": " 10.6k/10.6k [00:00&lt;00:00, 608kB/s]"}}, "1ef32d22d4524c21b990e261e9dc9a41": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d81fd21044d24fb09f4b288a4f005525": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "64d9c7dabbaa45fc8164f7d95a0db63d": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3b0e122de9a64dfd8849d16864a1e8dc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "03a32c88fe8c49c6b178ea0bd17bcca8": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d56321d81f574c248134b6a697e52982": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4f59356e38e14dc19cac4292183ca035": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e0f873a9077f4375ad68e52b87e689ff": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7888f27f6c8b416ba66945ba108b94e9", "IPY_MODEL_fd85bfbe602344bfb1a8a2e4d65b8e48", "IPY_MODEL_82601baaec594014822c0d5b15b922e5"], "layout": "IPY_MODEL_89cb8953aaf844b69829754affbbe02f"}}, "7888f27f6c8b416ba66945ba108b94e9": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b89a7d6a6d15479fb0010a8a27094df8", "placeholder": "​", "style": "IPY_MODEL_e0f24e6310e640bbbbb24585d32de2de", "value": "sentence_bert_config.json: 100%"}}, "fd85bfbe602344bfb1a8a2e4d65b8e48": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_84519662745849d19b56973752f0e0f6", "max": 53, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_077b75340d484d348f30b2087963b3ad", "value": 53}}, "82601baaec594014822c0d5b15b922e5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_61cc455588a0412f909f93f549d905cc", "placeholder": "​", "style": "IPY_MODEL_058abc0d7d8b4c18afe8a52b88186af3", "value": " 53.0/53.0 [00:00&lt;00:00, 3.55kB/s]"}}, "89cb8953aaf844b69829754affbbe02f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b89a7d6a6d15479fb0010a8a27094df8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e0f24e6310e640bbbbb24585d32de2de": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "84519662745849d19b56973752f0e0f6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "077b75340d484d348f30b2087963b3ad": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "61cc455588a0412f909f93f549d905cc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "058abc0d7d8b4c18afe8a52b88186af3": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6d22979dddfe4a79a18444ac17533e5c": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_79ed361591b9419cbea46987d5a54c89", "IPY_MODEL_96890db6d3cb4ff1967baeecf66cc21d", "IPY_MODEL_882cd564839d474f8da666f9706cb5b2"], "layout": "IPY_MODEL_d8d54ea71ee7470687fb24b21a4280b3"}}, "79ed361591b9419cbea46987d5a54c89": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_df51d71be5c74703b00383c39b27a999", "placeholder": "​", "style": "IPY_MODEL_fe6fcc6691034b42bd9851d36ac664cf", "value": "config.json: 100%"}}, "96890db6d3cb4ff1967baeecf66cc21d": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a6a5f2d7bb26485cac86ec5ba3306ae8", "max": 571, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2774719aa3b44699a21d63824f48e087", "value": 571}}, "882cd564839d474f8da666f9706cb5b2": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bfe1515c12f74d69a4f2ef0095faf929", "placeholder": "​", "style": "IPY_MODEL_27ba43b61b8f4142a09f58e96e224fbd", "value": " 571/571 [00:00&lt;00:00, 6.90kB/s]"}}, "d8d54ea71ee7470687fb24b21a4280b3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "df51d71be5c74703b00383c39b27a999": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fe6fcc6691034b42bd9851d36ac664cf": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a6a5f2d7bb26485cac86ec5ba3306ae8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2774719aa3b44699a21d63824f48e087": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "bfe1515c12f74d69a4f2ef0095faf929": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "27ba43b61b8f4142a09f58e96e224fbd": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1c08c7dee27c49c2b9928dfb995aab8d": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0921f03f08d44083b6f4fa7d228cb12a", "IPY_MODEL_7f9e0bf6f2aa4f5a8f074f4618ce14fa", "IPY_MODEL_03f15f89737c4bf2899fea40abd4f57f"], "layout": "IPY_MODEL_e033814b8576426c8c129bb0cc7d5989"}}, "0921f03f08d44083b6f4fa7d228cb12a": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5af78e959b904d2a8d674ff0f50cafc2", "placeholder": "​", "style": "IPY_MODEL_378145700f0e40ebb2c69c3a2bb6cc75", "value": "model.safetensors: 100%"}}, "7f9e0bf6f2aa4f5a8f074f4618ce14fa": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_da426d0e899c49e6ae78efac337a256d", "max": 437971872, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_037e87bc8039471c88175b65c98f8462", "value": 437971872}}, "03f15f89737c4bf2899fea40abd4f57f": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a23cd24e713a49abacb2beea0c49db4d", "placeholder": "​", "style": "IPY_MODEL_7f138e0339a2497a982177e146d9b778", "value": " 438M/438M [00:01&lt;00:00, 197MB/s]"}}, "e033814b8576426c8c129bb0cc7d5989": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5af78e959b904d2a8d674ff0f50cafc2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "378145700f0e40ebb2c69c3a2bb6cc75": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "da426d0e899c49e6ae78efac337a256d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "037e87bc8039471c88175b65c98f8462": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "a23cd24e713a49abacb2beea0c49db4d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7f138e0339a2497a982177e146d9b778": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d773fd1010e54c1290965df31f45c57a": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9b4f9d70e14448db901592b1cd2c808f", "IPY_MODEL_040d315fbe5f46cfaa3b56be7199a9e0", "IPY_MODEL_ecb43e36f20a49859bc7695bb4a21089"], "layout": "IPY_MODEL_cbc9f166ccbc4f169112ef848bdf2cdf"}}, "9b4f9d70e14448db901592b1cd2c808f": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4c6fb5207f6a4e36bc73acb94c761080", "placeholder": "​", "style": "IPY_MODEL_d89c07fb26db4783931c37c0c76917cb", "value": "tokenizer_config.json: 100%"}}, "040d315fbe5f46cfaa3b56be7199a9e0": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_82e0a7d44ddd4a26b44e374de4af4ae9", "max": 363, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_bda3531228994266aafda2501676ae4d", "value": 363}}, "ecb43e36f20a49859bc7695bb4a21089": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e3394b0da30f4e758059711db0ed665f", "placeholder": "​", "style": "IPY_MODEL_3511d760002d4d68a424b00724c363f6", "value": " 363/363 [00:00&lt;00:00, 20.0kB/s]"}}, "cbc9f166ccbc4f169112ef848bdf2cdf": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4c6fb5207f6a4e36bc73acb94c761080": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d89c07fb26db4783931c37c0c76917cb": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "82e0a7d44ddd4a26b44e374de4af4ae9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bda3531228994266aafda2501676ae4d": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e3394b0da30f4e758059711db0ed665f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3511d760002d4d68a424b00724c363f6": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "30d7f94a0bb8465b85a5f297095d6745": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b1d1b6c881284bd69b74121fc24620ca", "IPY_MODEL_b2ac29c1fb614df4ab71afb8bada8f1e", "IPY_MODEL_33f9b51d2cba4e0585ccc9e6a2781dd2"], "layout": "IPY_MODEL_df1348af99a74a0ebe1203202475461b"}}, "b1d1b6c881284bd69b74121fc24620ca": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_37a415f6e70b47449a76a6bce1030031", "placeholder": "​", "style": "IPY_MODEL_caa295fb977d4017b4524c0fbf343c7d", "value": "vocab.txt: 100%"}}, "b2ac29c1fb614df4ab71afb8bada8f1e": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cbae3ab46e4b4019b51071836917f301", "max": 231536, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_63c27f553f1449a9b21cbe944426bfef", "value": 231536}}, "33f9b51d2cba4e0585ccc9e6a2781dd2": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_54916166e38c433a998808b638d88840", "placeholder": "​", "style": "IPY_MODEL_79255c6e47ca40a2ad1c2bd037ab649e", "value": " 232k/232k [00:00&lt;00:00, 946kB/s]"}}, "df1348af99a74a0ebe1203202475461b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "37a415f6e70b47449a76a6bce1030031": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "caa295fb977d4017b4524c0fbf343c7d": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cbae3ab46e4b4019b51071836917f301": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "63c27f553f1449a9b21cbe944426bfef": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "54916166e38c433a998808b638d88840": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "79255c6e47ca40a2ad1c2bd037ab649e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e746226ce13f4875b81dbc746997ad84": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7e7b314134614b9d96a3c9f95a742c25", "IPY_MODEL_d67e9a5c64634e759d571b7774d204b6", "IPY_MODEL_90c4de7de9e2435ca36f5a4960b15fbb"], "layout": "IPY_MODEL_99b9da0ac0cd4919b715c19f7465af67"}}, "7e7b314134614b9d96a3c9f95a742c25": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ef5bde446e8142818ec57969144cd6b2", "placeholder": "​", "style": "IPY_MODEL_e14d67be717a4f9587643dd4af32c345", "value": "tokenizer.json: 100%"}}, "d67e9a5c64634e759d571b7774d204b6": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b682fbc3e6c849e2ac70934f52028323", "max": 466021, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d389de267d0b4ac5a3555714f1cfe80b", "value": 466021}}, "90c4de7de9e2435ca36f5a4960b15fbb": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_83c26a9b20444f26b60776ece61f69b2", "placeholder": "​", "style": "IPY_MODEL_ddf58aa133254cd3b95f31ccaf08b880", "value": " 466k/466k [00:00&lt;00:00, 946kB/s]"}}, "99b9da0ac0cd4919b715c19f7465af67": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ef5bde446e8142818ec57969144cd6b2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e14d67be717a4f9587643dd4af32c345": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b682fbc3e6c849e2ac70934f52028323": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d389de267d0b4ac5a3555714f1cfe80b": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "83c26a9b20444f26b60776ece61f69b2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ddf58aa133254cd3b95f31ccaf08b880": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8edffee4a0f3481ea460c50721cc13e5": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_db1c6b1f0cfe4c598caf741168068f25", "IPY_MODEL_6af1bee1967a40b89f8cfbd2687d96f9", "IPY_MODEL_15d13e6b1fba4ddabe0f5bbe5988bd94"], "layout": "IPY_MODEL_da98dc04099a4786b2f8f6232fd7c4c6"}}, "db1c6b1f0cfe4c598caf741168068f25": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d40e5726111a403c9101820dd1ab12c4", "placeholder": "​", "style": "IPY_MODEL_9d1b889b5d3044a680075411a4970a7e", "value": "special_tokens_map.json: 100%"}}, "6af1bee1967a40b89f8cfbd2687d96f9": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_933559963a5242a4943dadefca235ada", "max": 239, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b5d7a637707d48918217a68eeb8c4163", "value": 239}}, "15d13e6b1fba4ddabe0f5bbe5988bd94": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dd2a965527ae449f88696fe8a1e07a3e", "placeholder": "​", "style": "IPY_MODEL_d0893bc09aaf4bc88f5045ad0c3430b9", "value": " 239/239 [00:00&lt;00:00, 9.78kB/s]"}}, "da98dc04099a4786b2f8f6232fd7c4c6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d40e5726111a403c9101820dd1ab12c4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9d1b889b5d3044a680075411a4970a7e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "933559963a5242a4943dadefca235ada": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b5d7a637707d48918217a68eeb8c4163": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "dd2a965527ae449f88696fe8a1e07a3e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d0893bc09aaf4bc88f5045ad0c3430b9": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "25b636ca56bb4ceb9f8125899ee00297": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_165bac09663343c38c5e73c4d2494090", "IPY_MODEL_ce8a9d6496c04350a209c55f2b4cd37f", "IPY_MODEL_fb0ef984cde04b8e92c5fa41c59d3663"], "layout": "IPY_MODEL_61a2a20be6b64e8782237c6167d89745"}}, "165bac09663343c38c5e73c4d2494090": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_65d1cdaeb86d4f2c91d5f3cede8a24f4", "placeholder": "​", "style": "IPY_MODEL_d277f862ebb24112b16a53e3ae2282b8", "value": "1_Pooling/config.json: 100%"}}, "ce8a9d6496c04350a209c55f2b4cd37f": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cb7a4d55396743dfa0895cac3c4fd87a", "max": 190, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_68eb1154ac74496786e34b767ccdee66", "value": 190}}, "fb0ef984cde04b8e92c5fa41c59d3663": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_83ac4442dc4d4b88b8ffdac2564fffdb", "placeholder": "​", "style": "IPY_MODEL_49d914e1fd794ed9835c74227fb345f8", "value": " 190/190 [00:00&lt;00:00, 2.38kB/s]"}}, "61a2a20be6b64e8782237c6167d89745": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "65d1cdaeb86d4f2c91d5f3cede8a24f4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d277f862ebb24112b16a53e3ae2282b8": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cb7a4d55396743dfa0895cac3c4fd87a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "68eb1154ac74496786e34b767ccdee66": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "83ac4442dc4d4b88b8ffdac2564fffdb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "49d914e1fd794ed9835c74227fb345f8": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hT98mSf6USb8", "outputId": "23875c09-1677-411d-cb26-ef76d618bc7d"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting llama-index-llms-huggingface\n", "  Downloading llama_index_llms_huggingface-0.1.4-py3-none-any.whl (7.2 kB)\n", "Requirement already satisfied: huggingface-hub<0.21.0,>=0.20.3 in /usr/local/lib/python3.10/dist-packages (from llama-index-llms-huggingface) (0.20.3)\n", "Collecting llama-index-core<0.11.0,>=0.10.1 (from llama-index-llms-huggingface)\n", "  Downloading llama_index_core-0.10.28-py3-none-any.whl (15.4 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m15.4/15.4 MB\u001b[0m \u001b[31m17.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: torch<3.0.0,>=2.1.2 in /usr/local/lib/python3.10/dist-packages (from llama-index-llms-huggingface) (2.2.1+cu121)\n", "Requirement already satisfied: transformers[torch]<5.0.0,>=4.37.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-llms-huggingface) (4.38.2)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<0.21.0,>=0.20.3->llama-index-llms-huggingface) (3.13.3)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<0.21.0,>=0.20.3->llama-index-llms-huggingface) (2023.6.0)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<0.21.0,>=0.20.3->llama-index-llms-huggingface) (2.31.0)\n", "Requirement already satisfied: tqdm>=4.42.1 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<0.21.0,>=0.20.3->llama-index-llms-huggingface) (4.66.2)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<0.21.0,>=0.20.3->llama-index-llms-huggingface) (6.0.1)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<0.21.0,>=0.20.3->llama-index-llms-huggingface) (4.10.0)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<0.21.0,>=0.20.3->llama-index-llms-huggingface) (24.0)\n", "Requirement already satisfied: SQLAlchemy[asyncio]>=1.4.49 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (2.0.29)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.6 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (3.9.3)\n", "Collecting dataclasses-json (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface)\n", "  Downloading dataclasses_json-0.6.4-py3-none-any.whl (28 kB)\n", "Collecting deprecated>=1.2.9.3 (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface)\n", "  Downloading Deprecated-1.2.14-py2.py3-none-any.whl (9.6 kB)\n", "Collecting <PERSON><PERSON><PERSON><2.0.0,>=1.0.8 (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface)\n", "  Downloading dirtyjson-1.0.8-py3-none-any.whl (25 kB)\n", "Collecting httpx (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface)\n", "  Downloading httpx-0.27.0-py3-none-any.whl (75 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m11.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting llamaindex-py-client<0.2.0,>=0.1.16 (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface)\n", "  Downloading llamaindex_py_client-0.1.16-py3-none-any.whl (135 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m136.0/136.0 kB\u001b[0m \u001b[31m18.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: nest-asyncio<2.0.0,>=1.5.8 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (1.6.0)\n", "Requirement already satisfied: networkx>=3.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (3.2.1)\n", "Requirement already satisfied: nltk<4.0.0,>=3.8.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (3.8.1)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (1.25.2)\n", "Collecting openai>=1.1.0 (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface)\n", "  Downloading openai-1.16.2-py3-none-any.whl (267 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m267.1/267.1 kB\u001b[0m \u001b[31m34.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (2.0.3)\n", "Requirement already satisfied: pillow>=9.0.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (9.4.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.2.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (8.2.3)\n", "Collecting tiktoken>=0.3.3 (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface)\n", "  Downloading tiktoken-0.6.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.8/1.8 MB\u001b[0m \u001b[31m84.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting typing-inspect>=0.8.0 (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Requirement already satisfied: wrapt in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (1.14.1)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-llms-huggingface) (1.12)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-llms-huggingface) (3.1.3)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.1.105 (from torch<3.0.0,>=2.1.2->llama-index-llms-huggingface)\n", "  Using cached nvidia_cuda_nvrtc_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (23.7 MB)\n", "Collecting nvidia-cuda-runtime-cu12==12.1.105 (from torch<3.0.0,>=2.1.2->llama-index-llms-huggingface)\n", "  Using cached nvidia_cuda_runtime_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (823 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.1.105 (from torch<3.0.0,>=2.1.2->llama-index-llms-huggingface)\n", "  Using cached nvidia_cuda_cupti_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (14.1 MB)\n", "Collecting nvidia-cudnn-cu12==******** (from torch<3.0.0,>=2.1.2->llama-index-llms-huggingface)\n", "  Using cached nvidia_cudnn_cu12-********-py3-none-manylinux1_x86_64.whl (731.7 MB)\n", "Collecting nvidia-cublas-cu12==******** (from torch<3.0.0,>=2.1.2->llama-index-llms-huggingface)\n", "  Using cached nvidia_cublas_cu12-********-py3-none-manylinux1_x86_64.whl (410.6 MB)\n", "Collecting nvidia-cufft-cu12==********* (from torch<3.0.0,>=2.1.2->llama-index-llms-huggingface)\n", "  Using cached nvidia_cufft_cu12-*********-py3-none-manylinux1_x86_64.whl (121.6 MB)\n", "Collecting nvidia-curand-cu12==********** (from torch<3.0.0,>=2.1.2->llama-index-llms-huggingface)\n", "  Using cached nvidia_curand_cu12-**********-py3-none-manylinux1_x86_64.whl (56.5 MB)\n", "Collecting nvidia-cusolver-cu12==********** (from torch<3.0.0,>=2.1.2->llama-index-llms-huggingface)\n", "  Using cached nvidia_cusolver_cu12-**********-py3-none-manylinux1_x86_64.whl (124.2 MB)\n", "Collecting nvidia-cusparse-cu12==********** (from torch<3.0.0,>=2.1.2->llama-index-llms-huggingface)\n", "  Using cached nvidia_cusparse_cu12-**********-py3-none-manylinux1_x86_64.whl (196.0 MB)\n", "Collecting nvidia-nccl-cu12==2.19.3 (from torch<3.0.0,>=2.1.2->llama-index-llms-huggingface)\n", "  Using cached nvidia_nccl_cu12-2.19.3-py3-none-manylinux1_x86_64.whl (166.0 MB)\n", "Collecting nvidia-nvtx-cu12==12.1.105 (from torch<3.0.0,>=2.1.2->llama-index-llms-huggingface)\n", "  Using cached nvidia_nvtx_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (99 kB)\n", "Requirement already satisfied: triton==2.2.0 in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-llms-huggingface) (2.2.0)\n", "Collecting nvidia-nvjitlink-cu12 (from nvidia-cusolver-cu12==**********->torch<3.0.0,>=2.1.2->llama-index-llms-huggingface)\n", "  Using cached nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.10/dist-packages (from transformers[torch]<5.0.0,>=4.37.0->llama-index-llms-huggingface) (2023.12.25)\n", "Requirement already satisfied: tokenizers<0.19,>=0.14 in /usr/local/lib/python3.10/dist-packages (from transformers[torch]<5.0.0,>=4.37.0->llama-index-llms-huggingface) (0.15.2)\n", "Requirement already satisfied: safetensors>=0.4.1 in /usr/local/lib/python3.10/dist-packages (from transformers[torch]<5.0.0,>=4.37.0->llama-index-llms-huggingface) (0.4.2)\n", "Collecting accelerate>=0.21.0 (from transformers[torch]<5.0.0,>=4.37.0->llama-index-llms-huggingface)\n", "  Downloading accelerate-0.29.2-py3-none-any.whl (297 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m297.4/297.4 kB\u001b[0m \u001b[31m32.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: psutil in /usr/local/lib/python3.10/dist-packages (from accelerate>=0.21.0->transformers[torch]<5.0.0,>=4.37.0->llama-index-llms-huggingface) (5.9.5)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (4.0.3)\n", "Requirement already satisfied: pydantic>=1.10 in /usr/local/lib/python3.10/dist-packages (from llamaindex-py-client<0.2.0,>=0.1.16->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (2.6.4)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (2024.2.2)\n", "Collecting httpcore==1.* (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m11.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (3.6)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (1.3.1)\n", "Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface)\n", "  Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m8.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (8.1.7)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (1.3.2)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai>=1.1.0->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (1.7.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub<0.21.0,>=0.20.3->llama-index-llms-huggingface) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub<0.21.0,>=0.20.3->llama-index-llms-huggingface) (2.0.7)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy[asyncio]>=1.4.49->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (3.0.3)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect>=0.8.0->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface)\n", "  Downloading marshmallow-3.21.1-py3-none-any.whl (49 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.4/49.4 kB\u001b[0m \u001b[31m7.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch<3.0.0,>=2.1.2->llama-index-llms-huggingface) (2.1.5)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (2024.1)\n", "Requirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->torch<3.0.0,>=2.1.2->llama-index-llms-huggingface) (1.3.0)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (1.2.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->llamaindex-py-client<0.2.0,>=0.1.16->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.16.3 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->llamaindex-py-client<0.2.0,>=0.1.16->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (2.16.3)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.8.2->pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-llms-huggingface) (1.16.0)\n", "Installing collected packages: dirtyjson, nvidia-nvtx-cu12, nvidia-nvjitlink-cu12, nvidia-nccl-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, mypy-extensions, marshmallow, h11, deprecated, typing-inspect, tiktoken, nvidia-cusparse-cu12, nvidia-cudnn-cu12, httpcore, nvidia-cusolver-cu12, httpx, dataclasses-json, openai, llamaindex-py-client, llama-index-core, accelerate, llama-index-llms-huggingface\n", "Successfully installed accelerate-0.29.2 dataclasses-json-0.6.4 deprecated-1.2.14 dirtyjson-1.0.8 h11-0.14.0 httpcore-1.0.5 httpx-0.27.0 llama-index-core-0.10.28 llama-index-llms-huggingface-0.1.4 llamaindex-py-client-0.1.16 marshmallow-3.21.1 mypy-extensions-1.0.0 nvidia-cublas-cu12-******** nvidia-cuda-cupti-cu12-12.1.105 nvidia-cuda-nvrtc-cu12-12.1.105 nvidia-cuda-runtime-cu12-12.1.105 nvidia-cudnn-cu12-******** nvidia-cufft-cu12-********* nvidia-curand-cu12-********** nvidia-cusolver-cu12-********** nvidia-cusparse-cu12-********** nvidia-nccl-cu12-2.19.3 nvidia-nvjitlink-cu12-12.4.127 nvidia-nvtx-cu12-12.1.105 openai-1.16.2 tiktoken-0.6.0 typing-inspect-0.9.0\n"]}], "source": ["%pip install llama-index-llms-huggingface"]}, {"cell_type": "code", "source": ["!pip install llama-index"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8ujTBSlVxWce", "outputId": "a1d4041d-31c9-499f-cc08-fedb3228b937"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting llama-index\n", "  Downloading llama_index-0.10.28-py3-none-any.whl (6.9 kB)\n", "Collecting llama-index-agent-openai<0.3.0,>=0.1.4 (from llama-index)\n", "  Downloading llama_index_agent_openai-0.2.2-py3-none-any.whl (12 kB)\n", "Collecting llama-index-cli<0.2.0,>=0.1.2 (from llama-index)\n", "  Downloading llama_index_cli-0.1.11-py3-none-any.whl (26 kB)\n", "Requirement already satisfied: llama-index-core<0.11.0,>=0.10.28 in /usr/local/lib/python3.10/dist-packages (from llama-index) (0.10.28)\n", "Collecting llama-index-embeddings-openai<0.2.0,>=0.1.5 (from llama-index)\n", "  Downloading llama_index_embeddings_openai-0.1.7-py3-none-any.whl (6.0 kB)\n", "Collecting llama-index-indices-managed-llama-cloud<0.2.0,>=0.1.2 (from llama-index)\n", "  Downloading llama_index_indices_managed_llama_cloud-0.1.5-py3-none-any.whl (6.7 kB)\n", "Collecting llama-index-legacy<0.10.0,>=0.9.48 (from llama-index)\n", "  Downloading llama_index_legacy-0.9.48-py3-none-any.whl (2.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m5.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting llama-index-llms-openai<0.2.0,>=0.1.13 (from llama-index)\n", "  Downloading llama_index_llms_openai-0.1.15-py3-none-any.whl (10 kB)\n", "Collecting llama-index-multi-modal-llms-openai<0.2.0,>=0.1.3 (from llama-index)\n", "  Downloading llama_index_multi_modal_llms_openai-0.1.5-py3-none-any.whl (5.8 kB)\n", "Collecting llama-index-program-openai<0.2.0,>=0.1.3 (from llama-index)\n", "  Downloading llama_index_program_openai-0.1.5-py3-none-any.whl (4.1 kB)\n", "Collecting llama-index-question-gen-openai<0.2.0,>=0.1.2 (from llama-index)\n", "  Downloading llama_index_question_gen_openai-0.1.3-py3-none-any.whl (2.9 kB)\n", "Collecting llama-index-readers-file<0.2.0,>=0.1.4 (from llama-index)\n", "  Downloading llama_index_readers_file-0.1.16-py3-none-any.whl (36 kB)\n", "Collecting llama-index-readers-llama-parse<0.2.0,>=0.1.2 (from llama-index)\n", "  Downloading llama_index_readers_llama_parse-0.1.4-py3-none-any.whl (2.5 kB)\n", "Requirement already satisfied: openai>=1.14.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-agent-openai<0.3.0,>=0.1.4->llama-index) (1.16.2)\n", "Requirement already satisfied: PyYAML>=6.0.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy[asyncio]>=1.4.49 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (2.0.29)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.6 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (3.9.3)\n", "Requirement already satisfied: dataclasses-json in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (0.6.4)\n", "Requirement already satisfied: deprecated>=1.2.9.3 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (1.2.14)\n", "Requirement already satisfied: <PERSON><PERSON><PERSON><2.0.0,>=1.0.8 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (1.0.8)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (2023.6.0)\n", "Requirement already satisfied: httpx in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (0.27.0)\n", "Requirement already satisfied: llamaindex-py-client<0.2.0,>=0.1.16 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (0.1.16)\n", "Requirement already satisfied: nest-asyncio<2.0.0,>=1.5.8 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (1.6.0)\n", "Requirement already satisfied: networkx>=3.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (3.2.1)\n", "Requirement already satisfied: nltk<4.0.0,>=3.8.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (3.8.1)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (1.25.2)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (2.0.3)\n", "Requirement already satisfied: pillow>=9.0.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (9.4.0)\n", "Requirement already satisfied: requests>=2.31.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.2.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (8.2.3)\n", "Requirement already satisfied: tiktoken>=0.3.3 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (0.6.0)\n", "Requirement already satisfied: tqdm<5.0.0,>=4.66.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (4.66.2)\n", "Requirement already satisfied: typing-extensions>=4.5.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (4.10.0)\n", "Requirement already satisfied: typing-inspect>=0.8.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (0.9.0)\n", "Requirement already satisfied: wrapt in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.28->llama-index) (1.14.1)\n", "Requirement already satisfied: beautifulsoup4<5.0.0,>=4.12.3 in /usr/local/lib/python3.10/dist-packages (from llama-index-readers-file<0.2.0,>=0.1.4->llama-index) (4.12.3)\n", "Collecting pymupdf<2.0.0,>=1.23.21 (from llama-index-readers-file<0.2.0,>=0.1.4->llama-index)\n", "  Downloading PyMuPDF-1.24.1-cp310-none-manylinux2014_x86_64.whl (3.9 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.9/3.9 MB\u001b[0m \u001b[31m87.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting pypdf<5.0.0,>=4.0.1 (from llama-index-readers-file<0.2.0,>=0.1.4->llama-index)\n", "  Downloading pypdf-4.2.0-py3-none-any.whl (290 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m290.4/290.4 kB\u001b[0m \u001b[31m37.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting striprtf<0.0.27,>=0.0.26 (from llama-index-readers-file<0.2.0,>=0.1.4->llama-index)\n", "  Downloading striprtf-0.0.26-py3-none-any.whl (6.9 kB)\n", "Collecting llama-parse<0.5.0,>=0.4.0 (from llama-index-readers-llama-parse<0.2.0,>=0.1.2->llama-index)\n", "  Downloading llama_parse-0.4.0-py3-none-any.whl (7.0 kB)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.28->llama-index) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.28->llama-index) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.28->llama-index) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.28->llama-index) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.28->llama-index) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.28->llama-index) (4.0.3)\n", "Requirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.10/dist-packages (from beautifulsoup4<5.0.0,>=4.12.3->llama-index-readers-file<0.2.0,>=0.1.4->llama-index) (2.5)\n", "Requirement already satisfied: pydantic>=1.10 in /usr/local/lib/python3.10/dist-packages (from llamaindex-py-client<0.2.0,>=0.1.16->llama-index-core<0.11.0,>=0.10.28->llama-index) (2.6.4)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.28->llama-index) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.28->llama-index) (2024.2.2)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.28->llama-index) (1.0.5)\n", "Requirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.28->llama-index) (3.6)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.28->llama-index) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx->llama-index-core<0.11.0,>=0.10.28->llama-index) (0.14.0)\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.28->llama-index) (8.1.7)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.28->llama-index) (1.3.2)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.28->llama-index) (2023.12.25)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai>=1.14.0->llama-index-agent-openai<0.3.0,>=0.1.4->llama-index) (1.7.0)\n", "Collecting PyMuPDFb==1.24.1 (from pymupdf<2.0.0,>=1.23.21->llama-index-readers-file<0.2.0,>=0.1.4->llama-index)\n", "  Downloading PyMuPDFb-1.24.1-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (30.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m30.8/30.8 MB\u001b[0m \u001b[31m14.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->llama-index-core<0.11.0,>=0.10.28->llama-index) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->llama-index-core<0.11.0,>=0.10.28->llama-index) (2.0.7)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy[asyncio]>=1.4.49->llama-index-core<0.11.0,>=0.10.28->llama-index) (3.0.3)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from typing-inspect>=0.8.0->llama-index-core<0.11.0,>=0.10.28->llama-index) (1.0.0)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->llama-index-core<0.11.0,>=0.10.28->llama-index) (3.21.1)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.28->llama-index) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.28->llama-index) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.28->llama-index) (2024.1)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx->llama-index-core<0.11.0,>=0.10.28->llama-index) (1.2.0)\n", "Requirement already satisfied: packaging>=17.0 in /usr/local/lib/python3.10/dist-packages (from marshmallow<4.0.0,>=3.18.0->dataclasses-json->llama-index-core<0.11.0,>=0.10.28->llama-index) (24.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->llamaindex-py-client<0.2.0,>=0.1.16->llama-index-core<0.11.0,>=0.10.28->llama-index) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.16.3 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->llamaindex-py-client<0.2.0,>=0.1.16->llama-index-core<0.11.0,>=0.10.28->llama-index) (2.16.3)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.8.2->pandas->llama-index-core<0.11.0,>=0.10.28->llama-index) (1.16.0)\n", "Installing collected packages: striprtf, pypdf, PyMuPDFb, pymupdf, llama-index-legacy, llama-parse, llama-index-readers-file, llama-index-llms-openai, llama-index-indices-managed-llama-cloud, llama-index-embeddings-openai, llama-index-readers-llama-parse, llama-index-multi-modal-llms-openai, llama-index-cli, llama-index-agent-openai, llama-index-program-openai, llama-index-question-gen-openai, llama-index\n", "Successfully installed PyMuPDFb-1.24.1 llama-index-0.10.28 llama-index-agent-openai-0.2.2 llama-index-cli-0.1.11 llama-index-embeddings-openai-0.1.7 llama-index-indices-managed-llama-cloud-0.1.5 llama-index-legacy-0.9.48 llama-index-llms-openai-0.1.15 llama-index-multi-modal-llms-openai-0.1.5 llama-index-program-openai-0.1.5 llama-index-question-gen-openai-0.1.3 llama-index-readers-file-0.1.16 llama-index-readers-llama-parse-0.1.4 llama-parse-0.4.0 pymupdf-1.24.1 pypdf-4.2.0 striprtf-0.0.26\n"]}]}, {"cell_type": "code", "source": ["from llama_index.core import VectorStoreIndex, SimpleDirectoryReader\n", "from llama_index.llms.huggingface import HuggingFaceLLM"], "metadata": {"id": "kJnGN2Krxby3"}, "execution_count": 3, "outputs": []}, {"cell_type": "code", "source": ["!mkdir data"], "metadata": {"id": "D31Z5mDXx9n-"}, "execution_count": 4, "outputs": []}, {"cell_type": "code", "source": ["# load documents\n", "documents = SimpleDirectoryReader(\"./data/\").load_data()"], "metadata": {"id": "nP14A9EByIvt"}, "execution_count": 5, "outputs": []}, {"cell_type": "code", "source": ["print(documents)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "S2_y2X4SyVOp", "outputId": "8be4fadf-33c9-4c9e-db98-a65954b5d0b3"}, "execution_count": 7, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[Document(id_='429fb84a-9523-4fa9-8825-2431677a57bb', embedding=None, metadata={'page_label': '1', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, excluded_embed_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, text='See discussions, st ats, and author pr ofiles f or this public ation at : https://www .researchgate.ne t/public ation/355917108\\nNeu ral Machine T ranslation with Attention\\nTechnic al R eport  · August 2021\\nDOI: 10.13140/RG.2.2.29381.37607/1\\nCITATIONS\\n0READS\\n1,579\\n2 author s:\\nMohammad W asil Saleem\\nUniv ersität P otsdam\\n3 PUBLICA TIONS \\xa0\\xa0\\xa00 CITATIONS \\xa0\\xa0\\xa0\\nSEE PROFILE\\nSandeep Upr ety\\nUniv ersität P otsdam\\n1 PUBLICA TION \\xa0\\xa0\\xa00 CITATIONS \\xa0\\xa0\\xa0\\nSEE PROFILE\\nAll c ontent f ollo wing this p age was uplo aded b y Sandeep Upr ety on 05 No vember 2021.\\nThe user has r equest ed enhanc ement of the do wnlo aded file.', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'), Document(id_='69f91b78-8896-44fb-a247-bf12174338fb', embedding=None, metadata={'page_label': '2', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, excluded_embed_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, text='Neural Machine Translation with Attention\\nMohammad Wasil Saleem\\nMatrikel-Nr.: 805779\\nUniversit ¨at Potsdam\\<EMAIL> Uprety\\nMatrikel-Nr. 804982\\nUniversit ¨at Potsdam\\<EMAIL>\\nAbstract\\nIn recent years, the success achieved\\nthrough neural machine translation has\\nmade it mainstream in machine translation\\nsystems. In this work, encoder-decoder\\nwith attention system based on ”Neural\\nMachine Translation by Jointly Learning\\nto Align and Translate” by Bahdanau et al.\\n(2014) has been used to accomplish the\\nMachine Translation between English and\\nSpanish Language which has not seen\\nmuch research work done as compared\\nto other languages such as German and\\nFrench. We aim to demonstrate the re-\\nsults similar to the breakthrough paper on\\nwhich our work is based on. We achieved\\na BLEU score of 25.37, which was close\\nenough to what Bahdanau et al. (2014)\\nachieved in their work.\\n1 Introduction\\nMachine Translation (MT) is the task of translat-\\ning text without human assistance while preserv-\\ning the meaning of input text. The early approach\\nto machine translation relied heavily on hand-\\ncrafted translation rules and linguistic knowledge.\\nStarted in early around 1950s, unlike rule-based\\nmachine translation, Statistical machine transla-\\ntion (SMT) generated translations based on statis-\\ntical models whose parameters are derived from\\nthe analysis of bilingual text corpora (Koehn et al.,\\n2003). Though reliable, for SMT, it can be hard\\nto ﬁnd content for obscure languages and is less\\nsuitable for language pairs with big differences\\nin word order making the quality of translation\\nfar from satisfactory. With the progress in deep\\nlearning being applied to MT, in 2014, end-to-end\\nneural network translation model was proposed\\nby (Bahdanau et al., 2014; Sutskever et al., 2014)\\nwhere the term ”neural machine translation” wasformally used. Neural machine translation (NMT)\\nis the newest method of MT that uses a single\\nlarge neural network to model the entire transla-\\ntion process, freeing the need for excessive fea-\\nture engineering. Through the rapid research and\\nbreakthroughs, end-to-end neural machine trans-\\nlation has gained remarkable performances (Shi\\net al., 2021; Bahdanau et al., 2014) and have be-\\ncome mainstream approach to MT.\\n2 Related Work\\nEarly problem of NMT was often the poor trans-\\nlation for long sentences (Sutskever et al., 2014)\\nwhich can be attributed to the ﬁxed-length of\\nsource encoding in conventional encoder-decoder\\nas suggested by Cho et al. (2014a) for which the\\nconcept of attention to NMT was introduced by\\nBahdanau et al. (2014) to avoid keeping a ﬁxed\\nsource side representation.\\nAs compared to separately tuned components in\\nSMT, newly emerging Neural Machine translation\\nradically departures from previous machine learn-\\ning approaches as the training of NMT is end-to-\\nend which has signiﬁcantly improved translation\\nquality across 30 different languages (Junczys-\\nDowmunt et al., 2016). NMT model can be attrac-\\ntive for various reason one being scalability issue,\\nwhether it be memory requirements or computa-\\ntional speed. Another being able to train all the\\ncharacter embedding as each characters frequently\\noccurs in the training corpus.\\nMost neural machine translation models pro-\\nposed use encoder-decoder where a neural net-\\nwork reads and encodes a source sentence into\\na ﬁxed-length vector and a decoder then outputs\\na translation from the encoded vector, where in\\nmost of the cases the encoder and decoder are\\nmainly implemented as RNNs, CNNs or self-\\nattention network (Wu et al., 2018). The whole\\nencoder–decoder system, which consists of the\\nencoder and the decoder for a language pair, is', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'), Document(id_='bac2b3a8-e632-44c4-8e70-2ad08dd4ba2b', embedding=None, metadata={'page_label': '3', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, excluded_embed_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, text='jointly trained to maximize the probability of a\\ncorrect translation given a source sentence (Bah-\\ndanau et al., 2014). After the initial proposal by\\n(Sutskever et al., 2014; Bahdanau et al., 2014),\\nmuch work has been done on the sequence-to-\\nsequence neural machine translation model rang-\\ning from new attention mechanism (Luong et al.,\\n2015) to working on the problem of out-of-\\nvocabulary words (Jean et al., 2015) for which se-\\nquential RNNs are used both for encoding source\\nsentences and generating target translation.\\nWe draw our inspiration for machine transla-\\ntion with attention from Bahdanau et al. (2014).\\nWe have chosen to base our project on this paper\\nas attention mechanism has been widely used as\\nbaseline and is thoroughly studied among the NLP\\ncommunity.\\n3 Model\\nMachine Translation is equivalent to maximizing a\\nconditional probability of a target sentence given\\nthe source sentence. In Neural Machine Trans-\\nlation, we parameterize it to maximise the condi-\\ntional probability. The approach used by Cho et al.\\n(2014b) was to encode the source sentence into\\na ﬁxed-length vector, which becomes difﬁcult to\\ncompress all the necessary information into a ﬁxed\\nlength vector, which makes it difﬁcult for the Neu-\\nral Network to handle long sentences, and thus the\\nperformance of the encoder-decoder drops as the\\nlength of the sentences increases. So we use the\\nmodel proposed by Bahdanau et al. (2014), where\\nit does not encode the input sentence into a ﬁxed-\\nlength vector, rather than it simply encodes the in-\\nput sentence into sequence of vectors, and while\\ndecoding the translation, it select subset of vectors\\nfrom the using attention mechanism. And Bah-\\ndanau et al. (2014) showed that encoder decoder\\nmodel with attention mechanism cope better with\\nlong sentences. In the next section, we will ﬁrst\\ngive a brief introduction on encoder-decoder, the\\nRNN, and one of its type, GRU, the one we used\\nin our model and ﬁnally the attention mechanism.\\n3.1 Encoder-Decoder\\nEncoder-Decoder, ﬁrst proposed by Cho et al.\\n(2014b), basically consists of 2 parts, the encoder\\nand the decoder. Encoder codes the sequence\\nof input sentence into dense vector representa-\\ntion, and then decoder takes in the encoded sen-\\ntence and decode the representation into anothersequences of words. They are trained to maximize\\nthe conditional probability of the output sentence,\\ngiven the input sentence.\\nRNN is necessary when we need to maintain the\\nword order in a sentence. This is not handled by\\nbags of words model or other statistical models. In\\naddition to input, xiand output ˆyi, we also have a\\nstate vector, ai, which is initialized with vectors of\\nzeros.iwould be the ithtimestep In the ﬁrst layer\\nof RNN, the input and state vector is fed into the\\nrecurrent unit, the recurent unit may look like1:\\nat=f(Waaat−1+Waxxi),\\nand,\\nˆyt=g(Wyaat)\\nwhere, t is the time step, Waais the weights be-\\ntween two hidden layer, Waxis the weight be-\\ntween input and hidden layer, and Wyais the\\nweight between hidden and the output layer, and\\nfcan betanh orRelu activation function, and g\\ncan besigmoid orsoftmax activation function.\\nAfter feeding the input to rnn unit, it returns a new\\nstate vector in the next time step. This new state\\nvector will be mapped to the output vector using\\nsome function. This output vector can be used as\\na prediction. The new state vector is cached and is\\npassed across the next unit of the RNN, along with\\nthe input in order for it to return the next state vec-\\ntor. This happens recursively for all the input ele-\\nments. So, when the model is reading the second\\nword, instead of just predicting output using only\\nthe second word, it also gets information from the\\nprevious time step (ﬁrst word) in terms of the state\\nvector.\\nOne of the problem of RNN is that it runs into\\nthe problem of Vaishing gradient, ﬁrst described\\nby Hochreiter (1998). This happens when we have\\na very long sentence, which tends to have long\\nterm dependencies. That means a word at the end\\nof the sentence would be semantically dependent\\non the word occurring at the beginning of the sen-\\ntence. So, the gradients during the backpropaga-\\ntion step would have a very hard time propagating\\nback to affect the words or weights of the earlier\\nunits. The gradients diminishes in the backpropa-\\ngation step and not able to reach the earlier units.\\nGenerally, RNN has local inﬂuences where a word\\nis mainly inﬂuenced by words closed to it. So that\\nmakes it difﬁcult for the output at the later unit\\n1https://www.coursera.org/learn/nlp-sequence-\\nmodels/home/<USER>', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'), Document(id_='27fe23f4-7043-4deb-8421-93014e2dfead', embedding=None, metadata={'page_label': '4', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, excluded_embed_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, text='Figure 1: The left diagram represents Long Short Term Memory Unit, with ias an Input gate, oas an\\nOutput Gate, and fas a Forget Gate. The right diagram represents Gated Recurrent Unit, with ras Reset\\nGate, andzas an Update Gate. (Chung et al., 2014)\\nto be strongly inﬂuenced by a word that was very\\nearly in the sequence.\\nBut we can further improve the training by us-\\ning other RNN units, like GRU (Chung et al.,\\n2014) or LSTM (Hochreiter and Schmidhuber,\\n1997), which are better at capturing long-range de-\\npendencies. The state vector in simple RNN can\\nbe considered as a memory, where the memory ac-\\ncess was not controlled. At each step, the entire\\nmemory state was read and updated. But in GRU\\nand LSTM, we use a gating mechanism to control\\nthe memory. Since, we used GRU in our model,\\nso we will only describe GRU here.\\nIn GRU, (Chung et al., 2014; Rana, 2016), the\\nactivationhj\\ntis a linear interpolation between the\\nprevious activation hj\\nt−1and the candidate ativa-\\ntion˜hj\\nt:\\nhj\\nt= (1−zj\\nt)hj\\nt−1+zj\\nt˜hj\\nt\\nwhere,zj\\ntis the update gate, that decides how\\nmuch GRU units updates its activation [See Fig.\\n1]. The update gate is given by :\\nzj\\nt=σ(Wzxt+Uzht−1)j.\\nAnd the candidate activation ˆhj\\ntis computed by :\\nˆhj\\nt=tanh (Wx t+U(rt⊙ht−1))j,\\nwhere⊙denotes element-wise multiplication and\\nrj\\ntare reset gates. When a reset gate at speciﬁc\\ntime, t is set to 0, i.e. rj\\nt== 0 , which makes the\\nGRU to forget the past, i.e. forget the previousstate vectors. This is considered same as reading\\nthe ﬁrst word of the input sentence. And ﬁnally,\\nwe can compute the reset gate by :\\nrj\\nt=σ(Wrxt+Urht−1)j.\\nOne of the weakness of RNN is that it only uses\\ninformation that is earlier in the sequence to make\\npredictions but not the information which are later\\nin the sequence. When predicting the output at\\ntime step i, it does not use the word at time step i+1\\nor i+2 or any other words in the later time step. So,\\nit would be useful to know not just the information\\nfrom the words from the previous time step but\\nalso the information from the later time steps.\\nSo, we use Bidirectional RNN, ﬁrst proposed\\nby (Schuster and Paliwal, 1997). From a point\\nin time, it takes information from both the ear-\\nlier and later time step in the sequence. The\\nFirst RNN, which we called forward RNN,− →fis\\nfed the input sequence as it is. And the second\\nRNN, which is also called backward RNN,← −f\\nis fed the input sequence in reverse order. This\\ngives two separate state vectors – a forward state\\nvector,− →hT\\nj, and a backward state vector← −hT\\nj.\\n− →hT\\njwould be a sequence of forward hidden state\\nvectors, (− →h1,...,− →hTx), and similarly, backward\\nstate vector← −hT\\njwould be a sequence of backward\\nhidden state vector, (← −h1,...,← −hTx). And the out-\\nput at a speciﬁc timestep is accounted by the con-\\ncatenation of output of two RNN’s, concatenating− →hjand← −hj, i.e.hj= [− →hT\\nj,← −hT\\nj]. So, when pre-\\ndicting the output at a speciﬁc time step, it will', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'), Document(id_='5554f1d1-32b8-4775-b89e-deb2887239d5', embedding=None, metadata={'page_label': '5', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, excluded_embed_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, text='Figure 2: Attention Model (Luong et al., 2015)\\nuse the information from the past, present as well\\nas from the future. We need the entire sequence of\\ndata before we can make any predictions.\\nThe architecture that we are proposing here is\\nbased on the Encoder-Decoder Framework. The\\nencoder takes in the input sentence and converts\\nthem into a vector representation\\nThe encoder can be an RNN (Cho et al., 2014b)\\nor LSTM unit (Sutskever et al., 2014). They pro-\\ncesses the input sentence, pass it through RNN or\\nLSTM, and when it encounters the end of sen-\\ntence, then the hidden state, that captured all the\\nrelevant information passes it to the decoder. Then\\nthis information is used to predict the translations\\nin the decoder, which can be RNN or LSTM, until\\nit predicts the end of the sentence token. The hid-\\nden state needs to remember every word from the\\ninput sentence. So that is why this model tends to\\nwork for short sentences and not long sentences.\\nEven though if we used LSTM or GRU, which\\ntends to remember the words that occured very\\nearly in the sequence, it will still not be able to\\nlearn the alignment between the source word and\\nthe target word. They often forget the initial part\\nof the sentence once they are processed in the en-\\ncoder. That is why we use an alignment mecha-\\nnism called attention. They help to memorize this\\ninformation for longer sentences. But we need to\\nlearn this alignment. It can vary from language to\\nlanguage.3.2 Attention\\nIn this section, we will speciﬁcally deﬁne the\\nalignment mechanism, [See Figure 2] that we used\\nin our model. In RNN Encoder-Decoder model\\n(Sutskever et al., 2014), we faced with the bottle-\\nneck problem, where the complete sequence of in-\\nformation of the source sentence, must be captured\\nby one single vector, i.e. the last hidden unit of the\\nencoder RNN is used as a context vector for the\\ndecoder, which becomes difﬁcult for the decoder\\nto summarise large input sequence at once. This\\nalso poses a problem where the encoder is not able\\nto memorize the words coming at the beginning\\nof the sentences, which leads to poor translation\\nof the source sentence. The Attention mechanism\\njust addresses this issue, by retaining and utilising\\nall the hidden state of the input sentence during the\\ndecoding phase.\\nDuring the decoding phase, the model creates\\nan alignment between each time step of the de-\\ncoder output and all of the encoder hidden state.\\nWe need to learn this alignment. Each output of\\nthe decoder can selectively pick out speciﬁc ele-\\nments from the sequence to produce the output.\\nSo, this allows the model to focus and pay more\\n”Attention” to the relevant part of the input se-\\nquence.\\nThe ﬁrst attention model was proposed by Bah-\\ndanau et al. (2014), there are several other types of\\nattention proposed, such as the one by Luong et al.', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'), Document(id_='b39f5904-404c-4387-8eef-b132ce88f0fe', embedding=None, metadata={'page_label': '6', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, excluded_embed_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, text='Spanish en la estrategia 2020 , reconocimos el hecho de que , si queremos mantener nuestro\\nnivel de prosperidad en europa , tenemos que aumentar nuestra productividad .\\nEnglish in the 2020 strategy , we acknowledged the fact that , if we are to maintain our level\\nof prosperity in europe , we need to increase our productivity .\\nSpanish sin embargo , es algo que debemos hacer si queremos demostrar a los estados unidos\\nque nos deben considerar como un socio serio en la alianza contra el terrorismo .\\nEnglish yet do it we must , if we are to demonstrate to the usa that we are to be taken\\nseriously as a partner in an alliance against terrorism .\\nSpanish sabr´an ustedes que fue tambi ´en a instancias de esta c ´amara que la comisi ´on entabl ´o\\nnegociaciones , y estas han dado un resultado encomiable .\\nEnglish you will be aware that it was not least at the insistence of this house that the com-\\nmission entered into negotiations , and these have produced a creditable result .\\nTable 1: Examples of Spanish and English sentences from the dataset\\n(2015).\\nWe will only discuss the attention model, pro-\\nposed by Bahdanau et al. (2014). After the in-\\nput sequence is passed through the encoder, it pro-\\nduces hidden state for each of the elements in the\\nsequence (h1,...,h Tx). Then we multiply the de-\\ncoders hidden state at time step t (s1,...,s Ty),\\nwith all of the encoders hidden state, which gives\\nus the alignment score of each of the encoder out-\\nput with respect to the decoder input and hidden\\nstate at that time step:\\net= [sT\\nth1,...,sT\\nthTx]\\nThe alignment score quantiﬁes the amount of\\nAttention the decoder will place on each of the en-\\ncoder outputs when producing the next output, so\\ninstead of looking at the entire sequence, it just\\nconcentrate on few relevant parts of the sequence\\nwhen predicting the next word.\\nAfter calculating the alignment score, we pass\\nthe vectoretthrough the softmax layer, to calcu-\\nlate the probability distribution.\\nαt=softmax (et)\\nThen we multiply each of the attention weights\\nwith each of the encoder hidden state, to get con-\\ntext vector,at\\nat=Tx∑\\ni=1αt\\nihi\\nIf the attention score of speciﬁc element of the\\ninput sequence is close to 1, then its inﬂuence on\\nthe decoder output at that speciﬁc time step in-\\ncreases. And then ﬁnally, the context vector atproduced will be concatenated with the decoder\\nhidden state, st, i.e.\\n˜ht= [at,st]\\nand is fed into decoder RNN, which produces new\\nhidden state.\\n4 Data\\nWe used a Parallel Corpus for English-Spanish\\nlanguage, which was extracted from the proceed-\\nings of the European Parliament, also called Eu-\\nroparl dataset (Koehn, 2005). It contains 1.96\\nMillion sentences, each for English and Spanish.\\nMost common words and count for both languages\\nare shown below:\\nWords Count\\nde 1799827\\n, 1456229\\nla 1222089\\nque 992176\\n. 867284\\nen 790382\\nel 696521\\ny 692640\\na 577052\\nlos 548495\\nTable 2: SpanishWords Count\\nthe 1956558\\n, 1371506\\nof 932044\\nto 875415\\n. 864674\\nand 747108\\nin 622426\\nthat 476250\\na 430093\\nis 401782\\nTable 3: English\\nFirst we ﬁlter out all the sentences having words\\ngreater than 50. Then we sort these sentences\\nbased on the number of words in each of the sen-\\ntences, so that we have less padded sentences in\\nour initial indices and sentences with high padding\\nto be at the end of our indices, following with', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'), Document(id_='b06155eb-33c4-4950-baae-cb2612243111', embedding=None, metadata={'page_label': '7', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, excluded_embed_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, text='usual tokenization methods. Only preprocessing\\nwe used was to lower case the words.\\nWe selected 1 million sentences for the model-\\ning due to the hardware constraints. We split the\\ndataset into usual format, i.e. train, test and vali-\\ndation. We used 900K sentences for our training,\\n80K for validation and remaining 20K sentences\\nfor the test set, which was not seen by the model\\nduring training. We did not limit the vocabulary\\nsize to any hard coded number, i.e. to get top N\\nmost frequent words. The vocabulary size we got\\nfrom English sentences was 36838 and for Span-\\nish was 63220. Only token we added was End of\\nSentence and Start of Sentence Tokens. We used\\nSpanish as a source sentence and English as a tar-\\nget sentence. See Table 1.\\n5 Experiments\\nThe encoder and decoder of our model have 256\\nhidden units each. The encoder consist of for-\\nward and backward gated recurrent unit (GRU)\\neach having 256 hidden units. The decoder has\\na single forward gated recurrent unit (GRU), with\\n256 hidden dimensions, unlike 1000 hidden units,\\nas in Bahdanau et al. (2014) due to the hardware\\nlimitations. And we only trained the model for\\nSpanish to English translation.\\nWe used Adam optimizer to train the model, and\\ngradient update is computed with a batch size of\\n32 sentences.\\nWe initialized our weights with xavier (Glo-\\nrot) initializations, (Glorot and Bengio, 2010) with\\nUniform Distribution, U[−a,a], where\\na=√\\n6\\nnin+nout\\nwhereninis the number of input neurons in the\\nweight tensor, and noutis the number of output\\nneurons in the weight tensor.\\nThe total number of trainable parameters were\\n43,564,519. We trained the model for roughly 20\\nhours. After our model was trained, we use greedy\\nsearch to predict the translation for the given input\\nsentence, that maximizes the conditional proba-\\nbility instead of using beam search as mentioned\\nin the paper Bahdanau et al. (2014) due to our\\nunfamiliarity and technical difﬁculty dealing with\\nBeam search.\\nWe, then used BLEU (Papineni et al., 2002)\\nscore to evaluate how the model was working on\\nthe test data.6 Results\\n6.1 Quantitative Results\\nWe trained our model on maximum length of 50\\nsentences, so any length smaller than or equal to\\n50 is used for the training, We trained our model\\nuntil the error on the validation data or the devel-\\nopment data stops decreasing, in order to avoid\\nthe problem of overﬁtting. We achieved a BLUE\\nscore of 25.76on the test data and the error rate for\\nSpanish to English translation was 4.267 on our\\ntest data. According to Bahdanau et al. (2014), we\\ncan say that our model out perform the encoder\\ndecoder model, proposed by Cho et al. (2014b),\\nfor 50 sentences, where they got BLEU score of\\n17.82. Their performance drops when the length\\nof the sentence is increased (Cho et al., 2014b).\\nSo, the limitation of using ﬁxed length vector\\nin simple encoder decoder model in Cho et al.\\n(2014b) work was the reason that it was under per-\\nforming with long sentences.\\nThis was our motivation to use the proposed ap-\\nproach by Bahdanau et al. (2014), where the per-\\nformance of the encoder-decoder with attention\\nshows no deterioration with sentence of length\\ngreater than 50 sentences. The result that we\\ngot which was 25.76 was quite close to the Bah-\\ndanau et al. (2014), where they got BLEU score of\\n26.75,training with 1000 encoder and decoder di-\\nmensions, and training on corpus of 384M words.\\nThey were also able to achieve BLEU score of\\n28.45 when trained the data until the performance\\nof the validation stopped improving.\\n6.2 Qualitative Results\\nThe model proposed by Bahdanau et al. (2014)\\nprovides a way to investigate the soft alignment\\nbetween the translated sentence from the model\\nand the input sentence. The matrix given in Fig\\n3, each of the cells represent the weights αijof\\nthe annotation of the j-th source word for the i-\\nth target word. This helps in visualizing and see\\nwhich word from the input sentence were con-\\nsidered more important for generating the target\\nword.\\nWe see that majority of the weights are con-\\ncentrated on the diagonal matrix, along with non-\\nmonotonic alignments. The non-monotonic align-\\nments would be high for long sentences, since the\\nwords in long target sentence tends to have depen-\\ndence on more than one word in source sentence.', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'), Document(id_='9d1d4f83-96a0-4084-b8f5-545a30b1b311', embedding=None, metadata={'page_label': '8', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, excluded_embed_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, text='(a)\\n (b)\\n(c)\\n (d)\\nFigure 3: Alignments translated from Spanish to English by our model. The row represents the translated\\nsentence, English and the column represents the source sentence, Spanish. Each of the cells of the matrix\\nrepresents weights, αij, of the annotation of the j-th source word for the i-th translated word. (1:White,\\n0:Black)', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'), Document(id_='8a103d8e-e274-424d-a640-84c93966374d', embedding=None, metadata={'page_label': '9', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, excluded_embed_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, text='Let us take an example from the test set, con-\\nsider the source sentence:\\nson los estados miembros de la zona del\\neuro los que no han cumplido , en espe-\\ncialla republica federal de alemania que\\nse niega a mantener su promesa.\\nAnd its translation by our model is :\\nthat it is the member states of the euro\\narea which have not complied honour,\\nparticularly the federal republic of ger-\\nmany that refuses to keep their promise\\nto sustain their promise.\\nAnd the reference is:\\nit is the member states of the euro area\\nthat have not delivered - and particu-\\nlarly the federal republic of germany,\\nwhich is refusing to keep its promise.\\nWe can observe from our translated sentence,\\nthe model generates ”have not complied ” , instead\\nof”have not delivered” (from reference sentence),\\nwhich has the same meaning. It tries to preserve\\nthe meaning of the whole sentence, and it does not\\nblindly takes the word from the reference, it tries\\nto generalize.\\nRefer to Table 4 at the end of the paper for\\nmore translations from Spanish to English using\\nEncoder Decoder model with Attention Mecha-\\nnism.\\n7 Discussion\\nAfter trying to achieve the result similar to what\\nwas presented in the paper, we are satisﬁed with\\nour result though there is much that can be im-\\nproved. Limitation caused by the hardware held\\nus back from achieving better results. We used the\\nserver provided by the university and as a backup\\nused google colab for our work, so we had to be\\nwary of the maintenance schedule happening in\\nthe server and the limitation of 24 hrs of work-\\ntime on google colab, which otherwise could in-\\nterrupt while we were training our model. So,\\nto overcome these challenges we decided to use\\n1M sentences from each form Spanish and English\\ndataset, and reduce the parameters for encoder-\\ndecoder.\\nWe also faced problem with length of the vo-\\ncabulary size of English and Spanish sentences,\\nwhere the vocabulary size of the English sen-\\ntence were the output dimension, and vocabularysize of Spanish sentence were input dimensions of\\nour model. This leads to increase in number of\\ntrainable parameters, the decoding complexity in-\\ncreases with number of target words (vocabulary\\nsize of the English), where this problem has been\\naddressed by Jean et al. (2015).\\n8 Conclusion\\nThe approach proposed by Cho et al. (2014b) was\\nto encode the whole sentence into ﬁxed length vec-\\ntor, and this becomes problematic when dealing\\nwith long sentences. We extend this basic encoder-\\ndecoder model by an Attention mechanism (Bah-\\ndanau et al., 2014), where the model searches for\\nthe input word computed from the encoder, which\\nbest align with the target word, when generat-\\ning each target word. This frees the model from\\nhaving to encode the source sentence into a ﬁxed\\nlength vector, only rely on the information rele-\\nvant for generating each target word. We com-\\npared our model for Spanish to English translation\\nwith both of these approaches, and found that our\\nmodel works better than the encoder-decoder ap-\\nproach (Cho et al., 2014b), and have slightly lower\\nresults than the architecture with Attention mech-\\nanism, (Bahdanau et al., 2014). We also observed\\nthat the model tries to align the target word with\\nthe relevant word from the translated sentence.\\nIn the future work, there are several things we\\ncan try. We can train our model on much larger\\ndataset, with a better hardware and with differ-\\nent attention models. We can also focus on how\\nto handle the stop words, punctuation, as we can\\nsee on Table 2 and Table 3, which accounts for\\nthe highest word count, and also to handle the un-\\nknown words, which does not appear in the train-\\ning data, but in the test data.\\nReferences\\nDzmitry Bahdanau, Kyunghyun Cho, and Yoshua\\nBengio. Neural machine translation by jointly\\nlearning to align and translate. arXiv preprint\\narXiv:1409.0473 , 2014.\\nKyunghyun Cho, B. V . Merrienboer, Dzmitry\\nBahdanau, and Yoshua Bengio. On the\\nproperties of neural machine translation: En-\\ncoder–decoder approaches. In SSST@EMNLP ,\\n2014a.\\nKyunghyun Cho, B. V . Merrienboer, C ¸ aglar\\nG¨ulc ¸ehre, Dzmitry Bahdanau, Fethi Bougares,', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'), Document(id_='f8e3e207-3382-412a-b8e8-ec2b8ebf5f59', embedding=None, metadata={'page_label': '10', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, excluded_embed_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, text='Holger Schwenk, and Yoshua Bengio. Learn-\\ning phrase representations using rnn en-\\ncoder–decoder for statistical machine transla-\\ntion. In EMNLP , 2014b.\\nJ. Chung, C ¸ aglar G ¨ulc ¸ehre, Kyunghyun Cho, and\\nYoshua Bengio. Empirical evaluation of gated\\nrecurrent neural networks on sequence model-\\ning.ArXiv , abs/1412.3555, 2014.\\nXavier Glorot and Yoshua Bengio. Understanding\\nthe difﬁculty of training deep feedforward neu-\\nral networks. In AISTATS , 2010.\\nS. Hochreiter. The vanishing gradient problem\\nduring learning recurrent neural nets and prob-\\nlem solutions. Int. J. Uncertain. Fuzziness\\nKnowl. Based Syst. , 6:107–116, 1998.\\nS. Hochreiter and J. Schmidhuber. Long short-\\nterm memory. Neural Computation , 9:1735–\\n1780, 1997.\\nS´ebastien Jean, Kyunghyun Cho, R. Memisevic,\\nand Yoshua Bengio. On using very large tar-\\nget vocabulary for neural machine translation.\\nArXiv , abs/1412.2007, 2015.\\nMarcin Junczys-Dowmunt, Tomasz Dwojak, and\\nHieu Hoang. Is neural machine translation\\nready for deployment? a case study on 30 trans-\\nlation directions. 01 2016.\\nPhilipp Koehn. Europarl: A parallel corpus for\\nstatistical machine translation. In MTSUMMIT ,\\n2005.\\nPhilipp Koehn, Franz Josef Och, and Daniel\\nMarcu. Statistical phrase-based translation.\\nInProceedings of the 2003 Conference of the\\nNorth American Chapter of the Association\\nfor Computational Linguistics on Human Lan-\\nguage Technology - Volume 1 , NAACL ’03,\\npage 48–54, USA, 2003. Association for Com-\\nputational Linguistics. doi: 10.3115/1073445.\\n1073462. URL https://doi.org/10.\\n3115/1073445.1073462 .\\nThang Luong, Hieu Pham, and Christopher D.\\nManning. Effective approaches to attention-\\nbased neural machine translation. In EMNLP ,\\n2015.\\nKishore Papineni, S. Roukos, T. Ward, and Wei-\\nJing Zhu. Bleu: a method for automatic evalua-\\ntion of machine translation. In ACL, 2002.\\nR. Rana. Gated recurrent unit (gru) for emo-\\ntion classiﬁcation from noisy speech. ArXiv ,\\nabs/1612.07778, 2016.M. Schuster and K. Paliwal. Bidirectional recur-\\nrent neural networks. IEEE Trans. Signal Pro-\\ncess. , 45:2673–2681, 1997.\\nXuewen Shi, Heyan Huang, Ping Jian, and Yi-Kun\\nTang. Improving neural machine translation\\nwith sentence alignment learning. Neurocom-\\nputing , 420:15–26, 2021. ISSN 0925-2312. doi:\\nhttps://doi.org/10.1016/j.neucom.2020.05.104.\\nIlya Sutskever, Oriol Vinyals, and Quoc V . Le.\\nSequence to sequence learning with neural net-\\nworks. In NIPS , 2014.\\nShuangzhi Wu, Dongdong Zhang, Zhirui Zhang,\\nNan Yang, Mu Li, and M. Zhou. Dependency-\\nto-dependency neural machine translation.\\nIEEE/ACM Transactions on Audio, Speech, and\\nLanguage Processing , 26:2132–2141, 2018.', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'), Document(id_='0d0994f8-af26-44a1-8e53-5456643af9ef', embedding=None, metadata={'page_label': '11', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, excluded_embed_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, text='Source se˜nor presidente , se ˜nor presidente en ejercicio del consejo , se ˜nor presidente de la\\ncomisi ´on , se ˜nor´ıas , me gustar ´ıa hacer tres breves comentarios .\\nReference mr president , mr president - in - ofﬁce of the council , mr president of the com-\\nmission , ladies and gentlemen , i would just like to make three brief comments\\n.\\nOur Model president mr president , mr president - in - ofﬁce of the council , mr president of\\nthe commission , ladies and gentlemen , i would like to make three brief comments\\nbrief comments .\\nGoogle Translate Mr. Chairman, Mr. Chairman-in-Ofﬁce of the Council, Mr. Chairman of the Com-\\nmittee, ladies and gentlemen, I would like to make three brief comments.\\nSource como los estados miembros , la comisi ´on procura promover el estado de derecho ,\\nsin el cual los derechos humanos no obtendr ´an reconocimiento en ning ´un territorio\\n.\\nReference the commission is involved , as are member states , in the promotion of the rule of\\nlaw , without which human rights can not operate in any territory .\\nOur Model that as the member states , the commission intends to promote the rule of law ,\\nwithout human rights will not not be any recognition in any territory in any territory\\n.\\nGoogle Translate Like the member states, the commission seeks to promote the rule of law, without\\nwhich human rights will not be recognized in any territory.\\nSource por escrito . - he votado a favor del informe de la se ˜nora fraga , que permite a\\ngroenlandia exportar productos pesqueros a la ue a pesar de no ser miembro .\\nReference in writing . - i voted in favour of ms fraga ’s report , which allows greenland to\\nexport ﬁshery products to the eu despite not being a member .\\nOur Model in writing . - i voted in favour of mrs fraga est ´evez ’s report , which allows greenland\\nexport export to export to the eu despite despite not being member .\\nGoogle Translate written . - i voted in favor of the report by mrs fraga, which allows greenland to\\nexport ﬁshery products to the eu despite not being a member.\\nSource ( pl ) se ˜nor presidente , me gustar ´ıa una vez m ´as manifestar mi satisfacci ´on por la\\nimportancia que la comunidad conﬁere a la necesidad de innovaci ´on en europa .\\nReference ( pl ) mr president , i would like once again to express my pleasure at the importance\\nthat the community attaches to the need for innovation in europe .\\nOur Model that ( pl ) mr president , i would once again like to express my satisfaction satis-\\nfaction that the community attaches to the community to the need for innovation in\\neurope .\\nGoogle Translate (pl) mr president, i would like once again to express my satisfaction with the impor-\\ntance that the community attaches to the need for innovation in europe.\\nSource son los estados miembros de la zona del euro los que no han cumplido , en especial\\nla rep ´ublica federal de alemania que se niega a mantener su promesa .\\nReference it is the member states of the euro area that have not delivered - and particularly the\\nfederal republic of germany , which is refusing to keep its promise .\\nOur Model that it is the member states of the euro area which have not complied honour ,\\nparticularly the federal republic of germany that refuses to keep their promise to\\nsustain their promise .\\nGoogle Translate it is the eurozone member states that have not delivered, especially the federal re-\\npublic of germany which refuses to keep its promise.\\nTable 4: Source and Reference form the test data, with translated sentence from our model along with\\nGoogle translation (as of 16 August 2021)\\nView publication stats', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n')]\n"]}]}, {"cell_type": "code", "source": ["# setup prompts - specific to StableLM\n", "from llama_index.core import PromptTemplate\n", "\n", "system_prompt = \"\"\"<|SYSTEM|># You are a Q&A assistant. Your goal is to answer questions as\n", "accurately as possible based on the instructions and context provided.\n", "\"\"\"\n", "\n", "# This will wrap the default prompts that are internal to llama-index\n", "query_wrapper_prompt = PromptTemplate(\"<|USER|>{query_str}<|ASSISTANT|>\")"], "metadata": {"id": "a5X17y7FyWlF"}, "execution_count": 8, "outputs": []}, {"cell_type": "markdown", "source": ["https://github.com/run-llama/llama_index/blob/main/llama-index-integrations/llms/llama-index-llms-huggingface/llama_index/llms/huggingface/base.py"], "metadata": {"id": "nOTyc8jS0XYT"}}, {"cell_type": "code", "source": ["import torch\n", "\n", "llm = HuggingFaceLLM(\n", "    context_window=4096,\n", "    max_new_tokens=256,\n", "    generate_kwargs={\"temperature\": 0.7, \"do_sample\": False},\n", "    system_prompt=system_prompt,\n", "    query_wrapper_prompt=query_wrapper_prompt,\n", "    tokenizer_name=\"mistralai/Mistral-7B-Instruct-v0.1\",\n", "    model_name=\"mistralai/Mistral-7B-Instruct-v0.1\",\n", "    device_map=\"auto\",\n", "    stopping_ids=[50278, 50279, 50277, 1, 0],\n", "    tokenizer_kwargs={\"max_length\": 4096},\n", "    # uncomment this if using CUDA to reduce memory usage\n", "    model_kwargs={\"torch_dtype\": torch.float16}\n", ")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 511, "referenced_widgets": ["dd5cbd1a967c43cd8520fff6227a41b5", "8444d16768d8412db16f592c663ac2ee", "9bf68b229b554df1827b74efaffbbb5e", "1293aad1760d4bf68e58cf5d9888df78", "b8407a3d1ce845ce9183f378daf35c18", "5d372cc0a3294f21959367ae2408dfd6", "8983e11d5a2d4c6d986d7133877f5298", "b418f4a18d594d688b30095d6c307289", "a5fdf629d03f423ab810a33863b756a7", "695fbba5151e493ebd8e5637db31f18d", "8aaa86a36a084b979e8de9c19295473d", "492fbd0296944481987615718e8d9d9b", "1e0dd6926dd64f12ad95b17202b9dbaa", "79a2d832f73a4f1f9058de2b5489f07e", "13656119cab642f1979ecb7a6a224dec", "28ca702f4f604cc482dae607b828e917", "c2c5810a3c14495f91499b2d6b3c4627", "b61a39f8ce064b62a328d7da0823abb7", "22cbe78cd06d431688e7c44a3a790b79", "f2f3587adfbc44e5ab32f6c13c81f631", "afc409c61ddb4ea3abff15438b1c91c2", "7e96f952ee82402b903a1f2c23c8cf96", "cb50e138a59d4bdaab38a2ef6ea02339", "fe9526f59e774754a2ad4176d3db3c8c", "2fe02108b8ba458285603f50cda06c23", "2e4fdb1949b2498888c4a41c2f0a4d8e", "ee2acada158b4e03ad3b1c91667b73c8", "1e8dfc158ecf4706af5ccda8c4940b88", "3d25b560105344e0b2e55bea057c1459", "0968757ddc9c476f85311f7826b015ef", "b8404f1636d24c93831331f2ded431d8", "c85467854127467ba34e3c2a491d373c", "eafc35172453428bb6f9a7ed738be365", "6686977b63404f6db04e5586b6191e04", "b10835c6cfe44fb3b06c6a8a426f95da", "c987da924207433da56cd7180a1e3638", "d87dbe1bc9f9437db5db7b0346c7604d", "3db55bbcd3964060be33c17e768baf2a", "519ea56649674f678fa1294da9b7e4f5", "9f0b688cfc6647eda521ce087ef81667", "e955c27a42374a8a8d363fa77200cfa9", "67c028c10ce64914b83cf294375a4071", "26b14d42b2a744c38a3bc94150c8e36a", "c3b84d376faf4e6a8cd2f800be94783a", "2e645764024f44b197a15eb40f68aa52", "e45f5736462f472088e63e59f518f789", "50224ccf914d435c86f328fd04695022", "614e3411b6084ad69f72fbc047f60869", "abc08239af24459b95f7aa9be86da030", "e6fdd9c2150144098b7178d362b83b10", "977a192392194c3ba2dac845d940a92f", "1138b12d28a34eabbfb73030fc3055ca", "3a2c956b8eac434b91711f13da391d4f", "1720c99486e84def869dcd57197a443b", "19b34ddde5054448b9693b8bba725dc1", "e59a1f02c2c64c82a98ed4ec8126ac17", "0f76a73f9f4f4d73abbb23755d1d817f", "da756ae49de344eab2163133df806531", "583dbec04078405abd29cc56cb3a11a0", "226ea5b4838f45e2a24c2efb1849e34c", "a3ad56dc686b457b91daae39dca24835", "2dc829c8294a4c1491ff5d56364b72f6", "f25f2cef80c64cd0b40fe6d8dc09f5c8", "b8aa3c7b39494e95b7a710a742b5dbf9", "873b746879ee4a2eaa97e0c54e9a126b", "1797ce9fd78142c8b18a08716f4e7d28", "6870db13150147aabd935aa70ec350f1", "fd1cb02b527944eaab2783a99294d172", "217e56acadab4417b3a44eb8145732d5", "0513257d81154388870ffc7eab2317b1", "aa09362e2f3140d08c74dac129f170c3", "a3131bf61b7c4c5c80b5ade0d960cd1f", "325e80a4379240688d632ba3cad2e6aa", "33d52b220a3f4d90a340cffb51cb8333", "4b7e966f1ecd4b4f9e7bb5c55b7e1a7d", "29ae8e293137446fbea1384cdc78bfb9", "9f621c6a68394952963c27f049e195ce", "ac8fb708a6334bb3989c8dcdb9522b49", "33f55a66a8a84bf5a82662c15413103c", "f67a8d5b90b641e8ac3187c6831de143", "e22e792c0616459a97d62940fd15e669", "ee1f169d0091425286f200524a780a12", "aa01c4758dbf45f7959a2f3490ad2037", "b64410fdc9b049f9b996d54b3b5dec82", "77b8d398a61148f689b576a823f9f1f2", "4cf031673e284e54bcbc3ccf5b9e3868", "1b7f4d24fdd5473fba8e564452139e92", "5c395df37f6d4b049ce4c7c1a6b6eac6", "07cf3c551f3b4ca3a63f0b3e7501cd67", "c26643d93235443ea99f59a77f1c7b0a", "e87dcccfb96f47ba9099622c7b2d8a51", "379eb11fc5814f8799b8f45eeba8072c", "5eab37bc6617483ca2744c790e13d87d", "aa631f6a79ab4c2c9e2b11cc356a5633", "cf418f17a87744449c86c3b3db57dcad", "880554b7a0404dca997ce1d94da9c97f", "f580ddc5778649c2be1648cfdefe1b61", "b31241c7e9d341e7a61483d9aa47c898", "3a78b7b56320489aabb947d86b933f0c", "946da7977691449ca379b06a315334d5", "9b5c5c479edc49579fb61c406aea54bd", "80768bfd36b045fcaaf026d7047e3a44", "184e6a7917b14c6eb8edcf03288940f9", "225ad10f0d1048bba62990287fdb8f76", "a958a8da218a4a8b9ade2f643d293a78", "45e72108de31456a909f038b2e7487c3", "098cadbfe744497f91f54ad42c38f3b7", "b3642d7f5e8c48e7a1fb39822f1d1c2d", "588d0237a1774ab494c46ec13b7ab578", "9f734da521f74696a51e6ecf1420823f", "b56d4ded73cf4630afd9b6cbde49e0d5", "4f6b296e5ae2415592250b60d10a4a24", "f714e4af7d91463a8a228ede9a7f18c1", "2f583b4ef0bd47628b9145ef7f60e387", "a4b3af280d9845e291e621eafc51ab8c", "ce663a1b3d7c4d11ab52b813cb0a0f3a", "3b14dd425ff645c3a3b2f757e74990ef", "5c32940ce356438da6ec2bd7f42e7d47", "ebb47d7a41e3488cb7d7278497d8ee8c", "c9675199f6fb4fd3b28d49827299409c", "b5a4f9ddbf1a4dd9999c208261bde0ad"]}, "id": "F7RpE_7PzHIV", "outputId": "05315c69-eb1d-4204-bd4c-9ad31fc64dd2"}, "execution_count": 9, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/huggingface_hub/utils/_token.py:88: UserWarning: \n", "The secret `HF_TOKEN` does not exist in your Colab secrets.\n", "To authenticate with the Hugging Face Hub, create a token in your settings tab (https://huggingface.co/settings/tokens), set it as secret in your Google Colab and restart your session.\n", "You will be able to reuse this secret in all of your notebooks.\n", "Please note that authentication is recommended but still optional to access public models or datasets.\n", "  warnings.warn(\n"]}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/571 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "dd5cbd1a967c43cd8520fff6227a41b5"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors.index.json:   0%|          | 0.00/25.1k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "492fbd0296944481987615718e8d9d9b"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading shards:   0%|          | 0/2 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "cb50e138a59d4bdaab38a2ef6ea02339"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model-00001-of-00002.safetensors:   0%|          | 0.00/9.94G [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "6686977b63404f6db04e5586b6191e04"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model-00002-of-00002.safetensors:   0%|          | 0.00/4.54G [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "2e645764024f44b197a15eb40f68aa52"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "e59a1f02c2c64c82a98ed4ec8126ac17"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["generation_config.json:   0%|          | 0.00/116 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "6870db13150147aabd935aa70ec350f1"}}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["WARNING:root:Some parameters are on the meta device device because they were offloaded to the cpu.\n"]}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json:   0%|          | 0.00/1.47k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "ac8fb708a6334bb3989c8dcdb9522b49"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.model:   0%|          | 0.00/493k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "07cf3c551f3b4ca3a63f0b3e7501cd67"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.json:   0%|          | 0.00/1.80M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "946da7977691449ca379b06a315334d5"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["special_tokens_map.json:   0%|          | 0.00/72.0 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "b56d4ded73cf4630afd9b6cbde49e0d5"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["%pip install llama-index-embeddings-huggingface\n", "%pip install llama-index-embeddings-instructor"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FjgUy4py0aGU", "outputId": "e35875f7-609d-4eb3-81d4-07fbc080199a"}, "execution_count": 10, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting llama-index-embeddings-huggingface\n", "  Downloading llama_index_embeddings_huggingface-0.2.0-py3-none-any.whl (7.1 kB)\n", "Requirement already satisfied: huggingface-hub[inference]>=0.19.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-embeddings-huggingface) (0.20.3)\n", "Requirement already satisfied: llama-index-core<0.11.0,>=0.10.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-embeddings-huggingface) (0.10.28)\n", "Collecting sentence-transformers<3.0.0,>=2.6.1 (from llama-index-embeddings-huggingface)\n", "  Downloading sentence_transformers-2.6.1-py3-none-any.whl (163 kB)\n", "\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/163.3 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m163.3/163.3 kB\u001b[0m \u001b[31m4.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (3.13.3)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (2023.6.0)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (2.31.0)\n", "Requirement already satisfied: tqdm>=4.42.1 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (4.66.2)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (6.0.1)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (4.10.0)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (24.0)\n", "Requirement already satisfied: aiohttp in /usr/local/lib/python3.10/dist-packages (from huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (3.9.3)\n", "Requirement already satisfied: pydantic<3.0,>1.1 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (2.6.4)\n", "Requirement already satisfied: SQLAlchemy[asyncio]>=1.4.49 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (2.0.29)\n", "Requirement already satisfied: dataclasses-json in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (0.6.4)\n", "Requirement already satisfied: deprecated>=1.2.9.3 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (1.2.14)\n", "Requirement already satisfied: <PERSON><PERSON><PERSON><2.0.0,>=1.0.8 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (1.0.8)\n", "Requirement already satisfied: httpx in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (0.27.0)\n", "Requirement already satisfied: llamaindex-py-client<0.2.0,>=0.1.16 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (0.1.16)\n", "Requirement already satisfied: nest-asyncio<2.0.0,>=1.5.8 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (1.6.0)\n", "Requirement already satisfied: networkx>=3.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (3.2.1)\n", "Requirement already satisfied: nltk<4.0.0,>=3.8.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (3.8.1)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (1.25.2)\n", "Requirement already satisfied: openai>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (1.16.2)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (2.0.3)\n", "Requirement already satisfied: pillow>=9.0.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (9.4.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.2.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (8.2.3)\n", "Requirement already satisfied: tiktoken>=0.3.3 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (0.6.0)\n", "Requirement already satisfied: typing-inspect>=0.8.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (0.9.0)\n", "Requirement already satisfied: wrapt in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (1.14.1)\n", "Requirement already satisfied: transformers<5.0.0,>=4.32.0 in /usr/local/lib/python3.10/dist-packages (from sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (4.38.2)\n", "Requirement already satisfied: torch>=1.11.0 in /usr/local/lib/python3.10/dist-packages (from sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (2.2.1+cu121)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.10/dist-packages (from sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (1.2.2)\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.10/dist-packages (from sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (1.11.4)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (4.0.3)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (2024.2.2)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (1.0.5)\n", "Requirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (3.6)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (0.14.0)\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (8.1.7)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (1.3.2)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (2023.12.25)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai>=1.1.0->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (1.7.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0,>1.1->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.16.3 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0,>1.1->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (2.16.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (2.0.7)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy[asyncio]>=1.4.49->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (3.0.3)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (1.12)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (3.1.3)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (12.1.105)\n", "Requirement already satisfied: nvidia-cudnn-cu12==******** in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (********)\n", "Requirement already satisfied: nvidia-cublas-cu12==******** in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (********)\n", "Requirement already satisfied: nvidia-cufft-cu12==********* in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (*********)\n", "Requirement already satisfied: nvidia-curand-cu12==********** in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (**********)\n", "Requirement already satisfied: nvidia-cusolver-cu12==********** in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (**********)\n", "Requirement already satisfied: nvidia-cusparse-cu12==********** in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (**********)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.19.3 in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (2.19.3)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (12.1.105)\n", "Requirement already satisfied: triton==2.2.0 in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (2.2.0)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12 in /usr/local/lib/python3.10/dist-packages (from nvidia-cusolver-cu12==**********->torch>=1.11.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (12.4.127)\n", "Requirement already satisfied: tokenizers<0.19,>=0.14 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.32.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (0.15.2)\n", "Requirement already satisfied: safetensors>=0.4.1 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.32.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (0.4.2)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from typing-inspect>=0.8.0->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (1.0.0)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (3.21.1)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (2024.1)\n", "Requirement already satisfied: threadpoolctl>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from scikit-learn->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (3.4.0)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (1.2.0)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.8.2->pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-huggingface) (1.16.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch>=1.11.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (2.1.5)\n", "Requirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->torch>=1.11.0->sentence-transformers<3.0.0,>=2.6.1->llama-index-embeddings-huggingface) (1.3.0)\n", "Installing collected packages: sentence-transformers, llama-index-embeddings-huggingface\n", "Successfully installed llama-index-embeddings-huggingface-0.2.0 sentence-transformers-2.6.1\n", "Collecting llama-index-embeddings-instructor\n", "  Downloading llama_index_embeddings_instructor-0.1.3-py3-none-any.whl (3.6 kB)\n", "Collecting instructorembedding<2.0.0,>=1.0.1 (from llama-index-embeddings-instructor)\n", "  Downloading InstructorEmbedding-1.0.1-py2.py3-none-any.whl (19 kB)\n", "Requirement already satisfied: llama-index-core<0.11.0,>=0.10.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-embeddings-instructor) (0.10.28)\n", "Requirement already satisfied: sentence-transformers<3.0.0,>=2.2.2 in /usr/local/lib/python3.10/dist-packages (from llama-index-embeddings-instructor) (2.6.1)\n", "Requirement already satisfied: torch<3.0.0,>=2.1.2 in /usr/local/lib/python3.10/dist-packages (from llama-index-embeddings-instructor) (2.2.1+cu121)\n", "Requirement already satisfied: PyYAML>=6.0.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy[asyncio]>=1.4.49 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (2.0.29)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.6 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (3.9.3)\n", "Requirement already satisfied: dataclasses-json in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (0.6.4)\n", "Requirement already satisfied: deprecated>=1.2.9.3 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (1.2.14)\n", "Requirement already satisfied: <PERSON><PERSON><PERSON><2.0.0,>=1.0.8 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (1.0.8)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (2023.6.0)\n", "Requirement already satisfied: httpx in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (0.27.0)\n", "Requirement already satisfied: llamaindex-py-client<0.2.0,>=0.1.16 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (0.1.16)\n", "Requirement already satisfied: nest-asyncio<2.0.0,>=1.5.8 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (1.6.0)\n", "Requirement already satisfied: networkx>=3.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (3.2.1)\n", "Requirement already satisfied: nltk<4.0.0,>=3.8.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (3.8.1)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (1.25.2)\n", "Requirement already satisfied: openai>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (1.16.2)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (2.0.3)\n", "Requirement already satisfied: pillow>=9.0.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (9.4.0)\n", "Requirement already satisfied: requests>=2.31.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.2.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (8.2.3)\n", "Requirement already satisfied: tiktoken>=0.3.3 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (0.6.0)\n", "Requirement already satisfied: tqdm<5.0.0,>=4.66.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (4.66.2)\n", "Requirement already satisfied: typing-extensions>=4.5.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (4.10.0)\n", "Requirement already satisfied: typing-inspect>=0.8.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (0.9.0)\n", "Requirement already satisfied: wrapt in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (1.14.1)\n", "Requirement already satisfied: transformers<5.0.0,>=4.32.0 in /usr/local/lib/python3.10/dist-packages (from sentence-transformers<3.0.0,>=2.2.2->llama-index-embeddings-instructor) (4.38.2)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.10/dist-packages (from sentence-transformers<3.0.0,>=2.2.2->llama-index-embeddings-instructor) (1.2.2)\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.10/dist-packages (from sentence-transformers<3.0.0,>=2.2.2->llama-index-embeddings-instructor) (1.11.4)\n", "Requirement already satisfied: huggingface-hub>=0.15.1 in /usr/local/lib/python3.10/dist-packages (from sentence-transformers<3.0.0,>=2.2.2->llama-index-embeddings-instructor) (0.20.3)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (3.13.3)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (1.12)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (3.1.3)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (12.1.105)\n", "Requirement already satisfied: nvidia-cudnn-cu12==******** in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (********)\n", "Requirement already satisfied: nvidia-cublas-cu12==******** in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (********)\n", "Requirement already satisfied: nvidia-cufft-cu12==********* in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (*********)\n", "Requirement already satisfied: nvidia-curand-cu12==********** in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (**********)\n", "Requirement already satisfied: nvidia-cusolver-cu12==********** in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (**********)\n", "Requirement already satisfied: nvidia-cusparse-cu12==********** in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (**********)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.19.3 in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (2.19.3)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (12.1.105)\n", "Requirement already satisfied: triton==2.2.0 in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (2.2.0)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12 in /usr/local/lib/python3.10/dist-packages (from nvidia-cusolver-cu12==**********->torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (12.4.127)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (4.0.3)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence-transformers<3.0.0,>=2.2.2->llama-index-embeddings-instructor) (24.0)\n", "Requirement already satisfied: pydantic>=1.10 in /usr/local/lib/python3.10/dist-packages (from llamaindex-py-client<0.2.0,>=0.1.16->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (2.6.4)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (2024.2.2)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (1.0.5)\n", "Requirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (3.6)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (0.14.0)\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (8.1.7)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (1.3.2)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (2023.12.25)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai>=1.1.0->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (1.7.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (2.0.7)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy[asyncio]>=1.4.49->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (3.0.3)\n", "Requirement already satisfied: tokenizers<0.19,>=0.14 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.32.0->sentence-transformers<3.0.0,>=2.2.2->llama-index-embeddings-instructor) (0.15.2)\n", "Requirement already satisfied: safetensors>=0.4.1 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.32.0->sentence-transformers<3.0.0,>=2.2.2->llama-index-embeddings-instructor) (0.4.2)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from typing-inspect>=0.8.0->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (1.0.0)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (3.21.1)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (2.1.5)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (2024.1)\n", "Requirement already satisfied: threadpoolctl>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from scikit-learn->sentence-transformers<3.0.0,>=2.2.2->llama-index-embeddings-instructor) (3.4.0)\n", "Requirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->torch<3.0.0,>=2.1.2->llama-index-embeddings-instructor) (1.3.0)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (1.2.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->llamaindex-py-client<0.2.0,>=0.1.16->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.16.3 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->llamaindex-py-client<0.2.0,>=0.1.16->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (2.16.3)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.8.2->pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-instructor) (1.16.0)\n", "Installing collected packages: instructorembedding, llama-index-embeddings-instructor\n", "Successfully installed instructorembedding-1.0.1 llama-index-embeddings-instructor-0.1.3\n"]}]}, {"cell_type": "code", "source": ["from llama_index.embeddings.huggingface import HuggingFaceEmbedding\n", "embed_model =HuggingFaceEmbedding(model_name=\"sentence-transformers/all-mpnet-base-v2\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 369, "referenced_widgets": ["b374c20dc50e4850857b31dbad215cf1", "9a1c114114f04a4abbc9f163a0804f7a", "fc3ea5e27b974c51a7a5c6971474b5fb", "4f282fc21c1340148e43a3b56f0e8fc0", "d2649cc4517f4af9a23277131fa64a5a", "70238250c0684cdca666a931596c6dde", "f76ebed4482744eea4521c6250a867dd", "6140734d619e46018982df0fe5a7bb86", "f72a1a9f8cd845ea94587b6f6fab5474", "84c5417bada246d8b26122b4a76db143", "bd7eda890cd94ea9b5b5dfd23508b220", "ee5b6806f1fc491fbbe8bd6a5457f229", "92db8f39ebbc433896abb720a3324753", "e4450c44e4f84531a80a94a356c0094f", "ebdd0568585c4731865a2ac85ec677ee", "7ae45bc5a20b496da43a3d74c4eccee4", "e71c56df2aab4e208badc5b0225f3796", "0855dce963744867924d027baad24c4c", "e829a83fe16545f09deccafab0cec555", "a27b123120ce4238ab8f44e04dcb9595", "34b54e53fe314dfe8d5db61b927eee79", "3d10cb8d75954abc88868d7cbbf3d4c9", "6ac493552765456a903f531f421740e2", "d6bf34f7d3764d65bd296a29be4763ec", "c9650e654e16421ba3c565b2d16fde4e", "2fb0a9256f7045829c1685aeb5e88010", "1ef32d22d4524c21b990e261e9dc9a41", "d81fd21044d24fb09f4b288a4f005525", "64d9c7dabbaa45fc8164f7d95a0db63d", "3b0e122de9a64dfd8849d16864a1e8dc", "03a32c88fe8c49c6b178ea0bd17bcca8", "d56321d81f574c248134b6a697e52982", "4f59356e38e14dc19cac4292183ca035", "e0f873a9077f4375ad68e52b87e689ff", "7888f27f6c8b416ba66945ba108b94e9", "fd85bfbe602344bfb1a8a2e4d65b8e48", "82601baaec594014822c0d5b15b922e5", "89cb8953aaf844b69829754affbbe02f", "b89a7d6a6d15479fb0010a8a27094df8", "e0f24e6310e640bbbbb24585d32de2de", "84519662745849d19b56973752f0e0f6", "077b75340d484d348f30b2087963b3ad", "61cc455588a0412f909f93f549d905cc", "058abc0d7d8b4c18afe8a52b88186af3", "6d22979dddfe4a79a18444ac17533e5c", "79ed361591b9419cbea46987d5a54c89", "96890db6d3cb4ff1967baeecf66cc21d", "882cd564839d474f8da666f9706cb5b2", "d8d54ea71ee7470687fb24b21a4280b3", "df51d71be5c74703b00383c39b27a999", "fe6fcc6691034b42bd9851d36ac664cf", "a6a5f2d7bb26485cac86ec5ba3306ae8", "2774719aa3b44699a21d63824f48e087", "bfe1515c12f74d69a4f2ef0095faf929", "27ba43b61b8f4142a09f58e96e224fbd", "1c08c7dee27c49c2b9928dfb995aab8d", "0921f03f08d44083b6f4fa7d228cb12a", "7f9e0bf6f2aa4f5a8f074f4618ce14fa", "03f15f89737c4bf2899fea40abd4f57f", "e033814b8576426c8c129bb0cc7d5989", "5af78e959b904d2a8d674ff0f50cafc2", "378145700f0e40ebb2c69c3a2bb6cc75", "da426d0e899c49e6ae78efac337a256d", "037e87bc8039471c88175b65c98f8462", "a23cd24e713a49abacb2beea0c49db4d", "7f138e0339a2497a982177e146d9b778", "d773fd1010e54c1290965df31f45c57a", "9b4f9d70e14448db901592b1cd2c808f", "040d315fbe5f46cfaa3b56be7199a9e0", "ecb43e36f20a49859bc7695bb4a21089", "cbc9f166ccbc4f169112ef848bdf2cdf", "4c6fb5207f6a4e36bc73acb94c761080", "d89c07fb26db4783931c37c0c76917cb", "82e0a7d44ddd4a26b44e374de4af4ae9", "bda3531228994266aafda2501676ae4d", "e3394b0da30f4e758059711db0ed665f", "3511d760002d4d68a424b00724c363f6", "30d7f94a0bb8465b85a5f297095d6745", "b1d1b6c881284bd69b74121fc24620ca", "b2ac29c1fb614df4ab71afb8bada8f1e", "33f9b51d2cba4e0585ccc9e6a2781dd2", "df1348af99a74a0ebe1203202475461b", "37a415f6e70b47449a76a6bce1030031", "caa295fb977d4017b4524c0fbf343c7d", "cbae3ab46e4b4019b51071836917f301", "63c27f553f1449a9b21cbe944426bfef", "54916166e38c433a998808b638d88840", "79255c6e47ca40a2ad1c2bd037ab649e", "e746226ce13f4875b81dbc746997ad84", "7e7b314134614b9d96a3c9f95a742c25", "d67e9a5c64634e759d571b7774d204b6", "90c4de7de9e2435ca36f5a4960b15fbb", "99b9da0ac0cd4919b715c19f7465af67", "ef5bde446e8142818ec57969144cd6b2", "e14d67be717a4f9587643dd4af32c345", "b682fbc3e6c849e2ac70934f52028323", "d389de267d0b4ac5a3555714f1cfe80b", "83c26a9b20444f26b60776ece61f69b2", "ddf58aa133254cd3b95f31ccaf08b880", "8edffee4a0f3481ea460c50721cc13e5", "db1c6b1f0cfe4c598caf741168068f25", "6af1bee1967a40b89f8cfbd2687d96f9", "15d13e6b1fba4ddabe0f5bbe5988bd94", "da98dc04099a4786b2f8f6232fd7c4c6", "d40e5726111a403c9101820dd1ab12c4", "9d1b889b5d3044a680075411a4970a7e", "933559963a5242a4943dadefca235ada", "b5d7a637707d48918217a68eeb8c4163", "dd2a965527ae449f88696fe8a1e07a3e", "d0893bc09aaf4bc88f5045ad0c3430b9", "25b636ca56bb4ceb9f8125899ee00297", "165bac09663343c38c5e73c4d2494090", "ce8a9d6496c04350a209c55f2b4cd37f", "fb0ef984cde04b8e92c5fa41c59d3663", "61a2a20be6b64e8782237c6167d89745", "65d1cdaeb86d4f2c91d5f3cede8a24f4", "d277f862ebb24112b16a53e3ae2282b8", "cb7a4d55396743dfa0895cac3c4fd87a", "68eb1154ac74496786e34b767ccdee66", "83ac4442dc4d4b88b8ffdac2564fffdb", "49d914e1fd794ed9835c74227fb345f8"]}, "id": "C6-l096Z06Zf", "outputId": "5d8daa0d-33eb-493d-a362-f2f2f37ef73e"}, "execution_count": 11, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["modules.json:   0%|          | 0.00/349 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "b374c20dc50e4850857b31dbad215cf1"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config_sentence_transformers.json:   0%|          | 0.00/116 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "ee5b6806f1fc491fbbe8bd6a5457f229"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["README.md:   0%|          | 0.00/10.6k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "6ac493552765456a903f531f421740e2"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["sentence_bert_config.json:   0%|          | 0.00/53.0 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "e0f873a9077f4375ad68e52b87e689ff"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/571 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "6d22979dddfe4a79a18444ac17533e5c"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors:   0%|          | 0.00/438M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "1c08c7dee27c49c2b9928dfb995aab8d"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json:   0%|          | 0.00/363 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "d773fd1010e54c1290965df31f45c57a"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["vocab.txt:   0%|          | 0.00/232k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "30d7f94a0bb8465b85a5f297095d6745"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.json:   0%|          | 0.00/466k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "e746226ce13f4875b81dbc746997ad84"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["special_tokens_map.json:   0%|          | 0.00/239 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "8edffee4a0f3481ea460c50721cc13e5"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["1_Pooling/config.json:   0%|          | 0.00/190 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "25b636ca56bb4ceb9f8125899ee00297"}}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["https://github.com/run-llama/llama_index/blob/main/llama-index-core/llama_index/core/service_context.py"], "metadata": {"id": "4SiB7uf11quG"}}, {"cell_type": "code", "source": ["from llama_index.core import VectorStoreIndex, ServiceContext\n", "\n", "service_context = ServiceContext.from_defaults(\n", "    chunk_size=1024,\n", "    llm=llm,\n", "    embed_model=embed_model\n", ")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "S-HVDRiW1R6V", "outputId": "34c9f625-a563-4ec9-c4d4-95696a1699d3"}, "execution_count": 12, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-12-5b4ac3d90986>:3: DeprecationWarning: Call to deprecated class method from_defaults. (ServiceContext is deprecated, please use `llama_index.settings.Settings` instead.) -- Deprecated since version 0.10.0.\n", "  service_context = ServiceContext.from_defaults(\n"]}]}, {"cell_type": "code", "source": ["index = VectorStoreIndex.from_documents(documents, service_context=service_context)"], "metadata": {"id": "cpkAB41D1sxN"}, "execution_count": 13, "outputs": []}, {"cell_type": "code", "source": ["query_engine = index.as_query_engine()"], "metadata": {"id": "ueC80L6422mV"}, "execution_count": 14, "outputs": []}, {"cell_type": "code", "source": ["query_engine.query(\"what is attention?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WdHRSPfZ29fN", "outputId": "c2f8b40b-6a8e-4b38-eeea-73340b495fb0"}, "execution_count": 15, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/transformers/generation/configuration_utils.py:410: UserWarning: `do_sample` is set to `False`. However, `temperature` is set to `0.7` -- this flag is only used in sample-based generation modes. You should set `do_sample=True` or unset `temperature`.\n", "  warnings.warn(\n", "Setting `pad_token_id` to `eos_token_id`:2 for open-end generation.\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["Response(response='Attention is a mechanism used in machine translation to retain and utilize all the hidden state of the input sequence during the decoding phase. It allows the model to focus and pay more attention to the relevant part of the input sequence. The attention mechanism just addresses the issue of the encoder not being able to memorize the words coming at the beginning of the sentences, which leads to poor translation of the source sentence. During the decoding phase, the model creates an alignment between each time step of the decoder output and all of the encoder hidden state. We need to learn this alignment. Each output of the decoder can selectively pick out specific elements from the sequence to produce the output. This allows the model to focus and pay more attention to the relevant part of the input sequence.', source_nodes=[NodeWithScore(node=TextNode(id_='89e85e11-970a-4eec-b436-e9ebc5227e68', embedding=None, metadata={'page_label': '5', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, excluded_embed_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], relationships={<NodeRelationship.SOURCE: '1'>: RelatedNodeInfo(node_id='5554f1d1-32b8-4775-b89e-deb2887239d5', node_type=<ObjectType.DOCUMENT: '4'>, metadata={'page_label': '5', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, hash='12feae2fe1c7f84fb639ea342870052bd2c2441d653800599ee03fd048af18bc'), <NodeRelationship.PREVIOUS: '2'>: RelatedNodeInfo(node_id='8e760fa3-dc97-4527-9d98-3e7a1e6f5d28', node_type=<ObjectType.TEXT: '1'>, metadata={'page_label': '4', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, hash='ca13d963f8e248062344852a3d2a10370487a7e1bd90611e5017470778c80161'), <NodeRelationship.NEXT: '3'>: RelatedNodeInfo(node_id='c7a81ede-5c76-4938-a7a8-a9d7d9ff8931', node_type=<ObjectType.TEXT: '1'>, metadata={}, hash='2c09da031087bfc89a6ab7d3f5ead0f94a49955b5b61632b67f1aba8f4adb688')}, text='Figure 2: Attention Model (Luong et al., 2015)\\nuse the information from the past, present as well\\nas from the future. We need the entire sequence of\\ndata before we can make any predictions.\\nThe architecture that we are proposing here is\\nbased on the Encoder-Decoder Framework. The\\nencoder takes in the input sentence and converts\\nthem into a vector representation\\nThe encoder can be an RNN (Cho et al., 2014b)\\nor LSTM unit (Sutskever et al., 2014). They pro-\\ncesses the input sentence, pass it through RNN or\\nLSTM, and when it encounters the end of sen-\\ntence, then the hidden state, that captured all the\\nrelevant information passes it to the decoder. Then\\nthis information is used to predict the translations\\nin the decoder, which can be RNN or LSTM, until\\nit predicts the end of the sentence token. The hid-\\nden state needs to remember every word from the\\ninput sentence. So that is why this model tends to\\nwork for short sentences and not long sentences.\\nEven though if we used LSTM or GRU, which\\ntends to remember the words that occured very\\nearly in the sequence, it will still not be able to\\nlearn the alignment between the source word and\\nthe target word. They often forget the initial part\\nof the sentence once they are processed in the en-\\ncoder. That is why we use an alignment mecha-\\nnism called attention. They help to memorize this\\ninformation for longer sentences. But we need to\\nlearn this alignment. It can vary from language to\\nlanguage.3.2 Attention\\nIn this section, we will speciﬁcally deﬁne the\\nalignment mechanism, [See Figure 2] that we used\\nin our model. In RNN Encoder-Decoder model\\n(Sutskever et al., 2014), we faced with the bottle-\\nneck problem, where the complete sequence of in-\\nformation of the source sentence, must be captured\\nby one single vector, i.e. the last hidden unit of the\\nencoder RNN is used as a context vector for the\\ndecoder, which becomes difﬁcult for the decoder\\nto summarise large input sequence at once. This\\nalso poses a problem where the encoder is not able\\nto memorize the words coming at the beginning\\nof the sentences, which leads to poor translation\\nof the source sentence. The Attention mechanism\\njust addresses this issue, by retaining and utilising\\nall the hidden state of the input sentence during the\\ndecoding phase.\\nDuring the decoding phase, the model creates\\nan alignment between each time step of the de-\\ncoder output and all of the encoder hidden state.\\nWe need to learn this alignment. Each output of\\nthe decoder can selectively pick out speciﬁc ele-\\nments from the sequence to produce the output.\\nSo, this allows the model to focus and pay more\\n”Attention” to the relevant part of the input se-\\nquence.\\nThe ﬁrst attention model was proposed by Bah-\\ndanau et al. (2014), there are several other types of\\nattention proposed, such as the one by Luong et al.', start_char_idx=0, end_char_idx=2822, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'), score=0.34934048152358166), NodeWithScore(node=TextNode(id_='c7a81ede-5c76-4938-a7a8-a9d7d9ff8931', embedding=None, metadata={'page_label': '6', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, excluded_embed_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], relationships={<NodeRelationship.SOURCE: '1'>: RelatedNodeInfo(node_id='b39f5904-404c-4387-8eef-b132ce88f0fe', node_type=<ObjectType.DOCUMENT: '4'>, metadata={'page_label': '6', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, hash='f800ac700325a5686f168a5c94a8bd82b3aba510cdafc947e1c9c1149b5486bb'), <NodeRelationship.PREVIOUS: '2'>: RelatedNodeInfo(node_id='89e85e11-970a-4eec-b436-e9ebc5227e68', node_type=<ObjectType.TEXT: '1'>, metadata={'page_label': '5', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, hash='12feae2fe1c7f84fb639ea342870052bd2c2441d653800599ee03fd048af18bc'), <NodeRelationship.NEXT: '3'>: RelatedNodeInfo(node_id='d8e572a0-3037-4179-bf10-e29b8c6bc5d2', node_type=<ObjectType.TEXT: '1'>, metadata={}, hash='40b86899c90cb347e08964b2c7f0f6a65d79d0cdb56923ed215f25805eb87f65')}, text='Spanish en la estrategia 2020 , reconocimos el hecho de que , si queremos mantener nuestro\\nnivel de prosperidad en europa , tenemos que aumentar nuestra productividad .\\nEnglish in the 2020 strategy , we acknowledged the fact that , if we are to maintain our level\\nof prosperity in europe , we need to increase our productivity .\\nSpanish sin embargo , es algo que debemos hacer si queremos demostrar a los estados unidos\\nque nos deben considerar como un socio serio en la alianza contra el terrorismo .\\nEnglish yet do it we must , if we are to demonstrate to the usa that we are to be taken\\nseriously as a partner in an alliance against terrorism .\\nSpanish sabr´an ustedes que fue tambi ´en a instancias de esta c ´amara que la comisi ´on entabl ´o\\nnegociaciones , y estas han dado un resultado encomiable .\\nEnglish you will be aware that it was not least at the insistence of this house that the com-\\nmission entered into negotiations , and these have produced a creditable result .\\nTable 1: Examples of Spanish and English sentences from the dataset\\n(2015).\\nWe will only discuss the attention model, pro-\\nposed by Bahdanau et al. (2014). After the in-\\nput sequence is passed through the encoder, it pro-\\nduces hidden state for each of the elements in the\\nsequence (h1,...,h Tx). Then we multiply the de-\\ncoders hidden state at time step t (s1,...,s Ty),\\nwith all of the encoders hidden state, which gives\\nus the alignment score of each of the encoder out-\\nput with respect to the decoder input and hidden\\nstate at that time step:\\net= [sT\\nth1,...,sT\\nthTx]\\nThe alignment score quantiﬁes the amount of\\nAttention the decoder will place on each of the en-\\ncoder outputs when producing the next output, so\\ninstead of looking at the entire sequence, it just\\nconcentrate on few relevant parts of the sequence\\nwhen predicting the next word.\\nAfter calculating the alignment score, we pass\\nthe vectoretthrough the softmax layer, to calcu-\\nlate the probability distribution.\\nαt=softmax (et)\\nThen we multiply each of the attention weights\\nwith each of the encoder hidden state, to get con-\\ntext vector,at\\nat=Tx∑\\ni=1αt\\nihi\\nIf the attention score of speciﬁc element of the\\ninput sequence is close to 1, then its inﬂuence on\\nthe decoder output at that speciﬁc time step in-\\ncreases. And then ﬁnally, the context vector atproduced will be concatenated with the decoder\\nhidden state, st, i.e.\\n˜ht= [at,st]\\nand is fed into decoder RNN, which produces new\\nhidden state.\\n4 Data\\nWe used a Parallel Corpus for English-Spanish\\nlanguage, which was extracted from the proceed-\\nings of the European Parliament, also called Eu-\\nroparl dataset (Koehn, 2005). It contains 1.96\\nMillion sentences, each for English and Spanish.\\nMost common words and count for both languages\\nare shown below:\\nWords Count\\nde 1799827\\n, 1456229\\nla 1222089\\nque 992176\\n. 867284\\nen 790382\\nel 696521\\ny 692640\\na 577052\\nlos 548495\\nTable 2: SpanishWords Count\\nthe 1956558\\n, 1371506\\nof 932044\\nto 875415\\n. 864674\\nand 747108\\nin 622426\\nthat 476250\\na 430093\\nis 401782\\nTable 3: English\\nFirst we ﬁlter out all the sentences having words\\ngreater than 50. Then we sort these sentences\\nbased on the number of words in each of the sen-\\ntences, so that we have less padded sentences in\\nour initial indices and sentences with high padding\\nto be at the end of our indices, following with', start_char_idx=0, end_char_idx=3315, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'), score=0.296389907696953)], metadata={'89e85e11-970a-4eec-b436-e9ebc5227e68': {'page_label': '5', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, 'c7a81ede-5c76-4938-a7a8-a9d7d9ff8931': {'page_label': '6', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}})"]}, "metadata": {}, "execution_count": 15}]}, {"cell_type": "code", "source": ["query_engine.query(\"how attention is different from rnn and lstm\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "s5mHS65_3HGd", "outputId": "de5e6184-feea-405b-8296-0d7f818cdd3c"}, "execution_count": 16, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["Setting `pad_token_id` to `eos_token_id`:2 for open-end generation.\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["Response(response='Attention is different from RNN and LSTM in the sense that it allows the model to focus and pay more attention to the relevant part of the input sequence during the decoding phase. While RNN and LSTM process the input sequence and pass it through the encoder, they often forget the initial part of the sentence once they are processed in the encoder. Attention, on the other hand, retains and utilizes all the hidden state of the input sequence during the decoding phase, allowing the model to focus on the relevant part of the input sequence. Additionally, attention can vary from language to language, while RNN and LSTM tend to remember the words that occurred very early in the sequence.', source_nodes=[NodeWithScore(node=TextNode(id_='89e85e11-970a-4eec-b436-e9ebc5227e68', embedding=None, metadata={'page_label': '5', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, excluded_embed_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], relationships={<NodeRelationship.SOURCE: '1'>: RelatedNodeInfo(node_id='5554f1d1-32b8-4775-b89e-deb2887239d5', node_type=<ObjectType.DOCUMENT: '4'>, metadata={'page_label': '5', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, hash='12feae2fe1c7f84fb639ea342870052bd2c2441d653800599ee03fd048af18bc'), <NodeRelationship.PREVIOUS: '2'>: RelatedNodeInfo(node_id='8e760fa3-dc97-4527-9d98-3e7a1e6f5d28', node_type=<ObjectType.TEXT: '1'>, metadata={'page_label': '4', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, hash='ca13d963f8e248062344852a3d2a10370487a7e1bd90611e5017470778c80161'), <NodeRelationship.NEXT: '3'>: RelatedNodeInfo(node_id='c7a81ede-5c76-4938-a7a8-a9d7d9ff8931', node_type=<ObjectType.TEXT: '1'>, metadata={}, hash='2c09da031087bfc89a6ab7d3f5ead0f94a49955b5b61632b67f1aba8f4adb688')}, text='Figure 2: Attention Model (Luong et al., 2015)\\nuse the information from the past, present as well\\nas from the future. We need the entire sequence of\\ndata before we can make any predictions.\\nThe architecture that we are proposing here is\\nbased on the Encoder-Decoder Framework. The\\nencoder takes in the input sentence and converts\\nthem into a vector representation\\nThe encoder can be an RNN (Cho et al., 2014b)\\nor LSTM unit (Sutskever et al., 2014). They pro-\\ncesses the input sentence, pass it through RNN or\\nLSTM, and when it encounters the end of sen-\\ntence, then the hidden state, that captured all the\\nrelevant information passes it to the decoder. Then\\nthis information is used to predict the translations\\nin the decoder, which can be RNN or LSTM, until\\nit predicts the end of the sentence token. The hid-\\nden state needs to remember every word from the\\ninput sentence. So that is why this model tends to\\nwork for short sentences and not long sentences.\\nEven though if we used LSTM or GRU, which\\ntends to remember the words that occured very\\nearly in the sequence, it will still not be able to\\nlearn the alignment between the source word and\\nthe target word. They often forget the initial part\\nof the sentence once they are processed in the en-\\ncoder. That is why we use an alignment mecha-\\nnism called attention. They help to memorize this\\ninformation for longer sentences. But we need to\\nlearn this alignment. It can vary from language to\\nlanguage.3.2 Attention\\nIn this section, we will speciﬁcally deﬁne the\\nalignment mechanism, [See Figure 2] that we used\\nin our model. In RNN Encoder-Decoder model\\n(Sutskever et al., 2014), we faced with the bottle-\\nneck problem, where the complete sequence of in-\\nformation of the source sentence, must be captured\\nby one single vector, i.e. the last hidden unit of the\\nencoder RNN is used as a context vector for the\\ndecoder, which becomes difﬁcult for the decoder\\nto summarise large input sequence at once. This\\nalso poses a problem where the encoder is not able\\nto memorize the words coming at the beginning\\nof the sentences, which leads to poor translation\\nof the source sentence. The Attention mechanism\\njust addresses this issue, by retaining and utilising\\nall the hidden state of the input sentence during the\\ndecoding phase.\\nDuring the decoding phase, the model creates\\nan alignment between each time step of the de-\\ncoder output and all of the encoder hidden state.\\nWe need to learn this alignment. Each output of\\nthe decoder can selectively pick out speciﬁc ele-\\nments from the sequence to produce the output.\\nSo, this allows the model to focus and pay more\\n”Attention” to the relevant part of the input se-\\nquence.\\nThe ﬁrst attention model was proposed by Bah-\\ndanau et al. (2014), there are several other types of\\nattention proposed, such as the one by Luong et al.', start_char_idx=0, end_char_idx=2822, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'), score=0.5028499112163399), NodeWithScore(node=TextNode(id_='8e760fa3-dc97-4527-9d98-3e7a1e6f5d28', embedding=None, metadata={'page_label': '4', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, excluded_embed_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['file_name', 'file_type', 'file_size', 'creation_date', 'last_modified_date', 'last_accessed_date'], relationships={<NodeRelationship.SOURCE: '1'>: RelatedNodeInfo(node_id='27fe23f4-7043-4deb-8421-93014e2dfead', node_type=<ObjectType.DOCUMENT: '4'>, metadata={'page_label': '4', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, hash='ca13d963f8e248062344852a3d2a10370487a7e1bd90611e5017470778c80161'), <NodeRelationship.PREVIOUS: '2'>: RelatedNodeInfo(node_id='40d28edf-efee-44b3-ad3e-87542f3402dc', node_type=<ObjectType.TEXT: '1'>, metadata={'page_label': '3', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, hash='13221cd07ecc380f03b39e4a211ea5b4367dd7bec1d52eff74ddbdb8a64fd640'), <NodeRelationship.NEXT: '3'>: RelatedNodeInfo(node_id='89e85e11-970a-4eec-b436-e9ebc5227e68', node_type=<ObjectType.TEXT: '1'>, metadata={}, hash='e85cac65bdc3951f127ef5877b571e142b40f5a540ca7a1e4523046cd9ff390c')}, text='Figure 1: The left diagram represents Long Short Term Memory Unit, with ias an Input gate, oas an\\nOutput Gate, and fas a Forget Gate. The right diagram represents Gated Recurrent Unit, with ras Reset\\nGate, andzas an Update Gate. (Chung et al., 2014)\\nto be strongly inﬂuenced by a word that was very\\nearly in the sequence.\\nBut we can further improve the training by us-\\ning other RNN units, like GRU (Chung et al.,\\n2014) or LSTM (Hochreiter and Schmidhuber,\\n1997), which are better at capturing long-range de-\\npendencies. The state vector in simple RNN can\\nbe considered as a memory, where the memory ac-\\ncess was not controlled. At each step, the entire\\nmemory state was read and updated. But in GRU\\nand LSTM, we use a gating mechanism to control\\nthe memory. Since, we used GRU in our model,\\nso we will only describe GRU here.\\nIn GRU, (Chung et al., 2014; Rana, 2016), the\\nactivationhj\\ntis a linear interpolation between the\\nprevious activation hj\\nt−1and the candidate ativa-\\ntion˜hj\\nt:\\nhj\\nt= (1−zj\\nt)hj\\nt−1+zj\\nt˜hj\\nt\\nwhere,zj\\ntis the update gate, that decides how\\nmuch GRU units updates its activation [See Fig.\\n1]. The update gate is given by :\\nzj\\nt=σ(Wzxt+Uzht−1)j.\\nAnd the candidate activation ˆhj\\ntis computed by :\\nˆhj\\nt=tanh (Wx t+U(rt⊙ht−1))j,\\nwhere⊙denotes element-wise multiplication and\\nrj\\ntare reset gates. When a reset gate at speciﬁc\\ntime, t is set to 0, i.e. rj\\nt== 0 , which makes the\\nGRU to forget the past, i.e. forget the previousstate vectors. This is considered same as reading\\nthe ﬁrst word of the input sentence. And ﬁnally,\\nwe can compute the reset gate by :\\nrj\\nt=σ(Wrxt+Urht−1)j.\\nOne of the weakness of RNN is that it only uses\\ninformation that is earlier in the sequence to make\\npredictions but not the information which are later\\nin the sequence. When predicting the output at\\ntime step i, it does not use the word at time step i+1\\nor i+2 or any other words in the later time step. So,\\nit would be useful to know not just the information\\nfrom the words from the previous time step but\\nalso the information from the later time steps.\\nSo, we use Bidirectional RNN, ﬁrst proposed\\nby (Schuster and Paliwal, 1997). From a point\\nin time, it takes information from both the ear-\\nlier and later time step in the sequence. The\\nFirst RNN, which we called forward RNN,− →fis\\nfed the input sequence as it is. And the second\\nRNN, which is also called backward RNN,← −f\\nis fed the input sequence in reverse order. This\\ngives two separate state vectors – a forward state\\nvector,− →hT\\nj, and a backward state vector← −hT\\nj.\\n− →hT\\njwould be a sequence of forward hidden state\\nvectors, (− →h1,...,− →hTx), and similarly, backward\\nstate vector← −hT\\njwould be a sequence of backward\\nhidden state vector, (← −h1,...,← −hTx). And the out-\\nput at a speciﬁc timestep is accounted by the con-\\ncatenation of output of two RNN’s, concatenating− →hjand← −hj, i.e.hj= [− →hT\\nj,← −hT\\nj]. So, when pre-\\ndicting the output at a speciﬁc time step, it will', start_char_idx=0, end_char_idx=2948, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'), score=0.****************)], metadata={'89e85e11-970a-4eec-b436-e9ebc5227e68': {'page_label': '5', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}, '8e760fa3-dc97-4527-9d98-3e7a1e6f5d28': {'page_label': '4', 'file_name': 'MachineTranslationwithAttention.pdf', 'file_path': '/content/data/MachineTranslationwithAttention.pdf', 'file_type': 'application/pdf', 'file_size': 482134, 'creation_date': '2024-04-10', 'last_modified_date': '2024-04-10'}})"]}, "metadata": {}, "execution_count": 16}]}, {"cell_type": "code", "source": [], "metadata": {"id": "IfBLY3pm3Pnf"}, "execution_count": null, "outputs": []}]}