{"name": "@university/database", "version": "1.0.0", "description": "Database schemas and utilities for the university system", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "setup": "supabase init && supabase start", "migrate": "supabase db push", "seed": "supabase db seed", "reset": "supabase db reset", "types": "supabase gen types typescript --local > src/types/database.ts", "clean": "rm -rf dist"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "@university/types": "file:../types"}, "devDependencies": {"supabase": "^1.123.0", "typescript": "^5.3.0"}}