import { z } from 'zod';

// User roles
export enum UserRole {
  STUDENT = 'student',
  FACULTY = 'faculty',
  STAFF = 'staff',
  ADMIN = 'admin'
}

// Course status
export enum CourseStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived'
}

// Enrollment status
export enum EnrollmentStatus {
  ENROLLED = 'enrolled',
  DROPPED = 'dropped',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// Grade types
export enum GradeType {
  ASSIGNMENT = 'assignment',
  QUIZ = 'quiz',
  MIDTERM = 'midterm',
  FINAL = 'final',
  PROJECT = 'project'
}

// Zod schemas for validation
export const UserSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  role: z.nativeEnum(UserRole),
  studentId: z.string().optional(),
  employeeId: z.string().optional(),
  departmentId: z.string().uuid().optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  dateOfBirth: z.string().optional(),
  enrollmentDate: z.string().optional(),
  graduationDate: z.string().optional(),
  isActive: z.boolean().default(true),
  createdAt: z.string(),
  updatedAt: z.string()
});

export const DepartmentSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  code: z.string().min(1),
  description: z.string().optional(),
  headOfDepartmentId: z.string().uuid().optional(),
  isActive: z.boolean().default(true),
  createdAt: z.string(),
  updatedAt: z.string()
});

export const CourseSchema = z.object({
  id: z.string().uuid(),
  code: z.string().min(1),
  name: z.string().min(1),
  description: z.string().optional(),
  credits: z.number().min(1).max(6),
  departmentId: z.string().uuid(),
  instructorId: z.string().uuid(),
  semester: z.string(),
  year: z.number(),
  maxEnrollment: z.number().min(1),
  currentEnrollment: z.number().default(0),
  status: z.nativeEnum(CourseStatus),
  schedule: z.string().optional(),
  location: z.string().optional(),
  prerequisites: z.array(z.string().uuid()).default([]),
  createdAt: z.string(),
  updatedAt: z.string()
});

export const EnrollmentSchema = z.object({
  id: z.string().uuid(),
  studentId: z.string().uuid(),
  courseId: z.string().uuid(),
  status: z.nativeEnum(EnrollmentStatus),
  enrollmentDate: z.string(),
  dropDate: z.string().optional(),
  finalGrade: z.string().optional(),
  gpa: z.number().optional(),
  createdAt: z.string(),
  updatedAt: z.string()
});

export const GradeSchema = z.object({
  id: z.string().uuid(),
  enrollmentId: z.string().uuid(),
  type: z.nativeEnum(GradeType),
  name: z.string().min(1),
  points: z.number().min(0),
  maxPoints: z.number().min(1),
  percentage: z.number().min(0).max(100),
  letterGrade: z.string().optional(),
  comments: z.string().optional(),
  submittedAt: z.string().optional(),
  gradedAt: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string()
});

export const AssignmentSchema = z.object({
  id: z.string().uuid(),
  courseId: z.string().uuid(),
  title: z.string().min(1),
  description: z.string().optional(),
  type: z.nativeEnum(GradeType),
  maxPoints: z.number().min(1),
  dueDate: z.string(),
  submissionFormat: z.string().optional(),
  instructions: z.string().optional(),
  isPublished: z.boolean().default(false),
  createdAt: z.string(),
  updatedAt: z.string()
});

// TypeScript types derived from Zod schemas
export type User = z.infer<typeof UserSchema>;
export type Department = z.infer<typeof DepartmentSchema>;
export type Course = z.infer<typeof CourseSchema>;
export type Enrollment = z.infer<typeof EnrollmentSchema>;
export type Grade = z.infer<typeof GradeSchema>;
export type Assignment = z.infer<typeof AssignmentSchema>;

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Authentication types
export interface AuthUser {
  id: string;
  email: string;
  role: UserRole;
  firstName: string;
  lastName: string;
  studentId?: string;
  employeeId?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData extends LoginCredentials {
  firstName: string;
  lastName: string;
  role: UserRole;
  studentId?: string;
  employeeId?: string;
}

// Dashboard data types
export interface StudentDashboardData {
  enrolledCourses: Course[];
  recentGrades: Grade[];
  upcomingAssignments: Assignment[];
  gpa: number;
  totalCredits: number;
}

export interface AdminDashboardData {
  totalStudents: number;
  totalCourses: number;
  totalFaculty: number;
  recentEnrollments: Enrollment[];
  courseEnrollmentStats: Array<{
    courseId: string;
    courseName: string;
    enrolled: number;
    capacity: number;
  }>;
}

// Form types for creating/updating entities
export type CreateUserData = Omit<User, 'id' | 'createdAt' | 'updatedAt'>;
export type UpdateUserData = Partial<CreateUserData>;
export type CreateCourseData = Omit<Course, 'id' | 'currentEnrollment' | 'createdAt' | 'updatedAt'>;
export type UpdateCourseData = Partial<CreateCourseData>;
export type CreateEnrollmentData = Omit<Enrollment, 'id' | 'createdAt' | 'updatedAt'>;
export type CreateGradeData = Omit<Grade, 'id' | 'createdAt' | 'updatedAt'>;
