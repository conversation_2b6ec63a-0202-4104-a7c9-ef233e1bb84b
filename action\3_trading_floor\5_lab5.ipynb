{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 220px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/aaa.png\" width=\"220\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Autonomous Traders</h2>\n", "            <span style=\"color:#ff7800;\">An equity trading simulation to illustrate autonomous agents powered by tools and resources from MCP servers.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## It's time to try it out"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Introducing our FOUR traders\n", "\n", "Our traders are inspired by 4 titans of the industry.  \n", "\n", "**Warren**, in homage to <PERSON>  \n", "**<PERSON>**, in homage to <PERSON>  \n", "**Ray**, in homage to <PERSON>  \n", "**<PERSON><PERSON><PERSON>**, in homage to <PERSON><PERSON><PERSON>\n", "\n", "They have initial investment strategies that are inspired by their namesakes; but they have the autonomy to change their own strategy over time if they wish, using a tool.\n", "\n", "\n", "Check out the file `reset.py` to read their initial investment theses.\n", "\n", "Then: `uv run app.py` to bring up the UI\n", "\n", "Then: `uv run tradingfloor.py` to start trading\n", "\n", "## More notes below for running this yourself at home.."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Now, set our traders to the starting point - uncomment the line below!\n", "\n", "from reset import reset_traders\n", "# reset_traders()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Revealing the final changes\n", "\n", "Some new MCP servers - push notification - take a look at `mcp_params.py` and `push_server.py`\n", "\n", "And this is a cool thing:\n", "\n", "OpenAI Agents SDK has a nice feature that you can integrate with their Tracing code, so that you can monitor Trace messages in code.\n", "\n", "See `tracers.py` - I've written a custom tracer that records our Trader activity and stores it in the database, so we can surface the Traders' inner thoughts on our UI."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# AND NOW.. Reveal part 1\n", "\n", "First, there are some settings that you can optionally add to your .env file:\n", "\n", "`RUN_EVERY_N_MINUTES=60`  \n", "This determines how often the trader agents are run, and it defaults to every 60 minutes if not specified in your .env file.\n", "\n", "`RUN_EVEN_WHEN_MARKET_IS_CLOSED=False`  \n", "This determines if the traders should still run out of hours, and defaults to False if not specified in your .env file.\n", "\n", "`USE_MANY_MODELS=False`  \n", "This determines whether to use DeepSeek, Gemini and Grok in addition to OpenAI, using DeepSeek API and OpenRouter.  \n", "It defaults to False if not specified in your .env file, in which case gpt-4o-mini is used throughout.\n", "\n", "Please make those changes if you wish!\n", "\n", "THen take a look at the UI code in `app.py`\n", "\n", "Then, open a new terminal (Shift + Ctrl + Backtick)\n", "\n", "Change to this directory:  \n", "`cd 3_trading_floor`\n", "\n", "And run:  \n", "`uv run app.py`\n", "\n", "And take joy in the user interface!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# AND NOW.. Reveal part 2\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Now go and look at the engine:\n", "\n", "`trading_floor.py`\n", "\n", "It has the super-simple loop that's where the magic happens:\n", "\n", "\n", "```\n", "while True:\n", "    await asyncio.gather(*[trader.run() for trader in traders])\n", "    await asyncio.sleep(RUN_EVERY_N_MINUTES * 60)\n", "```\n", "\n", "You'll also see how it looks for the environment variables.\n", "\n", "And finally, open a new terminal (Shift + Ctrl + Backtick, or press the Plus on the top right of the Terminals below)\n", "\n", "Change to this directory:  \n", "`cd 3_trading_floor`\n", "\n", "And run:  \n", "`uv run trading_floor.py`\n", "\n", "And watch your user interface - see your trading team in action!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/stop.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">One last reminder to monitor your APIs</h2>\n", "            <span style=\"color:#ff7800;\">This will run on a loop, every hour, or however you specified. You should watch your API usage, and stop this running when you've had enough!<br/>\n", "            I find myself watching this happily for hours - and I hope you will too! A huge project that showcases the power of Autonomous Agents.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/thanks.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#00cc00;\">THANK YOU SO MUCH!</h2>\n", "            <span style=\"color:#00cc00;\">You've reached the conclusion! We're so grateful that you stuck at it all the way to the end. If you'd like to learn all this in a more structured way, please check out <PERSON>'s udemy course that leads up to this more thoroughly.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}