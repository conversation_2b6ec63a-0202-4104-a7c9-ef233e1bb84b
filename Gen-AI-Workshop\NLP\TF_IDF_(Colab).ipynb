{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"id": "imoKyamWilFt"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.metrics import classification_report, accuracy_score\n", "from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer"]}, {"cell_type": "code", "source": ["corpus1 = [\n", "    \"Thor eating pizza, <PERSON> is eating pizza, <PERSON><PERSON> ate pizza already\",\n", "    \"Apple is announcing new iphone tomorrow\",\n", "    \"Google is announcing new model-3 tomorrow\",\n", "    \"Samsung is announcing new pixel-6 tomorrow\",\n", "    \"Microsoft is announcing new surface tomorrow\",\n", "    \"Amazon is announcing new eco-dot tomorrow\",\n", "    \"I am eating biryani and you are eating grapes\"\n", "]"], "metadata": {"id": "08Y2IQvIilue"}, "execution_count": 2, "outputs": []}, {"cell_type": "code", "source": ["v = TfidfVectorizer()\n", "v.fit(corpus1)\n", "transform_output = v.transform(corpus1)"], "metadata": {"id": "YfG458hMinWl"}, "execution_count": 3, "outputs": []}, {"cell_type": "code", "source": ["v.get_feature_names_out()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "nkE26wM-iov1", "outputId": "25ab0ad8-8256-48f9-d122-5bc22a12bfdd"}, "execution_count": 4, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array(['already', 'am', 'amazon', 'and', 'announcing', 'apple', 'are',\n", "       'ate', 'biryani', 'dot', 'eating', 'eco', 'google', 'grapes',\n", "       'iphone', 'ironman', 'is', 'loki', 'microsoft', 'model', 'new',\n", "       'pixel', 'pizza', 'samsung', 'surface', 'thor', 'tomorrow', 'you'],\n", "      dtype=object)"]}, "metadata": {}, "execution_count": 4}]}, {"cell_type": "code", "source": ["# Print the idf of each word\n", "\n", "all_feature_names = v.get_feature_names_out()\n", "\n", "for word in all_feature_names:\n", "\n", "  indx = v.vocabulary_.get(word)\n", "\n", "  #get the score\n", "  idf_score = v.idf_[indx]\n", "\n", "  print(f\"{word}: {idf_score}\")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6k-_c-a3isYV", "outputId": "60b01fe9-828d-4c01-833c-af9493975657"}, "execution_count": 5, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["already: 2.386294361119891\n", "am: 2.386294361119891\n", "amazon: 2.386294361119891\n", "and: 2.386294361119891\n", "announcing: 1.2876820724517808\n", "apple: 2.386294361119891\n", "are: 2.386294361119891\n", "ate: 2.386294361119891\n", "biryani: 2.386294361119891\n", "dot: 2.386294361119891\n", "eating: 1.9808292530117262\n", "eco: 2.386294361119891\n", "google: 2.386294361119891\n", "grapes: 2.386294361119891\n", "iphone: 2.386294361119891\n", "ironman: 2.386294361119891\n", "is: 1.1335313926245225\n", "loki: 2.386294361119891\n", "microsoft: 2.386294361119891\n", "model: 2.386294361119891\n", "new: 1.2876820724517808\n", "pixel: 2.386294361119891\n", "pizza: 2.386294361119891\n", "samsung: 2.386294361119891\n", "surface: 2.386294361119891\n", "thor: 2.386294361119891\n", "tomorrow: 1.2876820724517808\n", "you: 2.386294361119891\n"]}]}, {"cell_type": "code", "source": ["corpus2 = [\n", "    \"Thor eating pizza, <PERSON> is eating pizza, <PERSON><PERSON> ate pizza already\",\n", "    \"Apple is announcing new iphone tomorrow\",\n", "    \"Apple is announcing new model-3 tomorrow\",\n", "    \"Apple is announcing new pixel-6 tomorrow\",\n", "    \"Apple is announcing new surface tomorrow\",\n", "    \"Amazon is announcing new eco-dot tomorrow\",\n", "    \"I am eating biryani and you are eating grapes\"\n", "]"], "metadata": {"id": "_75O65pKi0Lm"}, "execution_count": 6, "outputs": []}, {"cell_type": "code", "source": ["v2 = TfidfVectorizer()\n", "v2.fit(corpus2)\n", "transform_output2 = v2.transform(corpus2)"], "metadata": {"id": "hNc22BW7i6AN"}, "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": ["# Print the idf of each word\n", "\n", "all_feature_names2 = v2.get_feature_names_out()\n", "\n", "for word in all_feature_names2:\n", "\n", "  indx2 = v2.vocabulary_.get(word)\n", "\n", "  #get the score\n", "  idf_score2 = v2.idf_[indx2]\n", "\n", "  print(f\"{word}: {idf_score2}\")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "h06yiwzwi9f9", "outputId": "e480d3d7-703b-4ed6-abbc-b5eae786192d"}, "execution_count": 9, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["already: 2.386294361119891\n", "am: 2.386294361119891\n", "amazon: 2.386294361119891\n", "and: 2.386294361119891\n", "announcing: 1.2876820724517808\n", "apple: 1.4700036292457357\n", "are: 2.386294361119891\n", "ate: 2.386294361119891\n", "biryani: 2.386294361119891\n", "dot: 2.386294361119891\n", "eating: 1.9808292530117262\n", "eco: 2.386294361119891\n", "grapes: 2.386294361119891\n", "iphone: 2.386294361119891\n", "ironman: 2.386294361119891\n", "is: 1.1335313926245225\n", "loki: 2.386294361119891\n", "model: 2.386294361119891\n", "new: 1.2876820724517808\n", "pixel: 2.386294361119891\n", "pizza: 2.386294361119891\n", "surface: 2.386294361119891\n", "thor: 2.386294361119891\n", "tomorrow: 1.2876820724517808\n", "you: 2.386294361119891\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "p4wFGghzjEq1"}, "execution_count": null, "outputs": []}]}