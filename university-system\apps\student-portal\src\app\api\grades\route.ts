import { NextRequest, NextResponse } from 'next/server';
import { createServerClient, getCurrentUser } from '@university/database/src/client';
import { ApiResponse, PaginatedResponse } from '@university/types';

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient();
    const { searchParams } = new URL(request.url);
    
    // Get pagination parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;
    
    // Get filter parameters
    const courseId = searchParams.get('courseId');
    const semester = searchParams.get('semester');
    const year = searchParams.get('year');

    // Get current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    // Get user profile to verify student role
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError || profile?.role !== 'student') {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Access denied'
      }, { status: 403 });
    }

    // Build query for grades
    let query = supabase
      .from('grades')
      .select(`
        id,
        type,
        name,
        points,
        max_points,
        percentage,
        letter_grade,
        comments,
        submitted_at,
        graded_at,
        created_at,
        enrollments!inner (
          id,
          student_id,
          courses!inner (
            id,
            code,
            name,
            semester,
            year,
            users!courses_instructor_id_fkey (
              first_name,
              last_name
            )
          )
        )
      `)
      .eq('enrollments.student_id', user.id);

    // Apply filters
    if (courseId) {
      query = query.eq('enrollments.courses.id', courseId);
    }
    if (semester) {
      query = query.eq('enrollments.courses.semester', semester);
    }
    if (year) {
      query = query.eq('enrollments.courses.year', parseInt(year));
    }

    // Apply pagination and ordering
    const { data: grades, error: gradesError } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (gradesError) {
      console.error('Error fetching grades:', gradesError);
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Failed to fetch grades'
      }, { status: 500 });
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('grades')
      .select('*', { count: 'exact', head: true })
      .eq('enrollments.student_id', user.id);

    if (courseId) {
      countQuery = countQuery.eq('enrollments.courses.id', courseId);
    }
    if (semester) {
      countQuery = countQuery.eq('enrollments.courses.semester', semester);
    }
    if (year) {
      countQuery = countQuery.eq('enrollments.courses.year', parseInt(year));
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error('Error counting grades:', countError);
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Failed to count grades'
      }, { status: 500 });
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json<PaginatedResponse>({
      success: true,
      data: grades,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages
      }
    });

  } catch (error) {
    console.error('Grades API error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
