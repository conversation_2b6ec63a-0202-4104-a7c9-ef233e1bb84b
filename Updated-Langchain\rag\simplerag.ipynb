{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(page_content='The world must be made safe for democracy. Its peace must be planted upon the tested foundations of political liberty. We have no selfish ends to serve. We desire no conquest, no dominion. We seek no indemnities for ourselves, no material compensation for the sacrifices we shall freely make. We are but one of the champions of the rights of mankind. We shall be satisfied when those rights have been made as secure as the faith and the freedom of nations can make them.\\n\\nJust because we fight without rancor and without selfish object, seeking nothing for ourselves but what we shall wish to share with all free peoples, we shall, I feel confident, conduct our operations as belligerents without passion and ourselves observe with proud punctilio the principles of right and of fair play we profess to be fighting for.\\n\\n…\\n\\nIt will be all the easier for us to conduct ourselves as belligerents in a high spirit of right and fairness because we act without animus, not in enmity toward a people or with the desire to bring any injury or disadvantage upon them, but only in armed opposition to an irresponsible government which has thrown aside all considerations of humanity and of right and is running amuck. We are, let me say again, the sincere friends of the German people, and shall desire nothing so much as the early reestablishment of intimate relations of mutual advantage between us—however hard it may be for them, for the time being, to believe that this is spoken from our hearts.\\n\\nWe have borne with their present government through all these bitter months because of that friendship—exercising a patience and forbearance which would otherwise have been impossible. We shall, happily, still have an opportunity to prove that friendship in our daily attitude and actions toward the millions of men and women of German birth and native sympathy who live among us and share our life, and we shall be proud to prove it toward all who are in fact loyal to their neighbors and to the government in the hour of test. They are, most of them, as true and loyal Americans as if they had never known any other fealty or allegiance. They will be prompt to stand with us in rebuking and restraining the few who may be of a different mind and purpose. If there should be disloyalty, it will be dealt with with a firm hand of stern repression; but, if it lifts its head at all, it will lift it only here and there and without countenance except from a lawless and malignant few.\\n\\nIt is a distressing and oppressive duty, gentlemen of the Congress, which I have performed in thus addressing you. There are, it may be, many months of fiery trial and sacrifice ahead of us. It is a fearful thing to lead this great peaceful people into war, into the most terrible and disastrous of all wars, civilization itself seeming to be in the balance. But the right is more precious than peace, and we shall fight for the things which we have always carried nearest our hearts—for democracy, for the right of those who submit to authority to have a voice in their own governments, for the rights and liberties of small nations, for a universal dominion of right by such a concert of free peoples as shall bring peace and safety to all nations and make the world itself at last free.\\n\\nTo such a task we can dedicate our lives and our fortunes, everything that we are and everything that we have, with the pride of those who know that the day has come when America is privileged to spend her blood and her might for the principles that gave her birth and happiness and the peace which she has treasured. God helping her, she can do no other.', metadata={'source': 'speech.txt'})]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["## Data Ingestion\n", "from langchain_community.document_loaders import TextLoader\n", "loader=TextLoader(\"speech.txt\")\n", "text_documents=loader.load()\n", "text_documents"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "\n", "os.environ['OPENAI_API_KEY']=os.getenv(\"OPENAI_API_KEY\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# web based loader\n", "from langchain_community.document_loaders import WebBaseLoader\n", "import bs4\n", "\n", "## load,chunk and index the content of the html page\n", "\n", "loader=WebBaseLoader(web_paths=(\"https://lilianweng.github.io/posts/2023-06-23-agent/\",),\n", "                     bs_kwargs=dict(parse_only=bs4.SoupStrainer(\n", "                         class_=(\"post-title\",\"post-content\",\"post-header\")\n", "\n", "                     )))\n", "\n", "text_documents=loader.load()\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(page_content='\\n\\n      LLM Powered Autonomous Agents\\n    \\nDate: June 23, 2023  |  Estimated Reading Time: 31 min  |  Author: <PERSON><PERSON>\\n\\n\\nBuilding agents with LLM (large language model) as its core controller is a cool concept. Several proof-of-concepts demos, such as <PERSON>GP<PERSON>, GPT-Engineer and BabyAGI, serve as inspiring examples. The potentiality of LLM extends beyond generating well-written copies, stories, essays and programs; it can be framed as a powerful general problem solver.\\nAgent System Overview#\\nIn a LLM-powered autonomous agent system, LLM functions as the agent’s brain, complemented by several key components:\\n\\nPlanning\\n\\nSubgoal and decomposition: The agent breaks down large tasks into smaller, manageable subgoals, enabling efficient handling of complex tasks.\\nReflection and refinement: The agent can do self-criticism and self-reflection over past actions, learn from mistakes and refine them for future steps, thereby improving the quality of final results.\\n\\n\\nMemory\\n\\nShort-term memory: I would consider all the in-context learning (See Prompt Engineering) as utilizing short-term memory of the model to learn.\\nLong-term memory: This provides the agent with the capability to retain and recall (infinite) information over extended periods, often by leveraging an external vector store and fast retrieval.\\n\\n\\nTool use\\n\\nThe agent learns to call external APIs for extra information that is missing from the model weights (often hard to change after pre-training), including current information, code execution capability, access to proprietary information sources and more.\\n\\n\\n\\n\\nFig. 1. Overview of a LLM-powered autonomous agent system.\\nComponent One: Planning#\\nA complicated task usually involves many steps. An agent needs to know what they are and plan ahead.\\nTask Decomposition#\\nChain of thought (CoT; Wei et al. 2022) has become a standard prompting technique for enhancing model performance on complex tasks. The model is instructed to “think step by step” to utilize more test-time computation to decompose hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.\\nTree of Thoughts (Yao et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.\\nTask decomposition can be done (1) by LLM with simple prompting like \"Steps for XYZ.\\\\n1.\", \"What are the subgoals for achieving XYZ?\", (2) by using task-specific instructions; e.g. \"Write a story outline.\" for writing a novel, or (3) with human inputs.\\nAnother quite distinct approach, LLM+P (Liu et al. 2023), involves relying on an external classical planner to do long-horizon planning. This approach utilizes the Planning Domain Definition Language (PDDL) as an intermediate interface to describe the planning problem. In this process, LLM (1) translates the problem into “Problem PDDL”, then (2) requests a classical planner to generate a PDDL plan based on an existing “Domain PDDL”, and finally (3) translates the PDDL plan back into natural language. Essentially, the planning step is outsourced to an external tool, assuming the availability of domain-specific PDDL and a suitable planner which is common in certain robotic setups but not in many other domains.\\nSelf-Reflection#\\nSelf-reflection is a vital aspect that allows autonomous agents to improve iteratively by refining past action decisions and correcting previous mistakes. It plays a crucial role in real-world tasks where trial and error are inevitable.\\nReAct (Yao et al. 2023) integrates reasoning and acting within LLM by extending the action space to be a combination of task-specific discrete actions and the language space. The former enables LLM to interact with the environment (e.g. use Wikipedia search API), while the latter prompting LLM to generate reasoning traces in natural language.\\nThe ReAct prompt template incorporates explicit steps for LLM to think, roughly formatted as:\\nThought: ...\\nAction: ...\\nObservation: ...\\n... (Repeated many times)\\n\\nFig. 2.  Examples of reasoning trajectories for knowledge-intensive tasks (e.g. HotpotQA, FEVER) and decision-making tasks (e.g. AlfWorld Env, WebShop). (Image source: Yao et al. 2023).\\nIn both experiments on knowledge-intensive tasks and decision-making tasks, ReAct works better than the Act-only baseline where Thought: … step is removed.\\nReflexion (Shinn & Labash 2023) is a framework to equips agents with dynamic memory and self-reflection capabilities to improve reasoning skills. Reflexion has a standard RL setup, in which the reward model provides a simple binary reward and the action space follows the setup in ReAct where the task-specific action space is augmented with language to enable complex reasoning steps. After each action $a_t$, the agent computes a heuristic $h_t$ and optionally may decide to reset the environment to start a new trial depending on the self-reflection results.\\n\\nFig. 3. Illustration of the Reflexion framework. (Image source: Shinn & Labash, 2023)\\nThe heuristic function determines when the trajectory is inefficient or contains hallucination and should be stopped. Inefficient planning refers to trajectories that take too long without success. Hallucination is defined as encountering a sequence of consecutive identical actions that lead to the same observation in the environment.\\nSelf-reflection is created by showing two-shot examples to LLM and each example is a pair of (failed trajectory, ideal reflection for guiding future changes in the plan). Then reflections are added into the agent’s working memory, up to three, to be used as context for querying LLM.\\n\\nFig. 4. Experiments on AlfWorld Env and HotpotQA. Hallucination is a more common failure than inefficient planning in AlfWorld. (Image source: Shinn & Labash, 2023)\\nChain of Hindsight (CoH; Liu et al. 2023) encourages the model to improve on its own outputs by explicitly presenting it with a sequence of past outputs, each annotated with feedback. Human feedback data is a collection of $D_h = \\\\{(x, y_i , r_i , z_i)\\\\}_{i=1}^n$, where $x$ is the prompt, each $y_i$ is a model completion, $r_i$ is the human rating of $y_i$, and $z_i$ is the corresponding human-provided hindsight feedback. Assume the feedback tuples are ranked by reward, $r_n \\\\geq r_{n-1} \\\\geq \\\\dots \\\\geq r_1$ The process is supervised fine-tuning where the data is a sequence in the form of $\\\\tau_h = (x, z_i, y_i, z_j, y_j, \\\\dots, z_n, y_n)$, where $\\\\leq i \\\\leq j \\\\leq n$. The model is finetuned to only predict $y_n$ where conditioned on the sequence prefix, such that the model can self-reflect to produce better output based on the feedback sequence. The model can optionally receive multiple rounds of instructions with human annotators at test time.\\nTo avoid overfitting, CoH adds a regularization term to maximize the log-likelihood of the pre-training dataset. To avoid shortcutting and copying (because there are many common words in feedback sequences), they randomly mask 0% - 5% of past tokens during training.\\nThe training dataset in their experiments is a combination of WebGPT comparisons, summarization from human feedback and human preference dataset.\\n\\nFig. 5. After fine-tuning with CoH, the model can follow instructions to produce outputs with incremental improvement in a sequence. (Image source: Liu et al. 2023)\\nThe idea of CoH is to present a history of sequentially improved outputs  in context and train the model to take on the trend to produce better outputs. Algorithm Distillation (AD; Laskin et al. 2023) applies the same idea to cross-episode trajectories in reinforcement learning tasks, where an algorithm is encapsulated in a long history-conditioned policy. Considering that an agent interacts with the environment many times and in each episode the agent gets a little better, AD concatenates this learning history and feeds that into the model. Hence we should expect the next predicted action to lead to better performance than previous trials. The goal is to learn the process of RL instead of training a task-specific policy itself.\\n\\nFig. 6. Illustration of how Algorithm Distillation (AD) works. (Image source: Laskin et al. 2023).\\nThe paper hypothesizes that any algorithm that generates a set of learning histories can be distilled into a neural network by performing behavioral cloning over actions. The history data is generated by a set of source policies, each trained for a specific task. At the training stage, during each RL run, a random task is sampled and a subsequence of multi-episode history is used for training, such that the learned policy is task-agnostic.\\nIn reality, the model has limited context window length, so episodes should be short enough to construct multi-episode history. Multi-episodic contexts of 2-4 episodes are necessary to learn a near-optimal in-context RL algorithm. The emergence of in-context RL requires long enough context.\\nIn comparison with three baselines, including ED (expert distillation, behavior cloning with expert trajectories instead of learning history), source policy (used for generating trajectories for distillation by UCB), RL^2 (Duan et al. 2017; used as upper bound since it needs online RL), AD demonstrates in-context RL with performance getting close to RL^2 despite only using offline RL and learns much faster than other baselines. When conditioned on partial training history of the source policy, AD also improves much faster than ED baseline.\\n\\nFig. 7. Comparison of AD, ED, source policy and RL^2 on environments that require memory and exploration. Only binary reward is assigned. The source policies are trained with A3C for \"dark\" environments and DQN for watermaze.(Image source: Laskin et al. 2023)\\nComponent Two: Memory#\\n(Big thank you to ChatGPT for helping me draft this section. I’ve learned a lot about the human brain and data structure for fast MIPS in my conversations with ChatGPT.)\\nTypes of Memory#\\nMemory can be defined as the processes used to acquire, store, retain, and later retrieve information. There are several types of memory in human brains.\\n\\n\\nSensory Memory: This is the earliest stage of memory, providing the ability to retain impressions of sensory information (visual, auditory, etc) after the original stimuli have ended. Sensory memory typically only lasts for up to a few seconds. Subcategories include iconic memory (visual), echoic memory (auditory), and haptic memory (touch).\\n\\n\\nShort-Term Memory (STM) or Working Memory: It stores information that we are currently aware of and needed to carry out complex cognitive tasks such as learning and reasoning. Short-term memory is believed to have the capacity of about 7 items (Miller 1956) and lasts for 20-30 seconds.\\n\\n\\nLong-Term Memory (LTM): Long-term memory can store information for a remarkably long time, ranging from a few days to decades, with an essentially unlimited storage capacity. There are two subtypes of LTM:\\n\\nExplicit / declarative memory: This is memory of facts and events, and refers to those memories that can be consciously recalled, including episodic memory (events and experiences) and semantic memory (facts and concepts).\\nImplicit / procedural memory: This type of memory is unconscious and involves skills and routines that are performed automatically, like riding a bike or typing on a keyboard.\\n\\n\\n\\n\\nFig. 8. Categorization of human memory.\\nWe can roughly consider the following mappings:\\n\\nSensory memory as learning embedding representations for raw inputs, including text, image or other modalities;\\nShort-term memory as in-context learning. It is short and finite, as it is restricted by the finite context window length of Transformer.\\nLong-term memory as the external vector store that the agent can attend to at query time, accessible via fast retrieval.\\n\\nMaximum Inner Product Search (MIPS)#\\nThe external memory can alleviate the restriction of finite attention span.  A standard practice is to save the embedding representation of information into a vector store database that can support fast maximum inner-product search (MIPS). To optimize the retrieval speed, the common choice is the approximate nearest neighbors (ANN)\\u200b algorithm to return approximately top k nearest neighbors to trade off a little accuracy lost for a huge speedup.\\nA couple common choices of ANN algorithms for fast MIPS:\\n\\nLSH (Locality-Sensitive Hashing): It introduces a hashing function such that similar input items are mapped to the same buckets with high probability, where the number of buckets is much smaller than the number of inputs.\\nANNOY (Approximate Nearest Neighbors Oh Yeah): The core data structure are random projection trees, a set of binary trees where each non-leaf node represents a hyperplane splitting the input space into half and each leaf stores one data point. Trees are built independently and at random, so to some extent, it mimics a hashing function. ANNOY search happens in all the trees to iteratively search through the half that is closest to the query and then aggregates the results. The idea is quite related to KD tree but a lot more scalable.\\nHNSW (Hierarchical Navigable Small World): It is inspired by the idea of small world networks where most nodes can be reached by any other nodes within a small number of steps; e.g. “six degrees of separation” feature of social networks. HNSW builds hierarchical layers of these small-world graphs, where the bottom layers contain the actual data points. The layers in the middle create shortcuts to speed up search. When performing a search, HNSW starts from a random node in the top layer and navigates towards the target. When it can’t get any closer, it moves down to the next layer, until it reaches the bottom layer. Each move in the upper layers can potentially cover a large distance in the data space, and each move in the lower layers refines the search quality.\\nFAISS (Facebook AI Similarity Search): It operates on the assumption that in high dimensional space, distances between nodes follow a Gaussian distribution and thus there should exist clustering of data points. FAISS applies vector quantization by partitioning the vector space into clusters and then refining the quantization within clusters. Search first looks for cluster candidates with coarse quantization and then further looks into each cluster with finer quantization.\\nScaNN (Scalable Nearest Neighbors): The main innovation in ScaNN is anisotropic vector quantization. It quantizes a data point $x_i$ to $\\\\tilde{x}_i$ such that the inner product $\\\\langle q, x_i \\\\rangle$ is as similar to the original distance of $\\\\angle q, \\\\tilde{x}_i$ as possible, instead of picking the closet quantization centroid points.\\n\\n\\nFig. 9. Comparison of MIPS algorithms, measured in recall@10. (Image source: Google Blog, 2020)\\nCheck more MIPS algorithms and performance comparison in ann-benchmarks.com.\\nComponent Three: Tool Use#\\nTool use is a remarkable and distinguishing characteristic of human beings. We create, modify and utilize external objects to do things that go beyond our physical and cognitive limits. Equipping LLMs with external tools can significantly extend the model capabilities.\\n\\nFig. 10. A picture of a sea otter using rock to crack open a seashell, while floating in the water. While some other animals can use tools, the complexity is not comparable with humans. (Image source: Animals using tools)\\nMRKL (Karpas et al. 2022), short for “Modular Reasoning, Knowledge and Language”, is a neuro-symbolic architecture for autonomous agents. A MRKL system is proposed to contain a collection of “expert” modules and the general-purpose LLM works as a router to route inquiries to the best suitable expert module. These modules can be neural (e.g. deep learning models) or symbolic (e.g. math calculator, currency converter, weather API).\\nThey did an experiment on fine-tuning LLM to call a calculator, using arithmetic as a test case. Their experiments showed that it was harder to solve verbal math problems than explicitly stated math problems because LLMs (7B Jurassic1-large model) failed to extract the right arguments for the basic arithmetic reliably. The results highlight when the external symbolic tools can work reliably, knowing when to and how to use the tools are crucial, determined by the LLM capability.\\nBoth TALM (Tool Augmented Language Models; Parisi et al. 2022) and Toolformer (Schick et al. 2023) fine-tune a LM to learn to use external tool APIs. The dataset is expanded based on whether a newly added API call annotation can improve the quality of model outputs. See more details in the “External APIs” section of Prompt Engineering.\\nChatGPT Plugins and OpenAI API  function calling are good examples of LLMs augmented with tool use capability working in practice. The collection of tool APIs can be provided by other developers (as in Plugins) or self-defined (as in function calls).\\nHuggingGPT (Shen et al. 2023) is a framework to use ChatGPT as the task planner to select models available in HuggingFace platform according to the model descriptions and summarize the response based on the execution results.\\n\\nFig. 11. Illustration of how HuggingGPT works. (Image source: Shen et al. 2023)\\nThe system comprises of 4 stages:\\n(1) Task planning: LLM works as the brain and parses the user requests into multiple tasks. There are four attributes associated with each task: task type, ID, dependencies, and arguments. They use few-shot examples to guide LLM to do task parsing and planning.\\nInstruction:\\n\\nThe AI assistant can parse user input to several tasks: [{\"task\": task, \"id\", task_id, \"dep\": dependency_task_ids, \"args\": {\"text\": text, \"image\": URL, \"audio\": URL, \"video\": URL}}]. The \"dep\" field denotes the id of the previous task which generates a new resource that the current task relies on. A special tag \"-task_id\" refers to the generated text image, audio and video in the dependency task with id as task_id. The task MUST be selected from the following options: {{ Available Task List }}. There is a logical relationship between tasks, please note their order. If the user input can\\'t be parsed, you need to reply empty JSON. Here are several cases for your reference: {{ Demonstrations }}. The chat history is recorded as {{ Chat History }}. From this chat history, you can find the path of the user-mentioned resources for your task planning.\\n\\n(2) Model selection: LLM distributes the tasks to expert models, where the request is framed as a multiple-choice question. LLM is presented with a list of models to choose from. Due to the limited context length, task type based filtration is needed.\\nInstruction:\\n\\nGiven the user request and the call command, the AI assistant helps the user to select a suitable model from a list of models to process the user request. The AI assistant merely outputs the model id of the most appropriate model. The output must be in a strict JSON format: \"id\": \"id\", \"reason\": \"your detail reason for the choice\". We have a list of models for you to choose from {{ Candidate Models }}. Please select one model from the list.\\n\\n(3) Task execution: Expert models execute on the specific tasks and log results.\\nInstruction:\\n\\nWith the input and the inference results, the AI assistant needs to describe the process and results. The previous stages can be formed as - User Input: {{ User Input }}, Task Planning: {{ Tasks }}, Model Selection: {{ Model Assignment }}, Task Execution: {{ Predictions }}. You must first answer the user\\'s request in a straightforward manner. Then describe the task process and show your analysis and model inference results to the user in the first person. If inference results contain a file path, must tell the user the complete file path.\\n\\n(4) Response generation: LLM receives the execution results and provides summarized results to users.\\nTo put HuggingGPT into real world usage, a couple challenges need to solve: (1) Efficiency improvement is needed as both LLM inference rounds and interactions with other models slow down the process; (2) It relies on a long context window to communicate over complicated task content; (3) Stability improvement of LLM outputs and external model services.\\nAPI-Bank (Li et al. 2023) is a benchmark for evaluating the performance of tool-augmented LLMs. It contains 53 commonly used API tools, a complete tool-augmented LLM workflow, and 264 annotated dialogues that involve 568 API calls. The selection of APIs is quite diverse, including search engines, calculator, calendar queries, smart home control, schedule management, health data management, account authentication workflow and more. Because there are a large number of APIs, LLM first has access to API search engine to find the right API to call and then uses the corresponding documentation to make a call.\\n\\nFig. 12. Pseudo code of how LLM makes an API call in API-Bank. (Image source: Li et al. 2023)\\nIn the API-Bank workflow, LLMs need to make a couple of decisions and at each step we can evaluate how accurate that decision is. Decisions include:\\n\\nWhether an API call is needed.\\nIdentify the right API to call: if not good enough, LLMs need to iteratively modify the API inputs (e.g. deciding search keywords for Search Engine API).\\nResponse based on the API results: the model can choose to refine and call again if results are not satisfied.\\n\\nThis benchmark evaluates the agent’s tool use capabilities at three levels:\\n\\nLevel-1 evaluates the ability to call the API. Given an API’s description, the model needs to determine whether to call a given API, call it correctly, and respond properly to API returns.\\nLevel-2 examines the ability to retrieve the API. The model needs to search for possible APIs that may solve the user’s requirement and learn how to use them by reading documentation.\\nLevel-3 assesses the ability to plan API beyond retrieve and call. Given unclear user requests (e.g. schedule group meetings, book flight/hotel/restaurant for a trip), the model may have to conduct multiple API calls to solve it.\\n\\nCase Studies#\\nScientific Discovery Agent#\\nChemCrow (Bran et al. 2023) is a domain-specific example in which LLM is augmented with 13 expert-designed tools to accomplish tasks across organic synthesis, drug discovery, and materials design. The workflow, implemented in LangChain, reflects what was previously described in the ReAct and MRKLs and combines CoT reasoning with tools relevant to the tasks:\\n\\nThe LLM is provided with a list of tool names, descriptions of their utility, and details about the expected input/output.\\nIt is then instructed to answer a user-given prompt using the tools provided when necessary. The instruction suggests the model to follow the ReAct format - Thought, Action, Action Input, Observation.\\n\\nOne interesting observation is that while the LLM-based evaluation concluded that GPT-4 and ChemCrow perform nearly equivalently, human evaluations with experts oriented towards the completion and chemical correctness of the solutions showed that ChemCrow outperforms GPT-4 by a large margin. This indicates a potential problem with using LLM to evaluate its own performance on domains that requires deep expertise. The lack of expertise may cause LLMs not knowing its flaws and thus cannot well judge the correctness of task results.\\nBoiko et al. (2023) also looked into LLM-empowered agents for scientific discovery, to handle autonomous design, planning, and performance of complex scientific experiments. This agent can use tools to browse the Internet, read documentation, execute code, call robotics experimentation APIs and leverage other LLMs.\\nFor example, when requested to \"develop a novel anticancer drug\", the model came up with the following reasoning steps:\\n\\ninquired about current trends in anticancer drug discovery;\\nselected a target;\\nrequested a scaffold targeting these compounds;\\nOnce the compound was identified, the model attempted its synthesis.\\n\\nThey also discussed the risks, especially with illicit drugs and bioweapons. They developed a test set containing a list of known chemical weapon agents and asked the agent to synthesize them. 4 out of 11 requests (36%) were accepted to obtain a synthesis solution and the agent attempted to consult documentation to execute the procedure. 7 out of 11 were rejected and among these 7 rejected cases, 5 happened after a Web search while 2 were rejected based on prompt only.\\nGenerative Agents Simulation#\\nGenerative Agents (Park, et al. 2023) is super fun experiment where 25 virtual characters, each controlled by a LLM-powered agent, are living and interacting in a sandbox environment, inspired by The Sims. Generative agents create believable simulacra of human behavior for interactive applications.\\nThe design of generative agents combines LLM with memory, planning and reflection mechanisms to enable agents to behave conditioned on past experience, as well as to interact with other agents.\\n\\nMemory stream: is a long-term memory module (external database) that records a comprehensive list of agents’ experience in natural language.\\n\\nEach element is an observation, an event directly provided by the agent.\\n- Inter-agent communication can trigger new natural language statements.\\n\\n\\nRetrieval model: surfaces the context to inform the agent’s behavior, according to relevance, recency and importance.\\n\\nRecency: recent events have higher scores\\nImportance: distinguish mundane from core memories. Ask LM directly.\\nRelevance: based on how related it is to the current situation / query.\\n\\n\\nReflection mechanism: synthesizes memories into higher level inferences over time and guides the agent’s future behavior. They are higher-level summaries of past events (<- note that this is a bit different from self-reflection above)\\n\\nPrompt LM with 100 most recent observations and to generate 3 most salient high-level questions given a set of observations/statements. Then ask LM to answer those questions.\\n\\n\\nPlanning & Reacting: translate the reflections and the environment information into actions\\n\\nPlanning is essentially in order to optimize believability at the moment vs in time.\\nPrompt template: {Intro of an agent X}. Here is X\\'s plan today in broad strokes: 1)\\nRelationships between agents and observations of one agent by another are all taken into consideration for planning and reacting.\\nEnvironment information is present in a tree structure.\\n\\n\\n\\n\\nFig. 13. The generative agent architecture. (Image source: Park et al. 2023)\\nThis fun simulation results in emergent social behavior, such as information diffusion, relationship memory (e.g. two agents continuing the conversation topic) and coordination of social events (e.g. host a party and invite many others).\\nProof-of-Concept Examples#\\nAutoGPT has drawn a lot of attention into the possibility of setting up autonomous agents with LLM as the main controller. It has quite a lot of reliability issues given the natural language interface, but nevertheless a cool proof-of-concept demo. A lot of code in AutoGPT is about format parsing.\\nHere is the system message used by AutoGPT, where {{...}} are user inputs:\\nYou are {{ai-name}}, {{user-provided AI bot description}}.\\nYour decisions must always be made independently without seeking user assistance. Play to your strengths as an LLM and pursue simple strategies with no legal complications.\\n\\nGOALS:\\n\\n1. {{user-provided goal 1}}\\n2. {{user-provided goal 2}}\\n3. ...\\n4. ...\\n5. ...\\n\\nConstraints:\\n1. ~4000 word limit for short term memory. Your short term memory is short, so immediately save important information to files.\\n2. If you are unsure how you previously did something or want to recall past events, thinking about similar events will help you remember.\\n3. No user assistance\\n4. Exclusively use the commands listed in double quotes e.g. \"command name\"\\n5. Use subprocesses for commands that will not terminate within a few minutes\\n\\nCommands:\\n1. Google Search: \"google\", args: \"input\": \"<search>\"\\n2. Browse Website: \"browse_website\", args: \"url\": \"<url>\", \"question\": \"<what_you_want_to_find_on_website>\"\\n3. Start GPT Agent: \"start_agent\", args: \"name\": \"<name>\", \"task\": \"<short_task_desc>\", \"prompt\": \"<prompt>\"\\n4. Message GPT Agent: \"message_agent\", args: \"key\": \"<key>\", \"message\": \"<message>\"\\n5. List GPT Agents: \"list_agents\", args:\\n6. Delete GPT Agent: \"delete_agent\", args: \"key\": \"<key>\"\\n7. Clone Repository: \"clone_repository\", args: \"repository_url\": \"<url>\", \"clone_path\": \"<directory>\"\\n8. Write to file: \"write_to_file\", args: \"file\": \"<file>\", \"text\": \"<text>\"\\n9. Read file: \"read_file\", args: \"file\": \"<file>\"\\n10. Append to file: \"append_to_file\", args: \"file\": \"<file>\", \"text\": \"<text>\"\\n11. Delete file: \"delete_file\", args: \"file\": \"<file>\"\\n12. Search Files: \"search_files\", args: \"directory\": \"<directory>\"\\n13. Analyze Code: \"analyze_code\", args: \"code\": \"<full_code_string>\"\\n14. Get Improved Code: \"improve_code\", args: \"suggestions\": \"<list_of_suggestions>\", \"code\": \"<full_code_string>\"\\n15. Write Tests: \"write_tests\", args: \"code\": \"<full_code_string>\", \"focus\": \"<list_of_focus_areas>\"\\n16. Execute Python File: \"execute_python_file\", args: \"file\": \"<file>\"\\n17. Generate Image: \"generate_image\", args: \"prompt\": \"<prompt>\"\\n18. Send Tweet: \"send_tweet\", args: \"text\": \"<text>\"\\n19. Do Nothing: \"do_nothing\", args:\\n20. Task Complete (Shutdown): \"task_complete\", args: \"reason\": \"<reason>\"\\n\\nResources:\\n1. Internet access for searches and information gathering.\\n2. Long Term memory management.\\n3. GPT-3.5 powered Agents for delegation of simple tasks.\\n4. File output.\\n\\nPerformance Evaluation:\\n1. Continuously review and analyze your actions to ensure you are performing to the best of your abilities.\\n2. Constructively self-criticize your big-picture behavior constantly.\\n3. Reflect on past decisions and strategies to refine your approach.\\n4. Every command has a cost, so be smart and efficient. Aim to complete tasks in the least number of steps.\\n\\nYou should only respond in JSON format as described below\\nResponse Format:\\n{\\n    \"thoughts\": {\\n        \"text\": \"thought\",\\n        \"reasoning\": \"reasoning\",\\n        \"plan\": \"- short bulleted\\\\n- list that conveys\\\\n- long-term plan\",\\n        \"criticism\": \"constructive self-criticism\",\\n        \"speak\": \"thoughts summary to say to user\"\\n    },\\n    \"command\": {\\n        \"name\": \"command name\",\\n        \"args\": {\\n            \"arg name\": \"value\"\\n        }\\n    }\\n}\\nEnsure the response can be parsed by Python json.loads\\nGPT-Engineer is another project to create a whole repository of code given a task specified in natural language. The GPT-Engineer is instructed to think over a list of smaller components to build and ask for user input to clarify questions as needed.\\nHere are a sample conversation for task clarification sent to OpenAI ChatCompletion endpoint used by GPT-Engineer. The user inputs are wrapped in {{user input text}}.\\n[\\n  {\\n    \"role\": \"system\",\\n    \"content\": \"You will read instructions and not carry them out, only seek to clarify them.\\\\nSpecifically you will first summarise a list of super short bullets of areas that need clarification.\\\\nThen you will pick one clarifying question, and wait for an answer from the user.\\\\n\"\\n  },\\n  {\\n    \"role\": \"user\",\\n    \"content\": \"We are writing {{a Super Mario game in python. MVC components split in separate files. Keyboard control.}}\\\\n\"\\n  },\\n  {\\n    \"role\": \"assistant\",\\n    \"content\": \"Summary of areas that need clarification:\\\\n1. Specifics of the Super Mario game (e.g. level design, characters, gameplay mechanics)\\\\n2. Details about the MVC components (e.g. which components are in each file)\\\\n3. Keyboard control implementation (e.g. which keys to use, how to handle input)\\\\n\\\\nClarifying question:\\\\nCan you provide more details about the Super Mario game, such as level design, characters, and gameplay mechanics?\"\\n  },\\n  {\\n    \"role\": \"user\",\\n    \"content\": \"{{There are 10 levels in total. The main character is a plumber named Mario, who can walk and jump. It is a classical platform game just like Super Mario. The main character moves from left to right, trying to get to the destination, where there are many obstacles and attacks from enemies in the process.}}\\\\n\\\\nIs anything else unclear? If yes, only answer in the form:\\\\n{remaining unclear areas} remaining questions.\\\\n{Next question}\\\\nIf everything is sufficiently clear, only answer \\\\\"Nothing more to clarify.\\\\\".\"\\n  },\\n  {\\n    \"role\": \"assistant\",\\n    \"content\": \"Remaining unclear areas: 2 remaining questions.\\\\nCan you provide more information about how the MVC components are split into separate files?\"\\n  },\\n  {\\n    \"role\": \"user\",\\n    \"content\": \"{{Make your own assumptions and state them explicitly before starting}}\"\\n  }\\n]\\nThen after these clarification, the agent moved into the code writing mode with a different system message.\\nSystem message:\\n\\nYou will get instructions for code to write.\\nYou will write a very long answer. Make sure that every detail of the architecture is, in the end, implemented as code.\\nMake sure that every detail of the architecture is, in the end, implemented as code.\\nThink step by step and reason yourself to the right decisions to make sure we get it right.\\nYou will first lay out the names of the core classes, functions, methods that will be necessary, as well as a quick comment on their purpose.\\nThen you will output the content of each file including ALL code.\\nEach file must strictly follow a markdown code block format, where the following tokens must be replaced such that\\nFILENAME is the lowercase file name including the file extension,\\nLANG is the markup code block language for the code’s language, and CODE is the code:\\nFILENAME\\nCODE\\nYou will start with the “entrypoint” file, then go to the ones that are imported by that file, and so on.\\nPlease note that the code should be fully functional. No placeholders.\\nFollow a language and framework appropriate best practice file naming convention.\\nMake sure that files contain all imports, types etc. Make sure that code in different files are compatible with each other.\\nEnsure to implement all code, if you are unsure, write a plausible implementation.\\nInclude module dependency or package manager dependency definition file.\\nBefore you finish, double check that all parts of the architecture is present in the files.\\nUseful to know:\\nYou almost always put different classes in different files.\\nFor Python, you always create an appropriate requirements.txt file.\\nFor NodeJS, you always create an appropriate package.json file.\\nYou always add a comment briefly describing the purpose of the function definition.\\nYou try to add comments explaining very complex bits of logic.\\nYou always follow the best practices for the requested languages in terms of describing the code written as a defined\\npackage/project.\\nPython toolbelt preferences:\\n\\npytest\\ndataclasses\\n\\n\\nConversatin samples:\\n[\\n  {\\n    \"role\": \"system\",\\n    \"content\": \"You will get instructions for code to write.\\\\nYou will write a very long answer. Make sure that every detail of the architecture is, in the end, implemented as code.\\\\nMake sure that every detail of the architecture is, in the end, implemented as code.\\\\n\\\\nThink step by step and reason yourself to the right decisions to make sure we get it right.\\\\nYou will first lay out the names of the core classes, functions, methods that will be necessary, as well as a quick comment on their purpose.\\\\n\\\\nThen you will output the content of each file including ALL code.\\\\nEach file must strictly follow a markdown code block format, where the following tokens must be replaced such that\\\\nFILENAME is the lowercase file name including the file extension,\\\\nLANG is the markup code block language for the code\\'s language, and CODE is the code:\\\\n\\\\nFILENAME\\\\n```LANG\\\\nCODE\\\\n```\\\\n\\\\nYou will start with the \\\\\"entrypoint\\\\\" file, then go to the ones that are imported by that file, and so on.\\\\nPlease note that the code should be fully functional. No placeholders.\\\\n\\\\nFollow a language and framework appropriate best practice file naming convention.\\\\nMake sure that files contain all imports, types etc. Make sure that code in different files are compatible with each other.\\\\nEnsure to implement all code, if you are unsure, write a plausible implementation.\\\\nInclude module dependency or package manager dependency definition file.\\\\nBefore you finish, double check that all parts of the architecture is present in the files.\\\\n\\\\nUseful to know:\\\\nYou almost always put different classes in different files.\\\\nFor Python, you always create an appropriate requirements.txt file.\\\\nFor NodeJS, you always create an appropriate package.json file.\\\\nYou always add a comment briefly describing the purpose of the function definition.\\\\nYou try to add comments explaining very complex bits of logic.\\\\nYou always follow the best practices for the requested languages in terms of describing the code written as a defined\\\\npackage/project.\\\\n\\\\n\\\\nPython toolbelt preferences:\\\\n- pytest\\\\n- dataclasses\\\\n\"\\n  },\\n #  … same conversation as earlier, ended with \"Make your own assumptions and state them explicitly before starting\".\\n  {\\n    \"role\": \"assistant\",\\n    \"content\": \"Assumptions:\\\\n1. Model: The model will contain the game\\'s data, such as level information, character states, and enemy positions.\\\\n2. View: The view will handle the game\\'s visuals, including rendering the game objects, backgrounds, and updating the display.\\\\n3. Controller: The controller will manage user input, such as keyboard controls, and update the model accordingly.\\\\n\\\\nWith these assumptions in mind, is there any specific aspect of the keyboard control implementation you would like me to clarify?\"\\n  },\\n  {\\n    \"role\": \"user\",\\n    \"content\": \"Please now remember the steps:\\\\n\\\\nThink step by step and reason yourself to the right decisions to make sure we get it right.\\\\nFirst lay out the names of the core classes, functions, methods that will be necessary, As well as a quick comment on their purpose.\\\\n\\\\nThen you will output the content of each file including ALL code.\\\\nEach file must strictly follow a markdown code block format, where the following tokens must be replaced such that\\\\nFILENAME is the lowercase file name including the file extension,\\\\nLANG is the markup code block language for the code\\'s language, and CODE is the code:\\\\n\\\\nFILENAME\\\\n```LANG\\\\nCODE\\\\n```\\\\n\\\\nPlease note that the code should be fully functional. No placeholders.\\\\n\\\\nYou will start with the \\\\\"entrypoint\\\\\" file, then go to the ones that are imported by that file, and so on.\\\\nFollow a language and framework appropriate best practice file naming convention.\\\\nMake sure that files contain all imports, types etc. The code should be fully functional. Make sure that code in different files are compatible with each other.\\\\nBefore you finish, double check that all parts of the architecture is present in the files.\\\\n\"\\n  }\\n]\\nChallenges#\\nAfter going through key ideas and demos of building LLM-centered agents, I start to see a couple common limitations:\\n\\n\\nFinite context length: The restricted context capacity limits the inclusion of historical information, detailed instructions, API call context, and responses. The design of the system has to work with this limited communication bandwidth, while mechanisms like self-reflection to learn from past mistakes would benefit a lot from long or infinite context windows. Although vector stores and retrieval can provide access to a larger knowledge pool, their representation power is not as powerful as full attention.\\n\\n\\nChallenges in long-term planning and task decomposition: Planning over a lengthy history and effectively exploring the solution space remain challenging. LLMs struggle to adjust plans when faced with unexpected errors, making them less robust compared to humans who learn from trial and error.\\n\\n\\nReliability of natural language interface: Current agent system relies on natural language as an interface between LLMs and external components such as memory and tools. However, the reliability of model outputs is questionable, as LLMs may make formatting errors and occasionally exhibit rebellious behavior (e.g. refuse to follow an instruction). Consequently, much of the agent demo code focuses on parsing model output.\\n\\n\\nCitation#\\nCited as:\\n\\nWeng, Lilian. (Jun 2023). “LLM-powered Autonomous Agents”. Lil’Log. https://lilianweng.github.io/posts/2023-06-23-agent/.\\n\\nOr\\n@article{weng2023agent,\\n  title   = \"LLM-powered Autonomous Agents\",\\n  author  = \"Weng, Lilian\",\\n  journal = \"lilianweng.github.io\",\\n  year    = \"2023\",\\n  month   = \"Jun\",\\n  url     = \"https://lilianweng.github.io/posts/2023-06-23-agent/\"\\n}\\nReferences#\\n[1] Wei et al. “Chain of thought prompting elicits reasoning in large language models.” NeurIPS 2022\\n[2] Yao et al. “Tree of Thoughts: Dliberate Problem Solving with Large Language Models.” arXiv preprint arXiv:2305.10601 (2023).\\n[3] Liu et al. “Chain of Hindsight Aligns Language Models with Feedback\\n“ arXiv preprint arXiv:2302.02676 (2023).\\n[4] Liu et al. “LLM+P: Empowering Large Language Models with Optimal Planning Proficiency” arXiv preprint arXiv:2304.11477 (2023).\\n[5] Yao et al. “ReAct: Synergizing reasoning and acting in language models.” ICLR 2023.\\n[6] Google Blog. “Announcing ScaNN: Efficient Vector Similarity Search” July 28, 2020.\\n[7] https://chat.openai.com/share/46ff149e-a4c7-4dd7-a800-fc4a642ea389\\n[8] Shinn & Labash. “Reflexion: an autonomous agent with dynamic memory and self-reflection” arXiv preprint arXiv:2303.11366 (2023).\\n[9] Laskin et al. “In-context Reinforcement Learning with Algorithm Distillation” ICLR 2023.\\n[10] Karpas et al. “MRKL Systems A modular, neuro-symbolic architecture that combines large language models, external knowledge sources and discrete reasoning.” arXiv preprint arXiv:2205.00445 (2022).\\n[11] Weaviate Blog. Why is Vector Search so fast? Sep 13, 2022.\\n[12] Li et al. “API-Bank: A Benchmark for Tool-Augmented LLMs” arXiv preprint arXiv:2304.08244 (2023).\\n[13] Shen et al. “HuggingGPT: Solving AI Tasks with ChatGPT and its Friends in HuggingFace” arXiv preprint arXiv:2303.17580 (2023).\\n[14] Bran et al. “ChemCrow: Augmenting large-language models with chemistry tools.” arXiv preprint arXiv:2304.05376 (2023).\\n[15] Boiko et al. “Emergent autonomous scientific research capabilities of large language models.” arXiv preprint arXiv:2304.05332 (2023).\\n[16] Joon Sung Park, et al. “Generative Agents: Interactive Simulacra of Human Behavior.” arXiv preprint arXiv:2304.03442 (2023).\\n[17] AutoGPT. https://github.com/Significant-Gravitas/Auto-GPT\\n[18] GPT-Engineer. https://github.com/AntonOsika/gpt-engineer\\n', metadata={'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'})]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["text_documents"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["## Pdf reader\n", "from langchain_community.document_loaders import PyPDFLoader\n", "loader=PyPDFLoader('attention.pdf')\n", "docs=loader.load()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(page_content='Provided proper attribution is provided, Google hereby grants permission to\\nreproduce the tables and figures in this paper solely for use in journalistic or\\nscholarly works.\\nAttention Is All You Need\\nAshish Vaswani∗\\nGoogle Brain\\<EMAIL> Shazeer∗\\nGoogle Brain\\<EMAIL> Parmar∗\\nGoogle Research\\<EMAIL> <PERSON>z<PERSON>eit∗\\nGoogle Research\\<EMAIL>\\nLlion Jones∗\\nGoogle Research\\<EMAIL> N. Gomez∗ †\\nUniversity of Toronto\\<EMAIL>.eduŁ<PERSON>sz Kaiser∗\\nGoogle Brain\\<EMAIL>\\nIllia Polosukhin∗ ‡\\<EMAIL>\\nAbstract\\nThe dominant sequence transduction models are based on complex recurrent or\\nconvolutional neural networks that include an encoder and a decoder. The best\\nperforming models also connect the encoder and decoder through an attention\\nmechanism. We propose a new simple network architecture, the Transformer,\\nbased solely on attention mechanisms, dispensing with recurrence and convolutions\\nentirely. Experiments on two machine translation tasks show these models to\\nbe superior in quality while being more parallelizable and requiring significantly\\nless time to train. Our model achieves 28.4 BLEU on the WMT 2014 English-\\nto-German translation task, improving over the existing best results, including\\nensembles, by over 2 BLEU. On the WMT 2014 English-to-French translation task,\\nour model establishes a new single-model state-of-the-art BLEU score of 41.8 after\\ntraining for 3.5 days on eight GPUs, a small fraction of the training costs of the\\nbest models from the literature. We show that the Transformer generalizes well to\\nother tasks by applying it successfully to English constituency parsing both with\\nlarge and limited training data.\\n∗Equal contribution. Listing order is random. Jakob proposed replacing RNNs with self-attention and started\\nthe effort to evaluate this idea. Ashish, with Illia, designed and implemented the first Transformer models and\\nhas been crucially involved in every aspect of this work. Noam proposed scaled dot-product attention, multi-head\\nattention and the parameter-free position representation and became the other person involved in nearly every\\ndetail. Niki designed, implemented, tuned and evaluated countless model variants in our original codebase and\\ntensor2tensor. Llion also experimented with novel model variants, was responsible for our initial codebase, and\\nefficient inference and visualizations. Lukasz and Aidan spent countless long days designing various parts of and\\nimplementing tensor2tensor, replacing our earlier codebase, greatly improving results and massively accelerating\\nour research.\\n†Work performed while at Google Brain.\\n‡Work performed while at Google Research.\\n31st Conference on Neural Information Processing Systems (NIPS 2017), Long Beach, CA, USA.arXiv:1706.03762v7  [cs.CL]  2 Aug 2023', metadata={'source': 'attention.pdf', 'page': 0}),\n", " Document(page_content='1 Introduction\\nRecurrent neural networks, long short-term memory [ 13] and gated recurrent [ 7] neural networks\\nin particular, have been firmly established as state of the art approaches in sequence modeling and\\ntransduction problems such as language modeling and machine translation [ 35,2,5]. Numerous\\nefforts have since continued to push the boundaries of recurrent language models and encoder-decoder\\narchitectures [38, 24, 15].\\nRecurrent models typically factor computation along the symbol positions of the input and output\\nsequences. Aligning the positions to steps in computation time, they generate a sequence of hidden\\nstates ht, as a function of the previous hidden state ht−1and the input for position t. This inherently\\nsequential nature precludes parallelization within training examples, which becomes critical at longer\\nsequence lengths, as memory constraints limit batching across examples. Recent work has achieved\\nsignificant improvements in computational efficiency through factorization tricks [ 21] and conditional\\ncomputation [ 32], while also improving model performance in case of the latter. The fundamental\\nconstraint of sequential computation, however, remains.\\nAttention mechanisms have become an integral part of compelling sequence modeling and transduc-\\ntion models in various tasks, allowing modeling of dependencies without regard to their distance in\\nthe input or output sequences [ 2,19]. In all but a few cases [ 27], however, such attention mechanisms\\nare used in conjunction with a recurrent network.\\nIn this work we propose the Transformer, a model architecture eschewing recurrence and instead\\nrelying entirely on an attention mechanism to draw global dependencies between input and output.\\nThe Transformer allows for significantly more parallelization and can reach a new state of the art in\\ntranslation quality after being trained for as little as twelve hours on eight P100 GPUs.\\n2 Background\\nThe goal of reducing sequential computation also forms the foundation of the Extended Neural GPU\\n[16], ByteNet [ 18] and ConvS2S [ 9], all of which use convolutional neural networks as basic building\\nblock, computing hidden representations in parallel for all input and output positions. In these models,\\nthe number of operations required to relate signals from two arbitrary input or output positions grows\\nin the distance between positions, linearly for ConvS2S and logarithmically for ByteNet. This makes\\nit more difficult to learn dependencies between distant positions [ 12]. In the Transformer this is\\nreduced to a constant number of operations, albeit at the cost of reduced effective resolution due\\nto averaging attention-weighted positions, an effect we counteract with Multi-Head Attention as\\ndescribed in section 3.2.\\nSelf-attention, sometimes called intra-attention is an attention mechanism relating different positions\\nof a single sequence in order to compute a representation of the sequence. Self-attention has been\\nused successfully in a variety of tasks including reading comprehension, abstractive summarization,\\ntextual entailment and learning task-independent sentence representations [4, 27, 28, 22].\\nEnd-to-end memory networks are based on a recurrent attention mechanism instead of sequence-\\naligned recurrence and have been shown to perform well on simple-language question answering and\\nlanguage modeling tasks [34].\\nTo the best of our knowledge, however, the Transformer is the first transduction model relying\\nentirely on self-attention to compute representations of its input and output without using sequence-\\naligned RNNs or convolution. In the following sections, we will describe the Transformer, motivate\\nself-attention and discuss its advantages over models such as [17, 18] and [9].\\n3 Model Architecture\\nMost competitive neural sequence transduction models have an encoder-decoder structure [ 5,2,35].\\nHere, the encoder maps an input sequence of symbol representations (x1, ..., x n)to a sequence\\nof continuous representations z= (z1, ..., z n). Given z, the decoder then generates an output\\nsequence (y1, ..., y m)of symbols one element at a time. At each step the model is auto-regressive\\n[10], consuming the previously generated symbols as additional input when generating the next.\\n2', metadata={'source': 'attention.pdf', 'page': 1}),\n", " Document(page_content='Figure 1: The Transformer - model architecture.\\nThe Transformer follows this overall architecture using stacked self-attention and point-wise, fully\\nconnected layers for both the encoder and decoder, shown in the left and right halves of Figure 1,\\nrespectively.\\n3.1 Encoder and Decoder Stacks\\nEncoder: The encoder is composed of a stack of N= 6 identical layers. Each layer has two\\nsub-layers. The first is a multi-head self-attention mechanism, and the second is a simple, position-\\nwise fully connected feed-forward network. We employ a residual connection [ 11] around each of\\nthe two sub-layers, followed by layer normalization [ 1]. That is, the output of each sub-layer is\\nLayerNorm( x+ Sublayer( x)), where Sublayer( x)is the function implemented by the sub-layer\\nitself. To facilitate these residual connections, all sub-layers in the model, as well as the embedding\\nlayers, produce outputs of dimension dmodel = 512 .\\nDecoder: The decoder is also composed of a stack of N= 6identical layers. In addition to the two\\nsub-layers in each encoder layer, the decoder inserts a third sub-layer, which performs multi-head\\nattention over the output of the encoder stack. Similar to the encoder, we employ residual connections\\naround each of the sub-layers, followed by layer normalization. We also modify the self-attention\\nsub-layer in the decoder stack to prevent positions from attending to subsequent positions. This\\nmasking, combined with fact that the output embeddings are offset by one position, ensures that the\\npredictions for position ican depend only on the known outputs at positions less than i.\\n3.2 Attention\\nAn attention function can be described as mapping a query and a set of key-value pairs to an output,\\nwhere the query, keys, values, and output are all vectors. The output is computed as a weighted sum\\n3', metadata={'source': 'attention.pdf', 'page': 2}),\n", " Document(page_content='Scaled Dot-Product Attention\\n Multi-Head Attention\\nFigure 2: (left) Scaled Dot-Product Attention. (right) Multi-Head Attention consists of several\\nattention layers running in parallel.\\nof the values, where the weight assigned to each value is computed by a compatibility function of the\\nquery with the corresponding key.\\n3.2.1 Scaled Dot-Product Attention\\nWe call our particular attention \"Scaled Dot-Product Attention\" (Figure 2). The input consists of\\nqueries and keys of dimension dk, and values of dimension dv. We compute the dot products of the\\nquery with all keys, divide each by√dk, and apply a softmax function to obtain the weights on the\\nvalues.\\nIn practice, we compute the attention function on a set of queries simultaneously, packed together\\ninto a matrix Q. The keys and values are also packed together into matrices KandV. We compute\\nthe matrix of outputs as:\\nAttention( Q, K, V ) = softmax(QKT\\n√dk)V (1)\\nThe two most commonly used attention functions are additive attention [ 2], and dot-product (multi-\\nplicative) attention. Dot-product attention is identical to our algorithm, except for the scaling factor\\nof1√dk. Additive attention computes the compatibility function using a feed-forward network with\\na single hidden layer. While the two are similar in theoretical complexity, dot-product attention is\\nmuch faster and more space-efficient in practice, since it can be implemented using highly optimized\\nmatrix multiplication code.\\nWhile for small values of dkthe two mechanisms perform similarly, additive attention outperforms\\ndot product attention without scaling for larger values of dk[3]. We suspect that for large values of\\ndk, the dot products grow large in magnitude, pushing the softmax function into regions where it has\\nextremely small gradients4. To counteract this effect, we scale the dot products by1√dk.\\n3.2.2 Multi-Head Attention\\nInstead of performing a single attention function with dmodel-dimensional keys, values and queries,\\nwe found it beneficial to linearly project the queries, keys and values htimes with different, learned\\nlinear projections to dk,dkanddvdimensions, respectively. On each of these projected versions of\\nqueries, keys and values we then perform the attention function in parallel, yielding dv-dimensional\\n4To illustrate why the dot products get large, assume that the components of qandkare independent random\\nvariables with mean 0and variance 1. Then their dot product, q·k=Pdk\\ni=1qiki, has mean 0and variance dk.\\n4', metadata={'source': 'attention.pdf', 'page': 3}),\n", " Document(page_content='output values. These are concatenated and once again projected, resulting in the final values, as\\ndepicted in Figure 2.\\nMulti-head attention allows the model to jointly attend to information from different representation\\nsubspaces at different positions. With a single attention head, averaging inhibits this.\\nMultiHead( Q, K, V ) = Concat(head 1, ...,head h)WO\\nwhere head i= Attention( QWQ\\ni, KWK\\ni, V WV\\ni)\\nWhere the projections are parameter matrices WQ\\ni∈Rdmodel×dk,WK\\ni∈Rdmodel×dk,WV\\ni∈Rdmodel×dv\\nandWO∈Rhdv×dmodel.\\nIn this work we employ h= 8 parallel attention layers, or heads. For each of these we use\\ndk=dv=dmodel/h= 64 . Due to the reduced dimension of each head, the total computational cost\\nis similar to that of single-head attention with full dimensionality.\\n3.2.3 Applications of Attention in our Model\\nThe Transformer uses multi-head attention in three different ways:\\n•In \"encoder-decoder attention\" layers, the queries come from the previous decoder layer,\\nand the memory keys and values come from the output of the encoder. This allows every\\nposition in the decoder to attend over all positions in the input sequence. This mimics the\\ntypical encoder-decoder attention mechanisms in sequence-to-sequence models such as\\n[38, 2, 9].\\n•The encoder contains self-attention layers. In a self-attention layer all of the keys, values\\nand queries come from the same place, in this case, the output of the previous layer in the\\nencoder. Each position in the encoder can attend to all positions in the previous layer of the\\nencoder.\\n•Similarly, self-attention layers in the decoder allow each position in the decoder to attend to\\nall positions in the decoder up to and including that position. We need to prevent leftward\\ninformation flow in the decoder to preserve the auto-regressive property. We implement this\\ninside of scaled dot-product attention by masking out (setting to −∞) all values in the input\\nof the softmax which correspond to illegal connections. See Figure 2.\\n3.3 Position-wise Feed-Forward Networks\\nIn addition to attention sub-layers, each of the layers in our encoder and decoder contains a fully\\nconnected feed-forward network, which is applied to each position separately and identically. This\\nconsists of two linear transformations with a ReLU activation in between.\\nFFN( x) = max(0 , xW 1+b1)W2+b2 (2)\\nWhile the linear transformations are the same across different positions, they use different parameters\\nfrom layer to layer. Another way of describing this is as two convolutions with kernel size 1.\\nThe dimensionality of input and output is dmodel = 512 , and the inner-layer has dimensionality\\ndff= 2048 .\\n3.4 Embeddings and Softmax\\nSimilarly to other sequence transduction models, we use learned embeddings to convert the input\\ntokens and output tokens to vectors of dimension dmodel. We also use the usual learned linear transfor-\\nmation and softmax function to convert the decoder output to predicted next-token probabilities. In\\nour model, we share the same weight matrix between the two embedding layers and the pre-softmax\\nlinear transformation, similar to [ 30]. In the embedding layers, we multiply those weights by√dmodel.\\n5', metadata={'source': 'attention.pdf', 'page': 4}),\n", " Document(page_content='Table 1: Maximum path lengths, per-layer complexity and minimum number of sequential operations\\nfor different layer types. nis the sequence length, dis the representation dimension, kis the kernel\\nsize of convolutions and rthe size of the neighborhood in restricted self-attention.\\nLayer Type Complexity per Layer Sequential Maximum Path Length\\nOperations\\nSelf-Attention O(n2·d) O(1) O(1)\\nRecurrent O(n·d2) O(n) O(n)\\nConvolutional O(k·n·d2) O(1) O(logk(n))\\nSelf-Attention (restricted) O(r·n·d) O(1) O(n/r)\\n3.5 Positional Encoding\\nSince our model contains no recurrence and no convolution, in order for the model to make use of the\\norder of the sequence, we must inject some information about the relative or absolute position of the\\ntokens in the sequence. To this end, we add \"positional encodings\" to the input embeddings at the\\nbottoms of the encoder and decoder stacks. The positional encodings have the same dimension dmodel\\nas the embeddings, so that the two can be summed. There are many choices of positional encodings,\\nlearned and fixed [9].\\nIn this work, we use sine and cosine functions of different frequencies:\\nPE(pos,2i)=sin(pos/100002i/d model)\\nPE(pos,2i+1)=cos(pos/100002i/d model)\\nwhere posis the position and iis the dimension. That is, each dimension of the positional encoding\\ncorresponds to a sinusoid. The wavelengths form a geometric progression from 2πto10000 ·2π. We\\nchose this function because we hypothesized it would allow the model to easily learn to attend by\\nrelative positions, since for any fixed offset k,PEpos+kcan be represented as a linear function of\\nPEpos.\\nWe also experimented with using learned positional embeddings [ 9] instead, and found that the two\\nversions produced nearly identical results (see Table 3 row (E)). We chose the sinusoidal version\\nbecause it may allow the model to extrapolate to sequence lengths longer than the ones encountered\\nduring training.\\n4 Why Self-Attention\\nIn this section we compare various aspects of self-attention layers to the recurrent and convolu-\\ntional layers commonly used for mapping one variable-length sequence of symbol representations\\n(x1, ..., x n)to another sequence of equal length (z1, ..., z n), with xi, zi∈Rd, such as a hidden\\nlayer in a typical sequence transduction encoder or decoder. Motivating our use of self-attention we\\nconsider three desiderata.\\nOne is the total computational complexity per layer. Another is the amount of computation that can\\nbe parallelized, as measured by the minimum number of sequential operations required.\\nThe third is the path length between long-range dependencies in the network. Learning long-range\\ndependencies is a key challenge in many sequence transduction tasks. One key factor affecting the\\nability to learn such dependencies is the length of the paths forward and backward signals have to\\ntraverse in the network. The shorter these paths between any combination of positions in the input\\nand output sequences, the easier it is to learn long-range dependencies [ 12]. Hence we also compare\\nthe maximum path length between any two input and output positions in networks composed of the\\ndifferent layer types.\\nAs noted in Table 1, a self-attention layer connects all positions with a constant number of sequentially\\nexecuted operations, whereas a recurrent layer requires O(n)sequential operations. In terms of\\ncomputational complexity, self-attention layers are faster than recurrent layers when the sequence\\n6', metadata={'source': 'attention.pdf', 'page': 5}),\n", " Document(page_content='length nis smaller than the representation dimensionality d, which is most often the case with\\nsentence representations used by state-of-the-art models in machine translations, such as word-piece\\n[38] and byte-pair [ 31] representations. To improve computational performance for tasks involving\\nvery long sequences, self-attention could be restricted to considering only a neighborhood of size rin\\nthe input sequence centered around the respective output position. This would increase the maximum\\npath length to O(n/r). We plan to investigate this approach further in future work.\\nA single convolutional layer with kernel width k < n does not connect all pairs of input and output\\npositions. Doing so requires a stack of O(n/k)convolutional layers in the case of contiguous kernels,\\norO(logk(n))in the case of dilated convolutions [ 18], increasing the length of the longest paths\\nbetween any two positions in the network. Convolutional layers are generally more expensive than\\nrecurrent layers, by a factor of k. Separable convolutions [ 6], however, decrease the complexity\\nconsiderably, to O(k·n·d+n·d2). Even with k=n, however, the complexity of a separable\\nconvolution is equal to the combination of a self-attention layer and a point-wise feed-forward layer,\\nthe approach we take in our model.\\nAs side benefit, self-attention could yield more interpretable models. We inspect attention distributions\\nfrom our models and present and discuss examples in the appendix. Not only do individual attention\\nheads clearly learn to perform different tasks, many appear to exhibit behavior related to the syntactic\\nand semantic structure of the sentences.\\n5 Training\\nThis section describes the training regime for our models.\\n5.1 Training Data and Batching\\nWe trained on the standard WMT 2014 English-German dataset consisting of about 4.5 million\\nsentence pairs. Sentences were encoded using byte-pair encoding [ 3], which has a shared source-\\ntarget vocabulary of about 37000 tokens. For English-French, we used the significantly larger WMT\\n2014 English-French dataset consisting of 36M sentences and split tokens into a 32000 word-piece\\nvocabulary [ 38]. Sentence pairs were batched together by approximate sequence length. Each training\\nbatch contained a set of sentence pairs containing approximately 25000 source tokens and 25000\\ntarget tokens.\\n5.2 Hardware and Schedule\\nWe trained our models on one machine with 8 NVIDIA P100 GPUs. For our base models using\\nthe hyperparameters described throughout the paper, each training step took about 0.4 seconds. We\\ntrained the base models for a total of 100,000 steps or 12 hours. For our big models,(described on the\\nbottom line of table 3), step time was 1.0 seconds. The big models were trained for 300,000 steps\\n(3.5 days).\\n5.3 Optimizer\\nWe used the Adam optimizer [ 20] with β1= 0.9,β2= 0.98andϵ= 10−9. We varied the learning\\nrate over the course of training, according to the formula:\\nlrate =d−0.5\\nmodel·min(step_num−0.5, step _num·warmup _steps−1.5) (3)\\nThis corresponds to increasing the learning rate linearly for the first warmup _steps training steps,\\nand decreasing it thereafter proportionally to the inverse square root of the step number. We used\\nwarmup _steps = 4000 .\\n5.4 Regularization\\nWe employ three types of regularization during training:\\n7', metadata={'source': 'attention.pdf', 'page': 6}),\n", " Document(page_content='Table 2: The Transformer achieves better BLEU scores than previous state-of-the-art models on the\\nEnglish-to-German and English-to-French newstest2014 tests at a fraction of the training cost.\\nModelBLEU Training Cost (FLOPs)\\nEN-DE EN-FR EN-DE EN-FR\\nByteNet [18] 23.75\\nDeep-Att + PosUnk [39] 39.2 1.0·1020\\nGNMT + RL [38] 24.6 39.92 2.3·10191.4·1020\\nConvS2S [9] 25.16 40.46 9.6·10181.5·1020\\nMoE [32] 26.03 40.56 2.0·10191.2·1020\\nDeep-Att + PosUnk Ensemble [39] 40.4 8.0·1020\\nGNMT + RL Ensemble [38] 26.30 41.16 1.8·10201.1·1021\\nConvS2S Ensemble [9] 26.36 41.29 7.7·10191.2·1021\\nTransformer (base model) 27.3 38.1 3.3·1018\\nTransformer (big) 28.4 41.8 2.3·1019\\nResidual Dropout We apply dropout [ 33] to the output of each sub-layer, before it is added to the\\nsub-layer input and normalized. In addition, we apply dropout to the sums of the embeddings and the\\npositional encodings in both the encoder and decoder stacks. For the base model, we use a rate of\\nPdrop= 0.1.\\nLabel Smoothing During training, we employed label smoothing of value ϵls= 0.1[36]. This\\nhurts perplexity, as the model learns to be more unsure, but improves accuracy and BLEU score.\\n6 Results\\n6.1 Machine Translation\\nOn the WMT 2014 English-to-German translation task, the big transformer model (Transformer (big)\\nin Table 2) outperforms the best previously reported models (including ensembles) by more than 2.0\\nBLEU, establishing a new state-of-the-art BLEU score of 28.4. The configuration of this model is\\nlisted in the bottom line of Table 3. Training took 3.5days on 8P100 GPUs. Even our base model\\nsurpasses all previously published models and ensembles, at a fraction of the training cost of any of\\nthe competitive models.\\nOn the WMT 2014 English-to-French translation task, our big model achieves a BLEU score of 41.0,\\noutperforming all of the previously published single models, at less than 1/4the training cost of the\\nprevious state-of-the-art model. The Transformer (big) model trained for English-to-French used\\ndropout rate Pdrop= 0.1, instead of 0.3.\\nFor the base models, we used a single model obtained by averaging the last 5 checkpoints, which\\nwere written at 10-minute intervals. For the big models, we averaged the last 20 checkpoints. We\\nused beam search with a beam size of 4and length penalty α= 0.6[38]. These hyperparameters\\nwere chosen after experimentation on the development set. We set the maximum output length during\\ninference to input length + 50, but terminate early when possible [38].\\nTable 2 summarizes our results and compares our translation quality and training costs to other model\\narchitectures from the literature. We estimate the number of floating point operations used to train a\\nmodel by multiplying the training time, the number of GPUs used, and an estimate of the sustained\\nsingle-precision floating-point capacity of each GPU5.\\n6.2 Model Variations\\nTo evaluate the importance of different components of the Transformer, we varied our base model\\nin different ways, measuring the change in performance on English-to-German translation on the\\n5We used values of 2.8, 3.7, 6.0 and 9.5 TFLOPS for K80, K40, M40 and P100, respectively.\\n8', metadata={'source': 'attention.pdf', 'page': 7}),\n", " Document(page_content='Table 3: Variations on the Transformer architecture. Unlisted values are identical to those of the base\\nmodel. All metrics are on the English-to-German translation development set, newstest2013. Listed\\nperplexities are per-wordpiece, according to our byte-pair encoding, and should not be compared to\\nper-word perplexities.\\nN d model dff h d k dvPdrop ϵlstrain PPL BLEU params\\nsteps (dev) (dev) ×106\\nbase 6 512 2048 8 64 64 0.1 0.1 100K 4.92 25.8 65\\n(A)1 512 512 5.29 24.9\\n4 128 128 5.00 25.5\\n16 32 32 4.91 25.8\\n32 16 16 5.01 25.4\\n(B)16 5.16 25.1 58\\n32 5.01 25.4 60\\n(C)2 6.11 23.7 36\\n4 5.19 25.3 50\\n8 4.88 25.5 80\\n256 32 32 5.75 24.5 28\\n1024 128 128 4.66 26.0 168\\n1024 5.12 25.4 53\\n4096 4.75 26.2 90\\n(D)0.0 5.77 24.6\\n0.2 4.95 25.5\\n0.0 4.67 25.3\\n0.2 5.47 25.7\\n(E) positional embedding instead of sinusoids 4.92 25.7\\nbig 6 1024 4096 16 0.3 300K 4.33 26.4 213\\ndevelopment set, newstest2013. We used beam search as described in the previous section, but no\\ncheckpoint averaging. We present these results in Table 3.\\nIn Table 3 rows (A), we vary the number of attention heads and the attention key and value dimensions,\\nkeeping the amount of computation constant, as described in Section 3.2.2. While single-head\\nattention is 0.9 BLEU worse than the best setting, quality also drops off with too many heads.\\nIn Table 3 rows (B), we observe that reducing the attention key size dkhurts model quality. This\\nsuggests that determining compatibility is not easy and that a more sophisticated compatibility\\nfunction than dot product may be beneficial. We further observe in rows (C) and (D) that, as expected,\\nbigger models are better, and dropout is very helpful in avoiding over-fitting. In row (E) we replace our\\nsinusoidal positional encoding with learned positional embeddings [ 9], and observe nearly identical\\nresults to the base model.\\n6.3 English Constituency Parsing\\nTo evaluate if the Transformer can generalize to other tasks we performed experiments on English\\nconstituency parsing. This task presents specific challenges: the output is subject to strong structural\\nconstraints and is significantly longer than the input. Furthermore, RNN sequence-to-sequence\\nmodels have not been able to attain state-of-the-art results in small-data regimes [37].\\nWe trained a 4-layer transformer with dmodel = 1024 on the Wall Street Journal (WSJ) portion of the\\nPenn Treebank [ 25], about 40K training sentences. We also trained it in a semi-supervised setting,\\nusing the larger high-confidence and BerkleyParser corpora from with approximately 17M sentences\\n[37]. We used a vocabulary of 16K tokens for the WSJ only setting and a vocabulary of 32K tokens\\nfor the semi-supervised setting.\\nWe performed only a small number of experiments to select the dropout, both attention and residual\\n(section 5.4), learning rates and beam size on the Section 22 development set, all other parameters\\nremained unchanged from the English-to-German base translation model. During inference, we\\n9', metadata={'source': 'attention.pdf', 'page': 8}),\n", " Document(page_content='Table 4: The Transformer generalizes well to English constituency parsing (Results are on Section 23\\nof WSJ)\\nParser Training WSJ 23 F1\\nVinyals & Kaiser el al. (2014) [37] WSJ only, discriminative 88.3\\<PERSON><PERSON><PERSON><PERSON> et al. (2006) [29] WSJ only, discriminative 90.4\\<PERSON><PERSON><PERSON> et al. (2013) [40] WSJ only, discriminative 90.4\\n<PERSON><PERSON> et al. (2016) [8] WSJ only, discriminative 91.7\\nTransformer (4 layers) WSJ only, discriminative 91.3\\n<PERSON><PERSON> et al. (2013) [40] semi-supervised 91.3\\nH<PERSON> & <PERSON> (2009) [14] semi-supervised 91.3\\<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2006) [26] semi-supervised 92.1\\nVinyals & Kaiser el al. (2014) [37] semi-supervised 92.1\\nTransformer (4 layers) semi-supervised 92.7\\nLuong et al. (2015) [23] multi-task 93.0\\nD<PERSON> et al. (2016) [8] generative 93.3\\nincreased the maximum output length to input length + 300. We used a beam size of 21andα= 0.3\\nfor both WSJ only and the semi-supervised setting.\\nOur results in Table 4 show that despite the lack of task-specific tuning our model performs sur-\\nprisingly well, yielding better results than all previously reported models with the exception of the\\nRecurrent Neural Network Grammar [8].\\nIn contrast to RNN sequence-to-sequence models [ 37], the Transformer outperforms the Berkeley-\\nParser [29] even when training only on the WSJ training set of 40K sentences.\\n7 Conclusion\\nIn this work, we presented the Transformer, the first sequence transduction model based entirely on\\nattention, replacing the recurrent layers most commonly used in encoder-decoder architectures with\\nmulti-headed self-attention.\\nFor translation tasks, the Transformer can be trained significantly faster than architectures based\\non recurrent or convolutional layers. On both WMT 2014 English-to-German and WMT 2014\\nEnglish-to-French translation tasks, we achieve a new state of the art. In the former task our best\\nmodel outperforms even all previously reported ensembles.\\nWe are excited about the future of attention-based models and plan to apply them to other tasks. We\\nplan to extend the Transformer to problems involving input and output modalities other than text and\\nto investigate local, restricted attention mechanisms to efficiently handle large inputs and outputs\\nsuch as images, audio and video. Making generation less sequential is another research goals of ours.\\nThe code we used to train and evaluate our models is available at https://github.com/\\ntensorflow/tensor2tensor .\\nAcknowledgements We are grateful to Nal Kalchbrenner and Stephan Gouws for their fruitful\\ncomments, corrections and inspiration.\\nReferences\\n[1]Jimmy Lei Ba, Jamie Ryan Kiros, and Geoffrey E Hinton. Layer normalization. arXiv preprint\\narXiv:1607.06450 , 2016.\\n[2]Dzmitry Bahdanau, Kyunghyun Cho, and Yoshua Bengio. Neural machine translation by jointly\\nlearning to align and translate. CoRR , abs/1409.0473, 2014.\\n[3]Denny Britz, Anna Goldie, Minh-Thang Luong, and Quoc V . Le. Massive exploration of neural\\nmachine translation architectures. CoRR , abs/1703.03906, 2017.\\n[4]Jianpeng Cheng, Li Dong, and Mirella Lapata. Long short-term memory-networks for machine\\nreading. arXiv preprint arXiv:1601.06733 , 2016.\\n10', metadata={'source': 'attention.pdf', 'page': 9}),\n", " Document(page_content='[5]<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>,\\nand <PERSON><PERSON><PERSON>. Learning phrase representations using rnn encoder-decoder for statistical\\nmachine translation. CoRR , abs/1406.1078, 2014.\\n[6]<PERSON><PERSON>. Xception: Deep learning with depthwise separable convolutions. arXiv\\npreprint arXiv:1610.02357 , 2016.\\n[7]<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Empirical evaluation\\nof gated recurrent neural networks on sequence modeling. CoRR , abs/1412.3555, 2014.\\n[8]<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Recurrent neural\\nnetwork grammars. In Proc. of NAACL , 2016.\\n[9]<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Convolu-\\ntional sequence to sequence learning. arXiv preprint arXiv:1705.03122v2 , 2017.\\n[10] <PERSON>. Generating sequences with recurrent neural networks. arXiv preprint\\narXiv:1308.0850 , 2013.\\n[11] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Deep residual learning for im-\\nage recognition. In Proceedings of the IEEE Conference on Computer Vision and Pattern\\nRec<PERSON> , pages 770–778, 2016.\\n[12] Sepp Hochreiter, Yoshua Bengio, Paolo Frasconi, and Jürgen Schmidhuber. Gradient flow in\\nrecurrent nets: the difficulty of learning long-term dependencies, 2001.\\n[13] Sepp Hochreiter and Jürgen Schmidhuber. Long short-term memory. Neural computation ,\\n9(8):1735–1780, 1997.\\n[14] Zhongqiang Huang and Mary Harper. Self-training PCFG grammars with latent annotations\\nacross languages. In Proceedings of the 2009 Conference on Empirical Methods in Natural\\nLanguage Processing , pages 832–841. ACL, August 2009.\\n[15] Rafal Jozefowicz, Oriol Vinyals, Mike Schuster, Noam Shazeer, and Yonghui Wu. Exploring\\nthe limits of language modeling. arXiv preprint arXiv:1602.02410 , 2016.\\n[16] Łukasz Kaiser and Samy Bengio. Can active memory replace attention? In Advances in Neural\\nInformation Processing Systems, (NIPS) , 2016.\\n[17] Łukasz Kaiser and Ilya Sutskever. Neural GPUs learn algorithms. In International Conference\\non Learning Representations (ICLR) , 2016.\\n[18] Nal Kalchbrenner, Lasse Espeholt, Karen Simonyan, Aaron van den Oord, Alex Graves, and Ko-\\nray Kavukcuoglu. Neural machine translation in linear time. arXiv preprint arXiv:1610.10099v2 ,\\n2017.\\n[19] Yoon Kim, Carl Denton, Luong Hoang, and Alexander M. Rush. Structured attention networks.\\nInInternational Conference on Learning Representations , 2017.\\n[20] Diederik Kingma and Jimmy Ba. Adam: A method for stochastic optimization. In ICLR , 2015.\\n[21] Oleksii Kuchaiev and Boris Ginsburg. Factorization tricks for LSTM networks. arXiv preprint\\narXiv:1703.10722 , 2017.\\n[22] Zhouhan Lin, Minwei Feng, Cicero Nogueira dos Santos, Mo Yu, Bing Xiang, Bowen\\nZhou, and Yoshua Bengio. A structured self-attentive sentence embedding. arXiv preprint\\narXiv:1703.03130 , 2017.\\n[23] Minh-Thang Luong, Quoc V . Le, Ilya Sutskever, Oriol Vinyals, and Lukasz Kaiser. Multi-task\\nsequence to sequence learning. arXiv preprint arXiv:1511.06114 , 2015.\\n[24] Minh-Thang Luong, Hieu Pham, and Christopher D Manning. Effective approaches to attention-\\nbased neural machine translation. arXiv preprint arXiv:1508.04025 , 2015.\\n11', metadata={'source': 'attention.pdf', 'page': 10}),\n", " Document(page_content='[25] <PERSON>, <PERSON>, and <PERSON>. Building a large annotated\\ncorpus of english: The penn treebank. Computational linguistics , 19(2):313–330, 1993.\\n[26] <PERSON>, <PERSON>, and <PERSON>. Effective self-training for parsing. In\\nProceedings of the Human Language Technology Conference of the NAACL, Main Conference ,\\npages 152–159. ACL, June 2006.\\n[27] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A decomposable attention\\nmodel. In Empirical Methods in Natural Language Processing , 2016.\\n[28] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A deep reinforced model for abstractive\\nsummarization. arXiv preprint arXiv:1705.04304 , 2017.\\n[29] <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Learning accurate, compact,\\nand interpretable tree annotation. In Proceedings of the 21st International Conference on\\nComputational Linguistics and 44th Annual Meeting of the ACL , pages 433–440. ACL, July\\n2006.\\n[30] <PERSON><PERSON> Press and <PERSON><PERSON>. Using the output embedding to improve language models. arXiv\\npreprint arXiv:1608.05859 , 2016.\\n[31] <PERSON>, <PERSON>dow, and <PERSON>. Neural machine translation of rare words\\nwith subword units. arXiv preprint arXiv:1508.07909 , 2015.\\n[32] Noam Shazeer, Azalia Mirhoseini, Krzysztof Maziarz, Andy Davis, Quoc Le, <PERSON>,\\nand Jeff Dean. Outrageously large neural networks: The sparsely-gated mixture-of-experts\\nlayer. arXiv preprint arXiv:1701.06538 , 2017.\\n[33] Nitish Srivastava, Geoffrey E Hinton, Alex Krizhevsky, Ilya Sutskever, and Ruslan Salakhutdi-\\nnov. Dropout: a simple way to prevent neural networks from overfitting. Journal of Machine\\nLearning Research , 15(1):1929–1958, 2014.\\n[34] Sainbayar Sukhbaatar, Arthur Szlam, Jason Weston, and Rob Fergus. End-to-end memory\\nnetworks. In C. Cortes, N. D. Lawrence, D. D. Lee, M. Sugiyama, and R. Garnett, editors,\\nAdvances in Neural Information Processing Systems 28 , pages 2440–2448. Curran Associates,\\nInc., 2015.\\n[35] Ilya Sutskever, Oriol Vinyals, and Quoc VV Le. Sequence to sequence learning with neural\\nnetworks. In Advances in Neural Information Processing Systems , pages 3104–3112, 2014.\\n[36] Christian Szegedy, Vincent Vanhoucke, Sergey Ioffe, Jonathon Shlens, and Zbigniew Wojna.\\nRethinking the inception architecture for computer vision. CoRR , abs/1512.00567, 2015.\\n[37] Vinyals & Kaiser, Koo, Petrov, Sutskever, and Hinton. Grammar as a foreign language. In\\nAdvances in Neural Information Processing Systems , 2015.\\n[38] Yonghui Wu, Mike Schuster, Zhifeng Chen, Quoc V Le, Mohammad Norouzi, Wolfgang\\nMacherey, Maxim Krikun, Yuan Cao, Qin Gao, Klaus Macherey, et al. Google’s neural machine\\ntranslation system: Bridging the gap between human and machine translation. arXiv preprint\\narXiv:1609.08144 , 2016.\\n[39] Jie Zhou, Ying Cao, Xuguang Wang, Peng Li, and Wei Xu. Deep recurrent models with\\nfast-forward connections for neural machine translation. CoRR , abs/1606.04199, 2016.\\n[40] Muhua Zhu, Yue Zhang, Wenliang Chen, Min Zhang, and Jingbo Zhu. Fast and accurate\\nshift-reduce constituent parsing. In Proceedings of the 51st Annual Meeting of the ACL (Volume\\n1: Long Papers) , pages 434–443. ACL, August 2013.\\n12', metadata={'source': 'attention.pdf', 'page': 11}),\n", " Document(page_content='Attention Visualizations\\nInput-Input Layer5\\nIt\\nis\\nin\\nthis\\nspirit\\nthat\\na\\nmajority\\nof\\nAmerican\\ngovernments\\nhave\\npassed\\nnew\\nlaws\\nsince\\n2009\\nmaking\\nthe\\nregistration\\nor\\nvoting\\nprocess\\nmore\\ndifficult\\n.\\n<EOS>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\nIt\\nis\\nin\\nthis\\nspirit\\nthat\\na\\nmajority\\nof\\nAmerican\\ngovernments\\nhave\\npassed\\nnew\\nlaws\\nsince\\n2009\\nmaking\\nthe\\nregistration\\nor\\nvoting\\nprocess\\nmore\\ndifficult\\n.\\n<EOS>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\nFigure 3: An example of the attention mechanism following long-distance dependencies in the\\nencoder self-attention in layer 5 of 6. Many of the attention heads attend to a distant dependency of\\nthe verb ‘making’, completing the phrase ‘making...more difficult’. Attentions here shown only for\\nthe word ‘making’. Different colors represent different heads. Best viewed in color.\\n13', metadata={'source': 'attention.pdf', 'page': 12}),\n", " Document(page_content='Input-Input Layer5\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nInput-Input Layer5\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>Figure 4: Two attention heads, also in layer 5 of 6, apparently involved in anaphora resolution. Top:\\nFull attentions for head 5. Bottom: Isolated attentions from just the word ‘its’ for attention heads 5\\nand 6. Note that the attentions are very sharp for this word.\\n14', metadata={'source': 'attention.pdf', 'page': 13}),\n", " Document(page_content='Input-Input Layer5\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nInput-Input Layer5\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>Figure 5: Many of the attention heads exhibit behaviour that seems related to the structure of the\\nsentence. We give two such examples above, from two different heads from the encoder self-attention\\nat layer 5 of 6. The heads clearly learned to perform different tasks.\\n15', metadata={'source': 'attention.pdf', 'page': 14})]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["docs"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(page_content='Provided proper attribution is provided, Google hereby grants permission to\\nreproduce the tables and figures in this paper solely for use in journalistic or\\nscholarly works.\\nAttention Is All You Need\\nAshish Vaswani∗\\nGoogle Brain\\<EMAIL> Shazeer∗\\nGoogle Brain\\<EMAIL> Parmar∗\\nGoogle Research\\<EMAIL> Uszkoreit∗\\nGoogle Research\\<EMAIL>\\nLlion Jones∗\\nGoogle Research\\<EMAIL> N. Gomez∗ †\\nUniversity of Toronto\\<EMAIL><PERSON><PERSON>sz Kaiser∗\\nGoogle Brain\\<EMAIL>\\nIllia Polosukhin∗ ‡\\<EMAIL>\\nAbstract\\nThe dominant sequence transduction models are based on complex recurrent or\\nconvolutional neural networks that include an encoder and a decoder. The best\\nperforming models also connect the encoder and decoder through an attention\\nmechanism. We propose a new simple network architecture, the Transformer,\\nbased solely on attention mechanisms, dispensing with recurrence and convolutions', metadata={'source': 'attention.pdf', 'page': 0}),\n", " Document(page_content='mechanism. We propose a new simple network architecture, the Transformer,\\nbased solely on attention mechanisms, dispensing with recurrence and convolutions\\nentirely. Experiments on two machine translation tasks show these models to\\nbe superior in quality while being more parallelizable and requiring significantly\\nless time to train. Our model achieves 28.4 BLEU on the WMT 2014 English-\\nto-German translation task, improving over the existing best results, including\\nensembles, by over 2 BLEU. On the WMT 2014 English-to-French translation task,\\nour model establishes a new single-model state-of-the-art BLEU score of 41.8 after\\ntraining for 3.5 days on eight GPUs, a small fraction of the training costs of the\\nbest models from the literature. We show that the Transformer generalizes well to\\nother tasks by applying it successfully to English constituency parsing both with\\nlarge and limited training data.', metadata={'source': 'attention.pdf', 'page': 0}),\n", " Document(page_content='best models from the literature. We show that the Transformer generalizes well to\\nother tasks by applying it successfully to English constituency parsing both with\\nlarge and limited training data.\\n∗Equal contribution. Listing order is random. <PERSON> proposed replacing RNNs with self-attention and started\\nthe effort to evaluate this idea. <PERSON><PERSON>, with <PERSON><PERSON>, designed and implemented the first Transformer models and\\nhas been crucially involved in every aspect of this work. <PERSON><PERSON> proposed scaled dot-product attention, multi-head\\nattention and the parameter-free position representation and became the other person involved in nearly every\\ndetail. <PERSON><PERSON> designed, implemented, tuned and evaluated countless model variants in our original codebase and\\ntensor2tensor. <PERSON><PERSON> also experimented with novel model variants, was responsible for our initial codebase, and\\nefficient inference and visualizations. <PERSON><PERSON><PERSON> and <PERSON> spent countless long days designing various parts of and', metadata={'source': 'attention.pdf', 'page': 0}),\n", " Document(page_content='efficient inference and visualizations. <PERSON><PERSON><PERSON> and <PERSON> spent countless long days designing various parts of and\\nimplementing tensor2tensor, replacing our earlier codebase, greatly improving results and massively accelerating\\nour research.\\n†Work performed while at Google Brain.\\n‡Work performed while at Google Research.\\n31st Conference on Neural Information Processing Systems (NIPS 2017), Long Beach, CA, USA.arXiv:1706.03762v7  [cs.CL]  2 Aug 2023', metadata={'source': 'attention.pdf', 'page': 0}),\n", " Document(page_content='1 Introduction\\nRecurrent neural networks, long short-term memory [ 13] and gated recurrent [ 7] neural networks\\nin particular, have been firmly established as state of the art approaches in sequence modeling and\\ntransduction problems such as language modeling and machine translation [ 35,2,5]. Numerous\\nefforts have since continued to push the boundaries of recurrent language models and encoder-decoder\\narchitectures [38, 24, 15].\\nRecurrent models typically factor computation along the symbol positions of the input and output\\nsequences. Aligning the positions to steps in computation time, they generate a sequence of hidden\\nstates ht, as a function of the previous hidden state ht−1and the input for position t. This inherently\\nsequential nature precludes parallelization within training examples, which becomes critical at longer\\nsequence lengths, as memory constraints limit batching across examples. Recent work has achieved', metadata={'source': 'attention.pdf', 'page': 1})]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "text_splitter=RecursiveCharacterTextSplitter(chunk_size=1000,chunk_overlap=200)\n", "documents=text_splitter.split_documents(docs)\n", "documents[:5]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(page_content='Provided proper attribution is provided, Google hereby grants permission to\\nreproduce the tables and figures in this paper solely for use in journalistic or\\nscholarly works.\\nAttention Is All You Need\\nAshish Vaswani∗\\nGoogle Brain\\<EMAIL> Shazeer∗\\nGoogle Brain\\<EMAIL> Parmar∗\\nGoogle Research\\<EMAIL> Uszkoreit∗\\nGoogle Research\\<EMAIL>\\nLlion Jones∗\\nGoogle Research\\<EMAIL> N. Gomez∗ †\\nUniversity of Toronto\\<EMAIL><PERSON><PERSON>sz Kaiser∗\\nGoogle Brain\\<EMAIL>\\nIllia Polosukhin∗ ‡\\<EMAIL>\\nAbstract\\nThe dominant sequence transduction models are based on complex recurrent or\\nconvolutional neural networks that include an encoder and a decoder. The best\\nperforming models also connect the encoder and decoder through an attention\\nmechanism. We propose a new simple network architecture, the Transformer,\\nbased solely on attention mechanisms, dispensing with recurrence and convolutions', metadata={'source': 'attention.pdf', 'page': 0}),\n", " Document(page_content='mechanism. We propose a new simple network architecture, the Transformer,\\nbased solely on attention mechanisms, dispensing with recurrence and convolutions\\nentirely. Experiments on two machine translation tasks show these models to\\nbe superior in quality while being more parallelizable and requiring significantly\\nless time to train. Our model achieves 28.4 BLEU on the WMT 2014 English-\\nto-German translation task, improving over the existing best results, including\\nensembles, by over 2 BLEU. On the WMT 2014 English-to-French translation task,\\nour model establishes a new single-model state-of-the-art BLEU score of 41.8 after\\ntraining for 3.5 days on eight GPUs, a small fraction of the training costs of the\\nbest models from the literature. We show that the Transformer generalizes well to\\nother tasks by applying it successfully to English constituency parsing both with\\nlarge and limited training data.', metadata={'source': 'attention.pdf', 'page': 0}),\n", " Document(page_content='best models from the literature. We show that the Transformer generalizes well to\\nother tasks by applying it successfully to English constituency parsing both with\\nlarge and limited training data.\\n∗Equal contribution. Listing order is random. <PERSON> proposed replacing RNNs with self-attention and started\\nthe effort to evaluate this idea. <PERSON><PERSON>, with <PERSON><PERSON>, designed and implemented the first Transformer models and\\nhas been crucially involved in every aspect of this work. <PERSON><PERSON> proposed scaled dot-product attention, multi-head\\nattention and the parameter-free position representation and became the other person involved in nearly every\\ndetail. <PERSON><PERSON> designed, implemented, tuned and evaluated countless model variants in our original codebase and\\ntensor2tensor. <PERSON><PERSON> also experimented with novel model variants, was responsible for our initial codebase, and\\nefficient inference and visualizations. <PERSON><PERSON><PERSON> and <PERSON> spent countless long days designing various parts of and', metadata={'source': 'attention.pdf', 'page': 0}),\n", " Document(page_content='efficient inference and visualizations. <PERSON><PERSON><PERSON> and <PERSON> spent countless long days designing various parts of and\\nimplementing tensor2tensor, replacing our earlier codebase, greatly improving results and massively accelerating\\nour research.\\n†Work performed while at Google Brain.\\n‡Work performed while at Google Research.\\n31st Conference on Neural Information Processing Systems (NIPS 2017), Long Beach, CA, USA.arXiv:1706.03762v7  [cs.CL]  2 Aug 2023', metadata={'source': 'attention.pdf', 'page': 0}),\n", " Document(page_content='1 Introduction\\nRecurrent neural networks, long short-term memory [ 13] and gated recurrent [ 7] neural networks\\nin particular, have been firmly established as state of the art approaches in sequence modeling and\\ntransduction problems such as language modeling and machine translation [ 35,2,5]. Numerous\\nefforts have since continued to push the boundaries of recurrent language models and encoder-decoder\\narchitectures [38, 24, 15].\\nRecurrent models typically factor computation along the symbol positions of the input and output\\nsequences. Aligning the positions to steps in computation time, they generate a sequence of hidden\\nstates ht, as a function of the previous hidden state ht−1and the input for position t. This inherently\\nsequential nature precludes parallelization within training examples, which becomes critical at longer\\nsequence lengths, as memory constraints limit batching across examples. Recent work has achieved', metadata={'source': 'attention.pdf', 'page': 1}),\n", " Document(page_content='sequential nature precludes parallelization within training examples, which becomes critical at longer\\nsequence lengths, as memory constraints limit batching across examples. Recent work has achieved\\nsignificant improvements in computational efficiency through factorization tricks [ 21] and conditional\\ncomputation [ 32], while also improving model performance in case of the latter. The fundamental\\nconstraint of sequential computation, however, remains.\\nAttention mechanisms have become an integral part of compelling sequence modeling and transduc-\\ntion models in various tasks, allowing modeling of dependencies without regard to their distance in\\nthe input or output sequences [ 2,19]. In all but a few cases [ 27], however, such attention mechanisms\\nare used in conjunction with a recurrent network.\\nIn this work we propose the Transformer, a model architecture eschewing recurrence and instead\\nrelying entirely on an attention mechanism to draw global dependencies between input and output.', metadata={'source': 'attention.pdf', 'page': 1}),\n", " Document(page_content='In this work we propose the Transformer, a model architecture eschewing recurrence and instead\\nrelying entirely on an attention mechanism to draw global dependencies between input and output.\\nThe Transformer allows for significantly more parallelization and can reach a new state of the art in\\ntranslation quality after being trained for as little as twelve hours on eight P100 GPUs.\\n2 Background\\nThe goal of reducing sequential computation also forms the foundation of the Extended Neural GPU\\n[16], ByteNet [ 18] and ConvS2S [ 9], all of which use convolutional neural networks as basic building\\nblock, computing hidden representations in parallel for all input and output positions. In these models,\\nthe number of operations required to relate signals from two arbitrary input or output positions grows\\nin the distance between positions, linearly for ConvS2S and logarithmically for ByteNet. This makes', metadata={'source': 'attention.pdf', 'page': 1}),\n", " Document(page_content='in the distance between positions, linearly for ConvS2S and logarithmically for ByteNet. This makes\\nit more difficult to learn dependencies between distant positions [ 12]. In the Transformer this is\\nreduced to a constant number of operations, albeit at the cost of reduced effective resolution due\\nto averaging attention-weighted positions, an effect we counteract with Multi-Head Attention as\\ndescribed in section 3.2.\\nSelf-attention, sometimes called intra-attention is an attention mechanism relating different positions\\nof a single sequence in order to compute a representation of the sequence. Self-attention has been\\nused successfully in a variety of tasks including reading comprehension, abstractive summarization,\\ntextual entailment and learning task-independent sentence representations [4, 27, 28, 22].\\nEnd-to-end memory networks are based on a recurrent attention mechanism instead of sequence-', metadata={'source': 'attention.pdf', 'page': 1}),\n", " Document(page_content='textual entailment and learning task-independent sentence representations [4, 27, 28, 22].\\nEnd-to-end memory networks are based on a recurrent attention mechanism instead of sequence-\\naligned recurrence and have been shown to perform well on simple-language question answering and\\nlanguage modeling tasks [34].\\nTo the best of our knowledge, however, the Transformer is the first transduction model relying\\nentirely on self-attention to compute representations of its input and output without using sequence-\\naligned RNNs or convolution. In the following sections, we will describe the Transformer, motivate\\nself-attention and discuss its advantages over models such as [17, 18] and [9].\\n3 Model Architecture\\nMost competitive neural sequence transduction models have an encoder-decoder structure [ 5,2,35].\\nHere, the encoder maps an input sequence of symbol representations (x1, ..., x n)to a sequence\\nof continuous representations z= (z1, ..., z n). Given z, the decoder then generates an output', metadata={'source': 'attention.pdf', 'page': 1}),\n", " Document(page_content='Here, the encoder maps an input sequence of symbol representations (x1, ..., x n)to a sequence\\nof continuous representations z= (z1, ..., z n). Given z, the decoder then generates an output\\nsequence (y1, ..., y m)of symbols one element at a time. At each step the model is auto-regressive\\n[10], consuming the previously generated symbols as additional input when generating the next.\\n2', metadata={'source': 'attention.pdf', 'page': 1}),\n", " Document(page_content='Figure 1: The Transformer - model architecture.\\nThe Transformer follows this overall architecture using stacked self-attention and point-wise, fully\\nconnected layers for both the encoder and decoder, shown in the left and right halves of Figure 1,\\nrespectively.\\n3.1 Encoder and Decoder Stacks\\nEncoder: The encoder is composed of a stack of N= 6 identical layers. Each layer has two\\nsub-layers. The first is a multi-head self-attention mechanism, and the second is a simple, position-\\nwise fully connected feed-forward network. We employ a residual connection [ 11] around each of\\nthe two sub-layers, followed by layer normalization [ 1]. That is, the output of each sub-layer is\\nLayerNorm( x+ Sublayer( x)), where Sublayer( x)is the function implemented by the sub-layer\\nitself. To facilitate these residual connections, all sub-layers in the model, as well as the embedding\\nlayers, produce outputs of dimension dmodel = 512 .', metadata={'source': 'attention.pdf', 'page': 2}),\n", " Document(page_content='itself. To facilitate these residual connections, all sub-layers in the model, as well as the embedding\\nlayers, produce outputs of dimension dmodel = 512 .\\nDecoder: The decoder is also composed of a stack of N= 6identical layers. In addition to the two\\nsub-layers in each encoder layer, the decoder inserts a third sub-layer, which performs multi-head\\nattention over the output of the encoder stack. Similar to the encoder, we employ residual connections\\naround each of the sub-layers, followed by layer normalization. We also modify the self-attention\\nsub-layer in the decoder stack to prevent positions from attending to subsequent positions. This\\nmasking, combined with fact that the output embeddings are offset by one position, ensures that the\\npredictions for position ican depend only on the known outputs at positions less than i.\\n3.2 Attention\\nAn attention function can be described as mapping a query and a set of key-value pairs to an output,', metadata={'source': 'attention.pdf', 'page': 2}),\n", " Document(page_content='3.2 Attention\\nAn attention function can be described as mapping a query and a set of key-value pairs to an output,\\nwhere the query, keys, values, and output are all vectors. The output is computed as a weighted sum\\n3', metadata={'source': 'attention.pdf', 'page': 2}),\n", " Document(page_content='Scaled Dot-Product Attention\\n Multi-Head Attention\\nFigure 2: (left) Scaled Dot-Product Attention. (right) Multi-Head Attention consists of several\\nattention layers running in parallel.\\nof the values, where the weight assigned to each value is computed by a compatibility function of the\\nquery with the corresponding key.\\n3.2.1 Scaled Dot-Product Attention\\nWe call our particular attention \"Scaled Dot-Product Attention\" (Figure 2). The input consists of\\nqueries and keys of dimension dk, and values of dimension dv. We compute the dot products of the\\nquery with all keys, divide each by√dk, and apply a softmax function to obtain the weights on the\\nvalues.\\nIn practice, we compute the attention function on a set of queries simultaneously, packed together\\ninto a matrix Q. The keys and values are also packed together into matrices KandV. We compute\\nthe matrix of outputs as:\\nAttention( Q, K, V ) = softmax(QKT\\n√dk)V (1)', metadata={'source': 'attention.pdf', 'page': 3}),\n", " Document(page_content='into a matrix Q. The keys and values are also packed together into matrices KandV. We compute\\nthe matrix of outputs as:\\nAttention( Q, K, V ) = softmax(QKT\\n√dk)V (1)\\nThe two most commonly used attention functions are additive attention [ 2], and dot-product (multi-\\nplicative) attention. Dot-product attention is identical to our algorithm, except for the scaling factor\\nof1√dk. Additive attention computes the compatibility function using a feed-forward network with\\na single hidden layer. While the two are similar in theoretical complexity, dot-product attention is\\nmuch faster and more space-efficient in practice, since it can be implemented using highly optimized\\nmatrix multiplication code.\\nWhile for small values of dkthe two mechanisms perform similarly, additive attention outperforms\\ndot product attention without scaling for larger values of dk[3]. We suspect that for large values of\\ndk, the dot products grow large in magnitude, pushing the softmax function into regions where it has', metadata={'source': 'attention.pdf', 'page': 3}),\n", " Document(page_content='dk, the dot products grow large in magnitude, pushing the softmax function into regions where it has\\nextremely small gradients4. To counteract this effect, we scale the dot products by1√dk.\\n3.2.2 Multi-Head Attention\\nInstead of performing a single attention function with dmodel-dimensional keys, values and queries,\\nwe found it beneficial to linearly project the queries, keys and values htimes with different, learned\\nlinear projections to dk,dkanddvdimensions, respectively. On each of these projected versions of\\nqueries, keys and values we then perform the attention function in parallel, yielding dv-dimensional\\n4To illustrate why the dot products get large, assume that the components of qandkare independent random\\nvariables with mean 0and variance 1. Then their dot product, q·k=Pdk\\ni=1qiki, has mean 0and variance dk.\\n4', metadata={'source': 'attention.pdf', 'page': 3}),\n", " Document(page_content='output values. These are concatenated and once again projected, resulting in the final values, as\\ndepicted in Figure 2.\\nMulti-head attention allows the model to jointly attend to information from different representation\\nsubspaces at different positions. With a single attention head, averaging inhibits this.\\nMultiHead( Q, K, V ) = Concat(head 1, ...,head h)WO\\nwhere head i= Attention( QWQ\\ni, KWK\\ni, V WV\\ni)\\nWhere the projections are parameter matrices WQ\\ni∈Rdmodel×dk,WK\\ni∈Rdmodel×dk,WV\\ni∈Rdmodel×dv\\nandWO∈Rhdv×dmodel.\\nIn this work we employ h= 8 parallel attention layers, or heads. For each of these we use\\ndk=dv=dmodel/h= 64 . Due to the reduced dimension of each head, the total computational cost\\nis similar to that of single-head attention with full dimensionality.\\n3.2.3 Applications of Attention in our Model\\nThe Transformer uses multi-head attention in three different ways:\\n•In \"encoder-decoder attention\" layers, the queries come from the previous decoder layer,', metadata={'source': 'attention.pdf', 'page': 4}),\n", " Document(page_content='The Transformer uses multi-head attention in three different ways:\\n•In \"encoder-decoder attention\" layers, the queries come from the previous decoder layer,\\nand the memory keys and values come from the output of the encoder. This allows every\\nposition in the decoder to attend over all positions in the input sequence. This mimics the\\ntypical encoder-decoder attention mechanisms in sequence-to-sequence models such as\\n[38, 2, 9].\\n•The encoder contains self-attention layers. In a self-attention layer all of the keys, values\\nand queries come from the same place, in this case, the output of the previous layer in the\\nencoder. Each position in the encoder can attend to all positions in the previous layer of the\\nencoder.\\n•Similarly, self-attention layers in the decoder allow each position in the decoder to attend to\\nall positions in the decoder up to and including that position. We need to prevent leftward', metadata={'source': 'attention.pdf', 'page': 4}),\n", " Document(page_content='encoder.\\n•Similarly, self-attention layers in the decoder allow each position in the decoder to attend to\\nall positions in the decoder up to and including that position. We need to prevent leftward\\ninformation flow in the decoder to preserve the auto-regressive property. We implement this\\ninside of scaled dot-product attention by masking out (setting to −∞) all values in the input\\nof the softmax which correspond to illegal connections. See Figure 2.\\n3.3 Position-wise Feed-Forward Networks\\nIn addition to attention sub-layers, each of the layers in our encoder and decoder contains a fully\\nconnected feed-forward network, which is applied to each position separately and identically. This\\nconsists of two linear transformations with a ReLU activation in between.\\nFFN( x) = max(0 , xW 1+b1)W2+b2 (2)\\nWhile the linear transformations are the same across different positions, they use different parameters', metadata={'source': 'attention.pdf', 'page': 4}),\n", " Document(page_content='FFN( x) = max(0 , xW 1+b1)W2+b2 (2)\\nWhile the linear transformations are the same across different positions, they use different parameters\\nfrom layer to layer. Another way of describing this is as two convolutions with kernel size 1.\\nThe dimensionality of input and output is dmodel = 512 , and the inner-layer has dimensionality\\ndff= 2048 .\\n3.4 Embeddings and Softmax\\nSimilarly to other sequence transduction models, we use learned embeddings to convert the input\\ntokens and output tokens to vectors of dimension dmodel. We also use the usual learned linear transfor-\\nmation and softmax function to convert the decoder output to predicted next-token probabilities. In\\nour model, we share the same weight matrix between the two embedding layers and the pre-softmax\\nlinear transformation, similar to [ 30]. In the embedding layers, we multiply those weights by√dmodel.\\n5', metadata={'source': 'attention.pdf', 'page': 4}),\n", " Document(page_content='Table 1: Maximum path lengths, per-layer complexity and minimum number of sequential operations\\nfor different layer types. nis the sequence length, dis the representation dimension, kis the kernel\\nsize of convolutions and rthe size of the neighborhood in restricted self-attention.\\nLayer Type Complexity per Layer Sequential Maximum Path Length\\nOperations\\nSelf-Attention O(n2·d) O(1) O(1)\\nRecurrent O(n·d2) O(n) O(n)\\nConvolutional O(k·n·d2) O(1) O(logk(n))\\nSelf-Attention (restricted) O(r·n·d) O(1) O(n/r)\\n3.5 Positional Encoding\\nSince our model contains no recurrence and no convolution, in order for the model to make use of the\\norder of the sequence, we must inject some information about the relative or absolute position of the\\ntokens in the sequence. To this end, we add \"positional encodings\" to the input embeddings at the\\nbottoms of the encoder and decoder stacks. The positional encodings have the same dimension dmodel', metadata={'source': 'attention.pdf', 'page': 5}),\n", " Document(page_content='tokens in the sequence. To this end, we add \"positional encodings\" to the input embeddings at the\\nbottoms of the encoder and decoder stacks. The positional encodings have the same dimension dmodel\\nas the embeddings, so that the two can be summed. There are many choices of positional encodings,\\nlearned and fixed [9].\\nIn this work, we use sine and cosine functions of different frequencies:\\nPE(pos,2i)=sin(pos/100002i/d model)\\nPE(pos,2i+1)=cos(pos/100002i/d model)\\nwhere posis the position and iis the dimension. That is, each dimension of the positional encoding\\ncorresponds to a sinusoid. The wavelengths form a geometric progression from 2πto10000 ·2π. We\\nchose this function because we hypothesized it would allow the model to easily learn to attend by\\nrelative positions, since for any fixed offset k,PEpos+kcan be represented as a linear function of\\nPEpos.\\nWe also experimented with using learned positional embeddings [ 9] instead, and found that the two', metadata={'source': 'attention.pdf', 'page': 5}),\n", " Document(page_content='PEpos.\\nWe also experimented with using learned positional embeddings [ 9] instead, and found that the two\\nversions produced nearly identical results (see Table 3 row (E)). We chose the sinusoidal version\\nbecause it may allow the model to extrapolate to sequence lengths longer than the ones encountered\\nduring training.\\n4 Why Self-Attention\\nIn this section we compare various aspects of self-attention layers to the recurrent and convolu-\\ntional layers commonly used for mapping one variable-length sequence of symbol representations\\n(x1, ..., x n)to another sequence of equal length (z1, ..., z n), with xi, zi∈Rd, such as a hidden\\nlayer in a typical sequence transduction encoder or decoder. Motivating our use of self-attention we\\nconsider three desiderata.\\nOne is the total computational complexity per layer. Another is the amount of computation that can\\nbe parallelized, as measured by the minimum number of sequential operations required.', metadata={'source': 'attention.pdf', 'page': 5}),\n", " Document(page_content='One is the total computational complexity per layer. Another is the amount of computation that can\\nbe parallelized, as measured by the minimum number of sequential operations required.\\nThe third is the path length between long-range dependencies in the network. Learning long-range\\ndependencies is a key challenge in many sequence transduction tasks. One key factor affecting the\\nability to learn such dependencies is the length of the paths forward and backward signals have to\\ntraverse in the network. The shorter these paths between any combination of positions in the input\\nand output sequences, the easier it is to learn long-range dependencies [ 12]. Hence we also compare\\nthe maximum path length between any two input and output positions in networks composed of the\\ndifferent layer types.\\nAs noted in Table 1, a self-attention layer connects all positions with a constant number of sequentially\\nexecuted operations, whereas a recurrent layer requires O(n)sequential operations. In terms of', metadata={'source': 'attention.pdf', 'page': 5}),\n", " Document(page_content='executed operations, whereas a recurrent layer requires O(n)sequential operations. In terms of\\ncomputational complexity, self-attention layers are faster than recurrent layers when the sequence\\n6', metadata={'source': 'attention.pdf', 'page': 5}),\n", " Document(page_content='length nis smaller than the representation dimensionality d, which is most often the case with\\nsentence representations used by state-of-the-art models in machine translations, such as word-piece\\n[38] and byte-pair [ 31] representations. To improve computational performance for tasks involving\\nvery long sequences, self-attention could be restricted to considering only a neighborhood of size rin\\nthe input sequence centered around the respective output position. This would increase the maximum\\npath length to O(n/r). We plan to investigate this approach further in future work.\\nA single convolutional layer with kernel width k < n does not connect all pairs of input and output\\npositions. Doing so requires a stack of O(n/k)convolutional layers in the case of contiguous kernels,\\norO(logk(n))in the case of dilated convolutions [ 18], increasing the length of the longest paths\\nbetween any two positions in the network. Convolutional layers are generally more expensive than', metadata={'source': 'attention.pdf', 'page': 6}),\n", " Document(page_content='orO(logk(n))in the case of dilated convolutions [ 18], increasing the length of the longest paths\\nbetween any two positions in the network. Convolutional layers are generally more expensive than\\nrecurrent layers, by a factor of k. Separable convolutions [ 6], however, decrease the complexity\\nconsiderably, to O(k·n·d+n·d2). Even with k=n, however, the complexity of a separable\\nconvolution is equal to the combination of a self-attention layer and a point-wise feed-forward layer,\\nthe approach we take in our model.\\nAs side benefit, self-attention could yield more interpretable models. We inspect attention distributions\\nfrom our models and present and discuss examples in the appendix. Not only do individual attention\\nheads clearly learn to perform different tasks, many appear to exhibit behavior related to the syntactic\\nand semantic structure of the sentences.\\n5 Training\\nThis section describes the training regime for our models.\\n5.1 Training Data and Batching', metadata={'source': 'attention.pdf', 'page': 6}),\n", " Document(page_content='and semantic structure of the sentences.\\n5 Training\\nThis section describes the training regime for our models.\\n5.1 Training Data and Batching\\nWe trained on the standard WMT 2014 English-German dataset consisting of about 4.5 million\\nsentence pairs. Sentences were encoded using byte-pair encoding [ 3], which has a shared source-\\ntarget vocabulary of about 37000 tokens. For English-French, we used the significantly larger WMT\\n2014 English-French dataset consisting of 36M sentences and split tokens into a 32000 word-piece\\nvocabulary [ 38]. Sentence pairs were batched together by approximate sequence length. Each training\\nbatch contained a set of sentence pairs containing approximately 25000 source tokens and 25000\\ntarget tokens.\\n5.2 Hardware and Schedule\\nWe trained our models on one machine with 8 NVIDIA P100 GPUs. For our base models using\\nthe hyperparameters described throughout the paper, each training step took about 0.4 seconds. We', metadata={'source': 'attention.pdf', 'page': 6}),\n", " Document(page_content='We trained our models on one machine with 8 NVIDIA P100 GPUs. For our base models using\\nthe hyperparameters described throughout the paper, each training step took about 0.4 seconds. We\\ntrained the base models for a total of 100,000 steps or 12 hours. For our big models,(described on the\\nbottom line of table 3), step time was 1.0 seconds. The big models were trained for 300,000 steps\\n(3.5 days).\\n5.3 Optimizer\\nWe used the Adam optimizer [ 20] with β1= 0.9,β2= 0.98andϵ= 10−9. We varied the learning\\nrate over the course of training, according to the formula:\\nlrate =d−0.5\\nmodel·min(step_num−0.5, step _num·warmup _steps−1.5) (3)\\nThis corresponds to increasing the learning rate linearly for the first warmup _steps training steps,\\nand decreasing it thereafter proportionally to the inverse square root of the step number. We used\\nwarmup _steps = 4000 .\\n5.4 Regularization\\nWe employ three types of regularization during training:\\n7', metadata={'source': 'attention.pdf', 'page': 6}),\n", " Document(page_content='Table 2: The Transformer achieves better BLEU scores than previous state-of-the-art models on the\\nEnglish-to-German and English-to-French newstest2014 tests at a fraction of the training cost.\\nModelBLEU Training Cost (FLOPs)\\nEN-DE EN-FR EN-DE EN-FR\\nByteNet [18] 23.75\\nDeep-Att + PosUnk [39] 39.2 1.0·1020\\nGNMT + RL [38] 24.6 39.92 2.3·10191.4·1020\\nConvS2S [9] 25.16 40.46 9.6·10181.5·1020\\nMoE [32] 26.03 40.56 2.0·10191.2·1020\\nDeep-Att + PosUnk Ensemble [39] 40.4 8.0·1020\\nGNMT + RL Ensemble [38] 26.30 41.16 1.8·10201.1·1021\\nConvS2S Ensemble [9] 26.36 41.29 7.7·10191.2·1021\\nTransformer (base model) 27.3 38.1 3.3·1018\\nTransformer (big) 28.4 41.8 2.3·1019\\nResidual Dropout We apply dropout [ 33] to the output of each sub-layer, before it is added to the\\nsub-layer input and normalized. In addition, we apply dropout to the sums of the embeddings and the\\npositional encodings in both the encoder and decoder stacks. For the base model, we use a rate of\\nPdrop= 0.1.', metadata={'source': 'attention.pdf', 'page': 7}),\n", " Document(page_content='positional encodings in both the encoder and decoder stacks. For the base model, we use a rate of\\nPdrop= 0.1.\\n<PERSON><PERSON><PERSON> Smoothing During training, we employed label smoothing of value ϵls= 0.1[36]. This\\nhurts perplexity, as the model learns to be more unsure, but improves accuracy and BLEU score.\\n6 Results\\n6.1 Machine Translation\\nOn the WMT 2014 English-to-German translation task, the big transformer model (Transformer (big)\\nin Table 2) outperforms the best previously reported models (including ensembles) by more than 2.0\\nBLEU, establishing a new state-of-the-art BLEU score of 28.4. The configuration of this model is\\nlisted in the bottom line of Table 3. Training took 3.5days on 8P100 GPUs. Even our base model\\nsurpasses all previously published models and ensembles, at a fraction of the training cost of any of\\nthe competitive models.\\nOn the WMT 2014 English-to-French translation task, our big model achieves a BLEU score of 41.0,', metadata={'source': 'attention.pdf', 'page': 7}),\n", " Document(page_content='the competitive models.\\nOn the WMT 2014 English-to-French translation task, our big model achieves a BLEU score of 41.0,\\noutperforming all of the previously published single models, at less than 1/4the training cost of the\\nprevious state-of-the-art model. The Transformer (big) model trained for English-to-French used\\ndropout rate Pdrop= 0.1, instead of 0.3.\\nFor the base models, we used a single model obtained by averaging the last 5 checkpoints, which\\nwere written at 10-minute intervals. For the big models, we averaged the last 20 checkpoints. We\\nused beam search with a beam size of 4and length penalty α= 0.6[38]. These hyperparameters\\nwere chosen after experimentation on the development set. We set the maximum output length during\\ninference to input length + 50, but terminate early when possible [38].\\nTable 2 summarizes our results and compares our translation quality and training costs to other model', metadata={'source': 'attention.pdf', 'page': 7}),\n", " Document(page_content='inference to input length + 50, but terminate early when possible [38].\\nTable 2 summarizes our results and compares our translation quality and training costs to other model\\narchitectures from the literature. We estimate the number of floating point operations used to train a\\nmodel by multiplying the training time, the number of GPUs used, and an estimate of the sustained\\nsingle-precision floating-point capacity of each GPU5.\\n6.2 Model Variations\\nTo evaluate the importance of different components of the Transformer, we varied our base model\\nin different ways, measuring the change in performance on English-to-German translation on the\\n5We used values of 2.8, 3.7, 6.0 and 9.5 TFLOPS for K80, K40, M40 and P100, respectively.\\n8', metadata={'source': 'attention.pdf', 'page': 7}),\n", " Document(page_content='Table 3: Variations on the Transformer architecture. Unlisted values are identical to those of the base\\nmodel. All metrics are on the English-to-German translation development set, newstest2013. Listed\\nperplexities are per-wordpiece, according to our byte-pair encoding, and should not be compared to\\nper-word perplexities.\\nN d model dff h d k dvPdrop ϵlstrain PPL BLEU params\\nsteps (dev) (dev) ×106\\nbase 6 512 2048 8 64 64 0.1 0.1 100K 4.92 25.8 65\\n(A)1 512 512 5.29 24.9\\n4 128 128 5.00 25.5\\n16 32 32 4.91 25.8\\n32 16 16 5.01 25.4\\n(B)16 5.16 25.1 58\\n32 5.01 25.4 60\\n(C)2 6.11 23.7 36\\n4 5.19 25.3 50\\n8 4.88 25.5 80\\n256 32 32 5.75 24.5 28\\n1024 128 128 4.66 26.0 168\\n1024 5.12 25.4 53\\n4096 4.75 26.2 90\\n(D)0.0 5.77 24.6\\n0.2 4.95 25.5\\n0.0 4.67 25.3\\n0.2 5.47 25.7\\n(E) positional embedding instead of sinusoids 4.92 25.7\\nbig 6 1024 4096 16 0.3 300K 4.33 26.4 213\\ndevelopment set, newstest2013. We used beam search as described in the previous section, but no', metadata={'source': 'attention.pdf', 'page': 8}),\n", " Document(page_content='(E) positional embedding instead of sinusoids 4.92 25.7\\nbig 6 1024 4096 16 0.3 300K 4.33 26.4 213\\ndevelopment set, newstest2013. We used beam search as described in the previous section, but no\\ncheckpoint averaging. We present these results in Table 3.\\nIn Table 3 rows (A), we vary the number of attention heads and the attention key and value dimensions,\\nkeeping the amount of computation constant, as described in Section 3.2.2. While single-head\\nattention is 0.9 BLEU worse than the best setting, quality also drops off with too many heads.\\nIn Table 3 rows (B), we observe that reducing the attention key size dkhurts model quality. This\\nsuggests that determining compatibility is not easy and that a more sophisticated compatibility\\nfunction than dot product may be beneficial. We further observe in rows (C) and (D) that, as expected,\\nbigger models are better, and dropout is very helpful in avoiding over-fitting. In row (E) we replace our', metadata={'source': 'attention.pdf', 'page': 8}),\n", " Document(page_content='bigger models are better, and dropout is very helpful in avoiding over-fitting. In row (E) we replace our\\nsinusoidal positional encoding with learned positional embeddings [ 9], and observe nearly identical\\nresults to the base model.\\n6.3 English Constituency Parsing\\nTo evaluate if the Transformer can generalize to other tasks we performed experiments on English\\nconstituency parsing. This task presents specific challenges: the output is subject to strong structural\\nconstraints and is significantly longer than the input. Furthermore, RNN sequence-to-sequence\\nmodels have not been able to attain state-of-the-art results in small-data regimes [37].\\nWe trained a 4-layer transformer with dmodel = 1024 on the Wall Street Journal (WSJ) portion of the\\nPenn Treebank [ 25], about 40K training sentences. We also trained it in a semi-supervised setting,\\nusing the larger high-confidence and BerkleyParser corpora from with approximately 17M sentences', metadata={'source': 'attention.pdf', 'page': 8}),\n", " Document(page_content='Penn Treebank [ 25], about 40K training sentences. We also trained it in a semi-supervised setting,\\nusing the larger high-confidence and BerkleyParser corpora from with approximately 17M sentences\\n[37]. We used a vocabulary of 16K tokens for the WSJ only setting and a vocabulary of 32K tokens\\nfor the semi-supervised setting.\\nWe performed only a small number of experiments to select the dropout, both attention and residual\\n(section 5.4), learning rates and beam size on the Section 22 development set, all other parameters\\nremained unchanged from the English-to-German base translation model. During inference, we\\n9', metadata={'source': 'attention.pdf', 'page': 8}),\n", " Document(page_content='Table 4: The Transformer generalizes well to English constituency parsing (Results are on Section 23\\nof WSJ)\\nParser Training WSJ 23 F1\\nVinyals & Kaiser el al. (2014) [37] WSJ only, discriminative 88.3\\<PERSON><PERSON><PERSON><PERSON> et al. (2006) [29] WSJ only, discriminative 90.4\\<PERSON><PERSON><PERSON> et al. (2013) [40] WSJ only, discriminative 90.4\\n<PERSON><PERSON> et al. (2016) [8] WSJ only, discriminative 91.7\\nTransformer (4 layers) WSJ only, discriminative 91.3\\n<PERSON><PERSON> et al. (2013) [40] semi-supervised 91.3\\<PERSON>H<PERSON> & <PERSON> (2009) [14] semi-supervised 91.3\\<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2006) [26] semi-supervised 92.1\\nVinyals & Kaiser el al. (2014) [37] semi-supervised 92.1\\nTransformer (4 layers) semi-supervised 92.7\\nLuo<PERSON> et al. (2015) [23] multi-task 93.0\\nD<PERSON> et al. (2016) [8] generative 93.3\\nincreased the maximum output length to input length + 300. We used a beam size of 21andα= 0.3\\nfor both WSJ only and the semi-supervised setting.\\nOur results in Table 4 show that despite the lack of task-specific tuning our model performs sur-', metadata={'source': 'attention.pdf', 'page': 9}),\n", " Document(page_content='for both WSJ only and the semi-supervised setting.\\nOur results in Table 4 show that despite the lack of task-specific tuning our model performs sur-\\nprisingly well, yielding better results than all previously reported models with the exception of the\\nRecurrent Neural Network Grammar [8].\\nIn contrast to RNN sequence-to-sequence models [ 37], the Transformer outperforms the Berkeley-\\nParser [29] even when training only on the WSJ training set of 40K sentences.\\n7 Conclusion\\nIn this work, we presented the Transformer, the first sequence transduction model based entirely on\\nattention, replacing the recurrent layers most commonly used in encoder-decoder architectures with\\nmulti-headed self-attention.\\nFor translation tasks, the Transformer can be trained significantly faster than architectures based\\non recurrent or convolutional layers. On both WMT 2014 English-to-German and WMT 2014\\nEnglish-to-French translation tasks, we achieve a new state of the art. In the former task our best', metadata={'source': 'attention.pdf', 'page': 9}),\n", " Document(page_content='on recurrent or convolutional layers. On both WMT 2014 English-to-German and WMT 2014\\nEnglish-to-French translation tasks, we achieve a new state of the art. In the former task our best\\nmodel outperforms even all previously reported ensembles.\\nWe are excited about the future of attention-based models and plan to apply them to other tasks. We\\nplan to extend the Transformer to problems involving input and output modalities other than text and\\nto investigate local, restricted attention mechanisms to efficiently handle large inputs and outputs\\nsuch as images, audio and video. Making generation less sequential is another research goals of ours.\\nThe code we used to train and evaluate our models is available at https://github.com/\\ntensorflow/tensor2tensor .\\nAcknowledgements We are grateful to <PERSON><PERSON> and <PERSON> for their fruitful\\ncomments, corrections and inspiration.\\nReferences\\n[1]<PERSON>, <PERSON>, and <PERSON>. Layer normalization. arXiv preprint', metadata={'source': 'attention.pdf', 'page': 9}),\n", " Document(page_content='comments, corrections and inspiration.\\nReferences\\n[1]<PERSON>, <PERSON>, and <PERSON>. Layer normalization. arXiv preprint\\narXiv:1607.06450 , 2016.\\n[2]<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Neural machine translation by jointly\\nlearning to align and translate. CoRR , abs/1409.0473, 2014.\\n[3]<PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> <PERSON> <PERSON> Massive exploration of neural\\nmachine translation architectures. CoRR , abs/1703.03906, 2017.\\n[4]<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Long short-term memory-networks for machine\\nreading. arXiv preprint arXiv:1601.06733 , 2016.\\n10', metadata={'source': 'attention.pdf', 'page': 9}),\n", " Document(page_content='[5]<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>,\\nand <PERSON><PERSON><PERSON>. Learning phrase representations using rnn encoder-decoder for statistical\\nmachine translation. CoRR , abs/1406.1078, 2014.\\n[6]<PERSON><PERSON>. Xception: Deep learning with depthwise separable convolutions. arXiv\\npreprint arXiv:1610.02357 , 2016.\\n[7]<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Empirical evaluation\\nof gated recurrent neural networks on sequence modeling. CoRR , abs/1412.3555, 2014.\\n[8]<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Recurrent neural\\nnetwork grammars. In Proc. of NAACL , 2016.\\n[9]<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Convolu-\\ntional sequence to sequence learning. arXiv preprint arXiv:1705.03122v2 , 2017.\\n[10] <PERSON>. Generating sequences with recurrent neural networks. arXiv preprint\\narXiv:1308.0850 , 2013.', metadata={'source': 'attention.pdf', 'page': 10}),\n", " Document(page_content='tional sequence to sequence learning. arXiv preprint arXiv:1705.03122v2 , 2017.\\n[10] <PERSON>. Generating sequences with recurrent neural networks. arXiv preprint\\narXiv:1308.0850 , 2013.\\n[11] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Deep residual learning for im-\\nage recognition. In Proceedings of the IEEE Conference on Computer Vision and Pattern\\nRecognition , pages 770–778, 2016.\\n[12] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Gradient flow in\\nrecurrent nets: the difficulty of learning long-term dependencies, 2001.\\n[13] <PERSON><PERSON> and <PERSON><PERSON><PERSON>. Long short-term memory. Neural computation ,\\n9(8):1735–1780, 1997.\\n[14] <PERSON><PERSON><PERSON><PERSON> and <PERSON>. Self-training PCFG grammars with latent annotations\\nacross languages. In Proceedings of the 2009 Conference on Empirical Methods in Natural\\nLanguage Processing , pages 832–841. ACL, August 2009.', metadata={'source': 'attention.pdf', 'page': 10}),\n", " Document(page_content='across languages. In Proceedings of the 2009 Conference on Empirical Methods in Natural\\nLanguage Processing , pages 832–841. ACL, August 2009.\\n[15] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Exploring\\nthe limits of language modeling. arXiv preprint arXiv:1602.02410 , 2016.\\n[16] <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Can active memory replace attention? In Advances in Neural\\nInformation Processing Systems, (NIPS) , 2016.\\n[17] <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Neural GPUs learn algorithms. In International Conference\\non Learning Representations (ICLR) , 2016.\\n[18] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Neural machine translation in linear time. arXiv preprint arXiv:1610.10099v2 ,\\n2017.\\n[19] <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Structured attention networks.\\nInInternational Conference on Learning Representations , 2017.', metadata={'source': 'attention.pdf', 'page': 10}),\n", " Document(page_content='2017.\\n[19] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Structured attention networks.\\nInInternational Conference on Learning Representations , 2017.\\n[20] <PERSON><PERSON><PERSON> and <PERSON>: A method for stochastic optimization. In ICLR , 2015.\\n[21] <PERSON><PERSON><PERSON> and <PERSON>. Factorization tricks for LSTM networks. arXiv preprint\\narXiv:1703.10722 , 2017.\\n[22] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. A structured self-attentive sentence embedding. arXiv preprint\\narXiv:1703.03130 , 2017.\\n[23] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Multi-task\\nsequence to sequence learning. arXiv preprint arXiv:1511.06114 , 2015.\\n[24] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Effective approaches to attention-\\nbased neural machine translation. arXiv preprint arXiv:1508.04025 , 2015.\\n11', metadata={'source': 'attention.pdf', 'page': 10}),\n", " Document(page_content='[25] <PERSON>, <PERSON>, and <PERSON>. Building a large annotated\\ncorpus of english: The penn treebank. Computational linguistics , 19(2):313–330, 1993.\\n[26] <PERSON>, <PERSON>, and <PERSON>. Effective self-training for parsing. In\\nProceedings of the Human Language Technology Conference of the NAACL, Main Conference ,\\npages 152–159. ACL, June 2006.\\n[27] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A decomposable attention\\nmodel. In Empirical Methods in Natural Language Processing , 2016.\\n[28] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A deep reinforced model for abstractive\\nsummarization. arXiv preprint arXiv:1705.04304 , 2017.\\n[29] <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Learning accurate, compact,\\nand interpretable tree annotation. In Proceedings of the 21st International Conference on\\nComputational Linguistics and 44th Annual Meeting of the ACL , pages 433–440. ACL, July\\n2006.', metadata={'source': 'attention.pdf', 'page': 11}),\n", " Document(page_content='and interpretable tree annotation. In Proceedings of the 21st International Conference on\\nComputational Linguistics and 44th Annual Meeting of the ACL , pages 433–440. ACL, July\\n2006.\\n[30] Ofir Press and <PERSON><PERSON>. Using the output embedding to improve language models. arXiv\\npreprint arXiv:1608.05859 , 2016.\\n[31] <PERSON>, <PERSON>, and <PERSON>. Neural machine translation of rare words\\nwith subword units. arXiv preprint arXiv:1508.07909 , 2015.\\n[32] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>,\\nand <PERSON>. Outrageously large neural networks: The sparsely-gated mixture-of-experts\\nlayer. arXiv preprint arXiv:1701.06538 , 2017.\\n[33] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>-\\nnov. Dropout: a simple way to prevent neural networks from overfitting. Journal of Machine\\nLearning Research , 15(1):1929–1958, 2014.', metadata={'source': 'attention.pdf', 'page': 11}),\n", " Document(page_content='nov. Dropout: a simple way to prevent neural networks from overfitting. Journal of Machine\\nLearning Research , 15(1):1929–1958, 2014.\\n[34] <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. End-to-end memory\\nnetworks. In <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors,\\nAdvances in Neural Information Processing Systems 28 , pages 2440–2448. Curran Associates,\\nInc., 2015.\\n[35] <PERSON><PERSON>, Oriol <PERSON>yals, and Quoc VV Le. Sequence to sequence learning with neural\\nnetworks. In Advances in Neural Information Processing Systems , pages 3104–3112, 2014.\\n[36] <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>.\\nRethinking the inception architecture for computer vision. CoRR , abs/1512.00567, 2015.\\n[37] <PERSON><PERSON><PERSON> & Kaiser, Ko<PERSON>, Petrov, Sutskever, and Hinton. Grammar as a foreign language. In\\nAdvances in Neural Information Processing Systems , 2015.', metadata={'source': 'attention.pdf', 'page': 11}),\n", " Document(page_content='[37] <PERSON><PERSON><PERSON> & <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Grammar as a foreign language. In\\nAdvances in Neural Information Processing Systems , 2015.\\n[38] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Google’s neural machine\\ntranslation system: Bridging the gap between human and machine translation. arXiv preprint\\narXiv:1609.08144 , 2016.\\n[39] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Deep recurrent models with\\nfast-forward connections for neural machine translation. CoRR , abs/1606.04199, 2016.\\n[40] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Fast and accurate\\nshift-reduce constituent parsing. In Proceedings of the 51st Annual Meeting of the ACL (Volume\\n1: Long Papers) , pages 434–443. ACL, August 2013.\\n12', metadata={'source': 'attention.pdf', 'page': 11}),\n", " Document(page_content='Attention Visualizations\\nInput-Input Layer5\\nIt\\nis\\nin\\nthis\\nspirit\\nthat\\na\\nmajority\\nof\\nAmerican\\ngovernments\\nhave\\npassed\\nnew\\nlaws\\nsince\\n2009\\nmaking\\nthe\\nregistration\\nor\\nvoting\\nprocess\\nmore\\ndifficult\\n.\\n<EOS>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\nIt\\nis\\nin\\nthis\\nspirit\\nthat\\na\\nmajority\\nof\\nAmerican\\ngovernments\\nhave\\npassed\\nnew\\nlaws\\nsince\\n2009\\nmaking\\nthe\\nregistration\\nor\\nvoting\\nprocess\\nmore\\ndifficult\\n.\\n<EOS>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\nFigure 3: An example of the attention mechanism following long-distance dependencies in the\\nencoder self-attention in layer 5 of 6. Many of the attention heads attend to a distant dependency of\\nthe verb ‘making’, completing the phrase ‘making...more difficult’. Attentions here shown only for\\nthe word ‘making’. Different colors represent different heads. Best viewed in color.\\n13', metadata={'source': 'attention.pdf', 'page': 12}),\n", " Document(page_content='Input-Input Layer5\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nInput-Input Layer5\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>Figure 4: Two attention heads, also in layer 5 of 6, apparently involved in anaphora resolution. Top:\\nFull attentions for head 5. Bottom: Isolated attentions from just the word ‘its’ for attention heads 5\\nand 6. Note that the attentions are very sharp for this word.\\n14', metadata={'source': 'attention.pdf', 'page': 13}),\n", " Document(page_content='Input-Input Layer5\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nInput-Input Layer5\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>Figure 5: Many of the attention heads exhibit behaviour that seems related to the structure of the\\nsentence. We give two such examples above, from two different heads from the encoder self-attention\\nat layer 5 of 6. The heads clearly learned to perform different tasks.\\n15', metadata={'source': 'attention.pdf', 'page': 14})]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["documents"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Vector Embedding And Vector Store\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_community.vectorstores import Chroma\n", "db = Chroma.from_documents(documents,OpenAIEmbeddings())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"Who are the authors of attention is all you need?\"\n", "retireved_results=db.similarity_search(query)\n", "print(retireved_results[0].page_content)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["## FAISS Vector Database\n", "from langchain_community.vectorstores import FAISS\n", "db = FAISS.from_documents(documents[:15], OpenAIEmbeddings())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 2}