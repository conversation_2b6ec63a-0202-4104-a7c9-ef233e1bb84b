[project]
name = "action"
version = "0.1.0"
description = "Agentic AI in Action"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "anthropic>=0.49.0",
    "autogen-agentchat>=*******",
    "autogen-ext[grpc,mcp,ollama,openai]>=*******",
    "bs4>=0.0.2",
    "gradio>=5.22.0",
    "httpx>=0.28.1",
    "ipywidgets>=8.1.5",
    "langchain-anthropic>=0.3.10",
    "langchain-community>=0.3.20",
    "langchain-experimental>=0.3.4",
    "langchain-openai>=0.3.9",
    "langgraph>=0.3.18",
    "langgraph-checkpoint-sqlite>=2.0.6",
    "langsmith>=0.3.18",
    "lxml>=5.3.1",
    "mcp-server-fetch>=2025.1.17",
    "mcp[cli]>=1.5.0",
    "openai>=1.68.2",
    "openai-agents>=0.0.6",
    "playwright>=1.51.0",
    "plotly>=6.0.1",
    "polygon-api-client>=1.14.5",
    "psutil>=7.0.0",
    "pypdf>=5.4.0",
    "pypdf2>=3.0.1",
    "python-dotenv>=1.0.1",
    "python-hue-v2>=2.2.1",
    "requests>=2.32.3",
    "semantic-kernel>=1.25.0",
    "sendgrid>=6.11.0",
    "setuptools>=78.1.0",
    "smithery>=0.1.0",
    "speedtest-cli>=2.1.3",
    "wikipedia>=1.4.0",
]

[dependency-groups]
dev = [
    "ipykernel>=6.29.5",
]
