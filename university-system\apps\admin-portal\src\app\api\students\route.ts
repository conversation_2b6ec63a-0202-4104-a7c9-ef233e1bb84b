import { NextRequest, NextResponse } from 'next/server';
import { createServerClient, getCurrentUser, checkUserRole } from '@university/database/src/client';
import { ApiResponse, PaginatedResponse, CreateUserData } from '@university/types';

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient();
    const { searchParams } = new URL(request.url);
    
    // Get pagination parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;
    
    // Get filter parameters
    const search = searchParams.get('search');
    const departmentId = searchParams.get('departmentId');
    const status = searchParams.get('status');

    // Get current user and check permissions
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const hasPermission = await checkUserRole(user.id, ['admin', 'staff', 'faculty']);
    if (!hasPermission) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Access denied'
      }, { status: 403 });
    }

    // Build query for students
    let query = supabase
      .from('users')
      .select(`
        id,
        email,
        first_name,
        last_name,
        student_id,
        phone,
        address,
        date_of_birth,
        enrollment_date,
        graduation_date,
        is_active,
        created_at,
        departments (
          id,
          name,
          code
        )
      `)
      .eq('role', 'student');

    // Apply filters
    if (search) {
      query = query.or(`first_name.ilike.%${search}%,last_name.ilike.%${search}%,email.ilike.%${search}%,student_id.ilike.%${search}%`);
    }
    if (departmentId) {
      query = query.eq('department_id', departmentId);
    }
    if (status) {
      query = query.eq('is_active', status === 'active');
    }

    // Apply pagination and ordering
    const { data: students, error: studentsError } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (studentsError) {
      console.error('Error fetching students:', studentsError);
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Failed to fetch students'
      }, { status: 500 });
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .eq('role', 'student');

    if (search) {
      countQuery = countQuery.or(`first_name.ilike.%${search}%,last_name.ilike.%${search}%,email.ilike.%${search}%,student_id.ilike.%${search}%`);
    }
    if (departmentId) {
      countQuery = countQuery.eq('department_id', departmentId);
    }
    if (status) {
      countQuery = countQuery.eq('is_active', status === 'active');
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error('Error counting students:', countError);
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Failed to count students'
      }, { status: 500 });
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json<PaginatedResponse>({
      success: true,
      data: students,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages
      }
    });

  } catch (error) {
    console.error('Students API error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient();
    const body: CreateUserData = await request.json();

    // Get current user and check permissions
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const hasPermission = await checkUserRole(user.id, ['admin', 'staff']);
    if (!hasPermission) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Access denied'
      }, { status: 403 });
    }

    // Validate required fields
    if (!body.email || !body.firstName || !body.lastName || !body.studentId) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Missing required fields'
      }, { status: 400 });
    }

    // Create user in auth
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: body.email,
      password: body.studentId, // Temporary password, user should change it
      email_confirm: true
    });

    if (authError) {
      console.error('Error creating auth user:', authError);
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Failed to create user account'
      }, { status: 500 });
    }

    // Create user profile
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email: body.email,
        first_name: body.firstName,
        last_name: body.lastName,
        role: 'student',
        student_id: body.studentId,
        department_id: body.departmentId,
        phone: body.phone,
        address: body.address,
        date_of_birth: body.dateOfBirth,
        enrollment_date: body.enrollmentDate || new Date().toISOString()
      })
      .select()
      .single();

    if (profileError) {
      console.error('Error creating user profile:', profileError);
      // Clean up auth user if profile creation fails
      await supabase.auth.admin.deleteUser(authData.user.id);
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Failed to create user profile'
      }, { status: 500 });
    }

    return NextResponse.json<ApiResponse>({
      success: true,
      data: profile,
      message: 'Student created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Create student error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
