import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@university/database/src/client';
import { LoginCredentials, ApiResponse } from '@university/types';

export async function POST(request: NextRequest) {
  try {
    const body: LoginCredentials = await request.json();
    const { email, password } = body;

    if (!email || !password) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Email and password are required'
      }, { status: 400 });
    }

    const supabase = createServerClient();

    // Authenticate user
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (authError) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Invalid credentials'
      }, { status: 401 });
    }

    if (!authData.user) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Authentication failed'
      }, { status: 401 });
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('*')
      .eq('id', authData.user.id)
      .single();

    if (profileError || !profile) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'User profile not found'
      }, { status: 404 });
    }

    // Check if user has admin/staff/faculty role
    const allowedRoles = ['admin', 'staff', 'faculty'];
    if (!allowedRoles.includes(profile.role)) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Access denied. Admin portal is for administrative staff only.'
      }, { status: 403 });
    }

    return NextResponse.json<ApiResponse>({
      success: true,
      data: {
        user: authData.user,
        profile,
        session: authData.session
      },
      message: 'Login successful'
    });

  } catch (error) {
    console.error('Admin login error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
