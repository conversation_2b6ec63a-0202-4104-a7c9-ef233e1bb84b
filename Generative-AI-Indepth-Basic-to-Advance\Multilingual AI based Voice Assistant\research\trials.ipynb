{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["perfect!!\n", "AIzaSyB5Nlw2teuugvkFSGzMyYEvTZDRFojtNF0\n"]}], "source": ["from dotenv import load_dotenv\n", "import os\n", "\n", "print(\"perfect!!\")\n", "load_dotenv()\n", "\n", "GOOGLE_API_KEY=os.getenv(\"GOOGLE_API_KEY\")\n", "print(GOOGLE_API_KEY)\n", "os.environ[\"GOOGLE_API_KEY\"]=GOOGLE_API_KEY"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Multiligual-AI-Assistant\\env\\lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["# Import the Python SDK\n", "import google.generativeai as genai"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["genai.configure(api_key=GOOGLE_API_KEY)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["model = genai.GenerativeModel('gemini-pro')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["In the quaint and bustling town of Willow Creek, amidst ivy-covered cottages and blooming gardens, there lived a young girl named <PERSON>. One fateful morning, as she skipped towards the town square, her eyes widened at the sight of a peculiar display in the antique shop window. A magnificent backpack, crafted from shimmering midnight blue leather and adorned with intricate silver runes, seemed to whisper secrets to her from across the glass.\n", "\n", "Intrigued, <PERSON> pushed open the door and stepped inside. The shop was a treasure trove of forgotten relics and curiosities, its shelves crammed with antique jewelry, vintage toys, and dusty tomes. As she approached the display, the backpack's runes glowed faintly, as if calling out to her.\n", "\n", "Hesitantly, <PERSON> lifted the backpack from its pedestal. Instantly, a surge of warmth spread through her body, and she felt a strange connection to its enigmatic presence. She knew in that moment that this was no ordinary satchel but a vessel of ancient magic.\n", "\n", "As <PERSON> made her way home, her footsteps were lighter and her spirit soared. She couldn't wait to explore the secrets hidden within her new companion. With trembling hands, she unzipped the main compartment and gasped at the sight that greeted her.\n", "\n", "A shimmering portal shimmered in the center of the backpack, its edges swirling with iridescent colors. <PERSON> cautiously reached out and touched the portal, and in an instant, she was transported to a realm both familiar and utterly fantastical.\n", "\n", "Towering trees with emerald leaves and vines that danced in the wind surrounded her, while the air crackled with the scent of pine needles and wildflowers. A sparkling stream bubbled nearby, its waters reflecting the changing colors of the sky above.\n", "\n", "As <PERSON> ventured deeper into this magical realm, she encountered creatures she had never imagined. A mischievous pixie fluttered overhead, leaving a trail of shimmering dust in its wake. A wise old owl perched on a gnarled root, its eyes twinkling with ancient wisdom.\n", "\n", "With each step she took, <PERSON> discovered new wonders. A hidden waterfall cascaded into a shimmering pool, and a rainbow arced across the sky like a celestial bridge. She realized that her backpack was not merely a container but a gateway to a world of boundless possibility.\n", "\n", "As the sun began its descent, painting the sky in hues of gold and crimson, <PERSON> knew it was time to return. She stepped back through the portal and found herself once more in the confines of her humble cottage.\n", "\n", "From that day forward, <PERSON>'s backpack became her constant companion. It held not only her school books and pencils but also the secrets of a magical realm. She carried it with her on every adventure, knowing that within its leather folds lay a treasure that would forever enrich her life.\n", "\n", "And so, the legend of <PERSON> and her magic backpack passed down through generations, becoming a beloved tale whispered among the children of Willow Creek, reminding them that even in the ordinary, the extraordinary could be found.\n"]}], "source": ["response = model.generate_content(\"Write a story about a magic backpack.\")\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}