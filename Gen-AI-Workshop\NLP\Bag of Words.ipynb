{"cells": [{"cell_type": "code", "execution_count": 71, "metadata": {"id": "15Sa92e_2yjs"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.feature_extraction.text import CountVectorizer\n", "from sklearn.naive_bayes import MultinomialNB\n", "from sklearn.metrics import classification_report"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"id": "YP7yMpNK9VxI"}, "outputs": [], "source": ["df = pd.read_csv('spam.csv')"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "F-0RzqFS9aZ_", "outputId": "451e4559-d2d9-4f36-cce2-fa50ab1cdd5d"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"df\",\n  \"rows\": 5572,\n  \"fields\": [\n    {\n      \"column\": \"Category\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"spam\",\n          \"ham\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Message\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 5157,\n        \"samples\": [\n          \"Also sir, i sent you an email about how to log into the usc payment portal. I.ll send you another message that should explain how things are back home. Have a great weekend.\",\n          \"Are you free now?can i call now?\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "df"}, "text/html": ["\n", "  <div id=\"df-5eaf6c47-a6ce-4376-a153-27c45f9c7f9e\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Category</th>\n", "      <th>Message</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ham</td>\n", "      <td>Go until jurong point, crazy.. Available only ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ham</td>\n", "      <td>Ok lar... Joking wif u oni...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>spam</td>\n", "      <td>Free entry in 2 a wkly comp to win FA Cup fina...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ham</td>\n", "      <td>U dun say so early hor... U c already then say...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ham</td>\n", "      <td>Nah I don't think he goes to usf, he lives aro...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-5eaf6c47-a6ce-4376-a153-27c45f9c7f9e')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-5eaf6c47-a6ce-4376-a153-27c45f9c7f9e button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-5eaf6c47-a6ce-4376-a153-27c45f9c7f9e');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-26d7d6a7-1e01-49dd-a4db-7e013bb7d2c5\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-26d7d6a7-1e01-49dd-a4db-7e013bb7d2c5')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-26d7d6a7-1e01-49dd-a4db-7e013bb7d2c5 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "text/plain": ["  Category                                            Message\n", "0      ham  Go until jurong point, crazy.. Available only ...\n", "1      ham                      Ok lar... Joking wif u oni...\n", "2     spam  Free entry in 2 a wkly comp to win FA Cup fina...\n", "3      ham  U dun say so early hor... U c already then say...\n", "4      ham  Nah I don't think he goes to usf, he lives aro..."]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head(5)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "M9-Mi4OB9bJn", "outputId": "6925c2d8-dadd-45fb-81e0-07b3198b3f5a"}, "outputs": [{"data": {"text/plain": ["ham     4825\n", "spam     747\n", "Name: Category, dtype: int64"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["df.Category.value_counts()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8odnTKwm9gJm", "outputId": "679e5be0-b8f3-4770-fa50-65b096085be4"}, "outputs": [{"data": {"text/plain": ["ham     86.593683\n", "spam    13.406317\n", "Name: Category, dtype: float64"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["df.Category.value_counts()/len(df)*100"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"id": "Ka7uNX8V9lIv"}, "outputs": [], "source": ["df['spam'] = df['Category'].apply(lambda x: 1 if x=='spam' else 0)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "8CWO857-9wop", "outputId": "40f3ddcd-2a65-4290-bf55-8c27bc31817f"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"df\",\n  \"rows\": 5572,\n  \"fields\": [\n    {\n      \"column\": \"Category\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"spam\",\n          \"ham\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Message\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 5157,\n        \"samples\": [\n          \"Also sir, i sent you an email about how to log into the usc payment portal. I.ll send you another message that should explain how things are back home. Have a great weekend.\",\n          \"Are you free now?can i call now?\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"spam\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 0,\n        \"max\": 1,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          1,\n          0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "df"}, "text/html": ["\n", "  <div id=\"df-432600de-6054-4c7d-878d-ebb7856055a5\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Category</th>\n", "      <th>Message</th>\n", "      <th>spam</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ham</td>\n", "      <td>Go until jurong point, crazy.. Available only ...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ham</td>\n", "      <td>Ok lar... Joking wif u oni...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>spam</td>\n", "      <td>Free entry in 2 a wkly comp to win FA Cup fina...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ham</td>\n", "      <td>U dun say so early hor... U c already then say...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ham</td>\n", "      <td>Nah I don't think he goes to usf, he lives aro...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-432600de-6054-4c7d-878d-ebb7856055a5')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-432600de-6054-4c7d-878d-ebb7856055a5 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-432600de-6054-4c7d-878d-ebb7856055a5');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-4c102133-1230-4cb9-8bff-2d9ca3801c17\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-4c102133-1230-4cb9-8bff-2d9ca3801c17')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-4c102133-1230-4cb9-8bff-2d9ca3801c17 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "text/plain": ["  Category                                            Message  spam\n", "0      ham  Go until jurong point, crazy.. Available only ...     0\n", "1      ham                      Ok lar... Joking wif u oni...     0\n", "2     spam  Free entry in 2 a wkly comp to win FA Cup fina...     1\n", "3      ham  U dun say so early hor... U c already then say...     0\n", "4      ham  Nah I don't think he goes to usf, he lives aro...     0"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head(5)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"id": "i2-5dxsc9xQX"}, "outputs": [], "source": ["new_df = pd.read_csv('spam.csv')"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"id": "Q_RpcZ9-94jQ"}, "outputs": [], "source": ["new_df['Category'].replace({'ham':0,'spam':1}, inplace=True)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "VshTtpES9-Fn", "outputId": "27670eea-0375-4d81-9625-02adcc3502a2"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"new_df\",\n  \"rows\": 5572,\n  \"fields\": [\n    {\n      \"column\": \"Category\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 0,\n        \"max\": 1,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          1,\n          0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Message\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 5157,\n        \"samples\": [\n          \"Also sir, i sent you an email about how to log into the usc payment portal. I.ll send you another message that should explain how things are back home. Have a great weekend.\",\n          \"Are you free now?can i call now?\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "new_df"}, "text/html": ["\n", "  <div id=\"df-33308b53-d943-493c-9d38-1e775dccf8d5\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Category</th>\n", "      <th>Message</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>Go until jurong point, crazy.. Available only ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>Ok lar... Joking wif u oni...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>Free entry in 2 a wkly comp to win FA Cup fina...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>U dun say so early hor... U c already then say...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>Nah I don't think he goes to usf, he lives aro...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-33308b53-d943-493c-9d38-1e775dccf8d5')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-33308b53-d943-493c-9d38-1e775dccf8d5 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-33308b53-d943-493c-9d38-1e775dccf8d5');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-ed8e51a0-7c1d-41e8-a83d-561407988530\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-ed8e51a0-7c1d-41e8-a83d-561407988530')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-ed8e51a0-7c1d-41e8-a83d-561407988530 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "text/plain": ["   Category                                            Message\n", "0         0  Go until jurong point, crazy.. Available only ...\n", "1         0                      Ok lar... Joking wif u oni...\n", "2         1  Free entry in 2 a wkly comp to win FA Cup fina...\n", "3         0  U dun say so early hor... U c already then say...\n", "4         0  Nah I don't think he goes to usf, he lives aro..."]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["new_df.head(5)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "Vdc7Bejv9-yP", "outputId": "c404ae3b-5c7b-443f-e1eb-ba259114b178"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"df\",\n  \"rows\": 5572,\n  \"fields\": [\n    {\n      \"column\": \"Category\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"spam\",\n          \"ham\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Message\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 5157,\n        \"samples\": [\n          \"Also sir, i sent you an email about how to log into the usc payment portal. I.ll send you another message that should explain how things are back home. Have a great weekend.\",\n          \"Are you free now?can i call now?\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"spam\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 0,\n        \"max\": 1,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          1,\n          0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "df"}, "text/html": ["\n", "  <div id=\"df-abc0c9f7-42b7-48a7-8101-ca2f2fc95d1a\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Category</th>\n", "      <th>Message</th>\n", "      <th>spam</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ham</td>\n", "      <td>Go until jurong point, crazy.. Available only ...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ham</td>\n", "      <td>Ok lar... Joking wif u oni...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>spam</td>\n", "      <td>Free entry in 2 a wkly comp to win FA Cup fina...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ham</td>\n", "      <td>U dun say so early hor... U c already then say...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ham</td>\n", "      <td>Nah I don't think he goes to usf, he lives aro...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-abc0c9f7-42b7-48a7-8101-ca2f2fc95d1a')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-abc0c9f7-42b7-48a7-8101-ca2f2fc95d1a button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-abc0c9f7-42b7-48a7-8101-ca2f2fc95d1a');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-7d85fc61-9c7d-4e1a-9a2f-8192daea7c33\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-7d85fc61-9c7d-4e1a-9a2f-8192daea7c33')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-7d85fc61-9c7d-4e1a-9a2f-8192daea7c33 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "text/plain": ["  Category                                            Message  spam\n", "0      ham  Go until jurong point, crazy.. Available only ...     0\n", "1      ham                      Ok lar... Joking wif u oni...     0\n", "2     spam  Free entry in 2 a wkly comp to win FA Cup fina...     1\n", "3      ham  U dun say so early hor... U c already then say...     0\n", "4      ham  Nah I don't think he goes to usf, he lives aro...     0"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head(5)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZzYTKbNU-At_", "outputId": "11705395-b4d9-460d-aa0e-9e4dad86fd1e"}, "outputs": [{"data": {"text/plain": ["(5572, 3)"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "markdown", "metadata": {"id": "Sjr07i9h-FxX"}, "source": ["#### **Train Test Split**"]}, {"cell_type": "code", "execution_count": 44, "metadata": {"id": "p6AyGReR-CdG"}, "outputs": [], "source": ["X_train, X_test, y_train, y_test = train_test_split(df.Message, df.spam,test_size=0.2)"]}, {"cell_type": "code", "execution_count": 45, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aX3imrK3-S5v", "outputId": "f980b828-14b7-4ed6-b218-bf4beb6e0eeb"}, "outputs": [{"data": {"text/plain": ["(4457,)"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train.shape"]}, {"cell_type": "code", "execution_count": 46, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5OQvtVhh-Xhv", "outputId": "c03efc9b-6b84-4b5f-a7bc-df3da80c82b4"}, "outputs": [{"data": {"text/plain": ["(1115,)"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["X_test.shape"]}, {"cell_type": "code", "execution_count": 47, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TTG3SNKJ-Y2f", "outputId": "98169305-5aeb-4330-fafe-da13f85df95d"}, "outputs": [{"data": {"text/plain": ["224     U say leh... Of course nothing happen lar. Not...\n", "940     Better. Made up for Friday and stuffed myself ...\n", "1504                      Ill be there on  &lt;#&gt;  ok.\n", "1946    Can ü call me at 10:10 to make sure dat i've w...\n", "Name: Message, dtype: object"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train[:4]"]}, {"cell_type": "code", "execution_count": 48, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zKcL2MNo-dmG", "outputId": "ef84734d-0ba7-4e49-99c3-3462a8a239b2"}, "outputs": [{"data": {"text/plain": ["224     0\n", "940     0\n", "1504    0\n", "1946    0\n", "Name: spam, dtype: int64"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["y_train[:4]"]}, {"cell_type": "markdown", "metadata": {"id": "K73_Q7bQ-juM"}, "source": ["#### **Create bag of words representation using CountVectorizer**"]}, {"cell_type": "code", "execution_count": 69, "metadata": {"id": "8iLuJHUZ-hNm"}, "outputs": [], "source": ["v = CountVectorizer()\n", "\n", "X_train_cv = v.fit_transform(X_train.values)\n", "X_test_cv = v.transform(X_test)"]}, {"cell_type": "code", "execution_count": 54, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MdDdLMmb-x1w", "outputId": "b60694ad-4994-42d2-90c6-f04d8b58e614"}, "outputs": [{"data": {"text/plain": ["<4457x7749 sparse matrix of type '<class 'numpy.int64'>'\n", "\twith 59463 stored elements in Compressed Sparse Row format>"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train_cv"]}, {"cell_type": "code", "execution_count": 55, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aRb26x5R-9KI", "outputId": "7f318eb9-1348-498c-84d3-dd1dc646d7dd"}, "outputs": [{"data": {"text/plain": ["array([0, 0, 0, ..., 0, 0, 0])"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train_cv.toarray()[:2][0]"]}, {"cell_type": "code", "execution_count": 56, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dwYm47H__CBP", "outputId": "b33e3b33-0237-4825-d75f-ccc04f2fd8f3"}, "outputs": [{"data": {"text/plain": ["(4457, 7749)"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train_cv.shape"]}, {"cell_type": "code", "execution_count": 59, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 36}, "id": "qeeN_XlB_k8i", "outputId": "45cd0ae2-1152-4bf6-be17-286bb1c69106"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'cheesy'"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["v.get_feature_names_out()[1771]"]}, {"cell_type": "code", "execution_count": 60, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XKpDl11M_EI_", "outputId": "12c23e5b-b1a3-461f-adcb-6a923a98b51a"}, "outputs": [{"data": {"text/plain": ["{'say': 5922,\n", " 'leh': 4079,\n", " 'of': 4899,\n", " 'course': 2052,\n", " 'nothing': 4836,\n", " 'happen': 3322,\n", " 'lar': 4023,\n", " 'not': 4832,\n", " 'romantic': 5811,\n", " 'jus': 3875,\n", " 'bit': 1366,\n", " 'only': 4954,\n", " 'lor': 4214,\n", " 'thk': 6844,\n", " 'nite': 4793,\n", " 'scenery': 5933,\n", " 'so': 6278,\n", " 'nice': 4776,\n", " 'better': 1330,\n", " 'made': 4309,\n", " 'up': 7175,\n", " 'for': 2925,\n", " 'friday': 2988,\n", " 'and': 959,\n", " 'stuffed': 6541,\n", " 'myself': 4670,\n", " 'like': 4118,\n", " 'pig': 5218,\n", " 'yesterday': 7695,\n", " 'now': 4845,\n", " 'feel': 2795,\n", " 'bleh': 1384,\n", " 'but': 1580,\n", " 'at': 1118,\n", " 'least': 4065,\n", " 'its': 3767,\n", " 'writhing': 7619,\n", " 'pain': 5062,\n", " 'kind': 3943,\n", " 'ill': 3614,\n", " 'be': 1272,\n", " 'there': 6822,\n", " 'on': 4943,\n", " 'lt': 4254,\n", " 'gt': 3255,\n", " 'ok': 4924,\n", " 'can': 1634,\n", " 'call': 1611,\n", " 'me': 4412,\n", " '10': 252,\n", " 'to': 6923,\n", " 'make': 4331,\n", " 'sure': 6622,\n", " 'dat': 2178,\n", " 've': 7254,\n", " 'woken': 7568,\n", " 'that': 6802,\n", " 'was': 7386,\n", " 'random': 5565,\n", " 'saw': 5921,\n", " 'my': 4666,\n", " 'old': 4936,\n", " 'roomate': 5814,\n", " 'campus': 1633,\n", " 'he': 3361,\n", " 'graduated': 3210,\n", " 'hot': 3506,\n", " 'live': 4158,\n", " 'fantasies': 2766,\n", " '08707500020': 84,\n", " 'just': 3876,\n", " '20p': 351,\n", " 'per': 5149,\n", " 'min': 4485,\n", " 'ntt': 4854,\n", " 'ltd': 4255,\n", " 'po': 5274,\n", " 'box': 1456,\n", " '1327': 287,\n", " 'croydon': 2092,\n", " 'cr9': 2061,\n", " '5wb': 562,\n", " '0870': 66,\n", " 'is': 3748,\n", " 'national': 4707,\n", " 'rate': 5572,\n", " 'correct': 2025,\n", " 'how': 3518,\n", " 'work': 7589,\n", " 'today': 6930,\n", " 'sent': 6016,\n", " 'again': 854,\n", " 'do': 2380,\n", " 'you': 7707,\n", " 'scream': 5949,\n", " 'moan': 4545,\n", " 'in': 3645,\n", " 'bed': 1287,\n", " 'princess': 5416,\n", " 'any': 988,\n", " 'chance': 1726,\n", " 'might': 4477,\n", " 'have': 3352,\n", " 'had': 3287,\n", " 'with': 7542,\n", " 'evaporated': 2653,\n", " 'as': 1083,\n", " 'soon': 6316,\n", " 'violated': 7291,\n", " 'privacy': 5423,\n", " 'by': 1596,\n", " 'stealing': 6462,\n", " 'phone': 5191,\n", " 'number': 4860,\n", " 'from': 3008,\n", " 'your': 7713,\n", " 'employer': 2578,\n", " 'paperwork': 5079,\n", " 'cool': 2013,\n", " 'all': 908,\n", " 'please': 5257,\n", " 'contact': 1987,\n", " 'or': 4983,\n", " 'will': 7511,\n", " 'report': 5717,\n", " 'supervisor': 6609,\n", " 'thankyou': 6798,\n", " 'much': 4638,\n", " 'the': 6806,\n", " 'appreciate': 1024,\n", " 'care': 1657,\n", " 'this': 6843,\n", " 'couldn': 2042,\n", " 'come': 1917,\n", " 'worse': 7601,\n", " 'time': 6892,\n", " 'think': 6836,\n", " 'should': 6118,\n", " 'go': 3154,\n", " 'honesty': 3474,\n", " 'road': 5797,\n", " 'bank': 1222,\n", " 'tomorrow': 6950,\n", " 'tough': 6992,\n", " 'decisions': 2212,\n", " 'us': 7206,\n", " 'great': 3230,\n", " 'people': 5147,\n", " 'home': 3469,\n", " 'heard': 3374,\n", " 'u4': 7104,\n", " 'while': 7483,\n", " 'rude': 5841,\n", " 'chat': 1748,\n", " 'private': 5424,\n", " 'line': 4132,\n", " '***********': 6,\n", " 'cum': 2120,\n", " 'wan': 7368,\n", " '2c': 377,\n", " 'pics': 5210,\n", " 'gettin': 3120,\n", " 'shagged': 6051,\n", " 'then': 6818,\n", " 'text': 6777,\n", " 'pix': 5231,\n", " '8552': 679,\n", " '2end': 380,\n", " 'send': 6006,\n", " 'stop': 6491,\n", " 'sam': 5885,\n", " 'xxx': 7656,\n", " 'playng': 5254,\n", " 'doors': 2418,\n", " 'game': 3060,\n", " 'racing': 5541,\n", " 'lol': 4193,\n", " 'see': 5978,\n", " 'knew': 3962,\n", " 'giving': 3143,\n", " 'break': 1490,\n", " 'few': 2810,\n", " 'times': 6893,\n", " 'woul': 7608,\n", " 'lead': 4057,\n", " 'always': 927,\n", " 'wanting': 7375,\n", " 'miss': 4512,\n", " 'curfew': 2125,\n", " 'gonna': 3175,\n", " 'gibe': 3127,\n", " 'til': 6889,\n", " 'one': 4948,\n", " 'midnight': 4475,\n", " 'movie': 4610,\n", " 'get': 3113,\n", " 'out': 5018,\n", " 'after': 848,\n", " 'need': 4732,\n", " 'getsleep': 3118,\n", " 'if': 3601,\n", " 'anything': 995,\n", " 'studdying': 6533,\n", " 'ear': 2498,\n", " 'training': 7004,\n", " 'hey': 3416,\n", " 'tmr': 6917,\n", " 'meet': 4428,\n", " 'bugis': 1555,\n", " '930': 723,\n", " 'boy': 1473,\n", " 'loved': 4234,\n", " 'gal': 3055,\n", " 'propsd': 5466,\n", " 'bt': 1542,\n", " 'she': 6071,\n", " 'didnt': 2309,\n", " 'mind': 4488,\n", " 'gv': 3278,\n", " 'lv': 4275,\n", " 'lttrs': 4257,\n", " 'her': 3407,\n", " 'frnds': 3000,\n", " 'threw': 6861,\n", " 'thm': 6846,\n", " 'decided': 2208,\n", " 'aproach': 1036,\n", " 'dt': 2472,\n", " 'truck': 7041,\n", " 'speeding': 6367,\n", " 'towards': 6995,\n", " 'wn': 7561,\n", " 'it': 3759,\n", " 'about': 754,\n", " 'hit': 3436,\n", " 'girl': 3135,\n", " 'ran': 5564,\n", " 'hell': 3391,\n", " 'saved': 5918,\n", " 'asked': 1095,\n", " 'hw': 3569,\n", " 'cn': 1877,\n", " 'run': 5851,\n", " 'fast': 2774,\n", " 'replied': 5712,\n", " 'boost': 1435,\n", " 'secret': 5969,\n", " 'energy': 2591,\n", " 'instantly': 3700,\n", " 'shouted': 6120,\n", " 'our': 5015,\n", " 'thy': 6878,\n", " 'lived': 4159,\n", " 'happily': 3330,\n", " '2gthr': 385,\n", " 'drinking': 2451,\n", " 'evrydy': 2682,\n", " 'moral': 4586,\n", " 'story': 6504,\n", " 'hv': 3566,\n", " 'free': 2973,\n", " 'msgs': 4627,\n", " 'gud': 3260,\n", " 'ni8': 4774,\n", " 'congrats': 1971,\n", " 'mobile': 4548,\n", " '3g': 454,\n", " 'videophones': 7279,\n", " 'yours': 7717,\n", " '09061744553': 196,\n", " 'videochat': 7278,\n", " 'wid': 7500,\n", " 'ur': 7192,\n", " 'mates': 4387,\n", " 'play': 5248,\n", " 'java': 3802,\n", " 'games': 3061,\n", " 'dload': 2377,\n", " 'polyh': 5304,\n", " 'music': 4656,\n", " 'noline': 4812,\n", " 'rentl': 5702,\n", " 'bx420': 1594,\n", " 'ip4': 3734,\n", " '5we': 563,\n", " '150pm': 300,\n", " 'thats': 6805,\n", " 'offer': 4906,\n", " 'claim': 1830,\n", " '150': 296,\n", " 'worth': 7603,\n", " 'discount': 2354,\n", " 'vouchers': 7319,\n", " 'yes': 7693,\n", " '85023': 675,\n", " 'savamob': 5916,\n", " 'member': 4441,\n", " 'offers': 4909,\n", " 'cs': 2100,\n", " '08717898035': 129,\n", " '00': 0,\n", " 'sub': 6549,\n", " '16': 309,\n", " 'unsub': 7170,\n", " 'reply': 5714,\n", " '1st': 330,\n", " 'week': 7432,\n", " 'no1': 4800,\n", " 'nokia': 4808,\n", " 'tone': 6952,\n", " 'mob': 4546,\n", " 'every': 2663,\n", " 'txt': 7085,\n", " '8007': 635,\n", " 'txting': 7091,\n", " 'tell': 6745,\n", " 'www': 7640,\n", " 'getzed': 3122,\n", " 'co': 1881,\n", " 'uk': 7113,\n", " 'pobox': 5275,\n", " '36504': 445,\n", " 'w45wq': 7334,\n", " 'norm150p': 4826,\n", " 'still': 6481,\n", " 'going': 3166,\n", " 'very': 7266,\n", " 'small': 6237,\n", " 'house': 3513,\n", " 'oh': 4919,\n", " 'haha': 3289,\n", " 'den': 2248,\n", " 'we': 7409,\n", " 'shld': 6099,\n", " 'went': 7454,\n", " 'too': 6964,\n", " 'gee': 3093,\n", " 'nvm': 4870,\n", " 'la': 3991,\n", " 'kaiez': 3887,\n", " 'dun': 2483,\n", " 'goin': 3164,\n", " 'jazz': 3807,\n", " 'oso': 5007,\n", " 'scared': 5930,\n", " 'hiphop': 3433,\n", " 'open': 4964,\n", " 'cant': 1646,\n", " 'catch': 1688,\n", " 'urgent': 7196,\n", " 'are': 1046,\n", " 'trying': 7051,\n", " 'todays': 6931,\n", " 'draw': 2442,\n", " 'shows': 6131,\n", " 'won': 7572,\n", " '2000': 342,\n", " 'prize': 5426,\n", " 'guaranteed': 3258,\n", " '09058094507': 170,\n", " 'land': 4010,\n", " '3030': 424,\n", " 'valid': 7236,\n", " '12hrs': 284,\n", " 'slave': 6214,\n", " 'want': 7372,\n", " 'take': 6684,\n", " 'pictures': 5213,\n", " 'yourself': 7718,\n", " 'bright': 1504,\n", " 'light': 4114,\n", " 'cell': 1709,\n", " 'swt': 6660,\n", " 'thought': 6854,\n", " 'nver': 4869,\n", " 'tired': 6899,\n", " 'doing': 2402,\n", " 'little': 4157,\n", " 'things': 6835,\n", " 'lovable': 4232,\n", " 'persons': 5169,\n", " 'coz': 2057,\n", " 'somtimes': 6309,\n", " 'those': 6851,\n", " 'occupy': 4894,\n", " 'biggest': 1347,\n", " 'part': 5095,\n", " 'their': 6811,\n", " 'hearts': 3379,\n", " 'sad': 5868,\n", " 'man': 4341,\n", " 'last': 4030,\n", " 'day': 2187,\n", " 'wife': 7503,\n", " 'did': 2306,\n", " 'nt': 4851,\n", " 'wish': 7534,\n", " 'parents': 5088,\n", " 'forgot': 2938,\n", " 'kids': 3936,\n", " 'even': 2656,\n", " 'colleagues': 1901,\n", " 'also': 921,\n", " 'north': 4829,\n", " 'carolina': 1671,\n", " 'texas': 6775,\n", " 'atm': 1125,\n", " 'would': 7609,\n", " 'gre': 3229,\n", " 'site': 6187,\n", " 'pay': 5124,\n", " 'test': 6771,\n", " 'results': 5756,\n", " 'actually': 799,\n", " 'deleted': 2238,\n", " 'website': 7422,\n", " 'blogging': 1393,\n", " 'magicalsongs': 4317,\n", " 'blogspot': 1394,\n", " 'com': 1912,\n", " 'sarcasm': 5902,\n", " 'scarcasim': 5929,\n", " 'god': 3159,\n", " 'love': 4233,\n", " 'has': 3340,\n", " 'no': 4799,\n", " 'limit': 4127,\n", " 'grace': 3209,\n", " 'measure': 4422,\n", " 'power': 5352,\n", " 'boundaries': 1451,\n", " 'may': 4404,\n", " 'endless': 2585,\n", " 'blessings': 1388,\n", " 'life': 4107,\n", " 'ta': 6672,\n", " 'jobs': 3834,\n", " 'available': 1153,\n", " 'let': 4091,\n", " 'know': 3966,\n", " 'cos': 2030,\n", " 'really': 5605,\n", " 'start': 6442,\n", " 'working': 7592,\n", " 'what': 7466,\n", " 'mean': 4415,\n", " 'left': 4073,\n", " 'early': 2501,\n", " 'check': 1758,\n", " 'thinkin': 6837,\n", " 'malaria': 4336,\n", " 'relax': 5673,\n", " 'children': 1793,\n", " 'handle': 3309,\n", " 'been': 1292,\n", " 'gastroenteritis': 3076,\n", " 'takes': 6687,\n", " 'enough': 2602,\n", " 'replace': 5709,\n", " 'loss': 4218,\n", " 'temp': 6751,\n", " 'reduce': 5645,\n", " 'give': 3140,\n", " 'meds': 4427,\n", " 'vomit': 7312,\n", " 'self': 5994,\n", " 'limiting': 4129,\n", " 'illness': 3615,\n", " 'which': 7482,\n", " 'means': 4419,\n", " 'days': 2188,\n", " 'completely': 1942,\n", " 'don': 2409,\n", " 'same': 5889,\n", " 'thing': 6834,\n", " 'wrong': 7625,\n", " 'everyso': 2668,\n", " 'often': 4916,\n", " 'panicks': 5072,\n", " 'starts': 6445,\n", " 'bout': 1452,\n", " 'bein': 1307,\n", " 'good': 3177,\n", " 'hello': 3393,\n", " 'yeah': 7681,\n", " 'got': 3194,\n", " 'bath': 1246,\n", " 'hair': 3293,\n", " 'll': 4166,\n", " 'when': 7472,\n", " 'done': 2411,\n", " 'valued': 7240,\n", " 'customer': 2135,\n", " 'am': 929,\n", " 'pleased': 5258,\n", " 'advise': 832,\n", " 'following': 2909,\n", " 'recent': 5624,\n", " 'review': 5769,\n", " 'awarded': 1169,\n", " '1500': 297,\n", " 'bonus': 1425,\n", " '09066368470': 228,\n", " 'clearly': 1850,\n", " 'fault': 2781,\n", " 'dad': 2150,\n", " 'wanted': 7374,\n", " 'talk': 6692,\n", " 'apartment': 1002,\n", " 'late': 4033,\n", " 'omw': 4942,\n", " 'ha': 3284,\n", " 'popped': 5317,\n", " 'down': 2431,\n", " 'loo': 4203,\n", " 'ed': 2521,\n", " 'dont': 2413,\n", " 'bring': 1509,\n", " 'some': 6291,\n", " 'food': 2916,\n", " 'wat': 7392,\n", " 'liao': 4098,\n", " 'where': 7476,\n", " 're': 5587,\n", " 'opposite': 4976,\n", " 'side': 6147,\n", " 'dropped': 2461,\n", " 'off': 4901,\n", " 'poor': 5312,\n", " 'lmao': 4169,\n", " 'prob': 5428,\n", " '400': 470,\n", " 'xmas': 7651,\n", " 'reward': 5770,\n", " 'waiting': 7350,\n", " 'computer': 1949,\n", " 'randomly': 5567,\n", " 'picked': 5207,\n", " 'loyal': 4248,\n", " 'customers': 2137,\n", " 'receive': 5620,\n", " '09066380611': 230,\n", " 'planning': 5244,\n", " 'chennai': 1773,\n", " 'way': 7406,\n", " 'dinner': 2332,\n", " 'library': 4101,\n", " 'service': 6027,\n", " 'representative': 5719,\n", " 'freephone': 2980,\n", " '0808': 54,\n", " '145': 292,\n", " '4742': 500,\n", " 'between': 1333,\n", " '9am': 733,\n", " '11pm': 274,\n", " '1000': 254,\n", " 'cash': 1680,\n", " '5000': 533,\n", " 'though': 6853,\n", " 'membership': 4443,\n", " '100': 253,\n", " '000': 1,\n", " 'jackpot': 3784,\n", " 'word': 7587,\n", " '81010': 647,\n", " 'dbuk': 2191,\n", " 'net': 4751,\n", " 'lccltd': 4052,\n", " '4403ldnw1a7rw18': 488,\n", " 'gran': 3214,\n", " 'onlyfound': 4956,\n", " 'afew': 837,\n", " 'ago': 865,\n", " 'cusoon': 2131,\n", " 'honi': 3478,\n", " 'almost': 914,\n", " 'happy': 3332,\n", " 'babe': 1187,\n", " 'ya': 7666,\n", " 'baby': 1190,\n", " 'im': 3618,\n", " 'nasty': 4701,\n", " 'filthyguys': 2841,\n", " 'fancy': 2764,\n", " 'sexy': 6042,\n", " 'bitch': 1367,\n", " 'slo': 6226,\n", " 'hard': 3333,\n", " '4msgs': 516,\n", " 'future': 3043,\n", " 'planned': 5243,\n", " 'result': 5755,\n", " 'best': 1326,\n", " 'present': 5393,\n", " 'enjoy': 2598,\n", " 'blake': 1378,\n", " 'address': 808,\n", " 'carlos': 1669,\n", " 'him': 3428,\n", " 'lost': 4219,\n", " 'answering': 980,\n", " 'his': 3434,\n", " 'gotta': 3200,\n", " 'collect': 1902,\n", " 'da': 2148,\n", " 'car': 1652,\n", " 'lei': 4080,\n", " 'de': 2193,\n", " 'seeing': 5980,\n", " 'online': 4952,\n", " 'shop': 6105,\n", " 'india': 3665,\n", " 'series': 6022,\n", " 'many': 4355,\n", " 'years': 7683,\n", " 'south': 6340,\n", " 'african': 846,\n", " 'soil': 6285,\n", " 'outside': 5026,\n", " 'islands': 3754,\n", " 'head': 3362,\n", " 'rock': 5802,\n", " 'into': 3716,\n", " 'thanks': 6794,\n", " 'chikku': 1789,\n", " 'nyt': 4877,\n", " 'din': 2328,\n", " '420': 480,\n", " 'wouldn': 7611,\n", " 'leave': 4067,\n", " 'an': 955,\n", " 'hour': 3511,\n", " 'sound': 6335,\n", " 'didn': 2308,\n", " 'hi': 3419,\n", " 'finally': 2843,\n", " 'completed': 1941,\n", " 'putting': 5513,\n", " 'business': 1577,\n", " 'put': 5510,\n", " 'ass': 1103,\n", " 'facebook': 2740,\n", " 'most': 4596,\n", " 'ever': 2662,\n", " 'met': 4467,\n", " 'why': 7495,\n", " 'picture': 5212,\n", " 'room': 5813,\n", " 'hurt': 3560,\n", " 'guys': 3277,\n", " 'never': 4761,\n", " 'invite': 3725,\n", " 'anywhere': 999,\n", " 'fine': 2847,\n", " 'knows': 3968,\n", " 'wants': 7376,\n", " 'questions': 5528,\n", " 'die': 2310,\n", " 'toot': 6969,\n", " 'fringe': 2996,\n", " 'jane': 3795,\n", " 'babes': 1188,\n", " 'wrk': 7620,\n", " 'lst': 4253,\n", " 'foned': 2913,\n", " 'already': 918,\n", " 'cover': 2055,\n", " 'chuck': 1822,\n", " 'sorry': 6326,\n", " 'later': 4036,\n", " 'more': 4587,\n", " 'dogging': 2396,\n", " 'area': 1047,\n", " '***********': 238,\n", " 'join': 3840,\n", " 'minded': 4489,\n", " 'arrange': 1070,\n", " 'evening': 2657,\n", " '50': 531,\n", " 'minapn': 4487,\n", " 'ls278bb': 4252,\n", " 'mu': 4636,\n", " 'table': 6673,\n", " 'lambda': 4007,\n", " 'anthony': 985,\n", " 'bf': 1337,\n", " 'jamster': 3792,\n", " 'videosound': 7281,\n", " 'gold': 3168,\n", " 'club': 1867,\n", " 'credits': 2081,\n", " 'new': 4764,\n", " 'videosounds': 7282,\n", " 'logos': 4191,\n", " 'musicnews': 4657,\n", " 'fun': 3030,\n", " 'help': 3396,\n", " '09701213186': 249,\n", " 'swing': 6655,\n", " 'here': 3408,\n", " 'firsg': 2861,\n", " 'birthday': 1364,\n", " 'feb': 2791,\n", " 'keep': 3910,\n", " 'glad': 3144,\n", " 'talking': 6695,\n", " 'paper': 5078,\n", " 'morn': 4589,\n", " 'aft': 847,\n", " 'cps': 2059,\n", " 'causing': 1694,\n", " 'outages': 5020,\n", " 'conserve': 1981,\n", " 'someone': 6294,\n", " 'them': 6814,\n", " 'they': 6830,\n", " 'anyone': 992,\n", " 'except': 2690,\n", " 'nit': 4792,\n", " 'started': 6443,\n", " 'skye': 6207,\n", " 'whenever': 7473,\n", " 'hook': 3483,\n", " '09066364589': 226,\n", " 'bored': 1438,\n", " 'housewives': 3515,\n", " 'date': 2180,\n", " '0871750': 126,\n", " '77': 617,\n", " '11': 264,\n", " '10p': 263,\n", " 'landlines': 4013,\n", " 'does': 2390,\n", " 'cinema': 1826,\n", " 'plus': 5269,\n", " 'drink': 2449,\n", " 'appeal': 1012,\n", " 'tomo': 6948,\n", " 'fr': 2961,\n", " 'thriller': 6862,\n", " 'director': 2339,\n", " 'mac': 4298,\n", " '30': 419,\n", " 'read': 5592,\n", " 'shame': 6058,\n", " 'runs': 5853,\n", " 'blame': 1379,\n", " 'long': 4200,\n", " 'tonight': 6959,\n", " 'booked': 1428,\n", " 'kb': 3907,\n", " 'sat': 5906,\n", " 'other': 5009,\n", " 'lessons': 4090,\n", " 'ah': 867,\n", " 'night': 4783,\n", " 'confirm': 1965,\n", " 'lodging': 4183,\n", " 'said': 5874,\n", " 'eat': 2512,\n", " 'shit': 6092,\n", " 'bucks': 1547,\n", " 'something': 6300,\n", " 'moves': 4609,\n", " 'however': 3522,\n", " 'next': 4772,\n", " '6hrs': 598,\n", " 'doctor': 2386,\n", " 'sunshine': 6603,\n", " 'hols': 3467,\n", " 'med': 4425,\n", " 'holiday': 3464,\n", " 'stamped': 6429,\n", " 'envelope': 2617,\n", " 'drinks': 2452,\n", " '113': 266,\n", " 'bray': 1486,\n", " 'wicklow': 7499,\n", " 'eire': 2548,\n", " 'quiz': 5535,\n", " 'saturday': 5913,\n", " 'type': 7099,\n", " 'find': 2845,\n", " 'include': 3652,\n", " 'these': 6824,\n", " 'details': 2280,\n", " 'getting': 3121,\n", " 'messages': 4462,\n", " 'train': 7002,\n", " 'back': 1195,\n", " 'northampton': 4830,\n", " 'afraid': 844,\n", " 'staying': 6457,\n", " 'skyving': 6210,\n", " 'ho': 3453,\n", " 'around': 1067,\n", " 'wednesday': 7428,\n", " 'comedy': 1918,\n", " 'weekends': 7435,\n", " '900': 718,\n", " '09061701851': 190,\n", " 'code': 1890,\n", " 'k61': 3884,\n", " '12hours': 283,\n", " 'having': 3357,\n", " 'project': 5447,\n", " 'meeting': 4430,\n", " 'dunno': 2484,\n", " 'close': 1858,\n", " 'oredi': 4994,\n", " 'ma': 4294,\n", " 'fan': 2762,\n", " 'lock': 4181,\n", " 'keypad': 3922,\n", " 'lot': 4220,\n", " 'happened': 3324,\n", " 'feels': 2799,\n", " 'quiet': 5531,\n", " 'beth': 1329,\n", " 'aunts': 1145,\n", " 'charlie': 1742,\n", " 'lots': 4221,\n", " 'helen': 3390,\n", " 'mo': 4544,\n", " 'buzzzz': 1592,\n", " 'grins': 3240,\n", " 'buzz': 1591,\n", " 'chest': 1778,\n", " 'cock': 1887,\n", " 'vibrator': 7272,\n", " 'shake': 6054,\n", " 'bigger': 1346,\n", " 'than': 6790,\n", " 'reached': 5590,\n", " 'sch': 5934,\n", " 'laptop': 4022,\n", " 'noe': 4803,\n", " 'infra': 3679,\n", " 'slow': 6230,\n", " 'were': 7457,\n", " 'together': 6933,\n", " 'hiya': 3440,\n", " 'hav': 3350,\n", " 'signal': 6152,\n", " 'haven': 3353,\n", " 'seen': 5986,\n", " 'neither': 4746,\n", " 'unusual': 7174,\n", " 'itself': 3768,\n", " 'case': 1679,\n", " 'sort': 6327,\n", " 'hugs': 3542,\n", " 'snogs': 6273,\n", " 'jolly': 3848,\n", " 'tickets': 6881,\n", " 'eve': 2654,\n", " 'speak': 6354,\n", " 'before': 1298,\n", " 'bathe': 1247,\n", " 'thnk': 6847,\n", " 'calling': 1623,\n", " 'mag': 4313,\n", " 'avo': 1161,\n", " 'point': 5294,\n", " 'thank': 6793,\n", " 'monday': 4571,\n", " 'mum': 4645,\n", " 'dentist': 2253,\n", " 'inside': 3694,\n", " 'office': 4910,\n", " 'filling': 2835,\n", " 'forms': 2946,\n", " 'aiyah': 882,\n", " 'tok': 6936,\n", " 'song': 6311,\n", " 'dedicated': 2217,\n", " 'dedicate': 2216,\n", " 'valuable': 7238,\n", " 'first': 2862,\n", " 'rply': 5831,\n", " 'pic': 5205,\n", " 'gas': 3075,\n", " 'station': 6452,\n", " 'block': 1391,\n", " 'away': 1170,\n", " 'drive': 2454,\n", " 'right': 5780,\n", " 'since': 6170,\n", " 'armenia': 1062,\n", " 'ends': 2588,\n", " 'swann': 6639,\n", " 'howard': 3519,\n", " 'oooh': 4961,\n", " 'ridden': 5778,\n", " 'ey': 2731,\n", " 'thinking': 6838,\n", " 'pls': 5265,\n", " 'name': 4686,\n", " 'dey': 2290,\n", " 'swell': 6651,\n", " 'making': 4335,\n", " 'thesmszone': 6827,\n", " 'lets': 4092,\n", " 'anonymous': 973,\n", " 'masked': 4377,\n", " 'sending': 6008,\n", " 'message': 4460,\n", " 'potential': 5344,\n", " 'abuse': 763,\n", " '7250i': 610,\n", " 'win': 7514,\n", " 'auction': 1138,\n", " '86021': 681,\n", " 'hg': 3418,\n", " 'suite342': 6587,\n", " '2lands': 389,\n", " 'row': 5826,\n", " 'w1jhl': 7331,\n", " 'okay': 4925,\n", " 'over': 5032,\n", " 'dating': 2183,\n", " 'two': 7083,\n", " 'sport': 6399,\n", " 'radio': 5543,\n", " 'connection': 1976,\n", " 'coincidence': 1894,\n", " 'loan': 4174,\n", " 'purpose': 5505,\n", " '500': 532,\n", " '75': 613,\n", " 'homeowners': 3470,\n", " 'tenants': 6754,\n", " 'welcome': 7446,\n", " 'previously': 5408,\n", " 'refused': 5657,\n", " '0800': 42,\n", " '1956669': 319,\n", " 'brin': 1508,\n", " 'aiya': 881,\n", " 'mayb': 4405,\n", " 'neva': 4759,\n", " 'set': 6031,\n", " 'properly': 5462,\n", " 'sheet': 6072,\n", " 'wif': 7502,\n", " 'dolls': 2407,\n", " 'patrick': 5117,\n", " 'swayze': 6644,\n", " 'fuck': 3017,\n", " 'family': 2761,\n", " 'rhode': 5774,\n", " 'island': 3753,\n", " 'wherever': 7478,\n", " 'leaving': 4069,\n", " 'alone': 915,\n", " 'bong': 1424,\n", " 'match': 4384,\n", " 'heading': 3365,\n", " 'prediction': 5380,\n", " 'weeks': 7437,\n", " 'accessible': 770,\n", " '08709501522': 90,\n", " '139': 288,\n", " 'la3': 3993,\n", " '2wu': 415,\n", " 'honey': 3475,\n", " 'entry': 2613,\n", " 'weekly': 7436,\n", " 'comp': 1929,\n", " 'ipod': 3737,\n", " 'pod': 5291,\n", " '80182': 641,\n", " 'std': 6459,\n", " 'apply': 1019,\n", " '08452810073': 64,\n", " '18': 314,\n", " 'situations': 6192,\n", " 'second': 5966,\n", " 'loosing': 4212,\n", " 'takin': 6688,\n", " 'shower': 6126,\n", " 'bb': 1254,\n", " 'wont': 7579,\n", " 'use': 7210,\n", " 'opinion': 4971,\n", " 'jada': 3787,\n", " 'kusruthi': 3984,\n", " 'silent': 6159,\n", " 'spl': 6385,\n", " 'character': 1735,\n", " 'matured': 4398,\n", " 'stylish': 6547,\n", " 'simple': 6165,\n", " 'purchase': 5500,\n", " 'stuff': 6540,\n", " 'mail': 4323,\n", " 'spoil': 6392,\n", " 'well': 7448,\n", " 'evry': 2680,\n", " ...}"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["v.vocabulary_"]}, {"cell_type": "code", "execution_count": 62, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "z4AJRSwx_t1J", "outputId": "d879a003-93a6-46fa-891e-79e4e92f20b3"}, "outputs": [{"data": {"text/plain": ["array([0, 0, 0, ..., 0, 0, 0])"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train_np = X_train_cv.toarray()\n", "X_train_np[0]"]}, {"cell_type": "code", "execution_count": 63, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FsDOq5Tp_8b4", "outputId": "6652c471-1a2c-4f7e-c40c-bbf5c278593a"}, "outputs": [{"data": {"text/plain": ["(array([1366, 2052, 3322, 3875, 4023, 4079, 4214, 4776, 4793, 4832, 4836,\n", "        4899, 4954, 5811, 5922, 5933, 6278, 6844]),)"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["np.where(X_train_np[0]!=0)"]}, {"cell_type": "markdown", "metadata": {"id": "xc3HknAKAUJR"}, "source": ["#### **Naive <PERSON>es Classifier**"]}, {"cell_type": "code", "execution_count": 68, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 74}, "id": "ec-sGjslAOsY", "outputId": "80b495a2-3b15-4509-cd7c-d1aeed35e0b7"}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-1 {color: black;background-color: white;}#sk-container-id-1 pre{padding: 0;}#sk-container-id-1 div.sk-toggleable {background-color: white;}#sk-container-id-1 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-1 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-1 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-1 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-1 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-1 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-1 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-1 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-1 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-1 div.sk-item {position: relative;z-index: 1;}#sk-container-id-1 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-1 div.sk-item::before, #sk-container-id-1 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-1 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-1 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-1 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-1 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-1 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-1 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-1 div.sk-label-container {text-align: center;}#sk-container-id-1 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-1 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>MultinomialNB()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">MultinomialNB</label><div class=\"sk-toggleable__content\"><pre>MultinomialNB()</pre></div></div></div></div></div>"], "text/plain": ["MultinomialNB()"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["model = MultinomialNB()\n", "model.fit(X_train_cv, y_train)"]}, {"cell_type": "code", "execution_count": 70, "metadata": {"id": "iTYdqKQyAZ1B"}, "outputs": [], "source": ["y_pred = model.predict(X_test_cv)"]}, {"cell_type": "code", "execution_count": 73, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bRYregEqAt1f", "outputId": "8a335fdc-6a2f-4ff7-cf24-eeeccf7889e4"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.99      0.99      0.99       969\n", "           1       0.96      0.93      0.94       146\n", "\n", "    accuracy                           0.99      1115\n", "   macro avg       0.97      0.96      0.97      1115\n", "weighted avg       0.99      0.99      0.99      1115\n", "\n"]}], "source": ["print(classification_report(y_test, y_pred))"]}, {"cell_type": "markdown", "metadata": {"id": "-f7PWltCB767"}, "source": ["#### **Test on a random datapoint**"]}, {"cell_type": "code", "execution_count": 102, "metadata": {"id": "vZS1W-4zBvax"}, "outputs": [], "source": ["message = {\"Upto 20% off on parking, exclusing offer just for you\"}"]}, {"cell_type": "code", "execution_count": 103, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6Dvs3qn4CBvL", "outputId": "1dc87a5c-3c5b-4570-dcd4-afba25aaf326"}, "outputs": [{"data": {"text/plain": ["array([1])"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["message_cnt = v.transform(message)\n", "\n", "model.predict(message_cnt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "zMQd_pFHCTtR"}, "outputs": [], "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 1}