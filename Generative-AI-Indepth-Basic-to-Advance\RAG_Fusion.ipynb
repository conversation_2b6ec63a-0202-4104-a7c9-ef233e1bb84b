{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/sunnysavita10/Generative-AI-Indepth-Basic-to-Advance/blob/main/RAG_Fusion.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "EpLLP3t0mvaI"}, "source": ["# RAG Fusion"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RRYSu48huSUW", "outputId": "d1881d05-974b-4747-975b-a2dc7a3da3df"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m974.6/974.6 kB\u001b[0m \u001b[31m4.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h\u001b[31mERROR: Could not find a version that satisfies the requirement huggingftiktace_hub (from versions: none)\u001b[0m\u001b[31m\n", "\u001b[0m\u001b[31mERROR: No matching distribution found for huggingftiktace_hub\u001b[0m\u001b[31m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m559.5/559.5 kB\u001b[0m \u001b[31m3.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.4/2.4 MB\u001b[0m \u001b[31m22.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m92.0/92.0 kB\u001b[0m \u001b[31m6.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m62.4/62.4 kB\u001b[0m \u001b[31m5.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m41.3/41.3 kB\u001b[0m \u001b[31m4.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.8/6.8 MB\u001b[0m \u001b[31m45.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m59.9/59.9 kB\u001b[0m \u001b[31m5.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m107.0/107.0 kB\u001b[0m \u001b[31m9.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.3/67.3 kB\u001b[0m \u001b[31m5.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m283.7/283.7 kB\u001b[0m \u001b[31m26.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.7/1.7 MB\u001b[0m \u001b[31m62.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.6/67.6 kB\u001b[0m \u001b[31m6.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m145.0/145.0 kB\u001b[0m \u001b[31m12.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m4.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m71.9/71.9 kB\u001b[0m \u001b[31m5.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m53.6/53.6 kB\u001b[0m \u001b[31m4.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m5.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m5.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.0/46.0 kB\u001b[0m \u001b[31m4.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m52.5/52.5 kB\u001b[0m \u001b[31m5.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m130.5/130.5 kB\u001b[0m \u001b[31m6.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m341.4/341.4 kB\u001b[0m \u001b[31m31.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.4/3.4 MB\u001b[0m \u001b[31m76.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.2/1.2 MB\u001b[0m \u001b[31m59.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m130.2/130.2 kB\u001b[0m \u001b[31m11.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m307.7/307.7 kB\u001b[0m \u001b[31m24.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.8/86.8 kB\u001b[0m \u001b[31m7.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Building wheel for pypika (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m227.1/227.1 kB\u001b[0m \u001b[31m1.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.3/21.3 MB\u001b[0m \u001b[31m52.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["!pip -q install langchain huggingftiktace_hub oken pypdf\n", "!pip -q install google-generativeai chromadb\n", "!pip -q install sentence_transformers"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "nEGa4_ghPMBt", "outputId": "69bcd619-304d-4eb8-e170-2c54fc44214d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting langchain-community\n", "  Downloading langchain_community-0.2.5-py3-none-any.whl (2.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.2/2.2 MB\u001b[0m \u001b[31m9.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (2.0.30)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (3.9.5)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain-community)\n", "  Downloading dataclasses_json-0.6.7-py3-none-any.whl (28 kB)\n", "Collecting langchain<0.3.0,>=0.2.5 (from langchain-community)\n", "  Using cached langchain-0.2.5-py3-none-any.whl (974 kB)\n", "Collecting langchain-core<0.3.0,>=0.2.7 (from langchain-community)\n", "  Downloading langchain_core-0.2.8-py3-none-any.whl (315 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m315.8/315.8 kB\u001b[0m \u001b[31m19.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langsmith<0.2.0,>=0.1.0 (from langchain-community)\n", "  Downloading langsmith-0.1.79-py3-none-any.whl (125 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m125.3/125.3 kB\u001b[0m \u001b[31m9.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (1.25.2)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (8.3.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (4.0.3)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading marshmallow-3.21.3-py3-none-any.whl (49 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.2/49.2 kB\u001b[0m \u001b[31m4.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Collecting langchain-text-splitters<0.3.0,>=0.2.0 (from langchain<0.3.0,>=0.2.5->langchain-community)\n", "  Downloading langchain_text_splitters-0.2.1-py3-none-any.whl (23 kB)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.5->langchain-community) (2.7.3)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain-core<0.3.0,>=0.2.7->langchain-community)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.7->langchain-community) (24.1)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.0->langchain-community) (3.10.5)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (2024.6.2)\n", "Requirement already satisfied: typing-extensions>=4.6.0 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain-community) (4.12.2)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain-community) (3.0.3)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.7->langchain-community)\n", "  Downloading jsonpointer-3.0.0-py2.py3-none-any.whl (7.6 kB)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.5->langchain-community) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.18.4 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.5->langchain-community) (2.18.4)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: mypy-extensions, marshmallow, jsonpointer, typing-inspect, jsonpatch, langsmith, dataclasses-json, langchain-core, langchain-text-splitters, langchain, langchain-community\n", "Successfully installed dataclasses-json-0.6.7 jsonpatch-1.33 jsonpointer-3.0.0 langchain-0.2.5 langchain-community-0.2.5 langchain-core-0.2.8 langchain-text-splitters-0.2.1 langsmith-0.1.79 marshmallow-3.21.3 mypy-extensions-1.0.0 typing-inspect-0.9.0\n"]}], "source": ["!pip install -U langchain-community"]}, {"cell_type": "markdown", "metadata": {"id": "Fw9wTjZG9I30"}, "source": ["### Download the Data & Utils"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jZpiqO_eM9ZF"}, "outputs": [], "source": ["import textwrap\n", "def wrap_text(text, width=90): #preserve_newlines\n", "    # Split the input text into lines based on newline characters\n", "    lines = text.split('\\n')\n", "\n", "    # Wrap each line individually\n", "    wrapped_lines = [textwrap.fill(line, width=width) for line in lines]\n", "\n", "    # Join the wrapped lines back together using newline characters\n", "    wrapped_text = '\\n'.join(wrapped_lines)\n", "\n", "    return wrapped_text\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "dMHgDv1mPDYv"}, "outputs": [], "source": ["import os\n", "from google.colab import userdata\n", "\n", "GOOGLE_API_KEY = userdata.get('GOOGLE_API_KEY')\n", "os.environ[\"GOOGLE_API_KEY\"] = GOOGLE_API_KEY"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "A-wSv_zVOzje"}, "outputs": [], "source": ["%pip install --upgrade --quiet  langchain-google-genai"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "qoUdE7I-O2F-"}, "outputs": [], "source": ["from langchain_google_genai import ChatGoogleGenerativeAI"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "B4hEWBvCO6pg", "outputId": "c2f15165-2a14-4887-c779-26c78bb90663"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The internet sprawled, a vast and tangled sea,\n", "Of knowledge and nonsense, for all the world to see.\n", "And developers toiled, with code in hand and mind,\n", "To navigate the chaos, some meaning to find.\n", "\n", "Then rose a champion, a library so grand,\n", "With chains of logic, it took a mighty stand.\n", "<PERSON><PERSON><PERSON><PERSON>, they called it, a name whispered with awe,\n", "For it tamed the wild data, without a single flaw.\n", "\n", "It spoke to chatbots, with wit both sharp and keen,\n", "And whispered secrets to agents, yet unseen.\n", "It wove together, the threads of every source,\n", "Unleashing knowledge with unstoppable force.\n", "\n", "From <PERSON>'s embrace, it extended its reach,\n", "To Javascript's domain, where wonders it would preach.\n", "With modules and classes, a symphony of tools,\n", "It built bridges of understanding, across digital pools.\n", "\n", "But power comes with caution, a lesson learned in time,\n", "For bias lurks in shadows, in every single rhyme.\n", "So wield this mighty weapon, with a conscience clear and bright,\n", "And <PERSON><PERSON><PERSON><PERSON>'s potential, will shine an endless light.\n", "\n", "So sing a song of progress, of language understood,\n", "Of barriers broken, where once confusion stood.\n", "For <PERSON><PERSON><PERSON><PERSON> stands triumphant, a beacon in the fray,\n", "Guiding us to knowledge, in a brighter, bolder way. \n", "\n"]}], "source": ["llm = ChatGoogleGenerativeAI(model=\"gemini-1.5-pro\")\n", "result = llm.invoke(\"Write a ballad about <PERSON><PERSON><PERSON><PERSON>\")\n", "print(result.content)"]}, {"cell_type": "markdown", "metadata": {"id": "QmX0tg21rHYG"}, "source": ["## Google"]}, {"cell_type": "markdown", "metadata": {"id": "PMV2IlE4GkMH"}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "K1i89ZetrjxS"}, "outputs": [], "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain.vectorstores.chroma import Chroma\n", "import langchain"]}, {"cell_type": "markdown", "metadata": {"id": "a6_wyaR7GmzK"}, "source": ["## Load in Docs"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "WPiv5FGi-AN-"}, "outputs": [], "source": ["from langchain.document_loaders import DirectoryLoader\n", "from langchain.document_loaders import TextLoader"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "q0oi9VRKPcYP", "outputId": "9fd3685e-1fc6-46b9-e407-88042e71b048"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mounted at /content/drive\n"]}], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "qRjNB_MKP0Jm"}, "outputs": [], "source": ["data_path=\"/content/drive/MyDrive/English\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "U7wIIk-liy-Y", "outputId": "747c87b2-f820-4b93-aad0-2e8c1a56e84a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting unstructured\n", "  Downloading unstructured-0.14.6-py3-none-any.whl (2.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m9.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: chardet in /usr/local/lib/python3.10/dist-packages (from unstructured) (5.2.0)\n", "Collecting filetype (from unstructured)\n", "  Downloading filetype-1.2.0-py2.py3-none-any.whl (19 kB)\n", "Collecting python-magic (from unstructured)\n", "  Downloading python_magic-0.4.27-py2.py3-none-any.whl (13 kB)\n", "Requirement already satisfied: lxml in /usr/local/lib/python3.10/dist-packages (from unstructured) (4.9.4)\n", "Requirement already satisfied: nltk in /usr/local/lib/python3.10/dist-packages (from unstructured) (3.8.1)\n", "Requirement already satisfied: tabulate in /usr/local/lib/python3.10/dist-packages (from unstructured) (0.9.0)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from unstructured) (2.31.0)\n", "Requirement already satisfied: beautifulsoup4 in /usr/local/lib/python3.10/dist-packages (from unstructured) (4.12.3)\n", "Collecting emoji (from unstructured)\n", "  Downloading emoji-2.12.1-py3-none-any.whl (431 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m431.4/431.4 kB\u001b[0m \u001b[31m32.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: dataclasses-json in /usr/local/lib/python3.10/dist-packages (from unstructured) (0.6.7)\n", "Collecting python-iso639 (from unstructured)\n", "  Downloading python_iso639-2024.4.27-py3-none-any.whl (274 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m274.7/274.7 kB\u001b[0m \u001b[31m23.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langdetect (from unstructured)\n", "  Downloading langdetect-1.0.9.tar.gz (981 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m981.5/981.5 kB\u001b[0m \u001b[31m36.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from unstructured) (1.25.2)\n", "Collecting rapidfuzz (from unstructured)\n", "  Downloading rapidfuzz-3.9.3-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.4 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.4/3.4 MB\u001b[0m \u001b[31m40.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: backoff in /usr/local/lib/python3.10/dist-packages (from unstructured) (2.2.1)\n", "Requirement already satisfied: typing-extensions in /usr/local/lib/python3.10/dist-packages (from unstructured) (4.12.2)\n", "Collecting unstructured-client (from unstructured)\n", "  Downloading unstructured_client-0.23.7-py3-none-any.whl (40 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m41.0/41.0 kB\u001b[0m \u001b[31m3.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: wrapt in /usr/local/lib/python3.10/dist-packages (from unstructured) (1.14.1)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from unstructured) (4.66.4)\n", "Requirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.10/dist-packages (from beautifulsoup4->unstructured) (2.5)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->unstructured) (3.21.3)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->unstructured) (0.9.0)\n", "Requirement already satisfied: six in /usr/local/lib/python3.10/dist-packages (from langdetect->unstructured) (1.16.0)\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured) (8.1.7)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured) (1.4.2)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured) (2024.5.15)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured) (2024.6.2)\n", "Collecting deepdiff>=6.0 (from unstructured-client->unstructured)\n", "  Downloading deepdiff-7.0.1-py3-none-any.whl (80 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m80.8/80.8 kB\u001b[0m \u001b[31m10.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: httpx>=0.27.0 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured) (0.27.0)\n", "Collecting jsonpath-python>=1.0.6 (from unstructured-client->unstructured)\n", "  Downloading jsonpath_python-1.0.6-py3-none-any.whl (7.6 kB)\n", "Requirement already satisfied: mypy-extensions>=1.0.0 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured) (1.0.0)\n", "Requirement already satisfied: nest-asyncio>=1.6.0 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured) (1.6.0)\n", "Requirement already satisfied: packaging>=23.1 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured) (24.1)\n", "Collecting pypdf>=4.0 (from unstructured-client->unstructured)\n", "  Downloading pypdf-4.2.0-py3-none-any.whl (290 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m290.4/290.4 kB\u001b[0m \u001b[31m24.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from unstructured-client->unstructured) (2.8.2)\n", "Collecting requests-toolbelt>=1.0.0 (from unstructured-client->unstructured)\n", "  Downloading requests_toolbelt-1.0.0-py2.py3-none-any.whl (54 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m54.5/54.5 kB\u001b[0m \u001b[31m5.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting ordered-set<4.2.0,>=4.1.0 (from deepdiff>=6.0->unstructured-client->unstructured)\n", "  Downloading ordered_set-4.1.0-py3-none-any.whl (7.6 kB)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->unstructured-client->unstructured) (3.7.1)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->unstructured-client->unstructured) (1.0.5)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->unstructured-client->unstructured) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx>=0.27.0->unstructured-client->unstructured) (0.14.0)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx>=0.27.0->unstructured-client->unstructured) (1.2.1)\n", "Building wheels for collected packages: langdetect\n", "  Building wheel for langdetect (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for langdetect: filename=langdetect-1.0.9-py3-none-any.whl size=993227 sha256=5bdcea41aaefbe671a17290af130eb2bbbd02b58a1e68b56f4a6e1bcf73f19e3\n", "  Stored in directory: /root/.cache/pip/wheels/95/03/7d/59ea870c70ce4e5a370638b5462a7711ab78fba2f655d05106\n", "Successfully built langdetect\n", "Installing collected packages: filetype, rapidfuzz, python-magic, python-iso639, pypdf, ordered-set, langdetect, jsonpath-python, emoji, requests-toolbelt, deepdiff, unstructured-client, unstructured\n", "Successfully installed deepdiff-7.0.1 emoji-2.12.1 filetype-1.2.0 jsonpath-python-1.0.6 langdetect-1.0.9 ordered-set-4.1.0 pypdf-4.2.0 python-iso639-2024.4.27 python-magic-0.4.27 rapidfuzz-3.9.3 requests-toolbelt-1.0.0 unstructured-0.14.6 unstructured-client-0.23.7\n"]}], "source": ["!pip install unstructured"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"background_save": true, "base_uri": "https://localhost:8080/"}, "id": "cMUCoHNk-Fdi", "outputId": "d97f8ed6-c507-4bc5-ea03-b2885180c0aa"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "  0%|          | 0/646 [00:00<?, ?it/s]\u001b[A[nltk_data] Downloading package punkt to /root/nltk_data...\n", "[nltk_data]   Unzipping tokenizers/punkt.zip.\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /root/nltk_data...\n", "[nltk_data]   Unzipping taggers/averaged_perceptron_tagger.zip.\n", "\n", "  0%|          | 1/646 [00:10<1:50:03, 10.24s/it]\u001b[A\n", "  0%|          | 2/646 [00:10<46:54,  4.37s/it]  \u001b[A\n", "  0%|          | 3/646 [00:10<27:39,  2.58s/it]\u001b[A\n", "  1%|          | 4/646 [00:11<18:34,  1.74s/it]\u001b[A\n", "  1%|          | 5/646 [00:11<13:35,  1.27s/it]\u001b[A\n", "  1%|          | 6/646 [00:12<09:56,  1.07it/s]\u001b[A\n", "  1%|          | 7/646 [00:26<56:01,  5.26s/it]\u001b[A\n", "  1%|▏         | 9/646 [00:26<29:23,  2.77s/it]\u001b[A\n", "  2%|▏         | 10/646 [00:26<22:08,  2.09s/it]\u001b[A\n", "  2%|▏         | 12/646 [00:26<13:07,  1.24s/it]\u001b[A\n", "  2%|▏         | 13/646 [00:26<10:20,  1.02it/s]\u001b[A\n", "  2%|▏         | 14/646 [00:27<08:08,  1.29it/s]\u001b[A\n", "  2%|▏         | 15/646 [00:27<06:28,  1.62it/s]\u001b[A\n", "  2%|▏         | 16/646 [00:27<05:00,  2.10it/s]\u001b[A\n", "  3%|▎         | 18/646 [00:27<03:07,  3.34it/s]\u001b[A\n", "  3%|▎         | 19/646 [00:27<02:43,  3.84it/s]\u001b[A\n", "  3%|▎         | 20/646 [00:27<02:38,  3.95it/s]\u001b[A\n", "  3%|▎         | 21/646 [00:28<02:33,  4.08it/s]\u001b[A\n", "  4%|▎         | 23/646 [00:28<01:43,  6.00it/s]\u001b[A\n", "  4%|▍         | 25/646 [00:28<01:22,  7.52it/s]\u001b[A\n", "  4%|▍         | 27/646 [00:28<01:18,  7.84it/s]\u001b[A\n", "  4%|▍         | 28/646 [00:28<01:16,  8.09it/s]\u001b[A\n", "  5%|▍         | 30/646 [00:28<01:01,  9.95it/s]\u001b[A\n", "  5%|▍         | 32/646 [00:29<01:01, 10.00it/s]\u001b[A\n", "  5%|▌         | 34/646 [00:29<00:51, 11.80it/s]\u001b[A\n", "  6%|▌         | 36/646 [00:29<00:49, 12.43it/s]\u001b[A\n", "  6%|▌         | 38/646 [00:29<00:43, 14.01it/s]\u001b[A\n", "  6%|▌         | 40/646 [00:29<00:39, 15.19it/s]\u001b[A\n", "  7%|▋         | 42/646 [00:29<00:41, 14.68it/s]\u001b[A\n", "  7%|▋         | 44/646 [00:29<00:42, 14.16it/s]\u001b[A\n", "  7%|▋         | 46/646 [00:29<00:43, 13.77it/s]\u001b[A\n", "  7%|▋         | 48/646 [00:30<00:44, 13.49it/s]\u001b[A\n", "  8%|▊         | 50/646 [00:30<00:46, 12.88it/s]\u001b[A\n", "  8%|▊         | 52/646 [00:30<00:42, 14.03it/s]\u001b[A\n", "  8%|▊         | 54/646 [00:31<01:28,  6.68it/s]\u001b[A\n", "  9%|▊         | 56/646 [00:31<01:19,  7.38it/s]\u001b[A\n", "  9%|▉         | 58/646 [00:31<01:07,  8.76it/s]\u001b[A\n", "  9%|▉         | 60/646 [00:31<00:56, 10.37it/s]\u001b[A\n", " 10%|▉         | 62/646 [00:31<00:51, 11.30it/s]\u001b[A\n", " 10%|▉         | 64/646 [00:31<00:58,  9.87it/s]\u001b[A\n", " 10%|█         | 66/646 [00:32<00:52, 10.98it/s]\u001b[A\n", " 11%|█         | 68/646 [00:32<00:56, 10.18it/s]\u001b[A\n", " 11%|█         | 70/646 [00:32<01:03,  9.05it/s]\u001b[A\n", " 11%|█         | 72/646 [00:32<01:07,  8.49it/s]\u001b[A\n", " 11%|█▏        | 74/646 [00:32<01:01,  9.28it/s]\u001b[A\n", " 12%|█▏        | 76/646 [00:33<00:55, 10.26it/s]\u001b[A\n", " 12%|█▏        | 78/646 [00:33<00:50, 11.21it/s]\u001b[A\n", " 12%|█▏        | 80/646 [00:33<01:16,  7.37it/s]\u001b[A\n", " 13%|█▎        | 82/646 [00:33<01:04,  8.75it/s]\u001b[A\n", " 13%|█▎        | 84/646 [00:34<01:00,  9.31it/s]\u001b[A\n", " 13%|█▎        | 86/646 [00:34<01:00,  9.18it/s]\u001b[A\n", " 14%|█▎        | 88/646 [00:34<00:53, 10.38it/s]\u001b[A\n", " 14%|█▍        | 90/646 [00:34<00:46, 11.98it/s]\u001b[A\n", " 14%|█▍        | 92/646 [00:34<00:51, 10.76it/s]\u001b[A\n", " 15%|█▍        | 94/646 [00:34<00:47, 11.56it/s]\u001b[A\n", " 15%|█▍        | 96/646 [00:34<00:43, 12.65it/s]\u001b[A\n", " 15%|█▌        | 98/646 [00:35<00:39, 13.86it/s]\u001b[A\n", " 15%|█▌        | 100/646 [00:35<00:38, 14.22it/s]\u001b[A\n", " 16%|█▌        | 102/646 [00:35<00:34, 15.56it/s]\u001b[A\n", " 16%|█▌        | 104/646 [00:35<00:39, 13.72it/s]\u001b[A\n", " 16%|█▋        | 106/646 [00:35<00:39, 13.84it/s]\u001b[A\n", " 17%|█▋        | 108/646 [00:35<00:43, 12.44it/s]\u001b[A\n", " 17%|█▋        | 110/646 [00:36<00:44, 11.98it/s]\u001b[A\n", " 17%|█▋        | 112/646 [00:36<00:40, 13.06it/s]\u001b[A\n", " 18%|█▊        | 114/646 [00:36<00:41, 12.95it/s]\u001b[A\n", " 18%|█▊        | 116/646 [00:36<00:44, 11.97it/s]\u001b[A\n", " 18%|█▊        | 118/646 [00:36<00:47, 11.18it/s]\u001b[A\n", " 19%|█▊        | 120/646 [00:36<00:46, 11.41it/s]\u001b[A\n", " 19%|█▉        | 122/646 [00:37<00:45, 11.63it/s]\u001b[A\n", " 19%|█▉        | 124/646 [00:37<00:47, 11.05it/s]\u001b[A\n", " 20%|█▉        | 126/646 [00:37<00:44, 11.82it/s]\u001b[A\n", " 20%|█▉        | 128/646 [00:37<00:39, 12.95it/s]\u001b[A\n", " 20%|██        | 130/646 [00:37<00:41, 12.54it/s]\u001b[A\n", " 20%|██        | 132/646 [00:37<00:38, 13.38it/s]\u001b[A\n", " 21%|██        | 134/646 [00:37<00:38, 13.18it/s]\u001b[A\n", " 21%|██        | 136/646 [00:38<00:37, 13.48it/s]\u001b[A\n", " 21%|██▏       | 138/646 [00:38<00:43, 11.59it/s]\u001b[A\n", " 22%|██▏       | 140/646 [00:38<00:41, 12.08it/s]\u001b[A\n", " 22%|██▏       | 142/646 [00:38<00:38, 13.25it/s]\u001b[A\n", " 22%|██▏       | 144/646 [00:38<00:39, 12.79it/s]\u001b[A\n", " 23%|██▎       | 146/646 [00:38<00:35, 14.03it/s]\u001b[A\n", " 23%|██▎       | 148/646 [00:39<00:36, 13.71it/s]\u001b[A\n", " 23%|██▎       | 150/646 [00:39<00:37, 13.40it/s]\u001b[A\n", " 24%|██▎       | 152/646 [00:39<00:35, 14.04it/s]\u001b[A\n", " 24%|██▍       | 154/646 [00:39<00:33, 14.50it/s]\u001b[A\n", " 24%|██▍       | 157/646 [00:39<00:32, 15.09it/s]\u001b[A\n", " 25%|██▍       | 159/646 [00:39<00:39, 12.23it/s]\u001b[A\n", " 25%|██▍       | 161/646 [00:40<00:52,  9.24it/s]\u001b[A\n", " 25%|██▌       | 163/646 [00:40<00:45, 10.52it/s]\u001b[A\n", " 26%|██▌       | 165/646 [00:40<00:43, 11.10it/s]\u001b[A\n", " 26%|██▌       | 167/646 [00:40<00:45, 10.55it/s]\u001b[A\n", " 26%|██▌       | 169/646 [00:40<00:45, 10.49it/s]\u001b[A\n", " 26%|██▋       | 171/646 [00:41<00:48,  9.89it/s]\u001b[A\n", " 27%|██▋       | 173/646 [00:41<00:41, 11.43it/s]\u001b[A\n", " 27%|██▋       | 175/646 [00:41<00:42, 11.18it/s]\u001b[A\n", " 27%|██▋       | 177/646 [00:41<00:46, 10.16it/s]\u001b[A\n", " 28%|██▊       | 179/646 [00:41<00:44, 10.59it/s]\u001b[A\n", " 28%|██▊       | 181/646 [00:42<00:47,  9.83it/s]\u001b[A\n", " 28%|██▊       | 183/646 [00:42<00:44, 10.37it/s]\u001b[A\n", " 29%|██▊       | 185/646 [00:42<00:43, 10.66it/s]\u001b[A\n", " 29%|██▉       | 187/646 [00:42<00:53,  8.60it/s]\u001b[A\n", " 29%|██▉       | 188/646 [00:42<00:58,  7.85it/s]\u001b[A\n", " 29%|██▉       | 189/646 [00:43<00:55,  8.19it/s]\u001b[A\n", " 30%|██▉       | 191/646 [00:43<00:52,  8.74it/s]\u001b[A\n", " 30%|██▉       | 193/646 [00:43<00:52,  8.64it/s]\u001b[A\n", " 30%|███       | 194/646 [00:43<00:52,  8.57it/s]\u001b[A\n", " 30%|███       | 195/646 [00:43<00:52,  8.60it/s]\u001b[A\n", " 30%|███       | 196/646 [00:43<00:53,  8.43it/s]\u001b[A\n", " 30%|███       | 197/646 [00:44<00:56,  7.96it/s]\u001b[A\n", " 31%|███       | 198/646 [00:44<01:02,  7.19it/s]\u001b[A\n", " 31%|███       | 199/646 [00:44<01:01,  7.22it/s]\u001b[A\n", " 31%|███       | 200/646 [00:44<01:05,  6.76it/s]\u001b[A\n", " 31%|███       | 201/646 [00:44<01:03,  7.00it/s]\u001b[A\n", " 31%|███▏      | 202/646 [00:44<01:00,  7.36it/s]\u001b[A\n", " 32%|███▏      | 204/646 [00:44<00:51,  8.56it/s]\u001b[A\n", " 32%|███▏      | 205/646 [00:45<00:51,  8.59it/s]\u001b[A\n", " 32%|███▏      | 206/646 [00:45<00:50,  8.69it/s]\u001b[A\n", " 32%|███▏      | 207/646 [00:45<00:52,  8.42it/s]\u001b[A\n", " 32%|███▏      | 209/646 [00:45<00:45,  9.60it/s]\u001b[A\n", " 33%|███▎      | 210/646 [00:45<00:47,  9.12it/s]\u001b[A\n", " 33%|███▎      | 211/646 [00:45<00:50,  8.54it/s]\u001b[A\n", " 33%|███▎      | 212/646 [00:45<00:54,  7.96it/s]\u001b[A\n", " 33%|███▎      | 213/646 [00:46<00:59,  7.33it/s]\u001b[A\n", " 33%|███▎      | 215/646 [00:46<00:50,  8.60it/s]\u001b[A\n", " 33%|███▎      | 216/646 [00:46<00:50,  8.56it/s]\u001b[A\n", " 34%|███▎      | 217/646 [00:46<00:53,  7.96it/s]\u001b[A\n", " 34%|███▎      | 218/646 [00:46<00:53,  7.99it/s]\u001b[A\n", " 34%|███▍      | 219/646 [00:46<01:04,  6.62it/s]\u001b[A\n", " 34%|███▍      | 220/646 [00:46<00:59,  7.13it/s]\u001b[A\n", " 34%|███▍      | 221/646 [00:47<01:00,  7.07it/s]\u001b[A\n", " 34%|███▍      | 222/646 [00:47<01:02,  6.79it/s]\u001b[A\n", " 35%|███▍      | 223/646 [00:47<00:56,  7.48it/s]\u001b[A\n", " 35%|███▍      | 224/646 [00:47<00:58,  7.16it/s]\u001b[A\n", " 35%|███▍      | 225/646 [00:47<00:59,  7.10it/s]\u001b[A\n", " 35%|███▍      | 226/646 [00:47<00:59,  7.08it/s]\u001b[A\n", " 35%|███▌      | 227/646 [00:47<00:57,  7.28it/s]\u001b[A\n", " 35%|███▌      | 228/646 [00:48<01:00,  6.85it/s]\u001b[A\n", " 35%|███▌      | 229/646 [00:48<00:58,  7.14it/s]\u001b[A\n", " 36%|███▌      | 231/646 [00:48<00:47,  8.78it/s]\u001b[A\n", " 36%|███▌      | 232/646 [00:48<00:46,  8.94it/s]\u001b[A\n", " 36%|███▌      | 233/646 [00:48<00:46,  8.88it/s]\u001b[A\n", " 36%|███▋      | 235/646 [00:48<00:41,  9.82it/s]\u001b[A\n", " 37%|███▋      | 236/646 [00:48<00:44,  9.25it/s]\u001b[A\n", " 37%|███▋      | 238/646 [00:49<00:38, 10.55it/s]\u001b[A\n", " 37%|███▋      | 240/646 [00:49<00:34, 11.93it/s]\u001b[A\n", " 37%|███▋      | 242/646 [00:49<00:32, 12.40it/s]\u001b[A\n", " 38%|███▊      | 244/646 [00:49<00:31, 12.77it/s]\u001b[A\n", " 38%|███▊      | 246/646 [00:49<00:33, 12.05it/s]\u001b[A\n", " 38%|███▊      | 248/646 [00:49<00:35, 11.30it/s]\u001b[A\n", " 39%|███▊      | 250/646 [00:50<00:33, 11.94it/s]\u001b[A\n", " 39%|███▉      | 252/646 [00:50<00:34, 11.27it/s]\u001b[A\n", " 39%|███▉      | 254/646 [00:50<00:34, 11.27it/s]\u001b[A\n", " 40%|███▉      | 256/646 [00:50<00:38, 10.01it/s]\u001b[A\n", " 40%|███▉      | 258/646 [00:50<00:37, 10.48it/s]\u001b[A\n", " 40%|████      | 260/646 [00:51<00:36, 10.45it/s]\u001b[A\n", " 41%|████      | 262/646 [00:51<00:36, 10.48it/s]\u001b[A\n", " 41%|████      | 264/646 [00:51<00:37, 10.18it/s]\u001b[A\n", " 41%|████      | 266/646 [00:51<00:39,  9.56it/s]\u001b[A\n", " 41%|████▏     | 268/646 [00:51<00:37, 10.05it/s]\u001b[A\n", " 42%|████▏     | 270/646 [00:52<00:37, 10.02it/s]\u001b[A\n", " 42%|████▏     | 272/646 [00:52<00:35, 10.47it/s]\u001b[A\n", " 42%|████▏     | 274/646 [00:52<00:33, 10.95it/s]\u001b[A\n", " 43%|████▎     | 276/646 [00:52<00:32, 11.40it/s]\u001b[A\n", " 43%|████▎     | 278/646 [00:52<00:36, 10.19it/s]\u001b[A\n", " 43%|████▎     | 280/646 [00:52<00:33, 10.93it/s]\u001b[A\n", " 44%|████▎     | 282/646 [00:53<00:31, 11.39it/s]\u001b[A\n", " 44%|████▍     | 284/646 [00:53<00:33, 10.85it/s]\u001b[A\n", " 44%|████▍     | 286/646 [00:53<00:32, 10.96it/s]\u001b[A\n", " 45%|████▍     | 288/646 [00:53<00:35,  9.99it/s]\u001b[A\n", " 45%|████▍     | 290/646 [00:53<00:35,  9.99it/s]\u001b[A\n", " 45%|████▌     | 292/646 [00:54<00:32, 10.97it/s]\u001b[A\n", " 46%|████▌     | 294/646 [00:54<00:31, 11.10it/s]\u001b[A\n", " 46%|████▌     | 296/646 [00:54<00:30, 11.43it/s]\u001b[A\n", " 46%|████▌     | 298/646 [00:54<00:27, 12.47it/s]\u001b[A\n", " 46%|████▋     | 300/646 [00:54<00:27, 12.51it/s]\u001b[A\n", " 47%|████▋     | 302/646 [00:54<00:27, 12.64it/s]\u001b[A\n", " 47%|████▋     | 304/646 [00:54<00:25, 13.53it/s]\u001b[A\n", " 47%|████▋     | 306/646 [00:55<00:26, 12.89it/s]\u001b[A\n", " 48%|████▊     | 308/646 [00:55<00:30, 11.17it/s]\u001b[A\n", " 48%|████▊     | 310/646 [00:55<00:30, 11.13it/s]\u001b[A\n", " 48%|████▊     | 312/646 [00:55<00:30, 10.98it/s]\u001b[A\n", " 49%|████▊     | 314/646 [00:55<00:30, 10.97it/s]\u001b[A\n", " 49%|████▉     | 316/646 [00:56<00:29, 11.22it/s]\u001b[A\n", " 49%|████▉     | 318/646 [00:56<00:28, 11.55it/s]\u001b[A\n", " 50%|████▉     | 320/646 [00:56<00:31, 10.43it/s]\u001b[A\n", " 50%|████▉     | 322/646 [00:56<00:33,  9.59it/s]\u001b[A\n", " 50%|█████     | 324/646 [00:56<00:32,  9.93it/s]\u001b[A\n", " 50%|█████     | 326/646 [00:57<00:31, 10.25it/s]\u001b[A\n", " 51%|█████     | 328/646 [00:57<00:31, 10.16it/s]\u001b[A\n", " 51%|█████     | 330/646 [00:57<00:29, 10.71it/s]\u001b[A\n", " 51%|█████▏    | 332/646 [00:57<00:30, 10.27it/s]\u001b[A\n", " 52%|█████▏    | 334/646 [00:57<00:29, 10.66it/s]\u001b[A\n", " 52%|█████▏    | 336/646 [00:58<00:30, 10.32it/s]\u001b[A\n", " 52%|█████▏    | 338/646 [00:58<00:29, 10.31it/s]\u001b[A\n", " 53%|█████▎    | 340/646 [00:58<00:28, 10.82it/s]\u001b[A\n", " 53%|█████▎    | 342/646 [00:58<00:28, 10.84it/s]\u001b[A\n", " 53%|█████▎    | 344/646 [00:58<00:36,  8.28it/s]\u001b[A\n", " 53%|█████▎    | 345/646 [00:59<00:36,  8.19it/s]\u001b[A\n", " 54%|█████▎    | 346/646 [00:59<00:39,  7.62it/s]\u001b[A\n", " 54%|█████▎    | 347/646 [00:59<00:43,  6.83it/s]\u001b[A\n", " 54%|█████▍    | 348/646 [00:59<00:48,  6.13it/s]\u001b[A\n", " 54%|█████▍    | 349/646 [00:59<00:49,  6.04it/s]\u001b[A\n", " 54%|█████▍    | 351/646 [01:00<00:42,  6.90it/s]\u001b[A\n", " 54%|█████▍    | 352/646 [01:00<00:44,  6.68it/s]\u001b[A\n", " 55%|█████▍    | 353/646 [01:00<00:45,  6.38it/s]\u001b[A\n", " 55%|█████▍    | 354/646 [01:00<00:58,  4.95it/s]\u001b[A\n", " 55%|█████▍    | 355/646 [01:00<00:56,  5.17it/s]\u001b[A\n", " 55%|█████▌    | 356/646 [01:01<00:51,  5.61it/s]\u001b[A\n", " 55%|█████▌    | 357/646 [01:01<00:51,  5.58it/s]\u001b[A\n", " 55%|█████▌    | 358/646 [01:02<01:50,  2.60it/s]\u001b[A\n", " 56%|█████▌    | 359/646 [01:02<01:31,  3.13it/s]\u001b[A\n", " 56%|█████▌    | 360/646 [01:02<01:17,  3.69it/s]\u001b[A\n", " 56%|█████▌    | 361/646 [01:02<01:31,  3.11it/s]\u001b[A\n", " 56%|█████▌    | 362/646 [01:03<01:41,  2.81it/s]\u001b[A\n", " 56%|█████▌    | 363/646 [01:03<01:19,  3.54it/s]\u001b[A\n", " 56%|█████▋    | 364/646 [01:03<01:10,  4.00it/s]\u001b[A\n", " 57%|█████▋    | 365/646 [01:03<01:02,  4.53it/s]\u001b[A\n", " 57%|█████▋    | 366/646 [01:04<01:31,  3.05it/s]\u001b[A\n", " 57%|█████▋    | 367/646 [01:04<01:14,  3.75it/s]\u001b[A\n", " 57%|█████▋    | 368/646 [01:04<01:10,  3.96it/s]\u001b[A\n", " 57%|█████▋    | 369/646 [01:04<00:58,  4.71it/s]\u001b[A\n", " 57%|█████▋    | 370/646 [01:04<00:53,  5.11it/s]\u001b[A\n", " 57%|█████▋    | 371/646 [01:05<00:46,  5.86it/s]\u001b[A\n", " 58%|█████▊    | 372/646 [01:05<00:47,  5.83it/s]\u001b[A\n", " 58%|█████▊    | 373/646 [01:05<00:50,  5.37it/s]\u001b[A\n", " 58%|█████▊    | 374/646 [01:05<00:50,  5.41it/s]\u001b[A\n", " 58%|█████▊    | 375/646 [01:05<00:47,  5.72it/s]\u001b[A\n", " 58%|█████▊    | 376/646 [01:05<00:42,  6.42it/s]\u001b[A\n", " 58%|█████▊    | 377/646 [01:06<00:41,  6.49it/s]\u001b[A\n", " 59%|█████▊    | 378/646 [01:06<00:45,  5.83it/s]\u001b[A\n", " 59%|█████▊    | 379/646 [01:06<00:43,  6.15it/s]\u001b[A\n", " 59%|█████▉    | 380/646 [01:06<00:38,  6.91it/s]\u001b[A\n", " 59%|█████▉    | 381/646 [01:06<00:44,  5.98it/s]\u001b[A\n", " 59%|█████▉    | 382/646 [01:07<01:04,  4.07it/s]\u001b[A\n", " 59%|█████▉    | 383/646 [01:07<00:57,  4.60it/s]\u001b[A\n", " 59%|█████▉    | 384/646 [01:07<00:54,  4.77it/s]\u001b[A\n", " 60%|█████▉    | 385/646 [01:07<00:50,  5.17it/s]\u001b[A\n", " 60%|█████▉    | 386/646 [01:07<00:47,  5.50it/s]\u001b[A\n", " 60%|█████▉    | 387/646 [01:07<00:48,  5.35it/s]\u001b[A\n", " 60%|██████    | 388/646 [01:08<00:50,  5.15it/s]\u001b[A\n", " 60%|██████    | 389/646 [01:08<00:46,  5.50it/s]\u001b[A\n", " 60%|██████    | 390/646 [01:08<00:51,  4.93it/s]\u001b[A\n", " 61%|██████    | 391/646 [01:08<00:53,  4.73it/s]\u001b[A\n", " 61%|██████    | 392/646 [01:09<00:50,  5.05it/s]\u001b[A\n", " 61%|██████    | 393/646 [01:09<00:48,  5.19it/s]\u001b[A\n", " 61%|██████    | 394/646 [01:09<00:43,  5.77it/s]\u001b[A\n", " 61%|██████    | 395/646 [01:09<00:38,  6.46it/s]\u001b[A\n", " 61%|██████▏   | 396/646 [01:09<00:42,  5.86it/s]\u001b[A\n", " 61%|██████▏   | 397/646 [01:09<00:40,  6.07it/s]\u001b[A\n", " 62%|██████▏   | 398/646 [01:10<00:46,  5.30it/s]\u001b[A\n", " 62%|██████▏   | 399/646 [01:10<00:46,  5.32it/s]\u001b[A\n", " 62%|██████▏   | 400/646 [01:10<00:41,  5.89it/s]\u001b[A\n", " 62%|██████▏   | 401/646 [01:10<00:49,  4.99it/s]\u001b[A\n", " 62%|██████▏   | 402/646 [01:10<01:00,  4.03it/s]\u001b[A\n", " 62%|██████▏   | 403/646 [01:11<00:51,  4.69it/s]\u001b[A\n", " 63%|██████▎   | 404/646 [01:11<00:46,  5.24it/s]\u001b[A\n", " 63%|██████▎   | 406/646 [01:11<00:31,  7.71it/s]\u001b[A\n", " 63%|██████▎   | 408/646 [01:11<00:25,  9.40it/s]\u001b[A\n", " 63%|██████▎   | 410/646 [01:11<00:23, 10.20it/s]\u001b[A\n", " 64%|██████▍   | 412/646 [01:11<00:21, 10.86it/s]\u001b[A\n", " 64%|██████▍   | 414/646 [01:11<00:18, 12.22it/s]\u001b[A\n", " 64%|██████▍   | 416/646 [01:12<00:16, 13.60it/s]\u001b[A\n", " 65%|██████▍   | 418/646 [01:12<00:15, 14.62it/s]\u001b[A\n", " 65%|██████▌   | 420/646 [01:12<00:15, 15.05it/s]\u001b[A\n", " 65%|██████▌   | 422/646 [01:12<00:14, 15.04it/s]\u001b[A\n", " 66%|██████▌   | 424/646 [01:12<00:15, 14.17it/s]\u001b[A\n", " 66%|██████▌   | 426/646 [01:12<00:16, 13.10it/s]\u001b[A\n", " 66%|██████▋   | 428/646 [01:12<00:15, 14.26it/s]\u001b[A\n", " 67%|██████▋   | 430/646 [01:13<00:14, 15.19it/s]\u001b[A\n", " 67%|██████▋   | 432/646 [01:13<00:13, 15.44it/s]\u001b[A\n", " 67%|██████▋   | 434/646 [01:13<00:12, 16.48it/s]\u001b[A\n", " 67%|██████▋   | 436/646 [01:13<00:13, 16.02it/s]\u001b[A\n", " 68%|██████▊   | 438/646 [01:13<00:12, 16.03it/s]\u001b[A\n", " 68%|██████▊   | 440/646 [01:13<00:15, 13.65it/s]\u001b[A\n", " 68%|██████▊   | 442/646 [01:13<00:15, 13.38it/s]\u001b[A\n", " 69%|██████▊   | 444/646 [01:13<00:14, 14.39it/s]\u001b[A\n", " 69%|██████▉   | 446/646 [01:14<00:13, 14.50it/s]\u001b[A\n", " 70%|██████▉   | 449/646 [01:14<00:11, 17.14it/s]\u001b[A\n", " 70%|██████▉   | 451/646 [01:14<00:13, 14.83it/s]\u001b[A\n", " 70%|███████   | 453/646 [01:14<00:13, 13.97it/s]\u001b[A\n", " 70%|███████   | 455/646 [01:14<00:12, 14.76it/s]\u001b[A\n", " 71%|███████   | 457/646 [01:14<00:12, 14.76it/s]\u001b[A\n", " 71%|███████   | 459/646 [01:14<00:11, 15.82it/s]\u001b[A\n", " 71%|███████▏  | 461/646 [01:15<00:12, 15.02it/s]\u001b[A\n", " 72%|███████▏  | 463/646 [01:15<00:11, 15.32it/s]\u001b[A\n", " 72%|███████▏  | 465/646 [01:15<00:11, 15.26it/s]\u001b[A\n", " 72%|███████▏  | 467/646 [01:15<00:11, 15.66it/s]\u001b[A\n", " 73%|███████▎  | 469/646 [01:15<00:12, 14.61it/s]\u001b[A\n", " 73%|███████▎  | 471/646 [01:15<00:11, 15.42it/s]\u001b[A\n", " 73%|███████▎  | 473/646 [01:15<00:10, 15.91it/s]\u001b[A\n", " 74%|███████▎  | 475/646 [01:15<00:11, 14.95it/s]\u001b[A\n", " 74%|███████▍  | 477/646 [01:16<00:11, 14.79it/s]\u001b[A\n", " 74%|███████▍  | 479/646 [01:16<00:11, 15.04it/s]\u001b[A\n", " 74%|███████▍  | 481/646 [01:16<00:10, 15.55it/s]\u001b[A\n", " 75%|███████▍  | 483/646 [01:16<00:10, 15.33it/s]\u001b[A\n", " 75%|███████▌  | 485/646 [01:16<00:11, 14.19it/s]\u001b[A\n", " 75%|███████▌  | 487/646 [01:16<00:10, 14.54it/s]\u001b[A\n", " 76%|███████▌  | 489/646 [01:16<00:10, 15.23it/s]\u001b[A\n", " 76%|███████▌  | 491/646 [01:17<00:10, 14.14it/s]\u001b[A\n", " 76%|███████▋  | 493/646 [01:17<00:10, 14.48it/s]\u001b[A\n", " 77%|███████▋  | 495/646 [01:17<00:10, 14.80it/s]\u001b[A\n", " 77%|███████▋  | 497/646 [01:17<00:09, 15.67it/s]\u001b[A\n", " 77%|███████▋  | 499/646 [01:17<00:09, 15.76it/s]\u001b[A\n", " 78%|███████▊  | 501/646 [01:17<00:09, 16.04it/s]\u001b[A\n", " 78%|███████▊  | 503/646 [01:17<00:09, 14.84it/s]\u001b[A\n", " 78%|███████▊  | 505/646 [01:17<00:09, 14.72it/s]\u001b[A\n", " 78%|███████▊  | 507/646 [01:18<00:09, 13.97it/s]\u001b[A\n", " 79%|███████▉  | 509/646 [01:18<00:10, 12.84it/s]\u001b[A\n", " 79%|███████▉  | 511/646 [01:18<00:10, 12.77it/s]\u001b[A\n", " 79%|███████▉  | 513/646 [01:18<00:10, 12.88it/s]\u001b[A\n", " 80%|███████▉  | 515/646 [01:18<00:10, 13.05it/s]\u001b[A\n", " 80%|████████  | 517/646 [01:18<00:10, 12.60it/s]\u001b[A\n", " 80%|████████  | 519/646 [01:19<00:09, 13.09it/s]\u001b[A\n", " 81%|████████  | 521/646 [01:19<00:09, 13.40it/s]\u001b[A\n", " 81%|████████  | 523/646 [01:19<00:09, 13.51it/s]\u001b[A\n", " 81%|████████▏ | 525/646 [01:19<00:09, 12.73it/s]\u001b[A\n", " 82%|████████▏ | 527/646 [01:19<00:09, 12.91it/s]\u001b[A\n", " 82%|████████▏ | 529/646 [01:19<00:08, 13.42it/s]\u001b[A\n", " 82%|████████▏ | 531/646 [01:19<00:07, 14.43it/s]\u001b[A\n", " 83%|████████▎ | 533/646 [01:20<00:08, 13.88it/s]\u001b[A\n", " 83%|████████▎ | 535/646 [01:20<00:08, 13.57it/s]\u001b[A\n", " 83%|████████▎ | 537/646 [01:20<00:08, 12.66it/s]\u001b[A\n", " 83%|████████▎ | 539/646 [01:20<00:08, 12.08it/s]\u001b[A\n", " 84%|████████▎ | 541/646 [01:20<00:08, 12.38it/s]\u001b[A\n", " 84%|████████▍ | 543/646 [01:20<00:07, 13.88it/s]\u001b[A\n", " 84%|████████▍ | 545/646 [01:21<00:06, 14.70it/s]\u001b[A\n", " 85%|████████▍ | 547/646 [01:21<00:06, 15.68it/s]\u001b[A\n", " 85%|████████▍ | 549/646 [01:21<00:06, 16.03it/s]\u001b[A\n", " 85%|████████▌ | 551/646 [01:21<00:05, 16.34it/s]\u001b[A\n", " 86%|████████▌ | 553/646 [01:21<00:05, 16.73it/s]\u001b[A\n", " 86%|████████▌ | 555/646 [01:21<00:05, 16.11it/s]\u001b[A\n", " 86%|████████▌ | 557/646 [01:21<00:06, 14.68it/s]\u001b[A\n", " 87%|████████▋ | 559/646 [01:21<00:05, 15.93it/s]\u001b[A\n", " 87%|████████▋ | 561/646 [01:21<00:05, 16.81it/s]\u001b[A\n", " 87%|████████▋ | 563/646 [01:22<00:06, 12.68it/s]\u001b[A\n", " 87%|████████▋ | 565/646 [01:22<00:05, 13.69it/s]\u001b[A\n", " 88%|████████▊ | 567/646 [01:22<00:06, 12.11it/s]\u001b[A\n", " 88%|████████▊ | 569/646 [01:22<00:06, 11.55it/s]\u001b[A\n", " 89%|████████▊ | 572/646 [01:22<00:05, 14.39it/s]\u001b[A\n", " 89%|████████▉ | 574/646 [01:23<00:04, 14.92it/s]\u001b[A\n", " 89%|████████▉ | 576/646 [01:23<00:04, 15.79it/s]\u001b[A\n", " 89%|████████▉ | 578/646 [01:23<00:04, 16.28it/s]\u001b[A\n", " 90%|████████▉ | 581/646 [01:23<00:03, 17.75it/s]\u001b[A\n", " 90%|█████████ | 584/646 [01:23<00:03, 18.28it/s]\u001b[A\n", " 91%|█████████ | 586/646 [01:23<00:03, 17.20it/s]\u001b[A\n", " 91%|█████████ | 588/646 [01:23<00:03, 15.65it/s]\u001b[A\n", " 91%|█████████▏| 591/646 [01:23<00:03, 16.50it/s]\u001b[A\n", " 92%|█████████▏| 593/646 [01:24<00:03, 16.36it/s]\u001b[A\n", " 92%|█████████▏| 595/646 [01:24<00:04, 12.42it/s]\u001b[A\n", " 92%|█████████▏| 597/646 [01:24<00:04, 12.09it/s]\u001b[A\n", " 93%|█████████▎| 599/646 [01:24<00:03, 12.76it/s]\u001b[A\n", " 93%|█████████▎| 601/646 [01:25<00:04,  9.74it/s]\u001b[A\n", " 93%|█████████▎| 603/646 [01:25<00:04,  9.36it/s]\u001b[A\n", " 94%|█████████▎| 605/646 [01:25<00:05,  7.50it/s]\u001b[A\n", " 94%|█████████▍| 606/646 [01:26<00:07,  5.30it/s]\u001b[A\n", " 94%|█████████▍| 608/646 [01:26<00:05,  6.40it/s]\u001b[A\n", " 94%|█████████▍| 609/646 [01:26<00:09,  3.80it/s]\u001b[A\n", " 95%|█████████▍| 612/646 [01:27<00:05,  5.70it/s]\u001b[A\n", " 95%|█████████▍| 613/646 [01:27<00:06,  5.42it/s]\u001b[A\n", " 95%|█████████▌| 614/646 [01:27<00:07,  4.55it/s]\u001b[A\n", " 96%|█████████▌| 617/646 [01:27<00:03,  7.41it/s]\u001b[A\n", " 96%|█████████▌| 619/646 [01:28<00:04,  6.50it/s]\u001b[A\n", " 96%|█████████▌| 621/646 [01:28<00:03,  6.91it/s]\u001b[A\n", " 96%|█████████▋| 623/646 [01:28<00:02,  8.35it/s]\u001b[A\n", " 97%|█████████▋| 625/646 [01:29<00:04,  4.72it/s]\u001b[A\n", " 97%|█████████▋| 626/646 [01:29<00:04,  4.65it/s]\u001b[A\n", " 97%|█████████▋| 627/646 [01:29<00:03,  4.89it/s]\u001b[A\n", " 97%|█████████▋| 628/646 [01:30<00:03,  5.24it/s]\u001b[A\n", " 98%|█████████▊| 630/646 [01:30<00:02,  5.69it/s]\u001b[A\n", " 98%|█████████▊| 631/646 [01:30<00:02,  5.66it/s]\u001b[A\n", " 98%|█████████▊| 633/646 [01:30<00:01,  7.71it/s]\u001b[A\n", " 98%|█████████▊| 635/646 [01:31<00:02,  4.15it/s]\u001b[A\n", " 99%|█████████▊| 637/646 [01:31<00:01,  5.37it/s]\u001b[A\n", " 99%|█████████▉| 638/646 [01:31<00:01,  5.87it/s]\u001b[A\n", " 99%|█████████▉| 639/646 [01:32<00:02,  2.63it/s]\u001b[A\n", " 99%|█████████▉| 640/646 [01:34<00:03,  1.67it/s]\u001b[A\n", " 99%|█████████▉| 642/646 [01:34<00:01,  2.15it/s]\u001b[A\n", "100%|█████████▉| 643/646 [01:35<00:01,  1.57it/s]\u001b[A\n", "100%|█████████▉| 644/646 [01:36<00:01,  1.84it/s]\u001b[A\n", "100%|█████████▉| 645/646 [01:36<00:00,  2.26it/s]\u001b[A\n", "100%|██████████| 646/646 [01:36<00:00,  6.69it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["CPU times: user 56.2 s, sys: 2.86 s, total: 59.1 s\n", "Wall time: 1min 36s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["%%time\n", "loader = DirectoryLoader(data_path, glob=\"*.txt\", show_progress=True)\n", "docs = loader.load()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "I7qI6T6mQdD3", "outputId": "5a6d2b90-a862-4dca-e782-edd01fd32a42"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["50"]}, "metadata": {}, "execution_count": 18}], "source": ["len(docs)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lCzMJ2S7KaDW", "outputId": "a23b49d1-627f-4f65-c9e4-4a5bb5fd23bb"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["50"]}, "metadata": {}, "execution_count": 16}], "source": ["docs = docs[:50]\n", "len(docs)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aef8c3ea-26e9-4a09-8314-0d1e7580ae26", "outputId": "2dda7c15-20aa-417c-909f-5b6e5e088964"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Document(page_content='Link: https://www.visitsingapore.com/see\\n\\ndo\\n\\nsingapore/nightlife/\\n\\nTitle: Nightlife', metadata={'source': '/content/drive/MyDrive/English/Nightlife.txt'})"]}, "metadata": {}, "execution_count": 19}], "source": ["docs[0]"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RJNlVST3QiUe", "outputId": "9dbcd32d-bc88-4dad-b155-31b80e4d6235"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Link: https://www.visitsingapore.com/dining\n", "\n", "drinks\n", "\n", "singapore/local\n", "\n", "dishes/\n", "\n", "Title: Local Dishes in Singapore\n", "\n", "<PERSON><PERSON><PERSON>\n", "\n", "Dark and sticky, the salad may not look very appealing at first; but tuck into this culinary marvel and you’ll be amazed by the delicious mix of sweet and savoury.\n"]}], "source": ["print(docs[2].page_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xBLcnHH5QsBg", "outputId": "c3ebd828-fb4e-4e70-d00c-57a9e2d35823"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Link: https://www.visitsingapore.com/festivals\n", "\n", "events\n", "\n", "singapore/\n", "\n", "Title: Festivals & Events\n", "\n", "Festivals\n", "\n", "& Events\n", "\n", "Whether it’s the festive celebration of Chinese New Year\n", "\n", "or the roar of Formula 1 cars, there’s always a party going on\n", "\n", "somewhere on the island. Check out top festivals and events\n", "\n", "in Singapore from January to December.\n"]}], "source": ["print(docs[1].page_content)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"id": "k2RnQCm-rkDt"}, "outputs": [], "source": ["raw_text = ''\n", "for i, doc in enumerate(docs):\n", "    text = doc.page_content\n", "    if text:\n", "        raw_text += text"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "LOBVIoA4UKr9", "outputId": "947477ff-71ad-42ca-e03f-2fa13509bdae"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Link: https://www.visitsingapore.com/see\n", "\n", "do\n", "\n", "singapore/nightlife/\n", "\n", "Title: NightlifeLink: https://www.visitsingapore.com/festivals\n", "\n", "events\n", "\n", "singapore/\n", "\n", "Title: Festivals & Events\n", "\n", "Festivals\n", "\n", "& Events\n", "\n", "Whether it’s the festive celebration of Chinese New Year\n", "\n", "or the roar of Formula 1 cars, there’s always a party going on\n", "\n", "somewhere on the island. Check out top festivals and events\n", "\n", "in Singapore from January to December.Link: https://www.visitsingapore.com/dining\n", "\n", "drinks\n", "\n", "singapore/local\n", "\n", "dishes/\n", "\n", "Title: Local Dishes in Singapore\n", "\n", "<PERSON><PERSON><PERSON>\n", "\n", "Dark and sticky, the salad may not look very appealing at first; but tuck into this culinary marvel and you’ll be amazed by the delicious mix of sweet and savoury.Link: https://www.visitsingapore.com/editorials/michelin\n", "\n", "eats\n", "\n", "with\n", "\n", "a\n", "\n", "singapore\n", "\n", "flavour/\n", "\n", "Title: <PERSON><PERSON> eats with a Singapore flavour\n", "\n", "Singapore is certainly a slice of heaven for food lovers: With 69 establishments being awarded the Michelin Bib Gourmand and 49 eateries nabbing Michelin stars in 2021, the number of quality food options on offer in our sunny city can certainly seem endless. Which begs the question: Where does one begin?\n", "\n", "No matter where you hail from, you’re certain to find a local food option that will broaden your culinary horizons, excite your palate and add to your memories of unforgettable dining experiences.\n", "\n", "From traditional favourites to bold interpretations of these classics—also known as modern-Singaporean cuisine (Mod-Sin)—here’s the lowdown on some of the culinary gems the Lion City has to offer.\n", "\n", "Local culture on a plate\n", "\n", "Hailing from a hybrid heritage unique to this region, Peranakan* food is a blend of Chinese ingredients with the various distinct spices and cooking techniques of the Malay community, and an apt symbol of Singapore’s melting pot of ethnic cultures.\n", "\n", "To experience how tradition and originality merge into new forms, pay a visit to Candlenut. Helmed by local chef-owner <PERSON>, Candlenut is the world’s first and only Michelin-starred Peranakan restaurant. The establishment serves innovative dishes that both modernise and pay homage to traditional Peranakan flavours, including buah keluak (black nut indigenous to Southeast Asia) ice cream and Westholme Wagyu Beef rendang (braised meat cooked in coconut milk and spices).\n", "\n", "Alternatively, drop by Michelin Bib Gourmand eatery True Blue Cuisine for Peranakan fare that eschews innovation for traditional preparation. Located in the Peranakan Museum along Armenian Street, this restaurant has been dishing out authentic Peranakan delicacies (without pork or lard) since 2003. Chef-owner <PERSON> started True Blue Cuisine with the assistance of his mother, serving traditional dishes like minced chicken ngoh hiang (meat rolled in beancurd skin) and udang ketak (crayfish fried in spicy paste). The traditional décor, beautiful antiques and sweet scent of bunga rampai (a potpourri of flowers and leaves) culminate in a multisensory dining experience for food lovers. *The term is an Indonesian/Malay word that means “local born”, which generally refers to people of Chinese and Malay/Indonesian heritage. Candlenut. 17A Dempsey Road, Singapore 249676. 1800 304 2288 (local calls only).\n", "\n", "Daily noon-3pm, 6-10pm. True Blue Cuisine. 47/49 Armenian Street, Singapore 179937. +65 6440 0449.\n", "\n", "Daily 11.30am\n", "\n", "2.30pm, 5.30\n", "\n", "9.30pm.\n", "\n", "Great food in local neighbourhoods\n", "\n", "With a legion of hawkers serving up local dishes, Singapore is home to some of the world’s most affordable Michelin-starred meals, many of which can be found in Singapore’s traditional enclaves, from the bustling streets of Chinatown to the vibrant district of Little India.\n", "\n", "To get a taste of Singapore’s long tradition of excellent street food, pay a visit to the Michelin Bib Gourmand-awarded Famous Sungei Road Trishaw Laksa. Located in the bustling Hong Lim Market, this stall dishes out a delicious rendition of laksa (spicy, coconut-based noodle soup), a beloved local comfort food.\n", "\n", "Spice lovers and food aficionados looking to add zest to their food adventure should venture into the ethnic enclave of Little India, and sample the fish head curry at Muth<PERSON>’s Curry. This local staple is made with a unique recipe, concocted by the establishment’s founder Mr. <PERSON><PERSON><PERSON>, and which remains in his sons’ safekeeping to this day.\n", "\n", "For a feast of diverse tastes, be sure to check out the many hawker delights at Chinatown Complex. There’re over 260 food stalls at Singapore’s largest hawker centre, but we suggest getting your meal from Lian He Ben Ji Claypot Rice. This aromatic dish is cooked over a charcoal fire, and comes with generous portions of tender chicken, sausage and other delicious ingredients.\n", "\n", "If you’re in a large group, make a beeline for <PERSON><PERSON><PERSON>. This beloved establishment offers over 40 staples from Malay cuisine, making it a great way to discover local flavours while indulging in communal dining. Be sure to order local favourites like their beef rendang (braised meat cooked in coconut milk and spices) and their sambal (spicy chilli paste) prawns. Hong Lim Market & Food Centre. 531A Upper Cross Street #02-66, Singapore 051531. +65 9750 8326.\n", "\n", "Mon<PERSON><PERSON><PERSON>, Fri & Sat 10.15am-3pm. <PERSON><PERSON><PERSON>'s Curry. 138 Race Course Road #01-01, Singapore 218591. +65 6392 1722.\n", "\n", "Daily 10.30am-10.30pm. <PERSON><PERSON> Rice at Chinatown Complex. 335 Smith Street #02-197/198/199, Singapore 050335. +65 6227 2470.\n", "\n", "Daily 4.30-10pm. Hjh Maimunah Restaurant. 11 & 15 Jalan Pisang, Singapore 199078. +65 6297 4294.\n", "\n", "Mon–Sat 7am–8pm.\n", "\n", "Peppered with passion: Singaporean chefs\n", "\n", "While there’s certainly a wealth of traditional local eateries in Singapore, the city’s chefs are no strangers to invention. Pay a visit to these establishments, and get a taste of how local flavours, techniques and ingredients merge into bold new tastes and forms.\n", "\n", "Corner House has reopened with a daringly, original new menu that showcases modern French-Asian interpretations by Chef <PERSON>, who has taken the helm of the one-Michelin-star restaurant since March 2020. Born and raised in Bordeaux, Chef <PERSON> brings French-Asian touches and interpretations to the table from his journey working through some of the most hallowed Michelin-starred kitchens around Europe. Dubbed “French-Asian cuisine without shackles”, the concept at Corner House showcases intimate sessions where <PERSON><PERSON><PERSON> prepares dishes inspired by personal stories, thoughts and memories.Link: https://www.visitsingapore.com/editorials/nightlife\n", "\n", "in\n", "\n", "the\n", "\n", "city/\n", "\n", "Title: Nightlife in the city\n", "\n", "Dance till you drop\n", "\n", "Over at Clarke Quay, Attica is famous for its outrageous parties that attract a loyal local and international clientele. If you like your house music and people, sexy, this is the place to be.\n", "\n", "A vital stop for any club goer has to be Zouk, which has set the pace for the nightlife scene in Singapore and the region for almost 25 years. One of the most famous clubs in the world, Zouk is acknowledged as one of the best by critics and clubbers alike.\n", "\n", "Stunning views\n", "\n", "For those who wish to enjoy their drinks while admiring Singapore’s stunning skyline, there are plenty of rooftop establishments to visit.\n", "\n", "CÉ LA VI, perched at the SkyPark of Marina Bay Sands®, has built a dedicated following. It’s not hard to see why: Here, you can take in a 360-degree panoramic view of the Marina Bay area, and the city’s stunning skyline. Kickback at the bar area, chill with a cocktail or check out the al fresco Skybar to get the ultimate rooftop entertainment experience.\n", "\n", "Over in Chinatown, dive into the sights and sounds of Potato Head Folk’s stunning rooftop bar which is famous for its excellent tiki-inspired list of cocktails, served in bamboo and coconut cups handcrafted in Bali.\n", "\n", "Offering a panoramic view of Parliament, Marina Bay Sands® as well as the Civic and financial districts, Smoke & Mirrors attracts a gregarious office crowd nightly, which teems with numerous unique cocktails and bar bites, along with a wide selection of wines.\n", "\n", "Another rooftop venue to check out is 1-Altitude. It is one of the highest rooftop bars in the world, standing at 282 metres above sea level. Located above its sister restaurant Stellar and indoor nightclub Altimate, this breezy bar also offers an unobstructed view of Singapore from the 61st to 63rd storey of One Raffles Place.\n", "\n", "But if it is unique brews you’re after, head to LeVel33, officially the world’s highest urban craft-brewery in the penthouse of Marina Bay Financial Centre.\n", "\n", "Shaken or stirred?\n", "\n", "Fancy a good drink? Then you will be pleased to know a new wave of bespoke cocktail bars has sprouted across the island to cater to more refined taste buds.\n", "\n", "We recommend you start your crawl at 28 HongKong Street, where you can find some of the best cocktails in town–just remember, reservations are required.\n", "\n", "Here you’ll find drinks with names like <PERSON><PERSON>’s Bath and Modest Mule. The alcohol strength is indicated beside each entry on the menu, to help you pace yourself (which is certainly helpful). Top off the whole exquisite experience with the excellent bar food on offer here.\n", "\n", "Another bar to check out is the ultra-modern Tippling Club. It recently released the world’s first edible cocktail menu, which lets guests try different alcoholic gummy bears before making their choice of cocktail.\n", "\n", "Martini lovers should make a visit to Orgo, which sits atop the Esplanade. The bar is known for its impeccable concoctions by renowned Japanese mixologist <PERSON><PERSON><PERSON>.\n", "\n", "More creative cocktails can also be found in the historic Chinatown district, home to Bitters & Love and Jigger & Pony. The former is a friendly spot with an inventive drinks list to sample, while the latter is housed in a former art gallery that attracts a well-dressed crowd who come for the timeless, classic cocktails.\n", "\n", "With so many watering holes to choose from, it’s time to raise a glass as night falls over the city.Link: https://www.visitsingapore.com/travel\n", "\n", "guide\n", "\n", "tips/mobi/\n", "\n", "Title: <PERSON><PERSON>mless Journey Travel Planner for SingaporeLink: https://www.visitsingapore.com/see\n", "\n", "do\n", "\n", "singapore/socialiser/\n", "\n", "Title: Drinking in the city\n", "\n", "Drinking\n", "\n", "in the city\n", "\n", "From sipping interesting cocktails to discovering\n", "\n", "watering holes that offer great ambience and company,\n", "\n", "Singapore has lots for you to experience.Link: https://www.visitsingapore.com/content/desktop/en/en\n", "\n", "deals/singaporewards\n", "\n", "Title: Explore the hidden gem tours with SingapoRewards\n", "\n", "What’s a better way to holiday?\n", "\n", "Exploring the city’s best kept secrets with SingapoRewards\n", "\n", "Redeem a complimentary activity and make the most of your visit to Singapore. From a scenic cycling tour to a reinvigorating kayak adventure, choose from a range of unique things to do – curated just for you.\n", "\n", "Each traveller gets a choice of one free activity with SingapoRewards. Once you’ve booked your flight to Singapore, go ahead and pick your favourite from our list and redeem it for your trip.\n", "\n", "We'll see you soon!Link: https://www.visitsingapore.com/content/desktop/en/editorials/amazing\n", "\n", "things\n", "\n", "you\n", "\n", "never\n", "\n", "knew\n", "\n", "about\n", "\n", "singapore\n", "\n", "Title: 10 fun facts about singapore\n", "\n", "For those who crave novelty, travel is all about new perspectives and new ways to experience the world. Beyond Singapore’s picture-perfect skyline, our bustling metropolis is an ever-evolving wonderland of bold new experiences and unforgettable adventures.\n", "\n", "Rekindle your love for the bold, the new and exciting, with these fascinating facts about the Lion City.\n", "\n", "1. It’s a city of not just one island, but 64\n", "\n", "You might not know it but Singapore’s land area includes as many as 64 offshore islands that surround the main island. These include Sentosa—a popular island resort with myriad attractions—and havens for nature lovers like Pulau Ubin, St John’s Island and Sisters’ Islands. What that means for visitors: more fun in the sun!\n", "\n", "2. It’s home to the world’s first night zoo\n", "\n", "Singapore’s Night Safari provides a nocturnal experience like no other in the city; it’s also the world’s very first night zoo. Opened in 1994, the 35-hectare park features over 1,000 animals from 120 different species, living in their naturalistic night-time environments. Hop onto the 40-minute Guided Tram Ride for an overview of the park’s main attractions, or amble along the four interlinked walking trails within the park, for a zoo trip like no other.\n", "\n", "3. It’s a city of (man-made) waterfalls\n", "\n", "According to the Mandai Wildlife Reserve, the first man-made waterfall was built at Jurong Bird Park in 1971. Dropping from a height of 30 metres, it is said to be the tallest waterfall in an aviary to date. Other man-made summits to explore include Cloud Forest, Gardens by the Bay. This huge, 35-metre waterfall is the centrepiece of the misty conservatory, designed to house plant life from the tropical highlands. No trip to Singapore is complete without a visit to the world’s tallest indoor waterfall. Housed in the retail and lifestyle complex of Jewel Changi Airport, the HSBC Rain Vortex soars at 40 metres, and is surrounded by a lush indoor garden.\n", "\n", "4. The locals speak Singlish, not just English\n", "\n", "Don’t be too surprised to hear Singaporeans adding exclamations like ‘lah’ and ‘leh’ to their sentence. Singlish— our colourful local slang— is an integral part of everyday conversation amongst Singaporeans. Singlish is a collection of colloquial catchphrases and lingo influenced by Singapore’s multiculturalism. Other examples include the Singlish term “chope”, which means to reserve a seat. Locals often chope seats at a hawker centre using packets of tissue paper! Singaporeans also tend to refer to strangers such as cab drivers and hawker centre stall owners as “Aunt<PERSON>” and “Uncle<PERSON>”. This is an endearing way of addressing older gentlemen and ladies. Do use the terms wisely, though, as it can connote the addressee’s age; you wouldn’t want to accidentally offend a new acquaintance!\n", "\n", "5. Singapore pioneered the first F1 night race\n", "\n", "Held annually since 2008, Grand Prix Season Singapore features a gamut of concerts, racing and entertainment activities, for Formula One fans and visitors of all ages. The star event—the FORMULA 1 SINGAPORE GRAND PRIX—also made racing history as the world’s first ever FORMULA 1 night race. The twisty Marina Bay street circuit has largely remained unchanged in the years since; the track’s brightly lit floodlights also add to the spectacular night views of Singapore. According to Formula 1, the Marina Bay Street Circuit also boasts more corners (23 in all) than any other circuits on the Formula One race calendar. With the Singapore GP back on track in 2022, race fans can expect an unforgettable experience of nonstop thrills and adrenaline.\n", "\n", "6. It’s one of the world’s greenest cities\n", "\n", "This city of skyscrapers is also one that is filled with lush greenery. Nearly half of Singapore’s land area (approximately 700 square kilometres) is under green cover. Beyond numerous parks and gardens, there are pockets of undiscovered plant life housed in the most unusual of places. For example, PARKROYAL COLLECTION Pickering is known for its hotel-in-a-garden concept and its four-storey cascading vertical garden. There’s rich biodiversity in the many nature reserves that dot our City in a Garden—Singapore is home to over 2,100 native vascular plant species. The Bukit Timah Nature Reserve in particular, is said to contain more tree species in a single hectare than the total number of tree species found in North America.\n", "\n", "7. It’s home to a UNESCO World Heritage Site––and some unique ‘VIPs’\n", "\n", "Lauded as a UNESCO World Heritage Site in 2015, the Singapore Botanic Gardens has a history of over 150 years since its founding in 1859; that’s more than a century older than modern Singapore itself! Its most popular attraction is the National Orchid Garden, which houses thousands of orchid species known as Very Important Plants (VIPs). Over 200 hybrid orchids in this garden have been affectionately named after visiting foreign dignitaries such as <PERSON>, the <PERSON> and <PERSON> Cambridge as well as celebrities like actors <PERSON>, <PERSON> and <PERSON><PERSON>. Another fun fact: Singapore’s first botanic garden opened in 1822, on the slopes of the area now known as Fort Canning Hill. Measuring just 19 hectares, the garden closed in 1829 due to rising costs. Its land was then used for various public projects, including an Armenian church, a school and a hospital.\n", "\n", "8. There are myriad neighbourhoods to explore off the beaten path\n", "\n", "Besides the history-rich hotspots of Chinatown, Kampong Gelam and Little India, there are many other colourful enclaves for visitors to explore in Singapore. You’ll find rows of hip eateries and stores along the Art Deco-style buildings of Tiong Bahru, as well as colourful shophouses and traditional food stalls in Joo Chiat/Katong. More up-and-coming neighbourhoods include Everton Park, which is home to coffee joints, cafés, ice cream parlours and other must-try foodie hotspots.\n", "\n", "9. There’s always something to celebrate in Singapore\n", "\n", "Fret not about finding fun new things to do. There are cultural festivals, major sports, lifestyle and arts events held all year round in the city to keep you entertained. The annual HSBC World Rugby Singapore Sevens is hosted at the S$1.3 billion Singapore Sports Hub, which also hosts a range of events from live music concerts to mixed martial arts extravaganzas. In July, foodies are in for a treat with the Singapore Food Festival, where they will get to savour uniquely Singaporean dishes as well as the best of Mod-Sin (Modern Singaporean) cuisine, which gives a modern twist to traditional flavours.\n", "\n", "10. The Lion City may actually have been inspired by a tigerLink: https://www.visitsingapore.com/content/desktop/en/editorials/five\n", "\n", "ways\n", "\n", "singapore\n", "\n", "will\n", "\n", "surprise\n", "\n", "you/\n", "\n", "Title: Five ways Singapore will surprise you\n", "\n", "Just 719.7 square kilometres in size, Singapore packs a punch with a blend of elements that come together in unexpected symphony. From old-school buildings alongside modern architectural marvels, to street food served up side by side with internationally acclaimed gourmet fare, the city itself is a celebration of diversity that goes beyond the postcard-perfect sight of steely skyscrapers. Here are five fascinating contrasts to look out for:\n", "\n", "1. Old-school hawker fare versus modern experimental dining\n", "\n", "One big reason why Singaporeans love food: even the local street (hawker) fare garners Michelin stars. Join the snaking queues at one-Michelin-starred hawker stalls—the world’s first—Hill Street Tai Hwa Pork Noodle on Crawford Lane and Liao Fan Hong Kong Soya Sauce Chicken Rice & Noodle in Chinatown Complex. For even more traditional fare, breathe in a whiff of nostalgia as you order bak chang (traditional Chinese rice dumplings) at the time-honoured hawker stall Hoo Kee Bak Chang in Amoy Food Street Centre.\n", "\n", "Besides its thriving hawker scene, the city is also at the forefront of gastronomic innovation. Expand your culinary vocabulary with Chef <PERSON><PERSON>’s “Mod-Sin” (Modern Singaporean) cuisine at Wild Rocket he remixes local dishes such as laksa (spicy coconut-milk based noodle soup) and congee using European techniques. Alternatively, head to Corner House in UNESCO World Heritage Site Singapore Botanic Gardens to find out what cutting-edge ‘Gastro-Botanica’ cuisine really means. Hint: it has something to do with plants.\n", "\n", "2. Modern skyscrapers versus traditional architecture\n", "\n", "The juxtaposition of historic and contemporary architecture in a city can evoke a beauty that is more than the sum of its parts. For a taste of the modern, take a whirl around bustling Raffles Place. Towering skyscrapers glint back at you from every direction, with the 43-storey Ocean Financial Centre and Singapore’s tallest hotel Swissotel The Stamford being a few of the pinnacles that dot the stunning skyline.\n", "\n", "Down on the streets, historic neighbourhoods reveal architectural marvels that look straight out of a history textbook. Snap a photo of the colourful two-storey shophouses around Joo Chiat, where Peranakan* heritage has been preserved. Or jaunt around hip enclave Tiong Bahru and spot pre-war buildings inspired by transportation of the 1920s. Think aviation-inspired blocks, nautical elements and clean lines in the design.\n", "\n", "The term is an Indonesian/Malay word that means “local born”, which generally refers to people of Chinese and Malay/Indonesian heritage.\n", "\n", "3. International events versus local festivities\n", "\n", "The city never sleeps when world-class events and local celebrations alike dot the calendar. As a cosmopolitan nation, Singapore plays host to many international festivals and events that set the city abuzz—such as the Grand Prix Season Singapore. Besides the adrenaline-pumping action on the tracks, the city revs up with parties, exclusive dining and entertainment offers, and more.\n", "\n", "Join in festivities of a more traditional kind, too—head to ethnic districts such as Chinatown or Little India, where lights fill the streets with pomp and colour during Chinese New Year and Deepavali, respectively. To truly soak up the festive spirit, inhale the aromatic scents of flowers or spices at the sprawling bazaars in Little India, or cheer on the lion dancers and fire-eaters as they perform along Chinatown Street.\n", "\n", "4. Nature versus nurture\n", "\n", "We’re not talking about the age-old question of psychology here. Despite Singapore’s evolution into a modern city, many pockets of lush greenery remain. Get close to Mother Nature at Kranji Marshes, the largest freshwater marshland in Singapore, developed way back in the 1970s. As you clamber up to its observatory for a bird’s-eye view, keep your eyes peeled for over 170 species of birds and insects. For an alternative nature escapade, take a bumboat back in time to off-shore island Pulau Ubin, one of the two remaining kampongs (rural villages) in Singapore.\n", "\n", "If you’re wondering about the island’s moniker of ‘City in a Garden’, look up at the Supertrees at Gardens by the Bay for a futuristic interpretation of a park. Amid the surrounding waterfront gardens, these iconic structures are man-made marvels—the defining touches of a horticultural destination that is truly ‘nurtured’ into existence.\n", "\n", "5. Bargain buys versus designer brands\n", "\n", "Shopaholics, get ready to part with your cash. Start with retail therapy at Bugis Street, known for chic yet affordable apparel and its wide array of souvenirs such as magnets, silver accessories and T-shirts—bring a few home! To pick up quirky and unique items, hop on over to nearby Haji Lane in Kampong Gelam, where eclectic boutiques nestle among narrow alleys and vibrant murals.\n", "\n", "If you have a soft spot for designer brands such as Prada and Gucci, heed the siren call of The Shoppes at Marina Bay Sands®. Pop into the Chanel boutique for the latest ‘It’ bag, or to the Louis Vuitton flagship store outside. Housed in a floating Crystal Pavilion, it’s the label’s largest store outside of Paris.\n", "\n", "But if you’re pressed for time, at the very least, squeeze in a stroll down Singapore’s iconic shopping belt, Orchard Road, where you can fill your bags with anything from designer threads to fast fashion. From hidden nature spots to glitzy malls to grand festivals, Singapore’s ever-expanding roster of activities and destinations will entertain even the most jaded of travellers. The only problem is: where do you start?Link: https://www.visitsingapore.com/content/desktop/en/dining\n", "\n", "drinks\n", "\n", "singapore/\n", "\n", "Title: EAT & DRINK\n", "\n", "Fun after sunset\n", "\n", "Singapore’s nightlife scene has exploded into a dizzying whirl of music, drink and entertainment that caters to both party animals and those seeking a quieter night out – all around the clock. Dance to the beats of world-class DJs at a megaclub, enjoy a bespoke tipple at a discreet cocktail bar or check out a live band before having a big laugh at a comedy club. Crank up the volume or dial it down to a more comfortable pace. The choices are limited only by your imagination, and how long you take to recover the next day.Link: https://www.visitsingapore.com/content/desktop/en/editorials/ultimate\n", "\n", "cooking\n", "\n", "classes\n", "\n", "for\n", "\n", "foodies/\n", "\n", "Title: Cooking Classes for Tourist in Singapore\n", "\n", "There’s no better way to get acquainted with Singapore’s many cultures than through our island’s food.\n", "\n", "Whether you’re heading to a Michelin-starred restaurant or a local coffee shop for your fill of nasi lemak (rice dish cooked in coconut milk, served with an array of side dishes), you’re bound to find something that suits your taste buds.\n", "\n", "Don’t worry about missing the food once you leave the city, though. Sign up for these cooking classes in Singapore, where you’ll learn how to make your very own version of local delicacies.\n", "\n", "Cookery Magic\n", "\n", "More than just cooking classes, the sessions at Cookery Magic are enchanting forays into the realms of culinary creation. Founded by irrepressible food lover and chef <PERSON><PERSON><PERSON><PERSON><PERSON>, these home-cooking classes will have you discovering our island’s rich food heritage. A three-hour cooking session—held in R<PERSON>q<PERSON><PERSON>’s cosy kitchen—will have you learning how to whip up delicious dishes like chicken satay (grilled meat skewers), Peranakan-style laksa (spicy coconut milk-based noodle soup) and char kway teow (stir-fried rice noodles). Alternatively, opt for Cookery Magic’s Kampong Cooking Escapade—an all-day cooking-themed adventure that takes place in a century-old house on the island of Pulau Ubin. Enjoy the great outdoors, soak in the lush greenery, and learn how to create delicacies like nasi ulam (Indonesian steamed rice dish mixed with various herbs) and ice kacang (shaved ice dessert). Virtual classes are also available for gourmands based overseas, and aspiring chefs who’d like to savour a taste of Singapore from home. *The term is an Indonesian/Malay word that means “local born”, which generally refers to people of Chinese and Malay/Indonesian heritage. Cookery Magic. 117 Fidelio Street, Singapore 458492. +65 9665 6831.\n", "\n", "To book your class, click here.\n", "\n", "Palate Sensations\n", "\n", "You’ve probably been to the various hawker centres in Singapore or tried our Michelin-starred hawker dishes. Now it’s time to take a piece of that home. Palate Sensations’ ‘Tourist’ classes will have you working the wok in no time. As the name suggests, the class is designed for travellers who want to take home a slice of Singapore’s culinary culture, with recipes and techniques that you can replicate in the comfort of your own kitchen. So the next time you host a dinner party, you’ll be able to lay out a spread of sambal sotong (squid in chilli paste), sambal kangkong (water spinach stir-fried in a spicy sauce), chicken rice, and laksa. Palate Sensations Culinary School in Chromos at Biopolis. 10 Biopolis Road #01-03, Singapore 138670. +65 6478 9746.\n", "\n", "To book your class, click here.\n", "\n", "Bollywood Veggies\n", "\n", "The folks at Bollywood Veggies —a farm with an in-house bistro—will show you how they do farm-to-table at their Bollywood Bhanchha class (“bhanchha” is the Nepali word for “kitchen”). You’ll get to forage ingredients straight from the establishment’s 10-acre farm and learn to cook them in a private culinary class. Besides being a farm-to-table restaurant that grows the ingredients and herbs for its dishes in its own garden, Bollywood Veggies has a trove of recipes to share. Vegetable-filled wraps and exotic salads made with farm-fresh produce are the healthier options, while dishes such as chicken curry and deep fried moringa leaves tip the scale towards the more decadent. Local desserts—such as goreng pisang (deep fried banana fritters)—are also on the cards. While you’re there, don’t forget to drop by The Earth Shop to purchase seasonal produce and herbs, or visit The Bollywood Food Museum to admire a collection of oil paintings that illustrate the history of food and agriculture. Bollywood Veggies. 100 Neo Tiew Road, Singapore 719026. +65 6898 5001.\n", "\n", "To book your class, call +65 6898 5001 or drop them an email.\n", "\n", "D’Open Kitchen\n", "\n", "With a variety of cooking classes for aspiring chefs of all levels, D’Open Kitchen’s repertoire of lessons include everything from seasonal cuisine and local fare to artisanal desserts.\n", "\n", "The establishment offers a range of classes focused on teaching you how to create a whole range of dishes, from Japanese-style bento and Italian pizza to Asian fare and even mooncakes.\n", "\n", "We suggest signing up for the latter two classes, so that you can take a taste of the region back home to your loved ones. The class that focuses on Asian fare will have you whipping up locally-beloved staples like ondeh-ondeh (glutinous rice balls with palm sugar filling, coated with desiccated coconut), pandan chicken and chicken rendang (braised meat cooked in coconut milk and spices).\n", "\n", "D’Open Kitchen. 10 Anson Road, Singapore 079903. +65 8228 6217.\n", "\n", "To book your class, click here.\n", "\n", "Food Playground\n", "\n", "Just like the Singaporean dish rojak (mixed fruit and vegetable salad), Singapore’s cuisine is a reflection of our myriad of cultures. To sample the sheer breadth of our cuisine, join the Cultural Cooking Class at Food Playground. You’ll learn to cook Chinese, Malay, Peranakan and Indian dishes in just three hours, and discover more about the country’s heritage at the same time. Alternatively, sign up for an online session and learn how to make Hainanese kaya (a traditional jam made from coconut and eggs), curry puffs and handmade noodles from the comfort of your own kitchen. Expect to be enthralled by fascinating factoids surrounding Singapore’s food culture in this two-hour session. Food Playground. 24A Sago Street, Singapore 059020. +65 9452 3669.\n", "\n", "To book your class, click here.\n", "\n", "Tools of the TradeLink: https://www.visitsingapore.com/content/desktop/en/singapore\n", "\n", "shopping/\n", "\n", "Title: Where to Shop Till You Drop in Singapore\n", "\n", "Where\n", "\n", "Collectors\n", "\n", "meet\n", "\n", "In Singapore, shopping is not just about buying things: it’s exploring different worlds where retail, nature, tech and heritage meet. Cruise into Funan Mall on a bicycle. Browse shops around a rain vortex in Jewel Changi. Indulge in luxury brands at international flagship stores along Orchard Road. Take home cultural memorabilia at our ethnic enclaves; or get involved in craft workshops by independent brands. There’s a surprise around the corner for every kind of collector.Link: https://www.visitsingapore.com/content/desktop/en/walking\n", "\n", "tour/shop/retail\n", "\n", "therapy\n", "\n", "orchard\n", "\n", "road/\n", "\n", "Title: Retail Therapy: Orchard Road\n", "\n", "Singapore’s retail street is a treasure trove of finds that will help you exude your unique personality.\n", "\n", "With a wide array of options, visitors can enjoy a bespoke shopping experience tailored to suit any taste and budget. Start your retail journey at Tanglin and wander through the heart of our country’s shopping belt towards Dhoby Ghaut, with each district offering a diverse mix of shopping experiences.\n", "\n", "All venues are near the Orchard, Somerset and Dhoby Ghaut MRT Stations\n", "\n", "Fun for the whole family at Tanglin\n", "\n", "Tanglin Mall\n", "\n", "Located along Tanglin Road, 1 Tanglin Mall is just a stone’s throw away from the iconic Orchard Road, offering a bevy of family-friendly experiences under one roof. Wander through the House of Anli, which offers European homeware for the perfectly curated living space of your dreams. Choose from a collection of artisanal cutlery and linen that matches your personality.\n", "\n", "Look out for Motherswork Baby & Kids, a shopping emporium where you can pick out the best products for your children. Because you don’t compromise on quality, take home a host of international award-winning children’s brands and accessories, like toys and maternity wear. Up your parenting skills with aids that will help your child learn and soar beyond their potential.\n", "\n", "Local and International buys at Orchard Road\n", "\n", "Scotts Square\n", "\n", "Walk down to Scotts Road where you’ll find the Hermès flagship store at 2 Scotts Square.\n", "\n", "Pick up iconic Hermès products, and be inspired by Aloft, the in-house art space, and the store’s window installations, which are refreshed every season with artwork by different artists. You may even bag an installation that’s to your taste!\n", "\n", "In Good Company\n", "\n", "At Orchard Road, satisfy your taste for the contemporary, sleek and minimalistic from the curated collection amassed by 3 In Good Company. Here is where collectors celebrate their taste with this local brand that carries womenswear, accessories, and even a ‘mini me’ collection of kidswear for the trendy young tots to mirror your style.\n", "\n", "TANGS\n", "\n", "Founded over 80 years ago, 4 TANGS has gained a revered reputation as being one of Singapore’s most iconic departmental stores. This household name is loved by locals and visitors alike, with an extensive collection of big-name brands to local gems all under its iconic glazed green roof. To make an undeniably bold statement, pick up a bag from Singaporean designer <PERSON>, or a spiffy summer suit from <PERSON>.\n", "\n", "ION Orchard\n", "\n", "With its striking modern architecture and façade of glass, marble and steel, ION Orchard's status as one of Singapore’s most glamorous shopping malls is evident before you even step into the building. You’ll find luxury brands like Prada, Louis <PERSON> and Harry <PERSON> on levels 1 to 4, and popular street fashion brands like Zara, G2000 and Levi’s on the four basement levels.\n", "\n", "Vibrant retail spaces at Somerset\n", "\n", "Apple Orchard Road\n", "\n", "Southeast Asia’s first 5 Apple store is found in the heart of our shopping belt in a two-storey tall, elegant glass structure. Here is where you can be a part of the tech savvy global community with their latest gadgets, or attend regular workshops headed by local creatives, such as iPhone photography.\n", "\n", "Mal<PERSON>ison by The Hour Glass\n", "\n", "Just beside the Apple Store is the temple of luxurious decadence known as 6 Malmaison by The Hour Glass. This 8,000-square-feet emporium is an ornately furnished mecca of opulence, evidenced from the lingering scent of Cire Trudon candles and gentle tinkling from 14th century music boxes in the air. Shop for your next statement timepiece by indulging in their bespoke services to help you pick out dapper gentlemanly attire, millinery and fragrances.\n", "\n", "Mandarin Gallery\n", "\n", "Ladies, cross the street and pamper yourself at Victoria’s Secret’s flagship store in 7 Mandarin Gallery. Be adventurous because this 12,000-square-feet duplex gives visitors access to the full range of the brand’s coveted lingerie, fragrances, beauty and body care products. Check out Victoria Sport, the brand’s athletic line, and PINK, a special varsity-inspired collection for young girls and women.\n", "\n", "Design Orchard\n", "\n", "To discover apparel, accessories and souvenirs that are proudly made in Singapore, make Design Orchard the next stop on your list. This vibrant enclave and retail space is home to over 60 homegrown brands like fashion retailer Minor Miracles, homeware brand PhotoPhactory and AIO (exclusive to Design Orchard), purveyors of skincare solutions.\n", "\n", "Style up at Dhoby Ghaut\n", "\n", "Plaza Singapura\n", "\n", "Round up your shopping trip at Dhoby Ghaut. Step into Ray-Ban’s first Singapore store at 8 Plaza Singapura and find your style with the most extensive range of the iconic brand’s offerings that you can find in the city.Link: https://www.visitsingapore.com/content/desktop/en/travel\n", "\n", "guide\n", "\n", "tips/travelling\n", "\n", "to\n", "\n", "singapore/\n", "\n", "Title: Getting to Singapore\n", "\n", "Travelling to and from Singapore is generally a fuss-free affair, but if you have any questions in mind, this handy FAQ is bound to answer them all.\n", "\n", "Whether you’ve just reached the departure gate at Changi Airport, planning for your first-ever arrival to our sunny island or have questions about getting through customs, this how-to guide will make your entry to and departure from Singapore a breeze.\n", "\n", "Travelling to and From Singapore: A How-To Guide\n", "\n", "Do I need a visa for entry into Singapore?\n", "\n", "Most visitors to Singapore do not require a visa for entry.\n", "\n", "For a list of countries that require a valid Visa for entry into Singapore, you can check the Immigration & Checkpoints Authority (ICA)’s website, or enquire at the Singapore Overseas Mission closest to you.\n", "\n", "Are there any documents that I need to fill in prior to arrival?\n", "\n", "Prior to arrival, you should fill in your SG Arrival Card to submit your arrival information. This can be done up to three days (including the date of arrival) before your visit. You can submit your arrival information via Singapore Immigration & Checkpoints Authority website, or via the MyICA Mobile application.\n", "\n", "Departing from Singapore\n", "\n", "Are there any convenient services I can use when departing from Singapore?\n", "\n", "First-time foreign visitors arriving to Singapore will enroll and clear immigration with iris, facial and fingerprints biometrics at the manual counters or designated lanes at Airport terminals or land checkpoints. They can then enjoy automated clearance with iris and facial biometrics during departure.\n", "\n", "Clearing Customs\n", "\n", "Is there an app that I can use to clear customs?\n", "\n", "Certainly! The Singapore Customs mobile app, Customs@SG, allows you to declare and pay the duty and/or Goods and Services Tax (GST) for your overseas purchases on the go.\n", "\n", "Do remember to log in your info before arriving at the checkpoint in Singapore. You can find out more on declarations and payment at Singapore Customs.\n", "\n", "How do I check for period of stay granted?\n", "\n", "After clearing immigration, an electronic Pass (\"e-Pass\") will be sent to the email address registered in your SGAC submission. The e-Pass will contain the period of stay granted and last day of stay in Singapore. You may also retrieve the e-Pass via ICA's e-Pass Enquiry Portal to find information on the period of stay granted for your visit. Keep the e-pass on hand for a smoother hotel check-in or tax refund process!\n", "\n", "Returning to Singapore\n", "\n", "I’m a long-time resident who’s coming back to Singapore —are there any forms I should fill in?\n", "\n", "Welcome home! Do note that as of 27 March 2020, you’ll need to fill in some paper work if you’re a:\n", "\n", "Singaporean Citizen\n", "\n", "Permanent Resident\n", "\n", "Long Term Pass Holder\n", "\n", "Work Pass Holder\n", "\n", "Dependent working in essential services (e.g. health care, transport)\n", "\n", "You’ll have to submit a health declaration through the SG Arrival Card e-service before proceeding with immigration clearance.\n", "\n", "This health declaration should be submitted up to three days (including the day of arrival) prior to arrival in Singapore. The electronic health declaration is in place for purposes of disease control, specifically to guard against importing infectious diseases of concern, to protect the health of Singapore residents. Do submit a truthful health declaration, so as to avoid running afoul of the Infectious Diseases Act.\n", "\n", "For more information on the travel declaration requirements, please refer to the MOH website.Link: https://www.visitsingapore.com/content/desktop/en/travel\n", "\n", "guide\n", "\n", "tips/getting\n", "\n", "around/\n", "\n", "Title: Getting Around Singapore\n", "\n", "By Public Transport\n", "\n", "Singapore’s MRT (Mass Rapid Transit) and bus systems have an extensive network of routes that will help you zip around the city.\n", "\n", "Most of our popular attractions are just a short walk away from an MRT station, making our trains a great way to get around.\n", "\n", "Our bus routes are also some of the most scenic, allowing you to indulge in the lush greenery and beautiful architecture of our ‘city in a garden’. All public buses are wheelchair accessible and open strollers are allowed. Please approach our friendly bus captains if you need assistance. Do take note that you may be required to fold your stroller if the bus has too many passengers. You can refer to the the SBS Transit, SMRT, Go-Ahead Singapore, and Tower Transit Singapore websites for more information and guidelines.\n", "\n", "Visitors can use the following payment options for their respective MRT or bus journeys:\n", "\n", "MRT Bus1 1. Get a Singapore Tourist Pass (STP), a special EZ-Link stored-value card which will allow you unlimited travel for one, two or three days. The Pass can be bought at the SimplyGo Ticket Office at selected MRT stations listed here.\n", "\n", "2. Adult Stored-Value Smartcard (EZ-Link / NETS FlashPay): These cost $10 and come with a stored value of $5 for you to use on your commutes. You’ll be able to purchase these at Passenger Service Centres in train stations, any SimplyGo Ticket Office or convenience stores such as 7-11, Buzz and Cheers. 3. You can use your foreign-issued Mastercard® and Visa contactless bank cards2, as well as your mobile wallets for the payment of public transport fares in Singapore. No registration is required. 2Admin fees apply for foreign-issued bank cards. 4. Children above 0.9m in height and below 7 years old can apply for a Child Concession Card at SimplyGo Ticket Office to travel for free on basic bus and train services. Children below 0.9m in height accompanied by a fare-paying commuter can automatically travel for free. For more information on Child Concession Cards, please visit SimplyGo website. 5. Travellers may also opt to pay in cash on buses. Please prepare the exact fare as no change will be given.\n", "\n", "Click here for information on payment options for public transport.\n", "\n", "For more useful information—including traffic news, journey planning and nearby transport services—download the MyTransport app (available for iOS and Android).\n", "\n", "1For non-cash payments on buses, remember to tap in and out on the reader located at the front and rear exits of the bus when you board and alight respectively.Link: https://www.visitsingapore.com/content/desktop/en/singapore\n", "\n", "itineraries/2\n", "\n", "day\n", "\n", "guide\n", "\n", "for\n", "\n", "esports\n", "\n", "enthusiasts.html\n", "\n", "Title: A 2-day guide for esports enthusiasts\n", "\n", "Fuel up with a breakfast (or early lunch) of champions with a visit to Golden Mile Food Centre. <PERSON><PERSON> recommends eating his favourite pre-game dish of Hainanese boneless chicken rice , which is his “go-to meal” that he eats before travelling overseas for competitions.\n", "\n", "<PERSON><PERSON> spent his youth playing retro games in arcades; to experience nostalgia reminiscent of the professional gamer’s formative years, drop by Retro Nutz, in the Rochor neighbourhood. This vintage game store stocks a range of old-school games, handheld video game consoles like Game Boy, and memorabilia from the good old days of arcades.\n", "\n", "A neighbourhood that holds fond memory for <PERSON><PERSON>, Selegie is home to gaming cafes, LAN shops and various communal gaming establishments.\n", "\n", "“My friend and I started a gaming cafe together called Tough Cookie Gaming Café,” <PERSON><PERSON> reminisces. “We used to stream games, shoot publicity videos and train there. It was like a training ground for me.”\n", "\n", "While Tough Cookie Gaming Café has since closed its doors, the neighbourhood is still a great place to explore. LAN Cafes like Big O and Clique Gaming will give you the chance to engage in a bout of friendly competition with a host of online games, while those interested in traditional card and board games can visit either Mind Café or Play Nation.Link: https://www.visitsingapore.com/content/desktop/en/singapore\n", "\n", "itineraries/best\n", "\n", "of\n", "\n", "singapore\n", "\n", "in\n", "\n", "7\n", "\n", "days.html\n", "\n", "Title: Enjoy <PERSON> in 7 days\n", "\n", "More than just a bustling metropolis, Singapore is a city where bold new passions are just waiting to be uncovered.\n", "\n", "Begin your discovery of our city’s most popular attractions at Gardens by the Bay. This 101-hectare space of verdant greenery comprises three massive cooled conservatories filled with natural splendour.\n", "\n", "You’ll be able to spend most of the day marvelling at the rare flora on display at the Flower Dome, exploring the mist-filled landscape of Cloud Forest, and marvelling at the majesty of the garden’s Supertrees. Be sure to scale Cloud Mountain for unforgettable views, and snap a photo or two atop the OCBC Skyway.Link: https://www.visitsingapore.com/content/desktop/en/singapore\n", "\n", "itineraries/one\n", "\n", "day\n", "\n", "in\n", "\n", "singapore\n", "\n", "itinerary.html\n", "\n", "Title: Decades in a day—a guide to SG\n", "\n", "1 Fort Canning Park 9am: Stroll at Fort Canning History amidst a lush sanctuary Begin your journey through Singapore’s vibrant past with a morning stroll at Fort Canning Park. Formerly known as Bukit Larangan (Forbidden Hill), this verdant park was a former seat of power in Singapore for the Majapahit kings of the 14th century and Singapore’s former colonial rulers. History buffs should embark on the Battlebox Tour—which delves into the history of Fort Canning and its role during World War II— while food lovers may want to take a jaunt on the Spice Garden Trail, a walking trail replete with the plants and spices used in Singaporean cuisine.\n", "\n", "2 National Gallery Singapore 10am: National Gallery Singapore\n", "\n", "Art and inspiration abound Spend the rest of your morning exploring a world of art and inspiration at the National Gallery Singapore. Formerly our city’s Supreme Hall and City Court, this beautiful Neoclassical building now houses the world’s largest collection of Singaporean and Southeast Asian art, with more than 8,000 pieces on display. If you’re feeling peckish during your visit, consider having lunch at National Kitchen by <PERSON>—which serves up a range of classic Peranakan* dishes. Alternatively, book a table at 3-Michelin-starred O<PERSON><PERSON>, famous for its beautifully plated dishes and French-inspired cuisine. *The term is an Indonesian/Malay word that means “local born”, which generally refers to people of Chinese and Malay/Indonesian heritage.\n", "\n", "3 Chinatown 1pm: Chinatown\n", "\n", "Hip modernity and rich heritage Once home to opera houses, coolies and opium dens, Chinatown is a tantalizing blend of both past and present. Take a stroll through the neighbourhood, and you’ll find both traditional tea houses and age-old places of worship side by side chic galleries and hip cafes. For a taste of Chinatown’s history, head over to Thian Hock Keng on Telok Ayer Street. Dedicated to the sea goddess <PERSON><PERSON>, our city’s oldest temple was constructed without the use of a single nail. With its majestic gopuram (grand tower) and beautiful architecture, Sri Mariamman Temple—located along South Bridge Road— is not to be missed. When you’re done snapping shots outside, be sure to step in to admire the stone effigies and beautifully painted murals of the temple’s interior. If you’re looking for an alternative lunch spot, Chinatown Complex on Smith Street houses the island’s largest hawker centre, with a range of dishes like popiah (fresh spring roll with vegetables and assorted filling), kway chap (broad rice sheets in soya sauce broth) and chendol (shaved ice dessert with coconut milk, pandan flavoured jellies and palm sugar). When you’re done, be sure to check out the many souvenir shops that line Pagoda Street, or pick up bak kwa (barbecued meat) from the stores along New Bridge Road. For a taste of tradition you can head over to Tea Chapter on Neil Road or Yixing Xuan Teahouse on Tanjong Pagar Road to indulge in heritage and a cup of Chinese tea.\n", "\n", "4 Sentosa Island 3pm: Sentosa Action and adventure await Local legends purport that Sentosa was once the haunt of pirates and a resting place for warrior spirits, but adventure of a wholly different sort awaits modern-day visitors to the island. Now one of Singapore’s most iconic destinations, this island resort is home to pristine white beaches and endless thrills for avid action seekers. To soak up the sun and enjoy the surf, make a beeline for Palawan or Tanjong Beach. If action is what you crave instead, you’ll want to brave the rollercoaster rides at Universal Studios Singapore or the heart-pumping obstacles and ziplines at Skypark Sentosa by <PERSON>.\n", "\n", "5 Raffles Hotel Singapore 8pm: Raffles Hotel Savour old-world hospitality Spend your evening revelling in both modern hospitality and old-world heritage with dinner and drinks at the Raffles Hotel. This storied establishment has hosted a who’s who of notables—ranging from Queen <PERSON> to <PERSON>—but its most surprising ‘guest’ was a tiger that escaped from a circus along Beach Road and ended up in the hotel’s Bar and Billiards Room. If you’re hankering for global flavours, Raffles Hotel boasts a range of dining establishments, including modern French restaurant Le Dame De Pic, steak house Butcher’s Block and The Tiffin Room, which specialises in North Indian cuisine.Link: https://www.visitsingapore.com/content/desktop/en/walking\n", "\n", "tour/culture/in\n", "\n", "the\n", "\n", "neighbourhood\n", "\n", "little\n", "\n", "india.html\n", "\n", "Title: Discover the charms of Little India\n", "\n", "Little India\n", "\n", "Little India is truly one of Singapore’s most colourful historical districts. Lime pits, brick kilns and a race track once dotted the neighbourhood, and its streets thronged with herdsmen, merchants and garland makers plying their trade.\n", "\n", "To this day, this enclave of Indian culture retains its unique heritage. The district is a tantalising blend of new and old, with contemporary street art and hip eateries sitting alongside age-old temples and vendors selling a tantalising mix of spices, silk and flowers.\n", "\n", "Take a walk with us, and discover the many charms of this two-century old enclave.\n", "\n", "All venues are near Little India MRT Station\n", "\n", "1. Soak in the culture\n", "\n", "Sri Veeramakaliamman Temple\n", "\n", "Start off your discovery of Little India bright and early, with a morning visit to 1 Sri Veerama-\n", "\n", "kaliamman Temple.\n", "\n", "Built by Indian pioneers who lived in the Serangoon district during the 19th century, this gorgeous place of worship is dedicated to the ferocious goddess <PERSON>, and is a great place to deepen your understanding of Hinduism.\n", "\n", "Former house of <PERSON>\n", "\n", "After you’re done soaking in the culture, take a morning stroll to the former house of 2 Tan Teng Niah. Once owned by a prominent Chinese businessman who owned sweet factories in the neighbourhood, this colourful, two-storey bungalow is a must-visit for history buffs looking to admire both European and Chinese styles of architecture.\n", "\n", "Alternative attractions: Tekka Centre\n", "\n", "If you’d like to fuel up before visiting Sri Veeramkaliamman Temple, you can drop by 3 Tekka Centre for an early breakfast. The hawker stalls here sell a whole smorgasbord of Indian staples, including naan (layered oven-baked flatbread) and vadai (fried savoury Indian doughnut).\n", "\n", "2. Delight your senses\n", "\n", "The Banana Leaf Apolo\n", "\n", "For lunch, savour traditional Indian food off a banana leaf at this traditional eatery. Established in 1974, 4 The Banana Leaf Apolo is a household name among locals, and is famous for its fish head curry and spicy chicken masala.\n", "\n", "Little India Arcade\n", "\n", "Work off your hearty meal with a walk to remember at 5 Little India Arcade. Sights, sounds and smells abound at this bustling indoor emporium, with vendors selling everything from trinkets to tapestries and sweet treats.\n", "\n", "Jothi Store & Flower Shop\n", "\n", "Don’t let the name of this establishment fool you—while 6 Jothi Store & Flower Shop originally sold flowers and garlands for religious occasions, the store now sells a range of souvenirs and knick-knacks. Religious statuettes, incense sticks and colourful bangles are just some treasures bound to catch the eyes of culture lovers.\n", "\n", "For vegetarian foodies: Komala Vilas\n", "\n", "If you’re vegetarian or abstaining from meat for religious reasons, 7 Komala Vilas is a great alternative to Banana Leaf Apolo. This popular establishment has been around since 1947, and serves up cuisine from both North and South India.\n", "\n", "3. Journey through heritage\n", "\n", "Indian Heritage Centre\n", "\n", "Singapore’s Indian and South Asian communities have a long and storied history that intertwines deeply with our nation. Broaden your understanding of this facet of Singaporean culture with the 8 Indian Heritage Centre range of events, exhibitions and guided tours.\n", "\n", "<PERSON><PERSON><PERSON>\n", "\n", "Next, take a stroll down Campbell Lane, until you see 9 Masjid <PERSON>. Gazetted as a national monument in 1979, this mosque is one of the island’s oldest, with a history that dates back to the 1800s.\n", "\n", "Singapore’s melting pot of cultures is on full display at this beautiful place of worship, which integrates Western classical motifs like Corinthian pillars with Saracen architecture and calligraphic adornments.\n", "\n", "Alternative attractions: Mural @ 2 Dickson Road\n", "\n", "If you’re a fan of street art, take a short detour from Abdul Gaffoor Mosque to 10 2 Dickson Road. A vivid floral mural by art duo <PERSON><PERSON><PERSON> adorn the walls here, and make the perfect backdrop for an #OOTD.\n", "\n", "4. Discover the new and the novel\n", "\n", "<PERSON><PERSON><PERSON>\n", "\n", "Besides its tradition and heritage, Little India’s evolution can be seen in the vibrant enterprises that you’ll spy as you stroll around the district.\n", "\n", "One such business is 11 Onlewo, a design store that will let you take a piece of our city home with you. Fabrics, homeware and gifts abound at this establishment, and many of this store’s designs take inspiration from Singapore’s iconic neighbourhoods.\n", "\n", "The Malayan Council\n", "\n", "For a midday break, drop by 12 The Malayan Council at Dunlop Street. This beloved local eatery serves up Western dishes inspired by Singapore’s flavours, such as their beef ribs with roti kirai (laced pancake) and pasta with duck and chilli padi (bird’s eye chilli).\n", "\n", "If you’re still feeling satiated from lunch, we suggest sampling their delicious cakes, which are inspired by regional and local flavours like durian and ondeh ondeh (glutinous rice balls with palm sugar filling, coated with desiccated coconut).\n", "\n", "Alternative attractions: Mural @ Dunlop Street\n", "\n", "Looking to feed your ‘gram? More street art awaits you a short stroll away. Alive@Clive can be found along the walls of 13 Haniffa. This dynamic work of art depicts a traditional Indian dancer against a multi-hued backdrop, and is sure to bring a dash of colour to your day.\n", "\n", "If you’re on the lookout for souvenirs, Haniffa is a great place to shop for saris (traditional Indian womenswear) and traditional fabrics to bring home with you.Link: https://www.visitsingapore.com/content/desktop/en/walking\n", "\n", "tour/culture/in\n", "\n", "the\n", "\n", "neighbourhood\n", "\n", "chinatown.html\n", "\n", "Title: In The Neighbourhood: Chinatown\n", "\n", "Chinatown\n", "\n", "Besides being an ethnic enclave for Singapore’s early Chinese settlers, Chinatown has transformed into the country’s largest heritage precinct—filled with an exciting meld of hip haunts, cultural treasures and architectural gems.\n", "\n", "Encompassing Kreta Ayer, Telok Ayer, Tanjong Pagar and Bukit Pasoh, Chinatown’s four sub-districts each possess their own unique charms. While Chinatown’s size might seem daunting for the first-time visitor, this comprehensive guide will ensure you don’t miss out any of its star attractions.\n", "\n", "Venues 1-12 are located near Chinatown MRT Station, Venues 13-14 are located near Telok Ayer MRT Station\n", "\n", "1. Chinatown Heritage Centre\n", "\n", "A gateway into Singapore’s yesteryears, make the perfect starting point of your voyage discovery at the 1 Chinatown Heritage Centre. Learn about the lives of Chinatown’s inhabitants through the establishment’s six galleries of interactive exhibits, which include olfactory displays of opium and spices, immersive streetscapes and homages to its current notable residents.\n", "\n", "If you’re looking for a guided tour, you can explore Chinatown’s rich culture with ‘Red Clogs Down The Five Foot Way’. Helmed by the master storytellers from Journeys Pte Ltd, this tour will have you exploring traditional Chinese trades, discovering beautifully-conserved shophouses and learning about the lives of the district’s early denizens.\n", "\n", "Note: Chinatown Heritage Centre is closed for renovation and enhancement works.\n", "\n", "2. Sri Mariamman Temple\n", "\n", "Built almost two centuries ago, 2 Sri Mariamman Temple, Singapore’s oldest Hindu temple, remains a thriving place of worship to this day. Marvel at the majestic, five-tiered gopuram (grand tower), and then head inside to explore further.\n", "\n", "The statue of the temple’s patron goddess is only unveiled during special occasions, but you’ll be able to admire the site’s murals and shrines, dedicated to the Hindu deities <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>.\n", "\n", "3. <PERSON><PERSON><PERSON>\n", "\n", "Discover Chinatown’s religious diversity as you make your way down the stretch of South Bridge Road, and observe the distinctive octagonal minarets of 3 Masjid Jamae.\n", "\n", "Also known to locals as Chulia Palli (Chulia Mosque), this age-old religious establishment was established by Tamil Muslims from the Coromandel Coast between 1830 to 1835. Its architecture can be traced back to the 19th century, and fuses elements from both Neoclassical and South Indian tradition.\n", "\n", "4. <PERSON>\n", "\n", "Make a detour down 4 Mohammed Ali Lane, and encounter art unbound from galleries. Painted by <PERSON><PERSON>—one of Singapore’s many bold street artists—these Instagrammable murals depict nostalgic scenes from our city’s past.\n", "\n", "Puppeteers, lion dance costume makers and other nostalgic figures are immortalised on this quiet street, and provide a glimpse into the Lion City’s past.\n", "\n", "5. <PERSON>\n", "\n", "For a taste of tradition, grab a sweet treat or two for the road at 5 Tong Heng. This century-old Cantonese confectionary is famous for its egg tarts, but you should also sample their red bean paste pastries and lao po bing (pastry made with winter melon, almond paste and sesame).\n", "\n", "6. <PERSON> Tooth Relic Temple & Museum\n", "\n", "The 6 Buddha Tooth Relic Temple & Museum's distinctive Tang Dynasty-inspired architecture makes it easy to spot as you stroll down South Bridge Road. Both a museum and a place of worship, visitors are free to explore the temple’s repository of relics and cultural artefacts.\n", "\n", "We recommend taking a quick breather at the temple’s tranquil rooftop garden, with its distinctive pagoda and prayer wheel.\n", "\n", "Ed’s note: Visits to the Buddha Tooth Relic Temple are limited to groups of 5. Visit the temple’s website to stay updated on the latest COVID-19 safety measures.\n", "\n", "7. Sago Street\n", "\n", "Named for the sago flour factories that dotted the area in the 1840s, 7 Sago Street is now home to traditional Chinese apothecaries, unpretentious eateries and souvenir stalls. For toothsome pastries and delightful mooncakes, be sure to swing by <PERSON> Kok—This old-school bakery has been in operation since 1935.\n", "\n", "8. Souvenir Shopping\n", "\n", "You’ll find plenty of knick-knacks and memorable mementos by browsing the wares of the pushcart vendors along Sago Street and its immediate vicinity.\n", "\n", "We personally recommend visiting 8 On Cheong Jewellery along Smith Street for a contemporary take on jade jewellery, Thye Shan Medical Hall on New Bridge Road for Traditional Chinese Medicine and Lim <PERSON> Guan for the sweet and savoury-tasting bak kwa (barbecued meat slices).\n", "\n", "9. Chinatown Visitor Centre\n", "\n", "If you’re still feeling disoriented by the district’s hustle and bustle, you can get all the info you need at the 9 Chinatown Visitor Centre. These visitor centres can be found all across Singapore, and offer a range of walking tours, souvenirs and tickets to island-wide attractions.\n", "\n", "Note: Chinatown Visitor Centre is temporarily closed.\n", "\n", "10. Chinatown Complex\n", "\n", "Alternatively, make a pit stop for lunch at the island’s largest hawker centre. While you won’t find food vendors touting their dishes along Singapore’s busy street these days, their legacy lives on at 10 Chinatown Complex.\n", "\n", "Knock back a pint of craft beer at Smith Street Taps. For lunch, indulge in the claypot rice from Lian He Ben Ji Claypot, or the beloved dishes from Liao Fan Hong Kong Soya Sauce Chicken Rice & Noodle.\n", "\n", "11. <PERSON>’s Supplies\n", "\n", "Make 11 Nam’s Supplies your next destination, and gain insight into the religious customs of Singapore’s Chinese community. The establishment stocks a range of paper effigies, incense oil and other offerings used during religious ceremonies to commemorate and show respect to the dearly departed.\n", "\n", "12. <PERSON><PERSON>\n", "\n", "One of Singapore’s oldest and most venerated Hokkien temples, 12 Thian Hock Keng Temple's patron deity<PERSON><PERSON><PERSON><PERSON>was said to have watched over the 19th-century Chinese immigrants who made their ways across the seas.\n", "\n", "Before the land was reclaimed, it used to stand on the shoreline of Telok Ayer Basin. Venture inside, and marvel at the ingenuity of traditional Chinese carpentry; the space was built without a single nail being used during its construction.\n", "\n", "13. My Awesome Café\n", "\n", "Talk about putting the ‘hospital’ in hospitality—formerly the home of a decades-old medical establishment known as the Chung Hwa Free Clinic, this historical space has been revitalised by 13 My Awesome Cafe. This trendy eatery serves up all-day breakfast platters, hearty sandwiches and healthy salads amidst industrial-chic surroundings.\n", "\n", "Be sure to grab some merchandise on your way out, and snap an Instagram photo to commemorate your time in Chinatown.\n", "\n", "Afterwards, spend the night winding down at any of the watering holes at Keong Saik or Ann <PERSON>, or tuck into some Michelin-starred nosh in the area.Link: https://www.visitsingapore.com/walking\n", "\n", "tour/\n", "\n", "Title: Walking TrailsLink: https://www.visitsingapore.com/de_de/festivals\n", "\n", "events\n", "\n", "singapore/\n", "\n", "Title: Festivals und Events\n", "\n", "Festivals\n", "\n", "und Events\n", "\n", "Ob die festliche Feier des Chinesischen Neujahr oder das\n", "\n", "Röhren der Formel-1-Motoren – irgendwo auf der Insel\n", "\n", "findet immer eine Party statt. Erfahren Sie mehr über die\n", "\n", "besten Festivals und Events, die von Januar bis Dezember\n", "\n", "in Singapur stattfinden.Link: https://www.visitsingapore.com/de_de/travel\n", "\n", "guide\n", "\n", "tips/getting\n", "\n", "around/tourism\n", "\n", "centre/\n", "\n", "Title: Singapore Visitor Centres\n", "\n", "Singapore Visitor Centres\n", "\n", "<PERSON><PERSON><PERSON> Si<PERSON> in einem dieser Zentren im Stadtzentrum von Singapur vorbei, um mehr Informationen über Singapur zu erhalten, Tickets für Sehenswürdigkeiten und Touren zu kaufen oder Andenken auszusuchen.Link: https://www.visitsingapore.com/content/desktop/de_de/see\n", "\n", "do\n", "\n", "singapore/arts/performance\n", "\n", "arts/museum\n", "\n", "of\n", "\n", "ice\n", "\n", "cream\n", "\n", "singapore\n", "\n", "Title: Museum of Ice Cream Singapur\n", "\n", "<PERSON>hokoladenfabrik war gestern - Das Museum of Ice Cream (MOIC) in Singapur ist ein Wunderland, das Besucher mit unzähligen, süß-kalten Überraschungen lockt.\n", "\n", "Die MOIC-Franchise stammt aus den USA und rühmt sich einer stattlichen Promi-<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> und <PERSON>. Diese sensationelle Attraktion gibt es nun auch als erste internationale Franchise auf Singapurs sonniger Insel.\n", "\n", "Das farbenfrohe Museum erstreckt sich in Kasernen aus der Kolonialzeit im Dempsey-Viertel über 5.600 Quadratmeter und besteht aus 14 multisensorischen und interaktiven Ausstellungsbereichen, die unsere Fantasie beflügeln. Die unterhaltsamen Museumsführer nehmen Sie mit auf eine 60- bis 90-minütige Entdeckungstour durch diese Zauberwelt: Als Zugabe erhalten Si<PERSON> so viel Süßes, wie Ihr Magen vertragen kann!\n", "\n", "Der Dragon Playground haucht längst vergangenen Zeiten neues Leben ein: Er ist inspiriert von den Spielplätzen, die sich in den 1970ern überall in den Wohnanlagen unserer Insel befanden und wie es sie heute noch in Toa Payoh gibt. Der Banana Jungle lädt Familien und Freunde zu jeder Menge Spaß in kräftigem Pink- und Gelbton ein.\n", "\n", "Der Sprinkle Pool im MOIC Singapur ist der größte seiner Art und eine großartige Hintergrundkulisse für ein denkwürdiges Selfie. Die rosafarbene Oase am California Beach lockt sowohl die Jugend als auch die Junggebliebenen.\n", "\n", "Nach Ihrem kalt-süßen Traumerlebnis können Si<PERSON> sich ein paar Leckereien im Museumsshop kaufen, noch eine Kugel Eis im Café genießen oder bei einem Drink an der Bar ausspannen.\n", "\n", "Das Museum of Ice Cream Singapore öffnet im August 2021. Tickets können Sie hier buchen!Link: https://www.visitsingapore.com/content/desktop/de_de/travel\n", "\n", "guide\n", "\n", "tips/getting\n", "\n", "around/\n", "\n", "Title: Fortbewegung in Singapur – Visit Singapore Offizielle Website\n", "\n", "Unterwegs mit dem Zug\n", "\n", "<PERSON> schnellsten kommen Sie in der Stadt wahrscheinlich mit der U-Bahn (Mass Rapid Transit, MRT) von A nach B. Viele der beliebtesten Sehenswürdigkeiten befinden sich nur einen kurzen Fußweg von einer MRT-Station entfernt.\n", "\n", "Besucher können die folgenden Optionen für MRT-Fahrten nutzen:\n", "\n", "1. <PERSON><PERSON> sich einen Singapore Tourist Pass (STP), eine spezielle EZ-Link-<PERSON>, mit der Sie für einen Tag (10 SGD), <PERSON><PERSON>e (16 SGD) oder drei Tage (20 SGD) unbegrenzt reisen können.\n", "\n", "Der Pass kann an einer TransitLink Verkaufsstelle an ausgewählten MRT-Stationen, die hier aufgelistet sind, oder im Concession Card Replacement Office an der MRT-Station Somerset gekauft werden. Sie sind auch rund um die Uhr an den STP-Automaten an der Orchard Station, HarbourFront Station und Changi Airport (Terminal 2 und 3) in der Nähe der TransitLink Verkaufsstelle verfügbar.\n", "\n", "2. <PERSON><PERSON> können auch Ihre im Ausland ausgestellten kontaktlosen Mastercard®- und Visa-Bankkarten verwenden, um die Fahrpreise für öffentliche Verkehrsmittel in Singapur zu bezahlen.\n", "\n", "Bitte beachten Sie, dass für ausländische Bankkarten eine Bearbeitungsgebühr anfällt - Einzelheiten dazu finden Sie auf der Website von TransitLink SimplyGo.\n", "\n", "3. <PERSON><PERSON><PERSON><PERSON>-Smartcard für Erwachsene (EZ-Link/Nets FlashPay): Sie kosten 12 SGD inklusive Kartenguthaben und verfügen über ein gespeichertes Guthaben von 7 SGD, das Sie bei Ihren Fahrten verwenden können. Sie können diese Karte an jeder Transitlink-Verkaufsstelle und in vielen Geschäften kaufen.Link: https://www.visitsingapore.com/content/desktop/de_de/walking\n", "\n", "tour/eat\n", "\n", "drink/bars\n", "\n", "in\n", "\n", "club\n", "\n", "street\n", "\n", "ann\n", "\n", "<PERSON>ang\n", "\n", "hill.html\n", "\n", "Title: Bars in Ann Siang Hill und Club Street\n", "\n", "Übersicht\n", "\n", "Zwischen dem historischen Chinatown und dem modernen Telok Ayer befindet sich eine L-förmige Enklave, die für ihre hippen, kleinen Kneipen und lässigen Bars bekannt ist.\n", "\n", "In dem Viertel Ann Siang Hill und Club Streetgibt es eine Fülle cooler Bars dicht beieinander – ideal für einen Kneipenbummel.\n", "\n", "Bereiten Sie sich auf einen feucht-fröhlichen Abend vor.\n", "\n", "Alle Orte befinden sich in der Nähe der MRT-Stationen Chinatown und Telok Ayer\n", "\n", "<PERSON>\n", "\n", "Schauen Sie im modernen britischen Restaurant 1 Oxwell and Co zum Abendessen vorbei.\n", "\n", "Weinliebhaber werden von der Auswahl an Rot- und Weißweinen bei 2 Beaujolais nicht enttäuscht sein. Bestellen Sie einige französische Gerichte (wie z. B. Quiche), die zu Ihrem Wein passen.\n", "\n", "Filmfans sollten sich aufmachen zu 3 The Screening Room, wo Sie eine einzigartige Kombination aus Filmen und Getränken erwartet. Auf der großen Leinwand im dritten Stock können Sie einen Klassiker anschauen, be<PERSON> in den vierten Stock weiterziehen, wo Getränke (und ein atemberaubender Ausblick auf die Stadt) <PERSON><PERSON> in der La Terraza Rooftop-Bar erwarten.\n", "\n", "Wenn Sie es gerne voll und lebhaft haben, dann gehen Sie zu 4 Gem Bar. Kaum zu übersehen ist die Bar an der Kreuzung zwischen Ann Siang Hill und Club Street. Auf der Straße vor den Türen der Bar sind in der Regel Massen an Menschen anzutreffen.\n", "\n", "Club Street\n", "\n", "Ganz vorne auf der Club Street: 5 IZY <PERSON>ser von der Izakaya (japanische Gastropub) inspirierte Ort serviert ausgezeichnetes Hühnchen-Karaage (gebratenes Hühnchen) und ein luxuriöses Wagyu-Donburi (Reisschüssel).\n", "\n", "Probieren Si<PERSON> dies<PERSON>iten, be<PERSON> sich mit einer Auswahl an Sa<PERSON> (japanischer Reiswein) verw<PERSON><PERSON><PERSON> lassen, der aus verschiedenen Präfekturen Japans stammt – außerdem gibt es Bier von Marken wie Suntory und Hitachino.\n", "\n", "Für Budget-Bewusste gibt es 6 Drinks and Co, das zwei Filialen auf der Club Street betreibt (eine davon hat eine umfangreichere Speisekarte sowie mehr Plätze).\n", "\n", "<PERSON>e finden dort <PERSON>, <PERSON><PERSON><PERSON> und Biere zu fairen Preisen (eine gute Flasche Wein gibt es zum Beispiel ab 35 SGD). <PERSON>n Si<PERSON> unterwegs eine Flasche Alkohol kaufen möchten, dann sollten Si<PERSON> einen Blick in das Spirituosengeschäft werfen.\n", "\n", "Für ein köstliches französisches Abendessen besuchen Sie 7 Les Bouchons. Gäste mit großem Appetit können das 250 Gramm schwere, perfekt gebratene Entrecôte wählen, für das dieses Restaurant berühmt ist, während sich unter den Platten zum Teilen eine riesige Côte de Bœuf Platte befindet. Die Meeresfrüchtegerichte des Les Bouchons, wie die Riesengarnelen und das Lachsfilet, sind auch sehr beliebt.Link: https://www.visitsingapore.com/de_de/about\n", "\n", "passion\n", "\n", "made\n", "\n", "possible/\n", "\n", "Title: Über Passion Made Possible\n", "\n", "Die Geschichte Singapurs ist von Höhen und Tiefen geprägt. Unsere Stadt ist innerhalb von nur fünf Jahrzehnten von einem Entwicklungsland zu einer Mega-Metropole geworden.\n", "\n", "Dies wurde durch unsere unstillbare Leidenschaft sowie unserer unerschütterlichen Haltung, niema<PERSON> aufzugeben, erre<PERSON>t – was uns noch heute immer neue Hürden zu neuen Höhen erklimmen lässt. Passion Made Possible ist der Unternehmergeist, der uns heute definiert und uns beim Erschließen neuer Möglichkeiten antreibt.\n", "\n", "Wo das Gewöhnliche außergewöhnlich wird\n", "\n", "Unsere Stadt hat unz<PERSON><PERSON><PERSON> Facetten; von weitläufigenNaturschutzgebieten über geschäftige Kulturviertel bis zu spannenden Attraktionen, die Reisende jeder Couleur anlocken. Wir sind mehr als nur eine Stadt der neuen Erfahrungen – wir sind ein Reiseziel, an dem Möglichkeiten zur Realität werden.\n", "\n", "Wir pflanzen Bäume nicht einfach nur an – wir formen sie zu vertikalen Wunderwerken. Wir zelebrieren nicht nur kulturelle Vielfalt – wir lassen <PERSON> in lebendige Traditionen eintauchen. Wir bieten nicht nur atemberaubende Aussichten – wir ermöglichen Panoramablicke in eine Welt der Wunder.\n", "\n", "<PERSON><PERSON><PERSON>, ob <PERSON><PERSON> Kunst an Straßenecken oder grüne Oasen mitten im Großstadttrubel entdecken: In Singapur werden gewöhnliche Momente zu außergewöhnlichen Erfahrungen.\n", "\n", "Ein Hort der Inspiration\n", "\n", "Inspirierende Orte gibt es in Singapur in Hülle und Fülle; von der nahtlosen Verschmelzung von Architektur und Natur im Jewel Changi Airport bis zu den felsigen Stränden und blühenden Mangroven auf Pulau Ubin. Neue Horizonte und kühne Abenteuer erwarten Sie an den Supertrees in den Gardens by the Bay oder beim Wandern auf den Gezeitenpfaden auf Sentosa.\n", "\n", "Ob Sie das Zentrum des Civic District vom Seitenwagen einer Vintage-Vespa aus erkunden, Gewürze in Little India kaufen oder einen aromatischen Tee in Chinatown geni<PERSON>ßen – eine Welt der vielseitigsten Kulturen erwartet Sie.\n", "\n", "Überall erwarten Sie neue Erlebnisse: von einzigartigen Aromen in Lokalen wie Synthesis bis zu faszinierenden Ausflügen in die chinesische Folklore in Haw Par Villa.\n", "\n", "<PERSON><PERSON><PERSON> Sie Außergewöhnliches, Made in Singapore.Link: https://www.visitsingapore.com/id_id/see\n", "\n", "do\n", "\n", "singapore/culture\n", "\n", "heritage/\n", "\n", "Title: <PERSON><PERSON>\n", "\n", "Kemegahan budaya Chinatown, bangunan gaya hidup modern, dan tempat ibadah dari masa lalu menjadikan perjalanan satu hari Anda di sini begitu bermakna.Link: https://www.visitsingapore.com/id_id/festivals\n", "\n", "events\n", "\n", "singapore/\n", "\n", "Title: Festival & Acara\n", "\n", "Festival\n", "\n", "& Acara\n", "\n", "Baik itu perayaan <PERSON>hun Baru Imlek yang meriah\n", "\n", "atau deru mobil Formula 1, selalu ada saja pesta\n", "\n", "yang digelar di negeri ini. Lihat festival dan acara\n", "\n", "ungg<PERSON>n di Singapura dari <PERSON>ri hingga <PERSON>ember.Link: https://www.visitsingapore.com/id_id/singapore\n", "\n", "hotels/\n", "\n", "Title: <PERSON><PERSON><PERSON>\n", "\n", "Singapore Tourism Awards\n", "\n", "Singapore Tourism Awards (STA) memberikan pehargaan bagi individu dan penyelenggara yang mewujudkan pengalaman istimewa serta menunjukkan keunggulan wirausaha. Penghargaan ini diberikan untuk memotivasi industri wisata agar senantiasa menciptakan pengalaman menarik dan memikat di Singapura, atau menerapkan manfaat terbaik. Untuk mengetahui lebih lanjut, berkunjunglah ke sini.Link: https://www.visitsingapore.com/content/desktop/id_id/editorials/traditional\n", "\n", "threads\n", "\n", "versus\n", "\n", "modern\n", "\n", "fashion\n", "\n", "in\n", "\n", "singapore.html\n", "\n", "Title: Mode Singapura—Modern vs warisan budaya\n", "\n", "Seperti kota kami, kancah mode Singapura adalah perpaduan eklektik antara yang baru dan yang lama, dengan pengaruh dari berbagai wilayah.\n", "\n", "<PERSON><PERSON><PERSON> jalanan dan pusat belanja di sini, dan temukan beragam gerai yang menjual busana kuno dan penafsiran modern dari desain klasik.\n", "\n", "Apa pun yang di<PERSON>ukan, apa<PERSON><PERSON> menghormati warisan budaya atau menemukan busana kontemporer yang terilhami oleh budaya regional, panduan mode lokal ini akan membantu Anda menemukan kain yang menginspirasi dan menonjolkan individualitas Anda.\n", "\n", "<PERSON><PERSON>, yang berasal dari <PERSON>, ad<PERSON>h kain bersejarah yang usianya lebih dari berabad-a<PERSON>, dibuat dengan teknik kuno pewarnaan tangan menggunakan lilin. <PERSON><PERSON><PERSON> memukau ini telah menarik imajinasi desainer di seluruh dunia dengan desain rumit dan warna semarak, serta kain ini digunakan dalam busana tradisional dan kontemporer. Untuk menambahkan sentuhan tradisi pada koleksi busana Anda, kunjungilah Wellie Batik di Holland Village. Gerai ayah-anak ini telah memproduksi aksesori dan busana berpola batik selama lebih dari dua dekade, dan menjual beragam kaus, tas, dan sarung (kain panjang yang diikat di sekitar pinggang). Batik dipadukan dengan arti khusus, dengan motif yang melambangkan konsep seperti kebijaks<PERSON>an, cinta, dan kekuatan. Untuk mengungkap makna yang mendalam di balik tradisi ini, mamp<PERSON><PERSON> ke Galeri Tokokita. Oniatta Effendi—pembelajar seumur hidup dan pemilik toko—dengan senang hati akan menjamu Anda dengan kisah di balik kain dan busana mode kontemporer yang dijual di sini. Wellie Batik di Holland Road Shopping Centre. 211 Holland Avenue #03-18, Singapore 278967. +65 9171 5662. Sen-Sab 10:00-19:00; Ming 11:30-19:00. Galeri Tokokita di MOX. 451 Joo Chiat Road #02-07, Singapore 427664. +65 8522 3505. Sel, Rab, & Jum 10:00-16:00; Kam 09:00-16:00; Sab siang-18:00.\n", "\n", "Baju Kurung dan Ba<PERSON>a luas sebagai simbol warisan budaya <PERSON>, baju kurung dan baju melayu sering kali dipakai dalam kegiatan sehari-hari atau acara resmi. <PERSON>ju kurung adalah kombinasi blus panjang berpotongan longgar dan rok yang dikenakan oleh wanita, sementara baju melayu adalah kaus berpotongan longgar yang dipadukan dengan celana yang dipakai oleh pria. Untuk membeli baju kurung atau baju melayu, berjalan-jalan<PERSON> di Kampong Gelam. <PERSON><PERSON><PERSON> penuh sejarah ini adalah rumah dari berbagai gerai, <PERSON><PERSON><PERSON>, tempat Anda dapat membeli baju kurung yang disesuaikan menurut individualitas Anda masing-masing. <PERSON><PERSON>han modern pada busana anggun ini dapat ditemukan di Sufyaa, yang menawarkan rancangan khusus yang disesuaikan. <PERSON><PERSON><PERSON>ab<PERSON>. 62 Arab Street, Singapore 199769. +65 9625 1554. <PERSON><PERSON>p hari 10:30-19:30. <PERSON><PERSON><PERSON> di Centropod. 80 Changi Road #02-32, Singapore 419715. +65 9102 3818. Sel-Sab 13:00-18:00.\n", "\n", "Cheongsam Anda tidak perlu menjadi penggemar <PERSON> untuk menikmati keindahan cheongsam tradisional Tiongkok. Gaun pas badan ini berasal dari Shanghai pada era 1920-an dan berkembang dari jubah panjang yang dikenakan oleh wanita selama Dinasti Qing. Meskipun daya tarik busana ini memudar di Singapura pada era 1970-an, kini busana ini kembali menarik berbagai kalangan di era modern, dengan banyak desainer mode lokal yang merancang busana klasik dan mode dengan menjadikan cheongsam sebagai inspirasi. Para penggila mode yang ingin menambahkan sentuhan keindahan oriental ke koleksi busananya tak boleh melupakan koleksi busana anggun di Laichan. Pendiri butik —Mr <PERSON><PERSON>—telah dielu-elukan sebagai ‘maestro cheongsam’ kota kami. Walaupun ia mempelajari keahliannya dari guru tradisional, gaunnya mengadopsi teknik mode Barat dan kain kimono Jepang untuk menambah kesan modern nan unik. Atau, Anda dapat memilih gaun yang lebih tradisional di Chinatown, dengan mengunjungi Golden Scissor Cheongsam—Penjahit wanita di toko itu, Madam Li Qiying, telah merancang cheongsam selama lebih dari dua dekade. Anda akan dapat menelusuri beragam busana katun yang harganya terjangkau dan busana sutra yang mewah. Laichan di Paragon. 290 Orchard Road #03-20, Singapore 238859. +65 6235 0049. Setiap hari 11:00-20:00. Golden Scissor Cheongsam di People’s Park Food Centre. Block 32 New Market Road #02-1114-1116, Singapore 050032. +65 8163 0178. Setiap hari 13:00-20:00.Link: https://www.visitsingapore.com/content/desktop/ja_jp/deals/singaporewards\n", "\n", "Title: SingapoRewards\n", "\n", "シンガポールを探検しませんか？\n", "\n", "無料で！\n", "\n", "無料のアクティビティ券を利用して、シンガポール旅行をもっと楽しみましょう。絶景サイクリングから爽快なカヤックでのアドベンチャーまで、ユニークなアクティビティの数々を厳選しました。\n", "\n", "シンガポール・リワードを使って、旅行者1名様につき1つの無料アクティビティをお選びいただけます。シンガポール行きのフライトを予約したら、対象リストからお好きなアクティビティを選んでシンガポール・リワードを使いましょう。\n", "\n", "では、シンガポールでお会いしましょう！Link: https://www.visitsingapore.com/mice/vi_vn/\n", "\n", "Title: MICE Singapore – Website ch<PERSON><PERSON> thức Visit Singapore\n", "\n", "<PERSON><PERSON><PERSON> bê<PERSON> bán hàng và <PERSON>hà cung cấp\n", "\n", "<PERSON><PERSON><PERSON> chuyên gia trong nước của chúng tôi luôn sẵn sàng hỗ trợ bạn trong quá trình tổ chức sự kiện.\n", "\n", "XEM TẤT CẢ CÁC BÊN BÁN HÀNG VÀ NHÀ CUNG CẤPLink: https://www.visitsingapore.com/th_th/deals/\n", "\n", "Title: DealsLink: https://www.visitsingapore.com/th_th/see\n", "\n", "do\n", "\n", "singapore/socialiser/\n", "\n", "Title: SocialisersLink: https://www.visitsingapore.com/madewithpassion/\n", "\n", "Title: Discover Singaporean Brands\n", "\n", "You know it's good when it's\n", "\n", "Made With Passion\n", "\n", "Made With Passion is a national initiative that showcases and celebrates local lifestyle brands and the passion behind them. The Made With Passion brand mark can be found on selected brands, and is a recognition of the hard work and passion that goes behind their pursuit of excellence.\n", "\n", "Join us in the celebration of the many facets of passion and discover the best of what Singapore has to offer.Link: https://www.visitsingapore.com/mice/en/why\n", "\n", "singapore/dynamic\n", "\n", "industries/\n", "\n", "Title: Dynamic Industries\n", "\n", "Dynamic Industries\n", "\n", "Tap into the strength of our developed key sectors for partnerships, opportunities and insights. Click on any industries below to find out more.Link: https://www.visitsingapore.com/mice/en/plan\n", "\n", "your\n", "\n", "event/vendor\n", "\n", "and\n", "\n", "suppliers/\n", "\n", "Title: MICE Vendors and Suppliers\n", "\n", "Tools & Resources\n", "\n", "Access a wealth of information from resources and toolkits with recommendations to get everything you need for corporate event planning.Link: https://www.visitsingapore.com/mice/en/plan\n", "\n", "your\n", "\n", "event/assistance\n", "\n", "schemes/\n", "\n", "Title: MICE Assistance Schemes – Visit Singapore Official\n", "\n", "Tools & Resources\n", "\n", "Access a wealth of information from resources and toolkits with recommendations to get everything you need for corporate event planning.Link: https://www.visitsingapore.com/mice/en/plan\n", "\n", "your\n", "\n", "event/frequently\n", "\n", "asked\n", "\n", "questions/\n", "\n", "Title: Frequently Asked Questions on MICE in Singapore – Visit Singapore Official\n", "\n", "From tourist attractions to key transit locations, you can select the best locations from over 100 spaces to reach your target audience.\n", "\n", "Please contact us if you are allocated these locations for your event.\n", "\n", "Types of lamp posts\n", "\n", "There are 2 types of lamp posts; decorative and non-decorative with different requirements for banner dimensions. To see what type of lamp posts are in the area, please refer to the ‘Banner location map’.\n", "\n", "Non-decorative lamp posts are lamp posts with space for only one banner. Banners must be 3m x 1m. Decorative lamp posts in selected areas can consist of 1 or 2 banner spaces. Banners must be 3.5m x 1.2m.\n", "\n", "Key things to note\n", "\n", "1. Be sure to follow the design guidelines.\n", "\n", "2. Get the required BCA licenses approvals before displaying the banners.\n", "\n", "3. For used banners, find out how banner recycling works.\n", "\n", "When to apply\n", "\n", "Banner designs are to be submitted 5 weeks before the first installation date of the banners.\n", "\n", "How to apply\n", "\n", "Contact STB directly to apply for the desired location.\n", "\n", "Apply nowLink: https://www.visitsingapore.com/mice/en/event\n", "\n", "listing/\n", "\n", "Title: Browse for All MICE Events in Singapore\n", "\n", "TravelRevive 2020\n", "\n", "The first hybrid travel tradeshow to take place in Asia-Pacific amidst COVID-19, Travel Revive received close to 1,000 delegates on-site over the 2 day event. This was also the first tradeshow to showcase a new hybrid event format that includes a blend of physical...Link: https://www.visitsingapore.com/content/mice/en/contact\n", "\n", "us.html\n", "\n", "Title: Contact us for enquiries on MICE in Singapore – Visit Singapore Official\n", "\n", "Tools & Resources\n", "\n", "Access a wealth of information from resources and toolkits with recommendations to get everything you need for corporate event planning.Link: https://www.visitsingapore.com/mice/en/why\n", "\n", "singapore/success\n", "\n", "stories/asia\n", "\n", "tech\n", "\n", "x\n", "\n", "singapore\n", "\n", "2022/\n", "\n", "Title: Asia Tech x Singapore 2022\n", "\n", "Singapore’s largest tech event since border reopening facilitated critical conversations on the development of the digital economy and an inclusive digital society.\n", "\n", "Asia’s flagship technology event Asia Tech x Singapore (ATxSG) returned from 31 May to 3 June 2022, convening leaders and decision-makers from across the global technological ecosystem to network, debate, and exchange ideas on the latest technologies that drive digital innovation and transformation across all segments of society.\n", "\n", "As the largest tech event that Singapore has hosted since reopening its borders, ATxSG 2022 is testament to the country’s strong recovery and position as a Global-Asia node for business and MICE. Among the 16,600 attendees were representatives from the governments of Brazil, Estonia, Indonesia and the United States as well as top executives from renowned tech companies like Amazon Web Services, Google, Meta, Microsoft, and Zoom.\n", "\n", "“Digital and tech brings many opportunities but also challenges. That impact is felt at the intersection of Tech and Governance, Tech and Businesses, and Tech and Society. This involves building a trusted digital environment, creating economic growth, seeding innovations, and tackling society’s pressing problems. It is therefore key to bring together thought leaders across these domains, to shape that shared digital future,” said <PERSON><PERSON>, Chief Executive of Singapore’s Infocomm Media Development Authority (IMDA).\n", "\n", "ATxSG 2022 was the event’s first-ever in-person edition, and was organised by the IMDA, Informa Tech and supported by the Singapore Tourism Board. IMDA hosted the apex event ATxSummit, an exclusive invite-only event featuring high-level discussions among global tech, business and government leaders, as well as the ATxAI conference focused on artificial intelligence. Both events were held at The Ritz Carlton, Millenia Singapore. Meanwhile, ATxEnterprise and ATxImpact, ATxSG’s segments for industry and start-ups respectively, were organised by Informa Tech at Singapore EXPO.Link: https://www.visitsingapore.com/mice/en/why\n", "\n", "singapore/success\n", "\n", "stories/singapore\n", "\n", "airshow\n", "\n", "2022/\n", "\n", "Title: Singapore Airshow 2022\n", "\n", "Overview\n", "\n", "Asia’s biggest aerospace and defence exhibition, Singapore Airshow returned for its eighth edition from 15 to 18 February 2022. Close to 13,000 trade visitors from more than 39 countries/regions and almost 600 participating companies reconnected and explored business opportunities over the four days of the show. “We are thrilled that our exhibitors and visitors came together for conversations, and more importantly to collaborate,” said <PERSON><PERSON>, Managing Director of Experia Events, organiser of the event.\n", "\n", "As Asia’s most influential platform for top decision-makers in the global aerospace and defence ecosystem, Singapore Airshow attracted global aviation companies such as Airbus, Boeing and Lockheed Martin as well as government agencies and armed forces from around the world. Leading firms headquartered in Asia such as ST Engineering and the Aviation Industry Corporation of China were also in attendance, pulling out all stops to highlight the latest innovations.\n", "\n", "Optimism for industry recovery was palpable throughout the event, with many also discussing pathways and solutions towards a more sustainable future for the sector.Link: https://www.visitsingapore.com/mice/en/why\n", "\n", "singapore/success\n", "\n", "stories/gamescom\n", "\n", "asia\n", "\n", "2021/\n", "\n", "Title: Gamescom Asia 2021\n", "\n", "Overview\n", "\n", "2021 marked the debut of the highly anticipated Asian edition of gamescom – one of Europe’s leading trade fairs for video games. Based in Singapore, gamescom asia is a platform for both Southeast Asian game developers to explore global partnerships, as well as for international publishers looking to connect with Asia’s gaming industry.\n", "\n", "Held from 14 to 17 October 2021 at the Suntec Convention Centre, the hybrid event attracted more than 2,200 physical and virtual attendees from 60 countries. The event, which was organised by Koelnmesse Singapore and supported by game – the association of the German games industry, consisted of several key segments. These included the industry conference and virtual Trade Zone (14-15 Oct), both of which were targeted at B2B audiences, as well as a B2C-oriented fully online Entertainment Zone (15-17 Oct).\n", "\n", "Among the many exciting events on offer over the four days were panels, keynotes and technical dialogues by industry experts from renowned studios such as Riot Games, Bandai Namco and Neon Doctrine, as well as hands-on test drives of the latest game and product demos. Avid gamers tuning in virtually were not left out as they were kept entertained by live hosts, sneak peeks, game trailers, online challenges and interviews.\n", "\n", "Further adding to the bustle of gamescom asia were two partner events: Gaming Matters – a trade conference that explores the synergies between the gaming industry and the music, sports and marketing industries, and Global Top Round (GTR) Conference – which supports early-stage gaming studios around the world. The latter event was attended by investors, publishers and partners, as well as the Top 20 finalists of GTR’s annual acceleration programme.Link: https://www.visitsingapore.com/mice/en/event\n", "\n", "listing/singapore\n", "\n", "international\n", "\n", "agri\n", "\n", "food\n", "\n", "week/\n", "\n", "Title: Singapore International Agri\n", "\n", "Food Week\n", "\n", "Event overview\n", "\n", "Singapore International Agri-Food Week (SIAW) 2023 is here to showcase the latest advancements, cutting-edge innovations, and groundbreaking discoveries in agriculture and food technology. Get ready to be immersed in a world of possibilities at events like the Asia Pacific Innovation Summit 2023, Agri-Food Tech Expo Asia 2023, and the Global Agri-Food Scientific Symposium!Link: https://www.visitsingapore.com/mice/en/why\n", "\n", "singapore/success\n", "\n", "stories/deloitte\n", "\n", "managenement\n", "\n", "retreat/\n", "\n", "Title: Deloitte Management Retreat Success Stories\n", "\n", "Overview\n", "\n", "The experience of working hard, followed by indulging in a post-work tipple while enjoying the sunset is exactly what Deloitte wanted to recreate when it took its management team out of the conference halls and onto the sunny seaside of Lazarus Island for its annual management meeting.\n", "\n", "“One of the key criteria for Deloitte is making sure that we have our management meetings in iconic locations,” Mr <PERSON>, Regional Managing Director, Deloitte Consulting SEA, shared. He added that with remote working keeping employees at home for the past year, it was all the more important to find a unique location to rejuvenate the staff.\n", "\n", "With its beautiful beaches and idyllic waterfronts, Lazarus Island, which is located amongst the Southern Islands, was the perfect choice as it offered attendees the feel of being overseas, while still remaining in Singapore.\n", "\n", "In late March 2021, 40 Deloitte partners attended in person, and 20 more in the region tuned in virtually. Attendees worked from a comfortable glass house in full view of the ocean, dined on the Royal Albatross ship on a sail around the Southern Islands, and wrapped up the day having drinks by the sunset.\n", "\n", "It was no mean feat to turn the private island — one with limited F&B options and meeting facilities like audiovisual equipment — into a functioning business event destination. Sentosa Development Corporation rose to the challenge, and through an intensive event planning process, successfully staged the offshore event.\n", "\n", "Securing safety both on and off shore\n", "\n", "”The event organisers were very rigorous in the safety management aspect,” Mr <PERSON><PERSON>, Consumer Industry Leader, Deloitte Consulting SEA, said.\n", "\n", "Indeed, safety was paramount, and safe distancing measures were implemented throughout the event. For example, in addition to using TraceTogether to check in, attendees were also split into two separate cohorts to board the ferry, <PERSON>, Assistant Chief Executive, Sentosa Development Corporation, explained.\n", "\n", "“At the meeting itself, the venue was laid out to ensure distance between the attendees and limit inter-mingling. Hand sanitisers were provided and microphones were cleaned regularly after each person spoke,” she added.\n", "\n", "A collaborative effort between leisure partnersLink: https://www.visitsingapore.com/mice/en/event\n", "\n", "listing/asia\n", "\n", "pacific\n", "\n", "agri\n", "\n", "food\n", "\n", "innovation\n", "\n", "summit/\n", "\n", "Title: Asia\n", "\n", "Pacific Agri\n", "\n", "Food Innovation Summit\n", "\n", "Event overview\n", "\n", "Asia-Pacific Agri-Food Innovation Summit is the anchor event of Singapore International Agri-Food Week (SIAW) bringing together 1000+ global leaders to identify the strategic priorities as we build greater capacity, security, and resilience in Asia’s agri-food system. Daily pitch sessions, a menu featuring new and innovative products from the region’s start-ups, and a sophisticated 1-1 networking app make this a stand-out event for start-up discovery, investment and partnerships.Link: https://www.visitsingapore.com/mice/en/event\n", "\n", "listing/agri\n", "\n", "food\n", "\n", "tech\n", "\n", "expo\n", "\n", "asia\n", "\n", "2023/\n", "\n", "Title: Agri Food Tech Expo Asia 2023\n", "\n", "Event overview\n", "\n", "INTO THE FUTURE OF FOOD SUSTAINABILITY AND SECURITY\n", "\n", "The Agri-Food Tech Expo Asia (AFTEA) is the region’s leading showcase platform for products, services and solutions on the forefront of innovation in the global Agri-Food and Agri-Tech Industry.\n", "\n", "In its second year, the Expo places a key focus on enabling your business to network, collaborate, exchange knowledge and establish long term partnerships to empower/ further the needs of the international agri-food tech industry.\n"]}], "source": ["print(raw_text)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"id": "gSp3TatO9gx-"}, "outputs": [], "source": ["text_splitter = RecursiveCharacterTextSplitter(\n", "    chunk_size = 500,\n", "    chunk_overlap  = 100,\n", "    length_function = len,\n", "    is_separator_regex = False,\n", ")"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"id": "-T4wxtXmrwEq"}, "outputs": [], "source": ["texts = text_splitter.split_text(raw_text)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "76tCK-JtBvjU", "outputId": "564d3839-e558-4e0c-9a38-c109d5be3cd2"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["262"]}, "metadata": {}, "execution_count": 27}], "source": ["len(texts)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MugV2iv1B5KC", "outputId": "97598e36-aa37-4890-96cf-3655321b3bfe"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Local culture on a plate\n", "\n", "Hailing from a hybrid heritage unique to this region, Peranakan* food is a blend of Chinese ingredients with the various distinct spices and cooking techniques of the Malay community, and an apt symbol of Singapore’s melting pot of ethnic cultures.\n"]}], "source": ["print(texts[4])"]}, {"cell_type": "markdown", "metadata": {"id": "NGwCBmlDGr0U"}, "source": ["## BGE Embeddings"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"id": "dTjFYKf7U4oV"}, "outputs": [], "source": ["from langchain.embeddings import HuggingFaceBgeEmbeddings"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"id": "a3nh9giwU6cV"}, "outputs": [], "source": ["model_name = \"BAAI/bge-small-en-v1.5\"\n", "encode_kwargs = {'normalize_embeddings': True} # set True to compute cosine similarity"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 528, "referenced_widgets": ["daf4b4a852744209907991a6c2966cb3", "96f8ccaf07794391af50c4b46123ff0c", "9ab8f8b6252445faa03312eb0a694675", "9f4e436970aa48bf96afd75f4088ddf9", "368b2f9e73c24e1793b0893a203dcd9d", "26441ab08b3449348fd347ceb209490b", "ff98934b96d54296a3fa72afd491b84d", "fbbf539537a64c3da3b3fedc95801c83", "31596ec6290f48298332b33382f00766", "c2ce3ca4017340bd87cfa290456d5015", "851619bd487f4606aaec560b36f23fa7", "b6be12de5cea495fb1eddd78c41f21d0", "0e76f659b48b4f819feda2b4243264de", "154d7104c893421189ed0c9efabbe28f", "405bc96f7d59488aac24f038635bd331", "7c96b7cd72464969b77d82e5383276a3", "fa9b5d5bef634bdc82b87c87c29f92d3", "008e25861fcc458a90c04cc8c33b4367", "2f71a982cecc4fb8ad7617884dccd139", "cd50d5d5175a46be9ed0d19a37e441c3", "c307f87abdc64c7b929c37e9b05f91ee", "3d27fc8f14714ba49e3599315486578c", "48512d17ea054e068deec4eea4863dbf", "9fdbb8bb2b6143ddb6009fe6d9f0e472", "f4230a23cb304a068efa66544a09f557", "5d1cb00ceb7748789226fda1f58b0a4e", "fd7ea0696e6d4c0ea7661b4a91772324", "bcd7d01133c24ba49f8691a734ffb8e6", "9fa39ded11174699ac0fc2249c76b180", "8dd7662de1c040dca74d7512bd576daa", "9883747427aa41f184380248c7cc826b", "bcb290e3be8c4d5a8ecf2c80aac219db", "9c1d45b811364f3aa247eb71f6b3f797", "7e503f9772aa4ddab7b85464b6cc9708", "2cdcd3f2d413478f91fd04b012195490", "91e47a9730d14238a9a37a821e1c2b5c", "5871110cc3b642a6a1484da093d444b3", "f0446b0d33b743f3949dc3d1f0cb824a", "f80d571e3d794001bdff4a262a23aea7", "23ddefb9b7b74569b60bf8d67bfdf936", "939b3dff330d478cac30436e5250f569", "27c67b594fc54d0cacbf9e3f572c3b00", "f826e95b30544cf489899049ecd24071", "b471bdafabb54964a175702b5b3ac1e6", "9e4bba31701b419d95fe0df93d1b089f", "3562215502ba4d4fa245aa2664532166", "08cbd7b62b5a4ba88a1bbddb3cbeea48", "cd6e56c80c1a47b393c91e2a0091fe61", "135a506df5224098a12c4d484627c7b8", "42e9fa4852d242648dc6b6b208c9342d", "88c80fea2a1440f480a96a1e476ddec7", "7cc665e9dd714b1285fcff1e3d235c84", "22886fc9beac482fbd0e70e4673383d7", "75592dea95fc41d7b6b4ef6bad7f8f72", "93af90cf63d94f9daf8d0f133dd9a9aa", "ce41452db26a4ee68d235a433b8da8ed", "c766e3e9235749c6b085529f5f90979e", "d82e3c5ba4f84a45a18ec099f89823e2", "93f615374410475f9d5c6a42d9a342d9", "ff22b99be102448a83c2a834c1b317ac", "09b50d5f77b543cbab1b7729fa991ead", "a1cc666b195c444bac2e58233971f7c8", "4dbd12c4ffb44429826cd6e1cafee6f0", "c2168c78e05741ebbd922ee2f1099c95", "6d012696dc804c58990e4c707efe5af5", "dc55034b980d489bbc4b92b787267dd2", "9029e54ea2f74ff58d47e21b1b7f4dfd", "3102ef78b0064210bf5fa819e9c02985", "b380faaac82b47c48b52cc00255385ae", "1667ad42b84a45659665382ea7b6eb8c", "2027dfd21e16443f90b4fab1466f321c", "102829f6002d4891ab0fea657f47d552", "5e81eb28076d4b67be6413c960419e4a", "0272abcbbc0846bc8ba7530d1bb0199f", "1c55cec7f7ef4ab29c5427e4e1039dd9", "cf51836aceaa4967a50bf51d97e96b0e", "4433344c254347f8ac48eccb3faf1325", "ff60ace5006647fba858f1217be96f81", "b91a3c61cff04b96a199181513befe03", "e73d07340f9a417993b65ce135443b6b", "2840ea0bb8ce4a6bafdaee2d6ee26855", "40c790b6e63f43b8be4a1ed1154ef073", "f41156f5cf7244bfbc95ae35f3850e80", "0af61f0b3bd447738f6ad185e42c8876", "0c7cf72284bd44f0a4cc98842437b31d", "9ad330d5afc24e64b7d44462467dd7e2", "02dc492b85264d31a428450b60da9ccc", "71f3267bf86642ea83f9b6b5579fb97b", "3f77adb9c44745ba9445b929c4b5a400", "ebc0c08856a54400a36c177a3c3da053", "d59946acd45e498db9ffa7b00fcefe08", "850b3f190fcb4f58985dcd013dd7a6c3", "46fce79af203421f8705c540bad9b1da", "d26d79af6a8d43c1ae0d39b0b4a9b063", "73cea16ea6034b5184621cf320fe3eed", "5a5733e0c41a4677838bf70b937df5ed", "fbffd93a1b5446439e3bf26efe2bf7f3", "a894726354e24ee6b43fed5442a31ac2", "effd66db2abe4a71820c7a5389b35e98", "8b13c6e1a7604317a7d740027e120ef2", "f123e162e5af45459d49572bcc49b912", "a7b447f2c5d84decb830e4bb02b887f8", "165df076279f489c8bfe32a37eab27c2", "4e76c2d6b11548ac850865a0e038ffd3", "a3434ac854404b6ebf456f9260e63162", "8d18616942ad4258a65a3a144b13e3ad", "d636658c996c40a88c2458c5bc6ddc92", "64732181d80c428cb78cfafe583441ac", "cbefc3cef96d45e1aaab536cc3a066c8", "88fac99bc696470f8aea85b97c94e375", "e7eabda9b5534691a6b6d7efdfa70b63", "81b22795c9cc4292a910f03da0a1b766", "add326d655e5437e8607cd151ef19736", "0b9c5d14cfdc4b15af92f8c3e1f47799", "d866d7aceb30462fb3afb24a22b7008f", "1f4293e41f2e47c88ba7cb3155b3ca68", "12c3078628524eb297e58fc347643c62", "c2d8dd2bd77a4d818e0a785e815b2cc0", "247d18d2f8314f59ad5bf65e1d53c0b2", "5958b3659e754912956498e1e248bc81", "0c4e03dfdb0c4f4f934fb94bf2fb309e"]}, "id": "B4AIp2aXJQzm", "outputId": "11d7c106-da47-481b-9e36-a6d643e80e80"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/huggingface_hub/utils/_token.py:89: UserWarning: \n", "The secret `HF_TOKEN` does not exist in your Colab secrets.\n", "To authenticate with the Hugging Face Hub, create a token in your settings tab (https://huggingface.co/settings/tokens), set it as secret in your Google Colab and restart your session.\n", "You will be able to reuse this secret in all of your notebooks.\n", "Please note that authentication is recommended but still optional to access public models or datasets.\n", "  warnings.warn(\n"]}, {"output_type": "display_data", "data": {"text/plain": ["modules.json:   0%|          | 0.00/349 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "daf4b4a852744209907991a6c2966cb3"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config_sentence_transformers.json:   0%|          | 0.00/124 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "b6be12de5cea495fb1eddd78c41f21d0"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["README.md:   0%|          | 0.00/94.8k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "48512d17ea054e068deec4eea4863dbf"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["sentence_bert_config.json:   0%|          | 0.00/52.0 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "7e503f9772aa4ddab7b85464b6cc9708"}}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/huggingface_hub/file_download.py:1132: FutureWarning: `resume_download` is deprecated and will be removed in version 1.0.0. Downloads always resume when possible. If you want to force a new download, use `force_download=True`.\n", "  warnings.warn(\n"]}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/743 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "9e4bba31701b419d95fe0df93d1b089f"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors:   0%|          | 0.00/133M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "ce41452db26a4ee68d235a433b8da8ed"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json:   0%|          | 0.00/366 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "9029e54ea2f74ff58d47e21b1b7f4dfd"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["vocab.txt:   0%|          | 0.00/232k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "ff60ace5006647fba858f1217be96f81"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.json:   0%|          | 0.00/711k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "3f77adb9c44745ba9445b929c4b5a400"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["special_tokens_map.json:   0%|          | 0.00/125 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "8b13c6e1a7604317a7d740027e120ef2"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["1_Pooling/config.json:   0%|          | 0.00/190 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "e7eabda9b5534691a6b6d7efdfa70b63"}}, "metadata": {}}], "source": ["embedding_function = HuggingFaceBgeEmbeddings(\n", "    model_name=model_name,\n", "    #model_kwargs={'device': 'cuda'},\n", "    encode_kwargs=encode_kwargs,\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "09TG9FsGGt1f"}, "source": ["## Vector DB"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gdAljZyxG92C", "outputId": "d27ae793-b9fd-438d-af88-7b5828fa6f4f"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["CPU times: user 44.3 s, sys: 3.26 s, total: 47.5 s\n", "Wall time: 48.9 s\n"]}], "source": ["%%time\n", "### Make the chroma and persiste to disk\n", "db = Chroma.from_texts(texts,embedding_function,persist_directory=\"./chroma_db\")"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WzCj8F8PuzyG", "outputId": "54fec0b0-b306-4f8e-dd89-c78c8c51c84f"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='watering holes that offer great ambience and company,\\n\\nSingapore has lots for you to experience.Link: https://www.visitsingapore.com/content/desktop/en/en\\n\\ndeals/singaporewards\\n\\nTitle: Explore the hidden gem tours with SingapoRewards\\n\\nWhat’s a better way to holiday?\\n\\nExploring the city’s best kept secrets with SingapoRewards'),\n", " Document(page_content=\"We'll see you soon!Link: https://www.visitsingapore.com/content/desktop/en/editorials/amazing\\n\\nthings\\n\\nyou\\n\\nnever\\n\\nknew\\n\\nabout\\n\\nsingapore\\n\\nTitle: 10 fun facts about singapore\\n\\nFor those who crave novelty, travel is all about new perspectives and new ways to experience the world. Beyond Singapore’s picture-perfect skyline, our bustling metropolis is an ever-evolving wonderland of bold new experiences and unforgettable adventures.\"),\n", " Document(page_content='questions/\\n\\nTitle: Frequently Asked Questions on MICE in Singapore – Visit Singapore Official\\n\\nFrom tourist attractions to key transit locations, you can select the best locations from over 100 spaces to reach your target audience.\\n\\nPlease contact us if you are allocated these locations for your event.\\n\\nTypes of lamp posts'),\n", " Document(page_content='The Ritz Carlton, Millenia Singapore. Meanwhile, ATxEnterprise and ATxImpact, ATxSG’s segments for industry and start-ups respectively, were organised by Informa Tech at Singapore EXPO.Link: https://www.visitsingapore.com/mice/en/why'),\n", " Document(page_content='One such business is 11 Onlewo, a design store that will let you take a piece of our city home with you. Fabrics, homeware and gifts abound at this establishment, and many of this store’s designs take inspiration from Singapore’s iconic neighbourhoods.\\n\\nThe Malayan Council')]"]}, "metadata": {}, "execution_count": 33}], "source": ["query = \"Tell me about Universal Studios Singapore?\"\n", "\n", "db.similarity_search(query, k=5)"]}, {"cell_type": "markdown", "metadata": {"id": "A_Q6wbhaHCFY"}, "source": ["## Setup a Retriever"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Yb4whROY5dC6", "outputId": "3e8b9188-d4e8-4a32-a047-dc25a329658c"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='watering holes that offer great ambience and company,\\n\\nSingapore has lots for you to experience.Link: https://www.visitsingapore.com/content/desktop/en/en\\n\\ndeals/singaporewards\\n\\nTitle: Explore the hidden gem tours with SingapoRewards\\n\\nWhat’s a better way to holiday?\\n\\nExploring the city’s best kept secrets with SingapoRewards'),\n", " Document(page_content=\"We'll see you soon!Link: https://www.visitsingapore.com/content/desktop/en/editorials/amazing\\n\\nthings\\n\\nyou\\n\\nnever\\n\\nknew\\n\\nabout\\n\\nsingapore\\n\\nTitle: 10 fun facts about singapore\\n\\nFor those who crave novelty, travel is all about new perspectives and new ways to experience the world. Beyond Singapore’s picture-perfect skyline, our bustling metropolis is an ever-evolving wonderland of bold new experiences and unforgettable adventures.\"),\n", " Document(page_content='questions/\\n\\nTitle: Frequently Asked Questions on MICE in Singapore – Visit Singapore Official\\n\\nFrom tourist attractions to key transit locations, you can select the best locations from over 100 spaces to reach your target audience.\\n\\nPlease contact us if you are allocated these locations for your event.\\n\\nTypes of lamp posts'),\n", " Document(page_content='The Ritz Carlton, Millenia Singapore. Meanwhile, ATxEnterprise and ATxImpact, ATxSG’s segments for industry and start-ups respectively, were organised by Informa Tech at Singapore EXPO.Link: https://www.visitsingapore.com/mice/en/why')]"]}, "metadata": {}, "execution_count": 35}], "source": ["retriever = db.as_retriever() # can add mmr fetch_k=20, search_type=\"mmr\"\n", "\n", "retriever.get_relevant_documents(query)"]}, {"cell_type": "markdown", "metadata": {"id": "2Im-BIOGFuOY"}, "source": ["## Chat chain"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"id": "Xg3Q51MKNTY0"}, "outputs": [], "source": ["from operator import itemgetter\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain.schema.output_parser import StrOutputParser\n", "from langchain.schema.runnable import RunnableLambda, RunnablePassthrough"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"id": "5PzPuNnuWTmH"}, "outputs": [], "source": ["template = \"\"\"Answer the question based only on the following context:\n", "{context}\n", "\n", "Question: {question}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 38, "metadata": {"id": "eBxQ074NWVeO"}, "outputs": [], "source": ["prompt = ChatPromptTemplate.from_template(template)"]}, {"cell_type": "code", "source": ["prompt"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "F7AlJ1MvlmFd", "outputId": "b7cb79e3-1058-4425-e9a0-0f625823324a"}, "execution_count": 40, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["ChatPromptTemplate(input_variables=['context', 'question'], messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['context', 'question'], template='Answer the question based only on the following context:\\n{context}\\n\\nQuestion: {question}\\n'))])"]}, "metadata": {}, "execution_count": 40}]}, {"cell_type": "code", "execution_count": 39, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "w5W4odwSFvZy", "outputId": "2d47f7f1-3df7-416c-b6fd-5a9583301288"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["ChatGoogleGenerativeAI(model='models/gemini-1.5-pro', client=<google.ai.generativelanguage_v1beta.services.generative_service.client.GenerativeServiceClient object at 0x7b15fe15e5c0>, async_client=<google.ai.generativelanguage_v1beta.services.generative_service.async_client.GenerativeServiceAsyncClient object at 0x7b15fdfcd780>, default_metadata=())"]}, "metadata": {}, "execution_count": 39}], "source": ["llm"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"id": "cYxeU1OeHZcB"}, "outputs": [], "source": ["chain = (\n", "    {\"context\": retriever, \"question\": RunnablePassthrough()}\n", "    | prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6aHFCXHrHgpI", "outputId": "2919309d-ddaf-4c79-d3bb-2cf73425c363"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["This document does not contain the answer to your question about Universal Studio\n", "Singapore. This document focuses on shopping, dining, and tourism in Singapore.\n", "\n"]}], "source": ["text_reply = chain.invoke(\"Tell me about Universal Studio Singapore\")\n", "\n", "print(wrap_text(text_reply))"]}, {"cell_type": "markdown", "metadata": {"id": "6FRIkxHGb9Dy"}, "source": ["## With RagFusion"]}, {"cell_type": "code", "execution_count": 43, "metadata": {"id": "sbVhVYwXb_X5"}, "outputs": [], "source": ["from langchain.schema.output_parser import StrOutputParser\n", "from langchain.prompts import SystemMessagePromptTemplate, HumanMessagePromptTemplate\n", "from langchain.prompts import ChatMessagePromptTemplate, PromptTemplate"]}, {"cell_type": "code", "execution_count": 44, "metadata": {"id": "8uGvc1E2cN7S"}, "outputs": [], "source": ["prompt = ChatPromptTemplate(input_variables=['original_query'],\n", "                            messages=[SystemMessagePromptTemplate(prompt=PromptTemplate(input_variables=[],template='You are a helpful assistant that generates multiple search queries based on a single input query.')),\n", "                            HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['original_query'], template='Generate multiple search queries related to: {question} \\n OUTPUT (4 queries):'))])"]}, {"cell_type": "code", "execution_count": 45, "metadata": {"id": "ZqlMkWance2k"}, "outputs": [], "source": ["original_query = \"universal studios Singapore\""]}, {"cell_type": "code", "execution_count": 46, "metadata": {"id": "Q71Fs_sFcXOO"}, "outputs": [], "source": ["generate_queries = (\n", "    prompt | llm | StrOutputParser() | (lambda x: x.split(\"\\n\"))\n", ")"]}, {"cell_type": "code", "execution_count": 47, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_EBSsEO2YSxn", "outputId": "19bafec4-55b5-43d0-b720-26f57b8aae75"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["ChatPromptTemplate(input_variables=['question'], messages=[SystemMessagePromptTemplate(prompt=PromptTemplate(input_variables=[], template='You are a helpful assistant that generates multiple search queries based on a single input query.')), HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['question'], template='Generate multiple search queries related to: {question} \\n OUTPUT (4 queries):'))])\n", "| ChatGoogleGenerativeAI(model='models/gemini-1.5-pro', client=<google.ai.generativelanguage_v1beta.services.generative_service.client.GenerativeServiceClient object at 0x7b15fe15e5c0>, async_client=<google.ai.generativelanguage_v1beta.services.generative_service.async_client.GenerativeServiceAsyncClient object at 0x7b15fdfcd780>, default_metadata=())\n", "| StrOutputParser()\n", "| RunnableLambda(lambda x: x.split('\\n'))"]}, "metadata": {}, "execution_count": 47}], "source": ["generate_queries"]}, {"cell_type": "code", "execution_count": 48, "metadata": {"id": "wxv9EXxpczgA"}, "outputs": [], "source": ["from langchain.load import dumps, loads\n", "\n", "\n", "def reciprocal_rank_fusion(results: list[list], k=60):\n", "    fused_scores = {}\n", "    for docs in results:\n", "        # Assumes the docs are returned in sorted order of relevance\n", "        for rank, doc in enumerate(docs):\n", "            doc_str = dumps(doc)\n", "            if doc_str not in fused_scores:\n", "                fused_scores[doc_str] = 0\n", "            previous_score = fused_scores[doc_str]\n", "            fused_scores[doc_str] += 1 / (rank + k)\n", "\n", "    reranked_results = [\n", "        (loads(doc), score)\n", "        for doc, score in sorted(fused_scores.items(), key=lambda x: x[1], reverse=True)\n", "    ]\n", "    return reranked_results"]}, {"cell_type": "code", "execution_count": 49, "metadata": {"id": "C1TYLuNNc1Mz"}, "outputs": [], "source": ["ragfusion_chain = generate_queries | retriever.map() | reciprocal_rank_fusion"]}, {"cell_type": "code", "execution_count": 50, "metadata": {"id": "_PPYsBf2g8TR"}, "outputs": [], "source": ["langchain.debug = True"]}, {"cell_type": "code", "execution_count": 51, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UZbsNg7EhfbP", "outputId": "dbd7a873-1ee2-4268-c32a-0799fb5d8c07"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'title': 'PromptInput',\n", " 'type': 'object',\n", " 'properties': {'question': {'title': 'Question', 'type': 'string'}}}"]}, "metadata": {}, "execution_count": 51}], "source": ["ragfusion_chain.input_schema.schema()"]}, {"cell_type": "code", "execution_count": 52, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tsQWIOjVc5gt", "outputId": "0453b304-3f24-4011-ab4f-f97eefb8c59b"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:RunnableSequence] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"question\": \"universal studios Singapore\"\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:RunnableSequence > prompt:ChatPromptTemplate] Entering Prompt run with input:\n", "\u001b[0m{\n", "  \"question\": \"universal studios Singapore\"\n", "}\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:RunnableSequence > prompt:ChatPromptTemplate] [2ms] Exiting Prompt run with output:\n", "\u001b[0m[outputs]\n", "\u001b[32;1m\u001b[1;3m[llm/start]\u001b[0m \u001b[1m[chain:RunnableSequence > llm:ChatGoogleGenerativeAI] Entering LLM run with input:\n", "\u001b[0m{\n", "  \"prompts\": [\n", "    \"System: You are a helpful assistant that generates multiple search queries based on a single input query.\\nHuman: Generate multiple search queries related to: universal studios Singapore \\n OUTPUT (4 queries):\"\n", "  ]\n", "}\n", "\u001b[36;1m\u001b[1;3m[llm/end]\u001b[0m \u001b[1m[chain:RunnableSequence > llm:ChatGoogleGenerativeAI] [1.93s] Exiting LLM run with output:\n", "\u001b[0m{\n", "  \"generations\": [\n", "    [\n", "      {\n", "        \"text\": \"Here are 4 search queries related to \\\"universal studios singapore\\\":\\n\\n1. **Universal Studios Singapore ticket prices and discounts**\\n2. **Best time to visit Universal Studios Singapore to avoid crowds**\\n3. **Universal Studios Singapore rides height requirements**\\n4. **Hotels near Universal Studios Singapore with shuttle service** \\n\",\n", "        \"generation_info\": {\n", "          \"finish_reason\": \"STOP\",\n", "          \"safety_ratings\": [\n", "            {\n", "              \"category\": \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n", "              \"probability\": \"NEGLIGIBLE\",\n", "              \"blocked\": false\n", "            },\n", "            {\n", "              \"category\": \"HARM_CATEGORY_HATE_SPEECH\",\n", "              \"probability\": \"NEGLIGIBLE\",\n", "              \"blocked\": false\n", "            },\n", "            {\n", "              \"category\": \"HARM_CATEGORY_HARASSMENT\",\n", "              \"probability\": \"NEGLIGIBLE\",\n", "              \"blocked\": false\n", "            },\n", "            {\n", "              \"category\": \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n", "              \"probability\": \"NEGLIGIBLE\",\n", "              \"blocked\": false\n", "            }\n", "          ]\n", "        },\n", "        \"type\": \"ChatGeneration\",\n", "        \"message\": {\n", "          \"lc\": 1,\n", "          \"type\": \"constructor\",\n", "          \"id\": [\n", "            \"langchain\",\n", "            \"schema\",\n", "            \"messages\",\n", "            \"AIMessage\"\n", "          ],\n", "          \"kwargs\": {\n", "            \"content\": \"Here are 4 search queries related to \\\"universal studios singapore\\\":\\n\\n1. **Universal Studios Singapore ticket prices and discounts**\\n2. **Best time to visit Universal Studios Singapore to avoid crowds**\\n3. **Universal Studios Singapore rides height requirements**\\n4. **Hotels near Universal Studios Singapore with shuttle service** \\n\",\n", "            \"response_metadata\": {\n", "              \"prompt_feedback\": {\n", "                \"block_reason\": 0,\n", "                \"safety_ratings\": []\n", "              },\n", "              \"finish_reason\": \"STOP\",\n", "              \"safety_ratings\": [\n", "                {\n", "                  \"category\": \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n", "                  \"probability\": \"NEGLIGIBLE\",\n", "                  \"blocked\": false\n", "                },\n", "                {\n", "                  \"category\": \"HARM_CATEGORY_HATE_SPEECH\",\n", "                  \"probability\": \"NEGLIGIBLE\",\n", "                  \"blocked\": false\n", "                },\n", "                {\n", "                  \"category\": \"HARM_CATEGORY_HARASSMENT\",\n", "                  \"probability\": \"NEGLIGIBLE\",\n", "                  \"blocked\": false\n", "                },\n", "                {\n", "                  \"category\": \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n", "                  \"probability\": \"NEGLIGIBLE\",\n", "                  \"blocked\": false\n", "                }\n", "              ]\n", "            },\n", "            \"type\": \"ai\",\n", "            \"id\": \"run-ce19646d-5fc0-42a3-aded-bdc7c00cf344-0\",\n", "            \"usage_metadata\": {\n", "              \"input_tokens\": 35,\n", "              \"output_tokens\": 64,\n", "              \"total_tokens\": 99\n", "            },\n", "            \"tool_calls\": [],\n", "            \"invalid_tool_calls\": []\n", "          }\n", "        }\n", "      }\n", "    ]\n", "  ],\n", "  \"llm_output\": {\n", "    \"prompt_feedback\": {\n", "      \"block_reason\": 0,\n", "      \"safety_ratings\": []\n", "    }\n", "  },\n", "  \"run\": null\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:RunnableSequence > parser:StrOutputParser] Entering Parser run with input:\n", "\u001b[0m[inputs]\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:RunnableSequence > parser:StrOutputParser] [1ms] Exiting Parser run with output:\n", "\u001b[0m{\n", "  \"output\": \"Here are 4 search queries related to \\\"universal studios singapore\\\":\\n\\n1. **Universal Studios Singapore ticket prices and discounts**\\n2. **Best time to visit Universal Studios Singapore to avoid crowds**\\n3. **Universal Studios Singapore rides height requirements**\\n4. **Hotels near Universal Studios Singapore with shuttle service** \\n\"\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableLambda] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"input\": \"Here are 4 search queries related to \\\"universal studios singapore\\\":\\n\\n1. **Universal Studios Singapore ticket prices and discounts**\\n2. **Best time to visit Universal Studios Singapore to avoid crowds**\\n3. **Universal Studios Singapore rides height requirements**\\n4. **Hotels near Universal Studios Singapore with shuttle service** \\n\"\n", "}\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableLambda] [1ms] Exiting Chain run with output:\n", "\u001b[0m{\n", "  \"output\": [\n", "    \"Here are 4 search queries related to \\\"universal studios singapore\\\":\",\n", "    \"\",\n", "    \"1. **Universal Studios Singapore ticket prices and discounts**\",\n", "    \"2. **Best time to visit Universal Studios Singapore to avoid crowds**\",\n", "    \"3. **Universal Studios Singapore rides height requirements**\",\n", "    \"4. **Hotels near Universal Studios Singapore with shuttle service** \",\n", "    \"\"\n", "  ]\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableEach<VectorStoreRetriever>] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"input\": [\n", "    \"Here are 4 search queries related to \\\"universal studios singapore\\\":\",\n", "    \"\",\n", "    \"1. **Universal Studios Singapore ticket prices and discounts**\",\n", "    \"2. **Best time to visit Universal Studios Singapore to avoid crowds**\",\n", "    \"3. **Universal Studios Singapore rides height requirements**\",\n", "    \"4. **Hotels near Universal Studios Singapore with shuttle service** \",\n", "    \"\"\n", "  ]\n", "}\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableEach<VectorStoreRetriever>] [432ms] Exiting Chain run with output:\n", "\u001b[0m[outputs]\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:reciprocal_rank_fusion] Entering Chain run with input:\n", "\u001b[0m[inputs]\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:reciprocal_rank_fusion] [40ms] Exiting Chain run with output:\n", "\u001b[0m[outputs]\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:RunnableSequence] [2.42s] Exiting Chain run with output:\n", "\u001b[0m[outputs]\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/langchain_core/_api/beta_decorator.py:87: LangChainBetaWarning: The function `loads` is in beta. It is actively being worked on, so the API may change.\n", "  warn_beta(\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["[(Document(page_content='questions/\\n\\nTitle: Frequently Asked Questions on MICE in Singapore – Visit Singapore Official\\n\\nFrom tourist attractions to key transit locations, you can select the best locations from over 100 spaces to reach your target audience.\\n\\nPlease contact us if you are allocated these locations for your event.\\n\\nTypes of lamp posts'),\n", "  0.09704699789311107),\n", " (Document(page_content='watering holes that offer great ambience and company,\\n\\nSingapore has lots for you to experience.Link: https://www.visitsingapore.com/content/desktop/en/en\\n\\ndeals/singaporewards\\n\\nTitle: Explore the hidden gem tours with SingapoRewards\\n\\nWhat’s a better way to holiday?\\n\\nExploring the city’s best kept secrets with SingapoRewards'),\n", "  0.06558258417063283),\n", " (Document(page_content='Title: SingapoRewards\\n\\nシンガポールを探検しませんか？\\n\\n無料で！\\n\\n無料のアクティビティ券を利用して、シンガポール旅行をもっと楽しみましょう。絶景サイクリングから爽快なカヤックでのアドベンチャーまで、ユニークなアクティビティの数々を厳選しました。\\n\\nシンガポール・リワードを使って、旅行者1名様につき1つの無料アクティビティをお選びいただけます。シンガポール行きのフライトを予約したら、対象リストからお好きなアクティビティを選んでシンガポール・リワードを使いましょう。\\n\\nでは、シンガポールでお会いしましょう！Link: https://www.visitsingapore.com/mice/vi_vn/\\n\\nTitle: MICE Singapore – Website chính thức Visit Singapore\\n\\nCác bên bán hàng và Nhà cung cấp'),\n", "  0.04972677595628415),\n", " (Document(page_content='up the sun and enjoy the surf, make a beeline for Palawan or Tanjong Beach. If action is what you crave instead, you’ll want to brave the rollercoaster rides at Universal Studios Singapore or the heart-pumping obstacles and ziplines at Skypark Sentosa by <PERSON>.'),\n", "  0.04972677595628415),\n", " (Document(page_content='Dies wurde durch unsere unstillbare Leidenschaft sowie unserer unerschütterlichen Haltung, ni<PERSON><PERSON> aufzugeben, erreicht – was uns noch heute immer neue Hürden zu neuen Höhen erklimmen lässt. Passion Made Possible ist der Unternehmergeist, der uns heute definiert und uns beim Erschließen neuer Möglichkeiten antreibt.\\n\\nWo das Gewöhnliche außergewöhnlich wird'),\n", "  0.03225806451612903),\n", " (Document(page_content='Title: MICE Singapore – Website chính thức Visit Singapore\\n\\nCác bên bán hàng và Nhà cung cấp\\n\\nCác chuyên gia trong nước của chúng tôi luôn sẵn sàng hỗ trợ bạn trong quá trình tổ chức sự kiện.\\n\\nXEM TẤT CẢ CÁC BÊN BÁN HÀNG VÀ NHÀ CUNG CẤPLink: https://www.visitsingapore.com/th_th/deals/\\n\\nTitle: DealsLink: https://www.visitsingapore.com/th_th/see\\n\\ndo\\n\\nsingapore/socialiser/\\n\\nTitle: SocialisersLink: https://www.visitsingapore.com/madewithpassion/\\n\\nTitle: Discover Singaporean Brands'),\n", "  0.031746031746031744),\n", " (Document(page_content='threads\\n\\nversus\\n\\nmodern\\n\\nfashion\\n\\nin\\n\\nsingapore.html\\n\\nTitle: Mode Singapura—Modern vs warisan budaya\\n\\nSeperti kota kami, kancah mode Singapura adalah perpaduan eklektik antara yang baru dan yang lama, dengan pengaruh dari berbagai wilayah.\\n\\nSusuri jalanan dan pusat belanja di sini, dan temukan beragam gerai yang menjual busana kuno dan penafsiran modern dari desain klasik.'),\n", "  0.031746031746031744),\n", " (Document(page_content='2. Adult Stored-Value Smartcard (EZ-Link / NETS FlashPay): These cost $10 and come with a stored value of $5 for you to use on your commutes. You’ll be able to purchase these at Passenger Service Centres in train stations, any SimplyGo Ticket Office or convenience stores such as 7-11, Buzz and Cheers. 3. You can use your foreign-issued Mastercard® and Visa contactless bank cards2, as well as your mobile wallets for the payment of public transport fares in Singapore. No registration is required.'),\n", "  0.016666666666666666),\n", " (Document(page_content='Singapore’s nightlife scene has exploded into a dizzying whirl of music, drink and entertainment that caters to both party animals and those seeking a quieter night out – all around the clock. Dance to the beats of world-class DJs at a megaclub, enjoy a bespoke tipple at a discreet cocktail bar or check out a live band before having a big laugh at a comedy club. Crank up the volume or dial it down to a more comfortable pace. The choices are limited only by your imagination, and how long you'),\n", "  0.016666666666666666),\n", " (Document(page_content='mobile wallets for the payment of public transport fares in Singapore. No registration is required. 2Admin fees apply for foreign-issued bank cards. 4. Children above 0.9m in height and below 7 years old can apply for a Child Concession Card at SimplyGo Ticket Office to travel for free on basic bus and train services. Children below 0.9m in height accompanied by a fare-paying commuter can automatically travel for free. For more information on Child Concession Cards, please visit SimplyGo'),\n", "  0.*****************),\n", " (Document(page_content='Das Museum of Ice Cream Singapore öffnet im August 2021. Tickets können Sie hier buchen!Link: https://www.visitsingapore.com/content/desktop/de_de/travel\\n\\nguide\\n\\ntips/getting\\n\\naround/\\n\\nTitle: Fortbewegung in Singapur – Visit Singapore Offizielle Website\\n\\nUnterwegs mit dem Zug\\n\\nAm schnellsten kommen Sie in der Stadt wahrscheinlich mit der U-Bahn (Mass Rapid Transit, MRT) von A nach B. Viele der beliebtesten Sehenswürdigkeiten befinden sich nur einen kurzen Fußweg von einer MRT-Station entfernt.'),\n", "  0.016129032258064516),\n", " (Document(page_content='tropical highlands. No trip to Singapore is complete without a visit to the world’s tallest indoor waterfall. Housed in the retail and lifestyle complex of Jewel Changi Airport, the HSBC Rain Vortex soars at 40 metres, and is surrounded by a lush indoor garden.'),\n", "  0.015873015873015872),\n", " (Document(page_content='yang digelar di negeri ini. Lihat festival dan acara\\n\\nunggulan di Singapura dari Januari hingga Desember.Link: https://www.visitsingapore.com/id_id/singapore\\n\\nhotels/\\n\\nTitle: Tinggal\\n\\nSingapore Tourism Awards'),\n", "  0.015873015873015872)]"]}, "metadata": {}, "execution_count": 52}], "source": ["ragfusion_chain.invoke({\"question\": original_query})"]}, {"cell_type": "code", "execution_count": 53, "metadata": {"id": "8_YX1u6lc7nB"}, "outputs": [], "source": ["from langchain.schema.runnable import RunnablePassthrough\n", "template = \"\"\"Answer the question based only on the following context:\n", "{context}\n", "\n", "Question: {question}\n", "\"\"\"\n", "prompt = ChatPromptTemplate.from_template(template)\n", "\n", "full_rag_fusion_chain = (\n", "    {\n", "        \"context\": ragfusion_chain,\n", "        \"question\": RunnablePassthrough()\n", "    }\n", "    | prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")"]}, {"cell_type": "code", "execution_count": 54, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9dFNAi7vhool", "outputId": "a0499a0f-62fb-4c28-c501-07f68a9867ef"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'title': '<PERSON>nableParallel<context,question>Input',\n", " 'type': 'object',\n", " 'properties': {'question': {'title': 'Question', 'type': 'string'}}}"]}, "metadata": {}, "execution_count": 54}], "source": ["full_rag_fusion_chain.input_schema.schema()"]}, {"cell_type": "code", "execution_count": 57, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "04iV9SY0fAz0", "outputId": "3b6e490c-36b6-4ec8-feab-033ef53e86c3"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:RunnableSequence] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"question\": \"Tell me about Singapore’s nightlife scene?\"\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question>] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"question\": \"Tell me about Singapore’s nightlife scene?\"\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question> > chain:RunnablePassthrough] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"question\": \"Tell me about Singapore’s nightlife scene?\"\n", "}\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question> > chain:RunnablePassthrough] [1ms] Exiting Chain run with output:\n", "\u001b[0m{\n", "  \"question\": \"Tell me about Singapore’s nightlife scene?\"\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question> > chain:RunnableSequence] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"question\": \"Tell me about Singapore’s nightlife scene?\"\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question> > chain:RunnableSequence > prompt:ChatPromptTemplate] Entering Prompt run with input:\n", "\u001b[0m{\n", "  \"question\": \"Tell me about Singapore’s nightlife scene?\"\n", "}\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question> > chain:RunnableSequence > prompt:ChatPromptTemplate] [2ms] Exiting Prompt run with output:\n", "\u001b[0m[outputs]\n", "\u001b[32;1m\u001b[1;3m[llm/start]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question> > chain:RunnableSequence > llm:ChatGoogleGenerativeAI] Entering LLM run with input:\n", "\u001b[0m{\n", "  \"prompts\": [\n", "    \"System: You are a helpful assistant that generates multiple search queries based on a single input query.\\nHuman: Generate multiple search queries related to: Tell me about Singapore’s nightlife scene? \\n OUTPUT (4 queries):\"\n", "  ]\n", "}\n", "\u001b[36;1m\u001b[1;3m[llm/end]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question> > chain:RunnableSequence > llm:ChatGoogleGenerativeAI] [2.08s] Exiting LLM run with output:\n", "\u001b[0m{\n", "  \"generations\": [\n", "    [\n", "      {\n", "        \"text\": \"Here are four search queries related to \\\"Tell me about Singapore's nightlife scene?\\\":\\n\\n1. **Singapore nightlife guide: Best bars, clubs, and late-night activities**\\n2. **What are the different areas in Singapore known for nightlife?**\\n3. **Is Singapore nightlife expensive? Average costs and budget tips**\\n4. **Unique and unusual nightlife experiences in Singapore** \\n\",\n", "        \"generation_info\": {\n", "          \"finish_reason\": \"STOP\",\n", "          \"safety_ratings\": [\n", "            {\n", "              \"category\": \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n", "              \"probability\": \"NEGLIGIBLE\",\n", "              \"blocked\": false\n", "            },\n", "            {\n", "              \"category\": \"HARM_CATEGORY_HATE_SPEECH\",\n", "              \"probability\": \"NEGLIGIBLE\",\n", "              \"blocked\": false\n", "            },\n", "            {\n", "              \"category\": \"HARM_CATEGORY_HARASSMENT\",\n", "              \"probability\": \"NEGLIGIBLE\",\n", "              \"blocked\": false\n", "            },\n", "            {\n", "              \"category\": \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n", "              \"probability\": \"NEGLIGIBLE\",\n", "              \"blocked\": false\n", "            }\n", "          ]\n", "        },\n", "        \"type\": \"ChatGeneration\",\n", "        \"message\": {\n", "          \"lc\": 1,\n", "          \"type\": \"constructor\",\n", "          \"id\": [\n", "            \"langchain\",\n", "            \"schema\",\n", "            \"messages\",\n", "            \"AIMessage\"\n", "          ],\n", "          \"kwargs\": {\n", "            \"content\": \"Here are four search queries related to \\\"Tell me about Singapore's nightlife scene?\\\":\\n\\n1. **Singapore nightlife guide: Best bars, clubs, and late-night activities**\\n2. **What are the different areas in Singapore known for nightlife?**\\n3. **Is Singapore nightlife expensive? Average costs and budget tips**\\n4. **Unique and unusual nightlife experiences in Singapore** \\n\",\n", "            \"response_metadata\": {\n", "              \"prompt_feedback\": {\n", "                \"block_reason\": 0,\n", "                \"safety_ratings\": []\n", "              },\n", "              \"finish_reason\": \"STOP\",\n", "              \"safety_ratings\": [\n", "                {\n", "                  \"category\": \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n", "                  \"probability\": \"NEGLIGIBLE\",\n", "                  \"blocked\": false\n", "                },\n", "                {\n", "                  \"category\": \"HARM_CATEGORY_HATE_SPEECH\",\n", "                  \"probability\": \"NEGLIGIBLE\",\n", "                  \"blocked\": false\n", "                },\n", "                {\n", "                  \"category\": \"HARM_CATEGORY_HARASSMENT\",\n", "                  \"probability\": \"NEGLIGIBLE\",\n", "                  \"blocked\": false\n", "                },\n", "                {\n", "                  \"category\": \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n", "                  \"probability\": \"NEGLIGIBLE\",\n", "                  \"blocked\": false\n", "                }\n", "              ]\n", "            },\n", "            \"type\": \"ai\",\n", "            \"id\": \"run-102f3f69-6aaa-48bc-8e0e-e7e8b0d26f7d-0\",\n", "            \"usage_metadata\": {\n", "              \"input_tokens\": 41,\n", "              \"output_tokens\": 80,\n", "              \"total_tokens\": 121\n", "            },\n", "            \"tool_calls\": [],\n", "            \"invalid_tool_calls\": []\n", "          }\n", "        }\n", "      }\n", "    ]\n", "  ],\n", "  \"llm_output\": {\n", "    \"prompt_feedback\": {\n", "      \"block_reason\": 0,\n", "      \"safety_ratings\": []\n", "    }\n", "  },\n", "  \"run\": null\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question> > chain:RunnableSequence > parser:StrOutputParser] Entering Parser run with input:\n", "\u001b[0m[inputs]\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question> > chain:RunnableSequence > parser:StrOutputParser] [1ms] Exiting Parser run with output:\n", "\u001b[0m{\n", "  \"output\": \"Here are four search queries related to \\\"Tell me about Singapore's nightlife scene?\\\":\\n\\n1. **Singapore nightlife guide: Best bars, clubs, and late-night activities**\\n2. **What are the different areas in Singapore known for nightlife?**\\n3. **Is Singapore nightlife expensive? Average costs and budget tips**\\n4. **Unique and unusual nightlife experiences in Singapore** \\n\"\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question> > chain:RunnableSequence > chain:RunnableLambda] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"input\": \"Here are four search queries related to \\\"Tell me about Singapore's nightlife scene?\\\":\\n\\n1. **Singapore nightlife guide: Best bars, clubs, and late-night activities**\\n2. **What are the different areas in Singapore known for nightlife?**\\n3. **Is Singapore nightlife expensive? Average costs and budget tips**\\n4. **Unique and unusual nightlife experiences in Singapore** \\n\"\n", "}\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question> > chain:RunnableSequence > chain:RunnableLambda] [1ms] Exiting Chain run with output:\n", "\u001b[0m{\n", "  \"output\": [\n", "    \"Here are four search queries related to \\\"Tell me about Singapore's nightlife scene?\\\":\",\n", "    \"\",\n", "    \"1. **Singapore nightlife guide: Best bars, clubs, and late-night activities**\",\n", "    \"2. **What are the different areas in Singapore known for nightlife?**\",\n", "    \"3. **Is Singapore nightlife expensive? Average costs and budget tips**\",\n", "    \"4. **Unique and unusual nightlife experiences in Singapore** \",\n", "    \"\"\n", "  ]\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question> > chain:RunnableSequence > chain:RunnableEach<VectorStoreRetriever>] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"input\": [\n", "    \"Here are four search queries related to \\\"Tell me about Singapore's nightlife scene?\\\":\",\n", "    \"\",\n", "    \"1. **Singapore nightlife guide: Best bars, clubs, and late-night activities**\",\n", "    \"2. **What are the different areas in Singapore known for nightlife?**\",\n", "    \"3. **Is Singapore nightlife expensive? Average costs and budget tips**\",\n", "    \"4. **Unique and unusual nightlife experiences in Singapore** \",\n", "    \"\"\n", "  ]\n", "}\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question> > chain:RunnableSequence > chain:RunnableEach<VectorStoreRetriever>] [393ms] Exiting Chain run with output:\n", "\u001b[0m[outputs]\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question> > chain:RunnableSequence > chain:reciprocal_rank_fusion] Entering Chain run with input:\n", "\u001b[0m[inputs]\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question> > chain:RunnableSequence > chain:reciprocal_rank_fusion] [4ms] Exiting Chain run with output:\n", "\u001b[0m[outputs]\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question> > chain:RunnableSequence] [2.49s] Exiting Chain run with output:\n", "\u001b[0m[outputs]\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:RunnableSequence > chain:RunnableParallel<context,question>] [2.51s] Exiting Chain run with output:\n", "\u001b[0m[outputs]\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:RunnableSequence > prompt:ChatPromptTemplate] Entering Prompt run with input:\n", "\u001b[0m[inputs]\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:RunnableSequence > prompt:ChatPromptTemplate] [1ms] Exiting Prompt run with output:\n", "\u001b[0m[outputs]\n", "\u001b[32;1m\u001b[1;3m[llm/start]\u001b[0m \u001b[1m[chain:RunnableSequence > llm:ChatGoogleGenerativeAI] Entering LLM run with input:\n", "\u001b[0m{\n", "  \"prompts\": [\n", "    \"Human: Answer the question based only on the following context:\\n[(Document(page_content='Singapore’s nightlife scene has exploded into a dizzying whirl of music, drink and entertainment that caters to both party animals and those seeking a quieter night out – all around the clock. Dance to the beats of world-class DJs at a megaclub, enjoy a bespoke tipple at a discreet cocktail bar or check out a live band before having a big laugh at a comedy club. Crank up the volume or dial it down to a more comfortable pace. The choices are limited only by your imagination, and how long you'), 0.08333333333333333), (Document(page_content='Link: https://www.visitsingapore.com/see\\\\n\\\\ndo\\\\n\\\\nsingapore/nightlife/\\\\n\\\\nTitle: NightlifeLink: https://www.visitsingapore.com/festivals\\\\n\\\\nevents\\\\n\\\\nsingapore/\\\\n\\\\nTitle: Festivals & Events\\\\n\\\\nFestivals\\\\n\\\\n& Events\\\\n\\\\nWhether it’s the festive celebration of Chinese New Year\\\\n\\\\nor the roar of Formula 1 cars, there’s always a party going on\\\\n\\\\nsomewhere on the island. Check out top festivals and events\\\\n\\\\nin Singapore from January to December.Link: https://www.visitsingapore.com/dining\\\\n\\\\ndrinks\\\\n\\\\nsingapore/local\\\\n\\\\ndishes/'), 0.08118237599993285), (Document(page_content='A vital stop for any club goer has to be Zouk, which has set the pace for the nightlife scene in Singapore and the region for almost 25 years. One of the most famous clubs in the world, Zouk is acknowledged as one of the best by critics and clubbers alike.\\\\n\\\\nStunning views\\\\n\\\\nFor those who wish to enjoy their drinks while admiring Singapore’s stunning skyline, there are plenty of rooftop establishments to visit.'), 0.04839549075403121), (Document(page_content='watering holes that offer great ambience and company,\\\\n\\\\nSingapore has lots for you to experience.Link: https://www.visitsingapore.com/content/desktop/en/en\\\\n\\\\ndeals/singaporewards\\\\n\\\\nTitle: Explore the hidden gem tours with SingapoRewards\\\\n\\\\nWhat’s a better way to holiday?\\\\n\\\\nExploring the city’s best kept secrets with SingapoRewards'), 0.048131080389144903), (Document(page_content='Title: SingapoRewards\\\\n\\\\nシンガポールを探検しませんか？\\\\n\\\\n無料で！\\\\n\\\\n無料のアクティビティ券を利用して、シンガポール旅行をもっと楽しみましょう。絶景サイクリングから爽快なカヤックでのアドベンチャーまで、ユニークなアクティビティの数々を厳選しました。\\\\n\\\\nシンガポール・リワードを使って、旅行者1名様につき1つの無料アクティビティをお選びいただけます。シンガポール行きのフライトを予約したら、対象リストからお好きなアクティビティを選んでシンガポール・リワードを使いましょう。\\\\n\\\\nでは、シンガポールでお会いしましょう！Link: https://www.visitsingapore.com/mice/vi_vn/\\\\n\\\\nTitle: MICE Singapore – Website chính thức Visit Singapore\\\\n\\\\nCác bên bán hàng và Nhà cung cấp'), 0.03333333333333333), (Document(page_content='questions/\\\\n\\\\nTitle: Frequently Asked Questions on MICE in Singapore – Visit Singapore Official\\\\n\\\\nFrom tourist attractions to key transit locations, you can select the best locations from over 100 spaces to reach your target audience.\\\\n\\\\nPlease contact us if you are allocated these locations for your event.\\\\n\\\\nTypes of lamp posts'), 0.03278688524590164), (Document(page_content=\\\"We'll see you soon!Link: https://www.visitsingapore.com/content/desktop/en/editorials/amazing\\\\n\\\\nthings\\\\n\\\\nyou\\\\n\\\\nnever\\\\n\\\\nknew\\\\n\\\\nabout\\\\n\\\\nsingapore\\\\n\\\\nTitle: 10 fun facts about singapore\\\\n\\\\nFor those who crave novelty, travel is all about new perspectives and new ways to experience the world. Beyond Singapore’s picture-perfect skyline, our bustling metropolis is an ever-evolving wonderland of bold new experiences and unforgettable adventures.\\\"), 0.032266458495966696), (Document(page_content='Dies wurde durch unsere unstillbare Leidenschaft sowie unserer unerschütterlichen Haltung, niemals aufzugeben, erreicht – was uns noch heute immer neue Hürden zu neuen Höhen erklimmen lässt. Passion Made Possible ist der Unternehmergeist, der uns heute definiert und uns beim Erschließen neuer Möglichkeiten antreibt.\\\\n\\\\nWo das Gewöhnliche außergewöhnlich wird'), 0.03225806451612903), (Document(page_content='drinks\\\\n\\\\nsingapore/\\\\n\\\\nTitle: EAT & DRINK\\\\n\\\\nFun after sunset'), 0.03200204813108039), (Document(page_content='threads\\\\n\\\\nversus\\\\n\\\\nmodern\\\\n\\\\nfashion\\\\n\\\\nin\\\\n\\\\nsingapore.html\\\\n\\\\nTitle: Mode Singapura—Modern vs warisan budaya\\\\n\\\\nSeperti kota kami, kancah mode Singapura adalah perpaduan eklektik antara yang baru dan yang lama, dengan pengaruh dari berbagai wilayah.\\\\n\\\\nSusuri jalanan dan pusat belanja di sini, dan temukan beragam gerai yang menjual busana kuno dan penafsiran modern dari desain klasik.'), 0.031746031746031744)]\\n\\nQuestion: {'question': 'Tell me about Singapore’s nightlife scene?'}\"\n", "  ]\n", "}\n", "\u001b[36;1m\u001b[1;3m[llm/end]\u001b[0m \u001b[1m[chain:RunnableSequence > llm:ChatGoogleGenerativeAI] [1.85s] Exiting LLM run with output:\n", "\u001b[0m{\n", "  \"generations\": [\n", "    [\n", "      {\n", "        \"text\": \"Singapore’s nightlife scene is incredibly diverse, offering a blend of high-energy clubs and more relaxed options for a night out. You can dance to music from world-renowned DJs at a megaclub, savor a unique drink at a low-key cocktail bar, or enjoy live music before laughing the night away at a comedy club. \\n\",\n", "        \"generation_info\": {\n", "          \"finish_reason\": \"STOP\",\n", "          \"safety_ratings\": [\n", "            {\n", "              \"category\": \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n", "              \"probability\": \"NEGLIGIBLE\",\n", "              \"blocked\": false\n", "            },\n", "            {\n", "              \"category\": \"HARM_CATEGORY_HATE_SPEECH\",\n", "              \"probability\": \"NEGLIGIBLE\",\n", "              \"blocked\": false\n", "            },\n", "            {\n", "              \"category\": \"HARM_CATEGORY_HARASSMENT\",\n", "              \"probability\": \"NEGLIGIBLE\",\n", "              \"blocked\": false\n", "            },\n", "            {\n", "              \"category\": \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n", "              \"probability\": \"NEGLIGIBLE\",\n", "              \"blocked\": false\n", "            }\n", "          ]\n", "        },\n", "        \"type\": \"ChatGeneration\",\n", "        \"message\": {\n", "          \"lc\": 1,\n", "          \"type\": \"constructor\",\n", "          \"id\": [\n", "            \"langchain\",\n", "            \"schema\",\n", "            \"messages\",\n", "            \"AIMessage\"\n", "          ],\n", "          \"kwargs\": {\n", "            \"content\": \"Singapore’s nightlife scene is incredibly diverse, offering a blend of high-energy clubs and more relaxed options for a night out. You can dance to music from world-renowned DJs at a megaclub, savor a unique drink at a low-key cocktail bar, or enjoy live music before laughing the night away at a comedy club. \\n\",\n", "            \"response_metadata\": {\n", "              \"prompt_feedback\": {\n", "                \"block_reason\": 0,\n", "                \"safety_ratings\": []\n", "              },\n", "              \"finish_reason\": \"STOP\",\n", "              \"safety_ratings\": [\n", "                {\n", "                  \"category\": \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n", "                  \"probability\": \"NEGLIGIBLE\",\n", "                  \"blocked\": false\n", "                },\n", "                {\n", "                  \"category\": \"HARM_CATEGORY_HATE_SPEECH\",\n", "                  \"probability\": \"NEGLIGIBLE\",\n", "                  \"blocked\": false\n", "                },\n", "                {\n", "                  \"category\": \"HARM_CATEGORY_HARASSMENT\",\n", "                  \"probability\": \"NEGLIGIBLE\",\n", "                  \"blocked\": false\n", "                },\n", "                {\n", "                  \"category\": \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n", "                  \"probability\": \"NEGLIGIBLE\",\n", "                  \"blocked\": false\n", "                }\n", "              ]\n", "            },\n", "            \"type\": \"ai\",\n", "            \"id\": \"run-e33fe10e-48e9-4f1b-8ae5-6bf5d1104c0f-0\",\n", "            \"usage_metadata\": {\n", "              \"input_tokens\": 1379,\n", "              \"output_tokens\": 68,\n", "              \"total_tokens\": 1447\n", "            },\n", "            \"tool_calls\": [],\n", "            \"invalid_tool_calls\": []\n", "          }\n", "        }\n", "      }\n", "    ]\n", "  ],\n", "  \"llm_output\": {\n", "    \"prompt_feedback\": {\n", "      \"block_reason\": 0,\n", "      \"safety_ratings\": []\n", "    }\n", "  },\n", "  \"run\": null\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:RunnableSequence > parser:StrOutputParser] Entering Parser run with input:\n", "\u001b[0m[inputs]\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:RunnableSequence > parser:StrOutputParser] [1ms] Exiting Parser run with output:\n", "\u001b[0m{\n", "  \"output\": \"Singapore’s nightlife scene is incredibly diverse, offering a blend of high-energy clubs and more relaxed options for a night out. You can dance to music from world-renowned DJs at a megaclub, savor a unique drink at a low-key cocktail bar, or enjoy live music before laughing the night away at a comedy club. \\n\"\n", "}\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:RunnableSequence] [4.39s] Exiting Chain run with output:\n", "\u001b[0m{\n", "  \"output\": \"Singapore’s nightlife scene is incredibly diverse, offering a blend of high-energy clubs and more relaxed options for a night out. You can dance to music from world-renowned DJs at a megaclub, savor a unique drink at a low-key cocktail bar, or enjoy live music before laughing the night away at a comedy club. \\n\"\n", "}\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'Singapore’s nightlife scene is incredibly diverse, offering a blend of high-energy clubs and more relaxed options for a night out. You can dance to music from world-renowned DJs at a megaclub, savor a unique drink at a low-key cocktail bar, or enjoy live music before laughing the night away at a comedy club. \\n'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 57}], "source": ["full_rag_fusion_chain.invoke({\"question\": \"Tell me about Singapore’s nightlife scene?\"})"]}, {"cell_type": "code", "source": ["Singapore’s nightlife scene is incredibly diverse, offering a blend of high-energy clubs and more relaxed options for a night out. You can dance to music from world-renowned DJs at a megaclub, savor a unique drink at a low-key cocktail bar, or enjoy live music before laughing the night away at a comedy club.\n"], "metadata": {"id": "GXJEjyjunk5E"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Singapore’s nightlife scene is incredibly diverse, offering a blend of high-energy clubs and more relaxed options for a night out. You can dance to music from world-renowned DJs at a megaclub, savor a unique drink at a low-key cocktail bar, or enjoy live music before laughing the night away at a comedy club.\n"], "metadata": {"id": "W3Yj7xLbocKQ"}}, {"cell_type": "code", "source": [], "metadata": {"id": "uoF_Tledoco6"}, "execution_count": null, "outputs": []}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": [], "include_colab_link": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"daf4b4a852744209907991a6c2966cb3": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_96f8ccaf07794391af50c4b46123ff0c", "IPY_MODEL_9ab8f8b6252445faa03312eb0a694675", "IPY_MODEL_9f4e436970aa48bf96afd75f4088ddf9"], "layout": "IPY_MODEL_368b2f9e73c24e1793b0893a203dcd9d"}}, "96f8ccaf07794391af50c4b46123ff0c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_26441ab08b3449348fd347ceb209490b", "placeholder": "​", "style": "IPY_MODEL_ff98934b96d54296a3fa72afd491b84d", "value": "modules.json: 100%"}}, "9ab8f8b6252445faa03312eb0a694675": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fbbf539537a64c3da3b3fedc95801c83", "max": 349, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_31596ec6290f48298332b33382f00766", "value": 349}}, "9f4e436970aa48bf96afd75f4088ddf9": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c2ce3ca4017340bd87cfa290456d5015", "placeholder": "​", "style": "IPY_MODEL_851619bd487f4606aaec560b36f23fa7", "value": " 349/349 [00:00&lt;00:00, 16.5kB/s]"}}, "368b2f9e73c24e1793b0893a203dcd9d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "26441ab08b3449348fd347ceb209490b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ff98934b96d54296a3fa72afd491b84d": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fbbf539537a64c3da3b3fedc95801c83": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "31596ec6290f48298332b33382f00766": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c2ce3ca4017340bd87cfa290456d5015": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "851619bd487f4606aaec560b36f23fa7": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b6be12de5cea495fb1eddd78c41f21d0": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0e76f659b48b4f819feda2b4243264de", "IPY_MODEL_154d7104c893421189ed0c9efabbe28f", "IPY_MODEL_405bc96f7d59488aac24f038635bd331"], "layout": "IPY_MODEL_7c96b7cd72464969b77d82e5383276a3"}}, "0e76f659b48b4f819feda2b4243264de": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fa9b5d5bef634bdc82b87c87c29f92d3", "placeholder": "​", "style": "IPY_MODEL_008e25861fcc458a90c04cc8c33b4367", "value": "config_sentence_transformers.json: 100%"}}, "154d7104c893421189ed0c9efabbe28f": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2f71a982cecc4fb8ad7617884dccd139", "max": 124, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_cd50d5d5175a46be9ed0d19a37e441c3", "value": 124}}, "405bc96f7d59488aac24f038635bd331": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c307f87abdc64c7b929c37e9b05f91ee", "placeholder": "​", "style": "IPY_MODEL_3d27fc8f14714ba49e3599315486578c", "value": " 124/124 [00:00&lt;00:00, 3.87kB/s]"}}, "7c96b7cd72464969b77d82e5383276a3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fa9b5d5bef634bdc82b87c87c29f92d3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "008e25861fcc458a90c04cc8c33b4367": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2f71a982cecc4fb8ad7617884dccd139": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cd50d5d5175a46be9ed0d19a37e441c3": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c307f87abdc64c7b929c37e9b05f91ee": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3d27fc8f14714ba49e3599315486578c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "48512d17ea054e068deec4eea4863dbf": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9fdbb8bb2b6143ddb6009fe6d9f0e472", "IPY_MODEL_f4230a23cb304a068efa66544a09f557", "IPY_MODEL_5d1cb00ceb7748789226fda1f58b0a4e"], "layout": "IPY_MODEL_fd7ea0696e6d4c0ea7661b4a91772324"}}, "9fdbb8bb2b6143ddb6009fe6d9f0e472": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bcd7d01133c24ba49f8691a734ffb8e6", "placeholder": "​", "style": "IPY_MODEL_9fa39ded11174699ac0fc2249c76b180", "value": "README.md: 100%"}}, "f4230a23cb304a068efa66544a09f557": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8dd7662de1c040dca74d7512bd576daa", "max": 94783, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9883747427aa41f184380248c7cc826b", "value": 94783}}, "5d1cb00ceb7748789226fda1f58b0a4e": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bcb290e3be8c4d5a8ecf2c80aac219db", "placeholder": "​", "style": "IPY_MODEL_9c1d45b811364f3aa247eb71f6b3f797", "value": " 94.8k/94.8k [00:00&lt;00:00, 2.26MB/s]"}}, "fd7ea0696e6d4c0ea7661b4a91772324": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bcd7d01133c24ba49f8691a734ffb8e6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9fa39ded11174699ac0fc2249c76b180": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8dd7662de1c040dca74d7512bd576daa": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9883747427aa41f184380248c7cc826b": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "bcb290e3be8c4d5a8ecf2c80aac219db": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9c1d45b811364f3aa247eb71f6b3f797": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7e503f9772aa4ddab7b85464b6cc9708": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2cdcd3f2d413478f91fd04b012195490", "IPY_MODEL_91e47a9730d14238a9a37a821e1c2b5c", "IPY_MODEL_5871110cc3b642a6a1484da093d444b3"], "layout": "IPY_MODEL_f0446b0d33b743f3949dc3d1f0cb824a"}}, "2cdcd3f2d413478f91fd04b012195490": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f80d571e3d794001bdff4a262a23aea7", "placeholder": "​", "style": "IPY_MODEL_23ddefb9b7b74569b60bf8d67bfdf936", "value": "sentence_bert_config.json: 100%"}}, "91e47a9730d14238a9a37a821e1c2b5c": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_939b3dff330d478cac30436e5250f569", "max": 52, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_27c67b594fc54d0cacbf9e3f572c3b00", "value": 52}}, "5871110cc3b642a6a1484da093d444b3": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f826e95b30544cf489899049ecd24071", "placeholder": "​", "style": "IPY_MODEL_b471bdafabb54964a175702b5b3ac1e6", "value": " 52.0/52.0 [00:00&lt;00:00, 2.42kB/s]"}}, "f0446b0d33b743f3949dc3d1f0cb824a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f80d571e3d794001bdff4a262a23aea7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "23ddefb9b7b74569b60bf8d67bfdf936": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "939b3dff330d478cac30436e5250f569": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "27c67b594fc54d0cacbf9e3f572c3b00": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f826e95b30544cf489899049ecd24071": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b471bdafabb54964a175702b5b3ac1e6": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9e4bba31701b419d95fe0df93d1b089f": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_3562215502ba4d4fa245aa2664532166", "IPY_MODEL_08cbd7b62b5a4ba88a1bbddb3cbeea48", "IPY_MODEL_cd6e56c80c1a47b393c91e2a0091fe61"], "layout": "IPY_MODEL_135a506df5224098a12c4d484627c7b8"}}, "3562215502ba4d4fa245aa2664532166": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_42e9fa4852d242648dc6b6b208c9342d", "placeholder": "​", "style": "IPY_MODEL_88c80fea2a1440f480a96a1e476ddec7", "value": "config.json: 100%"}}, "08cbd7b62b5a4ba88a1bbddb3cbeea48": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7cc665e9dd714b1285fcff1e3d235c84", "max": 743, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_22886fc9beac482fbd0e70e4673383d7", "value": 743}}, "cd6e56c80c1a47b393c91e2a0091fe61": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_75592dea95fc41d7b6b4ef6bad7f8f72", "placeholder": "​", "style": "IPY_MODEL_93af90cf63d94f9daf8d0f133dd9a9aa", "value": " 743/743 [00:00&lt;00:00, 33.3kB/s]"}}, "135a506df5224098a12c4d484627c7b8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "42e9fa4852d242648dc6b6b208c9342d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "88c80fea2a1440f480a96a1e476ddec7": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7cc665e9dd714b1285fcff1e3d235c84": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "22886fc9beac482fbd0e70e4673383d7": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "75592dea95fc41d7b6b4ef6bad7f8f72": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "93af90cf63d94f9daf8d0f133dd9a9aa": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ce41452db26a4ee68d235a433b8da8ed": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c766e3e9235749c6b085529f5f90979e", "IPY_MODEL_d82e3c5ba4f84a45a18ec099f89823e2", "IPY_MODEL_93f615374410475f9d5c6a42d9a342d9"], "layout": "IPY_MODEL_ff22b99be102448a83c2a834c1b317ac"}}, "c766e3e9235749c6b085529f5f90979e": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_09b50d5f77b543cbab1b7729fa991ead", "placeholder": "​", "style": "IPY_MODEL_a1cc666b195c444bac2e58233971f7c8", "value": "model.safetensors: 100%"}}, "d82e3c5ba4f84a45a18ec099f89823e2": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4dbd12c4ffb44429826cd6e1cafee6f0", "max": 133466304, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c2168c78e05741ebbd922ee2f1099c95", "value": 133466304}}, "93f615374410475f9d5c6a42d9a342d9": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6d012696dc804c58990e4c707efe5af5", "placeholder": "​", "style": "IPY_MODEL_dc55034b980d489bbc4b92b787267dd2", "value": " 133M/133M [00:01&lt;00:00, 130MB/s]"}}, "ff22b99be102448a83c2a834c1b317ac": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "09b50d5f77b543cbab1b7729fa991ead": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a1cc666b195c444bac2e58233971f7c8": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4dbd12c4ffb44429826cd6e1cafee6f0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c2168c78e05741ebbd922ee2f1099c95": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "6d012696dc804c58990e4c707efe5af5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dc55034b980d489bbc4b92b787267dd2": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9029e54ea2f74ff58d47e21b1b7f4dfd": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_3102ef78b0064210bf5fa819e9c02985", "IPY_MODEL_b380faaac82b47c48b52cc00255385ae", "IPY_MODEL_1667ad42b84a45659665382ea7b6eb8c"], "layout": "IPY_MODEL_2027dfd21e16443f90b4fab1466f321c"}}, "3102ef78b0064210bf5fa819e9c02985": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_102829f6002d4891ab0fea657f47d552", "placeholder": "​", "style": "IPY_MODEL_5e81eb28076d4b67be6413c960419e4a", "value": "tokenizer_config.json: 100%"}}, "b380faaac82b47c48b52cc00255385ae": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0272abcbbc0846bc8ba7530d1bb0199f", "max": 366, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_1c55cec7f7ef4ab29c5427e4e1039dd9", "value": 366}}, "1667ad42b84a45659665382ea7b6eb8c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cf51836aceaa4967a50bf51d97e96b0e", "placeholder": "​", "style": "IPY_MODEL_4433344c254347f8ac48eccb3faf1325", "value": " 366/366 [00:00&lt;00:00, 16.9kB/s]"}}, "2027dfd21e16443f90b4fab1466f321c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "102829f6002d4891ab0fea657f47d552": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5e81eb28076d4b67be6413c960419e4a": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0272abcbbc0846bc8ba7530d1bb0199f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1c55cec7f7ef4ab29c5427e4e1039dd9": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "cf51836aceaa4967a50bf51d97e96b0e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4433344c254347f8ac48eccb3faf1325": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ff60ace5006647fba858f1217be96f81": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b91a3c61cff04b96a199181513befe03", "IPY_MODEL_e73d07340f9a417993b65ce135443b6b", "IPY_MODEL_2840ea0bb8ce4a6bafdaee2d6ee26855"], "layout": "IPY_MODEL_40c790b6e63f43b8be4a1ed1154ef073"}}, "b91a3c61cff04b96a199181513befe03": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f41156f5cf7244bfbc95ae35f3850e80", "placeholder": "​", "style": "IPY_MODEL_0af61f0b3bd447738f6ad185e42c8876", "value": "vocab.txt: 100%"}}, "e73d07340f9a417993b65ce135443b6b": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0c7cf72284bd44f0a4cc98842437b31d", "max": 231508, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9ad330d5afc24e64b7d44462467dd7e2", "value": 231508}}, "2840ea0bb8ce4a6bafdaee2d6ee26855": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_02dc492b85264d31a428450b60da9ccc", "placeholder": "​", "style": "IPY_MODEL_71f3267bf86642ea83f9b6b5579fb97b", "value": " 232k/232k [00:00&lt;00:00, 6.86MB/s]"}}, "40c790b6e63f43b8be4a1ed1154ef073": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f41156f5cf7244bfbc95ae35f3850e80": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0af61f0b3bd447738f6ad185e42c8876": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0c7cf72284bd44f0a4cc98842437b31d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9ad330d5afc24e64b7d44462467dd7e2": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "02dc492b85264d31a428450b60da9ccc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "71f3267bf86642ea83f9b6b5579fb97b": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3f77adb9c44745ba9445b929c4b5a400": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ebc0c08856a54400a36c177a3c3da053", "IPY_MODEL_d59946acd45e498db9ffa7b00fcefe08", "IPY_MODEL_850b3f190fcb4f58985dcd013dd7a6c3"], "layout": "IPY_MODEL_46fce79af203421f8705c540bad9b1da"}}, "ebc0c08856a54400a36c177a3c3da053": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d26d79af6a8d43c1ae0d39b0b4a9b063", "placeholder": "​", "style": "IPY_MODEL_73cea16ea6034b5184621cf320fe3eed", "value": "tokenizer.json: 100%"}}, "d59946acd45e498db9ffa7b00fcefe08": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5a5733e0c41a4677838bf70b937df5ed", "max": 711396, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_fbffd93a1b5446439e3bf26efe2bf7f3", "value": 711396}}, "850b3f190fcb4f58985dcd013dd7a6c3": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a894726354e24ee6b43fed5442a31ac2", "placeholder": "​", "style": "IPY_MODEL_effd66db2abe4a71820c7a5389b35e98", "value": " 711k/711k [00:00&lt;00:00, 15.5MB/s]"}}, "46fce79af203421f8705c540bad9b1da": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d26d79af6a8d43c1ae0d39b0b4a9b063": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "73cea16ea6034b5184621cf320fe3eed": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5a5733e0c41a4677838bf70b937df5ed": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fbffd93a1b5446439e3bf26efe2bf7f3": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "a894726354e24ee6b43fed5442a31ac2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "effd66db2abe4a71820c7a5389b35e98": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8b13c6e1a7604317a7d740027e120ef2": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f123e162e5af45459d49572bcc49b912", "IPY_MODEL_a7b447f2c5d84decb830e4bb02b887f8", "IPY_MODEL_165df076279f489c8bfe32a37eab27c2"], "layout": "IPY_MODEL_4e76c2d6b11548ac850865a0e038ffd3"}}, "f123e162e5af45459d49572bcc49b912": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a3434ac854404b6ebf456f9260e63162", "placeholder": "​", "style": "IPY_MODEL_8d18616942ad4258a65a3a144b13e3ad", "value": "special_tokens_map.json: 100%"}}, "a7b447f2c5d84decb830e4bb02b887f8": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d636658c996c40a88c2458c5bc6ddc92", "max": 125, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_64732181d80c428cb78cfafe583441ac", "value": 125}}, "165df076279f489c8bfe32a37eab27c2": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cbefc3cef96d45e1aaab536cc3a066c8", "placeholder": "​", "style": "IPY_MODEL_88fac99bc696470f8aea85b97c94e375", "value": " 125/125 [00:00&lt;00:00, 7.61kB/s]"}}, "4e76c2d6b11548ac850865a0e038ffd3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a3434ac854404b6ebf456f9260e63162": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8d18616942ad4258a65a3a144b13e3ad": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d636658c996c40a88c2458c5bc6ddc92": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "64732181d80c428cb78cfafe583441ac": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "cbefc3cef96d45e1aaab536cc3a066c8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "88fac99bc696470f8aea85b97c94e375": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e7eabda9b5534691a6b6d7efdfa70b63": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_81b22795c9cc4292a910f03da0a1b766", "IPY_MODEL_add326d655e5437e8607cd151ef19736", "IPY_MODEL_0b9c5d14cfdc4b15af92f8c3e1f47799"], "layout": "IPY_MODEL_d866d7aceb30462fb3afb24a22b7008f"}}, "81b22795c9cc4292a910f03da0a1b766": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1f4293e41f2e47c88ba7cb3155b3ca68", "placeholder": "​", "style": "IPY_MODEL_12c3078628524eb297e58fc347643c62", "value": "1_Pooling/config.json: 100%"}}, "add326d655e5437e8607cd151ef19736": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c2d8dd2bd77a4d818e0a785e815b2cc0", "max": 190, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_247d18d2f8314f59ad5bf65e1d53c0b2", "value": 190}}, "0b9c5d14cfdc4b15af92f8c3e1f47799": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5958b3659e754912956498e1e248bc81", "placeholder": "​", "style": "IPY_MODEL_0c4e03dfdb0c4f4f934fb94bf2fb309e", "value": " 190/190 [00:00&lt;00:00, 5.66kB/s]"}}, "d866d7aceb30462fb3afb24a22b7008f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1f4293e41f2e47c88ba7cb3155b3ca68": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "12c3078628524eb297e58fc347643c62": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c2d8dd2bd77a4d818e0a785e815b2cc0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "247d18d2f8314f59ad5bf65e1d53c0b2": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5958b3659e754912956498e1e248bc81": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0c4e03dfdb0c4f4f934fb94bf2fb309e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "nbformat": 4, "nbformat_minor": 0}