{"cells": [{"cell_type": "markdown", "metadata": {"id": "6JUkLoO0l9RC"}, "source": ["#What is the RAG system?\n", "\n", "## Defination:\n", "\n", "This is called retrieval augmented generation (RAG), as you would retrieve the relevant data and use it as augmented context for the LLM. Instead of relying solely on knowledge derived from the training data, a RAG workflow pulls relevant information and connects static LLMs with real-time data retrieval.\n", "\n", "## Architecture:\n", "\n", "![image.png](data:image/png;base64,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)"]}, {"cell_type": "markdown", "metadata": {"id": "hR_g22LnmCQP"}, "source": ["## Why we create a RAG System?\n", "\n", "Retrieval systems (RAG) give LLM systems access to factual, access-controlled, timely information.\n", "\n", "1. RAG REDUCES HALLUCINATION\n", "\n", "Example: In the financial services industry, providing accurate information on investment options is crucial because it directly impacts customers' purchasing decisions and financial well-being. RAG can help ensure that the information generated about stocks, bonds, or mutual funds\n", "\n", "2. COST-EFFECTIVE ALTERNATIVE\n", "\n", "Example: Banks often need to assess the creditworthiness of potential borrowers. Fine-tuning pre-trained language models to analyse credit histories can be resource-intensive. RAG architecture offers a cost-effective alternative by retrieving relevant financial data and credit history information from existing databases, combining this with pre-trained language models\n", "\n", "3. CREDIBLE AND ACCURATE RESPONSES\n", "\n", "Example: In customer support, providing accurate and helpful responses is essential for maintaining customer trust, as it demonstrates the company's commitment to providing reliable information and support. The RAG technique is able to do this very effectively by retrieving data from catalogues, policies, and past customer interactions to generate context-aware insights, ensuring that customers receive reliable information on product features, returns, and other inquiries.\n", "\n", "4. DOMAIN-SPECIFIC INFORMATION\n", "\n", "Example: In the legal industry, clients often require advice specific to their case or jurisdiction because different legal systems have unique rules and regulations, and understanding these nuances is crucial for effective legal representation. RAG can access domain-specific knowledge bases, such as local statutes and case law, to provide tailored information relevant to clients' legal needs.\n", "\n", "https://www.advancinganalytics.co.uk/blog/2023/11/7/10-reasons-why-you-need-to-implement-rag-a-game-changer-in-ai\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "8WZp8J48mB68"}, "source": ["# RAG Practical Usecase\n", "\n", "1. Document Question Answering Systems\n", "2. Conversational agents\n", "3. Real-time Event Commentary\n", "4. Content Generation\n", "5. Personalised Recommendation\n", "6. Virtual Assistants"]}, {"cell_type": "markdown", "metadata": {"id": "R-fUCj0KmJGX"}, "source": ["## Installing the necessary libraries"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zD4C31_TmFbY", "outputId": "5c332332-d246-4bab-c1e2-83534c8c2ac4"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting langchain\n", "  Downloading langchain-0.1.14-py3-none-any.whl (812 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m812.8/812.8 kB\u001b[0m \u001b[31m7.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting openai\n", "  Downloading openai-1.16.2-py3-none-any.whl (267 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m267.1/267.1 kB\u001b[0m \u001b[31m10.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting tiktoken\n", "  Downloading tiktoken-0.6.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.8/1.8 MB\u001b[0m \u001b[31m17.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting rapidocr-onnxruntime\n", "  Downloading rapidocr_onnxruntime-1.3.16-py3-none-any.whl (14.9 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m14.9/14.9 MB\u001b[0m \u001b[31m37.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.0.29)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.9.3)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (4.0.3)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain)\n", "  Downloading dataclasses_json-0.6.4-py3-none-any.whl (28 kB)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Collecting langchain-community<0.1,>=0.0.30 (from langchain)\n", "  Downloading langchain_community-0.0.31-py3-none-any.whl (1.9 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.9/1.9 MB\u001b[0m \u001b[31m54.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-core<0.2.0,>=0.1.37 (from langchain)\n", "  Downloading langchain_core-0.1.40-py3-none-any.whl (276 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m276.8/276.8 kB\u001b[0m \u001b[31m22.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-text-splitters<0.1,>=0.0.1 (from langchain)\n", "  Downloading langchain_text_splitters-0.0.1-py3-none-any.whl (21 kB)\n", "Collecting langsmith<0.2.0,>=0.1.17 (from langchain)\n", "  Downloading langsmith-0.1.40-py3-none-any.whl (87 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m87.5/87.5 kB\u001b[0m \u001b[31m7.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.25.2)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.6.4)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (8.2.3)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.10/dist-packages (from openai) (3.7.1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai) (1.7.0)\n", "Collecting httpx<1,>=0.23.0 (from openai)\n", "  Downloading httpx-0.27.0-py3-none-any.whl (75 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m5.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from openai) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /usr/local/lib/python3.10/dist-packages (from openai) (4.66.2)\n", "Requirement already satisfied: typing-extensions<5,>=4.7 in /usr/local/lib/python3.10/dist-packages (from openai) (4.10.0)\n", "Requirement already satisfied: regex>=2022.1.18 in /usr/local/lib/python3.10/dist-packages (from tiktoken) (2023.12.25)\n", "Collecting pyclipper>=1.2.0 (from rapidocr-onnxruntime)\n", "  Downloading pyclipper-1.3.0.post5-cp310-cp310-manylinux_2_12_x86_64.manylinux2010_x86_64.whl (908 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m908.3/908.3 kB\u001b[0m \u001b[31m40.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting onnxruntime>=1.7.0 (from rapidocr-onnxruntime)\n", "  Downloading onnxruntime-1.17.1-cp310-cp310-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (6.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.8/6.8 MB\u001b[0m \u001b[31m45.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: opencv-python>=******** in /usr/local/lib/python3.10/dist-packages (from rapidocr-onnxruntime) (********)\n", "Requirement already satisfied: six>=1.15.0 in /usr/local/lib/python3.10/dist-packages (from rapidocr-onnxruntime) (1.16.0)\n", "Requirement already satisfied: S<PERSON><PERSON>y>=1.7.1 in /usr/local/lib/python3.10/dist-packages (from rapidocr-onnxruntime) (2.0.3)\n", "Requirement already satisfied: Pillow in /usr/local/lib/python3.10/dist-packages (from rapidocr-onnxruntime) (9.4.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.9.4)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai) (3.6)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai) (1.2.0)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain)\n", "  Downloading marshmallow-3.21.1-py3-none-any.whl (49 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.4/49.4 kB\u001b[0m \u001b[31m3.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->openai) (2024.2.2)\n", "Collecting httpcore==1.* (from httpx<1,>=0.23.0->openai)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m6.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->openai)\n", "  Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m6.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain)\n", "  Downloading jsonpointer-2.4-py2.py3-none-any.whl (7.8 kB)\n", "Collecting packaging<24.0,>=23.2 (from langchain-core<0.2.0,>=0.1.37->langchain)\n", "  Downloading packaging-23.2-py3-none-any.whl (53 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m53.0/53.0 kB\u001b[0m \u001b[31m911.0 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hColl<PERSON>ting or<PERSON>son<4.0.0,>=3.9.14 (from langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading orjson-3.10.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (144 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m144.8/144.8 kB\u001b[0m \u001b[31m5.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting coloredlogs (from onnxruntime>=1.7.0->rapidocr-onnxruntime)\n", "  Downloading coloredlogs-15.0.1-py2.py3-none-any.whl (46 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.0/46.0 kB\u001b[0m \u001b[31m3.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: flatbuffers in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.7.0->rapidocr-onnxruntime) (24.3.25)\n", "Requirement already satisfied: protobuf in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.7.0->rapidocr-onnxruntime) (3.20.3)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.7.0->rapidocr-onnxruntime) (1.12)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.16.3 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (2.16.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2.0.7)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.0.3)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Collecting humanfriendly>=9.1 (from coloredlogs->onnxruntime>=1.7.0->rapidocr-onnxruntime)\n", "  Downloading humanfriendly-10.0-py2.py3-none-any.whl (86 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.8/86.8 kB\u001b[0m \u001b[31m5.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->onnxruntime>=1.7.0->rapidocr-onnxruntime) (1.3.0)\n", "Installing collected packages: pyclipper, packaging, orjson, mypy-extensions, jsonpointer, humanfriendly, h11, typing-inspect, tiktoken, marshmallow, jsonpatch, httpcore, coloredlogs, onnxruntime, langsmith, httpx, dataclasses-json, rapidocr-onnxruntime, openai, langchain-core, langchain-text-splitters, langchain-community, langchain\n", "  Attempting uninstall: packaging\n", "    Found existing installation: packaging 24.0\n", "    Uninstalling packaging-24.0:\n", "      Successfully uninstalled packaging-24.0\n", "Successfully installed coloredlogs-15.0.1 dataclasses-json-0.6.4 h11-0.14.0 httpcore-1.0.5 httpx-0.27.0 humanfriendly-10.0 jsonpatch-1.33 jsonpointer-2.4 langchain-0.1.14 langchain-community-0.0.31 langchain-core-0.1.40 langchain-text-splitters-0.0.1 langsmith-0.1.40 marshmallow-3.21.1 mypy-extensions-1.0.0 onnxruntime-1.17.1 openai-1.16.2 orjson-3.10.0 packaging-23.2 pyclipper-1.3.0.post5 rapidocr-onnxruntime-1.3.16 tiktoken-0.6.0 typing-inspect-0.9.0\n"]}], "source": ["!pip install langchain openai tiktoken rapidocr-onnxruntime"]}, {"cell_type": "markdown", "metadata": {"id": "gJ4mHOgxmIu_"}, "source": ["## Fetching OpenAI API key"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "jmGu5Lr-mPZG"}, "outputs": [], "source": ["from google.colab import userdata\n", "OPENAI_API_KEY=userdata.get('OPENAI_API_KEY')"]}, {"cell_type": "markdown", "metadata": {"id": "6TN6ZHo-uaWd"}, "source": ["## Setting Enviornment Variable"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "phbDr1pcuWl7"}, "outputs": [], "source": ["import os\n", "os.environ[\"OPENAI_API_KEY\"] = OPENAI_API_KEY"]}, {"cell_type": "markdown", "metadata": {"id": "oYPoOcMVutNQ"}, "source": ["1. Data Ingestion\n", "2. Data Reterival\n", "3. Data Generation"]}, {"cell_type": "markdown", "metadata": {"id": "cVSNQSzju1se"}, "source": ["# Data Ingestion\n", "\n", "https://en.wikipedia.org/wiki/State_of_the_Union#:~:text=Though%20the%20language%20of%20the,as%20late%20as%20March%207"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "TOkAHEuRu0jg"}, "outputs": [], "source": ["from langchain.document_loaders import TextLoader\n", "from langchain.vectorstores import FAISS"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "PPqWneqdvy_o"}, "outputs": [], "source": ["with open(\"state_of_the_union.txt\",\"r\", encoding=\"utf8\") as f:\n", "  data = f.read()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "muXK-ABgv6vY"}, "outputs": [], "source": ["loder=TextLoader('state_of_the_union.txt', encoding=\"utf8\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "LHt7Z4ZjwP03"}, "outputs": [], "source": ["document=loder.load()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "iCTmsK8KwW7H", "outputId": "4cb9de6f-6ced-4de8-c358-35bb5ab700f9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Madam Speaker, <PERSON>am Vice President, our First Lady and Second Gentleman. Members of Congress and the Cabinet. Justices of the Supreme Court. My fellow Americans.  \n", "\n", "Last year COVID-19 kept us apart. This year we are finally together again. \n", "\n", "Tonight, we meet as Democrats Republicans and Independents. But most importantly as Americans. \n", "\n", "With a duty to one another to the American people to the Constitution. \n", "\n", "And with an unwavering resolve that freedom will always triumph over tyranny. \n", "\n", "Six days ago, Russia’s <PERSON> sought to shake the foundations of the free world thinking he could make it bend to his menacing ways. But he badly miscalculated. \n", "\n", "He thought he could roll into Ukraine and the world would roll over. Instead he met a wall of strength he never imagined. \n", "\n", "He met the Ukrainian people. \n", "\n", "From President <PERSON><PERSON><PERSON><PERSON> to every Ukrainian, their fearlessness, their courage, their determination, inspires the world. \n", "\n", "Groups of citizens blocking tanks with their bodies. Everyone from students to retirees teachers turned soldiers defending their homeland. \n", "\n", "In this struggle as President <PERSON><PERSON><PERSON><PERSON> said in his speech to the European Parliament “Light will win over darkness.” The Ukrainian Ambassador to the United States is here tonight. \n", "\n", "Let each of us here tonight in this Chamber send an unmistakable signal to Ukraine and to the world. \n", "\n", "Please rise if you are able and show that, Yes, we the United States of America stand with the Ukrainian people. \n", "\n", "Throughout our history we’ve learned this lesson when dictators do not pay a price for their aggression they cause more chaos.   \n", "\n", "They keep moving.   \n", "\n", "And the costs and the threats to America and the world keep rising.   \n", "\n", "That’s why the NATO Alliance was created to secure peace and stability in Europe after World War 2. \n", "\n", "The United States is a member along with 29 other nations. \n", "\n", "It matters. American diplomacy matters. American resolve matters. \n", "\n", "<PERSON>’s latest attack on Ukraine was premeditated and unprovoked. \n", "\n", "He rejected repeated efforts at diplomacy. \n", "\n", "He thought the West and NATO wouldn’t respond. And he thought he could divide us at home. Putin was wrong. We were ready.  Here is what we did.   \n", "\n", "We prepared extensively and carefully. \n", "\n", "We spent months building a coalition of other freedom-loving nations from Europe and the Americas to Asia and Africa to confront <PERSON>. \n", "\n", "I spent countless hours unifying our European allies. We shared with the world in advance what we knew <PERSON> was planning and precisely how he would try to falsely justify his aggression.  \n", "\n", "We countered Russia’s lies with truth.   \n", "\n", "And now that he has acted the free world is holding him accountable. \n", "\n", "Along with twenty-seven members of the European Union including France, Germany, Italy, as well as countries like the United Kingdom, Canada, Japan, Korea, Australia, New Zealand, and many others, even Switzerland. \n", "\n", "We are inflicting pain on Russia and supporting the people of Ukraine. <PERSON> is now isolated from the world more than ever. \n", "\n", "Together with our allies –we are right now enforcing powerful economic sanctions. \n", "\n", "We are cutting off Russia’s largest banks from the international financial system.  \n", "\n", "Preventing Russia’s central bank from defending the Russian Ruble making <PERSON>’s $630 Billion “war fund” worthless.   \n", "\n", "We are choking off Russia’s access to technology that will sap its economic strength and weaken its military for years to come.  \n", "\n", "Tonight I say to the Russian oligarchs and corrupt leaders who have bilked billions of dollars off this violent regime no more. \n", "\n", "The U.S. Department of Justice is assembling a dedicated task force to go after the crimes of Russian oligarchs.  \n", "\n", "We are joining with our European allies to find and seize your yachts your luxury apartments your private jets. We are coming for your ill-begotten gains. \n", "\n", "And tonight I am announcing that we will join our allies in closing off American air space to all Russian flights – further isolating Russia – and adding an additional squeeze –on their economy. The Ruble has lost 30% of its value. \n", "\n", "The Russian stock market has lost 40% of its value and trading remains suspended. Russia’s economy is reeling and Putin alone is to blame. \n", "\n", "Together with our allies we are providing support to the Ukrainians in their fight for freedom. Military assistance. Economic assistance. Humanitarian assistance. \n", "\n", "We are giving more than $1 Billion in direct assistance to Ukraine. \n", "\n", "And we will continue to aid the Ukrainian people as they defend their country and to help ease their suffering.  \n", "\n", "Let me be clear, our forces are not engaged and will not engage in conflict with Russian forces in Ukraine.  \n", "\n", "Our forces are not going to Europe to fight in Ukraine, but to defend our NATO Allies – in the event that <PERSON> decides to keep moving west.  \n", "\n", "For that purpose we’ve mobilized American ground forces, air squadrons, and ship deployments to protect NATO countries including Poland, Romania, Latvia, Lithuania, and Estonia. \n", "\n", "As I have made crystal clear the United States and our Allies will defend every inch of territory of NATO countries with the full force of our collective power.  \n", "\n", "And we remain clear-eyed. The Ukrainians are fighting back with pure courage. But the next few days weeks, months, will be hard on them.  \n", "\n", "<PERSON> has unleashed violence and chaos.  But while he may make gains on the battlefield – he will pay a continuing high price over the long run. \n", "\n", "And a proud Ukrainian people, who have known 30 years  of independence, have repeatedly shown that they will not tolerate anyone who tries to take their country backwards.  \n", "\n", "To all Americans, I will be honest with you, as I’ve always promised. A Russian dictator, invading a foreign country, has costs around the world. \n", "\n", "And I’m taking robust action to make sure the pain of our sanctions  is targeted at Russia’s economy. And I will use every tool at our disposal to protect American businesses and consumers. \n", "\n", "Tonight, I can announce that the United States has worked with 30 other countries to release 60 Million barrels of oil from reserves around the world.  \n", "\n", "America will lead that effort, releasing 30 Million barrels from our own Strategic Petroleum Reserve. And we stand ready to do more if necessary, unified with our allies.  \n", "\n", "These steps will help blunt gas prices here at home. And I know the news about what’s happening can seem alarming. \n", "\n", "But I want you to know that we are going to be okay. \n", "\n", "When the history of this era is written <PERSON>’s war on Ukraine will have left Russia weaker and the rest of the world stronger. \n", "\n", "While it shouldn’t have taken something so terrible for people around the world to see what’s at stake now everyone sees it clearly. \n", "\n", "We see the unity among leaders of nations and a more unified Europe a more unified West. And we see unity among the people who are gathering in cities in large crowds around the world even in Russia to demonstrate their support for Ukraine.  \n", "\n", "In the battle between democracy and autocracy, democracies are rising to the moment, and the world is clearly choosing the side of peace and security. \n", "\n", "This is a real test. It’s going to take time. So let us continue to draw inspiration from the iron will of the Ukrainian people. \n", "\n", "To our fellow Ukrainian Americans who forge a deep bond that connects our two nations we stand with you. \n", "\n", "<PERSON> may circle Kyiv with tanks, but he will never gain the hearts and souls of the Ukrainian people. \n", "\n", "He will never extinguish their love of freedom. He will never weaken the resolve of the free world. \n", "\n", "We meet tonight in an America that has lived through two of the hardest years this nation has ever faced. \n", "\n", "The pandemic has been punishing. \n", "\n", "And so many families are living paycheck to paycheck, struggling to keep up with the rising cost of food, gas, housing, and so much more. \n", "\n", "I understand. \n", "\n", "I remember when my Dad had to leave our home in Scranton, Pennsylvania to find work. I grew up in a family where if the price of food went up, you felt it. \n", "\n", "That’s why one of the first things I did as President was fight to pass the American Rescue Plan.  \n", "\n", "Because people were hurting. We needed to act, and we did. \n", "\n", "Few pieces of legislation have done more in a critical moment in our history to lift us out of crisis. \n", "\n", "It fueled our efforts to vaccinate the nation and combat COVID-19. It delivered immediate economic relief for tens of millions of Americans.  \n", "\n", "Helped put food on their table, keep a roof over their heads, and cut the cost of health insurance. \n", "\n", "And as my Dad used to say, it gave people a little breathing room. \n", "\n", "And unlike the $2 Trillion tax cut passed in the previous administration that benefitted the top 1% of Americans, the American Rescue Plan helped working people—and left no one behind. \n", "\n", "And it worked. It created jobs. Lots of jobs. \n", "\n", "In fact—our economy created over 6.5 Million new jobs just last year, more jobs created in one year  \n", "than ever before in the history of America. \n", "\n", "Our economy grew at a rate of 5.7% last year, the strongest growth in nearly 40 years, the first step in bringing fundamental change to an economy that hasn’t worked for the working people of this nation for too long.  \n", "\n", "For the past 40 years we were told that if we gave tax breaks to those at the very top, the benefits would trickle down to everyone else. \n", "\n", "But that trickle-down theory led to weaker economic growth, lower wages, bigger deficits, and the widest gap between those at the top and everyone else in nearly a century. \n", "\n", "Vice President <PERSON> and <PERSON> ran for office with a new economic vision for America. \n", "\n", "Invest in America. Educate Americans. Grow the workforce. Build the economy from the bottom up  \n", "and the middle out, not from the top down.  \n", "\n", "Because we know that when the middle class grows, the poor have a ladder up and the wealthy do very well. \n", "\n", "America used to have the best roads, bridges, and airports on Earth. \n", "\n", "Now our infrastructure is ranked 13th in the world. \n", "\n", "We won’t be able to compete for the jobs of the 21st Century if we don’t fix that. \n", "\n", "That’s why it was so important to pass the Bipartisan Infrastructure Law—the most sweeping investment to rebuild America in history. \n", "\n", "This was a bipartisan effort, and I want to thank the members of both parties who worked to make it happen. \n", "\n", "We’re done talking about infrastructure weeks. \n", "\n", "We’re going to have an infrastructure decade. \n", "\n", "It is going to transform America and put us on a path to win the economic competition of the 21st Century that we face with the rest of the world—particularly with China.  \n", "\n", "As I’ve told <PERSON>, it is never a good bet to bet against the American people. \n", "\n", "We’ll create good jobs for millions of Americans, modernizing roads, airports, ports, and waterways all across America. \n", "\n", "And we’ll do it all to withstand the devastating effects of the climate crisis and promote environmental justice. \n", "\n", "We’ll build a national network of 500,000 electric vehicle charging stations, begin to replace poisonous lead pipes—so every child—and every American—has clean water to drink at home and at school, provide affordable high-speed internet for every American—urban, suburban, rural, and tribal communities. \n", "\n", "4,000 projects have already been announced. \n", "\n", "And tonight, I’m announcing that this year we will start fixing over 65,000 miles of highway and 1,500 bridges in disrepair. \n", "\n", "When we use taxpayer dollars to rebuild America – we are going to Buy American: buy American products to support American jobs. \n", "\n", "The federal government spends about $600 Billion a year to keep the country safe and secure. \n", "\n", "There’s been a law on the books for almost a century \n", "to make sure taxpayers’ dollars support American jobs and businesses. \n", "\n", "Every Administration says they’ll do it, but we are actually doing it. \n", "\n", "We will buy American to make sure everything from the deck of an aircraft carrier to the steel on highway guardrails are made in America. \n", "\n", "But to compete for the best jobs of the future, we also need to level the playing field with China and other competitors. \n", "\n", "That’s why it is so important to pass the Bipartisan Innovation Act sitting in Congress that will make record investments in emerging technologies and American manufacturing. \n", "\n", "Let me give you one example of why it’s so important to pass it. \n", "\n", "If you travel 20 miles east of Columbus, Ohio, you’ll find 1,000 empty acres of land. \n", "\n", "It won’t look like much, but if you stop and look closely, you’ll see a “Field of dreams,” the ground on which America’s future will be built. \n", "\n", "This is where Intel, the American company that helped build Silicon Valley, is going to build its $20 billion semiconductor “mega site”. \n", "\n", "Up to eight state-of-the-art factories in one place. 10,000 new good-paying jobs. \n", "\n", "Some of the most sophisticated manufacturing in the world to make computer chips the size of a fingertip that power the world and our everyday lives. \n", "\n", "Smartphones. The Internet. Technology we have yet to invent. \n", "\n", "But that’s just the beginning. \n", "\n", "Intel’s CEO, <PERSON>, who is here tonight, told me they are ready to increase their investment from  \n", "$20 billion to $100 billion. \n", "\n", "That would be one of the biggest investments in manufacturing in American history. \n", "\n", "And all they’re waiting for is for you to pass this bill. \n", "\n", "So let’s not wait any longer. Send it to my desk. I’ll sign it.  \n", "\n", "And we will really take off. \n", "\n", "And Intel is not alone. \n", "\n", "There’s something happening in America. \n", "\n", "Just look around and you’ll see an amazing story. \n", "\n", "The rebirth of the pride that comes from stamping products “Made In America.” The revitalization of American manufacturing.   \n", "\n", "Companies are choosing to build new factories here, when just a few years ago, they would have built them overseas. \n", "\n", "That’s what is happening. Ford is investing $11 billion to build electric vehicles, creating 11,000 jobs across the country. \n", "\n", "GM is making the largest investment in its history—$7 billion to build electric vehicles, creating 4,000 jobs in Michigan. \n", "\n", "All told, we created 369,000 new manufacturing jobs in America just last year. \n", "\n", "Powered by people I’ve met like <PERSON><PERSON><PERSON>, from generations of union steelworkers from Pittsburgh, who’s here with us tonight. \n", "\n", "As Ohio Senator <PERSON><PERSON><PERSON> says, “It’s time to bury the label “Rust Belt.” \n", "\n", "It’s time. \n", "\n", "But with all the bright spots in our economy, record job growth and higher wages, too many families are struggling to keep up with the bills.  \n", "\n", "Inflation is robbing them of the gains they might otherwise feel. \n", "\n", "I get it. That’s why my top priority is getting prices under control. \n", "\n", "Look, our economy roared back faster than most predicted, but the pandemic meant that businesses had a hard time hiring enough workers to keep up production in their factories. \n", "\n", "The pandemic also disrupted global supply chains. \n", "\n", "When factories close, it takes longer to make goods and get them from the warehouse to the store, and prices go up. \n", "\n", "Look at cars. \n", "\n", "Last year, there weren’t enough semiconductors to make all the cars that people wanted to buy. \n", "\n", "And guess what, prices of automobiles went up. \n", "\n", "So—we have a choice. \n", "\n", "One way to fight inflation is to drive down wages and make Americans poorer.  \n", "\n", "I have a better plan to fight inflation. \n", "\n", "Lower your costs, not your wages. \n", "\n", "Make more cars and semiconductors in America. \n", "\n", "More infrastructure and innovation in America. \n", "\n", "More goods moving faster and cheaper in America. \n", "\n", "More jobs where you can earn a good living in America. \n", "\n", "And instead of relying on foreign supply chains, let’s make it in America. \n", "\n", "Economists call it “increasing the productive capacity of our economy.” \n", "\n", "I call it building a better America. \n", "\n", "My plan to fight inflation will lower your costs and lower the deficit. \n", "\n", "17 Nobel laureates in economics say my plan will ease long-term inflationary pressures. Top business leaders and most Americans support my plan. And here’s the plan: \n", "\n", "First – cut the cost of prescription drugs. Just look at insulin. One in ten Americans has diabetes. In Virginia, I met a 13-year-old boy named <PERSON>.  \n", "\n", "He and his Dad both have Type 1 diabetes, which means they need insulin every day. Insulin costs about $10 a vial to make.  \n", "\n", "But drug companies charge families like <PERSON> and his Dad up to 30 times more. I spoke with <PERSON>’s mom. \n", "\n", "Imagine what it’s like to look at your child who needs insulin and have no idea how you’re going to pay for it.  \n", "\n", "What it does to your dignity, your ability to look your child in the eye, to be the parent you expect to be. \n", "\n", "<PERSON> is here with us tonight. Yesterday was his birthday. Happy birthday, buddy.  \n", "\n", "For <PERSON>, and for the 200,000 other young people with Type 1 diabetes, let’s cap the cost of insulin at $35 a month so everyone can afford it.  \n", "\n", "Drug companies will still do very well. And while we’re at it let Medicare negotiate lower prices for prescription drugs, like the VA already does. \n", "\n", "Look, the American Rescue Plan is helping millions of families on Affordable Care Act plans save $2,400 a year on their health care premiums. Let’s close the coverage gap and make those savings permanent. \n", "\n", "Second – cut energy costs for families an average of $500 a year by combatting climate change.  \n", "\n", "Let’s provide investments and tax credits to weatherize your homes and businesses to be energy efficient and you get a tax credit; double America’s clean energy production in solar, wind, and so much more;  lower the price of electric vehicles, saving you another $80 a month because you’ll never have to pay at the gas pump again. \n", "\n", "Third – cut the cost of child care. Many families pay up to $14,000 a year for child care per child.  \n", "\n", "Middle-class and working families shouldn’t have to pay more than 7% of their income for care of young children.  \n", "\n", "My plan will cut the cost in half for most families and help parents, including millions of women, who left the workforce during the pandemic because they couldn’t afford child care, to be able to get back to work. \n", "\n", "My plan doesn’t stop there. It also includes home and long-term care. More affordable housing. And Pre-K for every 3- and 4-year-old.  \n", "\n", "All of these will lower costs. \n", "\n", "And under my plan, nobody earning less than $400,000 a year will pay an additional penny in new taxes. Nobody.  \n", "\n", "The one thing all Americans agree on is that the tax system is not fair. We have to fix it.  \n", "\n", "I’m not looking to punish anyone. But let’s make sure corporations and the wealthiest Americans start paying their fair share. \n", "\n", "Just last year, 55 Fortune 500 corporations earned $40 billion in profits and paid zero dollars in federal income tax.  \n", "\n", "That’s simply not fair. That’s why I’ve proposed a 15% minimum tax rate for corporations. \n", "\n", "We got more than 130 countries to agree on a global minimum tax rate so companies can’t get out of paying their taxes at home by shipping jobs and factories overseas. \n", "\n", "That’s why I’ve proposed closing loopholes so the very wealthy don’t pay a lower tax rate than a teacher or a firefighter.  \n", "\n", "So that’s my plan. It will grow the economy and lower costs for families. \n", "\n", "So what are we waiting for? Let’s get this done. And while you’re at it, confirm my nominees to the Federal Reserve, which plays a critical role in fighting inflation.  \n", "\n", "My plan will not only lower costs to give families a fair shot, it will lower the deficit. \n", "\n", "The previous Administration not only ballooned the deficit with tax cuts for the very wealthy and corporations, it undermined the watchdogs whose job was to keep pandemic relief funds from being wasted. \n", "\n", "But in my administration, the watchdogs have been welcomed back. \n", "\n", "We’re going after the criminals who stole billions in relief money meant for small businesses and millions of Americans.  \n", "\n", "And tonight, I’m announcing that the Justice Department will name a chief prosecutor for pandemic fraud. \n", "\n", "By the end of this year, the deficit will be down to less than half what it was before I took office.  \n", "\n", "The only president ever to cut the deficit by more than one trillion dollars in a single year. \n", "\n", "Lowering your costs also means demanding more competition. \n", "\n", "I’m a capitalist, but capitalism without competition isn’t capitalism. \n", "\n", "It’s exploitation—and it drives up prices. \n", "\n", "When corporations don’t have to compete, their profits go up, your prices go up, and small businesses and family farmers and ranchers go under. \n", "\n", "We see it happening with ocean carriers moving goods in and out of America. \n", "\n", "During the pandemic, these foreign-owned companies raised prices by as much as 1,000% and made record profits. \n", "\n", "Tonight, I’m announcing a crackdown on these companies overcharging American businesses and consumers. \n", "\n", "And as Wall Street firms take over more nursing homes, quality in those homes has gone down and costs have gone up.  \n", "\n", "That ends on my watch. \n", "\n", "Medicare is going to set higher standards for nursing homes and make sure your loved ones get the care they deserve and expect. \n", "\n", "We’ll also cut costs and keep the economy going strong by giving workers a fair shot, provide more training and apprenticeships, hire them based on their skills not degrees. \n", "\n", "Let’s pass the Paycheck Fairness Act and paid leave.  \n", "\n", "Raise the minimum wage to $15 an hour and extend the Child Tax Credit, so no one has to raise a family in poverty. \n", "\n", "Let’s increase Pell Grants and increase our historic support of HBCUs, and invest in what <PERSON>—our First Lady who teaches full-time—calls America’s best-kept secret: community colleges. \n", "\n", "And let’s pass the PRO Act when a majority of workers want to form a union—they shouldn’t be stopped.  \n", "\n", "When we invest in our workers, when we build the economy from the bottom up and the middle out together, we can do something we haven’t done in a long time: build a better America. \n", "\n", "For more than two years, COVID-19 has impacted every decision in our lives and the life of the nation. \n", "\n", "And I know you’re tired, frustrated, and exhausted. \n", "\n", "But I also know this. \n", "\n", "Because of the progress we’ve made, because of your resilience and the tools we have, tonight I can say  \n", "we are moving forward safely, back to more normal routines.  \n", "\n", "We’ve reached a new moment in the fight against COVID-19, with severe cases down to a level not seen since last July.  \n", "\n", "Just a few days ago, the Centers for Disease Control and Prevention—the CDC—issued new mask guidelines. \n", "\n", "Under these new guidelines, most Americans in most of the country can now be mask free.   \n", "\n", "And based on the projections, more of the country will reach that point across the next couple of weeks. \n", "\n", "Thanks to the progress we have made this past year, COVID-19 need no longer control our lives.  \n", "\n", "I know some are talking about “living with COVID-19”. Tonight – I say that we will never just accept living with COVID-19. \n", "\n", "We will continue to combat the virus as we do other diseases. And because this is a virus that mutates and spreads, we will stay on guard. \n", "\n", "Here are four common sense steps as we move forward safely.  \n", "\n", "First, stay protected with vaccines and treatments. We know how incredibly effective vaccines are. If you’re vaccinated and boosted you have the highest degree of protection. \n", "\n", "We will never give up on vaccinating more Americans. Now, I know parents with kids under 5 are eager to see a vaccine authorized for their children. \n", "\n", "The scientists are working hard to get that done and we’ll be ready with plenty of vaccines when they do. \n", "\n", "We’re also ready with anti-viral treatments. If you get COVID-19, the Pfizer pill reduces your chances of ending up in the hospital by 90%.  \n", "\n", "We’ve ordered more of these pills than anyone in the world. And <PERSON><PERSON><PERSON> is working overtime to get us 1 Million pills this month and more than double that next month.  \n", "\n", "And we’re launching the “Test to Treat” initiative so people can get tested at a pharmacy, and if they’re positive, receive antiviral pills on the spot at no cost.  \n", "\n", "If you’re immunocompromised or have some other vulnerability, we have treatments and free high-quality masks. \n", "\n", "We’re leaving no one behind or ignoring anyone’s needs as we move forward. \n", "\n", "And on testing, we have made hundreds of millions of tests available for you to order for free.   \n", "\n", "Even if you already ordered free tests tonight, I am announcing that you can order more from covidtests.gov starting next week. \n", "\n", "Second – we must prepare for new variants. Over the past year, we’ve gotten much better at detecting new variants. \n", "\n", "If necessary, we’ll be able to deploy new vaccines within 100 days instead of many more months or years.  \n", "\n", "And, if Congress provides the funds we need, we’ll have new stockpiles of tests, masks, and pills ready if needed. \n", "\n", "I cannot promise a new variant won’t come. But I can promise you we’ll do everything within our power to be ready if it does.  \n", "\n", "Third – we can end the shutdown of schools and businesses. We have the tools we need. \n", "\n", "It’s time for Americans to get back to work and fill our great downtowns again.  People working from home can feel safe to begin to return to the office.   \n", "\n", "We’re doing that here in the federal government. The vast majority of federal workers will once again work in person. \n", "\n", "Our schools are open. Let’s keep it that way. Our kids need to be in school. \n", "\n", "And with 75% of adult Americans fully vaccinated and hospitalizations down by 77%, most Americans can remove their masks, return to work, stay in the classroom, and move forward safely. \n", "\n", "We achieved this because we provided free vaccines, treatments, tests, and masks. \n", "\n", "Of course, continuing this costs money. \n", "\n", "I will soon send Congress a request. \n", "\n", "The vast majority of Americans have used these tools and may want to again, so I expect Congress to pass it quickly.   \n", "\n", "Fourth, we will continue vaccinating the world.     \n", "\n", "We’ve sent 475 Million vaccine doses to 112 countries, more than any other nation. \n", "\n", "And we won’t stop. \n", "\n", "We have lost so much to COVID-19. Time with one another. And worst of all, so much loss of life. \n", "\n", "Let’s use this moment to reset. Let’s stop looking at COVID-19 as a partisan dividing line and see it for what it is: A God-awful disease.  \n", "\n", "Let’s stop seeing each other as enemies, and start seeing each other for who we really are: Fellow Americans.  \n", "\n", "We can’t change how divided we’ve been. But we can change how we move forward—on COVID-19 and other issues we must face together. \n", "\n", "I recently visited the New York City Police Department days after the funerals of Officer <PERSON><PERSON><PERSON> and his partner, Officer <PERSON>. \n", "\n", "They were responding to a 9-1-1 call when a man shot and killed them with a stolen gun. \n", "\n", "Officer <PERSON><PERSON> was 27 years old. \n", "\n", "Officer <PERSON> was 22. \n", "\n", "Both Dominican Americans who’d grown up on the same streets they later chose to patrol as police officers. \n", "\n", "I spoke with their families and told them that we are forever in debt for their sacrifice, and we will carry on their mission to restore the trust and safety every community deserves. \n", "\n", "I’ve worked on these issues a long time. \n", "\n", "I know what works: Investing in crime prevention and community police officers who’ll walk the beat, who’ll know the neighborhood, and who can restore trust and safety. \n", "\n", "So let’s not abandon our streets. Or choose between safety and equal justice. \n", "\n", "Let’s come together to protect our communities, restore trust, and hold law enforcement accountable. \n", "\n", "That’s why the Justice Department required body cameras, banned chokeholds, and restricted no-knock warrants for its officers. \n", "\n", "That’s why the American Rescue Plan provided $350 Billion that cities, states, and counties can use to hire more police and invest in proven strategies like community violence interruption—trusted messengers breaking the cycle of violence and trauma and giving young people hope.  \n", "\n", "We should all agree: The answer is not to Defund the police. The answer is to FUND the police with the resources and training they need to protect our communities. \n", "\n", "I ask Democrats and Republicans alike: Pass my budget and keep our neighborhoods safe.  \n", "\n", "And I will keep doing everything in my power to crack down on gun trafficking and ghost guns you can buy online and make at home—they have no serial numbers and can’t be traced. \n", "\n", "And I ask Congress to pass proven measures to reduce gun violence. Pass universal background checks. Why should anyone on a terrorist list be able to purchase a weapon? \n", "\n", "Ban assault weapons and high-capacity magazines. \n", "\n", "Repeal the liability shield that makes gun manufacturers the only industry in America that can’t be sued. \n", "\n", "These laws don’t infringe on the Second Amendment. They save lives. \n", "\n", "The most fundamental right in America is the right to vote – and to have it counted. And it’s under assault. \n", "\n", "In state after state, new laws have been passed, not only to suppress the vote, but to subvert entire elections. \n", "\n", "We cannot let this happen. \n", "\n", "Tonight. I call on the Senate to: Pass the Freedom to Vote Act. Pass the John Lewis Voting Rights Act. And while you’re at it, pass the Disclose Act so Americans can know who is funding our elections. \n", "\n", "Tonight, I’d like to honor someone who has dedicated his life to serve this country: Justice <PERSON>—an Army veteran, Constitutional scholar, and retiring Justice of the United States Supreme Court. Justice <PERSON>, thank you for your service. \n", "\n", "One of the most serious constitutional responsibilities a President has is nominating someone to serve on the United States Supreme Court. \n", "\n", "And I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>. One of our nation’s top legal minds, who will continue <PERSON>’s legacy of excellence. \n", "\n", "A former top litigator in private practice. A former federal public defender. And from a family of public school educators and police officers. A consensus builder. Since she’s been nominated, she’s received a broad range of support—from the Fraternal Order of Police to former judges appointed by Democrats and Republicans. \n", "\n", "And if we are to advance liberty and justice, we need to secure the Border and fix the immigration system. \n", "\n", "We can do both. At our border, we’ve installed new technology like cutting-edge scanners to better detect drug smuggling.  \n", "\n", "We’ve set up joint patrols with Mexico and Guatemala to catch more human traffickers.  \n", "\n", "We’re putting in place dedicated immigration judges so families fleeing persecution and violence can have their cases heard faster. \n", "\n", "We’re securing commitments and supporting partners in South and Central America to host more refugees and secure their own borders. \n", "\n", "We can do all this while keeping lit the torch of liberty that has led generations of immigrants to this land—my forefathers and so many of yours. \n", "\n", "Provide a pathway to citizenship for Dream<PERSON>, those on temporary status, farm workers, and essential workers. \n", "\n", "Revise our laws so businesses have the workers they need and families don’t wait decades to reunite. \n", "\n", "It’s not only the right thing to do—it’s the economically smart thing to do. \n", "\n", "That’s why immigration reform is supported by everyone from labor unions to religious leaders to the U.S. Chamber of Commerce. \n", "\n", "Let’s get it done once and for all. \n", "\n", "Advancing liberty and justice also requires protecting the rights of women. \n", "\n", "The constitutional right affirmed in <PERSON> v<PERSON>—standing precedent for half a century—is under attack as never before. \n", "\n", "If we want to go forward—not backward—we must protect access to health care. Preserve a woman’s right to choose. And let’s continue to advance maternal health care in America. \n", "\n", "And for our LGBTQ+ Americans, let’s finally get the bipartisan Equality Act to my desk. The onslaught of state laws targeting transgender Americans and their families is wrong. \n", "\n", "As I said last year, especially to our younger transgender Americans, I will always have your back as your President, so you can be yourself and reach your God-given potential. \n", "\n", "While it often appears that we never agree, that isn’t true. I signed 80 bipartisan bills into law last year. From preventing government shutdowns to protecting Asian-Americans from still-too-common hate crimes to reforming military justice. \n", "\n", "And soon, we’ll strengthen the Violence Against Women Act that I first wrote three decades ago. It is important for us to show the nation that we can come together and do big things. \n", "\n", "So tonight I’m offering a Unity Agenda for the Nation. Four big things we can do together.  \n", "\n", "First, beat the opioid epidemic. \n", "\n", "There is so much we can do. Increase funding for prevention, treatment, harm reduction, and recovery.  \n", "\n", "Get rid of outdated rules that stop doctors from prescribing treatments. And stop the flow of illicit drugs by working with state and local law enforcement to go after traffickers. \n", "\n", "If you’re suffering from addiction, know you are not alone. I believe in recovery, and I celebrate the 23 million Americans in recovery. \n", "\n", "Second, let’s take on mental health. Especially among our children, whose lives and education have been turned upside down.  \n", "\n", "The American Rescue Plan gave schools money to hire teachers and help students make up for lost learning.  \n", "\n", "I urge every parent to make sure your school does just that. And we can all play a part—sign up to be a tutor or a mentor. \n", "\n", "Children were also struggling before the pandemic. Bullying, violence, trauma, and the harms of social media. \n", "\n", "As <PERSON>, who is here with us tonight, has shown, we must hold social media platforms accountable for the national experiment they’re conducting on our children for profit. \n", "\n", "It’s time to strengthen privacy protections, ban targeted advertising to children, demand tech companies stop collecting personal data on our children. \n", "\n", "And let’s get all Americans the mental health services they need. More people they can turn to for help, and full parity between physical and mental health care. \n", "\n", "Third, support our veterans. \n", "\n", "Veterans are the best of us. \n", "\n", "I’ve always believed that we have a sacred obligation to equip all those we send to war and care for them and their families when they come home. \n", "\n", "My administration is providing assistance with job training and housing, and now helping lower-income veterans get VA care debt-free.  \n", "\n", "Our troops in Iraq and Afghanistan faced many dangers. \n", "\n", "One was stationed at bases and breathing in toxic smoke from “burn pits” that incinerated wastes of war—medical and hazard material, jet fuel, and more. \n", "\n", "When they came home, many of the world’s fittest and best trained warriors were never the same. \n", "\n", "Headaches. Numbness. Dizziness. \n", "\n", "A cancer that would put them in a flag-draped coffin. \n", "\n", "I know. \n", "\n", "One of those soldiers was my son Major <PERSON>. \n", "\n", "We don’t know for sure if a burn pit was the cause of his brain cancer, or the diseases of so many of our troops. \n", "\n", "But I’m committed to finding out everything we can. \n", "\n", "Committed to military families like <PERSON> from Ohio. \n", "\n", "The widow of Sergeant First Class <PERSON>.  \n", "\n", "He was born a soldier. Army National Guard. Combat medic in Kosovo and Iraq. \n", "\n", "Stationed near Baghdad, just yards from burn pits the size of football fields. \n", "\n", "<PERSON>’s widow <PERSON> is here with us tonight. They loved going to Ohio State football games. He loved building Legos with their daughter. \n", "\n", "But cancer from prolonged exposure to burn pits ravaged <PERSON>’s lungs and body. \n", "\n", "<PERSON> says <PERSON> was a fighter to the very end. \n", "\n", "He didn’t know how to stop fighting, and neither did she. \n", "\n", "Through her pain she found purpose to demand we do better. \n", "\n", "Tonight, <PERSON>—we are. \n", "\n", "The VA is pioneering new ways of linking toxic exposures to diseases, already helping more veterans get benefits. \n", "\n", "And tonight, I’m announcing we’re expanding eligibility to veterans suffering from nine respiratory cancers. \n", "\n", "I’m also calling on Congress: pass a law to make sure veterans devastated by toxic exposures in Iraq and Afghanistan finally get the benefits and comprehensive health care they deserve. \n", "\n", "And fourth, let’s end cancer as we know it. \n", "\n", "This is personal to me and <PERSON>, to <PERSON><PERSON>, and to so many of you. \n", "\n", "Cancer is the #2 cause of death in America–second only to heart disease. \n", "\n", "Last month, I announced our plan to supercharge  \n", "the Cancer Moonshot that President <PERSON> asked me to lead six years ago. \n", "\n", "Our goal is to cut the cancer death rate by at least 50% over the next 25 years, turn more cancers from death sentences into treatable diseases.  \n", "\n", "More support for patients and families. \n", "\n", "To get there, I call on Congress to fund ARPA-H, the Advanced Research Projects Agency for Health. \n", "\n", "It’s based on DARPA—the Defense Department project that led to the Internet, GPS, and so much more.  \n", "\n", "ARPA-H will have a singular purpose—to drive breakthroughs in cancer, Alzheimer’s, diabetes, and more. \n", "\n", "A unity agenda for the nation. \n", "\n", "We can do this. \n", "\n", "My fellow Americans—tonight , we have gathered in a sacred space—the citadel of our democracy. \n", "\n", "In this Capitol, generation after generation, Americans have debated great questions amid great strife, and have done great things. \n", "\n", "We have fought for freedom, expanded liberty, defeated totalitarianism and terror. \n", "\n", "And built the strongest, freest, and most prosperous nation the world has ever known. \n", "\n", "Now is the hour. \n", "\n", "Our moment of responsibility. \n", "\n", "Our test of resolve and conscience, of history itself. \n", "\n", "It is in this moment that our character is formed. Our purpose is found. Our future is forged. \n", "\n", "Well I know this nation.  \n", "\n", "We will meet the test. \n", "\n", "To protect freedom and liberty, to expand fairness and opportunity. \n", "\n", "We will save democracy. \n", "\n", "As hard as these times have been, I am more optimistic about America today than I have been my whole life. \n", "\n", "Because I see the future that is within our grasp. \n", "\n", "Because I know there is simply nothing beyond our capacity. \n", "\n", "We are the only nation on Earth that has always turned every crisis we have faced into an opportunity. \n", "\n", "The only nation that can be defined by a single word: possibilities. \n", "\n", "So on this night, in our 245th year as a nation, I have come to report on the State of the Union. \n", "\n", "And my report is this: the State of the Union is strong—because you, the American people, are strong. \n", "\n", "We are stronger today than we were a year ago. \n", "\n", "And we will be stronger a year from now than we are today. \n", "\n", "Now is our moment to meet and overcome the challenges of our time. \n", "\n", "And we will, as one people. \n", "\n", "One America. \n", "\n", "The United States of America. \n", "\n", "May God bless you all. May God protect our troops.\n"]}], "source": ["print(document[0].page_content)"]}, {"cell_type": "markdown", "metadata": {"id": "Bi-WS695wvpq"}, "source": ["# Chunking of the Data"]}, {"cell_type": "markdown", "metadata": {"id": "djfRyVb8xVjR"}, "source": ["# Here is all the text splitter which is available in Langchain\n", "\n", "https://python.langchain.com/docs/how_to/#text-splitters\n", "\n", "## CharacterTextSplitter v/s RecursiveCharacterTextSplitter\n", "\n", "## you can visualise the chunking also\n", "\n", "https://chunkviz.up.railway.app/\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"id": "1-SEIxghwYTX"}, "outputs": [], "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"id": "hmdC_UTaw104"}, "outputs": [], "source": ["text_splitter=RecursiveCharacterTextSplitter(chunk_size=500,chunk_overlap=50)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"id": "cmEcfiNOykdA"}, "outputs": [], "source": ["text_chunks=text_splitter.split_documents(document)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ME1HhJytzQou", "outputId": "00d9f9bf-93e2-458d-8753-153ba49b540f"}, "outputs": [{"data": {"text/plain": ["[Document(page_content='<PERSON><PERSON> Speaker, <PERSON>am Vice President, our First Lady and Second Gentleman. Members of Congress and the Cabinet. Justices of the Supreme Court. My fellow Americans.  \\n\\nLast year COVID-19 kept us apart. This year we are finally together again. \\n\\nTonight, we meet as Democrats Republicans and Independents. But most importantly as Americans. \\n\\nWith a duty to one another to the American people to the Constitution. \\n\\nAnd with an unwavering resolve that freedom will always triumph over tyranny.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Six days ago, Russia’s <PERSON> sought to shake the foundations of the free world thinking he could make it bend to his menacing ways. But he badly miscalculated. \\n\\nHe thought he could roll into Ukraine and the world would roll over. Instead he met a wall of strength he never imagined. \\n\\nHe met the Ukrainian people. \\n\\nFrom President <PERSON><PERSON><PERSON><PERSON> to every Ukrainian, their fearlessness, their courage, their determination, inspires the world.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Groups of citizens blocking tanks with their bodies. Everyone from students to retirees teachers turned soldiers defending their homeland. \\n\\nIn this struggle as President <PERSON><PERSON><PERSON><PERSON> said in his speech to the European Parliament “Light will win over darkness.” The Ukrainian Ambassador to the United States is here tonight. \\n\\nLet each of us here tonight in this Chamber send an unmistakable signal to Ukraine and to the world.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Please rise if you are able and show that, Yes, we the United States of America stand with the Ukrainian people. \\n\\nThroughout our history we’ve learned this lesson when dictators do not pay a price for their aggression they cause more chaos.   \\n\\nThey keep moving.   \\n\\nAnd the costs and the threats to America and the world keep rising.   \\n\\nThat’s why the NATO Alliance was created to secure peace and stability in Europe after World War 2.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='The United States is a member along with 29 other nations. \\n\\nIt matters. American diplomacy matters. American resolve matters. \\n\\n<PERSON><PERSON><PERSON>’s latest attack on Ukraine was premeditated and unprovoked. \\n\\nHe rejected repeated efforts at diplomacy. \\n\\nHe thought the West and NATO wouldn’t respond. And he thought he could divide us at home. Putin was wrong. We were ready.  Here is what we did.   \\n\\nWe prepared extensively and carefully.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We prepared extensively and carefully. \\n\\nWe spent months building a coalition of other freedom-loving nations from Europe and the Americas to Asia and Africa to confront <PERSON>. \\n\\nI spent countless hours unifying our European allies. We shared with the world in advance what we knew <PERSON> was planning and precisely how he would try to falsely justify his aggression.  \\n\\nWe countered Russia’s lies with truth.   \\n\\nAnd now that he has acted the free world is holding him accountable.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Along with twenty-seven members of the European Union including France, Germany, Italy, as well as countries like the United Kingdom, Canada, Japan, Korea, Australia, New Zealand, and many others, even Switzerland. \\n\\nWe are inflicting pain on Russia and supporting the people of Ukraine. <PERSON> is now isolated from the world more than ever. \\n\\nTogether with our allies –we are right now enforcing powerful economic sanctions.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We are cutting off Russia’s largest banks from the international financial system.  \\n\\nPreventing Russia’s central bank from defending the Russian Ruble making <PERSON>’s $630 Billion “war fund” worthless.   \\n\\nWe are choking off Russia’s access to technology that will sap its economic strength and weaken its military for years to come.  \\n\\nTonight I say to the Russian oligarchs and corrupt leaders who have bilked billions of dollars off this violent regime no more.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='The U.S. Department of Justice is assembling a dedicated task force to go after the crimes of Russian oligarchs.  \\n\\nWe are joining with our European allies to find and seize your yachts your luxury apartments your private jets. We are coming for your ill-begotten gains.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And tonight I am announcing that we will join our allies in closing off American air space to all Russian flights – further isolating Russia – and adding an additional squeeze –on their economy. The Ruble has lost 30% of its value. \\n\\nThe Russian stock market has lost 40% of its value and trading remains suspended. Russia’s economy is reeling and <PERSON> alone is to blame.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Together with our allies we are providing support to the Ukrainians in their fight for freedom. Military assistance. Economic assistance. Humanitarian assistance. \\n\\nWe are giving more than $1 Billion in direct assistance to Ukraine. \\n\\nAnd we will continue to aid the Ukrainian people as they defend their country and to help ease their suffering.  \\n\\nLet me be clear, our forces are not engaged and will not engage in conflict with Russian forces in Ukraine.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Our forces are not going to Europe to fight in Ukraine, but to defend our NATO Allies – in the event that <PERSON> decides to keep moving west.  \\n\\nFor that purpose we’ve mobilized American ground forces, air squadrons, and ship deployments to protect NATO countries including Poland, Romania, Latvia, Lithuania, and Estonia. \\n\\nAs I have made crystal clear the United States and our Allies will defend every inch of territory of NATO countries with the full force of our collective power.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And we remain clear-eyed. The Ukrainians are fighting back with pure courage. But the next few days weeks, months, will be hard on them.  \\n\\n<PERSON><PERSON><PERSON> has unleashed violence and chaos.  But while he may make gains on the battlefield – he will pay a continuing high price over the long run. \\n\\nAnd a proud Ukrainian people, who have known 30 years  of independence, have repeatedly shown that they will not tolerate anyone who tries to take their country backwards.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='To all Americans, I will be honest with you, as I’ve always promised. A Russian dictator, invading a foreign country, has costs around the world. \\n\\nAnd I’m taking robust action to make sure the pain of our sanctions  is targeted at Russia’s economy. And I will use every tool at our disposal to protect American businesses and consumers. \\n\\nTonight, I can announce that the United States has worked with 30 other countries to release 60 Million barrels of oil from reserves around the world.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='America will lead that effort, releasing 30 Million barrels from our own Strategic Petroleum Reserve. And we stand ready to do more if necessary, unified with our allies.  \\n\\nThese steps will help blunt gas prices here at home. And I know the news about what’s happening can seem alarming. \\n\\nBut I want you to know that we are going to be okay. \\n\\nWhen the history of this era is written <PERSON>’s war on Ukraine will have left Russia weaker and the rest of the world stronger.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='While it shouldn’t have taken something so terrible for people around the world to see what’s at stake now everyone sees it clearly. \\n\\nWe see the unity among leaders of nations and a more unified Europe a more unified West. And we see unity among the people who are gathering in cities in large crowds around the world even in Russia to demonstrate their support for Ukraine.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='In the battle between democracy and autocracy, democracies are rising to the moment, and the world is clearly choosing the side of peace and security. \\n\\nThis is a real test. It’s going to take time. So let us continue to draw inspiration from the iron will of the Ukrainian people. \\n\\nTo our fellow Ukrainian Americans who forge a deep bond that connects our two nations we stand with you. \\n\\n<PERSON><PERSON><PERSON> may circle Kyiv with tanks, but he will never gain the hearts and souls of the Ukrainian people.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='He will never extinguish their love of freedom. He will never weaken the resolve of the free world. \\n\\nWe meet tonight in an America that has lived through two of the hardest years this nation has ever faced. \\n\\nThe pandemic has been punishing. \\n\\nAnd so many families are living paycheck to paycheck, struggling to keep up with the rising cost of food, gas, housing, and so much more. \\n\\nI understand.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='I understand. \\n\\nI remember when my Dad had to leave our home in Scranton, Pennsylvania to find work. I grew up in a family where if the price of food went up, you felt it. \\n\\nThat’s why one of the first things I did as President was fight to pass the American Rescue Plan.  \\n\\nBecause people were hurting. We needed to act, and we did. \\n\\nFew pieces of legislation have done more in a critical moment in our history to lift us out of crisis.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='It fueled our efforts to vaccinate the nation and combat COVID-19. It delivered immediate economic relief for tens of millions of Americans.  \\n\\nHelped put food on their table, keep a roof over their heads, and cut the cost of health insurance. \\n\\nAnd as my Dad used to say, it gave people a little breathing room.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And unlike the $2 Trillion tax cut passed in the previous administration that benefitted the top 1% of Americans, the American Rescue Plan helped working people—and left no one behind. \\n\\nAnd it worked. It created jobs. Lots of jobs. \\n\\nIn fact—our economy created over 6.5 Million new jobs just last year, more jobs created in one year  \\nthan ever before in the history of America.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Our economy grew at a rate of 5.7% last year, the strongest growth in nearly 40 years, the first step in bringing fundamental change to an economy that hasn’t worked for the working people of this nation for too long.  \\n\\nFor the past 40 years we were told that if we gave tax breaks to those at the very top, the benefits would trickle down to everyone else.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='But that trickle-down theory led to weaker economic growth, lower wages, bigger deficits, and the widest gap between those at the top and everyone else in nearly a century. \\n\\nVice President <PERSON> and I ran for office with a new economic vision for America. \\n\\nInvest in America. Educate Americans. Grow the workforce. Build the economy from the bottom up  \\nand the middle out, not from the top down.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Because we know that when the middle class grows, the poor have a ladder up and the wealthy do very well. \\n\\nAmerica used to have the best roads, bridges, and airports on Earth. \\n\\nNow our infrastructure is ranked 13th in the world. \\n\\nWe won’t be able to compete for the jobs of the 21st Century if we don’t fix that. \\n\\nThat’s why it was so important to pass the Bipartisan Infrastructure Law—the most sweeping investment to rebuild America in history.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='This was a bipartisan effort, and I want to thank the members of both parties who worked to make it happen. \\n\\nWe’re done talking about infrastructure weeks. \\n\\nWe’re going to have an infrastructure decade. \\n\\nIt is going to transform America and put us on a path to win the economic competition of the 21st Century that we face with the rest of the world—particularly with China.  \\n\\nAs I’ve told Xi Jinping, it is never a good bet to bet against the American people.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We’ll create good jobs for millions of Americans, modernizing roads, airports, ports, and waterways all across America. \\n\\nAnd we’ll do it all to withstand the devastating effects of the climate crisis and promote environmental justice.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We’ll build a national network of 500,000 electric vehicle charging stations, begin to replace poisonous lead pipes—so every child—and every American—has clean water to drink at home and at school, provide affordable high-speed internet for every American—urban, suburban, rural, and tribal communities. \\n\\n4,000 projects have already been announced. \\n\\nAnd tonight, I’m announcing that this year we will start fixing over 65,000 miles of highway and 1,500 bridges in disrepair.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='When we use taxpayer dollars to rebuild America – we are going to Buy American: buy American products to support American jobs. \\n\\nThe federal government spends about $600 Billion a year to keep the country safe and secure. \\n\\nThere’s been a law on the books for almost a century \\nto make sure taxpayers’ dollars support American jobs and businesses. \\n\\nEvery Administration says they’ll do it, but we are actually doing it.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We will buy American to make sure everything from the deck of an aircraft carrier to the steel on highway guardrails are made in America. \\n\\nBut to compete for the best jobs of the future, we also need to level the playing field with China and other competitors. \\n\\nThat’s why it is so important to pass the Bipartisan Innovation Act sitting in Congress that will make record investments in emerging technologies and American manufacturing.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Let me give you one example of why it’s so important to pass it. \\n\\nIf you travel 20 miles east of Columbus, Ohio, you’ll find 1,000 empty acres of land. \\n\\nIt won’t look like much, but if you stop and look closely, you’ll see a “Field of dreams,” the ground on which America’s future will be built. \\n\\nThis is where Intel, the American company that helped build Silicon Valley, is going to build its $20 billion semiconductor “mega site”.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Up to eight state-of-the-art factories in one place. 10,000 new good-paying jobs. \\n\\nSome of the most sophisticated manufacturing in the world to make computer chips the size of a fingertip that power the world and our everyday lives. \\n\\nSmartphones. The Internet. Technology we have yet to invent. \\n\\nBut that’s just the beginning. \\n\\nIntel’s CEO, <PERSON>, who is here tonight, told me they are ready to increase their investment from  \\n$20 billion to $100 billion.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='That would be one of the biggest investments in manufacturing in American history. \\n\\nAnd all they’re waiting for is for you to pass this bill. \\n\\nSo let’s not wait any longer. Send it to my desk. I’ll sign it.  \\n\\nAnd we will really take off. \\n\\nAnd Intel is not alone. \\n\\nThere’s something happening in America. \\n\\nJust look around and you’ll see an amazing story. \\n\\nThe rebirth of the pride that comes from stamping products “Made In America.” The revitalization of American manufacturing.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Companies are choosing to build new factories here, when just a few years ago, they would have built them overseas. \\n\\nThat’s what is happening. Ford is investing $11 billion to build electric vehicles, creating 11,000 jobs across the country. \\n\\nGM is making the largest investment in its history—$7 billion to build electric vehicles, creating 4,000 jobs in Michigan. \\n\\nAll told, we created 369,000 new manufacturing jobs in America just last year.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Powered by people I’ve met like <PERSON><PERSON><PERSON>, from generations of union steelworkers from Pittsburgh, who’s here with us tonight. \\n\\nAs Ohio Senator <PERSON><PERSON><PERSON> says, “It’s time to bury the label “Rust Belt.” \\n\\nIt’s time. \\n\\nBut with all the bright spots in our economy, record job growth and higher wages, too many families are struggling to keep up with the bills.  \\n\\nInflation is robbing them of the gains they might otherwise feel.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='I get it. That’s why my top priority is getting prices under control. \\n\\nLook, our economy roared back faster than most predicted, but the pandemic meant that businesses had a hard time hiring enough workers to keep up production in their factories. \\n\\nThe pandemic also disrupted global supply chains. \\n\\nWhen factories close, it takes longer to make goods and get them from the warehouse to the store, and prices go up. \\n\\nLook at cars.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Look at cars. \\n\\nLast year, there weren’t enough semiconductors to make all the cars that people wanted to buy. \\n\\nAnd guess what, prices of automobiles went up. \\n\\nSo—we have a choice. \\n\\nOne way to fight inflation is to drive down wages and make Americans poorer.  \\n\\nI have a better plan to fight inflation. \\n\\nLower your costs, not your wages. \\n\\nMake more cars and semiconductors in America. \\n\\nMore infrastructure and innovation in America. \\n\\nMore goods moving faster and cheaper in America.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='More jobs where you can earn a good living in America. \\n\\nAnd instead of relying on foreign supply chains, let’s make it in America. \\n\\nEconomists call it “increasing the productive capacity of our economy.” \\n\\nI call it building a better America. \\n\\nMy plan to fight inflation will lower your costs and lower the deficit. \\n\\n17 Nobel laureates in economics say my plan will ease long-term inflationary pressures. Top business leaders and most Americans support my plan. And here’s the plan:', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='First – cut the cost of prescription drugs. Just look at insulin. One in ten Americans has diabetes. In Virginia, I met a 13-year-old boy named <PERSON>.  \\n\\n<PERSON><PERSON> and his Dad both have Type 1 diabetes, which means they need insulin every day. Insulin costs about $10 a vial to make.  \\n\\nBut drug companies charge families like <PERSON> and his Dad up to 30 times more. I spoke with <PERSON>’s mom.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Imagine what it’s like to look at your child who needs insulin and have no idea how you’re going to pay for it.  \\n\\nWhat it does to your dignity, your ability to look your child in the eye, to be the parent you expect to be. \\n\\n<PERSON><PERSON><PERSON> is here with us tonight. Yesterday was his birthday. Happy birthday, buddy.  \\n\\nFor <PERSON>, and for the 200,000 other young people with Type 1 diabetes, let’s cap the cost of insulin at $35 a month so everyone can afford it.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Drug companies will still do very well. And while we’re at it let Medicare negotiate lower prices for prescription drugs, like the VA already does. \\n\\nLook, the American Rescue Plan is helping millions of families on Affordable Care Act plans save $2,400 a year on their health care premiums. Let’s close the coverage gap and make those savings permanent. \\n\\nSecond – cut energy costs for families an average of $500 a year by combatting climate change.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Let’s provide investments and tax credits to weatherize your homes and businesses to be energy efficient and you get a tax credit; double America’s clean energy production in solar, wind, and so much more;  lower the price of electric vehicles, saving you another $80 a month because you’ll never have to pay at the gas pump again. \\n\\nThird – cut the cost of child care. Many families pay up to $14,000 a year for child care per child.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Middle-class and working families shouldn’t have to pay more than 7% of their income for care of young children.  \\n\\nMy plan will cut the cost in half for most families and help parents, including millions of women, who left the workforce during the pandemic because they couldn’t afford child care, to be able to get back to work. \\n\\nMy plan doesn’t stop there. It also includes home and long-term care. More affordable housing. And Pre-K for every 3- and 4-year-old.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='All of these will lower costs. \\n\\nAnd under my plan, nobody earning less than $400,000 a year will pay an additional penny in new taxes. Nobody.  \\n\\nThe one thing all Americans agree on is that the tax system is not fair. We have to fix it.  \\n\\nI’m not looking to punish anyone. But let’s make sure corporations and the wealthiest Americans start paying their fair share. \\n\\nJust last year, 55 Fortune 500 corporations earned $40 billion in profits and paid zero dollars in federal income tax.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='That’s simply not fair. That’s why I’ve proposed a 15% minimum tax rate for corporations. \\n\\nWe got more than 130 countries to agree on a global minimum tax rate so companies can’t get out of paying their taxes at home by shipping jobs and factories overseas. \\n\\nThat’s why I’ve proposed closing loopholes so the very wealthy don’t pay a lower tax rate than a teacher or a firefighter.  \\n\\nSo that’s my plan. It will grow the economy and lower costs for families.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='So what are we waiting for? Let’s get this done. And while you’re at it, confirm my nominees to the Federal Reserve, which plays a critical role in fighting inflation.  \\n\\nMy plan will not only lower costs to give families a fair shot, it will lower the deficit. \\n\\nThe previous Administration not only ballooned the deficit with tax cuts for the very wealthy and corporations, it undermined the watchdogs whose job was to keep pandemic relief funds from being wasted.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='But in my administration, the watchdogs have been welcomed back. \\n\\nWe’re going after the criminals who stole billions in relief money meant for small businesses and millions of Americans.  \\n\\nAnd tonight, I’m announcing that the Justice Department will name a chief prosecutor for pandemic fraud. \\n\\nBy the end of this year, the deficit will be down to less than half what it was before I took office.  \\n\\nThe only president ever to cut the deficit by more than one trillion dollars in a single year.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Lowering your costs also means demanding more competition. \\n\\nI’m a capitalist, but capitalism without competition isn’t capitalism. \\n\\nIt’s exploitation—and it drives up prices. \\n\\nWhen corporations don’t have to compete, their profits go up, your prices go up, and small businesses and family farmers and ranchers go under. \\n\\nWe see it happening with ocean carriers moving goods in and out of America.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='During the pandemic, these foreign-owned companies raised prices by as much as 1,000% and made record profits. \\n\\nTonight, I’m announcing a crackdown on these companies overcharging American businesses and consumers. \\n\\nAnd as Wall Street firms take over more nursing homes, quality in those homes has gone down and costs have gone up.  \\n\\nThat ends on my watch. \\n\\nMedicare is going to set higher standards for nursing homes and make sure your loved ones get the care they deserve and expect.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We’ll also cut costs and keep the economy going strong by giving workers a fair shot, provide more training and apprenticeships, hire them based on their skills not degrees. \\n\\nLet’s pass the Paycheck Fairness Act and paid leave.  \\n\\nRaise the minimum wage to $15 an hour and extend the Child Tax Credit, so no one has to raise a family in poverty.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Let’s increase Pell Grants and increase our historic support of HBCUs, and invest in what <PERSON>—our First Lady who teaches full-time—calls America’s best-kept secret: community colleges. \\n\\nAnd let’s pass the PRO Act when a majority of workers want to form a union—they shouldn’t be stopped.  \\n\\nWhen we invest in our workers, when we build the economy from the bottom up and the middle out together, we can do something we haven’t done in a long time: build a better America.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='For more than two years, COVID-19 has impacted every decision in our lives and the life of the nation. \\n\\nAnd I know you’re tired, frustrated, and exhausted. \\n\\nBut I also know this. \\n\\nBecause of the progress we’ve made, because of your resilience and the tools we have, tonight I can say  \\nwe are moving forward safely, back to more normal routines.  \\n\\nWe’ve reached a new moment in the fight against COVID-19, with severe cases down to a level not seen since last July.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Just a few days ago, the Centers for Disease Control and Prevention—the CDC—issued new mask guidelines. \\n\\nUnder these new guidelines, most Americans in most of the country can now be mask free.   \\n\\nAnd based on the projections, more of the country will reach that point across the next couple of weeks. \\n\\nThanks to the progress we have made this past year, COVID-19 need no longer control our lives.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='I know some are talking about “living with COVID-19”. Tonight – I say that we will never just accept living with COVID-19. \\n\\nWe will continue to combat the virus as we do other diseases. And because this is a virus that mutates and spreads, we will stay on guard. \\n\\nHere are four common sense steps as we move forward safely.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='First, stay protected with vaccines and treatments. We know how incredibly effective vaccines are. If you’re vaccinated and boosted you have the highest degree of protection. \\n\\nWe will never give up on vaccinating more Americans. Now, I know parents with kids under 5 are eager to see a vaccine authorized for their children. \\n\\nThe scientists are working hard to get that done and we’ll be ready with plenty of vaccines when they do.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We’re also ready with anti-viral treatments. If you get COVID-19, the Pfizer pill reduces your chances of ending up in the hospital by 90%.  \\n\\nWe’ve ordered more of these pills than anyone in the world. And Pfizer is working overtime to get us 1 Million pills this month and more than double that next month.  \\n\\nAnd we’re launching the “Test to Treat” initiative so people can get tested at a pharmacy, and if they’re positive, receive antiviral pills on the spot at no cost.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='If you’re immunocompromised or have some other vulnerability, we have treatments and free high-quality masks. \\n\\nWe’re leaving no one behind or ignoring anyone’s needs as we move forward. \\n\\nAnd on testing, we have made hundreds of millions of tests available for you to order for free.   \\n\\nEven if you already ordered free tests tonight, I am announcing that you can order more from covidtests.gov starting next week.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Second – we must prepare for new variants. Over the past year, we’ve gotten much better at detecting new variants. \\n\\nIf necessary, we’ll be able to deploy new vaccines within 100 days instead of many more months or years.  \\n\\nAnd, if Congress provides the funds we need, we’ll have new stockpiles of tests, masks, and pills ready if needed. \\n\\nI cannot promise a new variant won’t come. But I can promise you we’ll do everything within our power to be ready if it does.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Third – we can end the shutdown of schools and businesses. We have the tools we need. \\n\\nIt’s time for Americans to get back to work and fill our great downtowns again.  People working from home can feel safe to begin to return to the office.   \\n\\nWe’re doing that here in the federal government. The vast majority of federal workers will once again work in person. \\n\\nOur schools are open. Let’s keep it that way. Our kids need to be in school.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And with 75% of adult Americans fully vaccinated and hospitalizations down by 77%, most Americans can remove their masks, return to work, stay in the classroom, and move forward safely. \\n\\nWe achieved this because we provided free vaccines, treatments, tests, and masks. \\n\\nOf course, continuing this costs money. \\n\\nI will soon send Congress a request. \\n\\nThe vast majority of Americans have used these tools and may want to again, so I expect Congress to pass it quickly.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Fourth, we will continue vaccinating the world.     \\n\\nWe’ve sent 475 Million vaccine doses to 112 countries, more than any other nation. \\n\\nAnd we won’t stop. \\n\\nWe have lost so much to COVID-19. Time with one another. And worst of all, so much loss of life. \\n\\nLet’s use this moment to reset. Let’s stop looking at COVID-19 as a partisan dividing line and see it for what it is: A God-awful disease.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Let’s stop seeing each other as enemies, and start seeing each other for who we really are: Fellow Americans.  \\n\\nWe can’t change how divided we’ve been. But we can change how we move forward—on COVID-19 and other issues we must face together. \\n\\nI recently visited the New York City Police Department days after the funerals of Officer <PERSON><PERSON><PERSON> and his partner, Officer <PERSON>. \\n\\nT<PERSON> were responding to a 9-1-1 call when a man shot and killed them with a stolen gun.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Officer <PERSON><PERSON> was 27 years old. \\n\\nOffice<PERSON> was 22. \\n\\nBoth Dominican Americans who’d grown up on the same streets they later chose to patrol as police officers. \\n\\nI spoke with their families and told them that we are forever in debt for their sacrifice, and we will carry on their mission to restore the trust and safety every community deserves. \\n\\nI’ve worked on these issues a long time.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='I’ve worked on these issues a long time. \\n\\nI know what works: Investing in crime prevention and community police officers who’ll walk the beat, who’ll know the neighborhood, and who can restore trust and safety. \\n\\nSo let’s not abandon our streets. Or choose between safety and equal justice. \\n\\nLet’s come together to protect our communities, restore trust, and hold law enforcement accountable.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='That’s why the Justice Department required body cameras, banned chokeholds, and restricted no-knock warrants for its officers. \\n\\nThat’s why the American Rescue Plan provided $350 Billion that cities, states, and counties can use to hire more police and invest in proven strategies like community violence interruption—trusted messengers breaking the cycle of violence and trauma and giving young people hope.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We should all agree: The answer is not to Defund the police. The answer is to FUND the police with the resources and training they need to protect our communities. \\n\\nI ask Democrats and Republicans alike: Pass my budget and keep our neighborhoods safe.  \\n\\nAnd I will keep doing everything in my power to crack down on gun trafficking and ghost guns you can buy online and make at home—they have no serial numbers and can’t be traced.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And I ask Congress to pass proven measures to reduce gun violence. Pass universal background checks. Why should anyone on a terrorist list be able to purchase a weapon? \\n\\nBan assault weapons and high-capacity magazines. \\n\\nRepeal the liability shield that makes gun manufacturers the only industry in America that can’t be sued. \\n\\nThese laws don’t infringe on the Second Amendment. They save lives.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='The most fundamental right in America is the right to vote – and to have it counted. And it’s under assault. \\n\\nIn state after state, new laws have been passed, not only to suppress the vote, but to subvert entire elections. \\n\\nWe cannot let this happen. \\n\\nTonight. I call on the Senate to: Pass the Freedom to Vote Act. Pass the John Lewis Voting Rights Act. And while you’re at it, pass the Disclose Act so Americans can know who is funding our elections.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Tonight, I’d like to honor someone who has dedicated his life to serve this country: Justice <PERSON>—an Army veteran, Constitutional scholar, and retiring Justice of the United States Supreme Court. Justice <PERSON>, thank you for your service. \\n\\nOne of the most serious constitutional responsibilities a President has is nominating someone to serve on the United States Supreme Court.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>. One of our nation’s top legal minds, who will continue <PERSON>’s legacy of excellence.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='A former top litigator in private practice. A former federal public defender. And from a family of public school educators and police officers. A consensus builder. Since she’s been nominated, she’s received a broad range of support—from the Fraternal Order of Police to former judges appointed by Democrats and Republicans. \\n\\nAnd if we are to advance liberty and justice, we need to secure the Border and fix the immigration system.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We can do both. At our border, we’ve installed new technology like cutting-edge scanners to better detect drug smuggling.  \\n\\nWe’ve set up joint patrols with Mexico and Guatemala to catch more human traffickers.  \\n\\nWe’re putting in place dedicated immigration judges so families fleeing persecution and violence can have their cases heard faster. \\n\\nWe’re securing commitments and supporting partners in South and Central America to host more refugees and secure their own borders.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We can do all this while keeping lit the torch of liberty that has led generations of immigrants to this land—my forefathers and so many of yours. \\n\\nProvide a pathway to citizenship for Dreamers, those on temporary status, farm workers, and essential workers. \\n\\nRevise our laws so businesses have the workers they need and families don’t wait decades to reunite. \\n\\nIt’s not only the right thing to do—it’s the economically smart thing to do.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='That’s why immigration reform is supported by everyone from labor unions to religious leaders to the U.S. Chamber of Commerce. \\n\\nLet’s get it done once and for all. \\n\\nAdvancing liberty and justice also requires protecting the rights of women. \\n\\nThe constitutional right affirmed in <PERSON> v. <PERSON>—standing precedent for half a century—is under attack as never before.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='If we want to go forward—not backward—we must protect access to health care. Preserve a woman’s right to choose. And let’s continue to advance maternal health care in America. \\n\\nAnd for our LGBTQ+ Americans, let’s finally get the bipartisan Equality Act to my desk. The onslaught of state laws targeting transgender Americans and their families is wrong.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='As I said last year, especially to our younger transgender Americans, I will always have your back as your President, so you can be yourself and reach your God-given potential. \\n\\nWhile it often appears that we never agree, that isn’t true. I signed 80 bipartisan bills into law last year. From preventing government shutdowns to protecting Asian-Americans from still-too-common hate crimes to reforming military justice.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And soon, we’ll strengthen the Violence Against Women Act that I first wrote three decades ago. It is important for us to show the nation that we can come together and do big things. \\n\\nSo tonight I’m offering a Unity Agenda for the Nation. Four big things we can do together.  \\n\\nFirst, beat the opioid epidemic. \\n\\nThere is so much we can do. Increase funding for prevention, treatment, harm reduction, and recovery.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Get rid of outdated rules that stop doctors from prescribing treatments. And stop the flow of illicit drugs by working with state and local law enforcement to go after traffickers. \\n\\nIf you’re suffering from addiction, know you are not alone. I believe in recovery, and I celebrate the 23 million Americans in recovery. \\n\\nSecond, let’s take on mental health. Especially among our children, whose lives and education have been turned upside down.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='The American Rescue Plan gave schools money to hire teachers and help students make up for lost learning.  \\n\\nI urge every parent to make sure your school does just that. And we can all play a part—sign up to be a tutor or a mentor. \\n\\nChildren were also struggling before the pandemic. Bullying, violence, trauma, and the harms of social media.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='As <PERSON>, who is here with us tonight, has shown, we must hold social media platforms accountable for the national experiment they’re conducting on our children for profit. \\n\\nIt’s time to strengthen privacy protections, ban targeted advertising to children, demand tech companies stop collecting personal data on our children.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And let’s get all Americans the mental health services they need. More people they can turn to for help, and full parity between physical and mental health care. \\n\\nThird, support our veterans. \\n\\nVeterans are the best of us. \\n\\nI’ve always believed that we have a sacred obligation to equip all those we send to war and care for them and their families when they come home.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='My administration is providing assistance with job training and housing, and now helping lower-income veterans get VA care debt-free.  \\n\\nOur troops in Iraq and Afghanistan faced many dangers. \\n\\nO<PERSON> was stationed at bases and breathing in toxic smoke from “burn pits” that incinerated wastes of war—medical and hazard material, jet fuel, and more. \\n\\nWhen they came home, many of the world’s fittest and best trained warriors were never the same. \\n\\nHeadaches. Numbness. Dizziness.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Headaches. Numbness. Dizziness. \\n\\nA cancer that would put them in a flag-draped coffin. \\n\\nI know. \\n\\nOne of those soldiers was my son Major <PERSON>. \\n\\nWe don’t know for sure if a burn pit was the cause of his brain cancer, or the diseases of so many of our troops. \\n\\nBut I’m committed to finding out everything we can. \\n\\nCommitted to military families like <PERSON> from Ohio. \\n\\nThe widow of Sergeant First <PERSON>.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='He was born a soldier. Army National Guard. Combat medic in Kosovo and Iraq. \\n\\nStationed near Baghdad, just yards from burn pits the size of football fields. \\n\\n<PERSON><PERSON><PERSON>’s widow <PERSON> is here with us tonight. They loved going to Ohio State football games. He loved building Legos with their daughter. \\n\\nBut cancer from prolonged exposure to burn pits ravaged <PERSON>’s lungs and body. \\n\\nD<PERSON><PERSON> says <PERSON> was a fighter to the very end. \\n\\n<PERSON>e didn’t know how to stop fighting, and neither did she.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Through her pain she found purpose to demand we do better. \\n\\nTonight, <PERSON>—we are. \\n\\nThe VA is pioneering new ways of linking toxic exposures to diseases, already helping more veterans get benefits. \\n\\nAnd tonight, I’m announcing we’re expanding eligibility to veterans suffering from nine respiratory cancers.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='I’m also calling on Congress: pass a law to make sure veterans devastated by toxic exposures in Iraq and Afghanistan finally get the benefits and comprehensive health care they deserve. \\n\\nAnd fourth, let’s end cancer as we know it. \\n\\nThis is personal to me and <PERSON>, to <PERSON><PERSON>, and to so many of you. \\n\\nCancer is the #2 cause of death in America–second only to heart disease.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Last month, I announced our plan to supercharge  \\nthe Cancer Moonshot that President <PERSON> asked me to lead six years ago. \\n\\nOur goal is to cut the cancer death rate by at least 50% over the next 25 years, turn more cancers from death sentences into treatable diseases.  \\n\\nMore support for patients and families. \\n\\nTo get there, I call on Congress to fund ARPA-H, the Advanced Research Projects Agency for Health.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='It’s based on DARPA—the Defense Department project that led to the Internet, GPS, and so much more.  \\n\\nARPA-H will have a singular purpose—to drive breakthroughs in cancer, Alzheimer’s, diabetes, and more. \\n\\nA unity agenda for the nation. \\n\\nWe can do this. \\n\\nMy fellow Americans—tonight , we have gathered in a sacred space—the citadel of our democracy. \\n\\nIn this Capitol, generation after generation, Americans have debated great questions amid great strife, and have done great things.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We have fought for freedom, expanded liberty, defeated totalitarianism and terror. \\n\\nAnd built the strongest, freest, and most prosperous nation the world has ever known. \\n\\nNow is the hour. \\n\\nOur moment of responsibility. \\n\\nOur test of resolve and conscience, of history itself. \\n\\nIt is in this moment that our character is formed. Our purpose is found. Our future is forged. \\n\\nWell I know this nation.  \\n\\nWe will meet the test.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We will meet the test. \\n\\nTo protect freedom and liberty, to expand fairness and opportunity. \\n\\nWe will save democracy. \\n\\nAs hard as these times have been, I am more optimistic about America today than I have been my whole life. \\n\\nBecause I see the future that is within our grasp. \\n\\nBecause I know there is simply nothing beyond our capacity. \\n\\nWe are the only nation on Earth that has always turned every crisis we have faced into an opportunity.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='The only nation that can be defined by a single word: possibilities. \\n\\nSo on this night, in our 245th year as a nation, I have come to report on the State of the Union. \\n\\nAnd my report is this: the State of the Union is strong—because you, the American people, are strong. \\n\\nWe are stronger today than we were a year ago. \\n\\nAnd we will be stronger a year from now than we are today. \\n\\nNow is our moment to meet and overcome the challenges of our time. \\n\\nAnd we will, as one people. \\n\\nOne America.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And we will, as one people. \\n\\nOne America. \\n\\nThe United States of America. \\n\\nMay God bless you all. May God protect our troops.', metadata={'source': '/content/state_of_the_union.txt'})]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["text_chunks"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_jW9bahwyrpF", "outputId": "d04660f4-002d-4dca-c0fb-2721f729c451"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Please rise if you are able and show that, Yes, we the United States of America stand with the Ukrainian people. \n", "\n", "Throughout our history we’ve learned this lesson when dictators do not pay a price for their aggression they cause more chaos.   \n", "\n", "They keep moving.   \n", "\n", "And the costs and the threats to America and the world keep rising.   \n", "\n", "That’s why the NATO Alliance was created to secure peace and stability in Europe after World War 2.\n"]}], "source": ["print(text_chunks[3].page_content)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"id": "Ur_SVI_CzWFw"}, "outputs": [], "source": ["from langchain.embeddings import OpenAIEmbeddings\n", "from langchain.vectorstores import FAISS"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7v6pC9yrzmBA", "outputId": "da9de162-bbbf-4fd1-9dcf-038637a841ef"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/langchain_core/_api/deprecation.py:117: LangChainDeprecationWarning: The class `langchain_community.embeddings.openai.OpenAIEmbeddings` was deprecated in langchain-community 0.0.9 and will be removed in 0.2.0. An updated version of the class exists in the langchain-openai package and should be used instead. To use it run `pip install -U langchain-openai` and import as `from langchain_openai import OpenAIEmbeddings`.\n", "  warn_deprecated(\n"]}], "source": ["embeddings=OpenAIEmbeddings(openai_api_key=OPENAI_API_KEY)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "K1z0Nqe8z1B3", "outputId": "e204d916-da6a-4d6a-bc9b-c91ab94af523"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting faiss-cpu\n", "  Downloading faiss_cpu-1.8.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (27.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m27.0/27.0 MB\u001b[0m \u001b[31m28.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from faiss-cpu) (1.25.2)\n", "Installing collected packages: faiss-cpu\n", "Successfully installed faiss-cpu-1.8.0\n"]}], "source": ["!pip install faiss-cpu"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"id": "zl-jy02QzrJ7"}, "outputs": [], "source": ["vectorstore=FAISS.from_documents(text_chunks, embeddings)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"id": "YQGI-QvHzyhp"}, "outputs": [], "source": ["retriever=vectorstore.as_retriever()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"id": "W3fHisQz0XSn"}, "outputs": [], "source": ["from langchain.prompts import ChatPromptTemplate"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"id": "i79AEhET0rJY"}, "outputs": [], "source": ["template=\"\"\"You are an assistant for question-answering tasks.\n", "Use the following pieces of retrieved context to answer the question.\n", "If you don't know the answer, just say that you don't know.\n", "Use ten sentences maximum and keep the answer concise.\n", "Question: {question}\n", "Context: {context}\n", "Answer:\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 35, "metadata": {"id": "XjPxHyCq0xNB"}, "outputs": [], "source": ["prompt=ChatPromptTemplate.from_template(template)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"id": "1jDm8miC0zCY"}, "outputs": [], "source": ["from langchain.chat_models import ChatOpenAI\n", "from langchain.schema.runnable import RunnablePassthrough\n", "from langchain.schema.output_parser import StrOutputParser"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"id": "NGR2XWLh1t9S"}, "outputs": [], "source": ["output_parser=StrOutputParser()"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yMCDVqyM1Ma2", "outputId": "a68041b0-c5f1-4e9a-99d1-3d3a19ab6c66"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/langchain_core/_api/deprecation.py:117: LangChainDeprecationWarning: The class `langchain_community.chat_models.openai.ChatOpenAI` was deprecated in langchain-community 0.0.10 and will be removed in 0.2.0. An updated version of the class exists in the langchain-openai package and should be used instead. To use it run `pip install -U langchain-openai` and import as `from langchain_openai import ChatOpenAI`.\n", "  warn_deprecated(\n"]}], "source": ["llm_model=ChatOpenAI(openai_api_key=OPENAI_API_KEY,model_name=\"gpt-3.5-turbo\")"]}, {"cell_type": "code", "execution_count": 40, "metadata": {"id": "FJjxzAZn1p6-"}, "outputs": [], "source": ["rag_chain = (\n", "    {\"context\": retriever,  \"question\": RunnablePassthrough()}\n", "    | prompt\n", "    | llm_model\n", "    | output_parser\n", ")"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 157}, "id": "pr1POQp02Kmo", "outputId": "add0bf05-9483-4063-fe06-6af3d34a2638"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'The United States is supporting Ukraine economically and militarily by providing military assistance, economic assistance, and humanitarian assistance. They are giving more than $1 Billion in direct assistance to Ukraine. US forces are not engaged and will not engage in conflict with Russian forces in Ukraine. The world is choosing the side of peace and security in the battle between democracy and autocracy. The NATO Alliance was created to secure peace and stability in Europe after World War 2. Ukrainian citizens are showing incredible resilience and bravery in defending their homeland. President <PERSON><PERSON><PERSON><PERSON> expressed confidence that \"Light will win over darkness\" in the struggle. The United States is sending a clear signal of support to Ukraine and the world.'"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["rag_chain.invoke(\"How is the United States supporting Ukraine economically and militarily?\")"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 157}, "id": "ekErMhoI2wtZ", "outputId": "f5cafd34-d185-404f-c4ef-218b4e25458a"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["\"The U.S. is addressing rising gas prices by releasing 30 million barrels from the Strategic Petroleum Reserve and is prepared to do more if needed, in coordination with allies. This action aims to help reduce gas prices domestically. The U.S. is taking steps to ensure that the impact of sanctions on Russia's economy is felt, protecting American businesses and consumers. Additionally, the United States has collaborated with 30 other countries to release 60 million barrels of oil globally. There is a focus on increasing domestic production, creating more jobs, and improving infrastructure to combat inflation. The plan to fight inflation involves lowering costs, not wages, and increasing the productive capacity of the economy. This approach is supported by economists, business leaders, and the majority of Americans.\""]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["rag_chain.invoke(\"What action is the U.S. taking to address rising gas prices?\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "smZhFGIe3EB6"}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 0}