{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"1029b02ea1974b3fbf6ae5fd4b672e60": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c13b0e0a35994c50a4591ec48bbe9ec8", "IPY_MODEL_107eadc4300c44a7889f09672eca02fe", "IPY_MODEL_bc33b05ed006436d8aafa97a0c51abde"], "layout": "IPY_MODEL_f2d76e677e004baa8a806e8a2b91a01c"}}, "c13b0e0a35994c50a4591ec48bbe9ec8": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c3fbfd22a5fb4d47a7c5e75dde2289f6", "placeholder": "​", "style": "IPY_MODEL_f962fe8da6d74af3a24334f3f3024c8a", "value": "Downloading readme: 100%"}}, "107eadc4300c44a7889f09672eca02fe": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_86c68952a8934fd3a0c2d9e223ef4f77", "max": 6176, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e4e9cfe75b614823b6ee7aa5c3fa2d77", "value": 6176}}, "bc33b05ed006436d8aafa97a0c51abde": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7b4249a9e72d4780867cf742417339f3", "placeholder": "​", "style": "IPY_MODEL_c774b74fe854463f89e54cbd3d73e7e9", "value": " 6.18k/6.18k [00:00&lt;00:00, 108kB/s]"}}, "f2d76e677e004baa8a806e8a2b91a01c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c3fbfd22a5fb4d47a7c5e75dde2289f6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f962fe8da6d74af3a24334f3f3024c8a": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "86c68952a8934fd3a0c2d9e223ef4f77": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e4e9cfe75b614823b6ee7aa5c3fa2d77": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7b4249a9e72d4780867cf742417339f3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c774b74fe854463f89e54cbd3d73e7e9": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "25bd324303d14d0aa32494b014958e8e": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_295a7246575c455ca4ac15f6c331b676", "IPY_MODEL_7268522b6cce4bbbb991cb64742f08c2", "IPY_MODEL_d3567008d2da4c86a992c80c00eb8737"], "layout": "IPY_MODEL_4f67676d6d86488cbaffd6254c3fe5db"}}, "295a7246575c455ca4ac15f6c331b676": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1eb727fe9d5a4bd89b14c5f35a549f10", "placeholder": "​", "style": "IPY_MODEL_688788bbfa1f48d6829d2c60121379aa", "value": "Downloading data: 100%"}}, "7268522b6cce4bbbb991cb64742f08c2": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_de7f75132a8a4a458807209f8eec458d", "max": 42271667, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_6048948e3c044c94b7490cfd250f2ffd", "value": 42271667}}, "d3567008d2da4c86a992c80c00eb8737": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c8f4ba3150e648518f2ae1706721c505", "placeholder": "​", "style": "IPY_MODEL_434997a3c6524cc1859c86e354dbb7ec", "value": " 42.3M/42.3M [00:03&lt;00:00, 16.8MB/s]"}}, "4f67676d6d86488cbaffd6254c3fe5db": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1eb727fe9d5a4bd89b14c5f35a549f10": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "688788bbfa1f48d6829d2c60121379aa": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "de7f75132a8a4a458807209f8eec458d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6048948e3c044c94b7490cfd250f2ffd": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c8f4ba3150e648518f2ae1706721c505": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "434997a3c6524cc1859c86e354dbb7ec": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b781dfe891cc4676b0827a0e1d86fc9e": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_72306a6b64ef4a4e9b3a190cb9bbf971", "IPY_MODEL_84cc9e635d4046c0a0e3904f35d001fc", "IPY_MODEL_1b9a661e3fa446aa8be565552a853295"], "layout": "IPY_MODEL_8b6dc26221144b24950c1da666d058eb"}}, "72306a6b64ef4a4e9b3a190cb9bbf971": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5da7b973ca35496da771a8c84971c9eb", "placeholder": "​", "style": "IPY_MODEL_7b83376fe1804ff9bd999b6ea579706a", "value": "Generating train split: 100%"}}, "84cc9e635d4046c0a0e3904f35d001fc": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d89b4f6b5b994ea5a157edecd85b7f3b", "max": 1500, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_78e621d0575b4652ad550103796a9003", "value": 1500}}, "1b9a661e3fa446aa8be565552a853295": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bc2e1d0bff6540b2a61768fbfe79cf59", "placeholder": "​", "style": "IPY_MODEL_a29102b7ed40424db2b00bcdbcc6bdb3", "value": " 1500/1500 [00:02&lt;00:00, 669.15 examples/s]"}}, "8b6dc26221144b24950c1da666d058eb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5da7b973ca35496da771a8c84971c9eb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7b83376fe1804ff9bd999b6ea579706a": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d89b4f6b5b994ea5a157edecd85b7f3b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "78e621d0575b4652ad550103796a9003": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "bc2e1d0bff6540b2a61768fbfe79cf59": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a29102b7ed40424db2b00bcdbcc6bdb3": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c82b8a2349344c249a986dcc16927495": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6eb99d299ae14a8fb14ccb3cd38d6bf9", "IPY_MODEL_98a18f3c217642caac971bc91cfa7c3f", "IPY_MODEL_2f1be0c2d06648bab350b8503c41d459"], "layout": "IPY_MODEL_d76a6a94dcbf42648361ddf423d7f969"}}, "6eb99d299ae14a8fb14ccb3cd38d6bf9": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b1395879808b4142be236259e1c516e0", "placeholder": "​", "style": "IPY_MODEL_3a281b8d0f41493db24974880c4a7c6c", "value": "modules.json: 100%"}}, "98a18f3c217642caac971bc91cfa7c3f": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dacc696753d9475d886187f05e40ee7a", "max": 385, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9191ea078c3746dba4bb3f4900285bb8", "value": 385}}, "2f1be0c2d06648bab350b8503c41d459": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_18176f258c004d7a98a25930a5b057cc", "placeholder": "​", "style": "IPY_MODEL_7b32eedf72004288a702e58ee4a2408c", "value": " 385/385 [00:00&lt;00:00, 13.7kB/s]"}}, "d76a6a94dcbf42648361ddf423d7f969": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b1395879808b4142be236259e1c516e0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3a281b8d0f41493db24974880c4a7c6c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "dacc696753d9475d886187f05e40ee7a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9191ea078c3746dba4bb3f4900285bb8": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "18176f258c004d7a98a25930a5b057cc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7b32eedf72004288a702e58ee4a2408c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "36effe5381924399af196a6f24fd98e8": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ff28f9fa73224896983586a960e11fd4", "IPY_MODEL_e00d2c61d5844d48802b16f02786723e", "IPY_MODEL_7f2df9a4be9a45fa96eb3c08df38f3e1"], "layout": "IPY_MODEL_47dfcc76978f497fbb8f77449eba41f4"}}, "ff28f9fa73224896983586a960e11fd4": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_313d6c00af044940b2e1d8245fbf1837", "placeholder": "​", "style": "IPY_MODEL_8f7bd91baea24e66afe51a1380085c91", "value": "README.md: 100%"}}, "e00d2c61d5844d48802b16f02786723e": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_24e00d4b6656436abdebeb694ccd8980", "max": 67863, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_8de3cdd3e7ac4ce4825f2d5754acaecd", "value": 67863}}, "7f2df9a4be9a45fa96eb3c08df38f3e1": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1d0ab759a2404b8b88db0fe0d752b0ca", "placeholder": "​", "style": "IPY_MODEL_5595e14cbc8746caaaf08ced876455a5", "value": " 67.9k/67.9k [00:00&lt;00:00, 342kB/s]"}}, "47dfcc76978f497fbb8f77449eba41f4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "313d6c00af044940b2e1d8245fbf1837": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8f7bd91baea24e66afe51a1380085c91": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "24e00d4b6656436abdebeb694ccd8980": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8de3cdd3e7ac4ce4825f2d5754acaecd": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "1d0ab759a2404b8b88db0fe0d752b0ca": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5595e14cbc8746caaaf08ced876455a5": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a0e4ef164a394d2e990ebd1cb81ee41c": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9459985f94e541dbafd69fa36fa5023f", "IPY_MODEL_3a3ad499ffc14f70929ffe79bb1eb7c0", "IPY_MODEL_c98f5dae052c46e0b38f2c4839268fc5"], "layout": "IPY_MODEL_1166c0982782442ab5bfb36a117d324d"}}, "9459985f94e541dbafd69fa36fa5023f": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7f5914088bff434b9c1134ba21a7c961", "placeholder": "​", "style": "IPY_MODEL_fe1ca2c1e7ca4ba1953e14bef21a34af", "value": "sentence_bert_config.json: 100%"}}, "3a3ad499ffc14f70929ffe79bb1eb7c0": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9fb5fd20dbcb4e2bb8b41a1c021cebd1", "max": 57, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e03556fd85e04833981746ea0c1a68bb", "value": 57}}, "c98f5dae052c46e0b38f2c4839268fc5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2654a7026b0d4df4a63638c1ce1eab71", "placeholder": "​", "style": "IPY_MODEL_8ed98dbfc5f449f1a4dc1753e4dc232a", "value": " 57.0/57.0 [00:00&lt;00:00, 3.64kB/s]"}}, "1166c0982782442ab5bfb36a117d324d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7f5914088bff434b9c1134ba21a7c961": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fe1ca2c1e7ca4ba1953e14bef21a34af": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9fb5fd20dbcb4e2bb8b41a1c021cebd1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e03556fd85e04833981746ea0c1a68bb": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2654a7026b0d4df4a63638c1ce1eab71": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8ed98dbfc5f449f1a4dc1753e4dc232a": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "989ff4be9d8e4026bcffa4e13bd95088": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_15db6632bf294345acda1b9c4879c6a5", "IPY_MODEL_2afffa82d42a4b90b76935a0b1683ca5", "IPY_MODEL_657ca126c115418e85de0c8f19c4adcb"], "layout": "IPY_MODEL_d2a936181416420389f6678c4534fb72"}}, "15db6632bf294345acda1b9c4879c6a5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b0a4942167b74246a37910154809f9e2", "placeholder": "​", "style": "IPY_MODEL_14c4e67e040e4e2dbb8f0b968670a481", "value": "config.json: 100%"}}, "2afffa82d42a4b90b76935a0b1683ca5": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_51a4ebef5d37444ab9351cf55272b1ea", "max": 619, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5cc49be8b7af4c8d853074f0d0bb7873", "value": 619}}, "657ca126c115418e85de0c8f19c4adcb": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9bb202e839634afe87f5ce96d7a5118d", "placeholder": "​", "style": "IPY_MODEL_023bd7eea0164255aad63774e688f5c7", "value": " 619/619 [00:00&lt;00:00, 29.3kB/s]"}}, "d2a936181416420389f6678c4534fb72": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b0a4942167b74246a37910154809f9e2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "14c4e67e040e4e2dbb8f0b968670a481": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "51a4ebef5d37444ab9351cf55272b1ea": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5cc49be8b7af4c8d853074f0d0bb7873": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9bb202e839634afe87f5ce96d7a5118d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "023bd7eea0164255aad63774e688f5c7": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "bf2a331d5b384543bc248167afe08300": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_bd8c7ceb1e0a4bbc8582b22d9442c9b2", "IPY_MODEL_85c7ed73dc544ca38f8e71a5f38bf0a1", "IPY_MODEL_39621c73b3cd430a81ba8dc5d46c8f9a"], "layout": "IPY_MODEL_d2f67f6c5d5e427b8327f4f75c0f32c7"}}, "bd8c7ceb1e0a4bbc8582b22d9442c9b2": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2f64d610e4cb494aa12b459b9e60f637", "placeholder": "​", "style": "IPY_MODEL_b0aa660e14714c95bcf9fd619a75e442", "value": "model.safetensors: 100%"}}, "85c7ed73dc544ca38f8e71a5f38bf0a1": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b51755c4988046d4b74ed4041cde2af6", "max": 670332568, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e7ad44bffe0049d589a44ae4d043fc08", "value": 670332568}}, "39621c73b3cd430a81ba8dc5d46c8f9a": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d3df39ef598b4e7689f2cd5470fbcfc8", "placeholder": "​", "style": "IPY_MODEL_5c4878eb5178492f93158b0aeff1415d", "value": " 670M/670M [00:11&lt;00:00, 62.3MB/s]"}}, "d2f67f6c5d5e427b8327f4f75c0f32c7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2f64d610e4cb494aa12b459b9e60f637": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b0aa660e14714c95bcf9fd619a75e442": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b51755c4988046d4b74ed4041cde2af6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e7ad44bffe0049d589a44ae4d043fc08": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d3df39ef598b4e7689f2cd5470fbcfc8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5c4878eb5178492f93158b0aeff1415d": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "57f48528f7ab479a933a53b1eabd9793": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e1f26d4b639345fa91078fc6915478a8", "IPY_MODEL_8bc6f8ff00f941558623c1e73111e9f4", "IPY_MODEL_09741a07a2d143a5a6e3104f6806f7b2"], "layout": "IPY_MODEL_f095f95c046b45cbbc1f270b5638896f"}}, "e1f26d4b639345fa91078fc6915478a8": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_550239efa14b4150869ccd7fa6fafa79", "placeholder": "​", "style": "IPY_MODEL_f8de658756b6435cbab5da7a08246e4b", "value": "tokenizer_config.json: 100%"}}, "8bc6f8ff00f941558623c1e73111e9f4": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c35ba91f4da541c0b952c5ad7a15b2b5", "max": 342, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e9aef987924b4c5d9981ba53daebcc25", "value": 342}}, "09741a07a2d143a5a6e3104f6806f7b2": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7baaf7fcfc404271b3dd21079f6c5e14", "placeholder": "​", "style": "IPY_MODEL_76314a63041e4fc0aec7c20b9c1c876c", "value": " 342/342 [00:00&lt;00:00, 21.2kB/s]"}}, "f095f95c046b45cbbc1f270b5638896f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "550239efa14b4150869ccd7fa6fafa79": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f8de658756b6435cbab5da7a08246e4b": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c35ba91f4da541c0b952c5ad7a15b2b5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e9aef987924b4c5d9981ba53daebcc25": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7baaf7fcfc404271b3dd21079f6c5e14": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "76314a63041e4fc0aec7c20b9c1c876c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0d5e34d133234fefbf36c9b1e532f11c": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_fbef9146d0eb4cdeb6d1ed1e970e32ac", "IPY_MODEL_b3f6a640374a45899cd7e65754c1b433", "IPY_MODEL_429e3964ab3143c4a651cee20d6b1dea"], "layout": "IPY_MODEL_d5f9f7b3b372493aa241dee207ff5b37"}}, "fbef9146d0eb4cdeb6d1ed1e970e32ac": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_70286cceefc04519ada7b063ae33c90d", "placeholder": "​", "style": "IPY_MODEL_aab96e47d0f54292be0204564ab6da13", "value": "vocab.txt: 100%"}}, "b3f6a640374a45899cd7e65754c1b433": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bf40d9868e824e4188fc3185a8d2a6c2", "max": 231508, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_facf4c49b7a744f4ba4d327e5b633381", "value": 231508}}, "429e3964ab3143c4a651cee20d6b1dea": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_51dcb1403d0f4dd0aa70838a7088aca4", "placeholder": "​", "style": "IPY_MODEL_a3ea452bb9dd4cf09d24682730230fc2", "value": " 232k/232k [00:00&lt;00:00, 579kB/s]"}}, "d5f9f7b3b372493aa241dee207ff5b37": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "70286cceefc04519ada7b063ae33c90d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "aab96e47d0f54292be0204564ab6da13": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "bf40d9868e824e4188fc3185a8d2a6c2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "facf4c49b7a744f4ba4d327e5b633381": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "51dcb1403d0f4dd0aa70838a7088aca4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a3ea452bb9dd4cf09d24682730230fc2": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "adb01ed209ff44c59394971bd6a16970": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_85ba60f7954b46b6bdb77138f8181c62", "IPY_MODEL_605f1c241c5c4f50bed9b57fdf040a91", "IPY_MODEL_2ebf049412ff42778edbfc98583d258c"], "layout": "IPY_MODEL_906b2f45d1524449807c15dd1ce822e5"}}, "85ba60f7954b46b6bdb77138f8181c62": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f0ffd82f413f4cd1a3c6e39d4f0c6543", "placeholder": "​", "style": "IPY_MODEL_84aa43b0396f485f80e7a2a8aef0a617", "value": "tokenizer.json: 100%"}}, "605f1c241c5c4f50bed9b57fdf040a91": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_760a9421144b4f4b952f893ba57deeac", "max": 711661, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a3f16e0d255b41bcb4c73a59518bbdfe", "value": 711661}}, "2ebf049412ff42778edbfc98583d258c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b46b4a4a6ce74641995d722292cf446b", "placeholder": "​", "style": "IPY_MODEL_f24598749bf440738aa790b60845ba16", "value": " 712k/712k [00:00&lt;00:00, 1.78MB/s]"}}, "906b2f45d1524449807c15dd1ce822e5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f0ffd82f413f4cd1a3c6e39d4f0c6543": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "84aa43b0396f485f80e7a2a8aef0a617": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "760a9421144b4f4b952f893ba57deeac": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a3f16e0d255b41bcb4c73a59518bbdfe": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b46b4a4a6ce74641995d722292cf446b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f24598749bf440738aa790b60845ba16": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "75f0a82140d2408b8cfc968e65376568": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5be225e39c624163bdfc5e010d93026c", "IPY_MODEL_dd38b9e01bd94ce19294398200652271", "IPY_MODEL_bead11d2b0064cec8f9c8731b2316589"], "layout": "IPY_MODEL_04ec6699d63a453cb7a124cc57e90d54"}}, "5be225e39c624163bdfc5e010d93026c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2b1c0e48ea7149e0aef6b4d498ed6e8d", "placeholder": "​", "style": "IPY_MODEL_ee1b06c5bd7048fb9e39c3c304ee2169", "value": "special_tokens_map.json: 100%"}}, "dd38b9e01bd94ce19294398200652271": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8605aa79cbc14a83a4c44a5a577de9c7", "max": 125, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7ed3aa5d8f4547de978ef82a61210eb0", "value": 125}}, "bead11d2b0064cec8f9c8731b2316589": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d743f231885b4564a25bd18082c31390", "placeholder": "​", "style": "IPY_MODEL_62314dddce2544bcb1437f40070c76b0", "value": " 125/125 [00:00&lt;00:00, 5.20kB/s]"}}, "04ec6699d63a453cb7a124cc57e90d54": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2b1c0e48ea7149e0aef6b4d498ed6e8d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ee1b06c5bd7048fb9e39c3c304ee2169": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8605aa79cbc14a83a4c44a5a577de9c7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7ed3aa5d8f4547de978ef82a61210eb0": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d743f231885b4564a25bd18082c31390": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "62314dddce2544bcb1437f40070c76b0": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fa3b9a6f0d7143afa84ba909e024fadc": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_69acbc57fe4b4c3094c4558b2fc5fa28", "IPY_MODEL_a4f0602f2bc24b9887b3eede789752ea", "IPY_MODEL_5319df1153dd45cda829eedd5696c849"], "layout": "IPY_MODEL_c66a18a94e524124a100cdef36427cdb"}}, "69acbc57fe4b4c3094c4558b2fc5fa28": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0908e1c914574250b5f91ea1adb08a29", "placeholder": "​", "style": "IPY_MODEL_a3c2bbfd68bd4ab898d00ccb034b9b99", "value": "1_Pooling/config.json: 100%"}}, "a4f0602f2bc24b9887b3eede789752ea": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_394ef0bb5d5943b49e76de517f5db51a", "max": 191, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_140aa3439ea44396a7514c977985f86c", "value": 191}}, "5319df1153dd45cda829eedd5696c849": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cb34ee935b064fa0ae3302c5a240c974", "placeholder": "​", "style": "IPY_MODEL_83c32fe5831e49aca35b5af6eb409046", "value": " 191/191 [00:00&lt;00:00, 7.40kB/s]"}}, "c66a18a94e524124a100cdef36427cdb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0908e1c914574250b5f91ea1adb08a29": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a3c2bbfd68bd4ab898d00ccb034b9b99": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "394ef0bb5d5943b49e76de517f5db51a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "140aa3439ea44396a7514c977985f86c": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "cb34ee935b064fa0ae3302c5a240c974": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "83c32fe5831e49aca35b5af6eb409046": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Agcs9cqVRzlx", "outputId": "5b6bb263-6dfc-408e-808f-b56090d32263"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting datasets\n", "  Downloading datasets-2.19.1-py3-none-any.whl (542 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m542.0/542.0 kB\u001b[0m \u001b[31m8.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from datasets) (3.14.0)\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.10/dist-packages (from datasets) (1.25.2)\n", "Requirement already satisfied: pyarrow>=12.0.0 in /usr/local/lib/python3.10/dist-packages (from datasets) (14.0.2)\n", "Requirement already satisfied: pyarrow-hotfix in /usr/local/lib/python3.10/dist-packages (from datasets) (0.6)\n", "Collecting dill<0.3.9,>=0.3.0 (from datasets)\n", "  Downloading dill-0.3.8-py3-none-any.whl (116 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m116.3/116.3 kB\u001b[0m \u001b[31m12.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from datasets) (2.0.3)\n", "Requirement already satisfied: requests>=2.19.0 in /usr/local/lib/python3.10/dist-packages (from datasets) (2.31.0)\n", "Requirement already satisfied: tqdm>=4.62.1 in /usr/local/lib/python3.10/dist-packages (from datasets) (4.66.4)\n", "Collecting xxhash (from datasets)\n", "  Downloading xxhash-3.4.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (194 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m194.1/194.1 kB\u001b[0m \u001b[31m19.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting multiprocess (from datasets)\n", "  Downloading multiprocess-0.70.16-py310-none-any.whl (134 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m134.8/134.8 kB\u001b[0m \u001b[31m13.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: fsspec[http]<=2024.3.1,>=2023.1.0 in /usr/local/lib/python3.10/dist-packages (from datasets) (2023.6.0)\n", "Requirement already satisfied: aiohttp in /usr/local/lib/python3.10/dist-packages (from datasets) (3.9.5)\n", "Collecting huggingface-hub>=0.21.2 (from datasets)\n", "  Downloading huggingface_hub-0.23.0-py3-none-any.whl (401 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m401.2/401.2 kB\u001b[0m \u001b[31m27.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: packaging in /usr/local/lib/python3.10/dist-packages (from datasets) (24.0)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from datasets) (6.0.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->datasets) (4.0.3)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.21.2->datasets) (4.11.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.19.0->datasets) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests>=2.19.0->datasets) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.19.0->datasets) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests>=2.19.0->datasets) (2024.2.2)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from pandas->datasets) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->datasets) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas->datasets) (2024.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.8.2->pandas->datasets) (1.16.0)\n", "Installing collected packages: xxhash, dill, multiprocess, huggingface-hub, datasets\n", "  Attempting uninstall: huggingface-hub\n", "    Found existing installation: huggingface-hub 0.20.3\n", "    Uninstalling huggingface-hub-0.20.3:\n", "      Successfully uninstalled huggingface-hub-0.20.3\n", "Successfully installed datasets-2.19.1 dill-0.3.8 huggingface-hub-0.23.0 multiprocess-0.70.16 xxhash-3.4.1\n"]}], "source": ["!pip install datasets"]}, {"cell_type": "code", "source": ["!pip install pandas"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jmHr7IcRu1bg", "outputId": "30380457-63b3-4909-95f9-211b91eff8fc"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (2.0.3)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from pandas) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas) (2024.1)\n", "Requirement already satisfied: numpy>=1.21.0 in /usr/local/lib/python3.10/dist-packages (from pandas) (1.25.2)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.8.2->pandas) (1.16.0)\n"]}]}, {"cell_type": "code", "source": ["from datasets import load_dataset"], "metadata": {"id": "hywVUlFRu5Jw"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["dataset=load_dataset(\"MongoDB/embedded_movies\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 113, "referenced_widgets": ["1029b02ea1974b3fbf6ae5fd4b672e60", "c13b0e0a35994c50a4591ec48bbe9ec8", "107eadc4300c44a7889f09672eca02fe", "bc33b05ed006436d8aafa97a0c51abde", "f2d76e677e004baa8a806e8a2b91a01c", "c3fbfd22a5fb4d47a7c5e75dde2289f6", "f962fe8da6d74af3a24334f3f3024c8a", "86c68952a8934fd3a0c2d9e223ef4f77", "e4e9cfe75b614823b6ee7aa5c3fa2d77", "7b4249a9e72d4780867cf742417339f3", "c774b74fe854463f89e54cbd3d73e7e9", "25bd324303d14d0aa32494b014958e8e", "295a7246575c455ca4ac15f6c331b676", "7268522b6cce4bbbb991cb64742f08c2", "d3567008d2da4c86a992c80c00eb8737", "4f67676d6d86488cbaffd6254c3fe5db", "1eb727fe9d5a4bd89b14c5f35a549f10", "688788bbfa1f48d6829d2c60121379aa", "de7f75132a8a4a458807209f8eec458d", "6048948e3c044c94b7490cfd250f2ffd", "c8f4ba3150e648518f2ae1706721c505", "434997a3c6524cc1859c86e354dbb7ec", "b781dfe891cc4676b0827a0e1d86fc9e", "72306a6b64ef4a4e9b3a190cb9bbf971", "84cc9e635d4046c0a0e3904f35d001fc", "1b9a661e3fa446aa8be565552a853295", "8b6dc26221144b24950c1da666d058eb", "5da7b973ca35496da771a8c84971c9eb", "7b83376fe1804ff9bd999b6ea579706a", "d89b4f6b5b994ea5a157edecd85b7f3b", "78e621d0575b4652ad550103796a9003", "bc2e1d0bff6540b2a61768fbfe79cf59", "a29102b7ed40424db2b00bcdbcc6bdb3"]}, "id": "Zyd2Vp8uu9Yv", "outputId": "b0c4e38c-316c-4ac8-fa48-848b55d8c29d"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Downloading readme:   0%|          | 0.00/6.18k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "1029b02ea1974b3fbf6ae5fd4b672e60"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading data:   0%|          | 0.00/42.3M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "25bd324303d14d0aa32494b014958e8e"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Generating train split:   0%|          | 0/1500 [00:00<?, ? examples/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "b781dfe891cc4676b0827a0e1d86fc9e"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["dataset"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "uCQVhPi26MBr", "outputId": "b3d13efe-51e6-4fde-871a-1c4f6e6f8d4c"}, "execution_count": 51, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatasetDict({\n", "    train: Dataset({\n", "        features: ['directors', 'title', 'imdb', 'plot_embedding', 'awards', 'rated', 'countries', 'type', 'writers', 'metacritic', 'languages', 'fullplot', 'genres', 'poster', 'cast', 'runtime', 'plot', 'num_mflix_comments'],\n", "        num_rows: 1500\n", "    })\n", "})"]}, "metadata": {}, "execution_count": 51}]}, {"cell_type": "code", "source": ["import pandas as pd\n", "data=pd.DataFrame(dataset[\"train\"])"], "metadata": {"id": "m92LGvLuvGsX"}, "execution_count": 55, "outputs": []}, {"cell_type": "code", "source": ["data.shape"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3MhwK2v4vDWv", "outputId": "b18b4664-8768-40a2-8a96-40e58cfb2ab8"}, "execution_count": 56, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(1500, 18)"]}, "metadata": {}, "execution_count": 56}]}, {"cell_type": "code", "source": ["data.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 746}, "id": "tI_doa-L6PS6", "outputId": "2e15b25b-8897-459d-fdde-3198d9ce48a3"}, "execution_count": 57, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                              directors  ... num_mflix_comments\n", "0  [<PERSON>, <PERSON>]  ...                  0\n", "1       [<PERSON>, <PERSON>]  ...                  0\n", "2                      [<PERSON>]  ...                  0\n", "3                       [<PERSON>]  ...                  1\n", "4                          [<PERSON>]  ...                  0\n", "\n", "[5 rows x 18 columns]"], "text/html": ["\n", "  <div id=\"df-7277a9ee-5518-4563-9f4a-02fbcc1a5503\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>directors</th>\n", "      <th>title</th>\n", "      <th>imdb</th>\n", "      <th>plot_embedding</th>\n", "      <th>awards</th>\n", "      <th>rated</th>\n", "      <th>countries</th>\n", "      <th>type</th>\n", "      <th>writers</th>\n", "      <th>metacritic</th>\n", "      <th>languages</th>\n", "      <th>fullplot</th>\n", "      <th>genres</th>\n", "      <th>poster</th>\n", "      <th>cast</th>\n", "      <th>runtime</th>\n", "      <th>plot</th>\n", "      <th>num_mflix_comments</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>The Perils of Pauline</td>\n", "      <td>{'id': 4465, 'rating': 7.6, 'votes': 744}</td>\n", "      <td>[0.00072939653, -0.026834568, 0.013515796, -0....</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>None</td>\n", "      <td>[USA]</td>\n", "      <td>movie</td>\n", "      <td>[<PERSON> (screenplay), <PERSON>...</td>\n", "      <td>NaN</td>\n", "      <td>[English]</td>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>[Action]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzgxOD...</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>...</td>\n", "      <td>199.0</td>\n", "      <td>Young <PERSON> is left a lot of money when her ...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>[<PERSON>, <PERSON>]</td>\n", "      <td>From Hand to Mouth</td>\n", "      <td>{'id': 10146, 'rating': 7.0, 'votes': 639}</td>\n", "      <td>[-0.022837115, -0.022941574, 0.014937485, -0.0...</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>TV-G</td>\n", "      <td>[USA]</td>\n", "      <td>movie</td>\n", "      <td>[<PERSON><PERSON><PERSON><PERSON> (titles)]</td>\n", "      <td>NaN</td>\n", "      <td>[English]</td>\n", "      <td>As a penniless man worries about how he will m...</td>\n", "      <td>[Comedy, Short, Action]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BNzE1OW...</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, '<PERSON><PERSON><PERSON>' <PERSON><PERSON>, ...</td>\n", "      <td>22.0</td>\n", "      <td>A penniless young man tries to save an heiress...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>[<PERSON>]</td>\n", "      <td><PERSON></td>\n", "      <td>{'id': 16634, 'rating': 6.9, 'votes': 222}</td>\n", "      <td>[0.00023330493, -0.028511643, 0.014653289, -0....</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>None</td>\n", "      <td>[USA]</td>\n", "      <td>movie</td>\n", "      <td>[<PERSON> (adaptation), <PERSON> (ad...</td>\n", "      <td>NaN</td>\n", "      <td>[English]</td>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>[Action, Adventure, Drama]</td>\n", "      <td>None</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>, A<PERSON>..</td>\n", "      <td>101.0</td>\n", "      <td><PERSON> \"<PERSON>\" <PERSON> leaves England in disgrac...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>[<PERSON>]</td>\n", "      <td>The Black Pirate</td>\n", "      <td>{'id': 16654, 'rating': 7.2, 'votes': 1146}</td>\n", "      <td>[-0.005927917, -0.033394486, 0.0015323418, -0....</td>\n", "      <td>{'nominations': 0, 'text': '1 win.', 'wins': 1}</td>\n", "      <td>None</td>\n", "      <td>[USA]</td>\n", "      <td>movie</td>\n", "      <td>[<PERSON> (story), <PERSON> (a...</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>A nobleman vows to avenge the death of his fat...</td>\n", "      <td>[Adventure, Action]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMzU0ND...</td>\n", "      <td>[<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> ...</td>\n", "      <td>88.0</td>\n", "      <td>Seeking revenge, an athletic young man joins t...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>[<PERSON>]</td>\n", "      <td>For Heaven's Sake</td>\n", "      <td>{'id': 16895, 'rating': 7.6, 'votes': 918}</td>\n", "      <td>[-0.0059373598, -0.026604708, -0.0070914757, -...</td>\n", "      <td>{'nominations': 1, 'text': '1 nomination.', 'w...</td>\n", "      <td>PASSED</td>\n", "      <td>[USA]</td>\n", "      <td>movie</td>\n", "      <td>[<PERSON> (story), <PERSON> (story), <PERSON>.</td>\n", "      <td>NaN</td>\n", "      <td>[English]</td>\n", "      <td>The Uptown Boy, <PERSON><PERSON> (Lloyd) is a...</td>\n", "      <td>[Action, Comedy, Romance]</td>\n", "      <td>https://m.media-amazon.com/images/M/MV5BMTcxMT...</td>\n", "      <td>[<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>...</td>\n", "      <td>58.0</td>\n", "      <td>An irresponsible young millionaire changes his...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-7277a9ee-5518-4563-9f4a-02fbcc1a5503')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-7277a9ee-5518-4563-9f4a-02fbcc1a5503 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-7277a9ee-5518-4563-9f4a-02fbcc1a5503');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-93985e7b-d8d9-4b95-8402-59fa4da22d72\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-93985e7b-d8d9-4b95-8402-59fa4da22d72')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-93985e7b-d8d9-4b95-8402-59fa4da22d72 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "data", "summary": "{\n  \"name\": \"data\",\n  \"rows\": 1500,\n  \"fields\": [\n    {\n      \"column\": \"directors\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"title\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1435,\n        \"samples\": [\n          \"Turbo: A Power Rangers Movie\",\n          \"Neon Genesis Evangelion: Death & Rebirth\",\n          \"Johnny Mnemonic\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"imdb\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"plot_embedding\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"awards\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"rated\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 12,\n        \"samples\": [\n          \"TV-MA\",\n          \"TV-14\",\n          \"TV-G\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"countries\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"type\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"series\",\n          \"movie\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"writers\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"metacritic\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16.861995960390892,\n        \"min\": 9.0,\n        \"max\": 97.0,\n        \"num_unique_values\": 83,\n        \"samples\": [\n          50.0,\n          97.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"languages\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"fullplot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1409,\n        \"samples\": [\n          \"An undercover cop infiltrates a gang of thieves who plan to rob a jewelry store.\",\n          \"Godzilla returns in a brand-new movie that ignores all preceding movies except for the original with a brand new look and a powered up atomic ray. This time he battles a mysterious UFO that later transforms into a mysterious kaiju dubbed Orga. They meet up for the final showdown in the city of Shinjuku.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"genres\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"poster\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1368,\n        \"samples\": [\n          \"https://m.media-amazon.com/images/M/MV5BNWE5MzAwMjQtNzI1YS00YjZhLTkxNDItM2JjNjM3ZjI5NzBjXkEyXkFqcGdeQXVyMTQxNzMzNDI@._V1_SY1000_SX677_AL_.jpg\",\n          \"https://m.media-amazon.com/images/M/MV5BMTgwNjIyNTczMF5BMl5BanBnXkFtZTcwODI5MDkyMQ@@._V1_SY1000_SX677_AL_.jpg\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cast\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"runtime\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 42.09038552453906,\n        \"min\": 6.0,\n        \"max\": 1256.0,\n        \"num_unique_values\": 139,\n        \"samples\": [\n          152.0,\n          127.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"plot\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1429,\n        \"samples\": [\n          \"A New York City architect becomes a one-man vigilante squad after his wife is murdered by street punks in which he randomly goes out and kills would-be muggers on the mean streets after dark.\",\n          \"As the daring thief Ars\\u00e8ne Lupin (Duris) ransacks the homes of wealthy Parisians, the police, with a secret weapon in their arsenal, attempt to ferret him out.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"num_mflix_comments\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 27,\n        \"min\": 0,\n        \"max\": 158,\n        \"num_unique_values\": 40,\n        \"samples\": [\n          117,\n          134\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 57}]}, {"cell_type": "code", "source": ["data=data.sample(80)"], "metadata": {"id": "lHGcmKGPvKhp"}, "execution_count": 58, "outputs": []}, {"cell_type": "code", "source": ["data.shape"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Oby5bgI6vO5Q", "outputId": "02aac810-8cf7-41c2-efdb-f0deb054effa"}, "execution_count": 59, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(80, 18)"]}, "metadata": {}, "execution_count": 59}]}, {"cell_type": "code", "source": ["data.columns"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "R27ulcmfvP9w", "outputId": "541ef14c-44fa-4a43-d8d3-b9138518ff17"}, "execution_count": 60, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Index(['directors', 'title', 'imdb', 'plot_embedding', 'awards', 'rated',\n", "       'countries', 'type', 'writers', 'metacritic', 'languages', 'fullplot',\n", "       'genres', 'poster', 'cast', 'runtime', 'plot', 'num_mflix_comments'],\n", "      dtype='object')"]}, "metadata": {}, "execution_count": 60}]}, {"cell_type": "code", "source": ["data.isnull().sum()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Hthvn8BevSGQ", "outputId": "67f1356e-c68b-4e9e-e87d-c102c92ecf02"}, "execution_count": 61, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["directors              0\n", "title                  0\n", "imdb                   0\n", "plot_embedding         1\n", "awards                 0\n", "rated                 22\n", "countries              0\n", "type                   0\n", "writers                0\n", "metacritic            51\n", "languages              0\n", "fullplot               4\n", "genres                 0\n", "poster                 3\n", "cast                   0\n", "runtime                1\n", "plot                   1\n", "num_mflix_comments     0\n", "dtype: int64"]}, "metadata": {}, "execution_count": 61}]}, {"cell_type": "code", "source": ["data=data.dropna(subset=[\"fullplot\"])"], "metadata": {"id": "nqv7fLFsvXZv"}, "execution_count": 62, "outputs": []}, {"cell_type": "code", "source": ["data=data.drop(columns=[\"plot_embedding\"])"], "metadata": {"id": "ssTczw8Xvccg"}, "execution_count": 63, "outputs": []}, {"cell_type": "code", "source": ["data.isnull().sum()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ujGYzQNDvfwa", "outputId": "43700158-b703-46b8-e5f5-0a9d580d9404"}, "execution_count": 64, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["directors              0\n", "title                  0\n", "imdb                   0\n", "awards                 0\n", "rated                 21\n", "countries              0\n", "type                   0\n", "writers                0\n", "metacritic            48\n", "languages              0\n", "fullplot               0\n", "genres                 0\n", "poster                 3\n", "cast                   0\n", "runtime                1\n", "plot                   0\n", "num_mflix_comments     0\n", "dtype: int64"]}, "metadata": {}, "execution_count": 64}]}, {"cell_type": "code", "source": ["!pip install sentence_transformers"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "n08Io4ytvipn", "outputId": "fb229fc2-3524-444d-abd9-8fb336917e12"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting sentence_transformers\n", "  Downloading sentence_transformers-2.7.0-py3-none-any.whl (171 kB)\n", "\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/171.5 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[91m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[90m╺\u001b[0m\u001b[90m━━━━━━\u001b[0m \u001b[32m143.4/171.5 kB\u001b[0m \u001b[31m4.1 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m171.5/171.5 kB\u001b[0m \u001b[31m3.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: transformers<5.0.0,>=4.34.0 in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (4.40.1)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (4.66.4)\n", "Requirement already satisfied: torch>=1.11.0 in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (2.2.1+cu121)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (1.25.2)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (1.2.2)\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (1.11.4)\n", "Requirement already satisfied: huggingface-hub>=0.15.1 in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (0.23.0)\n", "Requirement already satisfied: Pillow in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (9.4.0)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence_transformers) (3.14.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence_transformers) (2023.6.0)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence_transformers) (24.0)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence_transformers) (6.0.1)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence_transformers) (2.31.0)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence_transformers) (4.11.0)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence_transformers) (1.12)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence_transformers) (3.3)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence_transformers) (3.1.3)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.1.105 (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_cuda_nvrtc_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (23.7 MB)\n", "Collecting nvidia-cuda-runtime-cu12==12.1.105 (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_cuda_runtime_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (823 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.1.105 (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_cuda_cupti_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (14.1 MB)\n", "Collecting nvidia-cudnn-cu12==******** (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_cudnn_cu12-********-py3-none-manylinux1_x86_64.whl (731.7 MB)\n", "Collecting nvidia-cublas-cu12==12.1.3.1 (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_cublas_cu12-12.1.3.1-py3-none-manylinux1_x86_64.whl (410.6 MB)\n", "Collecting nvidia-cufft-cu12==********* (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_cufft_cu12-*********-py3-none-manylinux1_x86_64.whl (121.6 MB)\n", "Collecting nvidia-curand-cu12==********** (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_curand_cu12-**********-py3-none-manylinux1_x86_64.whl (56.5 MB)\n", "Collecting nvidia-cusolver-cu12==********** (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_cusolver_cu12-**********-py3-none-manylinux1_x86_64.whl (124.2 MB)\n", "Collecting nvidia-cusparse-cu12==********** (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_cusparse_cu12-**********-py3-none-manylinux1_x86_64.whl (196.0 MB)\n", "Collecting nvidia-nccl-cu12==2.19.3 (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_nccl_cu12-2.19.3-py3-none-manylinux1_x86_64.whl (166.0 MB)\n", "Collecting nvidia-nvtx-cu12==12.1.105 (from torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_nvtx_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (99 kB)\n", "Requirement already satisfied: triton==2.2.0 in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence_transformers) (2.2.0)\n", "Collecting nvidia-nvjitlink-cu12 (from nvidia-cusolver-cu12==**********->torch>=1.11.0->sentence_transformers)\n", "  Using cached nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.34.0->sentence_transformers) (2023.12.25)\n", "Requirement already satisfied: tokenizers<0.20,>=0.19 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.34.0->sentence_transformers) (0.19.1)\n", "Requirement already satisfied: safetensors>=0.4.1 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.34.0->sentence_transformers) (0.4.3)\n", "Requirement already satisfied: joblib>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from scikit-learn->sentence_transformers) (1.4.2)\n", "Requirement already satisfied: threadpoolctl>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from scikit-learn->sentence_transformers) (3.5.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch>=1.11.0->sentence_transformers) (2.1.5)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.15.1->sentence_transformers) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.15.1->sentence_transformers) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.15.1->sentence_transformers) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.15.1->sentence_transformers) (2024.2.2)\n", "Requirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->torch>=1.11.0->sentence_transformers) (1.3.0)\n", "Installing collected packages: nvidia-nvtx-cu12, nvidia-nvjitlink-cu12, nvidia-nccl-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, nvidia-cusparse-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12, sentence_transformers\n", "Successfully installed nvidia-cublas-cu12-12.1.3.1 nvidia-cuda-cupti-cu12-12.1.105 nvidia-cuda-nvrtc-cu12-12.1.105 nvidia-cuda-runtime-cu12-12.1.105 nvidia-cudnn-cu12-******** nvidia-cufft-cu12-********* nvidia-curand-cu12-********** nvidia-cusolver-cu12-********** nvidia-cusparse-cu12-********** nvidia-nccl-cu12-2.19.3 nvidia-nvjitlink-cu12-12.4.127 nvidia-nvtx-cu12-12.1.105 sentence_transformers-2.7.0\n"]}]}, {"cell_type": "code", "source": ["from sentence_transformers import SentenceTransformer\n", "embedding_model = SentenceTransformer(\"thenlper/gte-large\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 392, "referenced_widgets": ["c82b8a2349344c249a986dcc16927495", "6eb99d299ae14a8fb14ccb3cd38d6bf9", "98a18f3c217642caac971bc91cfa7c3f", "2f1be0c2d06648bab350b8503c41d459", "d76a6a94dcbf42648361ddf423d7f969", "b1395879808b4142be236259e1c516e0", "3a281b8d0f41493db24974880c4a7c6c", "dacc696753d9475d886187f05e40ee7a", "9191ea078c3746dba4bb3f4900285bb8", "18176f258c004d7a98a25930a5b057cc", "7b32eedf72004288a702e58ee4a2408c", "36effe5381924399af196a6f24fd98e8", "ff28f9fa73224896983586a960e11fd4", "e00d2c61d5844d48802b16f02786723e", "7f2df9a4be9a45fa96eb3c08df38f3e1", "47dfcc76978f497fbb8f77449eba41f4", "313d6c00af044940b2e1d8245fbf1837", "8f7bd91baea24e66afe51a1380085c91", "24e00d4b6656436abdebeb694ccd8980", "8de3cdd3e7ac4ce4825f2d5754acaecd", "1d0ab759a2404b8b88db0fe0d752b0ca", "5595e14cbc8746caaaf08ced876455a5", "a0e4ef164a394d2e990ebd1cb81ee41c", "9459985f94e541dbafd69fa36fa5023f", "3a3ad499ffc14f70929ffe79bb1eb7c0", "c98f5dae052c46e0b38f2c4839268fc5", "1166c0982782442ab5bfb36a117d324d", "7f5914088bff434b9c1134ba21a7c961", "fe1ca2c1e7ca4ba1953e14bef21a34af", "9fb5fd20dbcb4e2bb8b41a1c021cebd1", "e03556fd85e04833981746ea0c1a68bb", "2654a7026b0d4df4a63638c1ce1eab71", "8ed98dbfc5f449f1a4dc1753e4dc232a", "989ff4be9d8e4026bcffa4e13bd95088", "15db6632bf294345acda1b9c4879c6a5", "2afffa82d42a4b90b76935a0b1683ca5", "657ca126c115418e85de0c8f19c4adcb", "d2a936181416420389f6678c4534fb72", "b0a4942167b74246a37910154809f9e2", "14c4e67e040e4e2dbb8f0b968670a481", "51a4ebef5d37444ab9351cf55272b1ea", "5cc49be8b7af4c8d853074f0d0bb7873", "9bb202e839634afe87f5ce96d7a5118d", "023bd7eea0164255aad63774e688f5c7", "bf2a331d5b384543bc248167afe08300", "bd8c7ceb1e0a4bbc8582b22d9442c9b2", "85c7ed73dc544ca38f8e71a5f38bf0a1", "39621c73b3cd430a81ba8dc5d46c8f9a", "d2f67f6c5d5e427b8327f4f75c0f32c7", "2f64d610e4cb494aa12b459b9e60f637", "b0aa660e14714c95bcf9fd619a75e442", "b51755c4988046d4b74ed4041cde2af6", "e7ad44bffe0049d589a44ae4d043fc08", "d3df39ef598b4e7689f2cd5470fbcfc8", "5c4878eb5178492f93158b0aeff1415d", "57f48528f7ab479a933a53b1eabd9793", "e1f26d4b639345fa91078fc6915478a8", "8bc6f8ff00f941558623c1e73111e9f4", "09741a07a2d143a5a6e3104f6806f7b2", "f095f95c046b45cbbc1f270b5638896f", "550239efa14b4150869ccd7fa6fafa79", "f8de658756b6435cbab5da7a08246e4b", "c35ba91f4da541c0b952c5ad7a15b2b5", "e9aef987924b4c5d9981ba53daebcc25", "7baaf7fcfc404271b3dd21079f6c5e14", "76314a63041e4fc0aec7c20b9c1c876c", "0d5e34d133234fefbf36c9b1e532f11c", "fbef9146d0eb4cdeb6d1ed1e970e32ac", "b3f6a640374a45899cd7e65754c1b433", "429e3964ab3143c4a651cee20d6b1dea", "d5f9f7b3b372493aa241dee207ff5b37", "70286cceefc04519ada7b063ae33c90d", "aab96e47d0f54292be0204564ab6da13", "bf40d9868e824e4188fc3185a8d2a6c2", "facf4c49b7a744f4ba4d327e5b633381", "51dcb1403d0f4dd0aa70838a7088aca4", "a3ea452bb9dd4cf09d24682730230fc2", "adb01ed209ff44c59394971bd6a16970", "85ba60f7954b46b6bdb77138f8181c62", "605f1c241c5c4f50bed9b57fdf040a91", "2ebf049412ff42778edbfc98583d258c", "906b2f45d1524449807c15dd1ce822e5", "f0ffd82f413f4cd1a3c6e39d4f0c6543", "84aa43b0396f485f80e7a2a8aef0a617", "760a9421144b4f4b952f893ba57deeac", "a3f16e0d255b41bcb4c73a59518bbdfe", "b46b4a4a6ce74641995d722292cf446b", "f24598749bf440738aa790b60845ba16", "75f0a82140d2408b8cfc968e65376568", "5be225e39c624163bdfc5e010d93026c", "dd38b9e01bd94ce19294398200652271", "bead11d2b0064cec8f9c8731b2316589", "04ec6699d63a453cb7a124cc57e90d54", "2b1c0e48ea7149e0aef6b4d498ed6e8d", "ee1b06c5bd7048fb9e39c3c304ee2169", "8605aa79cbc14a83a4c44a5a577de9c7", "7ed3aa5d8f4547de978ef82a61210eb0", "d743f231885b4564a25bd18082c31390", "62314dddce2544bcb1437f40070c76b0", "fa3b9a6f0d7143afa84ba909e024fadc", "69acbc57fe4b4c3094c4558b2fc5fa28", "a4f0602f2bc24b9887b3eede789752ea", "5319df1153dd45cda829eedd5696c849", "c66a18a94e524124a100cdef36427cdb", "0908e1c914574250b5f91ea1adb08a29", "a3c2bbfd68bd4ab898d00ccb034b9b99", "394ef0bb5d5943b49e76de517f5db51a", "140aa3439ea44396a7514c977985f86c", "cb34ee935b064fa0ae3302c5a240c974", "83c32fe5831e49aca35b5af6eb409046"]}, "id": "jmFFbkg-vxyw", "outputId": "ab5aae5c-bb5b-44bb-c4fe-6a265dc54247"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["modules.json:   0%|          | 0.00/385 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "c82b8a2349344c249a986dcc16927495"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["README.md:   0%|          | 0.00/67.9k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "36effe5381924399af196a6f24fd98e8"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["sentence_bert_config.json:   0%|          | 0.00/57.0 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "a0e4ef164a394d2e990ebd1cb81ee41c"}}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/huggingface_hub/file_download.py:1132: FutureWarning: `resume_download` is deprecated and will be removed in version 1.0.0. Downloads always resume when possible. If you want to force a new download, use `force_download=True`.\n", "  warnings.warn(\n"]}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/619 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "989ff4be9d8e4026bcffa4e13bd95088"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors:   0%|          | 0.00/670M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "bf2a331d5b384543bc248167afe08300"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json:   0%|          | 0.00/342 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "57f48528f7ab479a933a53b1eabd9793"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["vocab.txt:   0%|          | 0.00/232k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "0d5e34d133234fefbf36c9b1e532f11c"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.json:   0%|          | 0.00/712k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "adb01ed209ff44c59394971bd6a16970"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["special_tokens_map.json:   0%|          | 0.00/125 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "75f0a82140d2408b8cfc968e65376568"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["1_Pooling/config.json:   0%|          | 0.00/191 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "fa3b9a6f0d7143afa84ba909e024fadc"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["!pip install pymongo"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WORA6ITDwg8z", "outputId": "cf0cbc6f-e952-44dc-c9ef-0a9c1ff152ae"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting pym<PERSON>o\n", "  Downloading pymongo-4.7.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (670 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m670.0/670.0 kB\u001b[0m \u001b[31m9.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting dnspython<3.0.0,>=1.16.0 (from pymongo)\n", "  Downloading dnspython-2.6.1-py3-none-any.whl (307 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m307.7/307.7 kB\u001b[0m \u001b[31m20.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: dnspython, pymongo\n", "Successfully installed dnspython-2.6.1 pymongo-4.7.2\n"]}]}, {"cell_type": "code", "source": ["from pymongo.mongo_client import MongoClient\n", "\n", "uri = \"mongodb+srv://snshrivas:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0\"\n", "\n", "# Create a new client and connect to the server\n", "client = MongoClient(uri)\n", "\n", "# Send a ping to confirm a successful connection\n", "try:\n", "    client.admin.command('ping')\n", "    print(\"Pinged your deployment. You successfully connected to MongoDB!\")\n", "except Exception as e:\n", "    print(e)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kaivsjLSwSrs", "outputId": "87efceda-b1b6-4ddc-ea3c-6a032a150093"}, "execution_count": 65, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Pinged your deployment. You successfully connected to MongoDB!\n"]}]}, {"cell_type": "code", "source": ["db=client[\"moviemydb\"]"], "metadata": {"id": "2VnSfFW-wetw"}, "execution_count": 66, "outputs": []}, {"cell_type": "code", "source": ["collection=db[\"moviemycollection\"]"], "metadata": {"id": "y2eJXzmQwqfA"}, "execution_count": 67, "outputs": []}, {"cell_type": "code", "source": ["document=data.to_dict(\"records\")"], "metadata": {"id": "jxSE-sQ4wvlQ"}, "execution_count": 68, "outputs": []}, {"cell_type": "code", "source": ["collection.insert_many(document)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "odoVfsVow4Q_", "outputId": "7dcc4747-6f18-48a4-a087-2f2bfb6341dd"}, "execution_count": 69, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Insert<PERSON><PERSON><PERSON><PERSON><PERSON>([ObjectId('663ce86a57b4141aa769a43f'), ObjectId('663ce86a57b4141aa769a440'), ObjectId('663ce86a57b4141aa769a441'), ObjectId('663ce86a57b4141aa769a442'), ObjectId('663ce86a57b4141aa769a443'), ObjectId('663ce86a57b4141aa769a444'), ObjectId('663ce86a57b4141aa769a445'), ObjectId('663ce86a57b4141aa769a446'), ObjectId('663ce86a57b4141aa769a447'), ObjectId('663ce86a57b4141aa769a448'), ObjectId('663ce86a57b4141aa769a449'), ObjectId('663ce86a57b4141aa769a44a'), ObjectId('663ce86a57b4141aa769a44b'), ObjectId('663ce86a57b4141aa769a44c'), ObjectId('663ce86a57b4141aa769a44d'), ObjectId('663ce86a57b4141aa769a44e'), ObjectId('663ce86a57b4141aa769a44f'), ObjectId('663ce86a57b4141aa769a450'), ObjectId('663ce86a57b4141aa769a451'), ObjectId('663ce86a57b4141aa769a452'), ObjectId('663ce86a57b4141aa769a453'), ObjectId('663ce86a57b4141aa769a454'), ObjectId('663ce86a57b4141aa769a455'), ObjectId('663ce86a57b4141aa769a456'), ObjectId('663ce86a57b4141aa769a457'), ObjectId('663ce86a57b4141aa769a458'), ObjectId('663ce86a57b4141aa769a459'), ObjectId('663ce86a57b4141aa769a45a'), ObjectId('663ce86a57b4141aa769a45b'), ObjectId('663ce86a57b4141aa769a45c'), ObjectId('663ce86a57b4141aa769a45d'), ObjectId('663ce86a57b4141aa769a45e'), ObjectId('663ce86a57b4141aa769a45f'), ObjectId('663ce86a57b4141aa769a460'), ObjectId('663ce86a57b4141aa769a461'), ObjectId('663ce86a57b4141aa769a462'), ObjectId('663ce86a57b4141aa769a463'), ObjectId('663ce86a57b4141aa769a464'), ObjectId('663ce86a57b4141aa769a465'), ObjectId('663ce86a57b4141aa769a466'), ObjectId('663ce86a57b4141aa769a467'), ObjectId('663ce86a57b4141aa769a468'), ObjectId('663ce86a57b4141aa769a469'), ObjectId('663ce86a57b4141aa769a46a'), ObjectId('663ce86a57b4141aa769a46b'), ObjectId('663ce86a57b4141aa769a46c'), ObjectId('663ce86a57b4141aa769a46d'), ObjectId('663ce86a57b4141aa769a46e'), ObjectId('663ce86a57b4141aa769a46f'), ObjectId('663ce86a57b4141aa769a470'), ObjectId('663ce86a57b4141aa769a471'), ObjectId('663ce86a57b4141aa769a472'), ObjectId('663ce86a57b4141aa769a473'), ObjectId('663ce86a57b4141aa769a474'), ObjectId('663ce86a57b4141aa769a475'), ObjectId('663ce86a57b4141aa769a476'), ObjectId('663ce86a57b4141aa769a477'), ObjectId('663ce86a57b4141aa769a478'), ObjectId('663ce86a57b4141aa769a479'), ObjectId('663ce86a57b4141aa769a47a'), ObjectId('663ce86a57b4141aa769a47b'), ObjectId('663ce86a57b4141aa769a47c'), ObjectId('663ce86a57b4141aa769a47d'), ObjectId('663ce86a57b4141aa769a47e'), ObjectId('663ce86a57b4141aa769a47f'), ObjectId('663ce86a57b4141aa769a480'), ObjectId('663ce86a57b4141aa769a481'), ObjectId('663ce86a57b4141aa769a482'), ObjectId('663ce86a57b4141aa769a483'), ObjectId('663ce86a57b4141aa769a484'), ObjectId('663ce86a57b4141aa769a485'), ObjectId('663ce86a57b4141aa769a486'), ObjectId('663ce86a57b4141aa769a487'), ObjectId('663ce86a57b4141aa769a488'), ObjectId('663ce86a57b4141aa769a489'), ObjectId('663ce86a57b4141aa769a48a')], acknowledged=True)"]}, "metadata": {}, "execution_count": 69}]}, {"cell_type": "code", "source": ["!pip install pinecone-client"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7Mq2fx_Ow-gQ", "outputId": "135f336b-1727-4133-faab-10e5a8cc51c3"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting pinecone-client\n", "  Downloading pinecone_client-4.0.0-py3-none-any.whl (214 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m214.5/214.5 kB\u001b[0m \u001b[31m4.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: certifi>=2019.11.17 in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (2024.2.2)\n", "Requirement already satisfied: tqdm>=4.64.1 in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (4.66.4)\n", "Requirement already satisfied: typing-extensions>=3.7.4 in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (4.11.0)\n", "Requirement already satisfied: urllib3>=1.26.0 in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (2.0.7)\n", "Installing collected packages: pinecone-client\n", "Successfully installed pinecone-client-4.0.0\n"]}]}, {"cell_type": "code", "source": ["from pinecone import Pinecone\n", "PINECONE_API_KEY=\"cef794a5-a590-4234-99ff-e14b50257f93\"\n", "pc = Pinecone(api_key=PINECONE_API_KEY)\n", "index = pc.Index(\"mongomovie\")"], "metadata": {"id": "B_4B1quOxVU_"}, "execution_count": 70, "outputs": []}, {"cell_type": "code", "source": ["def get_result(query,similar_result=3):\n", "  embedding=embedding_model.encode(query)\n", "  embedding=embedding.tolist()\n", "\n", "  result=index.query(\n", "    vector=embedding,\n", "    top_k=similar_result,\n", "  )\n", "  return result"], "metadata": {"id": "t2p6gqSWxcnP"}, "execution_count": 79, "outputs": []}, {"cell_type": "code", "source": ["query=\"what is the best horror movie to watch and why?\""], "metadata": {"id": "lBTDaPtrxj2P"}, "execution_count": 80, "outputs": []}, {"cell_type": "code", "source": ["result=get_result(query)"], "metadata": {"id": "tK8JoCw-xp93"}, "execution_count": 81, "outputs": []}, {"cell_type": "code", "source": ["result"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bcoKC0TryXkf", "outputId": "219720f3-f1c4-4d36-b959-b9eaae30a5e2"}, "execution_count": 82, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'matches': [{'id': '663ce86a57b4141aa769a475',\n", "              'score': 0.792062521,\n", "              'values': []},\n", "             {'id': '663ce86a57b4141aa769a44b',\n", "              'score': 0.77806145,\n", "              'values': []},\n", "             {'id': '663ce86a57b4141aa769a482',\n", "              'score': 0.767377675,\n", "              'values': []}],\n", " 'namespace': '',\n", " 'usage': {'read_units': 5}}"]}, "metadata": {}, "execution_count": 82}]}, {"cell_type": "code", "source": ["from bson.objectid import ObjectId"], "metadata": {"id": "5V2O5aApyCmq"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["mylist=[]\n", "for i in  range(len(result[\"matches\"])):\n", "  value=result[\"matches\"][i]['id']\n", "  mylist.append(collection.find_one({\"_id\": ObjectId(value)}))"], "metadata": {"id": "g2FJgqVGx1ST"}, "execution_count": 83, "outputs": []}, {"cell_type": "code", "source": ["mylist"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "v61CvjPty6-l", "outputId": "0cf1dbbb-bb71-452b-8624-fc4fbd0ad424"}, "execution_count": 84, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[{'_id': ObjectId('663ce86a57b4141aa769a475'),\n", "  'directors': ['<PERSON>'],\n", "  'title': '<PERSON>',\n", "  'imdb': {'id': 338526, 'rating': 6.0, 'votes': 177269},\n", "  'awards': {'nominations': 13, 'text': '3 wins & 13 nominations.', 'wins': 3},\n", "  'rated': 'PG-13',\n", "  'countries': ['USA', 'Czech Republic'],\n", "  'type': 'movie',\n", "  'writers': ['<PERSON>'],\n", "  'metacritic': 35.0,\n", "  'languages': ['English', 'Latin'],\n", "  'fullplot': \"<PERSON> is in the world to rid all evil, even if not everyone agrees with him. The Vatican sends the monster hunter and his ally, <PERSON>, to Transylvania. They have been sent to this land to stop the powerful Count <PERSON>. Whilst there they join forces with a Gypsy Princess called <PERSON>, who is determined to end an ancient curse on her family by destroying the vampire. They just don't know how!\",\n", "  'genres': ['Action', 'Adventure', 'Fantasy'],\n", "  'poster': 'https://m.media-amazon.com/images/M/MV5BODRmY2NhNDItOWViNi00OTIyLTk3YjYtYzY0YTFlMDg1YzQ0L2ltYWdlL2ltYWdlXkEyXkFqcGdeQXVyMTQxNzMzNDI@._V1_SY1000_SX677_AL_.jpg',\n", "  'cast': ['<PERSON>',\n", "   '<PERSON>',\n", "   'Richard <PERSON>',\n", "   '<PERSON>'],\n", "  'runtime': 131.0,\n", "  'plot': \"The notorious monster hunter is sent to Transylvania to stop Count <PERSON> who is using Dr<PERSON> <PERSON>'s research and a werewolf for some sinister purpose.\",\n", "  'num_mflix_comments': 0},\n", " {'_id': ObjectId('663ce86a57b4141aa769a44b'),\n", "  'directors': ['<PERSON><PERSON>'],\n", "  'title': 'Timebomb',\n", "  'imdb': {'id': 105597, 'rating': 5.5, 'votes': 1040},\n", "  'awards': {'nominations': 0, 'text': '1 win.', 'wins': 1},\n", "  'rated': 'R',\n", "  'countries': ['USA'],\n", "  'type': 'movie',\n", "  'writers': ['<PERSON><PERSON>'],\n", "  'metacritic': nan,\n", "  'languages': ['English', 'Hungarian'],\n", "  'fullplot': \"When someone tries to murder watchmaker <PERSON>, the incident triggers a barrage of nightmares and flashbacks into a past that isn't his own. Fearing for his sanity, <PERSON> contacts psychiatrist Dr. <PERSON> for help. <PERSON> thinks he's hallucinating until another attack proves the dangers are all too real. The two of them go on the run, trying to discover the truth about <PERSON>'s past and true identity before it kills them.\",\n", "  'genres': ['Action', 'Drama', 'Sci-Fi'],\n", "  'poster': 'https://m.media-amazon.com/images/M/MV5BODIzMDQzMjQ3NF5BMl5BanBnXkFtZTcwMzExODM5NA@@._V1_SY1000_SX677_AL_.jpg',\n", "  'cast': ['<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>'],\n", "  'runtime': 96.0,\n", "  'plot': \"When someone tries to murder watchmaker <PERSON>, the incident triggers a barrage of nightmares and flashbacks into a past that isn't his own. Fearing for his sanity, <PERSON> contacts ...\",\n", "  'num_mflix_comments': 1},\n", " {'_id': ObjectId('663ce86a57b4141aa769a482'),\n", "  'directors': ['<PERSON>'],\n", "  'title': 'Resident Evil',\n", "  'imdb': {'id': 120804, 'rating': 6.7, 'votes': 185410},\n", "  'awards': {'nominations': 4, 'text': '4 nominations.', 'wins': 0},\n", "  'rated': 'R',\n", "  'countries': ['UK', 'Germany', 'France', 'USA'],\n", "  'type': 'movie',\n", "  'writers': ['<PERSON>'],\n", "  'metacritic': 33.0,\n", "  'languages': ['English'],\n", "  'fullplot': 'A virus has escaped in a secret facility called \"The Hive,\" turning the staff into hungry zombies and releasing the mutated Lab \"Animals\" that they were studying. The complex computer shuts down the base to prevent infection. The parent corporation sends in an elite military unit, where they meet <PERSON>, who is suffering from amnesia due to exposure to nerve gas. The military team must shut down the computer and get out, fighting their way past zombies, mutants, and the computer itself, before the virus escapes and infects the rest of the world. <PERSON> must also come to terms with her slowly-returning memories.',\n", "  'genres': ['Action', 'Fantasy', 'Horror'],\n", "  'poster': 'https://m.media-amazon.com/images/M/MV5BN2Y2MTljNjMtMDRlNi00ZWNhLThmMWItYTlmZjYyZDk4NzYxXkEyXkFqcGdeQXVyNjQ2MjQ5NzM@._V1_SY1000_SX677_AL_.jpg',\n", "  'cast': ['<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>'],\n", "  'runtime': 100.0,\n", "  'plot': 'A special military unit fights a powerful, out-of-control supercomputer and hundreds of scientists who have mutated into flesh-eating creatures after a laboratory accident.',\n", "  'num_mflix_comments': 1}]"]}, "metadata": {}, "execution_count": 84}]}, {"cell_type": "code", "source": ["combined_information = \"\"\n", "for i in range(len(mylist)):\n", "  fullplot=mylist[i][\"fullplot\"]\n", "  title=mylist[i][\"title\"]\n", "  combined_information += f\"Title:{title}, fullplot: {fullplot}\\n\""], "metadata": {"id": "GHqr8BXIx-uC"}, "execution_count": 87, "outputs": []}, {"cell_type": "code", "source": ["print(combined_information)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XGWVmhWgzJzh", "outputId": "9ac851e8-bd9c-4d07-c659-5a152f296e7c"}, "execution_count": 88, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Title:<PERSON>, fullplot: <PERSON> is in the world to rid all evil, even if not everyone agrees with him. The Vatican sends the monster hunter and his ally, <PERSON>, to Transylvania. They have been sent to this land to stop the powerful Count <PERSON>. Whilst there they join forces with a Gypsy Princess called <PERSON>, who is determined to end an ancient curse on her family by destroying the vampire. They just don't know how!\n", "Title:<PERSON>bomb, fullplot: When someone tries to murder watchmaker <PERSON>, the incident triggers a barrage of nightmares and flashbacks into a past that isn't his own. Fearing for his sanity, <PERSON> contacts psychiatrist Dr. <PERSON> for help. <PERSON> thinks he's hallucinating until another attack proves the dangers are all too real. The two of them go on the run, trying to discover the truth about <PERSON>'s past and true identity before it kills them.\n", "Title:Resident Evil, fullplot: A virus has escaped in a secret facility called \"The Hive,\" turning the staff into hungry zombies and releasing the mutated Lab \"Animals\" that they were studying. The complex computer shuts down the base to prevent infection. The parent corporation sends in an elite military unit, where they meet <PERSON>, who is suffering from amnesia due to exposure to nerve gas. The military team must shut down the computer and get out, fighting their way past zombies, mutants, and the computer itself, before the virus escapes and infects the rest of the world. <PERSON> must also come to terms with her slowly-returning memories.\n", "\n"]}]}, {"cell_type": "code", "source": ["query"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "94Ms5rAq8ysn", "outputId": "058878d3-8636-40db-e022-0fd0ae086750"}, "execution_count": 90, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'what is the best horror movie to watch and why?'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 90}]}, {"cell_type": "code", "source": ["prompt = f\"Query: {query}\\nContinue to answer the query by using the fullplot only:\\n{combined_information}.\""], "metadata": {"id": "Z5b06yDLzFgw"}, "execution_count": 91, "outputs": []}, {"cell_type": "code", "source": ["print(prompt)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RYHYR7fFztvD", "outputId": "6e3fa0aa-1a27-4e6c-ae08-be13473c06b3"}, "execution_count": 92, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Query: what is the best horror movie to watch and why?\n", "Continue to answer the query by using the fullplot only:\n", "Title:<PERSON>, fullplot: <PERSON> is in the world to rid all evil, even if not everyone agrees with him. The Vatican sends the monster hunter and his ally, <PERSON>, to Transylvania. They have been sent to this land to stop the powerful Count <PERSON>. Whilst there they join forces with a Gypsy Princess called <PERSON>, who is determined to end an ancient curse on her family by destroying the vampire. They just don't know how!\n", "Title:<PERSON>bomb, fullplot: When someone tries to murder watchmaker <PERSON>, the incident triggers a barrage of nightmares and flashbacks into a past that isn't his own. Fearing for his sanity, <PERSON> contacts psychiatrist Dr. <PERSON> for help. <PERSON> thinks he's hallucinating until another attack proves the dangers are all too real. The two of them go on the run, trying to discover the truth about <PERSON>'s past and true identity before it kills them.\n", "Title:Resident Evil, fullplot: A virus has escaped in a secret facility called \"The Hive,\" turning the staff into hungry zombies and releasing the mutated Lab \"Animals\" that they were studying. The complex computer shuts down the base to prevent infection. The parent corporation sends in an elite military unit, where they meet <PERSON>, who is suffering from amnesia due to exposure to nerve gas. The military team must shut down the computer and get out, fighting their way past zombies, mutants, and the computer itself, before the virus escapes and infects the rest of the world. <PERSON> must also come to terms with her slowly-returning memories.\n", ".\n"]}]}, {"cell_type": "code", "source": ["%pip install --upgrade  langchain-google-genai"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XqJxF7Z-zP5X", "outputId": "0adbaff1-8704-433a-c298-15a46bf1f6db"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain-google-genai\n", "  Downloading langchain_google_genai-1.0.3-py3-none-any.whl (31 kB)\n", "Requirement already satisfied: google-generativeai<0.6.0,>=0.5.2 in /usr/local/lib/python3.10/dist-packages (from langchain-google-genai) (0.5.2)\n", "Collecting langchain-core<0.2,>=0.1.45 (from langchain-google-genai)\n", "  Downloading langchain_core-0.1.52-py3-none-any.whl (302 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m302.9/302.9 kB\u001b[0m \u001b[31m8.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: google-ai-generativelanguage==0.6.2 in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (0.6.2)\n", "Requirement already satisfied: google-api-core in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (2.11.1)\n", "Requirement already satisfied: google-api-python-client in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (2.84.0)\n", "Requirement already satisfied: google-auth>=2.15.0 in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (2.27.0)\n", "Requirement already satisfied: protobuf in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (3.20.3)\n", "Requirement already satisfied: pydantic in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (2.7.1)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (4.66.4)\n", "Requirement already satisfied: typing-extensions in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (4.11.0)\n", "Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.3 in /usr/local/lib/python3.10/dist-packages (from google-ai-generativelanguage==0.6.2->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (1.23.0)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2,>=0.1.45->langchain-google-genai) (6.0.1)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain-core<0.2,>=0.1.45->langchain-google-genai)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Collecting langsmith<0.2.0,>=0.1.0 (from langchain-core<0.2,>=0.1.45->langchain-google-genai)\n", "  Downloading langsmith-0.1.56-py3-none-any.whl (120 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m120.8/120.8 kB\u001b[0m \u001b[31m14.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting packaging<24.0,>=23.2 (from langchain-core<0.2,>=0.1.45->langchain-google-genai)\n", "  Downloading packaging-23.2-py3-none-any.whl (53 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m53.0/53.0 kB\u001b[0m \u001b[31m6.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2,>=0.1.45->langchain-google-genai) (8.2.3)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth>=2.15.0->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (5.3.3)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth>=2.15.0->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (0.4.0)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth>=2.15.0->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (4.9)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core<0.2,>=0.1.45->langchain-google-genai)\n", "  Downloading jsonpointer-2.4-py2.py3-none-any.whl (7.8 kB)\n", "Collecting or<PERSON><PERSON><4.0.0,>=3.9.14 (from langsmith<0.2.0,>=0.1.0->langchain-core<0.2,>=0.1.45->langchain-google-genai)\n", "  Downloading orjson-3.10.3-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (142 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m142.5/142.5 kB\u001b[0m \u001b[31m17.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.0->langchain-core<0.2,>=0.1.45->langchain-google-genai) (2.31.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.2 in /usr/local/lib/python3.10/dist-packages (from pydantic->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (2.18.2)\n", "Requirement already satisfied: googleapis-common-protos<2.0.dev0,>=1.56.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (1.63.0)\n", "Requirement already satisfied: httplib2<1dev,>=0.15.0 in /usr/local/lib/python3.10/dist-packages (from google-api-python-client->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (0.22.0)\n", "Requirement already satisfied: google-auth-httplib2>=0.1.0 in /usr/local/lib/python3.10/dist-packages (from google-api-python-client->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (0.1.1)\n", "Requirement already satisfied: uritemplate<5,>=3.0.1 in /usr/local/lib/python3.10/dist-packages (from google-api-python-client->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (4.1.1)\n", "Requirement already satisfied: grpcio<2.0dev,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (1.63.0)\n", "Requirement already satisfied: grpcio-status<2.0.dev0,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (1.48.2)\n", "Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in /usr/local/lib/python3.10/dist-packages (from httplib2<1dev,>=0.15.0->google-api-python-client->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (3.1.2)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth>=2.15.0->google-generativeai<0.6.0,>=0.5.2->langchain-google-genai) (0.6.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langsmith<0.2.0,>=0.1.0->langchain-core<0.2,>=0.1.45->langchain-google-genai) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langsmith<0.2.0,>=0.1.0->langchain-core<0.2,>=0.1.45->langchain-google-genai) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langsmith<0.2.0,>=0.1.0->langchain-core<0.2,>=0.1.45->langchain-google-genai) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langsmith<0.2.0,>=0.1.0->langchain-core<0.2,>=0.1.45->langchain-google-genai) (2024.2.2)\n", "Installing collected packages: packaging, orjson, jsonpointer, jsonpatch, langsmith, langchain-core, langchain-google-genai\n", "  Attempting uninstall: packaging\n", "    Found existing installation: packaging 24.0\n", "    Uninstalling packaging-24.0:\n", "      Successfully uninstalled packaging-24.0\n", "Successfully installed jsonpatch-1.33 jsonpointer-2.4 langchain-core-0.1.52 langchain-google-genai-1.0.3 langsmith-0.1.56 orjson-3.10.3 packaging-23.2\n"]}]}, {"cell_type": "code", "source": ["import os\n", "from google.colab import userdata\n", "GOOGLE_API_KEY=userdata.get('GOOGLE_API_KEY')\n", "os.environ[\"GOOGLE_API_KEY\"] = GOOGLE_API_KEY"], "metadata": {"id": "wth_GgHHzTO4"}, "execution_count": 93, "outputs": []}, {"cell_type": "code", "source": ["from langchain_google_genai import ChatGoogleGenerativeAI\n", "def load_model(model_name):\n", "  if model_name==\"gemini-pro\":\n", "    llm = ChatGoogleGenerativeAI(model=\"gemini-pro\")\n", "  else:\n", "    llm=ChatGoogleGenerativeAI(model=\"gemini-pro-vision\")\n", "\n", "  return llm"], "metadata": {"id": "GwiF03ixzayg"}, "execution_count": 94, "outputs": []}, {"cell_type": "code", "source": ["model_text=load_model(\"gemini-pro\")"], "metadata": {"id": "MKc50pW9zgdo"}, "execution_count": 95, "outputs": []}, {"cell_type": "code", "source": ["model_text.invoke(prompt).content"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 87}, "id": "OWWfm8XRzlxY", "outputId": "61b0bec5-0a7a-4b08-e4d3-d55297bd3f74"}, "execution_count": 96, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'The best horror movie to watch depends on individual preferences. \"Van Helsing\" is a classic monster movie with a thrilling plot and visually stunning effects. \"Timebomb\" offers a unique and suspenseful experience with its twisty plot and psychological elements. \"Resident Evil\" is a popular horror franchise that combines action, horror, and science fiction elements. Ultimately, the best horror movie to watch is the one that resonates most with your personal interests and scares.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 96}]}, {"cell_type": "code", "source": [], "metadata": {"id": "nf6dl_t_zo-n"}, "execution_count": null, "outputs": []}]}