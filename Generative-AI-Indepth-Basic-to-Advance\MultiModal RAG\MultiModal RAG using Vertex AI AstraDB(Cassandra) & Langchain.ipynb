{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/sunnysavita10/Indepth-GENAI/blob/main/MultiModal%20RAG%20using%20Vertex%20AI%20AstraDB(Cassandra)%C2%A0%26%C2%A0Langchain.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["## Install Vertex AI SDK for Python"], "metadata": {"id": "Su9UaTllPPyT"}, "id": "Su9UaTllPPyT"}, {"cell_type": "code", "id": "sp5rXuilbYyz1ue2BFuSmJle", "metadata": {"tags": [], "id": "sp5rXuilbYyz1ue2BFuSmJle", "colab": {"base_uri": "https://localhost:8080/", "height": 845}, "outputId": "05bca8b5-a950-4817-fe9d-72c495126451"}, "source": ["!pip install --upgrade --user google-cloud-aiplatform"], "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: google-cloud-aiplatform in /usr/local/lib/python3.10/dist-packages (1.48.0)\n", "Collecting google-cloud-aiplatform\n", "  Downloading google_cloud_aiplatform-1.51.0-py2.py3-none-any.whl (5.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.0/5.0 MB\u001b[0m \u001b[31m23.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (2.11.1)\n", "Requirement already satisfied: google-auth<3.0.0dev,>=2.14.1 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (2.27.0)\n", "Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (1.23.0)\n", "Requirement already satisfied: protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (3.20.3)\n", "Requirement already satisfied: packaging>=14.3 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (24.0)\n", "Requirement already satisfied: google-cloud-storage<3.0.0dev,>=1.32.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (2.8.0)\n", "Requirement already satisfied: google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (3.21.0)\n", "Requirement already satisfied: google-cloud-resource-manager<3.0.0dev,>=1.3.3 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (1.12.3)\n", "Requirement already satisfied: shapely<3.0.0dev in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (2.0.4)\n", "Requirement already satisfied: pydantic<3 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (2.7.1)\n", "Requirement already satisfied: docstring-parser<1 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (0.16)\n", "Requirement already satisfied: googleapis-common-protos<2.0.dev0,>=1.56.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (1.63.0)\n", "Requirement already satisfied: requests<3.0.0.dev0,>=2.18.0 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (2.31.0)\n", "Requirement already satisfied: grpcio<2.0dev,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (1.63.0)\n", "Requirement already satisfied: grpcio-status<2.0.dev0,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (1.48.2)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0.0dev,>=2.14.1->google-cloud-aiplatform) (5.3.3)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0.0dev,>=2.14.1->google-cloud-aiplatform) (0.4.0)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0.0dev,>=2.14.1->google-cloud-aiplatform) (4.9)\n", "Requirement already satisfied: google-cloud-core<3.0.0dev,>=1.6.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform) (2.3.3)\n", "Requirement already satisfied: google-resumable-media<3.0dev,>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform) (2.7.0)\n", "Requirement already satisfied: python-dateutil<3.0dev,>=2.7.2 in /usr/local/lib/python3.10/dist-packages (from google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform) (2.8.2)\n", "Requirement already satisfied: grpc-google-iam-v1<1.0.0dev,>=0.12.4 in /usr/local/lib/python3.10/dist-packages (from google-cloud-resource-manager<3.0.0dev,>=1.3.3->google-cloud-aiplatform) (0.13.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3->google-cloud-aiplatform) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.2 in /usr/local/lib/python3.10/dist-packages (from pydantic<3->google-cloud-aiplatform) (2.18.2)\n", "Requirement already satisfied: typing-extensions>=4.6.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3->google-cloud-aiplatform) (4.11.0)\n", "Requirement already satisfied: numpy<3,>=1.14 in /usr/local/lib/python3.10/dist-packages (from shapely<3.0.0dev->google-cloud-aiplatform) (1.25.2)\n", "Requirement already satisfied: google-crc32c<2.0dev,>=1.0 in /usr/local/lib/python3.10/dist-packages (from google-resumable-media<3.0dev,>=0.6.0->google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform) (1.5.0)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth<3.0.0dev,>=2.14.1->google-cloud-aiplatform) (0.6.0)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil<3.0dev,>=2.7.2->google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform) (1.16.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (2024.2.2)\n", "Installing collected packages: google-cloud-aiplatform\n", "\u001b[33m  WARNING: The script tb-gcp-uploader is installed in '/root/.local/bin' which is not on PATH.\n", "  Consider adding this directory to PATH or, if you prefer to suppress this warning, use --no-warn-script-location.\u001b[0m\u001b[33m\n", "\u001b[0mSuccessfully installed google-cloud-aiplatform-1.51.0\n"]}, {"output_type": "display_data", "data": {"application/vnd.colab-display-data+json": {"pip_warning": {"packages": ["google"]}, "id": "6123d6b1a74449b5a25a22b8d0cc3605"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["!pip install ragstack-ai"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "DCoTFxquXd3m", "outputId": "97a836c7-7321-47f2-8850-57dd0709ad98"}, "id": "DCoTFxquXd3m", "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting ragstack-ai\n", "  Using cached ragstack_ai-1.0.3-py3-none-any.whl (4.2 kB)\n", "Collecting ragstack-ai-colbert==1.0.2 (from ragstack-ai)\n", "  Using cached ragstack_ai_colbert-1.0.2-py3-none-any.whl (19 kB)\n", "Collecting ragstack-ai-langchain[colbert,google,nvidia]==1.0.3 (from ragstack-ai)\n", "  Using cached ragstack_ai_langchain-1.0.3-py3-none-any.whl (4.8 kB)\n", "Collecting ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2 (from ragstack-ai)\n", "  Using cached ragstack_ai_llamaindex-1.0.2-py3-none-any.whl (3.1 kB)\n", "Collecting cassio<0.2.0,>=0.1.7 (from ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Using cached cassio-0.1.7-py3-none-any.whl (44 kB)\n", "Collecting colbert-ai==0.2.19 (from ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Using cached colbert-ai-0.2.19.tar.gz (86 kB)\n", "  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Collecting pyarrow==14.0.1 (from ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Downloading pyarrow-14.0.1-cp310-cp310-manylinux_2_28_x86_64.whl (38.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m38.0/38.0 MB\u001b[0m \u001b[31m28.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pydantic<3.0.0,>=2.7.1 in /usr/local/lib/python3.10/dist-packages (from ragstack-ai-colbert==1.0.2->ragstack-ai) (2.7.1)\n", "Requirement already satisfied: torch==2.2.1 in /usr/local/lib/python3.10/dist-packages (from ragstack-ai-colbert==1.0.2->ragstack-ai) (2.2.1+cu121)\n", "Collecting astrapy<2,>=1 (from ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading astrapy-1.1.0-py3-none-any.whl (124 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m124.4/124.4 kB\u001b[0m \u001b[31m3.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain==0.1.19 (from ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading langchain-0.1.19-py3-none-any.whl (1.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m49.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-astradb==0.3.0 (from ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading langchain_astradb-0.3.0-py3-none-any.whl (26 kB)\n", "Collecting langchain-community==0.0.38 (from ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading langchain_community-0.0.38-py3-none-any.whl (2.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m86.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-core==0.1.52 (from ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading langchain_core-0.1.52-py3-none-any.whl (302 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m302.9/302.9 kB\u001b[0m \u001b[31m34.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-openai==0.1.3 (from ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading langchain_openai-0.1.3-py3-none-any.whl (33 kB)\n", "Collecting unstructured==0.12.5 (from ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading unstructured-0.12.5-py3-none-any.whl (1.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.8/1.8 MB\u001b[0m \u001b[31m75.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-google-genai==0.0.11 (from ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading langchain_google_genai-0.0.11-py3-none-any.whl (28 kB)\n", "Collecting langchain-google-vertexai==1.0.1 (from ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading langchain_google_vertexai-1.0.1-py3-none-any.whl (53 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m53.3/53.3 kB\u001b[0m \u001b[31m8.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-nvidia-ai-endpoints==0.0.9 (from ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading langchain_nvidia_ai_endpoints-0.0.9-py3-none-any.whl (37 kB)\n", "Requirement already satisfied: cffi<2.0.0,>=1.16.0 in /usr/local/lib/python3.10/dist-packages (from ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai) (1.16.0)\n", "Collecting llama-index==0.10.31 (from ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index-0.10.31-py3-none-any.whl (6.9 kB)\n", "Collecting llama-index-embeddings-langchain==0.1.2 (from ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_embeddings_langchain-0.1.2-py3-none-any.whl (2.5 kB)\n", "Collecting llama-index-vector-stores-astra-db==0.1.7 (from ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_vector_stores_astra_db-0.1.7-py3-none-any.whl (6.1 kB)\n", "Collecting llama-index-vector-stores-cassandra==0.1.3 (from ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_vector_stores_cassandra-0.1.3-py3-none-any.whl (5.2 kB)\n", "Collecting llama-parse==0.4.1 (from ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_parse-0.4.1-py3-none-any.whl (7.3 kB)\n", "Collecting llama-index-embeddings-azure-openai==0.1.7 (from ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_embeddings_azure_openai-0.1.7-py3-none-any.whl (3.1 kB)\n", "Collecting llama-index-llms-azure-openai==0.1.6 (from ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_llms_azure_openai-0.1.6-py3-none-any.whl (4.8 kB)\n", "Collecting llama-index-embeddings-bedrock==0.1.4 (from ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_embeddings_bedrock-0.1.4-py3-none-any.whl (5.3 kB)\n", "Collecting llama-index-llms-bedrock==0.1.7 (from ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_llms_bedrock-0.1.7-py3-none-any.whl (8.2 kB)\n", "Collecting llama-index-embeddings-gemini==0.1.6 (from ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_embeddings_gemini-0.1.6-py3-none-any.whl (2.9 kB)\n", "Collecting llama-index-llms-gemini==0.1.7 (from ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_llms_gemini-0.1.7-py3-none-any.whl (4.8 kB)\n", "Collecting llama-index-llms-vertex==0.1.5 (from ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_llms_vertex-0.1.5-py3-none-any.whl (7.0 kB)\n", "Collecting llama-index-multi-modal-llms-gemini==0.1.5 (from ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_multi_modal_llms_gemini-0.1.5-py3-none-any.whl (3.9 kB)\n", "Collecting bitarray (from colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Downloading bitarray-2.9.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (288 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m288.3/288.3 kB\u001b[0m \u001b[31m31.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting datasets (from colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Downloading datasets-2.19.1-py3-none-any.whl (542 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m542.0/542.0 kB\u001b[0m \u001b[31m35.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: flask in /usr/local/lib/python3.10/dist-packages (from colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai) (2.2.5)\n", "Collecting git-python (from colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Downloading git_python-1.0.3-py2.py3-none-any.whl (1.9 kB)\n", "Collecting python-dotenv (from colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Downloading python_dotenv-1.0.1-py3-none-any.whl (19 kB)\n", "Collecting ninja (from colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Downloading ninja-********-py2.py3-none-manylinux1_x86_64.manylinux_2_5_x86_64.whl (307 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m307.2/307.2 kB\u001b[0m \u001b[31m15.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: scipy in /usr/local/lib/python3.10/dist-packages (from colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai) (1.11.4)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai) (4.66.4)\n", "Requirement already satisfied: transformers in /usr/local/lib/python3.10/dist-packages (from colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai) (4.40.2)\n", "Collecting ujson (from colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Downloading ujson-5.10.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (53 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m53.6/53.6 kB\u001b[0m \u001b[31m7.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (2.0.30)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (3.9.5)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (4.0.3)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading dataclasses_json-0.6.6-py3-none-any.whl (28 kB)\n", "Collecting langchain-text-splitters<0.1,>=0.0.1 (from langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading langchain_text_splitters-0.0.1-py3-none-any.whl (21 kB)\n", "Collecting langsmith<0.2.0,>=0.1.17 (from langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading langsmith-0.1.57-py3-none-any.whl (121 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m121.0/121.0 kB\u001b[0m \u001b[31m15.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (1.25.2)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (8.3.0)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain-core==0.1.52->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Collecting packaging<24.0,>=23.2 (from langchain-core==0.1.52->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading packaging-23.2-py3-none-any.whl (53 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m53.0/53.0 kB\u001b[0m \u001b[31m6.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting google-generativeai<0.5.0,>=0.4.1 (from langchain-google-genai==0.0.11->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading google_generativeai-0.4.1-py3-none-any.whl (137 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m137.4/137.4 kB\u001b[0m \u001b[31m15.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: google-cloud-aiplatform<2.0.0,>=1.47.0 in /root/.local/lib/python3.10/site-packages (from langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (1.51.0)\n", "Collecting google-cloud-storage<3.0.0,>=2.14.0 (from langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading google_cloud_storage-2.16.0-py2.py3-none-any.whl (125 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m125.6/125.6 kB\u001b[0m \u001b[31m16.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting types-protobuf<*******,>=******** (from langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading types_protobuf-4.25.0.20240417-py3-none-any.whl (67 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.9/67.9 kB\u001b[0m \u001b[31m6.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting types-requests<3.0.0,>=2.31.0 (from langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading types_requests-2.31.0.20240406-py3-none-any.whl (15 kB)\n", "Collecting pillow<11.0.0,>=10.0.0 (from langchain-nvidia-ai-endpoints==0.0.9->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading pillow-10.3.0-cp310-cp310-manylinux_2_28_x86_64.whl (4.5 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.5/4.5 MB\u001b[0m \u001b[31m63.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting openai<2.0.0,>=1.10.0 (from langchain-openai==0.1.3->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading openai-1.30.0-py3-none-any.whl (320 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m320.7/320.7 kB\u001b[0m \u001b[31m21.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting tiktoken<1,>=0.5.2 (from langchain-openai==0.1.3->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading tiktoken-0.7.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.1 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m27.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting llama-index-agent-openai<0.3.0,>=0.1.4 (from llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_agent_openai-0.2.4-py3-none-any.whl (13 kB)\n", "Collecting llama-index-cli<0.2.0,>=0.1.2 (from llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_cli-0.1.12-py3-none-any.whl (26 kB)\n", "Collecting llama-index-core<0.11.0,>=0.10.31 (from llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_core-0.10.36-py3-none-any.whl (15.4 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m15.4/15.4 MB\u001b[0m \u001b[31m24.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting llama-index-embeddings-openai<0.2.0,>=0.1.5 (from llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_embeddings_openai-0.1.9-py3-none-any.whl (6.0 kB)\n", "Collecting llama-index-indices-managed-llama-cloud<0.2.0,>=0.1.2 (from llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_indices_managed_llama_cloud-0.1.6-py3-none-any.whl (6.7 kB)\n", "Collecting llama-index-legacy<0.10.0,>=0.9.48 (from llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_legacy-0.9.48-py3-none-any.whl (2.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m57.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting llama-index-llms-openai<0.2.0,>=0.1.13 (from llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_llms_openai-0.1.19-py3-none-any.whl (11 kB)\n", "Collecting llama-index-multi-modal-llms-openai<0.2.0,>=0.1.3 (from llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_multi_modal_llms_openai-0.1.6-py3-none-any.whl (5.8 kB)\n", "Collecting llama-index-program-openai<0.2.0,>=0.1.3 (from llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_program_openai-0.1.6-py3-none-any.whl (5.2 kB)\n", "Collecting llama-index-question-gen-openai<0.2.0,>=0.1.2 (from llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_question_gen_openai-0.1.3-py3-none-any.whl (2.9 kB)\n", "Collecting llama-index-readers-file<0.2.0,>=0.1.4 (from llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_readers_file-0.1.22-py3-none-any.whl (36 kB)\n", "Collecting llama-index-readers-llama-parse<0.2.0,>=0.1.2 (from llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_readers_llama_parse-0.1.4-py3-none-any.whl (2.5 kB)\n", "Collecting boto3<2.0.0,>=1.34.23 (from llama-index-embeddings-bedrock==0.1.4->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading boto3-1.34.104-py3-none-any.whl (139 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m139.3/139.3 kB\u001b[0m \u001b[31m18.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting azure-identity<2.0.0,>=1.15.0 (from llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading azure_identity-1.16.0-py3-none-any.whl (166 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m166.1/166.1 kB\u001b[0m \u001b[31m21.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting httpx (from llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading httpx-0.27.0-py3-none-any.whl (75 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m10.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting llama-index-llms-anthropic<0.2.0,>=0.1.7 (from llama-index-llms-bedrock==0.1.7->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llama_index_llms_anthropic-0.1.11-py3-none-any.whl (6.1 kB)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai) (3.14.0)\n", "Requirement already satisfied: typing-extensions>=4.8.0 in /usr/local/lib/python3.10/dist-packages (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai) (4.11.0)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai) (1.12)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai) (3.3)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai) (3.1.4)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.10/dist-packages (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai) (2023.6.0)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.1.105 (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Using cached nvidia_cuda_nvrtc_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (23.7 MB)\n", "Collecting nvidia-cuda-runtime-cu12==12.1.105 (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Using cached nvidia_cuda_runtime_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (823 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.1.105 (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Using cached nvidia_cuda_cupti_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (14.1 MB)\n", "Collecting nvidia-cudnn-cu12==******** (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Using cached nvidia_cudnn_cu12-********-py3-none-manylinux1_x86_64.whl (731.7 MB)\n", "Collecting nvidia-cublas-cu12==******** (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Using cached nvidia_cublas_cu12-********-py3-none-manylinux1_x86_64.whl (410.6 MB)\n", "Collecting nvidia-cufft-cu12==********* (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Using cached nvidia_cufft_cu12-*********-py3-none-manylinux1_x86_64.whl (121.6 MB)\n", "Collecting nvidia-curand-cu12==********** (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Using cached nvidia_curand_cu12-**********-py3-none-manylinux1_x86_64.whl (56.5 MB)\n", "Collecting nvidia-cusolver-cu12==********** (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Using cached nvidia_cusolver_cu12-**********-py3-none-manylinux1_x86_64.whl (124.2 MB)\n", "Collecting nvidia-cusparse-cu12==********** (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Using cached nvidia_cusparse_cu12-**********-py3-none-manylinux1_x86_64.whl (196.0 MB)\n", "Collecting nvidia-nccl-cu12==2.19.3 (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Using cached nvidia_nccl_cu12-2.19.3-py3-none-manylinux1_x86_64.whl (166.0 MB)\n", "Collecting nvidia-nvtx-cu12==12.1.105 (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Using cached nvidia_nvtx_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (99 kB)\n", "Requirement already satisfied: triton==2.2.0 in /usr/local/lib/python3.10/dist-packages (from torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai) (2.2.0)\n", "Requirement already satisfied: chardet in /usr/local/lib/python3.10/dist-packages (from unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (5.2.0)\n", "Collecting filetype (from unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading filetype-1.2.0-py2.py3-none-any.whl (19 kB)\n", "Collecting python-magic (from unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading python_magic-0.4.27-py2.py3-none-any.whl (13 kB)\n", "Requirement already satisfied: lxml in /usr/local/lib/python3.10/dist-packages (from unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (4.9.4)\n", "Requirement already satisfied: nltk in /usr/local/lib/python3.10/dist-packages (from unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (3.8.1)\n", "Requirement already satisfied: tabulate in /usr/local/lib/python3.10/dist-packages (from unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (0.9.0)\n", "Requirement already satisfied: beautifulsoup4 in /usr/local/lib/python3.10/dist-packages (from unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (4.12.3)\n", "Collecting emoji (from unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading emoji-2.11.1-py2.py3-none-any.whl (433 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m433.8/433.8 kB\u001b[0m \u001b[31m49.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting python-iso639 (from unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading python_iso639-2024.4.27-py3-none-any.whl (274 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m274.7/274.7 kB\u001b[0m \u001b[31m36.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langdetect (from unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading langdetect-1.0.9.tar.gz (981 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m981.5/981.5 kB\u001b[0m \u001b[31m64.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Collecting rapidfuzz (from unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading rapidfuzz-3.9.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.4 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.4/3.4 MB\u001b[0m \u001b[31m105.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting backoff (from unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading backoff-2.2.1-py3-none-any.whl (15 kB)\n", "Collecting unstructured-client>=0.15.1 (from unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading unstructured_client-0.22.0-py3-none-any.whl (28 kB)\n", "Requirement already satisfied: wrapt in /usr/local/lib/python3.10/dist-packages (from unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (1.14.1)\n", "Collecting nvidia-nvjitlink-cu12 (from nvidia-cusolver-cu12==**********->torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Using cached nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n", "Collecting bson<0.6.0,>=0.5.10 (from astrapy<2,>=1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading bson-0.5.10.tar.gz (10 kB)\n", "  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Collecting deprecation<2.2.0,>=2.1.0 (from astrapy<2,>=1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading deprecation-2.1.0-py2.py3-none-any.whl (11 kB)\n", "Requirement already satisfied: toml<0.11.0,>=0.10.2 in /usr/local/lib/python3.10/dist-packages (from astrapy<2,>=1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (0.10.2)\n", "Collecting uuid6<2024.2.0,>=2024.1.12 (from astrapy<2,>=1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading uuid6-2024.1.12-py3-none-any.whl (6.4 kB)\n", "Collecting cassandra-driver<4.0.0,>=3.28.0 (from cassio<0.2.0,>=0.1.7->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Downloading cassandra_driver-3.29.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (18.9 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m18.9/18.9 MB\u001b[0m \u001b[31m64.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pycparser in /usr/local/lib/python3.10/dist-packages (from cffi<2.0.0,>=1.16.0->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai) (2.22)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0.0,>=2.7.1->ragstack-ai-colbert==1.0.2->ragstack-ai) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.2 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0.0,>=2.7.1->ragstack-ai-colbert==1.0.2->ragstack-ai) (2.18.2)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (1.9.4)\n", "Collecting azure-core>=1.23.0 (from azure-identity<2.0.0,>=1.15.0->llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading azure_core-1.30.1-py3-none-any.whl (193 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m193.4/193.4 kB\u001b[0m \u001b[31m28.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: cryptography>=2.5 in /usr/local/lib/python3.10/dist-packages (from azure-identity<2.0.0,>=1.15.0->llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai) (42.0.7)\n", "Collecting msal>=1.24.0 (from azure-identity<2.0.0,>=1.15.0->llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading msal-1.28.0-py3-none-any.whl (102 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m102.2/102.2 kB\u001b[0m \u001b[31m15.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting msal-extensions>=0.3.0 (from azure-identity<2.0.0,>=1.15.0->llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading msal_extensions-1.1.0-py3-none-any.whl (19 kB)\n", "Collecting botocore<1.35.0,>=1.34.104 (from boto3<2.0.0,>=1.34.23->llama-index-embeddings-bedrock==0.1.4->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading botocore-1.34.104-py3-none-any.whl (12.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m12.2/12.2 MB\u001b[0m \u001b[31m75.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting jmespath<2.0.0,>=0.7.1 (from boto3<2.0.0,>=1.34.23->llama-index-embeddings-bedrock==0.1.4->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading jmespath-1.0.1-py3-none-any.whl (20 kB)\n", "Collecting s3transfer<0.11.0,>=0.10.0 (from boto3<2.0.0,>=1.34.23->llama-index-embeddings-bedrock==0.1.4->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading s3transfer-0.10.1-py3-none-any.whl (82 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m82.2/82.2 kB\u001b[0m \u001b[31m12.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: python-dateutil>=2.4.0 in /usr/local/lib/python3.10/dist-packages (from bson<0.6.0,>=0.5.10->astrapy<2,>=1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (2.8.2)\n", "Requirement already satisfied: six>=1.9.0 in /usr/local/lib/python3.10/dist-packages (from bson<0.6.0,>=0.5.10->astrapy<2,>=1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (1.16.0)\n", "Collecting geomet<0.3,>=0.1 (from cassandra-driver<4.0.0,>=3.28.0->cassio<0.2.0,>=0.1.7->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Downloading geomet-0.2.1.post1-py3-none-any.whl (18 kB)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading marshmallow-3.21.2-py3-none-any.whl (49 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.3/49.3 kB\u001b[0m \u001b[31m5.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Requirement already satisfied: google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform<2.0.0,>=1.47.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (2.11.1)\n", "Requirement already satisfied: google-auth<3.0.0dev,>=2.14.1 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform<2.0.0,>=1.47.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (2.27.0)\n", "Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform<2.0.0,>=1.47.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (1.23.0)\n", "Requirement already satisfied: protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform<2.0.0,>=1.47.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (3.20.3)\n", "Requirement already satisfied: google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform<2.0.0,>=1.47.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (3.21.0)\n", "Requirement already satisfied: google-cloud-resource-manager<3.0.0dev,>=1.3.3 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform<2.0.0,>=1.47.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (1.12.3)\n", "Requirement already satisfied: shapely<3.0.0dev in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform<2.0.0,>=1.47.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (2.0.4)\n", "Requirement already satisfied: docstring-parser<1 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform<2.0.0,>=1.47.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (0.16)\n", "Collecting google-api-core<3.0.0dev,>=2.15.0 (from google-cloud-storage<3.0.0,>=2.14.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading google_api_core-2.19.0-py3-none-any.whl (139 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m139.0/139.0 kB\u001b[0m \u001b[31m20.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: google-cloud-core<3.0dev,>=2.3.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-storage<3.0.0,>=2.14.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (2.3.3)\n", "Requirement already satisfied: google-resumable-media>=2.6.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-storage<3.0.0,>=2.14.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (2.7.0)\n", "Requirement already satisfied: google-crc32c<2.0dev,>=1.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-storage<3.0.0,>=2.14.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (1.5.0)\n", "Collecting google-ai-generativelanguage==0.4.0 (from google-generativeai<0.5.0,>=0.4.1->langchain-google-genai==0.0.11->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading google_ai_generativelanguage-0.4.0-py3-none-any.whl (598 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m598.7/598.7 kB\u001b[0m \u001b[31m58.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai) (2024.2.2)\n", "Collecting httpcore==1.* (from httpx->llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m12.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai) (3.7)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai) (1.3.1)\n", "Collecting h2<5,>=3 (from httpx->llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading h2-4.1.0-py3-none-any.whl (57 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m57.5/57.5 kB\u001b[0m \u001b[31m9.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting h11<0.15,>=0.13 (from httpcore==1.*->httpx->llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m9.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core==0.1.52->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading jsonpointer-2.4-py2.py3-none-any.whl (7.8 kB)\n", "Collecting <PERSON><PERSON><PERSON><4.0.0,>=3.9.14 (from langsmith<0.2.0,>=0.1.17->langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading orjson-3.10.3-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (142 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m142.5/142.5 kB\u001b[0m \u001b[31m14.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting deprecated>=1.2.9.3 (from llama-index-core<0.11.0,>=0.10.31->llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading Deprecated-1.2.14-py2.py3-none-any.whl (9.6 kB)\n", "Collecting <PERSON><PERSON><PERSON><2.0.0,>=1.0.8 (from llama-index-core<0.11.0,>=0.10.31->llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading dirtyjson-1.0.8-py3-none-any.whl (25 kB)\n", "Collecting llamaindex-py-client<0.2.0,>=0.1.18 (from llama-index-core<0.11.0,>=0.10.31->llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading llamaindex_py_client-0.1.19-py3-none-any.whl (141 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m141.9/141.9 kB\u001b[0m \u001b[31m22.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: nest-asyncio<2.0.0,>=1.5.8 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.31->llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai) (1.6.0)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.31->llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai) (2.0.3)\n", "Collecting anthropic<0.24.0,>=0.23.1 (from llama-index-llms-anthropic<0.2.0,>=0.1.7->llama-index-llms-bedrock==0.1.7->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading anthropic-0.23.1-py3-none-any.whl (869 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m869.1/869.1 kB\u001b[0m \u001b[31m68.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting pypdf<5.0.0,>=4.0.1 (from llama-index-readers-file<0.2.0,>=0.1.4->llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading pypdf-4.2.0-py3-none-any.whl (290 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m290.4/290.4 kB\u001b[0m \u001b[31m35.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting striprtf<0.0.27,>=0.0.26 (from llama-index-readers-file<0.2.0,>=0.1.4->llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading striprtf-0.0.26-py3-none-any.whl (6.9 kB)\n", "Requirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.10/dist-packages (from beautifulsoup4->unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (2.5)\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (8.1.7)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (1.4.2)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (2023.12.25)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai<2.0.0,>=1.10.0->langchain-openai==0.1.3->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (1.7.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (2.0.7)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain==0.1.19->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (3.0.3)\n", "Collecting deepdiff>=6.0 (from unstructured-client>=0.15.1->unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading deepdiff-7.0.1-py3-none-any.whl (80 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m80.8/80.8 kB\u001b[0m \u001b[31m12.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting jsonpath-python>=1.0.6 (from unstructured-client>=0.15.1->unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading jsonpath_python-1.0.6-py3-none-any.whl (7.6 kB)\n", "Collecting mypy-extensions>=1.0.0 (from unstructured-client>=0.15.1->unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Requirement already satisfied: pyarrow-hotfix in /usr/local/lib/python3.10/dist-packages (from datasets->colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai) (0.6)\n", "Collecting dill<0.3.9,>=0.3.0 (from datasets->colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Downloading dill-0.3.8-py3-none-any.whl (116 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m116.3/116.3 kB\u001b[0m \u001b[31m18.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting xxhash (from datasets->colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Downloading xxhash-3.4.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (194 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m194.1/194.1 kB\u001b[0m \u001b[31m23.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting multiprocess (from datasets->colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Downloading multiprocess-0.70.16-py310-none-any.whl (134 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m134.8/134.8 kB\u001b[0m \u001b[31m19.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting huggingface-hub>=0.21.2 (from datasets->colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Downloading huggingface_hub-0.23.0-py3-none-any.whl (401 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m401.2/401.2 kB\u001b[0m \u001b[31m47.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: Werkzeug>=2.2.2 in /usr/local/lib/python3.10/dist-packages (from flask->colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai) (3.0.3)\n", "Requirement already satisfied: itsdangerous>=2.0 in /usr/local/lib/python3.10/dist-packages (from flask->colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai) (2.2.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai) (2.1.5)\n", "Collecting gitpython (from git-python->colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Downloading GitPython-3.1.43-py3-none-any.whl (207 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.3/207.3 kB\u001b[0m \u001b[31m28.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->torch==2.2.1->ragstack-ai-colbert==1.0.2->ragstack-ai) (1.3.0)\n", "Requirement already satisfied: tokenizers<0.20,>=0.19 in /usr/local/lib/python3.10/dist-packages (from transformers->colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai) (0.19.1)\n", "Requirement already satisfied: safetensors>=0.4.1 in /usr/local/lib/python3.10/dist-packages (from transformers->colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai) (0.4.3)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx->llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai) (1.2.1)\n", "Collecting ordered-set<4.2.0,>=4.1.0 (from deepdiff>=6.0->unstructured-client>=0.15.1->unstructured==0.12.5->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai)\n", "  Downloading ordered_set-4.1.0-py3-none-any.whl (7.6 kB)\n", "Requirement already satisfied: googleapis-common-protos<2.0.dev0,>=1.56.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core<3.0.0dev,>=2.15.0->google-cloud-storage<3.0.0,>=2.14.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (1.63.0)\n", "INFO: pip is looking at multiple versions of google-api-core[grpc] to determine which version is compatible with other requirements. This could take a while.\n", "Requirement already satisfied: grpcio<2.0dev,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core<3.0.0dev,>=2.15.0->google-cloud-storage<3.0.0,>=2.14.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (1.63.0)\n", "Requirement already satisfied: grpcio-status<2.0.dev0,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core<3.0.0dev,>=2.15.0->google-cloud-storage<3.0.0,>=2.14.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (1.48.2)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0.0dev,>=2.14.1->google-cloud-aiplatform<2.0.0,>=1.47.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (5.3.3)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0.0dev,>=2.14.1->google-cloud-aiplatform<2.0.0,>=1.47.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (0.4.0)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0.0dev,>=2.14.1->google-cloud-aiplatform<2.0.0,>=1.47.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (4.9)\n", "Requirement already satisfied: grpc-google-iam-v1<1.0.0dev,>=0.12.4 in /usr/local/lib/python3.10/dist-packages (from google-cloud-resource-manager<3.0.0dev,>=1.3.3->google-cloud-aiplatform<2.0.0,>=1.47.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (0.13.0)\n", "Collecting hyperframe<7,>=6.0 (from h2<5,>=3->httpx->llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading hyperframe-6.0.1-py3-none-any.whl (12 kB)\n", "Collecting hpack<5,>=4.0 (from h2<5,>=3->httpx->llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading hpack-4.0.0-py3-none-any.whl (32 kB)\n", "Requirement already satisfied: PyJWT[crypto]<3,>=1.0.0 in /usr/lib/python3/dist-packages (from msal>=1.24.0->azure-identity<2.0.0,>=1.15.0->llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai) (2.3.0)\n", "Collecting portalocker<3,>=1.0 (from msal-extensions>=0.3.0->azure-identity<2.0.0,>=1.15.0->llama-index-llms-azure-openai==0.1.6->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai)\n", "  Downloading portalocker-2.8.2-py3-none-any.whl (17 kB)\n", "Collecting gitdb<5,>=4.0.1 (from gitpython->git-python->colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Downloading gitdb-4.0.11-py3-none-any.whl (62 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m62.7/62.7 kB\u001b[0m \u001b[31m9.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.31->llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.31->llama-index==0.10.31->ragstack-ai-llamaindex[azure,bedrock,colbert,google]==1.0.2->ragstack-ai) (2024.1)\n", "Collecting smmap<6,>=3.0.1 (from gitdb<5,>=4.0.1->gitpython->git-python->colbert-ai==0.2.19->ragstack-ai-colbert==1.0.2->ragstack-ai)\n", "  Downloading smmap-5.0.1-py3-none-any.whl (24 kB)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth<3.0.0dev,>=2.14.1->google-cloud-aiplatform<2.0.0,>=1.47.0->langchain-google-vertexai==1.0.1->ragstack-ai-langchain[colbert,google,nvidia]==1.0.3->ragstack-ai) (0.6.0)\n", "Building wheels for collected packages: colbert-ai, bson, langdetect\n", "  Building wheel for colbert-ai (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for colbert-ai: filename=colbert_ai-0.2.19-py3-none-any.whl size=114762 sha256=28a947e5d0cdce7980cf2c570ef5541d5e6693d84097f5c37eba058bd7881a46\n", "  Stored in directory: /root/.cache/pip/wheels/90/b9/63/d4fc276c73c42ef7fc1065a26cf87e5a1cf56ef6498cbfbe5d\n", "  Building wheel for bson (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for bson: filename=bson-0.5.10-py3-none-any.whl size=11976 sha256=da4924d82b4f776731a8c77abd7f4c6ffb557aeb240b687eba24e8d131f0732e\n", "  Stored in directory: /root/.cache/pip/wheels/36/49/3b/8b33954dfae7a176009c4d721a45af56c8a9c1cdc3ee947945\n", "  Building wheel for langdetect (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for langdetect: filename=langdetect-1.0.9-py3-none-any.whl size=993227 sha256=55d76340415b90bc44caf54c70a4e0088836d56e3756985f8395bb711a1450e9\n", "  Stored in directory: /root/.cache/pip/wheels/95/03/7d/59ea870c70ce4e5a370638b5462a7711ab78fba2f655d05106\n", "Successfully built colbert-ai bson langdetect\n", "Installing collected packages: striprtf, ninja, filetype, dirtyjson, bitarray, xxhash, uuid6, ujson, types-requests, types-protobuf, smmap, rapidfuzz, python-magic, python-iso639, python-dotenv, pypdf, pyarrow, portalocker, pillow, packaging, orjson, ordered-set, nvidia-nvtx-cu12, nvidia-nvjitlink-cu12, nvidia-nccl-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, mypy-extensions, langdetect, jsonpointer, jsonpath-python, jmespath, hyperframe, hpack, h11, geomet, emoji, dill, deprecated, backoff, typing-inspect, tiktoken, nvidia-cusparse-cu12, nvidia-cudnn-cu12, multiprocess, marshmallow, jsonpatch, huggingface-hub, httpcore, h2, gitdb, deprecation, deepdiff, cassandra-driver, bson, botocore, azure-core, s3transfer, nvidia-cusolver-cu12, langsmith, httpx, google-api-core, gitpython, dataclasses-json, cassio, unstructured-client, openai, msal, llamaindex-py-client, langchain-core, git-python, datasets, boto3, anthropic, unstructured, msal-extensions, llama-index-legacy, llama-index-core, langchain-text-splitters, langchain-openai, langchain-nvidia-ai-endpoints, langchain-community, google-cloud-storage, google-ai-generativelanguage, colbert-ai, astrapy, ragstack-ai-colbert, llama-parse, llama-index-vector-stores-cassandra, llama-index-vector-stores-astra-db, llama-index-readers-file, llama-index-llms-openai, llama-index-llms-anthropic, llama-index-indices-managed-llama-cloud, llama-index-embeddings-openai, llama-index-embeddings-langchain, llama-index-embeddings-bedrock, langchain-astradb, langchain, google-generativeai, azure-identity, ragstack-ai-langchain, llama-index-readers-llama-parse, llama-index-multi-modal-llms-openai, llama-index-llms-vertex, llama-index-llms-gemini, llama-index-llms-bedrock, llama-index-llms-azure-openai, llama-index-embeddings-gemini, llama-index-cli, llama-index-agent-openai, langchain-google-vertexai, langchain-google-genai, llama-index-program-openai, llama-index-multi-modal-llms-gemini, llama-index-embeddings-azure-openai, llama-index-question-gen-openai, llama-index, ragstack-ai-llamaindex, ragstack-ai\n", "  Attempting uninstall: p<PERSON><PERSON>\n", "    Found existing installation: pyarrow 14.0.2\n", "    Uninstalling pyarrow-14.0.2:\n", "      Successfully uninstalled pyarrow-14.0.2\n", "  Attempting uninstall: pillow\n", "    Found existing installation: Pillow 9.4.0\n", "    Uninstalling Pillow-9.4.0:\n", "      Successfully uninstalled Pillow-9.4.0\n", "  Attempting uninstall: packaging\n", "    Found existing installation: packaging 24.0\n", "    Uninstalling packaging-24.0:\n", "      Successfully uninstalled packaging-24.0\n", "  Attempting uninstall: huggingface-hub\n", "    Found existing installation: huggingface-hub 0.20.3\n", "    Uninstalling huggingface-hub-0.20.3:\n", "      Successfully uninstalled huggingface-hub-0.20.3\n", "  Attempting uninstall: google-api-core\n", "    Found existing installation: google-api-core 2.11.1\n", "    Uninstalling google-api-core-2.11.1:\n", "      Successfully uninstalled google-api-core-2.11.1\n", "  Attempting uninstall: google-cloud-storage\n", "    Found existing installation: google-cloud-storage 2.8.0\n", "    Uninstalling google-cloud-storage-2.8.0:\n", "      Successfully uninstalled google-cloud-storage-2.8.0\n", "  Attempting uninstall: google-ai-generativelanguage\n", "    Found existing installation: google-ai-generativelanguage 0.6.2\n", "    Uninstalling google-ai-generativelanguage-0.6.2:\n", "      Successfully uninstalled google-ai-generativelanguage-0.6.2\n", "  Attempting uninstall: google-generativeai\n", "    Found existing installation: google-generativeai 0.5.2\n", "    Uninstalling google-generativeai-0.5.2:\n", "      Successfully uninstalled google-generativeai-0.5.2\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "imageio 2.31.6 requires pillow<10.1.0,>=8.3.2, but you have pillow 10.3.0 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed anthropic-0.23.1 astrapy-1.1.0 azure-core-1.30.1 azure-identity-1.16.0 backoff-2.2.1 bitarray-2.9.2 boto3-1.34.104 botocore-1.34.104 bson-0.5.10 cassandra-driver-3.29.1 cassio-0.1.7 colbert-ai-0.2.19 dataclasses-json-0.6.6 datasets-2.19.1 deepdiff-7.0.1 deprecated-1.2.14 deprecation-2.1.0 dill-0.3.8 dirtyjson-1.0.8 emoji-2.11.1 filetype-1.2.0 geomet-0.2.1.post1 git-python-1.0.3 gitdb-4.0.11 gitpython-3.1.43 google-ai-generativelanguage-0.4.0 google-api-core-2.19.0 google-cloud-storage-2.16.0 google-generativeai-0.4.1 h11-0.14.0 h2-4.1.0 hpack-4.0.0 httpcore-1.0.5 httpx-0.27.0 huggingface-hub-0.23.0 hyperframe-6.0.1 jmespath-1.0.1 jsonpatch-1.33 jsonpath-python-1.0.6 jsonpointer-2.4 langchain-0.1.19 langchain-astradb-0.3.0 langchain-community-0.0.38 langchain-core-0.1.52 langchain-google-genai-0.0.11 langchain-google-vertexai-1.0.1 langchain-nvidia-ai-endpoints-0.0.9 langchain-openai-0.1.3 langchain-text-splitters-0.0.1 langdetect-1.0.9 langsmith-0.1.57 llama-index-0.10.31 llama-index-agent-openai-0.2.4 llama-index-cli-0.1.12 llama-index-core-0.10.36 llama-index-embeddings-azure-openai-0.1.7 llama-index-embeddings-bedrock-0.1.4 llama-index-embeddings-gemini-0.1.6 llama-index-embeddings-langchain-0.1.2 llama-index-embeddings-openai-0.1.9 llama-index-indices-managed-llama-cloud-0.1.6 llama-index-legacy-0.9.48 llama-index-llms-anthropic-0.1.11 llama-index-llms-azure-openai-0.1.6 llama-index-llms-bedrock-0.1.7 llama-index-llms-gemini-0.1.7 llama-index-llms-openai-0.1.19 llama-index-llms-vertex-0.1.5 llama-index-multi-modal-llms-gemini-0.1.5 llama-index-multi-modal-llms-openai-0.1.6 llama-index-program-openai-0.1.6 llama-index-question-gen-openai-0.1.3 llama-index-readers-file-0.1.22 llama-index-readers-llama-parse-0.1.4 llama-index-vector-stores-astra-db-0.1.7 llama-index-vector-stores-cassandra-0.1.3 llama-parse-0.4.1 llamaindex-py-client-0.1.19 marshmallow-3.21.2 msal-1.28.0 msal-extensions-1.1.0 multiprocess-0.70.16 mypy-extensions-1.0.0 ninja-******** nvidia-cublas-cu12-******** nvidia-cuda-cupti-cu12-12.1.105 nvidia-cuda-nvrtc-cu12-12.1.105 nvidia-cuda-runtime-cu12-12.1.105 nvidia-cudnn-cu12-******** nvidia-cufft-cu12-********* nvidia-curand-cu12-********** nvidia-cusolver-cu12-********** nvidia-cusparse-cu12-********** nvidia-nccl-cu12-2.19.3 nvidia-nvjitlink-cu12-12.4.127 nvidia-nvtx-cu12-12.1.105 openai-1.30.0 ordered-set-4.1.0 orjson-3.10.3 packaging-23.2 pillow-10.3.0 portalocker-2.8.2 pyarrow-14.0.1 pypdf-4.2.0 python-dotenv-1.0.1 python-iso639-2024.4.27 python-magic-0.4.27 ragstack-ai-1.0.3 ragstack-ai-colbert-1.0.2 ragstack-ai-langchain-1.0.3 ragstack-ai-llamaindex-1.0.2 rapidfuzz-3.9.0 s3transfer-0.10.1 smmap-5.0.1 striprtf-0.0.26 tiktoken-0.7.0 types-protobuf-4.25.0.20240417 types-requests-2.31.0.20240406 typing-inspect-0.9.0 ujson-5.10.0 unstructured-0.12.5 unstructured-client-0.22.0 uuid6-2024.1.12 xxhash-3.4.1\n"]}, {"output_type": "display_data", "data": {"application/vnd.colab-display-data+json": {"pip_warning": {"packages": ["PIL", "google"]}, "id": "c63c4d7878574169afbde96bfb643b69"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["PROJECT_ID = \"red-delight-346705\"\n", "LOCATION = \"us-central1\""], "metadata": {"id": "xh6SW67NmMaj"}, "id": "xh6SW67NmMaj", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["ASTRA_DB_API_ENDPOINT=\"https://79b63042-b3d1-4163-b10a-75c9979ebf59-us-east-2.apps.astra.datastax.com\"\n", "ASTRA_DB_APPLICATION_TOKEN=\"AstraCS:qtZxIFJmAWgJLKMBHsbvAzjb:66d4ef1337add84bdf44d90afac64a0f2d7d04899249d30e7038fe404c45687f\"\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 70}, "id": "QiyrYIBKnElp", "outputId": "5f506fc5-2cb2-41df-cac4-7a7d8d260438"}, "id": "QiyrYIBKnElp", "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'\\nPROJECT_ID = \"red-delight-346705\"\\nLOCATION = \"us-central1\\nASTRA_DB_API_ENDPOINT=\"https://d2357619-8f04-4cfd-bc3a-16e410893ba3-us-east-2.apps.astra.datastax.com\"\\nASTRA_DB_APPLICATION_TOKEN=\"AstraCS:hTmlZSqmAOUHSWZaeNqzEDOR:1128826e960e49c2508b3014ae7fa40e6b5d0490d8565702a30b4ea338083a4a\"\\n'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 1}]}, {"cell_type": "code", "source": ["import getpass, os, requests\n", "\n", "if \"GCP_PROJECT_ID\" not in os.environ:\n", "  os.environ[\"GCP_PROJECT_ID\"] = getpass.getpass(\"Provide your GCP Project ID\")\n", "\n", "if \"LOCATION\" not in os.environ:\n", "  os.environ[\"LOCATION\"] = getpass.getpass(\"Provide your GCP Location\")\n", "\n", "if \"ASTRA_DB_ENDPOINT\" not in os.environ:\n", "  os.environ[\"ASTRA_DB_ENDPOINT\"] = getpass.getpass(\"Provide your Astra DB Endpoint\")\n", "\n", "if \"ASTRA_DB_TOKEN\" not in os.environ:\n", "  os.environ[\"ASTRA_DB_TOKEN\"] = getpass.getpass(\"Provide your Astra DB Token\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lKXWCbjHydhC", "outputId": "1849aa94-63dd-4438-8fdc-82c1158234a6"}, "id": "lKXWCbjHydhC", "execution_count": null, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Provide your GCP Project ID··········\n", "Provide your GCP Location··········\n", "Provide your Astra DB Endpoint··········\n", "Provide your Astra DB Token··········\n"]}]}, {"cell_type": "markdown", "source": ["## Authenticate your notebook environment ( Colab only )"], "metadata": {"id": "mO8cqwwwRIJv"}, "id": "mO8cqwwwRIJv"}, {"cell_type": "code", "source": ["!gcloud config set project {os.getenv(\"GCP_PROJECT_ID\")}"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xCh2oo_LYFxf", "outputId": "3d586ed2-30bd-40dd-ab77-d80cce5c15ec"}, "id": "xCh2oo_LYFxf", "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Updated property [core/project].\n"]}]}, {"cell_type": "code", "source": ["import sys\n", "\n", "# Additional authentication is required for Google Colab\n", "if \"google.colab\" in sys.modules:\n", "    # Authenticate user to Google Cloud\n", "    from google.colab import auth\n", "\n", "    auth.authenticate_user()"], "metadata": {"id": "BWflD-lzRFgC"}, "id": "BWflD-lzRFgC", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["!gcloud auth list"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MpyaRy-JXqYx", "outputId": "b59285fb-ef6f-4230-8c73-8f59adcc585a"}, "id": "MpyaRy-JXqYx", "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["  Credentialed Accounts\n", "ACTIVE  ACCOUNT\n", "*       <EMAIL>\n", "\n", "To set the active account, run:\n", "    $ gcloud config set account `ACCOUNT`\n", "\n"]}]}, {"cell_type": "markdown", "source": ["## Set Google Cloud project information and initialize Vertex AI SDK"], "metadata": {"id": "Ef3YjVSsRp9Q"}, "id": "Ef3YjVSsRp9Q"}, {"cell_type": "code", "source": ["# Define project information\n", "PROJECT_ID=os.getenv(\"GCP_PROJECT_ID\")\n", "LOCATION=os.getenv(\"LOCATION\")"], "metadata": {"id": "C8iMCMkeYgGT"}, "id": "C8iMCMkeYgGT", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Initialize Vertex AI\n", "import vertexai\n", "\n", "vertexai.init(project=PROJECT_ID, location=LOCATION)"], "metadata": {"id": "G1MCN16ZRFR3"}, "id": "G1MCN16ZRFR3", "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Import libraries"], "metadata": {"id": "KOBAtIV3R8mY"}, "id": "KOBAtIV3R8mY"}, {"cell_type": "code", "source": ["from vertexai.preview.generative_models import (\n", "    GenerationConfig,\n", "    GenerativeModel,\n", "    <PERSON>rm<PERSON>ate<PERSON><PERSON>,\n", "    <PERSON>rmBlockThreshold,\n", "    Image,\n", "    Part\n", ")"], "metadata": {"id": "lXlozq1mQThR"}, "id": "lXlozq1mQThR", "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Use the Gemini 1.0 Pro model\n", "\n", "The Gemini 1.0 Pro (`gemini-1.0-pro`) model is designed to handle natural language tasks, multi-turn text and code chat, and code generation.\n"], "metadata": {"id": "Pyza6kJuSCg_"}, "id": "Pyza6kJuSCg_"}, {"cell_type": "markdown", "source": ["## Load the Gemini 1.0 Pro model"], "metadata": {"id": "teZPcNCISLkQ"}, "id": "teZPcNCISLkQ"}, {"cell_type": "code", "source": ["model = GenerativeModel(\"gemini-1.0-pro\")"], "metadata": {"id": "kUB8nEGhQXMH"}, "id": "kUB8nEGhQXMH", "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Generate text from text prompts"], "metadata": {"id": "soDP_1kmSTSn"}, "id": "soDP_1kmSTSn"}, {"cell_type": "code", "source": ["responses = model.generate_content(\"Why is the sky blue?\", stream=True)"], "metadata": {"id": "cvWMgCTZntYK"}, "id": "cvWMgCTZntYK", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["for response in responses:\n", "    print(response.text, end=\"\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xuwrxROoSESn", "outputId": "f3ad777a-13dc-499e-cc23-e899e39456ec"}, "id": "xuwrxROoSESn", "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The sky appears blue due to a phenomenon called Rayleigh scattering. Sunlight is composed of all colors of the rainbow, each with a different wavelength. When sunlight enters the Earth's atmosphere, it interacts with the gas molecules present there. Blue light, with its shorter wavelength, is scattered more effectively by these molecules than other colors. This scattered blue light reaches our eyes from all directions in the sky, making it appear blue.\n", "\n", "Here's a more detailed explanation:\n", "\n", "1. **Sunlight:** Sunlight is a mixture of all colors, each with a different wavelength. Blue light has a shorter wavelength than other colors like red or yellow.\n", "2. **Earth's atmosphere:** The Earth has a blanket of gas surrounding it called the atmosphere. This atmosphere is made up of tiny particles, including nitrogen and oxygen molecules.\n", "3. **Rayleigh scattering:** When sunlight enters the atmosphere, it interacts with these gas molecules. The shorter wavelengths of blue light are scattered more effectively than longer wavelengths like red or yellow. This means that blue light gets redirected in all directions in the sky.\n", "4. **Blue sky:** The scattered blue light reaches our eyes from all directions in the sky, making it appear blue. Other colors, like red and yellow, are scattered less and tend to pass straight through the atmosphere, reaching our eyes directly from the sun.\n", "\n", "This scattering effect is also responsible for the reddish color of the sun during sunrise and sunset. As the sun is low on the horizon, sunlight has to travel through a thicker layer of atmosphere. This leads to more scattering of blue light, leaving the remaining colors, like red and orange, to dominate the sunlight reaching our eyes."]}]}, {"cell_type": "code", "source": ["prompt = \"\"\"Create a numbered list of 10 items. Each item in the list should be a trend in the tech industry.\n", "\n", "Each trend should be less than 5 words.\"\"\"  # try your own prompt\n"], "metadata": {"id": "yYfeGQzMn0E4"}, "id": "yYfeGQzMn0E4", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["responses = model.generate_content(prompt, stream=True)"], "metadata": {"id": "F2FOUPIfn4jp"}, "id": "F2FOUPIfn4jp", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["for response in responses:\n", "    print(response.text, end=\"\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yhtHW2wySENe", "outputId": "adf3ffdc-b7ca-4a73-c61d-6ce28279d22d"}, "id": "yhtHW2wySENe", "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["## 10 Tech Industry Trends:\n", "\n", "1. AI Personalization\n", "2. Metaverse Expansion\n", "3. Edge Computing Growth\n", "4. Quantum Computing Advancements\n", "5. Blockchain Security Focus\n", "6. Cybersecurity Mesh Architecture\n", "7. Hyperautomation Adoption\n", "8. Sustainable Tech Development\n", "9. Low-Code/No-Code Platforms\n", "10. Generative AI Applications"]}]}, {"cell_type": "markdown", "source": ["## Model parameters\n", "\n", "Every prompt you send to the model includes parameter values that control how the model generates a response. The model can generate different results for different parameter values. You can experiment with different model parameters to see how the results change."], "metadata": {"id": "lmoUexYKTFmj"}, "id": "lmoUexYKTFmj"}, {"cell_type": "code", "source": ["generation_config = GenerationConfig(\n", "    temperature=0.9,\n", "    top_p=1.0,\n", "    top_k=32,\n", "    candidate_count=1,\n", "    max_output_tokens=8192,\n", ")\n"], "metadata": {"id": "-96LHeIVoDVw"}, "id": "-96LHeIVoDVw", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["responses = model.generate_content(\n", "    \"Why is the sky blue?\",\n", "    generation_config=generation_config,\n", "    stream=True,\n", ")\n"], "metadata": {"id": "9CyRucFooHMH"}, "id": "9CyRucFooHMH", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["for response in responses:\n", "    print(response.text, end=\"\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "q4C71eAFSEK3", "outputId": "a5e31697-a5f8-423b-e35e-3ddfafab0993"}, "id": "q4C71eAFSEK3", "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The sky is blue because of a phenomenon called **Rayleigh scattering**. Sunlight is made up of all the colors of the rainbow, but blue light has a shorter wavelength than other colors. When light enters the Earth's atmosphere, it interacts with the molecules in the air. These molecules scatter the light in all directions. However, because blue light has a shorter wavelength, it is scattered more easily than other colors. This is why we see a blue sky. \n", "\n", "Here are some additional details about Rayleigh scattering:\n", "\n", "* The intensity of the scattered light is inversely proportional to the fourth power of the wavelength. This means that blue light is scattered much more strongly than red light.\n", "* The amount of scattering also depends on the density of the atmosphere. The higher the density, the more scattering there is. This is why the sky is often a deeper blue at higher altitudes.\n", "* Rayleigh scattering is also responsible for the red and orange colors of sunrises and sunsets. When the sun is low in the sky, its light has to travel through more of the atmosphere. This means that more blue light is scattered away, leaving the red and orange colors behind.\n", "\n", "I hope this explanation helps!"]}]}, {"cell_type": "code", "source": ["source_img_data = requests.get('https://drive.google.com/uc?export=view&id=15ddcn-AIxpvRdWcFGvIr77XLWdo4Maof').content"], "metadata": {"id": "UxflQ0mWoZcu"}, "id": "UxflQ0mWoZcu", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["with open('coffee_maker_part.png', 'wb') as handler:\n", "  handler.write(source_img_data)"], "metadata": {"id": "G2SNnO7JYtlu"}, "id": "G2SNnO7JYtlu", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from langchain_google_vertexai import ChatVertexAI"], "metadata": {"id": "lYAFQWLFurTf"}, "id": "lYAFQWLFurTf", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from langchain.schema.messages import HumanMessage\n", "from PIL import Image, ImageFile\n", "import os, sys\n"], "metadata": {"id": "h4_Cd_QP13jh"}, "id": "h4_Cd_QP13jh", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chat = ChatVertexAI(model_name=\"gemini-1.0-pro-vision\",safety_settings={\n", "        HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_LOW_AND_ABOVE\n", "    },)"], "metadata": {"id": "Q5-GXhql2amH"}, "id": "Q5-GXhql2amH", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["image_message = {\n", "    \"type\": \"image_url\",\n", "    \"image_url\": {\"url\": \"coffee_maker_part.png\"},\n", "}\n", "text_message = {\n", "    \"type\": \"text\",\n", "    \"text\": \"What is this image? Share a link to purchase a replacement\",\n", "}"], "metadata": {"id": "-5a4W_Os2iUo"}, "id": "-5a4W_Os2iUo", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["message = HumanMessage(content=[text_message, image_message])"], "metadata": {"id": "d3_y9HUppFS3"}, "id": "d3_y9HUppFS3", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["output = chat([message])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RO10SiFzpLX5", "outputId": "6ef1c5c2-33a6-4f28-c30d-1332d3379599"}, "id": "RO10SiFzpLX5", "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/langchain_core/_api/deprecation.py:119: LangChainDeprecationWarning: The method `BaseChatModel.__call__` was deprecated in langchain-core 0.1.7 and will be removed in 0.3.0. Use invoke instead.\n", "  warn_deprecated(\n"]}]}, {"cell_type": "code", "source": ["print(output.content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8i96veBrYwim", "outputId": "b90ee03e-51cf-4dac-d8e8-08971cbae5fd"}, "id": "8i96veBrYwim", "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": [" This is a replacement filter basket for a Moka pot coffee maker. You can purchase a replacement here:\n", "\n", "https://www.amazon.com/Replacement-Filter-Basket-Moka-Express/dp/B07171163K/\n"]}]}, {"cell_type": "code", "source": ["import pandas as pd\n", "\n", "d = {'name': [\"<PERSON><PERSON><PERSON>\", \"Saucer Ceramic\", \"Milk Jug Assembly\", \"Handle Steam Wand Kit (New Version From 0735 PDC)\", \"Spout Juice Small (From 0637 to 1041 PDC)\", \"Cleaning Steam Wand\", \"Jug Frothing\", \"Spoon Tamping 50mm\", \"Collar Grouphead 50mm\", \"Filter 2 Cup Dual Wall 50mm\", \"Filter 1 Cup 50mm\", \"Water Tank Assembly\", \"Portafilter Assembly 50mm\", \"Milk Jug Assembly\", \"Filter 2 Cup 50mm\" ],\n", "     'url': [\"https://www.breville.com/us/en/parts-accessories/parts/sp0014946.html?sku=*********\", \"https://www.breville.com/us/en/parts-accessories/parts/sp0014914.html?sku=*********\", \"https://www.breville.com/us/en/parts-accessories/parts/sp0011391.html?sku=*********\", \"https://www.breville.com/us/en/parts-accessories/parts/sp0010719.html?sku=*********\", \"https://www.breville.com/us/en/parts-accessories/parts/sp0010718.html?sku=*********\", \"https://www.breville.com/us/en/parts-accessories/parts/sp0003247.html?sku=*********\", \"https://www.breville.com/us/en/parts-accessories/parts/sp0003246.html?sku=*********\", \"https://www.breville.com/us/en/parts-accessories/parts/sp0003243.html?sku=SP0003243\", \"https://www.breville.com/us/en/parts-accessories/parts/sp0003232.html?sku=SP0003232\", \"https://www.breville.com/us/en/parts-accessories/parts/sp0003231.html?sku=SP0003231\", \"https://www.breville.com/us/en/parts-accessories/parts/sp0003230.html?sku=SP0003230\", \"https://www.breville.com/us/en/parts-accessories/parts/sp0003225.html?sku=SP0003225\", \"https://www.breville.com/us/en/parts-accessories/parts/sp0003216.html?sku=SP0003216\", \"https://www.breville.com/us/en/parts-accessories/parts/sp0001875.html?sku=SP0001875\", \"https://www.breville.com/us/en/parts-accessories/parts/sp0000166.html?sku=SP0000166\"],\n", "     'price': [\"10.95\", \"4.99\", \"14.95\", \"8.95\", \"10.95\", \"6.95\", \"24.95\", \"8.95\", \"6.95\", \"12.95\", \"12.95\", \"14.95\", \"10.95\", \"16.95\", \"11.95\"],\n", "     'image': [\"https://www.breville.com/content/dam/breville/us/catalog/products/images/sp0/sp0014946/tile.jpg\", \"https://www.breville.com/content/dam/breville/us/catalog/products/images/sp0/sp0014914/tile.jpg\", \"https://www.breville.com/content/dam/breville/us/catalog/products/images/sp0/sp0011391/tile.jpg\", \"https://www.breville.com/content/dam/breville/ca/catalog/products/images/sp0/sp0010719/tile.jpg\", \"https://www.breville.com/content/dam/breville/ca/catalog/products/images/sp0/sp0010718/tile.jpg\", \"https://www.breville.com/content/dam/breville/ca/catalog/products/images/sp0/sp0003247/tile.jpg\", \"https://assets.breville.com/cdn-cgi/image/width=400,format=auto/Spare+Parts+/Espresso+Machines/BES250/*********/*********_IMAGE1_400X400.jpg\", \"https://assets.breville.com/cdn-cgi/image/width=400,format=auto/Spare+Parts+/Espresso+Machines/ESP8/SP0003243/SP0003243_IMAGE1_400X400.jpg\", \"https://assets.breville.com/cdn-cgi/image/width=400,format=auto/Spare+Parts+/Espresso+Machines/ESP8/SP0003232/SP0003232_IMAGE1_400x400.jpg\", \"https://www.breville.com/content/dam/breville/au/catalog/products/images/sp0/sp0003231/tile.jpg\", \"https://www.breville.com/content/dam/breville/au/catalog/products/images/sp0/sp0003230/tile.jpg\", \"https://www.breville.com/content/dam/breville/ca/catalog/products/images/sp0/sp0003225/tile.jpg\", \"https://www.breville.com/content/dam/breville/ca/catalog/products/images/sp0/sp0003216/tile.jpg\", \"https://www.breville.com/content/dam/breville/au/catalog/products/images/sp0/sp0001875/tile.jpg\", \"https://www.breville.com/content/dam/breville/us/catalog/products/images/sp0/sp0000166/tile.jpg\"]}\n", "df = pd.DataFrame(data=d)\n", "df"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 780}, "id": "s0kOfknRY20q", "outputId": "98be1d6b-9791-4743-88c3-72193503d3a6"}, "id": "s0kOfknRY20q", "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                                                 name  \\\n", "0                                              Saucer   \n", "1                                      Saucer Ceramic   \n", "2                                   Milk Jug Assembly   \n", "3   Handle Steam Wand Kit (New Version From 0735 PDC)   \n", "4           Spout Juice Small (From 0637 to 1041 PDC)   \n", "5                                 Cleaning Steam Wand   \n", "6                                        Ju<PERSON>   \n", "7                                  Spoon Tamping 50mm   \n", "8                               Collar Grouphead 50mm   \n", "9                         Filter 2 Cup Dual Wall 50mm   \n", "10                                  Filter 1 Cup 50mm   \n", "11                                Water Tank Assembly   \n", "12                          Portafilter Assembly 50mm   \n", "13                                  Milk Jug Assembly   \n", "14                                  Filter 2 Cup 50mm   \n", "\n", "                                                  url  price  \\\n", "0   https://www.breville.com/us/en/parts-accessori...  10.95   \n", "1   https://www.breville.com/us/en/parts-accessori...   4.99   \n", "2   https://www.breville.com/us/en/parts-accessori...  14.95   \n", "3   https://www.breville.com/us/en/parts-accessori...   8.95   \n", "4   https://www.breville.com/us/en/parts-accessori...  10.95   \n", "5   https://www.breville.com/us/en/parts-accessori...   6.95   \n", "6   https://www.breville.com/us/en/parts-accessori...  24.95   \n", "7   https://www.breville.com/us/en/parts-accessori...   8.95   \n", "8   https://www.breville.com/us/en/parts-accessori...   6.95   \n", "9   https://www.breville.com/us/en/parts-accessori...  12.95   \n", "10  https://www.breville.com/us/en/parts-accessori...  12.95   \n", "11  https://www.breville.com/us/en/parts-accessori...  14.95   \n", "12  https://www.breville.com/us/en/parts-accessori...  10.95   \n", "13  https://www.breville.com/us/en/parts-accessori...  16.95   \n", "14  https://www.breville.com/us/en/parts-accessori...  11.95   \n", "\n", "                                                image  \n", "0   https://www.breville.com/content/dam/breville/...  \n", "1   https://www.breville.com/content/dam/breville/...  \n", "2   https://www.breville.com/content/dam/breville/...  \n", "3   https://www.breville.com/content/dam/breville/...  \n", "4   https://www.breville.com/content/dam/breville/...  \n", "5   https://www.breville.com/content/dam/breville/...  \n", "6   https://assets.breville.com/cdn-cgi/image/widt...  \n", "7   https://assets.breville.com/cdn-cgi/image/widt...  \n", "8   https://assets.breville.com/cdn-cgi/image/widt...  \n", "9   https://www.breville.com/content/dam/breville/...  \n", "10  https://www.breville.com/content/dam/breville/...  \n", "11  https://www.breville.com/content/dam/breville/...  \n", "12  https://www.breville.com/content/dam/breville/...  \n", "13  https://www.breville.com/content/dam/breville/...  \n", "14  https://www.breville.com/content/dam/breville/...  "], "text/html": ["\n", "  <div id=\"df-861cf4b5-b07a-45dd-abe8-f5487d0e8724\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>url</th>\n", "      <th>price</th>\n", "      <th>image</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Saucer</td>\n", "      <td>https://www.breville.com/us/en/parts-accessori...</td>\n", "      <td>10.95</td>\n", "      <td>https://www.breville.com/content/dam/breville/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>https://www.breville.com/us/en/parts-accessori...</td>\n", "      <td>4.99</td>\n", "      <td>https://www.breville.com/content/dam/breville/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Milk Jug Assembly</td>\n", "      <td>https://www.breville.com/us/en/parts-accessori...</td>\n", "      <td>14.95</td>\n", "      <td>https://www.breville.com/content/dam/breville/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Handle Steam Wand Kit (New Version From 0735 PDC)</td>\n", "      <td>https://www.breville.com/us/en/parts-accessori...</td>\n", "      <td>8.95</td>\n", "      <td>https://www.breville.com/content/dam/breville/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Spout Juice Small (From 0637 to 1041 PDC)</td>\n", "      <td>https://www.breville.com/us/en/parts-accessori...</td>\n", "      <td>10.95</td>\n", "      <td>https://www.breville.com/content/dam/breville/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Cleaning Steam Wand</td>\n", "      <td>https://www.breville.com/us/en/parts-accessori...</td>\n", "      <td>6.95</td>\n", "      <td>https://www.breville.com/content/dam/breville/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>https://www.breville.com/us/en/parts-accessori...</td>\n", "      <td>24.95</td>\n", "      <td>https://assets.breville.com/cdn-cgi/image/widt...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td><PERSON><PERSON> Tamping 50mm</td>\n", "      <td>https://www.breville.com/us/en/parts-accessori...</td>\n", "      <td>8.95</td>\n", "      <td>https://assets.breville.com/cdn-cgi/image/widt...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Collar Grouphead 50mm</td>\n", "      <td>https://www.breville.com/us/en/parts-accessori...</td>\n", "      <td>6.95</td>\n", "      <td>https://assets.breville.com/cdn-cgi/image/widt...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Filter 2 Cup Dual Wall 50mm</td>\n", "      <td>https://www.breville.com/us/en/parts-accessori...</td>\n", "      <td>12.95</td>\n", "      <td>https://www.breville.com/content/dam/breville/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Filter 1 Cup 50mm</td>\n", "      <td>https://www.breville.com/us/en/parts-accessori...</td>\n", "      <td>12.95</td>\n", "      <td>https://www.breville.com/content/dam/breville/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Water Tank Assembly</td>\n", "      <td>https://www.breville.com/us/en/parts-accessori...</td>\n", "      <td>14.95</td>\n", "      <td>https://www.breville.com/content/dam/breville/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Portafilter Assembly 50mm</td>\n", "      <td>https://www.breville.com/us/en/parts-accessori...</td>\n", "      <td>10.95</td>\n", "      <td>https://www.breville.com/content/dam/breville/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Milk Jug Assembly</td>\n", "      <td>https://www.breville.com/us/en/parts-accessori...</td>\n", "      <td>16.95</td>\n", "      <td>https://www.breville.com/content/dam/breville/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Filter 2 Cup 50mm</td>\n", "      <td>https://www.breville.com/us/en/parts-accessori...</td>\n", "      <td>11.95</td>\n", "      <td>https://www.breville.com/content/dam/breville/...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-861cf4b5-b07a-45dd-abe8-f5487d0e8724')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-861cf4b5-b07a-45dd-abe8-f5487d0e8724 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-861cf4b5-b07a-45dd-abe8-f5487d0e8724');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-e4d799c1-40ba-45aa-addf-bc779b3fbe77\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-e4d799c1-40ba-45aa-addf-bc779b3fbe77')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-e4d799c1-40ba-45aa-addf-bc779b3fbe77 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 15,\n  \"fields\": [\n    {\n      \"column\": \"name\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 14,\n        \"samples\": [\n          \"Filter 2 Cup Dual Wall 50mm\",\n          \"Water Tank Assembly\",\n          \"Saucer\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"url\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 15,\n        \"samples\": [\n          \"https://www.breville.com/us/en/parts-accessories/parts/sp0003231.html?sku=SP0003231\",\n          \"https://www.breville.com/us/en/parts-accessories/parts/sp0003225.html?sku=SP0003225\",\n          \"https://www.breville.com/us/en/parts-accessories/parts/sp0014946.html?sku=*********\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"price\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 9,\n        \"samples\": [\n          \"16.95\",\n          \"4.99\",\n          \"24.95\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"image\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 15,\n        \"samples\": [\n          \"https://www.breville.com/content/dam/breville/au/catalog/products/images/sp0/sp0003231/tile.jpg\",\n          \"https://www.breville.com/content/dam/breville/ca/catalog/products/images/sp0/sp0003225/tile.jpg\",\n          \"https://www.breville.com/content/dam/breville/us/catalog/products/images/sp0/sp0014946/tile.jpg\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 31}]}, {"cell_type": "code", "source": ["import <PERSON><PERSON>, json, requests\n", "from vertexai.preview.vision_models import MultiModalEmbeddingModel, Image\n", "from astrapy.db import AstraDB, AstraDBCollection\n", "from google.colab import files"], "metadata": {"id": "2fcMmT6x2_Fu"}, "id": "2fcMmT6x2_Fu", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["model = MultiModalEmbeddingModel.from_pretrained(\"multimodalembedding@001\")"], "metadata": {"id": "c5RNXJmb3BVw"}, "id": "c5RNXJmb3BVw", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Initialize our vector db\n", "astra_db = AstraDB(token=os.getenv(\"ASTRA_DB_TOKEN\"), api_endpoint=os.getenv(\"ASTRA_DB_ENDPOINT\"))"], "metadata": {"id": "MmAw9Z8D3EYM"}, "id": "MmAw9Z8D3EYM", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["collection = astra_db.create_collection(collection_name=\"coffee_shop_ecommerce\", dimension=1408)"], "metadata": {"id": "rGf8tmF23Gxc"}, "id": "rGf8tmF23Gxc", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["for i in range(len(df)):\n", "  name = df.loc[i, \"name\"]\n", "  image = df.loc[i, \"image\"]\n", "  price = df.loc[i, \"price\"]\n", "  url = df.loc[i, \"url\"]\n", "\n", "  # Download this product's image and save it to the Colab filesystem.\n", "  # In a production system this binary data would be stored in Google Cloud Storage\n", "  img_data = requests.get(image).content\n", "  with open(f'{name}.png', 'wb') as handler:\n", "    handler.write(img_data)\n", "\n", "  # load the image from filesystem and compute the embedding value\n", "  img = Image.load_from_file(f'{name}.png')\n", "  embeddings = model.get_embeddings(image=img, contextual_text=name)\n", "\n", "  try:\n", "    # add to the AstraDB Vector Database\n", "    collection.insert_one({\n", "        \"_id\": i,\n", "        \"name\": name,\n", "        \"image\": image,\n", "        \"url\": url,\n", "        \"price\": price,\n", "        \"$vector\": embeddings.image_embedding,\n", "      })\n", "  except Exception as error:\n", "    # if you've already added this record, skip the error message\n", "    error_info = json.loads(str(error))\n", "    if error_info[0]['errorCode'] == \"DOCUMENT_ALREADY_EXISTS\":\n", "      print(\"Document already exists in the database.  Skipping.\")"], "metadata": {"id": "Qc2D1qqvY6Jy"}, "id": "Qc2D1qqvY6Jy", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["import json\n", "\n", "# Embed the similar item\n", "img = Image.load_from_file('coffee_maker_part.png')"], "metadata": {"id": "A8OALWDt4alj"}, "id": "A8OALWDt4alj", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["embeddings = model.get_embeddings(image=img, contextual_text=\"A espresso machine part\")"], "metadata": {"id": "FYEBo0rO3uV9"}, "id": "FYEBo0rO3uV9", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["embeddings.image_embedding"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "GfTqY9MKouR_", "outputId": "582f5bfb-86ea-4b5c-9c12-db60cdffe617"}, "id": "GfTqY9MKouR_", "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[0.0103443023,\n", " -0.0423360355,\n", " 0.0025271,\n", " 0.0468370356,\n", " -0.00584532041,\n", " -0.0222770683,\n", " -0.0102790007,\n", " -0.0213940851,\n", " -0.0253922492,\n", " -0.00449423306,\n", " -0.0144735314,\n", " -0.0385552645,\n", " 0.0178169888,\n", " 0.16114834,\n", " -0.0240380447,\n", " 0.00393703813,\n", " 0.0563112088,\n", " -0.00262522465,\n", " -0.0355723463,\n", " 0.0185569692,\n", " -0.0134987067,\n", " -0.00920229312,\n", " -0.00511650136,\n", " -0.01109982,\n", " 0.00243064575,\n", " 0.00353293261,\n", " 0.00684218062,\n", " -0.0174581669,\n", " 0.0498942062,\n", " -0.0408516265,\n", " 0.027069347,\n", " -0.00324063702,\n", " 0.00235783681,\n", " 0.0173611976,\n", " 0.0509904698,\n", " -0.0192037039,\n", " -0.0190860294,\n", " 0.0200846344,\n", " -0.00735383,\n", " -0.00653002458,\n", " -0.0258874968,\n", " 0.0159182101,\n", " -0.028728202,\n", " -0.0242897402,\n", " 0.0104897087,\n", " -0.0224464387,\n", " -0.030899208,\n", " 0.00114898989,\n", " 0.00913333,\n", " -0.000930706214,\n", " -0.0330116823,\n", " -0.0142512815,\n", " 0.0255830139,\n", " -0.0319835283,\n", " 0.0354433805,\n", " 0.00188259548,\n", " -0.0174734276,\n", " -0.0277424678,\n", " 0.041069936,\n", " -0.0386415832,\n", " 0.00900212117,\n", " -0.00827264,\n", " 0.0344879851,\n", " -0.0153776715,\n", " 0.0109624974,\n", " 0.00850054342,\n", " 0.0304393023,\n", " 0.0167925674,\n", " -0.00304819224,\n", " -0.0133295022,\n", " -0.00502276141,\n", " -0.001809721,\n", " -0.0146918977,\n", " 0.00217283564,\n", " -0.0291582048,\n", " -0.0217577331,\n", " -0.0397390574,\n", " 0.00424241973,\n", " -0.00531004462,\n", " -0.0219716933,\n", " 0.0311140139,\n", " 0.00899406429,\n", " -0.00975704752,\n", " -0.00482970895,\n", " 0.0041992194,\n", " -0.00215634191,\n", " -0.00535582704,\n", " -0.00608062558,\n", " -0.0268260445,\n", " -0.00297428295,\n", " 0.0419727564,\n", " 0.0255308114,\n", " 0.000876593578,\n", " -0.244043231,\n", " -0.0096452143,\n", " -0.051594805,\n", " -0.0332899652,\n", " 0.0064974525,\n", " 0.0124820005,\n", " -0.0203746129,\n", " 0.0101544037,\n", " 0.00509286299,\n", " 0.030445328,\n", " 0.0342152864,\n", " -0.0427300557,\n", " -0.0240902156,\n", " -0.0172130428,\n", " -0.00801799,\n", " -0.00543288467,\n", " -0.0357411169,\n", " -0.0438533463,\n", " 0.00356129697,\n", " -0.00534522394,\n", " -0.0221106634,\n", " 0.02623244,\n", " -0.0201115217,\n", " -0.039816156,\n", " 0.0234409235,\n", " -0.0169480667,\n", " 0.0459393337,\n", " -0.0213627424,\n", " -0.0235334709,\n", " -0.00194211875,\n", " 0.00291381124,\n", " -0.00875948463,\n", " -0.0839483961,\n", " 0.0247152019,\n", " -0.0270348694,\n", " 0.00327553088,\n", " -0.0261187777,\n", " 0.0272643231,\n", " 0.0223003011,\n", " 0.0341238044,\n", " -0.0321658067,\n", " 0.00622226577,\n", " -0.000637094898,\n", " -0.025673287,\n", " 0.0114835212,\n", " 0.0537299886,\n", " -0.0141120804,\n", " -0.0128882919,\n", " 0.0205999091,\n", " -0.0144611094,\n", " -0.0378132761,\n", " 0.0315978341,\n", " -0.00337057933,\n", " -0.000395274488,\n", " -0.020516878,\n", " -0.0276697073,\n", " -0.0133176958,\n", " -0.00916543417,\n", " -0.0194873046,\n", " -0.045305144,\n", " 0.0278039258,\n", " -0.00763554731,\n", " -0.00172101078,\n", " 0.00555963162,\n", " -0.0074644289,\n", " -0.0268224403,\n", " 0.00534371287,\n", " -0.00817754399,\n", " -0.00439360971,\n", " -0.0346724465,\n", " -0.00490585715,\n", " 0.0170159433,\n", " 0.00343145756,\n", " -0.00984645076,\n", " -0.0186217744,\n", " 0.0198042262,\n", " 0.0198841877,\n", " 0.0390070528,\n", " 0.039534118,\n", " 0.00482342,\n", " -0.0228405502,\n", " 0.0402764156,\n", " -0.00147934,\n", " -0.0270232633,\n", " 0.00844554,\n", " -0.00696794409,\n", " -0.00795746315,\n", " 0.00472885184,\n", " 0.00767816883,\n", " -0.0544797368,\n", " 0.0155482888,\n", " 0.0403630771,\n", " 0.00947891362,\n", " -0.0216384437,\n", " -0.00736964541,\n", " -0.00258853496,\n", " 0.0405963585,\n", " -0.0109421499,\n", " -0.0293272976,\n", " -0.000150648368,\n", " 0.00621146616,\n", " 0.0117974598,\n", " 0.0173322242,\n", " 0.0053044972,\n", " -0.0333643071,\n", " 0.000519836845,\n", " 0.003606566,\n", " 0.0372801349,\n", " -0.00545576494,\n", " 0.0241974853,\n", " -0.0162273962,\n", " 0.00726281,\n", " 0.0208657961,\n", " 0.0112189269,\n", " -0.00310890283,\n", " 0.00288895844,\n", " 0.00753353024,\n", " -0.011229503,\n", " -0.029863121,\n", " 0.0216411762,\n", " 0.0211069565,\n", " -0.00727295969,\n", " 0.00752760749,\n", " -0.0388854295,\n", " -0.000681540638,\n", " -0.00188999239,\n", " 0.00695374794,\n", " -0.0182590559,\n", " 0.0208173972,\n", " 0.00820563827,\n", " -0.0186969433,\n", " 0.00831573363,\n", " -0.0215286314,\n", " 0.0229553077,\n", " 0.0345581621,\n", " -0.0427697189,\n", " 0.0326661,\n", " -0.0300407838,\n", " -0.00152556819,\n", " -0.0302154217,\n", " -0.0323622264,\n", " -0.0273493975,\n", " -0.00966993626,\n", " 0.00416145241,\n", " 0.0150238816,\n", " 0.00746436417,\n", " -0.0367888249,\n", " 0.0323068351,\n", " -0.000225069743,\n", " -0.012946697,\n", " -0.0544167385,\n", " -0.00490216445,\n", " -0.00539495749,\n", " 0.00584722031,\n", " -0.0122598149,\n", " -0.0148988618,\n", " -0.00964978151,\n", " 0.00138315267,\n", " 0.0188248958,\n", " -0.0130065056,\n", " 0.00308349333,\n", " -0.0568052977,\n", " -0.0166620594,\n", " -0.00401561614,\n", " 0.00273494353,\n", " 0.00101731205,\n", " 0.0145705892,\n", " 0.0218152683,\n", " -0.0150628034,\n", " 0.00931525,\n", " -0.0106561845,\n", " 0.000638744794,\n", " 0.0291380491,\n", " 0.00942065567,\n", " 0.00617768802,\n", " 0.00850653183,\n", " 0.0424510613,\n", " -0.0606367029,\n", " 0.008300527,\n", " -0.00716218166,\n", " 0.0181104392,\n", " -0.013972111,\n", " -0.0195369553,\n", " -0.0233433824,\n", " -0.0182440244,\n", " 0.00796255,\n", " 0.0763352215,\n", " -0.0246421415,\n", " 0.0217942167,\n", " 0.0112501113,\n", " 0.00227179285,\n", " -0.0164277591,\n", " 0.0350596346,\n", " -0.158459768,\n", " -0.0122975325,\n", " -0.00270367297,\n", " 0.00137230358,\n", " -0.00718642399,\n", " -0.0287281945,\n", " 0.00317007233,\n", " -0.0086720055,\n", " -0.0115487352,\n", " 0.0045855986,\n", " 0.0272947438,\n", " -0.008059524,\n", " -0.0193439052,\n", " 0.00733261509,\n", " 0.00119285379,\n", " 0.00848114584,\n", " 0.00856698211,\n", " -0.0249720495,\n", " -0.027801102,\n", " 0.0143674435,\n", " -0.0115377363,\n", " 0.0386347361,\n", " -0.0100356042,\n", " -0.00781036401,\n", " 0.0276910961,\n", " 0.0191894155,\n", " -0.00203930703,\n", " 0.0488899723,\n", " 0.00790771,\n", " -0.00501327356,\n", " 0.0120158857,\n", " -0.0314805657,\n", " -0.00651794253,\n", " 0.0409667417,\n", " 0.0520656109,\n", " 0.0147184711,\n", " 0.0253645778,\n", " -0.00770446099,\n", " -0.00119706732,\n", " -0.0420660153,\n", " -0.0124015687,\n", " -0.0143966507,\n", " 0.0398339815,\n", " 0.014644145,\n", " -0.0333836302,\n", " -0.0257547908,\n", " 0.00196008128,\n", " -0.00975638907,\n", " -0.0328272022,\n", " -0.039686989,\n", " -0.0346079916,\n", " 0.0174662322,\n", " 0.00307205273,\n", " 0.000731531472,\n", " -0.0392618254,\n", " 0.0395048708,\n", " -0.00865575206,\n", " -0.0163635593,\n", " 0.0192320719,\n", " 0.053792581,\n", " -0.0466478132,\n", " 0.00755664427,\n", " -0.0197121035,\n", " -0.0015767992,\n", " 0.0617365614,\n", " -0.00464420347,\n", " -0.0105050448,\n", " 0.0456794947,\n", " -0.0140022226,\n", " 0.0263457242,\n", " -0.00647895318,\n", " 0.0170823317,\n", " -0.0261109881,\n", " -0.00860153884,\n", " -0.00199266314,\n", " 0.00993947871,\n", " -0.014445724,\n", " -0.0122508788,\n", " -0.0127059855,\n", " -0.0191699285,\n", " -0.00362067018,\n", " -0.0650439,\n", " -0.0267419405,\n", " 0.00911840331,\n", " 0.0295923613,\n", " 0.0241206661,\n", " 0.00575215416,\n", " -0.00369270938,\n", " 0.0170693882,\n", " -0.0182472132,\n", " 0.0232224911,\n", " -0.0205348432,\n", " -0.0313022062,\n", " -0.0259437151,\n", " -0.00399352517,\n", " -0.0117405681,\n", " -0.00239062286,\n", " -0.00582487788,\n", " 0.0192605481,\n", " 0.0237209052,\n", " 0.00774597749,\n", " 0.00070229976,\n", " -0.00481389789,\n", " -0.0146342162,\n", " -0.0255587306,\n", " 0.0124380793,\n", " 0.0266383365,\n", " 0.0141472816,\n", " -0.00373248057,\n", " -0.00455358,\n", " 0.0264955983,\n", " -0.0130588431,\n", " 0.00764231477,\n", " 0.0403427146,\n", " -0.0030498961,\n", " 0.0387821645,\n", " 0.0300427619,\n", " 0.033784885,\n", " -0.0158616062,\n", " -0.0152607104,\n", " 0.0221405197,\n", " 0.00175453385,\n", " -0.0376307517,\n", " 0.00149131974,\n", " -0.0422175154,\n", " -0.00141642441,\n", " -0.00354437204,\n", " 0.0122215822,\n", " 0.0124758929,\n", " -0.0051837475,\n", " -0.00642738957,\n", " 0.0369977653,\n", " -0.0284222905,\n", " 0.0117159709,\n", " 0.00631890865,\n", " -0.0113253882,\n", " 0.000876903359,\n", " -0.0214760154,\n", " -0.0334583707,\n", " -0.0153740933,\n", " 0.0161514096,\n", " -0.0399066433,\n", " -0.0286233407,\n", " -0.00489145471,\n", " -0.0375218019,\n", " -0.0405680574,\n", " 0.0118712811,\n", " 0.000159649368,\n", " 0.0197956637,\n", " 0.00186324213,\n", " -3.01733162e-05,\n", " -0.0503767915,\n", " 0.00713279238,\n", " -0.019527765,\n", " -0.00874135084,\n", " -0.0422537364,\n", " -0.0422981,\n", " 0.00511907833,\n", " -0.036124412,\n", " 0.00456811674,\n", " -0.00410597352,\n", " -0.0286044236,\n", " -0.0110224336,\n", " 0.00641201157,\n", " -0.00753302686,\n", " 0.0581242926,\n", " -0.000275572907,\n", " -0.00168551493,\n", " -0.00980704278,\n", " 0.0136616807,\n", " -0.000893572695,\n", " 0.0356021933,\n", " -0.00370346359,\n", " -0.0214819219,\n", " -0.0162028298,\n", " 0.00614284677,\n", " 0.00667345757,\n", " -0.010709364,\n", " -0.00178815541,\n", " 0.0147811165,\n", " 0.00399414031,\n", " 0.000294898113,\n", " 0.0066563338,\n", " -0.0071143019,\n", " 0.00576241827,\n", " -0.00723552285,\n", " 0.0115939789,\n", " 0.00392590556,\n", " -0.00121551694,\n", " -0.00638436945,\n", " -0.0201376025,\n", " -0.0175314639,\n", " 0.00660546403,\n", " 0.0165167376,\n", " -0.0208050925,\n", " -0.0335730053,\n", " -0.0366046838,\n", " 0.0146176834,\n", " -0.00299833226,\n", " -0.0105612613,\n", " 0.0272169858,\n", " -0.0314017273,\n", " 0.00272217183,\n", " 0.00803972501,\n", " -0.051113911,\n", " 0.0101873102,\n", " 0.0054617743,\n", " 0.00465316325,\n", " 0.0119472602,\n", " -0.0309998095,\n", " 0.00533414958,\n", " 0.0124506149,\n", " 0.00986624695,\n", " 0.0114211859,\n", " -0.0222741663,\n", " 0.0012058526,\n", " 0.0662786886,\n", " 0.00519942446,\n", " -0.04597269,\n", " -0.0193580948,\n", " -0.0212021079,\n", " -0.0369098894,\n", " -0.00462302705,\n", " 0.0245429762,\n", " -0.0383346453,\n", " 0.0219616927,\n", " 0.0130747277,\n", " -0.0250386409,\n", " -0.00878348388,\n", " 0.0239507798,\n", " 0.0151429903,\n", " -0.0560021251,\n", " 0.00655058818,\n", " 0.00316105573,\n", " 0.00514321402,\n", " 0.00529210642,\n", " -0.013264087,\n", " 0.0174984932,\n", " 0.0261495672,\n", " -0.0157361962,\n", " 0.041390609,\n", " 0.0297169834,\n", " -0.00155339274,\n", " -0.0034791634,\n", " 0.0367939398,\n", " -0.021488104,\n", " 0.00346440799,\n", " -0.0098677231,\n", " -0.00187881477,\n", " -0.0324204043,\n", " -0.00439200643,\n", " -0.0214396473,\n", " -0.00553848315,\n", " 0.00107273308,\n", " 0.0105750225,\n", " 0.0156985056,\n", " -0.0318867192,\n", " -0.0213294923,\n", " -0.0217905771,\n", " 0.0245967228,\n", " -0.0243771486,\n", " 0.00378284045,\n", " 0.0299545508,\n", " -0.0319611914,\n", " -0.00545417611,\n", " -0.0127572054,\n", " 0.00385754905,\n", " -0.00321567641,\n", " 0.0433764569,\n", " -0.0189352296,\n", " -0.00899099838,\n", " 0.00889174733,\n", " -0.00188918016,\n", " -0.0358673409,\n", " -0.0123095959,\n", " -0.0354834534,\n", " -0.0330750123,\n", " -0.0236629304,\n", " 0.0112771625,\n", " -0.0114244912,\n", " -0.0427749455,\n", " 0.00164739,\n", " 0.0302738287,\n", " -0.0194551647,\n", " 0.0139890257,\n", " 0.0103652421,\n", " -0.0262695476,\n", " -0.00231122621,\n", " -0.0183611047,\n", " -0.0230800919,\n", " -0.00718518067,\n", " 0.0151038067,\n", " -0.0274307672,\n", " 0.0119020287,\n", " 0.0112020914,\n", " 0.0264276415,\n", " -0.0113134896,\n", " 0.0297258291,\n", " -0.0189480223,\n", " 0.0012591281,\n", " -0.0327018872,\n", " -0.00604500622,\n", " 0.0203643627,\n", " -0.0341208428,\n", " -0.0150880534,\n", " 0.021382764,\n", " -0.00856472738,\n", " 0.0130324028,\n", " -0.0164786763,\n", " -0.00485688308,\n", " -0.00572509784,\n", " -0.00200441387,\n", " -0.0139548192,\n", " -0.00875431392,\n", " 0.0281174816,\n", " -0.00701050274,\n", " 0.0334269144,\n", " -0.0338665806,\n", " -0.0113591254,\n", " -0.0768974721,\n", " 0.01694577,\n", " -0.00783859193,\n", " 0.00640902063,\n", " 0.0142383268,\n", " 0.0136061879,\n", " -0.000830696954,\n", " 0.02006674,\n", " 0.0471914671,\n", " -0.0110469041,\n", " 0.00474848505,\n", " -0.0391385518,\n", " -0.00368137588,\n", " -0.010765478,\n", " 0.0244100988,\n", " -0.0211417843,\n", " -0.00623041391,\n", " 0.0408075266,\n", " 0.028535217,\n", " -0.00801164191,\n", " 0.024208976,\n", " -0.0164256822,\n", " -0.0128833512,\n", " 0.000311049051,\n", " -0.00950113218,\n", " -0.0123988148,\n", " -0.00311834482,\n", " -0.00900808722,\n", " -0.00837779697,\n", " 0.00398754841,\n", " 0.0449206,\n", " 0.0331120752,\n", " -0.0168257765,\n", " 0.0443200693,\n", " 0.006117607,\n", " 0.00970684,\n", " -0.0394934416,\n", " 0.0136445612,\n", " 0.0219576806,\n", " -0.010921292,\n", " 0.00641283765,\n", " 0.0113523649,\n", " 0.00909079146,\n", " 0.00377822598,\n", " -0.0645972267,\n", " 0.0119491918,\n", " 0.0128334174,\n", " 0.0183844175,\n", " -0.0154310698,\n", " 0.0112220524,\n", " -0.00741486438,\n", " 0.0246020183,\n", " 0.0186289102,\n", " -0.00878119841,\n", " -0.0153971426,\n", " -0.155469224,\n", " -0.0153095303,\n", " 0.0173830036,\n", " -0.04309953,\n", " -0.00149943354,\n", " 0.000220249494,\n", " 0.0178734,\n", " 0.00939253438,\n", " -0.0280510187,\n", " -0.0466411486,\n", " 0.0399672203,\n", " -0.0048429505,\n", " -0.138153136,\n", " 0.0285430029,\n", " -0.0458802618,\n", " 0.0153684793,\n", " 0.000584398513,\n", " 0.00651904615,\n", " -0.00191171688,\n", " 0.00381810474,\n", " -0.0413796231,\n", " 0.00848077517,\n", " 0.0149802268,\n", " -0.0260082837,\n", " 0.0179700665,\n", " 0.00598235102,\n", " 0.0197714902,\n", " -0.0165468659,\n", " -0.00446386216,\n", " -0.0413117483,\n", " -0.0176946316,\n", " -0.0121425064,\n", " 0.00156934245,\n", " 0.0443214178,\n", " -0.00276605366,\n", " -0.0258738,\n", " -0.0308482349,\n", " 0.00373741682,\n", " 0.0372134708,\n", " 0.0128080612,\n", " 0.0174259674,\n", " 0.0447199158,\n", " -0.0177530702,\n", " -0.00787915289,\n", " 0.0141520733,\n", " 0.0208419468,\n", " -0.0102570895,\n", " 0.014389894,\n", " -0.0127234738,\n", " -0.0831042156,\n", " 0.0195751898,\n", " -0.0033304519,\n", " 0.00553740375,\n", " -0.0261277296,\n", " 0.0221802741,\n", " 0.0319548063,\n", " -0.00398435723,\n", " 0.0045634741,\n", " -0.0283972211,\n", " -0.00801189709,\n", " -0.0074476,\n", " -0.0123436293,\n", " 0.0121857636,\n", " -0.00647217408,\n", " -0.00567082502,\n", " -0.00956430752,\n", " -0.00405508745,\n", " 0.0286074337,\n", " 0.0203863885,\n", " 0.0555167235,\n", " -0.0130302431,\n", " -0.0393968932,\n", " 0.000756496505,\n", " 0.00785524,\n", " 0.00468574464,\n", " -0.0112628639,\n", " 0.0054421681,\n", " 0.0335524678,\n", " 0.00629264722,\n", " -0.0316479914,\n", " 0.0206759833,\n", " 0.0952559039,\n", " 0.01113097,\n", " 0.0144801466,\n", " 0.0120449308,\n", " 0.0250044651,\n", " 0.00977367,\n", " -0.0170088913,\n", " -0.194295838,\n", " 0.00243961066,\n", " -0.00381236081,\n", " 0.0117437653,\n", " -0.0250054598,\n", " 0.00723531749,\n", " -0.0196053106,\n", " -0.0238738041,\n", " -0.0145419044,\n", " 0.0334960818,\n", " 0.0355865769,\n", " -0.00448700413,\n", " 0.00354164536,\n", " 0.00155396271,\n", " -0.0266864207,\n", " 0.0190289821,\n", " 0.0537854396,\n", " 0.00807104819,\n", " -0.00989548862,\n", " -0.00261666859,\n", " -0.0230201744,\n", " -0.025750339,\n", " -0.0315706059,\n", " -0.00699894829,\n", " 0.00775106438,\n", " 0.0277604572,\n", " -0.0171194877,\n", " -0.0111451512,\n", " 0.0233815219,\n", " 0.0105079068,\n", " -0.05441048,\n", " 0.00173962477,\n", " 0.00446701888,\n", " -0.02016907,\n", " 0.0371980965,\n", " -0.0219349265,\n", " 0.0219353884,\n", " -0.0260709617,\n", " 0.0237087701,\n", " -0.0220353696,\n", " -0.0226157866,\n", " -0.0113264397,\n", " 0.00584347313,\n", " -0.00155542151,\n", " -0.0319844931,\n", " 0.0450468771,\n", " -0.000185223,\n", " -0.0224934425,\n", " 0.0128555242,\n", " -0.000410619861,\n", " 0.000101942896,\n", " 0.0254096501,\n", " 0.0180156846,\n", " -0.0163780767,\n", " 0.00737548666,\n", " -0.00609494234,\n", " 0.0311497226,\n", " -0.0213314369,\n", " -0.00627973629,\n", " -0.00224614,\n", " 0.0043844278,\n", " -0.00591680966,\n", " 0.0173521768,\n", " -0.0399430953,\n", " 0.00796752796,\n", " -0.0111262426,\n", " 0.00474244868,\n", " -0.028502604,\n", " 0.0252100881,\n", " -0.026557833,\n", " 0.0187736545,\n", " -0.0476052351,\n", " -0.0058178762,\n", " -0.00827595312,\n", " 0.0942505226,\n", " 0.0120646218,\n", " 0.0287466068,\n", " -0.0327693745,\n", " -0.00224366528,\n", " 0.00857185572,\n", " 0.0652673915,\n", " 0.00729541248,\n", " -0.0309771877,\n", " 0.0104513261,\n", " 0.0694124,\n", " 0.0219127182,\n", " -0.0172555652,\n", " 0.00949559454,\n", " 0.00764502864,\n", " 0.0197236482,\n", " -0.0304161198,\n", " 0.0330554731,\n", " 0.0566199571,\n", " -0.02109343,\n", " 0.000955365307,\n", " 0.0187841207,\n", " -0.00894628,\n", " -0.00358245848,\n", " -0.0397174954,\n", " -0.0524272062,\n", " -0.00387793942,\n", " 0.0373020321,\n", " -0.00374928303,\n", " -0.019264387,\n", " 0.0112017021,\n", " -0.0329169072,\n", " -0.0492151193,\n", " -0.0152984187,\n", " 0.00873907097,\n", " 0.0128635978,\n", " -0.0367964283,\n", " 0.0215702392,\n", " 0.00623166608,\n", " -0.0566086546,\n", " 0.0146926278,\n", " -0.0226140264,\n", " 0.0433377661,\n", " 0.0213586148,\n", " -0.0271207802,\n", " 0.0162841026,\n", " -0.0219545253,\n", " 0.0212937761,\n", " 0.0189863071,\n", " 0.00490976544,\n", " 0.000157603718,\n", " 0.0444988832,\n", " -0.004374607,\n", " -0.0230155978,\n", " -0.0541895181,\n", " 0.00113490946,\n", " -0.0264878105,\n", " 0.0184311364,\n", " -0.0187671501,\n", " -0.0307778772,\n", " 0.00602874812,\n", " 0.0209102705,\n", " -0.0565395243,\n", " -0.0129775684,\n", " 0.00211072434,\n", " -0.0219780132,\n", " -0.00252568582,\n", " 0.02036478,\n", " -0.0131615177,\n", " -0.0453127213,\n", " -0.0457299091,\n", " 0.00725595,\n", " 0.0306143966,\n", " 0.00249367184,\n", " -0.00260065752,\n", " -0.0241120383,\n", " 0.0360229313,\n", " -0.0318002962,\n", " 0.0445718914,\n", " -0.00437507778,\n", " -0.00580608938,\n", " 0.0696484447,\n", " -0.0151572293,\n", " -0.0166376662,\n", " 0.0207580496,\n", " -0.0193250943,\n", " 0.00544984778,\n", " -0.0144155705,\n", " -0.00566181913,\n", " -0.00966744591,\n", " -0.0200698171,\n", " 0.019872563,\n", " -0.0341653563,\n", " -0.0178293977,\n", " -0.037074618,\n", " -0.00802604947,\n", " 0.0176282562,\n", " -0.00975127239,\n", " 0.00790531654,\n", " -0.019547822,\n", " 0.00473070797,\n", " 0.0165971443,\n", " 0.0090939356,\n", " -0.0129579846,\n", " 0.0241313428,\n", " 0.00631413888,\n", " -0.0270176884,\n", " -0.000447686733,\n", " -0.00663245143,\n", " 0.00399389397,\n", " -0.00558971893,\n", " -0.0212323107,\n", " 0.0652961358,\n", " -0.0120047061,\n", " 0.00321205845,\n", " -0.00466345623,\n", " 0.00854109228,\n", " 0.0174909122,\n", " -0.0133245168,\n", " -0.000662649,\n", " 0.0147806872,\n", " -0.0366441421,\n", " -0.0408754833,\n", " 0.0086537,\n", " -0.0123990886,\n", " -0.0030897432,\n", " -0.00288536376,\n", " -0.0517198,\n", " -0.025565248,\n", " -0.0256417897,\n", " -0.00415760512,\n", " 0.0345138833,\n", " 0.0174551271,\n", " -0.00639548525,\n", " -0.00738896849,\n", " 0.0184949767,\n", " -0.00358044519,\n", " 0.00993304793,\n", " -0.0202265251,\n", " 0.012798503,\n", " -0.00257629831,\n", " 0.087436,\n", " 0.0111393025,\n", " -0.0225607697,\n", " -0.0194887687,\n", " -0.0172076747,\n", " 0.00957445893,\n", " -0.0175518095,\n", " -0.0391286165,\n", " 0.0312343556,\n", " -0.0473724045,\n", " -0.00444553792,\n", " -0.0116493749,\n", " 0.0294700507,\n", " 0.0102394093,\n", " 0.00641273102,\n", " -0.0209732316,\n", " -0.00369666307,\n", " 0.0080663655,\n", " -0.0469232425,\n", " 0.0442262664,\n", " -0.00650086906,\n", " -0.00919162482,\n", " 0.00634621736,\n", " -0.00794075709,\n", " 0.0297976807,\n", " 0.0101758074,\n", " 0.0111476397,\n", " -0.0409644581,\n", " -0.0262494925,\n", " 0.0012885608,\n", " -0.0265220962,\n", " 0.0129925162,\n", " 0.00102972495,\n", " 0.0042914371,\n", " 0.0131014418,\n", " -0.0234113541,\n", " -0.0178376324,\n", " ...]"]}, "metadata": {}, "execution_count": 42}]}, {"cell_type": "code", "source": ["# Perform the vector search against AstraDB Vector\n", "documents = collection.vector_find(\n", "    embeddings.image_embedding,\n", "    limit=3,\n", ")"], "metadata": {"id": "UE6SRN1t3wEv"}, "id": "UE6SRN1t3wEv", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["documents"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5rhq7QNQrM-f", "outputId": "97790f82-3584-4cb1-f482-444f07f93609"}, "id": "5rhq7QNQrM-f", "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[{'_id': 9,\n", "  'name': 'Filter 2 Cup Dual Wall 50mm',\n", "  'image': 'https://www.breville.com/content/dam/breville/au/catalog/products/images/sp0/sp0003231/tile.jpg',\n", "  'url': 'https://www.breville.com/us/en/parts-accessories/parts/sp0003231.html?sku=SP0003231',\n", "  'price': '12.95',\n", "  '$vector': [-0.0016570494,\n", "   -0.0407555103,\n", "   0.00935325678,\n", "   0.0240484532,\n", "   -0.00291985,\n", "   -0.023472514,\n", "   0.0204959288,\n", "   -0.0365891866,\n", "   -0.0348752663,\n", "   -0.0137095125,\n", "   0.00393085787,\n", "   -0.0286927726,\n", "   -0.00180356076,\n", "   0.144395873,\n", "   -0.0417751111,\n", "   0.0265446492,\n", "   0.0673256218,\n", "   6.99815646e-05,\n", "   -0.00777975097,\n", "   0.00130614766,\n", "   -0.0132598253,\n", "   -0.00493014464,\n", "   -0.0316618159,\n", "   -0.02309875,\n", "   0.000352492963,\n", "   0.0071709957,\n", "   -0.00876005273,\n", "   -0.0158251692,\n", "   0.0584977381,\n", "   -0.0318364725,\n", "   0.0148035036,\n", "   0.00369193917,\n", "   0.00901080202,\n", "   0.001295547,\n", "   0.0110316193,\n", "   -0.0195775498,\n", "   -0.00320023973,\n", "   0.0139058586,\n", "   0.00714511704,\n", "   -0.00286253169,\n", "   -0.025412485,\n", "   -0.0167989414,\n", "   -0.0228072572,\n", "   -0.0181891937,\n", "   0.0314026065,\n", "   -0.0170622151,\n", "   -0.0114925215,\n", "   -0.00482304813,\n", "   0.0121454587,\n", "   -0.00400023488,\n", "   -0.0196949355,\n", "   -0.0214342959,\n", "   0.0165638365,\n", "   -0.0247385353,\n", "   0.0337835252,\n", "   -0.00276976777,\n", "   -0.0382150784,\n", "   -0.0249049868,\n", "   0.0544191189,\n", "   -0.0269167982,\n", "   0.0274652615,\n", "   -0.00188279571,\n", "   0.00941697229,\n", "   -0.0274401773,\n", "   -0.0033545706,\n", "   0.00951780658,\n", "   0.0177562609,\n", "   -0.00833008625,\n", "   -0.00573461689,\n", "   -0.0357742496,\n", "   -0.00958852749,\n", "   -0.0357336961,\n", "   -0.025153175,\n", "   0.0174889322,\n", "   -0.0260030404,\n", "   -0.00382101606,\n", "   -0.0551257581,\n", "   0.00892487355,\n", "   -0.0219150037,\n", "   -0.0434417501,\n", "   0.038047459,\n", "   0.0185175184,\n", "   -0.00715512084,\n", "   -0.00365230511,\n", "   -0.00589452172,\n", "   0.0187367741,\n", "   0.00630402891,\n", "   -0.0115533015,\n", "   -0.0558993705,\n", "   0.0254152436,\n", "   0.0466964915,\n", "   0.0213260427,\n", "   -0.00920796115,\n", "   -0.212391898,\n", "   -0.0203790069,\n", "   -0.0640814453,\n", "   -0.0426158383,\n", "   -0.00295098894,\n", "   0.0285672639,\n", "   -0.04379908,\n", "   0.00716530578,\n", "   -0.0194734354,\n", "   0.0166544206,\n", "   0.0202988312,\n", "   -0.0587413423,\n", "   0.00167468539,\n", "   -0.0182531178,\n", "   -0.0382456407,\n", "   -0.00333192968,\n", "   -0.0326557495,\n", "   -0.0206276,\n", "   0.0232437495,\n", "   -0.00795456953,\n", "   -0.0252030957,\n", "   0.01619008,\n", "   -0.0120441588,\n", "   -0.0317810513,\n", "   0.00391556276,\n", "   -0.0109679746,\n", "   0.0475892946,\n", "   -0.0136750676,\n", "   -0.0210018978,\n", "   0.00238138949,\n", "   -0.000522442511,\n", "   -0.0512189493,\n", "   -0.0713663474,\n", "   -0.0139678167,\n", "   -0.04599512,\n", "   -0.00134823588,\n", "   -0.0110219792,\n", "   0.0422401875,\n", "   0.0357588269,\n", "   0.0146727962,\n", "   -0.0259162914,\n", "   0.0126554985,\n", "   0.00727293035,\n", "   -0.011337881,\n", "   -0.00572964223,\n", "   0.0121862907,\n", "   -0.00535490923,\n", "   0.0100395503,\n", "   0.0144536467,\n", "   -0.00926743913,\n", "   -0.0451650023,\n", "   0.0239400193,\n", "   -0.0169101264,\n", "   0.00350415031,\n", "   -0.0171836354,\n", "   -0.0320472,\n", "   -0.0377386473,\n", "   -0.0137323784,\n", "   -0.0541451909,\n", "   -0.0534039252,\n", "   0.0145492116,\n", "   0.0148012741,\n", "   -0.0070604966,\n", "   0.0191379394,\n", "   0.0144963246,\n", "   -0.0103843203,\n", "   -0.00838569924,\n", "   0.0015416838,\n", "   -0.0249046,\n", "   -0.0545735881,\n", "   -0.00695204129,\n", "   0.00867365114,\n", "   0.000819423527,\n", "   -0.0247303322,\n", "   -0.0259499867,\n", "   -0.00127305684,\n", "   -0.00397711573,\n", "   0.0266769342,\n", "   0.0670888275,\n", "   0.0341880098,\n", "   -0.0208303966,\n", "   0.0607832968,\n", "   -0.0243635699,\n", "   -0.0158855226,\n", "   -0.00517179351,\n", "   0.00724978046,\n", "   -0.0387589671,\n", "   0.0148761291,\n", "   -0.00187950756,\n", "   -0.0609260723,\n", "   0.0121786138,\n", "   0.00791897438,\n", "   0.0417934619,\n", "   -0.020674184,\n", "   0.000952673086,\n", "   0.0198473614,\n", "   0.00873706304,\n", "   0.00955807511,\n", "   -0.0194783248,\n", "   0.0049889232,\n", "   0.00956442766,\n", "   0.0233892072,\n", "   -0.000955737138,\n", "   -0.0108669903,\n", "   -0.0238591861,\n", "   0.0238197967,\n", "   0.00174756558,\n", "   0.0299280304,\n", "   -0.0238600224,\n", "   0.015208574,\n", "   -0.0127028795,\n", "   0.0156393945,\n", "   0.0388081074,\n", "   0.0247424,\n", "   -0.0062543368,\n", "   0.000170785963,\n", "   0.0092145931,\n", "   0.0193969272,\n", "   -0.0302884318,\n", "   0.0236017443,\n", "   0.0366688855,\n", "   0.00559845287,\n", "   0.0242947116,\n", "   0.00307073887,\n", "   -0.0176014211,\n", "   -0.0309032705,\n", "   0.0184714682,\n", "   -0.0106057469,\n", "   0.0236521605,\n", "   0.0025398694,\n", "   -0.0139807491,\n", "   0.0135707743,\n", "   0.0128079141,\n", "   0.0142722176,\n", "   0.0575953871,\n", "   -0.0438407026,\n", "   0.0229826905,\n", "   -0.00599033851,\n", "   0.00385694834,\n", "   0.00758918,\n", "   -0.051237084,\n", "   -0.0194741357,\n", "   -0.0174463205,\n", "   -0.00545758381,\n", "   0.0261757672,\n", "   0.0130058099,\n", "   -0.0410934202,\n", "   0.0409190468,\n", "   -0.0128879789,\n", "   0.00905207358,\n", "   -0.0528616,\n", "   -0.00196890975,\n", "   -0.0167426802,\n", "   0.0180973839,\n", "   -0.00548402313,\n", "   0.00177069986,\n", "   -0.00767262606,\n", "   0.0137200467,\n", "   0.0326405317,\n", "   0.00299011706,\n", "   -0.00868851598,\n", "   -0.050801374,\n", "   -0.0250248257,\n", "   0.000445542857,\n", "   -0.0127881104,\n", "   0.00532563683,\n", "   -0.0110490872,\n", "   0.0243735164,\n", "   -0.0360136181,\n", "   0.0119271632,\n", "   -0.00506165251,\n", "   0.0021659059,\n", "   0.0112519097,\n", "   0.0300829075,\n", "   -0.00692087412,\n", "   0.00333238742,\n", "   0.0263190046,\n", "   -0.0395876244,\n", "   -0.000300656655,\n", "   -0.00288907182,\n", "   0.0111401593,\n", "   -0.00959030446,\n", "   -0.0216725674,\n", "   -0.0152074536,\n", "   0.000620191568,\n", "   -0.000918816135,\n", "   0.0786124393,\n", "   -0.0203573033,\n", "   0.0155137144,\n", "   0.0055152867,\n", "   -0.00881652,\n", "   0.00400751317,\n", "   0.0194048062,\n", "   -0.15483363,\n", "   -0.0303991083,\n", "   -0.00606513722,\n", "   -0.00067619863,\n", "   -0.0291646626,\n", "   -0.0399746075,\n", "   -0.0241807103,\n", "   -0.0297738891,\n", "   -0.0227140021,\n", "   -0.0117014581,\n", "   0.0421138555,\n", "   -0.0331923366,\n", "   -0.00915229227,\n", "   0.00510046631,\n", "   0.00824742764,\n", "   0.00977222901,\n", "   0.0137052406,\n", "   -0.0235539582,\n", "   -0.0221292526,\n", "   0.0106849028,\n", "   -0.00345948222,\n", "   0.029944526,\n", "   -0.0091318246,\n", "   -0.00436012913,\n", "   0.01705065,\n", "   0.0297246799,\n", "   -0.0203531291,\n", "   0.0342463665,\n", "   -0.0017272518,\n", "   -0.0271730199,\n", "   0.00295462832,\n", "   -0.0135343885,\n", "   -0.00657965848,\n", "   0.0601813383,\n", "   0.0274941,\n", "   0.0251081921,\n", "   0.0438779928,\n", "   -0.00482178945,\n", "   0.00144964689,\n", "   -0.0350635946,\n", "   -0.0115541983,\n", "   -0.0114675295,\n", "   0.0187749956,\n", "   0.0363530107,\n", "   -0.0368260667,\n", "   0.000332299707,\n", "   -0.00123887928,\n", "   -0.043686673,\n", "   -0.0473541841,\n", "   -0.0251685716,\n", "   -0.00966024399,\n", "   0.0134183373,\n", "   -0.015706392,\n", "   -0.0150509458,\n", "   -0.0503266,\n", "   0.0428089052,\n", "   -0.0117599145,\n", "   -0.0177397486,\n", "   0.00776691502,\n", "   0.0532107353,\n", "   -0.0292352922,\n", "   0.00714168092,\n", "   -0.00149917079,\n", "   0.0112065393,\n", "   0.0240546931,\n", "   0.00516151218,\n", "   -0.0178259071,\n", "   0.0241552368,\n", "   -0.0287281852,\n", "   -0.000226170829,\n", "   -0.00493461965,\n", "   0.0416365899,\n", "   -0.0189951453,\n", "   -0.0235623326,\n", "   0.00135881582,\n", "   0.000780581962,\n", "   -0.00344350259,\n", "   0.00957579445,\n", "   0.00541168218,\n", "   0.00136015436,\n", "   -0.00770182209,\n", "   -0.0739512816,\n", "   -0.0251311865,\n", "   0.00688495766,\n", "   0.0287694614,\n", "   -0.0238754097,\n", "   0.0307055227,\n", "   -0.00992546324,\n", "   0.002712616,\n", "   -0.0486387089,\n", "   0.0205370244,\n", "   -0.0169869605,\n", "   -0.00604198687,\n", "   -0.00431956816,\n", "   -0.0189029295,\n", "   -0.000680021942,\n", "   0.00210223487,\n", "   0.00491197314,\n", "   0.0180867612,\n", "   -0.00511865504,\n", "   -0.0106401732,\n", "   0.00974068791,\n", "   -0.0159273855,\n", "   -0.0142987277,\n", "   -0.00898170844,\n", "   0.0432581194,\n", "   0.0184525251,\n", "   -0.00845171139,\n", "   -0.0135475257,\n", "   0.0109996218,\n", "   0.0131268017,\n", "   0.0108917067,\n", "   0.00832745,\n", "   -0.00212246296,\n", "   0.00467417343,\n", "   0.0358864442,\n", "   -0.0245826673,\n", "   0.0063003148,\n", "   -0.00950602442,\n", "   0.0078677265,\n", "   0.0142467152,\n", "   -0.0125795407,\n", "   -0.0413156413,\n", "   0.0252746549,\n", "   -0.0472930148,\n", "   0.0111548007,\n", "   0.012075399,\n", "   0.0213641413,\n", "   0.0126804588,\n", "   0.0183166899,\n", "   -0.00661844527,\n", "   0.0167697612,\n", "   -0.0431091227,\n", "   0.024734322,\n", "   -0.0103106815,\n", "   0.000852683617,\n", "   0.00668726349,\n", "   -0.0163962618,\n", "   -0.0493248366,\n", "   -0.00504526496,\n", "   0.0323314406,\n", "   -0.0364449583,\n", "   -0.0454002731,\n", "   -0.0122620007,\n", "   -0.0364327,\n", "   -0.036116451,\n", "   0.00463471236,\n", "   -0.00204482302,\n", "   0.0153766591,\n", "   0.0117172739,\n", "   -0.018132776,\n", "   -0.0394639187,\n", "   -0.0162242167,\n", "   -0.0186182261,\n", "   -0.00429470791,\n", "   -0.0370763578,\n", "   -0.037787538,\n", "   0.020040134,\n", "   -0.0225570854,\n", "   -0.00524203107,\n", "   -0.0271280296,\n", "   -0.0148712713,\n", "   0.000399917364,\n", "   0.00917576347,\n", "   -0.00776499882,\n", "   0.0394242182,\n", "   -0.0304420814,\n", "   -0.0281178635,\n", "   -0.0202546045,\n", "   -0.00841739308,\n", "   -0.0192097574,\n", "   -8.78350838e-05,\n", "   -0.0231675971,\n", "   -0.031613566,\n", "   -0.0224745441,\n", "   0.0024674728,\n", "   0.0179552268,\n", "   -0.0352309197,\n", "   -0.000747424201,\n", "   0.00533752237,\n", "   0.00987931,\n", "   -0.0171834957,\n", "   0.0113367345,\n", "   -0.00755123654,\n", "   0.00874816813,\n", "   -0.00743617443,\n", "   -0.0188391972,\n", "   0.011982209,\n", "   -0.00550748035,\n", "   -0.0112377005,\n", "   0.00356020266,\n", "   -0.0148031479,\n", "   0.0129267899,\n", "   -0.00671247393,\n", "   -0.00759130064,\n", "   -0.0324293114,\n", "   -0.0278775226,\n", "   0.00971174706,\n", "   -0.00371296611,\n", "   -0.0240826327,\n", "   0.0102031389,\n", "   -0.0495990813,\n", "   0.0149722965,\n", "   0.000473470514,\n", "   -0.0295124985,\n", "   0.0230336506,\n", "   0.00984597672,\n", "   0.00915399753,\n", "   -0.00689517,\n", "   -0.025412634,\n", "   -0.00568648661,\n", "   0.00535085564,\n", "   0.00381233636,\n", "   0.0186336935,\n", "   -0.00918122754,\n", "   -0.0157072823,\n", "   0.027951384,\n", "   0.00542537821,\n", "   -0.0374221019,\n", "   -0.0198027492,\n", "   -0.0211681798,\n", "   -0.0370443128,\n", "   -0.0094717918,\n", "   0.0114364559,\n", "   -0.0423071943,\n", "   -0.0187826306,\n", "   0.014118975,\n", "   0.00749820052,\n", "   0.00196934119,\n", "   0.00593614811,\n", "   0.023406256,\n", "   -0.0313365087,\n", "   0.0167573635,\n", "   -0.00604882091,\n", "   0.00334436982,\n", "   0.0135535412,\n", "   -0.0190169103,\n", "   0.037266247,\n", "   0.0432399735,\n", "   5.46086812e-05,\n", "   0.0165723637,\n", "   0.0208506752,\n", "   0.00723584834,\n", "   0.0098296646,\n", "   0.0307437051,\n", "   -0.0281617288,\n", "   -0.00600888766,\n", "   -0.0129341939,\n", "   -0.0170226619,\n", "   -0.0419492349,\n", "   -0.0094757732,\n", "   -0.0155830812,\n", "   0.0146701597,\n", "   -0.00628540246,\n", "   -0.0107294684,\n", "   -0.000534298713,\n", "   -0.0297907777,\n", "   -0.00721319811,\n", "   -0.0548978858,\n", "   0.016639119,\n", "   -0.00803484116,\n", "   -0.0044591222,\n", "   0.0285902768,\n", "   -0.0348913483,\n", "   -0.00439254753,\n", "   -0.0177756306,\n", "   -0.0213384498,\n", "   -0.00263558049,\n", "   0.0580815598,\n", "   0.0168863442,\n", "   -0.0360246636,\n", "   -0.00490574958,\n", "   0.00212190906,\n", "   -0.0174026154,\n", "   -0.00172062335,\n", "   -0.0311930683,\n", "   -0.0241949018,\n", "   -0.00276837475,\n", "   0.00526990183,\n", "   -0.000910527422,\n", "   -0.0512738824,\n", "   0.0287775658,\n", "   0.0109758107,\n", "   -0.0159783,\n", "   0.00935895275,\n", "   0.00755539304,\n", "   -0.006994,\n", "   0.00911760796,\n", "   -0.0203491263,\n", "   -0.0464415625,\n", "   -0.00538986968,\n", "   0.0256398506,\n", "   -0.017696755,\n", "   -0.0136808604,\n", "   0.00677481107,\n", "   0.0133596323,\n", "   -0.0019575567,\n", "   0.00289190514,\n", "   -0.0266775917,\n", "   -0.0207125302,\n", "   -0.0353355072,\n", "   0.000118187454,\n", "   0.0282863788,\n", "   -0.0109982044,\n", "   -0.000830707722,\n", "   0.0227018073,\n", "   0.000158486073,\n", "   -0.0121143339,\n", "   -0.00888754893,\n", "   -0.00323723303,\n", "   -0.00198396295,\n", "   -0.0213215817,\n", "   0.0113635948,\n", "   -0.0134692593,\n", "   0.013322033,\n", "   -0.00729085552,\n", "   0.00742867,\n", "   -0.0128974756,\n", "   0.00412745401,\n", "   -0.0847318843,\n", "   0.0360378511,\n", "   -0.0273377355,\n", "   0.0419145,\n", "   0.00704701059,\n", "   0.0216623396,\n", "   0.00028421462,\n", "   -0.00507104024,\n", "   0.0434082821,\n", "   -0.0242413655,\n", "   -0.00894137658,\n", "   -0.000396625284,\n", "   0.0138703836,\n", "   0.0192973241,\n", "   0.0126202544,\n", "   -0.0127655761,\n", "   -0.00649031484,\n", "   0.0190916359,\n", "   0.0270106364,\n", "   -0.00803163741,\n", "   0.0300297085,\n", "   -0.0242875293,\n", "   -0.00371544808,\n", "   -0.00153446349,\n", "   -0.0131750265,\n", "   -0.0108925439,\n", "   0.0125231799,\n", "   -0.0176402424,\n", "   -0.00327558909,\n", "   0.0149195651,\n", "   0.0342708193,\n", "   0.0111544542,\n", "   -0.0234794989,\n", "   0.00762140146,\n", "   -0.00392675167,\n", "   0.00799637754,\n", "   -0.02684897,\n", "   0.0244356468,\n", "   0.00641759671,\n", "   -0.0227065068,\n", "   -0.0187496915,\n", "   -0.00843477715,\n", "   0.0271126926,\n", "   -0.00365502643,\n", "   -0.0505430773,\n", "   0.0240559336,\n", "   0.0103015583,\n", "   0.0152175678,\n", "   -0.0266690906,\n", "   0.00705662603,\n", "   -0.0092792483,\n", "   0.00734073482,\n", "   0.0286570136,\n", "   -0.00500450376,\n", "   -0.0293732565,\n", "   -0.144583821,\n", "   -0.0209662523,\n", "   -0.00655124756,\n", "   -0.0635312572,\n", "   -0.0223132949,\n", "   -0.00122606487,\n", "   0.0214430708,\n", "   0.037389908,\n", "   -0.028207453,\n", "   -0.0101023046,\n", "   0.0536526628,\n", "   0.00585524086,\n", "   -0.125183851,\n", "   -0.00699849613,\n", "   -0.0239569657,\n", "   0.00170170516,\n", "   0.013879871,\n", "   -0.0218172446,\n", "   0.00185606885,\n", "   0.0197750609,\n", "   -0.0279947463,\n", "   0.0245454665,\n", "   0.0185433775,\n", "   0.000910729403,\n", "   0.024671562,\n", "   -0.00954633299,\n", "   0.0184657387,\n", "   -0.0129662808,\n", "   0.0145524153,\n", "   -0.0214033071,\n", "   0.0144459698,\n", "   0.0060765883,\n", "   -0.0126748905,\n", "   0.044832252,\n", "   0.0196349751,\n", "   -0.0315287262,\n", "   -0.0288128033,\n", "   0.018204039,\n", "   0.003962941,\n", "   0.0213972535,\n", "   -0.00811180566,\n", "   0.0451961905,\n", "   -0.0237785596,\n", "   0.0157495905,\n", "   -0.000907260226,\n", "   -0.00506728329,\n", "   -0.00765838567,\n", "   0.0390501916,\n", "   -0.0105704712,\n", "   -0.0548747815,\n", "   0.0175376534,\n", "   0.00530221406,\n", "   0.0173840839,\n", "   -0.0298947599,\n", "   0.0230476893,\n", "   0.0178778972,\n", "   -0.0077023562,\n", "   0.0226221494,\n", "   -0.0299088228,\n", "   -0.0199432541,\n", "   -0.0116480067,\n", "   -0.021548992,\n", "   -0.0320698917,\n", "   -0.0342686251,\n", "   0.00723284855,\n", "   -0.0165100433,\n", "   -0.0166889448,\n", "   0.0454638302,\n", "   0.00705047743,\n", "   0.0714525282,\n", "   -0.000220214642,\n", "   -0.0337371193,\n", "   -0.000185466371,\n", "   0.00708855828,\n", "   0.0224644653,\n", "   0.0229536016,\n", "   -0.00719089247,\n", "   0.0104853176,\n", "   0.017462736,\n", "   -0.0466015227,\n", "   0.0536888056,\n", "   0.0796394423,\n", "   -0.00383868185,\n", "   -0.00873902813,\n", "   0.0193505082,\n", "   -0.00373915839,\n", "   0.0281537902,\n", "   -0.00156790484,\n", "   -0.16788429,\n", "   0.00106078549,\n", "   -0.0155825503,\n", "   0.022185009,\n", "   -0.0301392376,\n", "   0.0148533331,\n", "   -0.000567343668,\n", "   -0.0144326556,\n", "   -0.0025436,\n", "   0.0347647,\n", "   0.0160890874,\n", "   0.0205950327,\n", "   0.0174763221,\n", "   0.00707936659,\n", "   -0.013084718,\n", "   0.0103576351,\n", "   0.040309675,\n", "   0.00920453202,\n", "   -0.00813397579,\n", "   -0.00463764323,\n", "   -0.0351081,\n", "   -0.0214713961,\n", "   -0.00224740081,\n", "   -0.0228223354,\n", "   -0.0165204853,\n", "   0.00681932271,\n", "   -0.00435205642,\n", "   -0.00811584201,\n", "   0.0310515184,\n", "   0.0244199596,\n", "   -0.0475072,\n", "   0.00432535866,\n", "   -0.00835522544,\n", "   -0.0110745197,\n", "   0.0221303049,\n", "   -0.027185183,\n", "   0.0140302805,\n", "   -0.0178679954,\n", "   0.00456612743,\n", "   0.00195960863,\n", "   -0.0472552925,\n", "   -0.0200952329,\n", "   0.0053011165,\n", "   0.00297658728,\n", "   -0.0358138345,\n", "   0.0139616244,\n", "   0.0173015408,\n", "   -0.00157542259,\n", "   0.0147369895,\n", "   0.0136518329,\n", "   0.00292773871,\n", "   0.0106373699,\n", "   0.0322181098,\n", "   -0.0166140366,\n", "   0.017331481,\n", "   -0.00523170969,\n", "   0.0268950947,\n", "   0.0063786027,\n", "   -0.0150884027,\n", "   -0.00424848543,\n", "   0.000331583637,\n", "   -0.000617059122,\n", "   0.0336492285,\n", "   -0.0556678511,\n", "   0.0182202067,\n", "   -0.0129221855,\n", "   0.022780573,\n", "   -0.0290868487,\n", "   0.00694200955,\n", "   -0.0311148167,\n", "   0.0256243274,\n", "   -0.0388887338,\n", "   0.000112005757,\n", "   -0.0104286866,\n", "   0.0546167567,\n", "   -0.00882015284,\n", "   0.0293875244,\n", "   -0.0468536206,\n", "   0.0162017848,\n", "   0.0270224661,\n", "   0.0788664147,\n", "   0.00233986741,\n", "   -0.0362862125,\n", "   0.0078153573,\n", "   0.0653237551,\n", "   0.0180285014,\n", "   -0.0238776281,\n", "   -0.0275860578,\n", "   0.00355997146,\n", "   0.019247815,\n", "   -0.0168683212,\n", "   0.0256695431,\n", "   0.0599227175,\n", "   -0.00763268536,\n", "   0.0376873314,\n", "   0.018516168,\n", "   -0.0279468857,\n", "   0.00634821225,\n", "   -0.0376185253,\n", "   -0.0694081709,\n", "   -0.00407433324,\n", "   0.0168318972,\n", "   -0.00926529523,\n", "   0.000384035491,\n", "   0.0276266541,\n", "   -0.037010204,\n", "   -0.0329852,\n", "   -0.0286789332,\n", "   0.0104202973,\n", "   0.000334482087,\n", "   -0.0574898534,\n", "   -0.0142484009,\n", "   -0.012045254,\n", "   -0.0179980211,\n", "   -0.021662809,\n", "   -0.00946151465,\n", "   0.0337492898,\n", "   0.0212940592,\n", "   0.0113697834,\n", "   -0.000927298,\n", "   -0.0329875685,\n", "   -0.000616950914,\n", "   0.000993012218,\n", "   0.00282013882,\n", "   -0.0200874787,\n", "   0.0457894057,\n", "   -0.0105021829,\n", "   -0.0282762442,\n", "   -0.0500517,\n", "   -0.00227439334,\n", "   -0.0243159775,\n", "   0.022775542,\n", "   -0.0257138014,\n", "   -0.0167306475,\n", "   0.0233987551,\n", "   0.00910983328,\n", "   -0.0262735449,\n", "   -0.00228772312,\n", "   0.010719751,\n", "   -0.0152561655,\n", "   0.00621602405,\n", "   0.0221160743,\n", "   0.00333869341,\n", "   -0.0264158174,\n", "   -0.0429339409,\n", "   0.0186719336,\n", "   0.0264487937,\n", "   0.0129683651,\n", "   -0.0226065628,\n", "   -0.0338416621,\n", "   0.0573747307,\n", "   -0.030046707,\n", "   0.0680659562,\n", "   -0.00449245935,\n", "   -0.00626359554,\n", "   0.0571239404,\n", "   -0.0109125366,\n", "   -0.03887235,\n", "   0.0101087792,\n", "   -0.0168540422,\n", "   -0.00278215786,\n", "   0.000277052925,\n", "   -0.00325803319,\n", "   -0.0280842651,\n", "   -0.0152849937,\n", "   0.00970299821,\n", "   -0.0224446747,\n", "   -0.0233558454,\n", "   -0.0177765973,\n", "   0.0183340441,\n", "   0.0336613059,\n", "   0.0112207653,\n", "   0.0269384179,\n", "   -0.0403991118,\n", "   0.00913302228,\n", "   0.00647973921,\n", "   0.026439717,\n", "   0.00131579826,\n", "   0.034913823,\n", "   0.00186191499,\n", "   -0.00948355813,\n", "   -0.00760868192,\n", "   -0.00191481307,\n", "   -0.00203672773,\n", "   0.0144932689,\n", "   -0.0229134448,\n", "   0.0583214276,\n", "   -0.0353528261,\n", "   0.0324698947,\n", "   -0.00680733752,\n", "   0.0296025015,\n", "   0.00809030421,\n", "   0.00189846416,\n", "   0.0125356372,\n", "   -0.00308652548,\n", "   -0.0467098281,\n", "   -0.0327151679,\n", "   -0.00663286401,\n", "   -0.00500001153,\n", "   0.00151349523,\n", "   0.00814026408,\n", "   -0.050608851,\n", "   -0.0345958956,\n", "   -0.0405786484,\n", "   -0.00282377354,\n", "   0.0222595856,\n", "   0.0203703046,\n", "   0.0121854292,\n", "   -0.00238963612,\n", "   0.0340137668,\n", "   -0.00585140847,\n", "   0.00460050628,\n", "   -0.0136705767,\n", "   -0.00412887381,\n", "   0.00569567364,\n", "   0.0981713459,\n", "   0.00974858,\n", "   -0.0187235381,\n", "   -0.0077194809,\n", "   0.00978741609,\n", "   0.00771071436,\n", "   0.0052219159,\n", "   -0.03645752,\n", "   0.0199433938,\n", "   -0.0211821832,\n", "   0.00333418511,\n", "   -0.017311193,\n", "   0.0165174864,\n", "   0.0330298468,\n", "   -0.00277433987,\n", "   -0.0226683188,\n", "   -0.00859233551,\n", "   0.0157456696,\n", "   -0.0519375652,\n", "   0.00811520219,\n", "   -0.0011906554,\n", "   -0.0419305041,\n", "   0.00469839759,\n", "   -0.00985397305,\n", "   0.000345878332,\n", "   0.0119942874,\n", "   0.0156665985,\n", "   -0.0191565938,\n", "   -0.0327870324,\n", "   -0.00174538814,\n", "   -0.0232697483,\n", "   0.0413756073,\n", "   -0.0281270109,\n", "   0.0124676554,\n", "   0.0211318322,\n", "   0.0117071094,\n", "   -0.0254289322,\n", "   ...],\n", "  '$similarity': 0.9142833},\n", " {'_id': 14,\n", "  'name': 'Filter 2 Cup 50mm',\n", "  'image': 'https://www.breville.com/content/dam/breville/us/catalog/products/images/sp0/sp0000166/tile.jpg',\n", "  'url': 'https://www.breville.com/us/en/parts-accessories/parts/sp0000166.html?sku=SP0000166',\n", "  'price': '11.95',\n", "  '$vector': [-0.00165699038,\n", "   -0.0407552,\n", "   0.00935592,\n", "   0.0240488704,\n", "   -0.00292191794,\n", "   -0.0234730896,\n", "   0.0204971228,\n", "   -0.0365898088,\n", "   -0.0348756835,\n", "   -0.0137100108,\n", "   0.00392858079,\n", "   -0.028694503,\n", "   -0.00180248544,\n", "   0.144398272,\n", "   -0.0417757742,\n", "   0.0265440661,\n", "   0.0673257187,\n", "   7.06843784e-05,\n", "   -0.00778054353,\n", "   0.00130644615,\n", "   -0.013262108,\n", "   -0.00493173162,\n", "   -0.0316620916,\n", "   -0.02310054,\n", "   0.000354597811,\n", "   0.00716842338,\n", "   -0.00875866,\n", "   -0.0158262737,\n", "   0.0584966503,\n", "   -0.0318339802,\n", "   0.0148021951,\n", "   0.00368932518,\n", "   0.00901262,\n", "   0.00129902817,\n", "   0.0110299718,\n", "   -0.0195786431,\n", "   -0.00320326327,\n", "   0.0139071718,\n", "   0.00714749517,\n", "   -0.00286160875,\n", "   -0.0254110862,\n", "   -0.0167990774,\n", "   -0.0228026174,\n", "   -0.0181889348,\n", "   0.0314006768,\n", "   -0.0170592647,\n", "   -0.0114893783,\n", "   -0.00482409867,\n", "   0.0121465586,\n", "   -0.00399773289,\n", "   -0.0196958072,\n", "   -0.0214321315,\n", "   0.016565552,\n", "   -0.0247377157,\n", "   0.0337828696,\n", "   -0.00276688696,\n", "   -0.0382150039,\n", "   -0.0249067377,\n", "   0.0544169769,\n", "   -0.0269161854,\n", "   0.0274631623,\n", "   -0.00188212399,\n", "   0.00941899512,\n", "   -0.0274392311,\n", "   -0.00335363788,\n", "   0.00952113513,\n", "   0.0177560523,\n", "   -0.00833044481,\n", "   -0.00573350582,\n", "   -0.0357770324,\n", "   -0.00958771911,\n", "   -0.0357328765,\n", "   -0.0251551978,\n", "   0.0174910687,\n", "   -0.0260026194,\n", "   -0.0038226957,\n", "   -0.0551266298,\n", "   0.00892452244,\n", "   -0.0219158139,\n", "   -0.0434414186,\n", "   0.0380463712,\n", "   0.0185155235,\n", "   -0.00715654762,\n", "   -0.00365037448,\n", "   -0.00589947123,\n", "   0.0187356584,\n", "   0.00630183,\n", "   -0.0115513559,\n", "   -0.0559021831,\n", "   0.0254162923,\n", "   0.0466984771,\n", "   0.0213260278,\n", "   -0.00920727383,\n", "   -0.212387532,\n", "   -0.0203801375,\n", "   -0.0640808567,\n", "   -0.0426175781,\n", "   -0.00295306835,\n", "   0.0285645742,\n", "   -0.0437956043,\n", "   0.00716584641,\n", "   -0.0194792822,\n", "   0.0166544747,\n", "   0.0202985331,\n", "   -0.0587412231,\n", "   0.00167152868,\n", "   -0.0182512961,\n", "   -0.0382449701,\n", "   -0.00333153,\n", "   -0.0326583348,\n", "   -0.0206277147,\n", "   0.0232429691,\n", "   -0.00795351434,\n", "   -0.0252031945,\n", "   0.0161882173,\n", "   -0.0120440181,\n", "   -0.031782303,\n", "   0.00391474273,\n", "   -0.0109693529,\n", "   0.0475919172,\n", "   -0.0136757111,\n", "   -0.0209989715,\n", "   0.00238125259,\n", "   -0.000523344846,\n", "   -0.0512199067,\n", "   -0.0713636056,\n", "   -0.0139688235,\n", "   -0.0459943,\n", "   -0.00134782924,\n", "   -0.0110227969,\n", "   0.0422348119,\n", "   0.0357554071,\n", "   0.014670372,\n", "   -0.025916541,\n", "   0.0126572773,\n", "   0.00727090333,\n", "   -0.0113343578,\n", "   -0.00573126646,\n", "   0.0121880174,\n", "   -0.00535292085,\n", "   0.0100382809,\n", "   0.0144581795,\n", "   -0.00926827453,\n", "   -0.0451646298,\n", "   0.0239401907,\n", "   -0.0169087667,\n", "   0.00350715267,\n", "   -0.0171826333,\n", "   -0.0320435539,\n", "   -0.0377351344,\n", "   -0.01373671,\n", "   -0.0541445278,\n", "   -0.0534058549,\n", "   0.0145495636,\n", "   0.0148027772,\n", "   -0.00706152432,\n", "   0.0191402975,\n", "   0.0144958664,\n", "   -0.0103841554,\n", "   -0.00838515814,\n", "   0.00154108764,\n", "   -0.0249086041,\n", "   -0.0545742624,\n", "   -0.00695430627,\n", "   0.00867411308,\n", "   0.000819350535,\n", "   -0.0247305185,\n", "   -0.0259500872,\n", "   -0.0012707063,\n", "   -0.00397609221,\n", "   0.0266794171,\n", "   0.0670898333,\n", "   0.0341863632,\n", "   -0.0208290219,\n", "   0.0607810467,\n", "   -0.0243644286,\n", "   -0.0158834737,\n", "   -0.00517305266,\n", "   0.00724863,\n", "   -0.0387597196,\n", "   0.0148768183,\n", "   -0.00187612,\n", "   -0.0609261468,\n", "   0.0121783763,\n", "   0.00791584514,\n", "   0.0417951643,\n", "   -0.0206758864,\n", "   0.000954985677,\n", "   0.0198463164,\n", "   0.00873773824,\n", "   0.00955811702,\n", "   -0.0194737893,\n", "   0.0049878438,\n", "   0.00956780836,\n", "   0.0233882163,\n", "   -0.000955397845,\n", "   -0.0108648585,\n", "   -0.0238588154,\n", "   0.0238238927,\n", "   0.00175041484,\n", "   0.029928118,\n", "   -0.0238613579,\n", "   0.015209252,\n", "   -0.01270347,\n", "   0.0156427734,\n", "   0.0388091728,\n", "   0.0247402135,\n", "   -0.00625440571,\n", "   0.000170107014,\n", "   0.00921322498,\n", "   0.0193982348,\n", "   -0.0302859508,\n", "   0.0236026365,\n", "   0.0366658643,\n", "   0.00560564362,\n", "   0.0242977068,\n", "   0.00306859729,\n", "   -0.0176014267,\n", "   -0.0309054144,\n", "   0.0184714273,\n", "   -0.0106080975,\n", "   0.0236526243,\n", "   0.00254326034,\n", "   -0.01398117,\n", "   0.0135688223,\n", "   0.0128080556,\n", "   0.0142711503,\n", "   0.0575934686,\n", "   -0.0438396893,\n", "   0.0229850654,\n", "   -0.00598857179,\n", "   0.00385655835,\n", "   0.00758605869,\n", "   -0.0512355268,\n", "   -0.0194748677,\n", "   -0.0174459238,\n", "   -0.00545807322,\n", "   0.0261732191,\n", "   0.0130077545,\n", "   -0.0410951748,\n", "   0.0409215055,\n", "   -0.0128875272,\n", "   0.009051295,\n", "   -0.052863162,\n", "   -0.00196978683,\n", "   -0.0167412944,\n", "   0.018095497,\n", "   -0.0054828506,\n", "   0.00176954502,\n", "   -0.00767643284,\n", "   0.0137190726,\n", "   0.0326385871,\n", "   0.00298834196,\n", "   -0.0086878147,\n", "   -0.0508025736,\n", "   -0.025027018,\n", "   0.000445678626,\n", "   -0.0127879269,\n", "   0.00532633625,\n", "   -0.011048276,\n", "   0.0243760329,\n", "   -0.0360178724,\n", "   0.011925607,\n", "   -0.00506204553,\n", "   0.00216519367,\n", "   0.0112525458,\n", "   0.030082345,\n", "   -0.00691803638,\n", "   0.0033318582,\n", "   0.0263168402,\n", "   -0.0395819098,\n", "   -0.000301772292,\n", "   -0.00289047067,\n", "   0.0111391619,\n", "   -0.00959105138,\n", "   -0.0216701366,\n", "   -0.0152074145,\n", "   0.000620350591,\n", "   -0.000919000478,\n", "   0.0786180422,\n", "   -0.0203565601,\n", "   0.0155139044,\n", "   0.00551667158,\n", "   -0.00881455466,\n", "   0.00400846964,\n", "   0.0194074102,\n", "   -0.154840231,\n", "   -0.0303998105,\n", "   -0.0060690539,\n", "   -0.000676687458,\n", "   -0.0291659124,\n", "   -0.0399734974,\n", "   -0.0241812784,\n", "   -0.0297725424,\n", "   -0.0227124225,\n", "   -0.0117016118,\n", "   0.0421178117,\n", "   -0.0331915729,\n", "   -0.00915551558,\n", "   0.00509839412,\n", "   0.00824988447,\n", "   0.00977005,\n", "   0.0137055255,\n", "   -0.0235525519,\n", "   -0.0221296772,\n", "   0.0106850825,\n", "   -0.00346014462,\n", "   0.0299397446,\n", "   -0.00913351681,\n", "   -0.00435495144,\n", "   0.0170477778,\n", "   0.0297257248,\n", "   -0.0203548651,\n", "   0.0342440493,\n", "   -0.00172713178,\n", "   -0.0271689426,\n", "   0.00295832101,\n", "   -0.0135372635,\n", "   -0.00657674903,\n", "   0.0601768419,\n", "   0.0274925213,\n", "   0.0251085106,\n", "   0.0438763,\n", "   -0.00482022529,\n", "   0.0014497363,\n", "   -0.0350650549,\n", "   -0.0115545159,\n", "   -0.0114695951,\n", "   0.0187754221,\n", "   0.0363508798,\n", "   -0.0368260331,\n", "   0.000330882845,\n", "   -0.00124161725,\n", "   -0.0436892919,\n", "   -0.0473565944,\n", "   -0.025170574,\n", "   -0.00966328,\n", "   0.013422884,\n", "   -0.0157091971,\n", "   -0.0150552709,\n", "   -0.0503252745,\n", "   0.0428112969,\n", "   -0.0117599415,\n", "   -0.0177364927,\n", "   0.00776406378,\n", "   0.0532066,\n", "   -0.0292388108,\n", "   0.00714325625,\n", "   -0.00150057,\n", "   0.011204253,\n", "   0.024055196,\n", "   0.00516005186,\n", "   -0.0178255588,\n", "   0.0241555534,\n", "   -0.0287274774,\n", "   -0.00022497015,\n", "   -0.00493165432,\n", "   0.0416364931,\n", "   -0.0189931616,\n", "   -0.0235626139,\n", "   0.00135925715,\n", "   0.000781312643,\n", "   -0.00344456802,\n", "   0.00957202818,\n", "   0.00541636394,\n", "   0.00135971338,\n", "   -0.00770456064,\n", "   -0.0739490539,\n", "   -0.0251314789,\n", "   0.00688610971,\n", "   0.0287713502,\n", "   -0.0238724444,\n", "   0.0307027157,\n", "   -0.00992571283,\n", "   0.00271152682,\n", "   -0.0486381,\n", "   0.0205388386,\n", "   -0.0169884954,\n", "   -0.0060432707,\n", "   -0.00432091067,\n", "   -0.018898081,\n", "   -0.000683572143,\n", "   0.00209982181,\n", "   0.00491065811,\n", "   0.0180862416,\n", "   -0.00512032118,\n", "   -0.0106390435,\n", "   0.00973738264,\n", "   -0.0159235857,\n", "   -0.0143027427,\n", "   -0.00898399856,\n", "   0.0432573,\n", "   0.0184516,\n", "   -0.00845692679,\n", "   -0.0135459965,\n", "   0.0110000558,\n", "   0.0131297177,\n", "   0.0108892,\n", "   0.00832816865,\n", "   -0.00212435191,\n", "   0.00467366865,\n", "   0.0358879454,\n", "   -0.0245872289,\n", "   0.00629902771,\n", "   -0.00950406492,\n", "   0.00786924083,\n", "   0.0142435562,\n", "   -0.0125826057,\n", "   -0.041315563,\n", "   0.0252758488,\n", "   -0.0472910441,\n", "   0.0111574586,\n", "   0.0120740328,\n", "   0.0213675778,\n", "   0.0126786716,\n", "   0.0183160342,\n", "   -0.00661858,\n", "   0.016772382,\n", "   -0.0431143157,\n", "   0.0247338675,\n", "   -0.0103121642,\n", "   0.000856862403,\n", "   0.0066869366,\n", "   -0.0163999666,\n", "   -0.0493271649,\n", "   -0.00504213758,\n", "   0.0323362276,\n", "   -0.0364427418,\n", "   -0.0454015918,\n", "   -0.012260071,\n", "   -0.0364325829,\n", "   -0.0361175835,\n", "   0.00463350723,\n", "   -0.00204425515,\n", "   0.0153766312,\n", "   0.0117188282,\n", "   -0.0181353129,\n", "   -0.0394646488,\n", "   -0.0162236188,\n", "   -0.0186181925,\n", "   -0.0042953603,\n", "   -0.0370766222,\n", "   -0.0377907977,\n", "   0.0200404041,\n", "   -0.0225558523,\n", "   -0.00523769762,\n", "   -0.0271267667,\n", "   -0.014872984,\n", "   0.000402934413,\n", "   0.00917523075,\n", "   -0.00776439812,\n", "   0.0394241288,\n", "   -0.0304402746,\n", "   -0.0281176623,\n", "   -0.0202518255,\n", "   -0.00841772184,\n", "   -0.0192121714,\n", "   -8.77707862e-05,\n", "   -0.0231657214,\n", "   -0.0316124186,\n", "   -0.0224734936,\n", "   0.00247004209,\n", "   0.0179578755,\n", "   -0.0352323614,\n", "   -0.000750422594,\n", "   0.00533691514,\n", "   0.00988111272,\n", "   -0.0171848647,\n", "   0.0113350404,\n", "   -0.00755118113,\n", "   0.00874939654,\n", "   -0.00743619865,\n", "   -0.0188412145,\n", "   0.0119848959,\n", "   -0.00550583703,\n", "   -0.0112384064,\n", "   0.00355843641,\n", "   -0.0148023367,\n", "   0.0129250139,\n", "   -0.00670897681,\n", "   -0.00759061147,\n", "   -0.0324264839,\n", "   -0.0278805345,\n", "   0.00970963109,\n", "   -0.00371329021,\n", "   -0.0240832046,\n", "   0.0102046449,\n", "   -0.0496001877,\n", "   0.0149737159,\n", "   0.00047178645,\n", "   -0.0295107104,\n", "   0.0230313875,\n", "   0.00984656,\n", "   0.00915276166,\n", "   -0.00689572934,\n", "   -0.025412472,\n", "   -0.00568926195,\n", "   0.00535366405,\n", "   0.00381387421,\n", "   0.0186330881,\n", "   -0.0091810422,\n", "   -0.0157052185,\n", "   0.0279423092,\n", "   0.00542825321,\n", "   -0.0374203622,\n", "   -0.0198017117,\n", "   -0.0211680178,\n", "   -0.037046086,\n", "   -0.0094723478,\n", "   0.0114385169,\n", "   -0.0423060767,\n", "   -0.0187813397,\n", "   0.0141209876,\n", "   0.00749538466,\n", "   0.00197027414,\n", "   0.00593834091,\n", "   0.0234074071,\n", "   -0.0313391723,\n", "   0.0167561565,\n", "   -0.00605131034,\n", "   0.00334432744,\n", "   0.0135547593,\n", "   -0.0190170091,\n", "   0.0372653753,\n", "   0.0432371758,\n", "   5.50087752e-05,\n", "   0.0165683758,\n", "   0.020849051,\n", "   0.00723405136,\n", "   0.0098267477,\n", "   0.0307427309,\n", "   -0.0281606931,\n", "   -0.00600869535,\n", "   -0.0129355043,\n", "   -0.0170238148,\n", "   -0.0419472,\n", "   -0.00947854947,\n", "   -0.015586813,\n", "   0.0146694882,\n", "   -0.00628737081,\n", "   -0.0107262321,\n", "   -0.000534670195,\n", "   -0.0297924876,\n", "   -0.00721271243,\n", "   -0.0548993908,\n", "   0.0166399293,\n", "   -0.0080372924,\n", "   -0.00446016714,\n", "   0.0285881739,\n", "   -0.0348903537,\n", "   -0.00439227093,\n", "   -0.0177771952,\n", "   -0.0213359818,\n", "   -0.00264026714,\n", "   0.0580813102,\n", "   0.0168863572,\n", "   -0.0360252783,\n", "   -0.00490538636,\n", "   0.00211853371,\n", "   -0.0174026117,\n", "   -0.00172022625,\n", "   -0.0311935134,\n", "   -0.0241951309,\n", "   -0.00276775844,\n", "   0.00526994606,\n", "   -0.000910549541,\n", "   -0.0512712821,\n", "   0.0287785437,\n", "   0.0109746428,\n", "   -0.0159746297,\n", "   0.00936115533,\n", "   0.00755464891,\n", "   -0.00699295197,\n", "   0.00911641866,\n", "   -0.0203501582,\n", "   -0.0464422517,\n", "   -0.00538920937,\n", "   0.0256427936,\n", "   -0.0176939517,\n", "   -0.0136800157,\n", "   0.00677697686,\n", "   0.0133606335,\n", "   -0.00195736624,\n", "   0.0028904886,\n", "   -0.0266789012,\n", "   -0.0207120106,\n", "   -0.0353361852,\n", "   0.000114857256,\n", "   0.0282859616,\n", "   -0.0109949568,\n", "   -0.000833100465,\n", "   0.0227017142,\n", "   0.000158426803,\n", "   -0.012114984,\n", "   -0.00888792612,\n", "   -0.00323386956,\n", "   -0.00198472,\n", "   -0.0213201419,\n", "   0.0113614481,\n", "   -0.0134665268,\n", "   0.0133196125,\n", "   -0.00729132956,\n", "   0.00742703956,\n", "   -0.012896549,\n", "   0.00412517088,\n", "   -0.0847359523,\n", "   0.0360366553,\n", "   -0.0273404568,\n", "   0.0419124551,\n", "   0.0070510935,\n", "   0.0216605868,\n", "   0.000286032504,\n", "   -0.00506973965,\n", "   0.0434047766,\n", "   -0.0242428053,\n", "   -0.00894109812,\n", "   -0.000394789415,\n", "   0.0138710076,\n", "   0.019298669,\n", "   0.0126186749,\n", "   -0.012764046,\n", "   -0.00649039773,\n", "   0.0190884303,\n", "   0.0270107463,\n", "   -0.00803163368,\n", "   0.0300322603,\n", "   -0.0242881719,\n", "   -0.0037127668,\n", "   -0.00153640425,\n", "   -0.0131731732,\n", "   -0.0108942194,\n", "   0.0125226527,\n", "   -0.0176371802,\n", "   -0.00327453017,\n", "   0.0149148293,\n", "   0.0342671648,\n", "   0.0111530824,\n", "   -0.0234761219,\n", "   0.0076215677,\n", "   -0.00392811093,\n", "   0.00799785927,\n", "   -0.0268464424,\n", "   0.0244354401,\n", "   0.00641419133,\n", "   -0.0227050129,\n", "   -0.0187511873,\n", "   -0.00843408145,\n", "   0.0271137822,\n", "   -0.00365450629,\n", "   -0.0505422279,\n", "   0.0240555014,\n", "   0.0103016337,\n", "   0.0152169764,\n", "   -0.0266676657,\n", "   0.00705535803,\n", "   -0.00927661266,\n", "   0.00733872456,\n", "   0.0286584031,\n", "   -0.00500415685,\n", "   -0.0293735601,\n", "   -0.144584969,\n", "   -0.020967776,\n", "   -0.00655294023,\n", "   -0.0635309219,\n", "   -0.0223131645,\n", "   -0.00122601329,\n", "   0.0214444976,\n", "   0.0373907052,\n", "   -0.0282090232,\n", "   -0.010101608,\n", "   0.0536552556,\n", "   0.00585323293,\n", "   -0.125179559,\n", "   -0.00699926075,\n", "   -0.0239576288,\n", "   0.001704711,\n", "   0.0138790822,\n", "   -0.0218146015,\n", "   0.00185303367,\n", "   0.0197774954,\n", "   -0.0279958267,\n", "   0.0245444644,\n", "   0.0185423493,\n", "   0.000910454837,\n", "   0.0246719643,\n", "   -0.00954828691,\n", "   0.0184670389,\n", "   -0.0129678892,\n", "   0.0145515362,\n", "   -0.0214029867,\n", "   0.0144481948,\n", "   0.00607730262,\n", "   -0.0126747256,\n", "   0.044834014,\n", "   0.0196376219,\n", "   -0.0315311216,\n", "   -0.0288124643,\n", "   0.018206086,\n", "   0.00396398036,\n", "   0.0213972349,\n", "   -0.008110594,\n", "   0.0451966487,\n", "   -0.0237775613,\n", "   0.0157476552,\n", "   -0.000909935567,\n", "   -0.0050676954,\n", "   -0.00766079593,\n", "   0.0390481614,\n", "   -0.0105713699,\n", "   -0.0548735633,\n", "   0.017537985,\n", "   0.00530012092,\n", "   0.0173802599,\n", "   -0.0298924614,\n", "   0.0230456665,\n", "   0.0178775154,\n", "   -0.00770055503,\n", "   0.0226256922,\n", "   -0.0299092792,\n", "   -0.0199440084,\n", "   -0.0116450097,\n", "   -0.0215494074,\n", "   -0.0320700444,\n", "   -0.0342683084,\n", "   0.00723196799,\n", "   -0.0165118482,\n", "   -0.0166910067,\n", "   0.0454619452,\n", "   0.00705005787,\n", "   0.0714524686,\n", "   -0.000222492905,\n", "   -0.0337360501,\n", "   -0.000187860249,\n", "   0.00709090475,\n", "   0.022463724,\n", "   0.0229537878,\n", "   -0.00718946336,\n", "   0.0104867127,\n", "   0.017463401,\n", "   -0.0465997979,\n", "   0.0536884516,\n", "   0.0796399787,\n", "   -0.00383709255,\n", "   -0.00873902626,\n", "   0.0193543453,\n", "   -0.0037406187,\n", "   0.0281554889,\n", "   -0.00156906561,\n", "   -0.167881176,\n", "   0.00105823344,\n", "   -0.0155822756,\n", "   0.0221844018,\n", "   -0.0301396083,\n", "   0.0148541247,\n", "   -0.000563806505,\n", "   -0.0144323539,\n", "   -0.00254240725,\n", "   0.0347642303,\n", "   0.0160884336,\n", "   0.0205961838,\n", "   0.0174751505,\n", "   0.00708184252,\n", "   -0.013084596,\n", "   0.0103551848,\n", "   0.0403096117,\n", "   0.00920574274,\n", "   -0.00813520793,\n", "   -0.00463530701,\n", "   -0.0351092853,\n", "   -0.021472482,\n", "   -0.00224860525,\n", "   -0.0228237119,\n", "   -0.0165243689,\n", "   0.00682210131,\n", "   -0.00435310416,\n", "   -0.00811636169,\n", "   0.0310548618,\n", "   0.0244222116,\n", "   -0.0475054085,\n", "   0.00432174886,\n", "   -0.00835442,\n", "   -0.0110724429,\n", "   0.0221319757,\n", "   -0.0271862764,\n", "   0.014030166,\n", "   -0.0178680811,\n", "   0.00456344476,\n", "   0.00195796671,\n", "   -0.0472551845,\n", "   -0.0200979058,\n", "   0.00530214049,\n", "   0.0029779,\n", "   -0.0358144231,\n", "   0.0139577212,\n", "   0.0173011385,\n", "   -0.00157337473,\n", "   0.0147398748,\n", "   0.0136530539,\n", "   0.00292634987,\n", "   0.0106391693,\n", "   0.0322179832,\n", "   -0.0166153368,\n", "   0.017330179,\n", "   -0.00522930734,\n", "   0.0268919,\n", "   0.00638089236,\n", "   -0.0150889242,\n", "   -0.00424932363,\n", "   0.000331066549,\n", "   -0.000618944527,\n", "   0.0336502902,\n", "   -0.0556718595,\n", "   0.0182192307,\n", "   -0.0129228933,\n", "   0.0227786563,\n", "   -0.0290831141,\n", "   0.00693926448,\n", "   -0.0311172642,\n", "   0.0256284177,\n", "   -0.0388916582,\n", "   0.000109155269,\n", "   -0.0104270019,\n", "   0.0546073541,\n", "   -0.00881886,\n", "   0.0293868892,\n", "   -0.0468549877,\n", "   0.016199315,\n", "   0.0270225871,\n", "   0.0788640454,\n", "   0.00234043179,\n", "   -0.0362866558,\n", "   0.00781863928,\n", "   0.0653242767,\n", "   0.0180277806,\n", "   -0.0238778107,\n", "   -0.0275868103,\n", "   0.00356073747,\n", "   0.0192475487,\n", "   -0.0168723464,\n", "   0.0256694909,\n", "   0.0599225238,\n", "   -0.00763517432,\n", "   0.0376911163,\n", "   0.0185188241,\n", "   -0.0279494599,\n", "   0.00634861365,\n", "   -0.0376207344,\n", "   -0.0694084913,\n", "   -0.00407687575,\n", "   0.0168324821,\n", "   -0.0092662992,\n", "   0.000385182531,\n", "   0.0276273657,\n", "   -0.0370098799,\n", "   -0.0329841487,\n", "   -0.0286797415,\n", "   0.0104206121,\n", "   0.000333215692,\n", "   -0.0574902743,\n", "   -0.0142486328,\n", "   -0.0120474547,\n", "   -0.0180014502,\n", "   -0.0216617212,\n", "   -0.00946220476,\n", "   0.0337501056,\n", "   0.0212933253,\n", "   0.0113687087,\n", "   -0.000925776374,\n", "   -0.032990139,\n", "   -0.000618938415,\n", "   0.000993509893,\n", "   0.00282027829,\n", "   -0.0200876184,\n", "   0.0457909442,\n", "   -0.0105017712,\n", "   -0.0282768086,\n", "   -0.05004742,\n", "   -0.00227624015,\n", "   -0.0243141018,\n", "   0.0227768626,\n", "   -0.0257111564,\n", "   -0.0167320073,\n", "   0.023399394,\n", "   0.00910695642,\n", "   -0.026271984,\n", "   -0.00228538178,\n", "   0.0107177375,\n", "   -0.0152573716,\n", "   0.00621477049,\n", "   0.0221151114,\n", "   0.00333770877,\n", "   -0.0264165718,\n", "   -0.0429394543,\n", "   0.0186703112,\n", "   0.0264471918,\n", "   0.0129693644,\n", "   -0.0226066746,\n", "   -0.0338374227,\n", "   0.0573786274,\n", "   -0.0300479271,\n", "   0.0680657178,\n", "   -0.00449167797,\n", "   -0.00625818362,\n", "   0.0571237355,\n", "   -0.0109123262,\n", "   -0.0388713814,\n", "   0.0101079978,\n", "   -0.0168529786,\n", "   -0.00278022862,\n", "   0.000276387,\n", "   -0.00325911981,\n", "   -0.0280847214,\n", "   -0.0152892359,\n", "   0.00970323849,\n", "   -0.022443369,\n", "   -0.0233574919,\n", "   -0.0177765321,\n", "   0.0183294024,\n", "   0.033658471,\n", "   0.0112211453,\n", "   0.0269369408,\n", "   -0.0404001661,\n", "   0.0091337,\n", "   0.00648069102,\n", "   0.0264417101,\n", "   0.00131391431,\n", "   0.0349151343,\n", "   0.00186032965,\n", "   -0.00948848762,\n", "   -0.007606728,\n", "   -0.00191481505,\n", "   -0.0020366488,\n", "   0.0144945085,\n", "   -0.0229131952,\n", "   0.0583214127,\n", "   -0.0353577,\n", "   0.0324712507,\n", "   -0.00680477731,\n", "   0.029601343,\n", "   0.00809292588,\n", "   0.00189606007,\n", "   0.0125348633,\n", "   -0.00308805355,\n", "   -0.0467124246,\n", "   -0.0327143967,\n", "   -0.00663435366,\n", "   -0.00500002131,\n", "   0.00151038403,\n", "   0.0081403479,\n", "   -0.0506082475,\n", "   -0.0345982499,\n", "   -0.0405773409,\n", "   -0.00281901937,\n", "   0.0222548116,\n", "   0.0203694534,\n", "   0.0121873431,\n", "   -0.00238838443,\n", "   0.0340157151,\n", "   -0.00585057773,\n", "   0.00460229395,\n", "   -0.0136690158,\n", "   -0.00412545446,\n", "   0.00569216069,\n", "   0.0981723443,\n", "   0.00975064281,\n", "   -0.0187239852,\n", "   -0.00772301201,\n", "   0.00978999306,\n", "   0.00770796929,\n", "   0.0052227159,\n", "   -0.0364563651,\n", "   0.0199458748,\n", "   -0.021180477,\n", "   0.00333561515,\n", "   -0.0173078589,\n", "   0.0165160224,\n", "   0.0330334082,\n", "   -0.00277401973,\n", "   -0.0226696096,\n", "   -0.00859527197,\n", "   0.0157417282,\n", "   -0.0519393943,\n", "   0.00811555143,\n", "   -0.00119101489,\n", "   -0.0419302918,\n", "   0.00469629327,\n", "   -0.00985144544,\n", "   0.000344844942,\n", "   0.011995445,\n", "   0.0156682879,\n", "   -0.0191581901,\n", "   -0.0327879936,\n", "   -0.00174434844,\n", "   -0.0232716,\n", "   0.0413746424,\n", "   -0.0281293094,\n", "   0.0124663273,\n", "   0.021132214,\n", "   0.0117070181,\n", "   -0.0254273303,\n", "   ...],\n", "  '$similarity': 0.91428196},\n", " {'_id': 10,\n", "  'name': 'Filter 1 Cup 50mm',\n", "  'image': 'https://www.breville.com/content/dam/breville/au/catalog/products/images/sp0/sp0003230/tile.jpg',\n", "  'url': 'https://www.breville.com/us/en/parts-accessories/parts/sp0003230.html?sku=SP0003230',\n", "  'price': '12.95',\n", "  '$vector': [-0.0010560978,\n", "   -0.0431940332,\n", "   0.0136121009,\n", "   0.0263081584,\n", "   0.00125906884,\n", "   -0.0114325806,\n", "   0.00988424569,\n", "   -0.0379222743,\n", "   -0.035005521,\n", "   -0.00637047,\n", "   0.00328597822,\n", "   -0.0269535985,\n", "   -0.00206734636,\n", "   0.143738538,\n", "   -0.0381479,\n", "   0.0236927848,\n", "   0.0564847738,\n", "   -0.00301328325,\n", "   -0.021547053,\n", "   -0.00583824236,\n", "   -0.00753126573,\n", "   -0.00549318083,\n", "   -0.0264043827,\n", "   -0.015405762,\n", "   -0.00152926904,\n", "   0.00619565975,\n", "   -0.0116193034,\n", "   -0.0259707887,\n", "   0.0625111163,\n", "   -0.0350108072,\n", "   -0.00463276776,\n", "   -0.00734956143,\n", "   0.0149143776,\n", "   -0.00478663808,\n", "   0.0162652675,\n", "   -0.0204267278,\n", "   -0.00925257336,\n", "   0.0195609666,\n", "   0.00516383257,\n", "   -0.00905659,\n", "   -0.0201603658,\n", "   -0.0158579554,\n", "   -0.0315382145,\n", "   -0.0237132367,\n", "   0.0430171862,\n", "   -0.0206457432,\n", "   -0.0155891348,\n", "   -0.000952589,\n", "   0.00396825094,\n", "   -0.0039271987,\n", "   -0.0305131245,\n", "   -0.0190763231,\n", "   0.0067038415,\n", "   -0.0303528961,\n", "   0.0220818054,\n", "   -0.00504151,\n", "   -0.0328027345,\n", "   -0.0203116164,\n", "   0.0536186509,\n", "   -0.0330883302,\n", "   0.0212096591,\n", "   -0.0102613214,\n", "   0.0138666527,\n", "   -0.0301209614,\n", "   0.000291181117,\n", "   0.0190858878,\n", "   0.0166939199,\n", "   0.000727185863,\n", "   -0.0124818599,\n", "   -0.0262704957,\n", "   -0.00721699046,\n", "   -0.030688785,\n", "   -0.0237736329,\n", "   0.0190103967,\n", "   -0.0305122491,\n", "   0.00495687546,\n", "   -0.0496739186,\n", "   0.00646758871,\n", "   -0.014495885,\n", "   -0.0358845033,\n", "   0.035339728,\n", "   0.000541316695,\n", "   -0.00213617878,\n", "   -0.00533506693,\n", "   0.00592615362,\n", "   0.019027466,\n", "   -0.00205068337,\n", "   -0.0201536193,\n", "   -0.0571009368,\n", "   0.027164869,\n", "   0.0522765331,\n", "   0.0310845859,\n", "   -0.00932269264,\n", "   -0.219696909,\n", "   -0.0244725682,\n", "   -0.0764894634,\n", "   -0.0428297445,\n", "   0.00635613129,\n", "   0.0227158,\n", "   -0.0466018431,\n", "   -0.00564558804,\n", "   -0.00921120215,\n", "   0.017298596,\n", "   0.0228692945,\n", "   -0.0609436557,\n", "   0.00554603757,\n", "   -0.0166489072,\n", "   -0.036725,\n", "   -0.0143255349,\n", "   -0.0305648968,\n", "   -0.0271283668,\n", "   0.0245434381,\n", "   0.00425564451,\n", "   -0.0157661084,\n", "   0.00669785123,\n", "   -0.0124906991,\n", "   -0.0226280969,\n", "   0.0055794064,\n", "   -0.0179004408,\n", "   0.0449699312,\n", "   -0.0213026721,\n", "   -0.0162545014,\n", "   -0.00491117081,\n", "   -0.00722792605,\n", "   -0.047530897,\n", "   -0.0818941593,\n", "   -0.00762769114,\n", "   -0.0374398828,\n", "   -0.00697832275,\n", "   -0.00725573581,\n", "   0.0339209139,\n", "   0.0240065772,\n", "   0.0173068065,\n", "   -0.0295771398,\n", "   0.0271837562,\n", "   0.0131476307,\n", "   0.00640849629,\n", "   -0.00156119291,\n", "   0.0261859521,\n", "   0.00592331681,\n", "   0.00927404594,\n", "   0.0166795235,\n", "   -0.00685063098,\n", "   -0.039373666,\n", "   0.0259271674,\n", "   -0.0281596519,\n", "   -0.00632843142,\n", "   -0.0194935575,\n", "   -0.0387589782,\n", "   -0.0431845188,\n", "   -0.0309930053,\n", "   -0.0463168137,\n", "   -0.040020749,\n", "   0.0150622427,\n", "   0.0113571212,\n", "   -0.00494674407,\n", "   0.02025274,\n", "   0.0216226671,\n", "   -0.0102921845,\n", "   -0.0133495731,\n", "   0.00384576782,\n", "   -0.0198901,\n", "   -0.0326414891,\n", "   0.00424213428,\n", "   0.00733064301,\n", "   -0.00438431464,\n", "   -0.0185281411,\n", "   -0.0222789701,\n", "   0.0041571646,\n", "   -0.00235260557,\n", "   0.0282675773,\n", "   0.0553543568,\n", "   0.0415179171,\n", "   -0.0124299703,\n", "   0.0747422278,\n", "   -0.0155519126,\n", "   -0.0117722126,\n", "   -0.00965590309,\n", "   0.00719360774,\n", "   -0.0300525315,\n", "   0.00946734753,\n", "   0.0069967676,\n", "   -0.0533619,\n", "   0.0116908643,\n", "   0.0103714876,\n", "   0.0437677205,\n", "   -0.0267576445,\n", "   0.000584997411,\n", "   0.0161054488,\n", "   0.00209029671,\n", "   0.00441647926,\n", "   -0.0262720268,\n", "   0.00443519186,\n", "   0.00596780423,\n", "   0.0162881427,\n", "   0.00806925818,\n", "   -0.0138468603,\n", "   -0.0130764144,\n", "   0.0129050296,\n", "   -0.00508339889,\n", "   0.0254556723,\n", "   -0.0245984979,\n", "   0.0229452979,\n", "   -0.00643870421,\n", "   0.00913858321,\n", "   0.0280596316,\n", "   0.0188452154,\n", "   -0.00914563704,\n", "   0.000598574523,\n", "   0.0123123592,\n", "   0.021285722,\n", "   -0.0242919363,\n", "   0.0205865074,\n", "   0.0406568274,\n", "   0.00382283027,\n", "   0.0154386517,\n", "   -0.00411803043,\n", "   -0.0193736497,\n", "   -0.0260152277,\n", "   0.0157712027,\n", "   -0.0136272423,\n", "   0.0317909904,\n", "   -0.0110233873,\n", "   -0.0110462243,\n", "   0.0169113334,\n", "   -0.00311060832,\n", "   0.0184051394,\n", "   0.0478113703,\n", "   -0.0448283479,\n", "   0.0184963793,\n", "   -0.00780671276,\n", "   0.000627400354,\n", "   0.00817193184,\n", "   -0.0468107425,\n", "   -0.0186659936,\n", "   -0.0170759577,\n", "   5.65021255e-05,\n", "   0.0127869984,\n", "   0.0201196019,\n", "   -0.051659964,\n", "   0.0365497,\n", "   -0.0144818528,\n", "   0.0099030789,\n", "   -0.0593859665,\n", "   0.00387149886,\n", "   -0.0155124478,\n", "   0.00775774429,\n", "   0.0114011541,\n", "   -0.0094009852,\n", "   -0.0086163152,\n", "   0.016897548,\n", "   0.0323677212,\n", "   0.0100876484,\n", "   -0.0115898708,\n", "   -0.0473594777,\n", "   -0.0206971485,\n", "   -0.000362439081,\n", "   -0.012756411,\n", "   0.0154875582,\n", "   -0.0143346526,\n", "   0.0119750053,\n", "   -0.0416958444,\n", "   0.00632204581,\n", "   -0.00385508221,\n", "   0.00783918239,\n", "   0.0129411323,\n", "   0.038810689,\n", "   0.008211229,\n", "   0.0138904518,\n", "   0.0253699832,\n", "   -0.0401998051,\n", "   0.000102468097,\n", "   -0.00842131395,\n", "   0.00581990695,\n", "   -0.00743973954,\n", "   -0.0156126767,\n", "   -0.0105340071,\n", "   0.000921529892,\n", "   0.00838290062,\n", "   0.0877981633,\n", "   -0.0111368168,\n", "   0.00992348418,\n", "   -0.00505884644,\n", "   -0.00477638142,\n", "   0.0118243797,\n", "   0.0167679228,\n", "   -0.162249371,\n", "   -0.0241233576,\n", "   -0.0171382353,\n", "   -0.0010630145,\n", "   -0.0289365426,\n", "   -0.0377950817,\n", "   -0.0195378903,\n", "   -0.0315448307,\n", "   -0.0234409589,\n", "   -0.00526268734,\n", "   0.0324956514,\n", "   -0.0408315249,\n", "   -0.00390899787,\n", "   0.00978709292,\n", "   0.0064356639,\n", "   0.00244128983,\n", "   0.00657224795,\n", "   -0.0278560836,\n", "   -0.0222843364,\n", "   0.010674,\n", "   -0.00215384574,\n", "   0.0288694277,\n", "   -0.00912363827,\n", "   -0.00352261099,\n", "   0.0195972156,\n", "   0.0235041641,\n", "   -0.0149932336,\n", "   0.0380596928,\n", "   0.00776447682,\n", "   -0.0252892803,\n", "   0.00495557487,\n", "   -0.0139161786,\n", "   -0.00648087217,\n", "   0.0619094931,\n", "   0.0364690647,\n", "   0.021518698,\n", "   0.0424545,\n", "   -0.00458903657,\n", "   -0.00157011976,\n", "   -0.0350499749,\n", "   -0.0177561883,\n", "   -0.0218191911,\n", "   0.0235485192,\n", "   0.0248831436,\n", "   -0.0438671634,\n", "   0.0155964606,\n", "   0.00227504293,\n", "   -0.0473306701,\n", "   -0.0488775298,\n", "   -0.0377110839,\n", "   -0.0165745039,\n", "   -0.00190511544,\n", "   -0.00966971181,\n", "   -0.0130097531,\n", "   -0.0486290641,\n", "   0.03444013,\n", "   -0.0126707079,\n", "   -0.0208592433,\n", "   0.0188527312,\n", "   0.0451475531,\n", "   -0.0304909293,\n", "   0.00098316,\n", "   -0.00696801301,\n", "   -0.00690515572,\n", "   0.0336549096,\n", "   0.00590293296,\n", "   -0.0268561076,\n", "   0.0201844573,\n", "   -0.0307493377,\n", "   -0.010825892,\n", "   -0.00179326732,\n", "   0.0566668957,\n", "   -0.0150954481,\n", "   -0.0119819921,\n", "   -0.00756422617,\n", "   0.00227373256,\n", "   -0.00936855748,\n", "   0.00630122703,\n", "   0.00355366454,\n", "   0.000395705953,\n", "   -0.0235086754,\n", "   -0.0666838065,\n", "   -0.0324830674,\n", "   0.0120624881,\n", "   0.0338900648,\n", "   -0.00197713287,\n", "   0.0276625454,\n", "   -0.0136289475,\n", "   0.0199338123,\n", "   -0.0408178382,\n", "   0.0245183166,\n", "   -0.0187071264,\n", "   -0.00893385801,\n", "   -0.00412566401,\n", "   -0.0189303476,\n", "   -0.00688123,\n", "   0.00520076649,\n", "   0.00317758531,\n", "   0.027724823,\n", "   -0.00173423381,\n", "   -0.0067525208,\n", "   0.02038824,\n", "   -0.00814495515,\n", "   -0.0072740661,\n", "   -0.00863050763,\n", "   0.0445601381,\n", "   0.018075943,\n", "   -0.00111842074,\n", "   -0.00422274275,\n", "   0.0305270031,\n", "   0.00181035092,\n", "   -0.00163682771,\n", "   0.0139120845,\n", "   0.00109249214,\n", "   0.00136604859,\n", "   0.0325497836,\n", "   -0.0180487409,\n", "   0.0034954783,\n", "   -0.0240796674,\n", "   0.00291730696,\n", "   0.0141789922,\n", "   -0.0174475722,\n", "   -0.0264751,\n", "   0.0121857915,\n", "   -0.0419326872,\n", "   0.0107469875,\n", "   0.00462575164,\n", "   0.0199854616,\n", "   0.0249810349,\n", "   0.0162467789,\n", "   -0.00664552487,\n", "   0.0162220839,\n", "   -0.0348092727,\n", "   0.0205889512,\n", "   0.00328978617,\n", "   0.00613427814,\n", "   0.00245865923,\n", "   -0.025169678,\n", "   -0.0439971164,\n", "   -0.00285677286,\n", "   0.0306561887,\n", "   -0.0392882191,\n", "   -0.0545563437,\n", "   -0.0188341588,\n", "   -0.035133861,\n", "   -0.0379892364,\n", "   0.0154832499,\n", "   0.00792219397,\n", "   0.0205268785,\n", "   0.0030584773,\n", "   -0.0116740894,\n", "   -0.0402049944,\n", "   -0.023190219,\n", "   -0.018266432,\n", "   -0.0076479027,\n", "   -0.0451918878,\n", "   -0.0411415771,\n", "   0.0214124676,\n", "   -0.0358313583,\n", "   -0.00981262419,\n", "   -0.026762912,\n", "   -0.0131733641,\n", "   -0.00762919569,\n", "   0.0112939291,\n", "   -0.00483849226,\n", "   0.0424217507,\n", "   -0.0132944593,\n", "   -0.0308691133,\n", "   -0.0289582,\n", "   -0.00775289396,\n", "   -0.0201041233,\n", "   0.00178576587,\n", "   -0.0182388537,\n", "   -0.0277285706,\n", "   -0.027507117,\n", "   0.00352106942,\n", "   0.0253178123,\n", "   -0.017054718,\n", "   -0.00242614234,\n", "   -0.00465876795,\n", "   0.00199001888,\n", "   -0.0187465344,\n", "   0.0113913175,\n", "   -0.00858615804,\n", "   0.00594027108,\n", "   -0.00741987489,\n", "   -0.0193431415,\n", "   0.0106287692,\n", "   -0.0207499135,\n", "   -0.00319077028,\n", "   -0.00857528858,\n", "   -0.0144694112,\n", "   0.0178784952,\n", "   -0.0071099,\n", "   -0.00381359062,\n", "   -0.0278196763,\n", "   -0.0159833822,\n", "   0.0189095438,\n", "   -0.0100163361,\n", "   -0.02293556,\n", "   0.0113233039,\n", "   -0.0503345393,\n", "   0.022363022,\n", "   -0.00320849521,\n", "   -0.0348827951,\n", "   0.0282731149,\n", "   0.0123412358,\n", "   0.00960870087,\n", "   0.00500811683,\n", "   -0.0200769603,\n", "   -0.000259322667,\n", "   0.000211073464,\n", "   0.00966225937,\n", "   0.0166920889,\n", "   -0.00884962734,\n", "   -0.0173769165,\n", "   0.00873366371,\n", "   -0.0116476044,\n", "   -0.0298118033,\n", "   -0.0293862093,\n", "   -0.00956131425,\n", "   -0.0339057185,\n", "   0.00228180503,\n", "   0.0111191208,\n", "   -0.0278905146,\n", "   -0.0207242668,\n", "   0.00373020349,\n", "   -0.00159209967,\n", "   0.00784943346,\n", "   0.00459582638,\n", "   0.0211983789,\n", "   -0.0341036841,\n", "   0.0193638187,\n", "   -0.00816904474,\n", "   0.00456541497,\n", "   0.00610890519,\n", "   -0.0214701872,\n", "   0.0320867039,\n", "   0.044937782,\n", "   0.00104619097,\n", "   0.019170532,\n", "   0.0302462019,\n", "   -0.00411139429,\n", "   0.0109368591,\n", "   0.0171889756,\n", "   -0.0359326936,\n", "   -0.0069037443,\n", "   -0.0134794731,\n", "   -0.00784303,\n", "   -0.0399835929,\n", "   -0.0159206651,\n", "   -0.0211202148,\n", "   -0.00209117983,\n", "   0.00718762,\n", "   0.0053263274,\n", "   0.00868510082,\n", "   -0.0167556293,\n", "   -0.00136312644,\n", "   -0.0599381626,\n", "   0.0249304902,\n", "   -0.00170108629,\n", "   0.00768304337,\n", "   0.0266090147,\n", "   -0.0303139593,\n", "   -0.0219138134,\n", "   -0.00039126788,\n", "   -0.0152947726,\n", "   0.00150367455,\n", "   0.0604137145,\n", "   0.0157075301,\n", "   -0.0264842175,\n", "   -0.00175026024,\n", "   -0.000194107459,\n", "   -0.0202024486,\n", "   -0.00552256,\n", "   -0.0381140187,\n", "   -0.0225731581,\n", "   -0.00676404126,\n", "   0.000712177,\n", "   -0.0049591423,\n", "   -0.0433722846,\n", "   0.0177357011,\n", "   0.0157587156,\n", "   -0.0237409547,\n", "   0.000857150648,\n", "   0.0122604379,\n", "   -0.00950681325,\n", "   0.00769660901,\n", "   -0.0266242307,\n", "   -0.0497770682,\n", "   -0.0159810074,\n", "   0.0283428375,\n", "   -0.0205763206,\n", "   -0.00780584337,\n", "   -0.00330296624,\n", "   0.0188798085,\n", "   0.00305112801,\n", "   0.00916700345,\n", "   -0.0138339559,\n", "   -0.018628953,\n", "   -0.0314400941,\n", "   -0.00268881861,\n", "   0.0354872644,\n", "   -0.0217045359,\n", "   0.00828506332,\n", "   0.0262941942,\n", "   0.00803411286,\n", "   7.60018083e-05,\n", "   -0.00169618649,\n", "   -0.0130316513,\n", "   -0.000185065524,\n", "   -0.0149332294,\n", "   0.00177708501,\n", "   -0.0166005976,\n", "   0.0214015208,\n", "   -0.0187755786,\n", "   0.0133048296,\n", "   -0.0114137633,\n", "   0.00683413,\n", "   -0.0855533406,\n", "   0.0373201184,\n", "   -0.0259042867,\n", "   0.0283757914,\n", "   0.0047982526,\n", "   0.0295737814,\n", "   0.00341688981,\n", "   -0.00377683109,\n", "   0.0278545264,\n", "   -0.0300274678,\n", "   0.00279626716,\n", "   -0.00462696236,\n", "   0.0115503538,\n", "   0.0106754107,\n", "   0.0199093893,\n", "   -0.00875928532,\n", "   -0.00657150941,\n", "   0.0283262283,\n", "   0.0378960259,\n", "   -0.00805937871,\n", "   0.031730894,\n", "   -0.0212332383,\n", "   -0.00835895352,\n", "   -0.000523101538,\n", "   -0.0203717984,\n", "   -0.0208118316,\n", "   0.012826845,\n", "   -0.0223803408,\n", "   -0.00740277255,\n", "   0.0313895792,\n", "   0.0328978,\n", "   0.012957314,\n", "   -0.0133856377,\n", "   0.0142258713,\n", "   -0.00427295547,\n", "   0.000295455888,\n", "   -0.0261694249,\n", "   0.0223786198,\n", "   -0.0030528754,\n", "   -0.0103802839,\n", "   -0.0265536439,\n", "   -0.0131079191,\n", "   0.0203061942,\n", "   -0.00208193832,\n", "   -0.0596476458,\n", "   0.0179038402,\n", "   0.013706808,\n", "   0.0201310907,\n", "   -0.0244476721,\n", "   0.00147714687,\n", "   -0.0110300686,\n", "   0.00680008158,\n", "   0.0268691666,\n", "   -0.00432429044,\n", "   -0.02947006,\n", "   -0.144537851,\n", "   -0.0287753735,\n", "   -0.0148012266,\n", "   -0.0423074923,\n", "   -0.0208130665,\n", "   -0.00281523797,\n", "   0.0233125817,\n", "   0.0282231942,\n", "   -0.0342379399,\n", "   -0.0163625218,\n", "   0.0460315756,\n", "   -0.00419721752,\n", "   -0.122708701,\n", "   -0.00184273138,\n", "   -0.0203165356,\n", "   0.00609564269,\n", "   0.0116810296,\n", "   -0.0236054454,\n", "   0.00324909645,\n", "   0.0287360679,\n", "   -0.0321660526,\n", "   0.0243424848,\n", "   0.0124349063,\n", "   0.00703955302,\n", "   0.0181250479,\n", "   -0.0184272863,\n", "   0.0157976132,\n", "   -0.0109608285,\n", "   0.0093174614,\n", "   -0.0182417631,\n", "   0.0109509537,\n", "   -0.00299141393,\n", "   -0.00909058191,\n", "   0.0508398563,\n", "   0.0224868804,\n", "   -0.0312844701,\n", "   -0.0356419384,\n", "   0.00883551314,\n", "   0.00208868063,\n", "   0.0187676027,\n", "   -0.0116544645,\n", "   0.0460222587,\n", "   -0.016127158,\n", "   0.0205501579,\n", "   -0.00526354555,\n", "   -0.0109948544,\n", "   -0.0130111733,\n", "   0.0514764525,\n", "   -0.00781252142,\n", "   -0.0646644756,\n", "   0.0208225474,\n", "   -0.00494619785,\n", "   0.0250245258,\n", "   -0.0318170339,\n", "   0.0188602898,\n", "   0.0201850161,\n", "   -0.00232687732,\n", "   0.0185016245,\n", "   -0.0297998413,\n", "   -0.0312440097,\n", "   -0.0145807704,\n", "   -0.0119312136,\n", "   -0.0348802954,\n", "   -0.0184999816,\n", "   0.00595783722,\n", "   -0.00585934194,\n", "   -0.015332358,\n", "   0.0385688283,\n", "   0.00691580679,\n", "   0.0589005053,\n", "   -0.00279411324,\n", "   -0.0195891652,\n", "   0.00475648,\n", "   0.00327643915,\n", "   0.0289297141,\n", "   0.0200339593,\n", "   -0.0147082647,\n", "   0.0123416139,\n", "   0.0134697184,\n", "   -0.0366640687,\n", "   0.0533261336,\n", "   0.0803186819,\n", "   -0.00656205276,\n", "   -0.00605005492,\n", "   0.0124711813,\n", "   -0.0081315767,\n", "   0.0206321124,\n", "   -0.0030021714,\n", "   -0.1705845,\n", "   -0.00813364796,\n", "   -0.0193716493,\n", "   0.0286949687,\n", "   -0.0233503,\n", "   0.0191884115,\n", "   0.0112302201,\n", "   -0.012582859,\n", "   -0.00803743396,\n", "   0.0316283293,\n", "   0.0183115266,\n", "   0.0214399956,\n", "   0.016507335,\n", "   0.0183243416,\n", "   -0.0153293936,\n", "   0.0132733211,\n", "   0.0403463058,\n", "   0.00280322297,\n", "   -0.0108670993,\n", "   -0.00756021077,\n", "   -0.0331268683,\n", "   -0.0237826779,\n", "   -0.00666622026,\n", "   -0.0161496568,\n", "   -0.00654235,\n", "   0.00114749197,\n", "   -0.00764772575,\n", "   -0.0090591982,\n", "   0.0314727165,\n", "   0.0168804117,\n", "   -0.0483855903,\n", "   -0.00307273958,\n", "   -0.00759517774,\n", "   -0.00671014888,\n", "   0.0211589746,\n", "   -0.0262225233,\n", "   0.0230467953,\n", "   -0.017517766,\n", "   0.0125888046,\n", "   -0.0106490115,\n", "   -0.0418133624,\n", "   -0.0225392096,\n", "   0.00388334855,\n", "   -0.0121985329,\n", "   -0.0164348427,\n", "   0.0246976856,\n", "   0.0256113894,\n", "   -0.00152366702,\n", "   0.00597348064,\n", "   0.0104017248,\n", "   0.00400691619,\n", "   0.00355356536,\n", "   0.0373968519,\n", "   -0.0178657304,\n", "   0.0116282664,\n", "   -0.00532681774,\n", "   0.025260793,\n", "   0.00502283638,\n", "   -0.0198532697,\n", "   -0.0029977269,\n", "   -0.00497955689,\n", "   0.00529074483,\n", "   0.0453885794,\n", "   -0.0370209329,\n", "   0.0170697533,\n", "   -0.0100122737,\n", "   0.0295297876,\n", "   -0.0356043614,\n", "   0.0108787147,\n", "   -0.0433758944,\n", "   0.011423423,\n", "   -0.0343356729,\n", "   -0.00278831576,\n", "   -0.0146304928,\n", "   0.0470688455,\n", "   -0.00824073,\n", "   0.033216428,\n", "   -0.0430045091,\n", "   0.0177206863,\n", "   0.0238432698,\n", "   0.0623814501,\n", "   0.00290399115,\n", "   -0.0326150209,\n", "   0.00718871877,\n", "   0.0742067844,\n", "   0.0178088397,\n", "   -0.0188850686,\n", "   -0.0215638652,\n", "   0.00159988517,\n", "   0.0191889685,\n", "   -0.0230602939,\n", "   0.0183076691,\n", "   0.0676314384,\n", "   -0.000998692703,\n", "   0.0359023288,\n", "   0.0160771087,\n", "   -0.0331870429,\n", "   -0.00207172614,\n", "   -0.037376307,\n", "   -0.0721597895,\n", "   0.000533259299,\n", "   0.0197661258,\n", "   -0.00152686157,\n", "   -0.00482262531,\n", "   0.0263135768,\n", "   -0.0172653254,\n", "   -0.0372055732,\n", "   -0.0367444083,\n", "   0.0100519387,\n", "   -0.00100016629,\n", "   -0.0626090765,\n", "   -0.01515665,\n", "   -0.0079502482,\n", "   -0.017942002,\n", "   -0.0235005971,\n", "   -0.00822161883,\n", "   0.0250923671,\n", "   0.0280678812,\n", "   0.0130168563,\n", "   -0.00421592221,\n", "   -0.0327812023,\n", "   0.0102129802,\n", "   0.000291197532,\n", "   0.00761556718,\n", "   -0.0255444963,\n", "   0.0548792705,\n", "   -0.0159027614,\n", "   -0.0335704088,\n", "   -0.0449844152,\n", "   -0.00930746831,\n", "   -0.0201939866,\n", "   0.0185878184,\n", "   -0.0239286423,\n", "   -0.0189098679,\n", "   0.0114655802,\n", "   0.0217901878,\n", "   -0.0271013211,\n", "   -0.00583981303,\n", "   0.00647946447,\n", "   -0.0106057627,\n", "   0.00867015123,\n", "   0.0219815,\n", "   -0.003389752,\n", "   -0.0151731614,\n", "   -0.0330219604,\n", "   0.0168551411,\n", "   0.014461454,\n", "   0.0213393252,\n", "   -0.0175209027,\n", "   -0.0357248075,\n", "   0.0661680847,\n", "   -0.0327865258,\n", "   0.0754200742,\n", "   -0.00771361776,\n", "   -0.011175761,\n", "   0.0588043742,\n", "   -0.0169346761,\n", "   -0.0492652878,\n", "   0.013595893,\n", "   -0.0174481068,\n", "   0.00271067838,\n", "   -0.00388018182,\n", "   0.00541552063,\n", "   -0.0255229473,\n", "   0.00294444361,\n", "   0.00535813812,\n", "   -0.0295684636,\n", "   -0.017746672,\n", "   -0.0246932525,\n", "   0.0180981308,\n", "   0.0372068845,\n", "   0.00525231194,\n", "   0.0245496351,\n", "   -0.0317674913,\n", "   0.0142562594,\n", "   0.00690105604,\n", "   0.0168513134,\n", "   -0.0179171488,\n", "   0.0308320764,\n", "   0.00278372248,\n", "   -0.0150601082,\n", "   -0.0102740573,\n", "   -0.00629186025,\n", "   -0.00129610032,\n", "   0.0184466951,\n", "   -0.0161451548,\n", "   0.0540568717,\n", "   -0.0252752658,\n", "   0.0301058386,\n", "   -0.00740714371,\n", "   0.0161165893,\n", "   -0.00329922978,\n", "   0.00290528685,\n", "   0.00995152816,\n", "   -0.0133705009,\n", "   -0.052840054,\n", "   -0.028885467,\n", "   -0.0114273569,\n", "   -0.00969977118,\n", "   0.00255211163,\n", "   0.00996570103,\n", "   -0.0374664553,\n", "   -0.0281325299,\n", "   -0.0495278537,\n", "   -0.0118557643,\n", "   0.0322747119,\n", "   0.0174083542,\n", "   0.0126586771,\n", "   0.00389197934,\n", "   0.0342441797,\n", "   0.00165207835,\n", "   -0.00240378012,\n", "   -0.0115319286,\n", "   0.00104002189,\n", "   0.0121228695,\n", "   0.0982072949,\n", "   0.0136225633,\n", "   -0.0158295725,\n", "   0.00121267571,\n", "   0.0117555456,\n", "   0.0148887718,\n", "   -0.00808157213,\n", "   -0.0252978224,\n", "   0.0200615954,\n", "   -0.0152765317,\n", "   0.00338651612,\n", "   -0.0145241357,\n", "   0.0208590943,\n", "   0.0347241126,\n", "   -0.0048776716,\n", "   -0.0175328199,\n", "   -0.00318263564,\n", "   0.0197269917,\n", "   -0.0562760532,\n", "   0.00649626134,\n", "   -0.00291036372,\n", "   -0.0517733619,\n", "   0.0109861381,\n", "   -0.00995235518,\n", "   0.00666763354,\n", "   0.0116654374,\n", "   0.0113993464,\n", "   -0.0254605822,\n", "   -0.0364729129,\n", "   -0.000405102532,\n", "   -0.0234982148,\n", "   0.0336386077,\n", "   -0.0191729125,\n", "   0.0108480621,\n", "   0.016578069,\n", "   -0.0042908336,\n", "   -0.0242968202,\n", "   ...],\n", "  '$similarity': 0.9116895}]"]}, "metadata": {}, "execution_count": 44}]}, {"cell_type": "code", "source": ["related_products_csv = \"name, image, price, url\\n\"\n", "for doc in documents:\n", "  related_products_csv += f\"{doc['name']}, {doc['image']}, {doc['price']}, {doc['url']},\\n\""], "metadata": {"id": "4eTwAQKH3yD6"}, "id": "4eTwAQKH3yD6", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(related_products_csv)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "T-A4o7wIrmTj", "outputId": "020b73b5-5520-4c00-92b0-af67b6d83f55"}, "id": "T-A4o7wIrmTj", "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["name, image, price, url\n", "Filter 2 Cup Dual Wall 50mm, https://www.breville.com/content/dam/breville/au/catalog/products/images/sp0/sp0003231/tile.jpg, 12.95, https://www.breville.com/us/en/parts-accessories/parts/sp0003231.html?sku=SP0003231,\n", "Filter 2 Cup 50mm, https://www.breville.com/content/dam/breville/us/catalog/products/images/sp0/sp0000166/tile.jpg, 11.95, https://www.breville.com/us/en/parts-accessories/parts/sp0000166.html?sku=SP0000166,\n", "Filter 1 Cup 50mm, https://www.breville.com/content/dam/breville/au/catalog/products/images/sp0/sp0003230/tile.jpg, 12.95, https://www.breville.com/us/en/parts-accessories/parts/sp0003230.html?sku=SP0003230,\n", "\n"]}]}, {"cell_type": "code", "source": ["image_message = {\n", "    \"type\": \"image_url\",\n", "    \"image_url\": {\"url\": \"/content/coffee_maker_part.png\"},\n", "}\n", "text_message = {\n", "    \"type\": \"text\",\n", "    \"text\": f\"What we have in this image? Share the URL and price to purchase a replacement. Here are related products {related_products_csv}\",\n", "}"], "metadata": {"id": "Li-fX8pz30kz"}, "id": "Li-fX8pz30kz", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["message = HumanMessage(content=[text_message, image_message])\n"], "metadata": {"id": "57KzUhbd4B2e"}, "id": "57KzUhbd4B2e", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chat = ChatVertexAI(model_name=\"gemini-1.0-pro-vision\",safety_settings={\n", "        HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_LOW_AND_ABOVE\n", "    },)"], "metadata": {"id": "Q7_Ktwg7tBTR"}, "id": "Q7_Ktwg7tBTR", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["output = chat([message])"], "metadata": {"id": "opNLdOPw4DTk"}, "id": "opNLdOPw4DTk", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(output.content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "rUDI6iZyY-yc", "outputId": "41ffd1bf-68eb-4a74-c78d-a2367da381a1"}, "id": "rUDI6iZyY-yc", "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": [" The image shows a Breville 50mm filter basket. It can be purchased for $12.95 from the Breville website.\n", "\n", "Related products:\n", "Filter 2 Cup Dual Wall 50mm, https://www.breville.com/content/dam/breville/au/catalog/products/images/sp0/sp0003231/tile.jpg, 12.95, https://www.breville.com/us/en/parts-accessories/parts/sp0003231.html?sku=SP0003231,\n", "Filter 2 Cup 50mm, https://www.breville.com/content/dam/breville/us/catalog/products/images/sp0/sp0000166/tile.jpg, 11.95, https://www.breville.com/us/en/parts-accessories/parts/sp0000166.html?sku=SP0000166,\n", "Filter 1 Cup 50mm, https://www.breville.com/content/dam/breville/au/catalog/products/images/sp0/sp0003230/tile.jpg, 12.95, https://www.breville.com/us/en/parts-accessories/parts/sp0003230.html?sku=SP0003230,\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "SWqUjjMMWWfH"}, "id": "SWqUjjMMWWfH", "execution_count": null, "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}, "colab": {"provenance": [], "gpuType": "T4", "include_colab_link": true}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 5}