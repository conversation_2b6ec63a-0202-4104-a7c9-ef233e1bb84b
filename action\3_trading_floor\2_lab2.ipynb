{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Lab 2\n", "\n", "We're about to create and use our own MCP Server and MCP Client!\n", "\n", "It's pretty simple, but it's not super-simple. The excitment around MCP is about how easy it is to share and use other MCP Servers - making our own does involve a bit of work.\n", "\n", "## First, looking at `accounts.py`\n", "\n", "Let's review some python code made mostly by a hard-working Engineering Team."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "from agents import Agent, Runner, trace\n", "from agents.mcp import MCPServerStdio\n", "from IPython.display import display, Markdown\n", "\n", "load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from accounts import Account"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["account = Account.get(\"Ed\")\n", "account"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["account.buy_shares(\"AMZN\", 1, \"Because this bookstore website looks promising\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["account.report()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["account.list_transactions()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Now we make an MCP server: `accounts_server.py`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Now let's use our accounts server as an MCP server\n", "\n", "params = {\"command\": \"uv\", \"args\": [\"run\", \"accounts_server.py\"]}\n", "async with MCPServerStdio(params=params) as server:\n", "    mcp_tools = await server.list_tools()\n", "\n", "mcp_tools"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["instructions = \"You are able to manage an account for a client, and answer questions about the account.\"\n", "request = \"My name is <PERSON> and my account is under the name <PERSON>. What's my balance and my holdings?\"\n", "model = \"gpt-4o-mini\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "async with MCPServerStdio(params=params) as mcp_server:\n", "    agent = Agent(name=\"account_manager\", instructions=instructions, model=model, mcp_servers=[mcp_server])\n", "    with trace(\"account_manager\"):\n", "        result = await Runner.run(agent, request)\n", "    display(Markdown(result.final_output))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["OpenAI Agents SDK takes care of the MCP Client, but we can also make one ourselves\n", "\n", "## See `accounts_client.py` for our MCP Client"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from accounts_client import read_accounts_resource\n", "\n", "context = await read_accounts_resource(\"ed\")\n", "print(context)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}