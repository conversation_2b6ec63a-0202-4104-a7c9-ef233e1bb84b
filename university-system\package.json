{"name": "university-record-system", "version": "1.0.0", "description": "Comprehensive university record management system", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run dev:student\" \"npm run dev:admin\"", "dev:student": "cd apps/student-portal && npm run dev", "dev:admin": "cd apps/admin-portal && npm run dev", "build": "npm run build:student && npm run build:admin", "build:student": "cd apps/student-portal && npm run build", "build:admin": "cd apps/admin-portal && npm run build", "start": "concurrently \"npm run start:student\" \"npm run start:admin\"", "start:student": "cd apps/student-portal && npm run start", "start:admin": "cd apps/admin-portal && npm run start", "lint": "npm run lint:student && npm run lint:admin", "lint:student": "cd apps/student-portal && npm run lint", "lint:admin": "cd apps/admin-portal && npm run lint", "test": "npm run test:student && npm run test:admin", "test:student": "cd apps/student-portal && npm run test", "test:admin": "cd apps/admin-portal && npm run test", "type-check": "npm run type-check:student && npm run type-check:admin", "type-check:student": "cd apps/student-portal && npm run type-check", "type-check:admin": "cd apps/admin-portal && npm run type-check", "clean": "rm -rf node_modules apps/*/node_modules packages/*/node_modules apps/*/.next", "setup": "npm install && npm run db:setup", "db:setup": "cd packages/database && npm run setup", "db:migrate": "cd packages/database && npm run migrate", "db:seed": "cd packages/database && npm run seed"}, "devDependencies": {"@types/node": "^20.10.0", "concurrently": "^8.2.2", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/university-record-system.git"}, "keywords": ["university", "education", "student-management", "nextjs", "supabase", "typescript"], "author": "Your Organization", "license": "MIT"}