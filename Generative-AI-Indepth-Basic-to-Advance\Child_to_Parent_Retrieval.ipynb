{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyMhxzSd/m4NaE57flW3r70r", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/sunnysavita10/Generative-AI-Indepth-Basic-to-Advance/blob/main/Child_to_Parent_Retrieval.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["![image.png](data:image/png;base64,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)"], "metadata": {"id": "o7u2h6FLqlhE"}}, {"cell_type": "markdown", "source": ["# Parent Document Retriever\n", "\n", "which issue this parent-child retrieval will solve.\n", "\n", "You may want to have small documents, so that their embeddings can most accurately reflect their meaning. If too long, then the embeddings can lose meaning.\n", "\n", "You want to have long enough documents that the context of each chunk is retained.\n", "\n", "The ParentDocumentRetriever strikes that balance by splitting and storing small chunks of data. During retrieval, it first fetches the small chunks but then looks up the parent ids for those chunks and returns those larger documents.\n", "\n", "Note that \"parent document\" refers to the document that a small chunk originated from. This can either be the whole raw document OR a larger chunk."], "metadata": {"id": "wfE3n6CrrnvD"}}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "G4ayMTWunxMO", "outputId": "cd1eee99-fdde-4282-87b5-ac6c08fb82f3"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain\n", "  Downloading langchain-0.2.11-py3-none-any.whl.metadata (7.1 kB)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.0.31)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.9.5)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (4.0.3)\n", "Collecting langchain-core<0.3.0,>=0.2.23 (from langchain)\n", "  Downloading langchain_core-0.2.26-py3-none-any.whl.metadata (6.2 kB)\n", "Collecting langchain-text-splitters<0.3.0,>=0.2.0 (from langchain)\n", "  Downloading langchain_text_splitters-0.2.2-py3-none-any.whl.metadata (2.1 kB)\n", "Collecting langsmith<0.2.0,>=0.1.17 (from langchain)\n", "  Downloading langsmith-0.1.95-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.26.4)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.8.2)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.31.0)\n", "Collecting tenacity!=8.4.0,<9.0.0,>=8.1.0 (from langchain)\n", "  Downloading tenacity-8.5.0-py3-none-any.whl.metadata (1.2 kB)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.9.4)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain-core<0.3.0,>=0.2.23->langchain)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl.metadata (3.0 kB)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.23->langchain) (24.1)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.23->langchain) (4.12.2)\n", "Collecting <PERSON><PERSON><PERSON><4.0.0,>=3.9.14 (from langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading orjson-3.10.6-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (50 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.4/50.4 kB\u001b[0m \u001b[31m679.1 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.20.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (2.20.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2024.7.4)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.0.3)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.23->langchain)\n", "  Downloading jsonpointer-3.0.0-py2.py3-none-any.whl.metadata (2.3 kB)\n", "Downloading langchain-0.2.11-py3-none-any.whl (990 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m990.3/990.3 kB\u001b[0m \u001b[31m14.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langchain_core-0.2.26-py3-none-any.whl (378 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m378.9/378.9 kB\u001b[0m \u001b[31m16.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langchain_text_splitters-0.2.2-py3-none-any.whl (25 kB)\n", "Downloading langsmith-0.1.95-py3-none-any.whl (275 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m275.8/275.8 kB\u001b[0m \u001b[31m12.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading tenacity-8.5.0-py3-none-any.whl (28 kB)\n", "Downloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Downloading orjson-3.10.6-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (141 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m141.1/141.1 kB\u001b[0m \u001b[31m6.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading jsonpointer-3.0.0-py2.py3-none-any.whl (7.6 kB)\n", "Installing collected packages: tenacity, orjson, jsonpointer, jsonpatch, langsmith, langchain-core, langchain-text-splitters, langchain\n", "  Attempting uninstall: tenacity\n", "    Found existing installation: tenacity 9.0.0\n", "    Uninstalling tenacity-9.0.0:\n", "      Successfully uninstalled tenacity-9.0.0\n", "Successfully installed jsonpatch-1.33 jsonpointer-3.0.0 langchain-0.2.11 langchain-core-0.2.26 langchain-text-splitters-0.2.2 langsmith-0.1.95 orjson-3.10.6 tenacity-8.5.0\n"]}], "source": ["!pip install langchain"]}, {"cell_type": "code", "source": ["!pip install -U langchain-community"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ii1EIj8gD5tS", "outputId": "8c5a3ec6-f553-451d-e970-9ecbe0e6e25a"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain-community\n", "  Downloading langchain_community-0.2.10-py3-none-any.whl.metadata (2.7 kB)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (2.0.31)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (3.9.5)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain-community)\n", "  Downloading dataclasses_json-0.6.7-py3-none-any.whl.metadata (25 kB)\n", "Requirement already satisfied: langchain<0.3.0,>=0.2.9 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.2.11)\n", "Requirement already satisfied: langchain-core<0.3.0,>=0.2.23 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.2.26)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.1.95)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (1.26.4)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (2.31.0)\n", "Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (8.5.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (4.0.3)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading marshmallow-3.21.3-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: langchain-text-splitters<0.3.0,>=0.2.0 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.9->langchain-community) (0.2.2)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.9->langchain-community) (2.8.2)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.23->langchain-community) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.23->langchain-community) (24.1)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.23->langchain-community) (4.12.2)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.0->langchain-community) (3.10.6)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (2024.7.4)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain-community) (3.0.3)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.23->langchain-community) (3.0.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.9->langchain-community) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.20.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.9->langchain-community) (2.20.1)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl.metadata (1.1 kB)\n", "Downloading langchain_community-0.2.10-py3-none-any.whl (2.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.3/2.3 MB\u001b[0m \u001b[31m26.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading dataclasses_json-0.6.7-py3-none-any.whl (28 kB)\n", "Downloading marshmallow-3.21.3-py3-none-any.whl (49 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.2/49.2 kB\u001b[0m \u001b[31m3.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: mypy-extensions, marshmallow, typing-inspect, dataclasses-json, langchain-community\n", "Successfully installed dataclasses-json-0.6.7 langchain-community-0.2.10 marshmallow-3.21.3 mypy-extensions-1.0.0 typing-inspect-0.9.0\n"]}]}, {"cell_type": "code", "source": ["!pip install sentence-transformers"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tfjfXJr1D5vs", "outputId": "7c44ade3-687d-4f1b-d2e1-74155f4a8b3a"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting sentence-transformers\n", "  Downloading sentence_transformers-3.0.1-py3-none-any.whl.metadata (10 kB)\n", "Requirement already satisfied: transformers<5.0.0,>=4.34.0 in /usr/local/lib/python3.10/dist-packages (from sentence-transformers) (4.42.4)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from sentence-transformers) (4.66.4)\n", "Requirement already satisfied: torch>=1.11.0 in /usr/local/lib/python3.10/dist-packages (from sentence-transformers) (2.3.1+cu121)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from sentence-transformers) (1.26.4)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.10/dist-packages (from sentence-transformers) (1.3.2)\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.10/dist-packages (from sentence-transformers) (1.13.1)\n", "Requirement already satisfied: huggingface-hub>=0.15.1 in /usr/local/lib/python3.10/dist-packages (from sentence-transformers) (0.23.5)\n", "Requirement already satisfied: Pillow in /usr/local/lib/python3.10/dist-packages (from sentence-transformers) (9.4.0)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence-transformers) (3.15.4)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence-transformers) (2024.6.1)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence-transformers) (24.1)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence-transformers) (6.0.1)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence-transformers) (2.31.0)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.15.1->sentence-transformers) (4.12.2)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers) (1.13.1)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers) (3.3)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers) (3.1.4)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.1.105 (from torch>=1.11.0->sentence-transformers)\n", "  Using cached nvidia_cuda_nvrtc_cu12-12.1.105-py3-none-manylinux1_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-runtime-cu12==12.1.105 (from torch>=1.11.0->sentence-transformers)\n", "  Using cached nvidia_cuda_runtime_cu12-12.1.105-py3-none-manylinux1_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.1.105 (from torch>=1.11.0->sentence-transformers)\n", "  Using cached nvidia_cuda_cupti_cu12-12.1.105-py3-none-manylinux1_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cudnn-cu12==******** (from torch>=1.11.0->sentence-transformers)\n", "  Using cached nvidia_cudnn_cu12-********-py3-none-manylinux1_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cublas-cu12==******** (from torch>=1.11.0->sentence-transformers)\n", "  Using cached nvidia_cublas_cu12-********-py3-none-manylinux1_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cufft-cu12==********* (from torch>=1.11.0->sentence-transformers)\n", "  Using cached nvidia_cufft_cu12-*********-py3-none-manylinux1_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-curand-cu12==********** (from torch>=1.11.0->sentence-transformers)\n", "  Using cached nvidia_curand_cu12-**********-py3-none-manylinux1_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cusolver-cu12==********** (from torch>=1.11.0->sentence-transformers)\n", "  Using cached nvidia_cusolver_cu12-**********-py3-none-manylinux1_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cusparse-cu12==********** (from torch>=1.11.0->sentence-transformers)\n", "  Using cached nvidia_cusparse_cu12-**********-py3-none-manylinux1_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-nccl-cu12==2.20.5 (from torch>=1.11.0->sentence-transformers)\n", "  Using cached nvidia_nccl_cu12-2.20.5-py3-none-manylinux2014_x86_64.whl.metadata (1.8 kB)\n", "Collecting nvidia-nvtx-cu12==12.1.105 (from torch>=1.11.0->sentence-transformers)\n", "  Using cached nvidia_nvtx_cu12-12.1.105-py3-none-manylinux1_x86_64.whl.metadata (1.7 kB)\n", "Requirement already satisfied: triton==2.3.1 in /usr/local/lib/python3.10/dist-packages (from torch>=1.11.0->sentence-transformers) (2.3.1)\n", "Collecting nvidia-nvjitlink-cu12 (from nvidia-cusolver-cu12==**********->torch>=1.11.0->sentence-transformers)\n", "  Downloading nvidia_nvjitlink_cu12-12.5.82-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.34.0->sentence-transformers) (2024.5.15)\n", "Requirement already satisfied: safetensors>=0.4.1 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.34.0->sentence-transformers) (0.4.3)\n", "Requirement already satisfied: tokenizers<0.20,>=0.19 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.34.0->sentence-transformers) (0.19.1)\n", "Requirement already satisfied: joblib>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from scikit-learn->sentence-transformers) (1.4.2)\n", "Requirement already satisfied: threadpoolctl>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from scikit-learn->sentence-transformers) (3.5.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch>=1.11.0->sentence-transformers) (2.1.5)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.15.1->sentence-transformers) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.15.1->sentence-transformers) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.15.1->sentence-transformers) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.15.1->sentence-transformers) (2024.7.4)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from sympy->torch>=1.11.0->sentence-transformers) (1.3.0)\n", "Downloading sentence_transformers-3.0.1-py3-none-any.whl (227 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m227.1/227.1 kB\u001b[0m \u001b[31m4.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hUsing cached nvidia_cublas_cu12-********-py3-none-manylinux1_x86_64.whl (410.6 MB)\n", "Using cached nvidia_cuda_cupti_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (14.1 MB)\n", "Using cached nvidia_cuda_nvrtc_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (23.7 MB)\n", "Using cached nvidia_cuda_runtime_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (823 kB)\n", "Using cached nvidia_cudnn_cu12-********-py3-none-manylinux1_x86_64.whl (731.7 MB)\n", "Using cached nvidia_cufft_cu12-*********-py3-none-manylinux1_x86_64.whl (121.6 MB)\n", "Using cached nvidia_curand_cu12-**********-py3-none-manylinux1_x86_64.whl (56.5 MB)\n", "Using cached nvidia_cusolver_cu12-**********-py3-none-manylinux1_x86_64.whl (124.2 MB)\n", "Using cached nvidia_cusparse_cu12-**********-py3-none-manylinux1_x86_64.whl (196.0 MB)\n", "Using cached nvidia_nccl_cu12-2.20.5-py3-none-manylinux2014_x86_64.whl (176.2 MB)\n", "Using cached nvidia_nvtx_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (99 kB)\n", "Downloading nvidia_nvjitlink_cu12-12.5.82-py3-none-manylinux2014_x86_64.whl (21.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.3/21.3 MB\u001b[0m \u001b[31m12.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: nvidia-nvtx-cu12, nvidia-nvjitlink-cu12, nvidia-nccl-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, nvidia-cusparse-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12, sentence-transformers\n", "Successfully installed nvidia-cublas-cu12-******** nvidia-cuda-cupti-cu12-12.1.105 nvidia-cuda-nvrtc-cu12-12.1.105 nvidia-cuda-runtime-cu12-12.1.105 nvidia-cudnn-cu12-******** nvidia-cufft-cu12-********* nvidia-curand-cu12-********** nvidia-cusolver-cu12-********** nvidia-cusparse-cu12-********** nvidia-nccl-cu12-2.20.5 nvidia-nvjitlink-cu12-12.5.82 nvidia-nvtx-cu12-12.1.105 sentence-transformers-3.0.1\n"]}]}, {"cell_type": "code", "source": ["!pip install langchain_chroma"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "m3xnoTsMD5yQ", "outputId": "47f1b53a-bdff-4057-d5cc-e9a4910ac681"}, "execution_count": 10, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain_chroma\n", "  Downloading langchain_chroma-0.1.2-py3-none-any.whl.metadata (1.3 kB)\n", "Collecting chromadb<0.6.0,>=0.4.0 (from langchain_chroma)\n", "  Downloading chromadb-0.5.5-py3-none-any.whl.metadata (6.8 kB)\n", "Collecting fastapi<1,>=0.95.2 (from langchain_chroma)\n", "  Downloading fastapi-0.111.1-py3-none-any.whl.metadata (26 kB)\n", "Requirement already satisfied: langchain-core<0.3,>=0.1.40 in /usr/local/lib/python3.10/dist-packages (from langchain_chroma) (0.2.26)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain_chroma) (1.26.4)\n", "Requirement already satisfied: build>=1.0.3 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.2.1)\n", "Requirement already satisfied: pydantic>=1.9 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.8.2)\n", "Collecting chroma-hnswlib==0.7.6 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading chroma_hnswlib-0.7.6-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (252 bytes)\n", "Collecting uvicorn>=0.18.3 (from uvicorn[standard]>=0.18.3->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading uvicorn-0.30.4-py3-none-any.whl.metadata (6.6 kB)\n", "Collecting posthog>=2.4.0 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading posthog-3.5.0-py2.py3-none-any.whl.metadata (2.0 kB)\n", "Requirement already satisfied: typing-extensions>=4.5.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (4.12.2)\n", "Collecting onnxruntime>=1.14.1 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading onnxruntime-1.18.1-cp310-cp310-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl.metadata (4.3 kB)\n", "Collecting opentelemetry-api>=1.2.0 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_api-1.26.0-py3-none-any.whl.metadata (1.4 kB)\n", "Collecting opentelemetry-exporter-otlp-proto-grpc>=1.2.0 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_exporter_otlp_proto_grpc-1.26.0-py3-none-any.whl.metadata (2.3 kB)\n", "Collecting opentelemetry-instrumentation-fastapi>=0.41b0 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_instrumentation_fastapi-0.47b0-py3-none-any.whl.metadata (2.1 kB)\n", "Collecting opentelemetry-sdk>=1.2.0 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_sdk-1.26.0-py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: tokenizers>=0.13.2 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.19.1)\n", "Collecting pypika>=0.48.9 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading PyPika-0.48.9.tar.gz (67 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.3/67.3 kB\u001b[0m \u001b[31m4.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: tqdm>=4.65.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (4.66.4)\n", "Collecting overrides>=7.3.1 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading overrides-7.7.0-py3-none-any.whl.metadata (5.8 kB)\n", "Requirement already satisfied: importlib-resources in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (6.4.0)\n", "Requirement already satisfied: grpcio>=1.58.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.64.1)\n", "Collecting bcrypt>=4.0.1 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading bcrypt-4.2.0-cp39-abi3-manylinux_2_28_x86_64.whl.metadata (9.6 kB)\n", "Requirement already satisfied: typer>=0.9.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.12.3)\n", "Collecting kubernetes>=28.1.0 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading kubernetes-30.1.0-py2.py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: tenacity>=8.2.3 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (8.5.0)\n", "Requirement already satisfied: PyYAML>=6.0.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (6.0.1)\n", "Collecting mmh3>=4.0.1 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading mmh3-4.1.0-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (13 kB)\n", "Requirement already satisfied: orjson>=3.9.12 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.10.6)\n", "Collecting httpx>=0.27.0 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading httpx-0.27.0-py3-none-any.whl.metadata (7.2 kB)\n", "Collecting starlette<0.38.0,>=0.37.2 (from fastapi<1,>=0.95.2->langchain_chroma)\n", "  Downloading starlette-0.37.2-py3-none-any.whl.metadata (5.9 kB)\n", "Collecting fastapi-cli>=0.0.2 (from fastapi<1,>=0.95.2->langchain_chroma)\n", "  Downloading fastapi_cli-0.0.4-py3-none-any.whl.metadata (7.0 kB)\n", "Requirement already satisfied: jinja2>=2.11.2 in /usr/local/lib/python3.10/dist-packages (from fastapi<1,>=0.95.2->langchain_chroma) (3.1.4)\n", "Collecting python-multipart>=0.0.7 (from fastapi<1,>=0.95.2->langchain_chroma)\n", "  Downloading python_multipart-0.0.9-py3-none-any.whl.metadata (2.5 kB)\n", "Collecting email_validator>=2.0.0 (from fastapi<1,>=0.95.2->langchain_chroma)\n", "  Downloading email_validator-2.2.0-py3-none-any.whl.metadata (25 kB)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.1.40->langchain_chroma) (1.33)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.75 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.1.40->langchain_chroma) (0.1.95)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.1.40->langchain_chroma) (24.1)\n", "Requirement already satisfied: pyproject_hooks in /usr/local/lib/python3.10/dist-packages (from build>=1.0.3->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.1.0)\n", "Requirement already satisfied: tomli>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from build>=1.0.3->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.0.1)\n", "Collecting dnspython>=2.0.0 (from email_validator>=2.0.0->fastapi<1,>=0.95.2->langchain_chroma)\n", "  Downloading dnspython-2.6.1-py3-none-any.whl.metadata (5.8 kB)\n", "Requirement already satisfied: idna>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from email_validator>=2.0.0->fastapi<1,>=0.95.2->langchain_chroma) (3.7)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2024.7.4)\n", "Collecting httpcore==1.* (from httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl.metadata (20 kB)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.3.1)\n", "Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading h11-0.14.0-py3-none-any.whl.metadata (8.2 kB)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2>=2.11.2->fastapi<1,>=0.95.2->langchain_chroma) (2.1.5)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3,>=0.1.40->langchain_chroma) (3.0.0)\n", "Requirement already satisfied: six>=1.9.0 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.16.0)\n", "Requirement already satisfied: python-dateutil>=2.5.3 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.8.2)\n", "Requirement already satisfied: google-auth>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.27.0)\n", "Requirement already satisfied: websocket-client!=0.40.0,!=0.41.*,!=0.42.*,>=0.32.0 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.8.0)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.31.0)\n", "Requirement already satisfied: requests-oauthlib in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.3.1)\n", "Requirement already satisfied: oauthlib>=3.2.2 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.2.2)\n", "Requirement already satisfied: urllib3>=1.24.2 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.0.7)\n", "Collecting coloredlogs (from onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading coloredlogs-15.0.1-py2.py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: flatbuffers in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma) (24.3.25)\n", "Requirement already satisfied: protobuf in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.20.3)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.13.1)\n", "Collecting deprecated>=1.2.6 (from opentelemetry-api>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading Deprecated-1.2.14-py2.py3-none-any.whl.metadata (5.4 kB)\n", "Collecting importlib-metadata<=8.0.0,>=6.0 (from opentelemetry-api>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading importlib_metadata-8.0.0-py3-none-any.whl.metadata (4.6 kB)\n", "Requirement already satisfied: googleapis-common-protos~=1.52 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.63.2)\n", "Collecting opentelemetry-exporter-otlp-proto-common==1.26.0 (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_exporter_otlp_proto_common-1.26.0-py3-none-any.whl.metadata (1.8 kB)\n", "Collecting opentelemetry-proto==1.26.0 (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_proto-1.26.0-py3-none-any.whl.metadata (2.3 kB)\n", "Collecting opentelemetry-instrumentation-asgi==0.47b0 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_instrumentation_asgi-0.47b0-py3-none-any.whl.metadata (2.0 kB)\n", "Collecting opentelemetry-instrumentation==0.47b0 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_instrumentation-0.47b0-py3-none-any.whl.metadata (6.1 kB)\n", "Collecting opentelemetry-semantic-conventions==0.47b0 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_semantic_conventions-0.47b0-py3-none-any.whl.metadata (2.4 kB)\n", "Collecting opentelemetry-util-http==0.47b0 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_util_http-0.47b0-py3-none-any.whl.metadata (2.5 kB)\n", "Requirement already satisfied: setuptools>=16.0 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-instrumentation==0.47b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (71.0.4)\n", "Requirement already satisfied: wrapt<2.0.0,>=1.0.0 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-instrumentation==0.47b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.16.0)\n", "Collecting asgiref~=3.0 (from opentelemetry-instrumentation-asgi==0.47b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading asgiref-3.8.1-py3-none-any.whl.metadata (9.3 kB)\n", "Collecting monotonic>=1.5 (from posthog>=2.4.0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading monotonic-1.6-py2.py3-none-any.whl.metadata (1.5 kB)\n", "Collecting backoff>=1.10.0 (from posthog>=2.4.0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading backoff-2.2.1-py3-none-any.whl.metadata (14 kB)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.9->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.20.1 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.9->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.20.1)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.16.4 in /usr/local/lib/python3.10/dist-packages (from tokenizers>=0.13.2->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.23.5)\n", "Requirement already satisfied: click>=8.0.0 in /usr/local/lib/python3.10/dist-packages (from typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (8.1.7)\n", "Requirement already satisfied: shellingham>=1.3.0 in /usr/local/lib/python3.10/dist-packages (from typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.5.4)\n", "Requirement already satisfied: rich>=10.11.0 in /usr/local/lib/python3.10/dist-packages (from typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (13.7.1)\n", "Collecting httptools>=0.5.0 (from uvicorn[standard]>=0.18.3->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading httptools-0.6.1-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.6 kB)\n", "Collecting python-dotenv>=0.13 (from uvicorn[standard]>=0.18.3->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading python_dotenv-1.0.1-py3-none-any.whl.metadata (23 kB)\n", "Collecting uvloop!=0.15.0,!=0.15.1,>=0.14.0 (from uvicorn[standard]>=0.18.3->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading uvloop-0.19.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)\n", "Collecting watchfiles>=0.13 (from uvicorn[standard]>=0.18.3->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading watchfiles-0.22.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)\n", "Collecting websockets>=10.4 (from uvicorn[standard]>=0.18.3->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading websockets-12.0-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.6 kB)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.2.2)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (5.4.0)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.4.0)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (4.9)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.15.4)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2024.6.1)\n", "Requirement already satisfied: zipp>=0.5 in /usr/local/lib/python3.10/dist-packages (from importlib-metadata<=8.0.0,>=6.0->opentelemetry-api>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.19.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.3.2)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /usr/local/lib/python3.10/dist-packages (from rich>=10.11.0->typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /usr/local/lib/python3.10/dist-packages (from rich>=10.11.0->typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.16.1)\n", "Collecting humanfriendly>=9.1 (from coloredlogs->onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading humanfriendly-10.0-py2.py3-none-any.whl.metadata (9.2 kB)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from sympy->onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.3.0)\n", "Requirement already satisfied: mdurl~=0.1 in /usr/local/lib/python3.10/dist-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.1.2)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth>=1.0.1->kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.6.0)\n", "Downloading langchain_chroma-0.1.2-py3-none-any.whl (9.3 kB)\n", "Downloading chromadb-0.5.5-py3-none-any.whl (584 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m584.3/584.3 kB\u001b[0m \u001b[31m19.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading chroma_hnswlib-0.7.6-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.4/2.4 MB\u001b[0m \u001b[31m56.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading fastapi-0.111.1-py3-none-any.whl (92 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m92.2/92.2 kB\u001b[0m \u001b[31m6.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading bcrypt-4.2.0-cp39-abi3-manylinux_2_28_x86_64.whl (273 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m273.8/273.8 kB\u001b[0m \u001b[31m16.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading email_validator-2.2.0-py3-none-any.whl (33 kB)\n", "Downloading fastapi_cli-0.0.4-py3-none-any.whl (9.5 kB)\n", "Downloading httpx-0.27.0-py3-none-any.whl (75 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m5.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m5.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading kubernetes-30.1.0-py2.py3-none-any.whl (1.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.7/1.7 MB\u001b[0m \u001b[31m52.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mmh3-4.1.0-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (67 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.6/67.6 kB\u001b[0m \u001b[31m4.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading onnxruntime-1.18.1-cp310-cp310-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (6.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.8/6.8 MB\u001b[0m \u001b[31m82.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_api-1.26.0-py3-none-any.whl (61 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m61.5/61.5 kB\u001b[0m \u001b[31m4.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_exporter_otlp_proto_grpc-1.26.0-py3-none-any.whl (18 kB)\n", "Downloading opentelemetry_exporter_otlp_proto_common-1.26.0-py3-none-any.whl (17 kB)\n", "Downloading opentelemetry_proto-1.26.0-py3-none-any.whl (52 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m52.5/52.5 kB\u001b[0m \u001b[31m3.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_instrumentation_fastapi-0.47b0-py3-none-any.whl (11 kB)\n", "Downloading opentelemetry_instrumentation-0.47b0-py3-none-any.whl (29 kB)\n", "Downloading opentelemetry_instrumentation_asgi-0.47b0-py3-none-any.whl (15 kB)\n", "Downloading opentelemetry_semantic_conventions-0.47b0-py3-none-any.whl (138 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m138.0/138.0 kB\u001b[0m \u001b[31m10.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_util_http-0.47b0-py3-none-any.whl (6.9 kB)\n", "Downloading opentelemetry_sdk-1.26.0-py3-none-any.whl (109 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m109.5/109.5 kB\u001b[0m \u001b[31m8.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading overrides-7.7.0-py3-none-any.whl (17 kB)\n", "Downloading posthog-3.5.0-py2.py3-none-any.whl (41 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m41.3/41.3 kB\u001b[0m \u001b[31m2.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading python_multipart-0.0.9-py3-none-any.whl (22 kB)\n", "Downloading starlette-0.37.2-py3-none-any.whl (71 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m71.9/71.9 kB\u001b[0m \u001b[31m5.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading uvicorn-0.30.4-py3-none-any.whl (62 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m62.8/62.8 kB\u001b[0m \u001b[31m4.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading backoff-2.2.1-py3-none-any.whl (15 kB)\n", "Downloading Deprecated-1.2.14-py2.py3-none-any.whl (9.6 kB)\n", "Downloading dnspython-2.6.1-py3-none-any.whl (307 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m307.7/307.7 kB\u001b[0m \u001b[31m19.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m4.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading httptools-0.6.1-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (341 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m341.4/341.4 kB\u001b[0m \u001b[31m21.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading importlib_metadata-8.0.0-py3-none-any.whl (24 kB)\n", "Downloading monotonic-1.6-py2.py3-none-any.whl (8.2 kB)\n", "Downloading python_dotenv-1.0.1-py3-none-any.whl (19 kB)\n", "Downloading uvloop-0.19.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.4/3.4 MB\u001b[0m \u001b[31m65.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading watchfiles-0.22.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.2/1.2 MB\u001b[0m \u001b[31m48.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading websockets-12.0-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (130 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m130.2/130.2 kB\u001b[0m \u001b[31m10.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading coloredlogs-15.0.1-py2.py3-none-any.whl (46 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.0/46.0 kB\u001b[0m \u001b[31m3.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading asgiref-3.8.1-py3-none-any.whl (23 kB)\n", "Downloading humanfriendly-10.0-py2.py3-none-any.whl (86 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.8/86.8 kB\u001b[0m \u001b[31m5.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hBuilding wheels for collected packages: pypika\n", "  Building wheel for pypika (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for pypika: filename=PyPika-0.48.9-py2.py3-none-any.whl size=53725 sha256=76a30885deb5d2a5cab036709f749f6d613809acef3059d3d7927a293a6d97b3\n", "  Stored in directory: /root/.cache/pip/wheels/e1/26/51/d0bffb3d2fd82256676d7ad3003faea3bd6dddc9577af665f4\n", "Successfully built pypika\n", "Installing collected packages: pypika, monotonic, mmh3, websockets, uvloop, python-multipart, python-dotenv, overrides, opentelemetry-util-http, opentelemetry-proto, importlib-metadata, humanfriendly, httptools, h11, dnspython, deprecated, chroma-hnswlib, bcrypt, backoff, asgiref, watchfiles, uvicorn, starlette, posthog, opentelemetry-exporter-otlp-proto-common, opentelemetry-api, httpcore, email_validator, coloredlogs, opentelemetry-semantic-conventions, opentelemetry-instrumentation, onnxruntime, kubernetes, httpx, opentelemetry-sdk, opentelemetry-instrumentation-asgi, fastapi-cli, opentelemetry-instrumentation-fastapi, opentelemetry-exporter-otlp-proto-grpc, fastapi, chromadb, langchain_chroma\n", "  Attempting uninstall: importlib-metadata\n", "    Found existing installation: importlib_metadata 8.2.0\n", "    Uninstalling importlib_metadata-8.2.0:\n", "      Successfully uninstalled importlib_metadata-8.2.0\n", "Successfully installed asgiref-3.8.1 backoff-2.2.1 bcrypt-4.2.0 chroma-hnswlib-0.7.6 chromadb-0.5.5 coloredlogs-15.0.1 deprecated-1.2.14 dnspython-2.6.1 email_validator-2.2.0 fastapi-0.111.1 fastapi-cli-0.0.4 h11-0.14.0 httpcore-1.0.5 httptools-0.6.1 httpx-0.27.0 humanfriendly-10.0 importlib-metadata-8.0.0 kubernetes-30.1.0 langchain_chroma-0.1.2 mmh3-4.1.0 monotonic-1.6 onnxruntime-1.18.1 opentelemetry-api-1.26.0 opentelemetry-exporter-otlp-proto-common-1.26.0 opentelemetry-exporter-otlp-proto-grpc-1.26.0 opentelemetry-instrumentation-0.47b0 opentelemetry-instrumentation-asgi-0.47b0 opentelemetry-instrumentation-fastapi-0.47b0 opentelemetry-proto-1.26.0 opentelemetry-sdk-1.26.0 opentelemetry-semantic-conventions-0.47b0 opentelemetry-util-http-0.47b0 overrides-7.7.0 posthog-3.5.0 pypika-0.48.9 python-dotenv-1.0.1 python-multipart-0.0.9 starlette-0.37.2 uvicorn-0.30.4 uvloop-0.19.0 watchfiles-0.22.0 websockets-12.0\n"]}]}, {"cell_type": "code", "source": ["####if you want to use gemini feel free to use this code.\n", "\n", "%pip install --upgrade --quiet  google-generativeai langchain-google-genai"], "metadata": {"id": "VskBgo9gGAlO"}, "execution_count": 11, "outputs": []}, {"cell_type": "markdown", "source": ["# Data Ingestion"], "metadata": {"id": "2xJdEql4oqCc"}}, {"cell_type": "code", "source": ["from langchain_community.document_loaders import TextLoader"], "metadata": {"id": "Eh9IIWPsowTD"}, "execution_count": 4, "outputs": []}, {"cell_type": "code", "source": ["loaders = [\n", "    TextLoader(\"/content/data/paul_graham_essay.txt\"),\n", "    TextLoader(\"/content/data/state_of_the_union.txt\"),\n", "]"], "metadata": {"id": "Z-zV6y-powVj"}, "execution_count": 5, "outputs": []}, {"cell_type": "code", "source": ["docs = []"], "metadata": {"id": "obRj2p23EwPv"}, "execution_count": 7, "outputs": []}, {"cell_type": "code", "source": ["for loader in loaders:\n", "    docs.extend(loader.load())"], "metadata": {"id": "zT9DHChvowYL"}, "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": ["docs"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SYT5-y2wowaj", "outputId": "c89b2637-2acd-4eb4-accd-f0257ee542f4"}, "execution_count": 9, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(metadata={'source': '/content/data/paul_graham_essay.txt'}, page_content='\\t\\t\\n\\nWhat I Worked On\\n\\nFebruary 2021\\n\\nBefore college the two main things I worked on, outside of school, were writing and programming. I didn\\'t write essays. I wrote what beginning writers were supposed to write then, and probably still are: short stories. My stories were awful. They had hardly any plot, just characters with strong feelings, which I imagined made them deep.\\n\\nThe first programs I tried writing were on the IBM 1401 that our school district used for what was then called \"data processing.\" This was in 9th grade, so I was 13 or 14. The school district\\'s 1401 happened to be in the basement of our junior high school, and my friend <PERSON> and I got permission to use it. It was like a mini Bond villain\\'s lair down there, with all these alien-looking machines — CPU, disk drives, printer, card reader — sitting up on a raised floor under bright fluorescent lights.\\n\\nThe language we used was an early version of Fortran. You had to type programs on punch cards, then stack them in the card reader and press a button to load the program into memory and run it. The result would ordinarily be to print something on the spectacularly loud printer.\\n\\n<PERSON> was puzzled by the 1401. I couldn\\'t figure out what to do with it. And in retrospect there\\'s not much I could have done with it. The only form of input to programs was data stored on punched cards, and I didn\\'t have any data stored on punched cards. The only other option was to do things that didn\\'t rely on any input, like calculate approximations of pi, but I didn\\'t know enough math to do anything interesting of that type. So I\\'m not surprised I can\\'t remember any programs I wrote, because they can\\'t have done much. My clearest memory is of the moment I learned it was possible for programs not to terminate, when one of mine didn\\'t. On a machine without time-sharing, this was a social as well as a technical error, as the data center manager\\'s expression made clear.\\n\\nWith microcomputers, everything changed. Now you could have a computer sitting right in front of you, on a desk, that could respond to your keystrokes as it was running instead of just churning through a stack of punch cards and then stopping. [1]\\n\\nThe first of my friends to get a microcomputer built it himself. It was sold as a kit by Heathkit. I remember vividly how impressed and envious I felt watching him sitting in front of it, typing programs right into the computer.\\n\\nComputers were expensive in those days and it took me years of nagging before I convinced my father to buy one, a TRS-80, in about 1980. The gold standard then was the Apple II, but a TRS-80 was good enough. This was when I really started programming. I wrote simple games, a program to predict how high my model rockets would fly, and a word processor that my father used to write at least one book. There was only room in memory for about 2 pages of text, so he\\'d write 2 pages at a time and then print them out, but it was a lot better than a typewriter.\\n\\nThough I liked programming, I didn\\'t plan to study it in college. In college I was going to study philosophy, which sounded much more powerful. It seemed, to my naive high school self, to be the study of the ultimate truths, compared to which the things studied in other fields would be mere domain knowledge. What I discovered when I got to college was that the other fields took up so much of the space of ideas that there wasn\\'t much left for these supposed ultimate truths. All that seemed left for philosophy were edge cases that people in other fields felt could safely be ignored.\\n\\nI couldn\\'t have put this into words when I was 18. All I knew at the time was that I kept taking philosophy courses and they kept being boring. So I decided to switch to AI.\\n\\nAI was in the air in the mid 1980s, but there were two things especially that made me want to work on it: a novel by Heinlein called The Moon is a Harsh Mistress, which featured an intelligent computer called Mike, and a PBS documentary that showed Terry Winograd using SHRDLU. I haven\\'t tried rereading The Moon is a Harsh Mistress, so I don\\'t know how well it has aged, but when I read it I was drawn entirely into its world. It seemed only a matter of time before we\\'d have Mike, and when I saw Winograd using SHRDLU, it seemed like that time would be a few years at most. All you had to do was teach SHRDLU more words.\\n\\nThere weren\\'t any classes in AI at Cornell then, not even graduate classes, so I started trying to teach myself. Which meant learning Lisp, since in those days Lisp was regarded as the language of AI. The commonly used programming languages then were pretty primitive, and programmers\\' ideas correspondingly so. The default language at Cornell was a Pascal-like language called PL/I, and the situation was similar elsewhere. Learning Lisp expanded my concept of a program so fast that it was years before I started to have a sense of where the new limits were. This was more like it; this was what I had expected college to do. It wasn\\'t happening in a class, like it was supposed to, but that was ok. For the next couple years I was on a roll. I knew what I was going to do.\\n\\nFor my undergraduate thesis, I reverse-engineered SHRDLU. My God did I love working on that program. It was a pleasing bit of code, but what made it even more exciting was my belief — hard to imagine now, but not unique in 1985 — that it was already climbing the lower slopes of intelligence.\\n\\nI had gotten into a program at Cornell that didn\\'t make you choose a major. You could take whatever classes you liked, and choose whatever you liked to put on your degree. I of course chose \"Artificial Intelligence.\" When I got the actual physical diploma, I was dismayed to find that the quotes had been included, which made them read as scare-quotes. At the time this bothered me, but now it seems amusingly accurate, for reasons I was about to discover.\\n\\nI applied to 3 grad schools: MIT and Yale, which were renowned for AI at the time, and Harvard, which I\\'d visited because Rich Draves went there, and was also home to Bill Woods, who\\'d invented the type of parser I used in my SHRDLU clone. Only Harvard accepted me, so that was where I went.\\n\\nI don\\'t remember the moment it happened, or if there even was a specific moment, but during the first year of grad school I realized that AI, as practiced at the time, was a hoax. By which I mean the sort of AI in which a program that\\'s told \"the dog is sitting on the chair\" translates this into some formal representation and adds it to the list of things it knows.\\n\\nWhat these programs really showed was that there\\'s a subset of natural language that\\'s a formal language. But a very proper subset. It was clear that there was an unbridgeable gap between what they could do and actually understanding natural language. It was not, in fact, simply a matter of teaching SHRDLU more words. That whole way of doing AI, with explicit data structures representing concepts, was not going to work. Its brokenness did, as so often happens, generate a lot of opportunities to write papers about various band-aids that could be applied to it, but it was never going to get us Mike.\\n\\nSo I looked around to see what I could salvage from the wreckage of my plans, and there was Lisp. I knew from experience that Lisp was interesting for its own sake and not just for its association with AI, even though that was the main reason people cared about it at the time. So I decided to focus on Lisp. In fact, I decided to write a book about Lisp hacking. It\\'s scary to think how little I knew about Lisp hacking when I started writing that book. But there\\'s nothing like writing a book about something to help you learn it. The book, On Lisp, wasn\\'t published till 1993, but I wrote much of it in grad school.\\n\\nComputer Science is an uneasy alliance between two halves, theory and systems. The theory people prove things, and the systems people build things. I wanted to build things. I had plenty of respect for theory — indeed, a sneaking suspicion that it was the more admirable of the two halves — but building things seemed so much more exciting.\\n\\nThe problem with systems work, though, was that it didn\\'t last. Any program you wrote today, no matter how good, would be obsolete in a couple decades at best. People might mention your software in footnotes, but no one would actually use it. And indeed, it would seem very feeble work. Only people with a sense of the history of the field would even realize that, in its time, it had been good.\\n\\nThere were some surplus Xerox Dandelions floating around the computer lab at one point. Anyone who wanted one to play around with could have one. I was briefly tempted, but they were so slow by present standards; what was the point? No one else wanted one either, so off they went. That was what happened to systems work.\\n\\nI wanted not just to build things, but to build things that would last.\\n\\nIn this dissatisfied state I went in 1988 to visit Rich Draves at CMU, where he was in grad school. One day I went to visit the Carnegie Institute, where I\\'d spent a lot of time as a kid. While looking at a painting there I realized something that might seem obvious, but was a big surprise to me. There, right on the wall, was something you could make that would last. Paintings didn\\'t become obsolete. Some of the best ones were hundreds of years old.\\n\\nAnd moreover this was something you could make a living doing. Not as easily as you could by writing software, of course, but I thought if you were really industrious and lived really cheaply, it had to be possible to make enough to survive. And as an artist you could be truly independent. You wouldn\\'t have a boss, or even need to get research funding.\\n\\nI had always liked looking at paintings. Could I make them? I had no idea. I\\'d never imagined it was even possible. I knew intellectually that people made art — that it didn\\'t just appear spontaneously — but it was as if the people who made it were a different species. They either lived long ago or were mysterious geniuses doing strange things in profiles in Life magazine. The idea of actually being able to make art, to put that verb before that noun, seemed almost miraculous.\\n\\nThat fall I started taking art classes at Harvard. Grad students could take classes in any department, and my advisor, Tom Cheatham, was very easy going. If he even knew about the strange classes I was taking, he never said anything.\\n\\nSo now I was in a PhD program in computer science, yet planning to be an artist, yet also genuinely in love with Lisp hacking and working away at On Lisp. In other words, like many a grad student, I was working energetically on multiple projects that were not my thesis.\\n\\nI didn\\'t see a way out of this situation. I didn\\'t want to drop out of grad school, but how else was I going to get out? I remember when my friend Robert Morris got kicked out of Cornell for writing the internet worm of 1988, I was envious that he\\'d found such a spectacular way to get out of grad school.\\n\\nThen one day in April 1990 a crack appeared in the wall. I ran into professor Cheatham and he asked if I was far enough along to graduate that June. I didn\\'t have a word of my dissertation written, but in what must have been the quickest bit of thinking in my life, I decided to take a shot at writing one in the 5 weeks or so that remained before the deadline, reusing parts of On Lisp where I could, and I was able to respond, with no perceptible delay \"Yes, I think so. I\\'ll give you something to read in a few days.\"\\n\\nI picked applications of continuations as the topic. In retrospect I should have written about macros and embedded languages. There\\'s a whole world there that\\'s barely been explored. But all I wanted was to get out of grad school, and my rapidly written dissertation sufficed, just barely.\\n\\nMeanwhile I was applying to art schools. I applied to two: RISD in the US, and the Accademia di Belli Arti in Florence, which, because it was the oldest art school, I imagined would be good. RISD accepted me, and I never heard back from the Accademia, so off to Providence I went.\\n\\nI\\'d applied for the BFA program at RISD, which meant in effect that I had to go to college again. This was not as strange as it sounds, because I was only 25, and art schools are full of people of different ages. RISD counted me as a transfer sophomore and said I had to do the foundation that summer. The foundation means the classes that everyone has to take in fundamental subjects like drawing, color, and design.\\n\\nToward the end of the summer I got a big surprise: a letter from the Accademia, which had been delayed because they\\'d sent it to Cambridge England instead of Cambridge Massachusetts, inviting me to take the entrance exam in Florence that fall. This was now only weeks away. My nice landlady let me leave my stuff in her attic. I had some money saved from consulting work I\\'d done in grad school; there was probably enough to last a year if I lived cheaply. Now all I had to do was learn Italian.\\n\\nOnly stranieri (foreigners) had to take this entrance exam. In retrospect it may well have been a way of excluding them, because there were so many stranieri attracted by the idea of studying art in Florence that the Italian students would otherwise have been outnumbered. I was in decent shape at painting and drawing from the RISD foundation that summer, but I still don\\'t know how I managed to pass the written exam. I remember that I answered the essay question by writing about Cezanne, and that I cranked up the intellectual level as high as I could to make the most of my limited vocabulary. [2]\\n\\nI\\'m only up to age 25 and already there are such conspicuous patterns. Here I was, yet again about to attend some august institution in the hopes of learning about some prestigious subject, and yet again about to be disappointed. The students and faculty in the painting department at the Accademia were the nicest people you could imagine, but they had long since arrived at an arrangement whereby the students wouldn\\'t require the faculty to teach anything, and in return the faculty wouldn\\'t require the students to learn anything. And at the same time all involved would adhere outwardly to the conventions of a 19th century atelier. We actually had one of those little stoves, fed with kindling, that you see in 19th century studio paintings, and a nude model sitting as close to it as possible without getting burned. Except hardly anyone else painted her besides me. The rest of the students spent their time chatting or occasionally trying to imitate things they\\'d seen in American art magazines.\\n\\nOur model turned out to live just down the street from me. She made a living from a combination of modelling and making fakes for a local antique dealer. She\\'d copy an obscure old painting out of a book, and then he\\'d take the copy and maltreat it to make it look old. [3]\\n\\nWhile I was a student at the Accademia I started painting still lives in my bedroom at night. These paintings were tiny, because the room was, and because I painted them on leftover scraps of canvas, which was all I could afford at the time. Painting still lives is different from painting people, because the subject, as its name suggests, can\\'t move. People can\\'t sit for more than about 15 minutes at a time, and when they do they don\\'t sit very still. So the traditional m.o. for painting people is to know how to paint a generic person, which you then modify to match the specific person you\\'re painting. Whereas a still life you can, if you want, copy pixel by pixel from what you\\'re seeing. You don\\'t want to stop there, of course, or you get merely photographic accuracy, and what makes a still life interesting is that it\\'s been through a head. You want to emphasize the visual cues that tell you, for example, that the reason the color changes suddenly at a certain point is that it\\'s the edge of an object. By subtly emphasizing such things you can make paintings that are more realistic than photographs not just in some metaphorical sense, but in the strict information-theoretic sense. [4]\\n\\nI liked painting still lives because I was curious about what I was seeing. In everyday life, we aren\\'t consciously aware of much we\\'re seeing. Most visual perception is handled by low-level processes that merely tell your brain \"that\\'s a water droplet\" without telling you details like where the lightest and darkest points are, or \"that\\'s a bush\" without telling you the shape and position of every leaf. This is a feature of brains, not a bug. In everyday life it would be distracting to notice every leaf on every bush. But when you have to paint something, you have to look more closely, and when you do there\\'s a lot to see. You can still be noticing new things after days of trying to paint something people usually take for granted, just as you can after days of trying to write an essay about something people usually take for granted.\\n\\nThis is not the only way to paint. I\\'m not 100% sure it\\'s even a good way to paint. But it seemed a good enough bet to be worth trying.\\n\\nOur teacher, professor Ulivi, was a nice guy. He could see I worked hard, and gave me a good grade, which he wrote down in a sort of passport each student had. But the Accademia wasn\\'t teaching me anything except Italian, and my money was running out, so at the end of the first year I went back to the US.\\n\\nI wanted to go back to RISD, but I was now broke and RISD was very expensive, so I decided to get a job for a year and then return to RISD the next fall. I got one at a company called Interleaf, which made software for creating documents. You mean like Microsoft Word? Exactly. That was how I learned that low end software tends to eat high end software. But Interleaf still had a few years to live yet. [5]\\n\\nInterleaf had done something pretty bold. Inspired by Emacs, they\\'d added a scripting language, and even made the scripting language a dialect of Lisp. Now they wanted a Lisp hacker to write things in it. This was the closest thing I\\'ve had to a normal job, and I hereby apologize to my boss and coworkers, because I was a bad employee. Their Lisp was the thinnest icing on a giant C cake, and since I didn\\'t know C and didn\\'t want to learn it, I never understood most of the software. Plus I was terribly irresponsible. This was back when a programming job meant showing up every day during certain working hours. That seemed unnatural to me, and on this point the rest of the world is coming around to my way of thinking, but at the time it caused a lot of friction. Toward the end of the year I spent much of my time surreptitiously working on On Lisp, which I had by this time gotten a contract to publish.\\n\\nThe good part was that I got paid huge amounts of money, especially by art student standards. In Florence, after paying my part of the rent, my budget for everything else had been $7 a day. Now I was getting paid more than 4 times that every hour, even when I was just sitting in a meeting. By living cheaply I not only managed to save enough to go back to RISD, but also paid off my college loans.\\n\\nI learned some useful things at Interleaf, though they were mostly about what not to do. I learned that it\\'s better for technology companies to be run by product people than sales people (though sales is a real skill and people who are good at it are really good at it), that it leads to bugs when code is edited by too many people, that cheap office space is no bargain if it\\'s depressing, that planned meetings are inferior to corridor conversations, that big, bureaucratic customers are a dangerous source of money, and that there\\'s not much overlap between conventional office hours and the optimal time for hacking, or conventional offices and the optimal place for it.\\n\\nBut the most important thing I learned, and which I used in both Viaweb and Y Combinator, is that the low end eats the high end: that it\\'s good to be the \"entry level\" option, even though that will be less prestigious, because if you\\'re not, someone else will be, and will squash you against the ceiling. Which in turn means that prestige is a danger sign.\\n\\nWhen I left to go back to RISD the next fall, I arranged to do freelance work for the group that did projects for customers, and this was how I survived for the next several years. When I came back to visit for a project later on, someone told me about a new thing called HTML, which was, as he described it, a derivative of SGML. Markup language enthusiasts were an occupational hazard at Interleaf and I ignored him, but this HTML thing later became a big part of my life.\\n\\nIn the fall of 1992 I moved back to Providence to continue at RISD. The foundation had merely been intro stuff, and the Accademia had been a (very civilized) joke. Now I was going to see what real art school was like. But alas it was more like the Accademia than not. Better organized, certainly, and a lot more expensive, but it was now becoming clear that art school did not bear the same relationship to art that medical school bore to medicine. At least not the painting department. The textile department, which my next door neighbor belonged to, seemed to be pretty rigorous. No doubt illustration and architecture were too. But painting was post-rigorous. Painting students were supposed to express themselves, which to the more worldly ones meant to try to cook up some sort of distinctive signature style.\\n\\nA signature style is the visual equivalent of what in show business is known as a \"schtick\": something that immediately identifies the work as yours and no one else\\'s. For example, when you see a painting that looks like a certain kind of cartoon, you know it\\'s by Roy Lichtenstein. So if you see a big painting of this type hanging in the apartment of a hedge fund manager, you know he paid millions of dollars for it. That\\'s not always why artists have a signature style, but it\\'s usually why buyers pay a lot for such work. [6]\\n\\nThere were plenty of earnest students too: kids who \"could draw\" in high school, and now had come to what was supposed to be the best art school in the country, to learn to draw even better. They tended to be confused and demoralized by what they found at RISD, but they kept going, because painting was what they did. I was not one of the kids who could draw in high school, but at RISD I was definitely closer to their tribe than the tribe of signature style seekers.\\n\\nI learned a lot in the color class I took at RISD, but otherwise I was basically teaching myself to paint, and I could do that for free. So in 1993 I dropped out. I hung around Providence for a bit, and then my college friend Nancy Parmet did me a big favor. A rent-controlled apartment in a building her mother owned in New York was becoming vacant. Did I want it? It wasn\\'t much more than my current place, and New York was supposed to be where the artists were. So yes, I wanted it! [7]\\n\\nAsterix comics begin by zooming in on a tiny corner of Roman Gaul that turns out not to be controlled by the Romans. You can do something similar on a map of New York City: if you zoom in on the Upper East Side, there\\'s a tiny corner that\\'s not rich, or at least wasn\\'t in 1993. It\\'s called Yorkville, and that was my new home. Now I was a New York artist — in the strictly technical sense of making paintings and living in New York.\\n\\nI was nervous about money, because I could sense that Interleaf was on the way down. Freelance Lisp hacking work was very rare, and I didn\\'t want to have to program in another language, which in those days would have meant C++ if I was lucky. So with my unerring nose for financial opportunity, I decided to write another book on Lisp. This would be a popular book, the sort of book that could be used as a textbook. I imagined myself living frugally off the royalties and spending all my time painting. (The painting on the cover of this book, ANSI Common Lisp, is one that I painted around this time.)\\n\\nThe best thing about New York for me was the presence of Idelle and Julian Weber. Idelle Weber was a painter, one of the early photorealists, and I\\'d taken her painting class at Harvard. I\\'ve never known a teacher more beloved by her students. Large numbers of former students kept in touch with her, including me. After I moved to New York I became her de facto studio assistant.\\n\\nShe liked to paint on big, square canvases, 4 to 5 feet on a side. One day in late 1994 as I was stretching one of these monsters there was something on the radio about a famous fund manager. He wasn\\'t that much older than me, and was super rich. The thought suddenly occurred to me: why don\\'t I become rich? Then I\\'ll be able to work on whatever I want.\\n\\nMeanwhile I\\'d been hearing more and more about this new thing called the World Wide Web. Robert Morris showed it to me when I visited him in Cambridge, where he was now in grad school at Harvard. It seemed to me that the web would be a big deal. I\\'d seen what graphical user interfaces had done for the popularity of microcomputers. It seemed like the web would do the same for the internet.\\n\\nIf I wanted to get rich, here was the next train leaving the station. I was right about that part. What I got wrong was the idea. I decided we should start a company to put art galleries online. I can\\'t honestly say, after reading so many Y Combinator applications, that this was the worst startup idea ever, but it was up there. Art galleries didn\\'t want to be online, and still don\\'t, not the fancy ones. That\\'s not how they sell. I wrote some software to generate web sites for galleries, and Robert wrote some to resize images and set up an http server to serve the pages. Then we tried to sign up galleries. To call this a difficult sale would be an understatement. It was difficult to give away. A few galleries let us make sites for them for free, but none paid us.\\n\\nThen some online stores started to appear, and I realized that except for the order buttons they were identical to the sites we\\'d been generating for galleries. This impressive-sounding thing called an \"internet storefront\" was something we already knew how to build.\\n\\nSo in the summer of 1995, after I submitted the camera-ready copy of ANSI Common Lisp to the publishers, we started trying to write software to build online stores. At first this was going to be normal desktop software, which in those days meant Windows software. That was an alarming prospect, because neither of us knew how to write Windows software or wanted to learn. We lived in the Unix world. But we decided we\\'d at least try writing a prototype store builder on Unix. Robert wrote a shopping cart, and I wrote a new site generator for stores — in Lisp, of course.\\n\\nWe were working out of Robert\\'s apartment in Cambridge. His roommate was away for big chunks of time, during which I got to sleep in his room. For some reason there was no bed frame or sheets, just a mattress on the floor. One morning as I was lying on this mattress I had an idea that made me sit up like a capital L. What if we ran the software on the server, and let users control it by clicking on links? Then we\\'d never have to write anything to run on users\\' computers. We could generate the sites on the same server we\\'d serve them from. Users wouldn\\'t need anything more than a browser.\\n\\nThis kind of software, known as a web app, is common now, but at the time it wasn\\'t clear that it was even possible. To find out, we decided to try making a version of our store builder that you could control through the browser. A couple days later, on August 12, we had one that worked. The UI was horrible, but it proved you could build a whole store through the browser, without any client software or typing anything into the command line on the server.\\n\\nNow we felt like we were really onto something. I had visions of a whole new generation of software working this way. You wouldn\\'t need versions, or ports, or any of that crap. At Interleaf there had been a whole group called Release Engineering that seemed to be at least as big as the group that actually wrote the software. Now you could just update the software right on the server.\\n\\nWe started a new company we called Viaweb, after the fact that our software worked via the web, and we got $10,000 in seed funding from Idelle\\'s husband Julian. In return for that and doing the initial legal work and giving us business advice, we gave him 10% of the company. Ten years later this deal became the model for Y Combinator\\'s. We knew founders needed something like this, because we\\'d needed it ourselves.\\n\\nAt this stage I had a negative net worth, because the thousand dollars or so I had in the bank was more than counterbalanced by what I owed the government in taxes. (Had I diligently set aside the proper proportion of the money I\\'d made consulting for Interleaf? No, I had not.) So although Robert had his graduate student stipend, I needed that seed funding to live on.\\n\\nWe originally hoped to launch in September, but we got more ambitious about the software as we worked on it. Eventually we managed to build a WYSIWYG site builder, in the sense that as you were creating pages, they looked exactly like the static ones that would be generated later, except that instead of leading to static pages, the links all referred to closures stored in a hash table on the server.\\n\\nIt helped to have studied art, because the main goal of an online store builder is to make users look legit, and the key to looking legit is high production values. If you get page layouts and fonts and colors right, you can make a guy running a store out of his bedroom look more legit than a big company.\\n\\n(If you\\'re curious why my site looks so old-fashioned, it\\'s because it\\'s still made with this software. It may look clunky today, but in 1996 it was the last word in slick.)\\n\\nIn September, Robert rebelled. \"We\\'ve been working on this for a month,\" he said, \"and it\\'s still not done.\" This is funny in retrospect, because he would still be working on it almost 3 years later. But I decided it might be prudent to recruit more programmers, and I asked Robert who else in grad school with him was really good. He recommended Trevor Blackwell, which surprised me at first, because at that point I knew Trevor mainly for his plan to reduce everything in his life to a stack of notecards, which he carried around with him. But Rtm was right, as usual. Trevor turned out to be a frighteningly effective hacker.\\n\\nIt was a lot of fun working with Robert and Trevor. They\\'re the two most independent-minded people I know, and in completely different ways. If you could see inside Rtm\\'s brain it would look like a colonial New England church, and if you could see inside Trevor\\'s it would look like the worst excesses of Austrian Rococo.\\n\\nWe opened for business, with 6 stores, in January 1996. It was just as well we waited a few months, because although we worried we were late, we were actually almost fatally early. There was a lot of talk in the press then about ecommerce, but not many people actually wanted online stores. [8]\\n\\nThere were three main parts to the software: the editor, which people used to build sites and which I wrote, the shopping cart, which Robert wrote, and the manager, which kept track of orders and statistics, and which Trevor wrote. In its time, the editor was one of the best general-purpose site builders. I kept the code tight and didn\\'t have to integrate with any other software except Robert\\'s and Trevor\\'s, so it was quite fun to work on. If all I\\'d had to do was work on this software, the next 3 years would have been the easiest of my life. Unfortunately I had to do a lot more, all of it stuff I was worse at than programming, and the next 3 years were instead the most stressful.\\n\\nThere were a lot of startups making ecommerce software in the second half of the 90s. We were determined to be the Microsoft Word, not the Interleaf. Which meant being easy to use and inexpensive. It was lucky for us that we were poor, because that caused us to make Viaweb even more inexpensive than we realized. We charged $100 a month for a small store and $300 a month for a big one. This low price was a big attraction, and a constant thorn in the sides of competitors, but it wasn\\'t because of some clever insight that we set the price low. We had no idea what businesses paid for things. $300 a month seemed like a lot of money to us.\\n\\nWe did a lot of things right by accident like that. For example, we did what\\'s now called \"doing things that don\\'t scale,\" although at the time we would have described it as \"being so lame that we\\'re driven to the most desperate measures to get users.\" The most common of which was building stores for them. This seemed particularly humiliating, since the whole raison d\\'etre of our software was that people could use it to make their own stores. But anything to get users.\\n\\nWe learned a lot more about retail than we wanted to know. For example, that if you could only have a small image of a man\\'s shirt (and all images were small then by present standards), it was better to have a closeup of the collar than a picture of the whole shirt. The reason I remember learning this was that it meant I had to rescan about 30 images of men\\'s shirts. My first set of scans were so beautiful too.\\n\\nThough this felt wrong, it was exactly the right thing to be doing. Building stores for users taught us about retail, and about how it felt to use our software. I was initially both mystified and repelled by \"business\" and thought we needed a \"business person\" to be in charge of it, but once we started to get users, I was converted, in much the same way I was converted to fatherhood once I had kids. Whatever users wanted, I was all theirs. Maybe one day we\\'d have so many users that I couldn\\'t scan their images for them, but in the meantime there was nothing more important to do.\\n\\nAnother thing I didn\\'t get at the time is that growth rate is the ultimate test of a startup. Our growth rate was fine. We had about 70 stores at the end of 1996 and about 500 at the end of 1997. I mistakenly thought the thing that mattered was the absolute number of users. And that is the thing that matters in the sense that that\\'s how much money you\\'re making, and if you\\'re not making enough, you might go out of business. But in the long term the growth rate takes care of the absolute number. If we\\'d been a startup I was advising at Y Combinator, I would have said: Stop being so stressed out, because you\\'re doing fine. You\\'re growing 7x a year. Just don\\'t hire too many more people and you\\'ll soon be profitable, and then you\\'ll control your own destiny.\\n\\nAlas I hired lots more people, partly because our investors wanted me to, and partly because that\\'s what startups did during the Internet Bubble. A company with just a handful of employees would have seemed amateurish. So we didn\\'t reach breakeven until about when Yahoo bought us in the summer of 1998. Which in turn meant we were at the mercy of investors for the entire life of the company. And since both we and our investors were noobs at startups, the result was a mess even by startup standards.\\n\\nIt was a huge relief when Yahoo bought us. In principle our Viaweb stock was valuable. It was a share in a business that was profitable and growing rapidly. But it didn\\'t feel very valuable to me; I had no idea how to value a business, but I was all too keenly aware of the near-death experiences we seemed to have every few months. Nor had I changed my grad student lifestyle significantly since we started. So when Yahoo bought us it felt like going from rags to riches. Since we were going to California, I bought a car, a yellow 1998 VW GTI. I remember thinking that its leather seats alone were by far the most luxurious thing I owned.\\n\\nThe next year, from the summer of 1998 to the summer of 1999, must have been the least productive of my life. I didn\\'t realize it at the time, but I was worn out from the effort and stress of running Viaweb. For a while after I got to California I tried to continue my usual m.o. of programming till 3 in the morning, but fatigue combined with Yahoo\\'s prematurely aged culture and grim cube farm in Santa Clara gradually dragged me down. After a few months it felt disconcertingly like working at Interleaf.\\n\\nYahoo had given us a lot of options when they bought us. At the time I thought Yahoo was so overvalued that they\\'d never be worth anything, but to my astonishment the stock went up 5x in the next year. I hung on till the first chunk of options vested, then in the summer of 1999 I left. It had been so long since I\\'d painted anything that I\\'d half forgotten why I was doing this. My brain had been entirely full of software and men\\'s shirts for 4 years. But I had done this to get rich so I could paint, I reminded myself, and now I was rich, so I should go paint.\\n\\nWhen I said I was leaving, my boss at Yahoo had a long conversation with me about my plans. I told him all about the kinds of pictures I wanted to paint. At the time I was touched that he took such an interest in me. Now I realize it was because he thought I was lying. My options at that point were worth about $2 million a month. If I was leaving that kind of money on the table, it could only be to go and start some new startup, and if I did, I might take people with me. This was the height of the Internet Bubble, and Yahoo was ground zero of it. My boss was at that moment a billionaire. Leaving then to start a new startup must have seemed to him an insanely, and yet also plausibly, ambitious plan.\\n\\nBut I really was quitting to paint, and I started immediately. There was no time to lose. I\\'d already burned 4 years getting rich. Now when I talk to founders who are leaving after selling their companies, my advice is always the same: take a vacation. That\\'s what I should have done, just gone off somewhere and done nothing for a month or two, but the idea never occurred to me.\\n\\nSo I tried to paint, but I just didn\\'t seem to have any energy or ambition. Part of the problem was that I didn\\'t know many people in California. I\\'d compounded this problem by buying a house up in the Santa Cruz Mountains, with a beautiful view but miles from anywhere. I stuck it out for a few more months, then in desperation I went back to New York, where unless you understand about rent control you\\'ll be surprised to hear I still had my apartment, sealed up like a tomb of my old life. Idelle was in New York at least, and there were other people trying to paint there, even though I didn\\'t know any of them.\\n\\nWhen I got back to New York I resumed my old life, except now I was rich. It was as weird as it sounds. I resumed all my old patterns, except now there were doors where there hadn\\'t been. Now when I was tired of walking, all I had to do was raise my hand, and (unless it was raining) a taxi would stop to pick me up. Now when I walked past charming little restaurants I could go in and order lunch. It was exciting for a while. Painting started to go better. I experimented with a new kind of still life where I\\'d paint one painting in the old way, then photograph it and print it, blown up, on canvas, and then use that as the underpainting for a second still life, painted from the same objects (which hopefully hadn\\'t rotted yet).\\n\\nMeanwhile I looked for an apartment to buy. Now I could actually choose what neighborhood to live in. Where, I asked myself and various real estate agents, is the Cambridge of New York? Aided by occasional visits to actual Cambridge, I gradually realized there wasn\\'t one. Huh.\\n\\nAround this time, in the spring of 2000, I had an idea. It was clear from our experience with Viaweb that web apps were the future. Why not build a web app for making web apps? Why not let people edit code on our server through the browser, and then host the resulting applications for them? [9] You could run all sorts of services on the servers that these applications could use just by making an API call: making and receiving phone calls, manipulating images, taking credit card payments, etc.\\n\\nI got so excited about this idea that I couldn\\'t think about anything else. It seemed obvious that this was the future. I didn\\'t particularly want to start another company, but it was clear that this idea would have to be embodied as one, so I decided to move to Cambridge and start it. I hoped to lure Robert into working on it with me, but there I ran into a hitch. Robert was now a postdoc at MIT, and though he\\'d made a lot of money the last time I\\'d lured him into working on one of my schemes, it had also been a huge time sink. So while he agreed that it sounded like a plausible idea, he firmly refused to work on it.\\n\\nHmph. Well, I\\'d do it myself then. I recruited Dan Giffin, who had worked for Viaweb, and two undergrads who wanted summer jobs, and we got to work trying to build what it\\'s now clear is about twenty companies and several open source projects worth of software. The language for defining applications would of course be a dialect of Lisp. But I wasn\\'t so naive as to assume I could spring an overt Lisp on a general audience; we\\'d hide the parentheses, like Dylan did.\\n\\nBy then there was a name for the kind of company Viaweb was, an \"application service provider,\" or ASP. This name didn\\'t last long before it was replaced by \"software as a service,\" but it was current for long enough that I named this new company after it: it was going to be called Aspra.\\n\\nI started working on the application builder, Dan worked on network infrastructure, and the two undergrads worked on the first two services (images and phone calls). But about halfway through the summer I realized I really didn\\'t want to run a company — especially not a big one, which it was looking like this would have to be. I\\'d only started Viaweb because I needed the money. Now that I didn\\'t need money anymore, why was I doing this? If this vision had to be realized as a company, then screw the vision. I\\'d build a subset that could be done as an open source project.\\n\\nMuch to my surprise, the time I spent working on this stuff was not wasted after all. After we started Y Combinator, I would often encounter startups working on parts of this new architecture, and it was very useful to have spent so much time thinking about it and even trying to write some of it.\\n\\nThe subset I would build as an open source project was the new Lisp, whose parentheses I now wouldn\\'t even have to hide. A lot of Lisp hackers dream of building a new Lisp, partly because one of the distinctive features of the language is that it has dialects, and partly, I think, because we have in our minds a Platonic form of Lisp that all existing dialects fall short of. I certainly did. So at the end of the summer Dan and I switched to working on this new dialect of Lisp, which I called Arc, in a house I bought in Cambridge.\\n\\nThe following spring, lightning struck. I was invited to give a talk at a Lisp conference, so I gave one about how we\\'d used Lisp at Viaweb. Afterward I put a postscript file of this talk online, on paulgraham.com, which I\\'d created years before using Viaweb but had never used for anything. In one day it got 30,000 page views. What on earth had happened? The referring urls showed that someone had posted it on Slashdot. [10]\\n\\nWow, I thought, there\\'s an audience. If I write something and put it on the web, anyone can read it. That may seem obvious now, but it was surprising then. In the print era there was a narrow channel to readers, guarded by fierce monsters known as editors. The only way to get an audience for anything you wrote was to get it published as a book, or in a newspaper or magazine. Now anyone could publish anything.\\n\\nThis had been possible in principle since 1993, but not many people had realized it yet. I had been intimately involved with building the infrastructure of the web for most of that time, and a writer as well, and it had taken me 8 years to realize it. Even then it took me several years to understand the implications. It meant there would be a whole new generation of essays. [11]\\n\\nIn the print era, the channel for publishing essays had been vanishingly small. Except for a few officially anointed thinkers who went to the right parties in New York, the only people allowed to publish essays were specialists writing about their specialties. There were so many essays that had never been written, because there had been no way to publish them. Now they could be, and I was going to write them. [12]\\n\\nI\\'ve worked on several different things, but to the extent there was a turning point where I figured out what to work on, it was when I started publishing essays online. From then on I knew that whatever else I did, I\\'d always write essays too.\\n\\nI knew that online essays would be a marginal medium at first. Socially they\\'d seem more like rants posted by nutjobs on their GeoCities sites than the genteel and beautifully typeset compositions published in The New Yorker. But by this point I knew enough to find that encouraging instead of discouraging.\\n\\nOne of the most conspicuous patterns I\\'ve noticed in my life is how well it has worked, for me at least, to work on things that weren\\'t prestigious. Still life has always been the least prestigious form of painting. Viaweb and Y Combinator both seemed lame when we started them. I still get the glassy eye from strangers when they ask what I\\'m writing, and I explain that it\\'s an essay I\\'m going to publish on my web site. Even Lisp, though prestigious intellectually in something like the way Latin is, also seems about as hip.\\n\\nIt\\'s not that unprestigious types of work are good per se. But when you find yourself drawn to some kind of work despite its current lack of prestige, it\\'s a sign both that there\\'s something real to be discovered there, and that you have the right kind of motives. Impure motives are a big danger for the ambitious. If anything is going to lead you astray, it will be the desire to impress people. So while working on things that aren\\'t prestigious doesn\\'t guarantee you\\'re on the right track, it at least guarantees you\\'re not on the most common type of wrong one.\\n\\nOver the next several years I wrote lots of essays about all kinds of different topics. O\\'Reilly reprinted a collection of them as a book, called Hackers & Painters after one of the essays in it. I also worked on spam filters, and did some more painting. I used to have dinners for a group of friends every thursday night, which taught me how to cook for groups. And I bought another building in Cambridge, a former candy factory (and later, twas said, porn studio), to use as an office.\\n\\nOne night in October 2003 there was a big party at my house. It was a clever idea of my friend Maria Daniels, who was one of the thursday diners. Three separate hosts would all invite their friends to one party. So for every guest, two thirds of the other guests would be people they didn\\'t know but would probably like. One of the guests was someone I didn\\'t know but would turn out to like a lot: a woman called Jessica Livingston. A couple days later I asked her out.\\n\\nJessica was in charge of marketing at a Boston investment bank. This bank thought it understood startups, but over the next year, as she met friends of mine from the startup world, she was surprised how different reality was. And how colorful their stories were. So she decided to compile a book of interviews with startup founders.\\n\\nWhen the bank had financial problems and she had to fire half her staff, she started looking for a new job. In early 2005 she interviewed for a marketing job at a Boston VC firm. It took them weeks to make up their minds, and during this time I started telling her about all the things that needed to be fixed about venture capital. They should make a larger number of smaller investments instead of a handful of giant ones, they should be funding younger, more technical founders instead of MBAs, they should let the founders remain as CEO, and so on.\\n\\nOne of my tricks for writing essays had always been to give talks. The prospect of having to stand up in front of a group of people and tell them something that won\\'t waste their time is a great spur to the imagination. When the Harvard Computer Society, the undergrad computer club, asked me to give a talk, I decided I would tell them how to start a startup. Maybe they\\'d be able to avoid the worst of the mistakes we\\'d made.\\n\\nSo I gave this talk, in the course of which I told them that the best sources of seed funding were successful startup founders, because then they\\'d be sources of advice too. Whereupon it seemed they were all looking expectantly at me. Horrified at the prospect of having my inbox flooded by business plans (if I\\'d only known), I blurted out \"But not me!\" and went on with the talk. But afterward it occurred to me that I should really stop procrastinating about angel investing. I\\'d been meaning to since Yahoo bought us, and now it was 7 years later and I still hadn\\'t done one angel investment.\\n\\nMeanwhile I had been scheming with Robert and Trevor about projects we could work on together. I missed working with them, and it seemed like there had to be something we could collaborate on.\\n\\nAs Jessica and I were walking home from dinner on March 11, at the corner of Garden and Walker streets, these three threads converged. Screw the VCs who were taking so long to make up their minds. We\\'d start our own investment firm and actually implement the ideas we\\'d been talking about. I\\'d fund it, and Jessica could quit her job and work for it, and we\\'d get Robert and Trevor as partners too. [13]\\n\\nOnce again, ignorance worked in our favor. We had no idea how to be angel investors, and in Boston in 2005 there were no Ron Conways to learn from. So we just made what seemed like the obvious choices, and some of the things we did turned out to be novel.\\n\\nThere are multiple components to Y Combinator, and we didn\\'t figure them all out at once. The part we got first was to be an angel firm. In those days, those two words didn\\'t go together. There were VC firms, which were organized companies with people whose job it was to make investments, but they only did big, million dollar investments. And there were angels, who did smaller investments, but these were individuals who were usually focused on other things and made investments on the side. And neither of them helped founders enough in the beginning. We knew how helpless founders were in some respects, because we remembered how helpless we\\'d been. For example, one thing Julian had done for us that seemed to us like magic was to get us set up as a company. We were fine writing fairly difficult software, but actually getting incorporated, with bylaws and stock and all that stuff, how on earth did you do that? Our plan was not only to make seed investments, but to do for startups everything Julian had done for us.\\n\\nYC was not organized as a fund. It was cheap enough to run that we funded it with our own money. That went right by 99% of readers, but professional investors are thinking \"Wow, that means they got all the returns.\" But once again, this was not due to any particular insight on our part. We didn\\'t know how VC firms were organized. It never occurred to us to try to raise a fund, and if it had, we wouldn\\'t have known where to start. [14]\\n\\nThe most distinctive thing about YC is the batch model: to fund a bunch of startups all at once, twice a year, and then to spend three months focusing intensively on trying to help them. That part we discovered by accident, not merely implicitly but explicitly due to our ignorance about investing. We needed to get experience as investors. What better way, we thought, than to fund a whole bunch of startups at once? We knew undergrads got temporary jobs at tech companies during the summer. Why not organize a summer program where they\\'d start startups instead? We wouldn\\'t feel guilty for being in a sense fake investors, because they would in a similar sense be fake founders. So while we probably wouldn\\'t make much money out of it, we\\'d at least get to practice being investors on them, and they for their part would probably have a more interesting summer than they would working at Microsoft.\\n\\nWe\\'d use the building I owned in Cambridge as our headquarters. We\\'d all have dinner there once a week — on tuesdays, since I was already cooking for the thursday diners on thursdays — and after dinner we\\'d bring in experts on startups to give talks.\\n\\nWe knew undergrads were deciding then about summer jobs, so in a matter of days we cooked up something we called the Summer Founders Program, and I posted an announcement on my site, inviting undergrads to apply. I had never imagined that writing essays would be a way to get \"deal flow,\" as investors call it, but it turned out to be the perfect source. [15] We got 225 applications for the Summer Founders Program, and we were surprised to find that a lot of them were from people who\\'d already graduated, or were about to that spring. Already this SFP thing was starting to feel more serious than we\\'d intended.\\n\\nWe invited about 20 of the 225 groups to interview in person, and from those we picked 8 to fund. They were an impressive group. That first batch included reddit, Justin Kan and Emmett Shear, who went on to found Twitch, Aaron Swartz, who had already helped write the RSS spec and would a few years later become a martyr for open access, and Sam Altman, who would later become the second president of YC. I don\\'t think it was entirely luck that the first batch was so good. You had to be pretty bold to sign up for a weird thing like the Summer Founders Program instead of a summer job at a legit place like Microsoft or Goldman Sachs.\\n\\nThe deal for startups was based on a combination of the deal we did with Julian ($10k for 10%) and what Robert said MIT grad students got for the summer ($6k). We invested $6k per founder, which in the typical two-founder case was $12k, in return for 6%. That had to be fair, because it was twice as good as the deal we ourselves had taken. Plus that first summer, which was really hot, Jessica brought the founders free air conditioners. [16]\\n\\nFairly quickly I realized that we had stumbled upon the way to scale startup funding. Funding startups in batches was more convenient for us, because it meant we could do things for a lot of startups at once, but being part of a batch was better for the startups too. It solved one of the biggest problems faced by founders: the isolation. Now you not only had colleagues, but colleagues who understood the problems you were facing and could tell you how they were solving them.\\n\\nAs YC grew, we started to notice other advantages of scale. The alumni became a tight community, dedicated to helping one another, and especially the current batch, whose shoes they remembered being in. We also noticed that the startups were becoming one another\\'s customers. We used to refer jokingly to the \"YC GDP,\" but as YC grows this becomes less and less of a joke. Now lots of startups get their initial set of customers almost entirely from among their batchmates.\\n\\nI had not originally intended YC to be a full-time job. I was going to do three things: hack, write essays, and work on YC. As YC grew, and I grew more excited about it, it started to take up a lot more than a third of my attention. But for the first few years I was still able to work on other things.\\n\\nIn the summer of 2006, Robert and I started working on a new version of Arc. This one was reasonably fast, because it was compiled into Scheme. To test this new Arc, I wrote Hacker News in it. It was originally meant to be a news aggregator for startup founders and was called Startup News, but after a few months I got tired of reading about nothing but startups. Plus it wasn\\'t startup founders we wanted to reach. It was future startup founders. So I changed the name to Hacker News and the topic to whatever engaged one\\'s intellectual curiosity.\\n\\nHN was no doubt good for YC, but it was also by far the biggest source of stress for me. If all I\\'d had to do was select and help founders, life would have been so easy. And that implies that HN was a mistake. Surely the biggest source of stress in one\\'s work should at least be something close to the core of the work. Whereas I was like someone who was in pain while running a marathon not from the exertion of running, but because I had a blister from an ill-fitting shoe. When I was dealing with some urgent problem during YC, there was about a 60% chance it had to do with HN, and a 40% chance it had do with everything else combined. [17]\\n\\nAs well as HN, I wrote all of YC\\'s internal software in Arc. But while I continued to work a good deal in Arc, I gradually stopped working on Arc, partly because I didn\\'t have time to, and partly because it was a lot less attractive to mess around with the language now that we had all this infrastructure depending on it. So now my three projects were reduced to two: writing essays and working on YC.\\n\\nYC was different from other kinds of work I\\'ve done. Instead of deciding for myself what to work on, the problems came to me. Every 6 months there was a new batch of startups, and their problems, whatever they were, became our problems. It was very engaging work, because their problems were quite varied, and the good founders were very effective. If you were trying to learn the most you could about startups in the shortest possible time, you couldn\\'t have picked a better way to do it.\\n\\nThere were parts of the job I didn\\'t like. Disputes between cofounders, figuring out when people were lying to us, fighting with people who maltreated the startups, and so on. But I worked hard even at the parts I didn\\'t like. I was haunted by something Kevin Hale once said about companies: \"No one works harder than the boss.\" He meant it both descriptively and prescriptively, and it was the second part that scared me. I wanted YC to be good, so if how hard I worked set the upper bound on how hard everyone else worked, I\\'d better work very hard.\\n\\nOne day in 2010, when he was visiting California for interviews, Robert Morris did something astonishing: he offered me unsolicited advice. I can only remember him doing that once before. One day at Viaweb, when I was bent over double from a kidney stone, he suggested that it would be a good idea for him to take me to the hospital. That was what it took for Rtm to offer unsolicited advice. So I remember his exact words very clearly. \"You know,\" he said, \"you should make sure Y Combinator isn\\'t the last cool thing you do.\"\\n\\nAt the time I didn\\'t understand what he meant, but gradually it dawned on me that he was saying I should quit. This seemed strange advice, because YC was doing great. But if there was one thing rarer than Rtm offering advice, it was Rtm being wrong. So this set me thinking. It was true that on my current trajectory, YC would be the last thing I did, because it was only taking up more of my attention. It had already eaten Arc, and was in the process of eating essays too. Either YC was my life\\'s work or I\\'d have to leave eventually. And it wasn\\'t, so I would.\\n\\nIn the summer of 2012 my mother had a stroke, and the cause turned out to be a blood clot caused by colon cancer. The stroke destroyed her balance, and she was put in a nursing home, but she really wanted to get out of it and back to her house, and my sister and I were determined to help her do it. I used to fly up to Oregon to visit her regularly, and I had a lot of time to think on those flights. On one of them I realized I was ready to hand YC over to someone else.\\n\\nI asked Jessica if she wanted to be president, but she didn\\'t, so we decided we\\'d try to recruit Sam Altman. We talked to Robert and Trevor and we agreed to make it a complete changing of the guard. Up till that point YC had been controlled by the original LLC we four had started. But we wanted YC to last for a long time, and to do that it couldn\\'t be controlled by the founders. So if Sam said yes, we\\'d let him reorganize YC. Robert and I would retire, and Jessica and Trevor would become ordinary partners.\\n\\nWhen we asked Sam if he wanted to be president of YC, initially he said no. He wanted to start a startup to make nuclear reactors. But I kept at it, and in October 2013 he finally agreed. We decided he\\'d take over starting with the winter 2014 batch. For the rest of 2013 I left running YC more and more to Sam, partly so he could learn the job, and partly because I was focused on my mother, whose cancer had returned.\\n\\nShe died on January 15, 2014. We knew this was coming, but it was still hard when it did.\\n\\nI kept working on YC till March, to help get that batch of startups through Demo Day, then I checked out pretty completely. (I still talk to alumni and to new startups working on things I\\'m interested in, but that only takes a few hours a week.)\\n\\nWhat should I do next? Rtm\\'s advice hadn\\'t included anything about that. I wanted to do something completely different, so I decided I\\'d paint. I wanted to see how good I could get if I really focused on it. So the day after I stopped working on YC, I started painting. I was rusty and it took a while to get back into shape, but it was at least completely engaging. [18]\\n\\nI spent most of the rest of 2014 painting. I\\'d never been able to work so uninterruptedly before, and I got to be better than I had been. Not good enough, but better. Then in November, right in the middle of a painting, I ran out of steam. Up till that point I\\'d always been curious to see how the painting I was working on would turn out, but suddenly finishing this one seemed like a chore. So I stopped working on it and cleaned my brushes and haven\\'t painted since. So far anyway.\\n\\nI realize that sounds rather wimpy. But attention is a zero sum game. If you can choose what to work on, and you choose a project that\\'s not the best one (or at least a good one) for you, then it\\'s getting in the way of another project that is. And at 50 there was some opportunity cost to screwing around.\\n\\nI started writing essays again, and wrote a bunch of new ones over the next few months. I even wrote a couple that weren\\'t about startups. Then in March 2015 I started working on Lisp again.\\n\\nThe distinctive thing about Lisp is that its core is a language defined by writing an interpreter in itself. It wasn\\'t originally intended as a programming language in the ordinary sense. It was meant to be a formal model of computation, an alternative to the Turing machine. If you want to write an interpreter for a language in itself, what\\'s the minimum set of predefined operators you need? The Lisp that John McCarthy invented, or more accurately discovered, is an answer to that question. [19]\\n\\nMcCarthy didn\\'t realize this Lisp could even be used to program computers till his grad student Steve Russell suggested it. Russell translated McCarthy\\'s interpreter into IBM 704 machine language, and from that point Lisp started also to be a programming language in the ordinary sense. But its origins as a model of computation gave it a power and elegance that other languages couldn\\'t match. It was this that attracted me in college, though I didn\\'t understand why at the time.\\n\\nMcCarthy\\'s 1960 Lisp did nothing more than interpret Lisp expressions. It was missing a lot of things you\\'d want in a programming language. So these had to be added, and when they were, they weren\\'t defined using McCarthy\\'s original axiomatic approach. That wouldn\\'t have been feasible at the time. McCarthy tested his interpreter by hand-simulating the execution of programs. But it was already getting close to the limit of interpreters you could test that way — indeed, there was a bug in it that McCarthy had overlooked. To test a more complicated interpreter, you\\'d have had to run it, and computers then weren\\'t powerful enough.\\n\\nNow they are, though. Now you could continue using McCarthy\\'s axiomatic approach till you\\'d defined a complete programming language. And as long as every change you made to McCarthy\\'s Lisp was a discoveredness-preserving transformation, you could, in principle, end up with a complete language that had this quality. Harder to do than to talk about, of course, but if it was possible in principle, why not try? So I decided to take a shot at it. It took 4 years, from March 26, 2015 to October 12, 2019. It was fortunate that I had a precisely defined goal, or it would have been hard to keep at it for so long.\\n\\nI wrote this new Lisp, called Bel, in itself in Arc. That may sound like a contradiction, but it\\'s an indication of the sort of trickery I had to engage in to make this work. By means of an egregious collection of hacks I managed to make something close enough to an interpreter written in itself that could actually run. Not fast, but fast enough to test.\\n\\nI had to ban myself from writing essays during most of this time, or I\\'d never have finished. In late 2015 I spent 3 months writing essays, and when I went back to working on Bel I could barely understand the code. Not so much because it was badly written as because the problem is so convoluted. When you\\'re working on an interpreter written in itself, it\\'s hard to keep track of what\\'s happening at what level, and errors can be practically encrypted by the time you get them.\\n\\nSo I said no more essays till Bel was done. But I told few people about Bel while I was working on it. So for years it must have seemed that I was doing nothing, when in fact I was working harder than I\\'d ever worked on anything. Occasionally after wrestling for hours with some gruesome bug I\\'d check Twitter or HN and see someone asking \"Does Paul Graham still code?\"\\n\\nWorking on Bel was hard but satisfying. I worked on it so intensively that at any given time I had a decent chunk of the code in my head and could write more there. I remember taking the boys to the coast on a sunny day in 2015 and figuring out how to deal with some problem involving continuations while I watched them play in the tide pools. It felt like I was doing life right. I remember that because I was slightly dismayed at how novel it felt. The good news is that I had more moments like this over the next few years.\\n\\nIn the summer of 2016 we moved to England. We wanted our kids to see what it was like living in another country, and since I was a British citizen by birth, that seemed the obvious choice. We only meant to stay for a year, but we liked it so much that we still live there. So most of Bel was written in England.\\n\\nIn the fall of 2019, Bel was finally finished. Like McCarthy\\'s original Lisp, it\\'s a spec rather than an implementation, although like McCarthy\\'s Lisp it\\'s a spec expressed as code.\\n\\nNow that I could write essays again, I wrote a bunch about topics I\\'d had stacked up. I kept writing essays through 2020, but I also started to think about other things I could work on. How should I choose what to do? Well, how had I chosen what to work on in the past? I wrote an essay for myself to answer that question, and I was surprised how long and messy the answer turned out to be. If this surprised me, who\\'d lived it, then I thought perhaps it would be interesting to other people, and encouraging to those with similarly messy lives. So I wrote a more detailed version for others to read, and this is the last sentence of it.\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nNotes\\n\\n[1] My experience skipped a step in the evolution of computers: time-sharing machines with interactive OSes. I went straight from batch processing to microcomputers, which made microcomputers seem all the more exciting.\\n\\n[2] Italian words for abstract concepts can nearly always be predicted from their English cognates (except for occasional traps like polluzione). It\\'s the everyday words that differ. So if you string together a lot of abstract concepts with a few simple verbs, you can make a little Italian go a long way.\\n\\n[3] I lived at Piazza San Felice 4, so my walk to the Accademia went straight down the spine of old Florence: past the Pitti, across the bridge, past Orsanmichele, between the Duomo and the Baptistery, and then up Via Ricasoli to Piazza San Marco. I saw Florence at street level in every possible condition, from empty dark winter evenings to sweltering summer days when the streets were packed with tourists.\\n\\n[4] You can of course paint people like still lives if you want to, and they\\'re willing. That sort of portrait is arguably the apex of still life painting, though the long sitting does tend to produce pained expressions in the sitters.\\n\\n[5] Interleaf was one of many companies that had smart people and built impressive technology, and yet got crushed by Moore\\'s Law. In the 1990s the exponential growth in the power of commodity (i.e. Intel) processors rolled up high-end, special-purpose hardware and software companies like a bulldozer.\\n\\n[6] The signature style seekers at RISD weren\\'t specifically mercenary. In the art world, money and coolness are tightly coupled. Anything expensive comes to be seen as cool, and anything seen as cool will soon become equally expensive.\\n\\n[7] Technically the apartment wasn\\'t rent-controlled but rent-stabilized, but this is a refinement only New Yorkers would know or care about. The point is that it was really cheap, less than half market price.\\n\\n[8] Most software you can launch as soon as it\\'s done. But when the software is an online store builder and you\\'re hosting the stores, if you don\\'t have any users yet, that fact will be painfully obvious. So before we could launch publicly we had to launch privately, in the sense of recruiting an initial set of users and making sure they had decent-looking stores.\\n\\n[9] We\\'d had a code editor in Viaweb for users to define their own page styles. They didn\\'t know it, but they were editing Lisp expressions underneath. But this wasn\\'t an app editor, because the code ran when the merchants\\' sites were generated, not when shoppers visited them.\\n\\n[10] This was the first instance of what is now a familiar experience, and so was what happened next, when I read the comments and found they were full of angry people. How could I claim that Lisp was better than other languages? Weren\\'t they all Turing complete? People who see the responses to essays I write sometimes tell me how sorry they feel for me, but I\\'m not exaggerating when I reply that it has always been like this, since the very beginning. It comes with the territory. An essay must tell readers things they don\\'t already know, and some people dislike being told such things.\\n\\n[11] People put plenty of stuff on the internet in the 90s of course, but putting something online is not the same as publishing it online. Publishing online means you treat the online version as the (or at least a) primary version.\\n\\n[12] There is a general lesson here that our experience with Y Combinator also teaches: Customs continue to constrain you long after the restrictions that caused them have disappeared. Customary VC practice had once, like the customs about publishing essays, been based on real constraints. Startups had once been much more expensive to start, and proportionally rare. Now they could be cheap and common, but the VCs\\' customs still reflected the old world, just as customs about writing essays still reflected the constraints of the print era.\\n\\nWhich in turn implies that people who are independent-minded (i.e. less influenced by custom) will have an advantage in fields affected by rapid change (where customs are more likely to be obsolete).\\n\\nHere\\'s an interesting point, though: you can\\'t always predict which fields will be affected by rapid change. Obviously software and venture capital will be, but who would have predicted that essay writing would be?\\n\\n[13] Y Combinator was not the original name. At first we were called Cambridge Seed. But we didn\\'t want a regional name, in case someone copied us in Silicon Valley, so we renamed ourselves after one of the coolest tricks in the lambda calculus, the Y combinator.\\n\\nI picked orange as our color partly because it\\'s the warmest, and partly because no VC used it. In 2005 all the VCs used staid colors like maroon, navy blue, and forest green, because they were trying to appeal to LPs, not founders. The YC logo itself is an inside joke: the Viaweb logo had been a white V on a red circle, so I made the YC logo a white Y on an orange square.\\n\\n[14] YC did become a fund for a couple years starting in 2009, because it was getting so big I could no longer afford to fund it personally. But after Heroku got bought we had enough money to go back to being self-funded.\\n\\n[15] I\\'ve never liked the term \"deal flow,\" because it implies that the number of new startups at any given time is fixed. This is not only false, but it\\'s the purpose of YC to falsify it, by causing startups to be founded that would not otherwise have existed.\\n\\n[16] She reports that they were all different shapes and sizes, because there was a run on air conditioners and she had to get whatever she could, but that they were all heavier than she could carry now.\\n\\n[17] Another problem with HN was a bizarre edge case that occurs when you both write essays and run a forum. When you run a forum, you\\'re assumed to see if not every conversation, at least every conversation involving you. And when you write essays, people post highly imaginative misinterpretations of them on forums. Individually these two phenomena are tedious but bearable, but the combination is disastrous. You actually have to respond to the misinterpretations, because the assumption that you\\'re present in the conversation means that not responding to any sufficiently upvoted misinterpretation reads as a tacit admission that it\\'s correct. But that in turn encourages more; anyone who wants to pick a fight with you senses that now is their chance.\\n\\n[18] The worst thing about leaving YC was not working with Jessica anymore. We\\'d been working on YC almost the whole time we\\'d known each other, and we\\'d neither tried nor wanted to separate it from our personal lives, so leaving was like pulling up a deeply rooted tree.\\n\\n[19] One way to get more precise about the concept of invented vs discovered is to talk about space aliens. Any sufficiently advanced alien civilization would certainly know about the Pythagorean theorem, for example. I believe, though with less certainty, that they would also know about the Lisp in McCarthy\\'s 1960 paper.\\n\\nBut if so there\\'s no reason to suppose that this is the limit of the language that might be known to them. Presumably aliens need numbers and errors and I/O too. So it seems likely there exists at least one path out of McCarthy\\'s Lisp along which discoveredness is preserved.\\n\\n\\n\\nThanks to Trevor Blackwell, John Collison, Patrick Collison, Daniel Gackle, Ralph Hazell, Jessica Livingston, Robert Morris, and Harj Taggar for reading drafts of this.\\n \\n'),\n", " Document(metadata={'source': '/content/data/state_of_the_union.txt'}, page_content='<PERSON><PERSON> Speaker, <PERSON><PERSON> Vice President, our First Lady and Second Gentleman. Members of Congress and the Cabinet. Justices of the Supreme Court. My fellow Americans.  \\n\\nLast year COVID-19 kept us apart. This year we are finally together again. \\n\\nTonight, we meet as Democrats Republicans and Independents. But most importantly as Americans. \\n\\nWith a duty to one another to the American people to the Constitution. \\n\\nAnd with an unwavering resolve that freedom will always triumph over tyranny. \\n\\nSix days ago, Russia’s <PERSON> sought to shake the foundations of the free world thinking he could make it bend to his menacing ways. But he badly miscalculated. \\n\\nHe thought he could roll into Ukraine and the world would roll over. Instead he met a wall of strength he never imagined. \\n\\nHe met the Ukrainian people. \\n\\nFrom President <PERSON><PERSON><PERSON><PERSON> to every Ukrainian, their fearlessness, their courage, their determination, inspires the world. \\n\\nGroups of citizens blocking tanks with their bodies. Everyone from students to retirees teachers turned soldiers defending their homeland. \\n\\nIn this struggle as President <PERSON><PERSON><PERSON><PERSON> said in his speech to the European Parliament “Light will win over darkness.” The Ukrainian Ambassador to the United States is here tonight. \\n\\nLet each of us here tonight in this Chamber send an unmistakable signal to Ukraine and to the world. \\n\\nPlease rise if you are able and show that, Yes, we the United States of America stand with the Ukrainian people. \\n\\nThroughout our history we’ve learned this lesson when dictators do not pay a price for their aggression they cause more chaos.   \\n\\nThey keep moving.   \\n\\nAnd the costs and the threats to America and the world keep rising.   \\n\\nThat’s why the NATO Alliance was created to secure peace and stability in Europe after World War 2. \\n\\nThe United States is a member along with 29 other nations. \\n\\nIt matters. American diplomacy matters. American resolve matters. \\n\\nPutin’s latest attack on Ukraine was premeditated and unprovoked. \\n\\nHe rejected repeated efforts at diplomacy. \\n\\nHe thought the West and NATO wouldn’t respond. And he thought he could divide us at home. Putin was wrong. We were ready.  Here is what we did.   \\n\\nWe prepared extensively and carefully. \\n\\nWe spent months building a coalition of other freedom-loving nations from Europe and the Americas to Asia and Africa to confront Putin. \\n\\nI spent countless hours unifying our European allies. We shared with the world in advance what we knew Putin was planning and precisely how he would try to falsely justify his aggression.  \\n\\nWe countered Russia’s lies with truth.   \\n\\nAnd now that he has acted the free world is holding him accountable. \\n\\nAlong with twenty-seven members of the European Union including France, Germany, Italy, as well as countries like the United Kingdom, Canada, Japan, Korea, Australia, New Zealand, and many others, even Switzerland. \\n\\nWe are inflicting pain on Russia and supporting the people of Ukraine. Putin is now isolated from the world more than ever. \\n\\nTogether with our allies –we are right now enforcing powerful economic sanctions. \\n\\nWe are cutting off Russia’s largest banks from the international financial system.  \\n\\nPreventing Russia’s central bank from defending the Russian Ruble making Putin’s $630 Billion “war fund” worthless.   \\n\\nWe are choking off Russia’s access to technology that will sap its economic strength and weaken its military for years to come.  \\n\\nTonight I say to the Russian oligarchs and corrupt leaders who have bilked billions of dollars off this violent regime no more. \\n\\nThe U.S. Department of Justice is assembling a dedicated task force to go after the crimes of Russian oligarchs.  \\n\\nWe are joining with our European allies to find and seize your yachts your luxury apartments your private jets. We are coming for your ill-begotten gains. \\n\\nAnd tonight I am announcing that we will join our allies in closing off American air space to all Russian flights – further isolating Russia – and adding an additional squeeze –on their economy. The Ruble has lost 30% of its value. \\n\\nThe Russian stock market has lost 40% of its value and trading remains suspended. Russia’s economy is reeling and Putin alone is to blame. \\n\\nTogether with our allies we are providing support to the Ukrainians in their fight for freedom. Military assistance. Economic assistance. Humanitarian assistance. \\n\\nWe are giving more than $1 Billion in direct assistance to Ukraine. \\n\\nAnd we will continue to aid the Ukrainian people as they defend their country and to help ease their suffering.  \\n\\nLet me be clear, our forces are not engaged and will not engage in conflict with Russian forces in Ukraine.  \\n\\nOur forces are not going to Europe to fight in Ukraine, but to defend our NATO Allies – in the event that Putin decides to keep moving west.  \\n\\nFor that purpose we’ve mobilized American ground forces, air squadrons, and ship deployments to protect NATO countries including Poland, Romania, Latvia, Lithuania, and Estonia. \\n\\nAs I have made crystal clear the United States and our Allies will defend every inch of territory of NATO countries with the full force of our collective power.  \\n\\nAnd we remain clear-eyed. The Ukrainians are fighting back with pure courage. But the next few days weeks, months, will be hard on them.  \\n\\nPutin has unleashed violence and chaos.  But while he may make gains on the battlefield – he will pay a continuing high price over the long run. \\n\\nAnd a proud Ukrainian people, who have known 30 years  of independence, have repeatedly shown that they will not tolerate anyone who tries to take their country backwards.  \\n\\nTo all Americans, I will be honest with you, as I’ve always promised. A Russian dictator, invading a foreign country, has costs around the world. \\n\\nAnd I’m taking robust action to make sure the pain of our sanctions  is targeted at Russia’s economy. And I will use every tool at our disposal to protect American businesses and consumers. \\n\\nTonight, I can announce that the United States has worked with 30 other countries to release 60 Million barrels of oil from reserves around the world.  \\n\\nAmerica will lead that effort, releasing 30 Million barrels from our own Strategic Petroleum Reserve. And we stand ready to do more if necessary, unified with our allies.  \\n\\nThese steps will help blunt gas prices here at home. And I know the news about what’s happening can seem alarming. \\n\\nBut I want you to know that we are going to be okay. \\n\\nWhen the history of this era is written Putin’s war on Ukraine will have left Russia weaker and the rest of the world stronger. \\n\\nWhile it shouldn’t have taken something so terrible for people around the world to see what’s at stake now everyone sees it clearly. \\n\\nWe see the unity among leaders of nations and a more unified Europe a more unified West. And we see unity among the people who are gathering in cities in large crowds around the world even in Russia to demonstrate their support for Ukraine.  \\n\\nIn the battle between democracy and autocracy, democracies are rising to the moment, and the world is clearly choosing the side of peace and security. \\n\\nThis is a real test. It’s going to take time. So let us continue to draw inspiration from the iron will of the Ukrainian people. \\n\\nTo our fellow Ukrainian Americans who forge a deep bond that connects our two nations we stand with you. \\n\\nPutin may circle Kyiv with tanks, but he will never gain the hearts and souls of the Ukrainian people. \\n\\nHe will never extinguish their love of freedom. He will never weaken the resolve of the free world. \\n\\nWe meet tonight in an America that has lived through two of the hardest years this nation has ever faced. \\n\\nThe pandemic has been punishing. \\n\\nAnd so many families are living paycheck to paycheck, struggling to keep up with the rising cost of food, gas, housing, and so much more. \\n\\nI understand. \\n\\nI remember when my Dad had to leave our home in Scranton, Pennsylvania to find work. I grew up in a family where if the price of food went up, you felt it. \\n\\nThat’s why one of the first things I did as President was fight to pass the American Rescue Plan.  \\n\\nBecause people were hurting. We needed to act, and we did. \\n\\nFew pieces of legislation have done more in a critical moment in our history to lift us out of crisis. \\n\\nIt fueled our efforts to vaccinate the nation and combat COVID-19. It delivered immediate economic relief for tens of millions of Americans.  \\n\\nHelped put food on their table, keep a roof over their heads, and cut the cost of health insurance. \\n\\nAnd as my Dad used to say, it gave people a little breathing room. \\n\\nAnd unlike the $2 Trillion tax cut passed in the previous administration that benefitted the top 1% of Americans, the American Rescue Plan helped working people—and left no one behind. \\n\\nAnd it worked. It created jobs. Lots of jobs. \\n\\nIn fact—our economy created over 6.5 Million new jobs just last year, more jobs created in one year  \\nthan ever before in the history of America. \\n\\nOur economy grew at a rate of 5.7% last year, the strongest growth in nearly 40 years, the first step in bringing fundamental change to an economy that hasn’t worked for the working people of this nation for too long.  \\n\\nFor the past 40 years we were told that if we gave tax breaks to those at the very top, the benefits would trickle down to everyone else. \\n\\nBut that trickle-down theory led to weaker economic growth, lower wages, bigger deficits, and the widest gap between those at the top and everyone else in nearly a century. \\n\\nVice President Harris and I ran for office with a new economic vision for America. \\n\\nInvest in America. Educate Americans. Grow the workforce. Build the economy from the bottom up  \\nand the middle out, not from the top down.  \\n\\nBecause we know that when the middle class grows, the poor have a ladder up and the wealthy do very well. \\n\\nAmerica used to have the best roads, bridges, and airports on Earth. \\n\\nNow our infrastructure is ranked 13th in the world. \\n\\nWe won’t be able to compete for the jobs of the 21st Century if we don’t fix that. \\n\\nThat’s why it was so important to pass the Bipartisan Infrastructure Law—the most sweeping investment to rebuild America in history. \\n\\nThis was a bipartisan effort, and I want to thank the members of both parties who worked to make it happen. \\n\\nWe’re done talking about infrastructure weeks. \\n\\nWe’re going to have an infrastructure decade. \\n\\nIt is going to transform America and put us on a path to win the economic competition of the 21st Century that we face with the rest of the world—particularly with China.  \\n\\nAs I’ve told Xi Jinping, it is never a good bet to bet against the American people. \\n\\nWe’ll create good jobs for millions of Americans, modernizing roads, airports, ports, and waterways all across America. \\n\\nAnd we’ll do it all to withstand the devastating effects of the climate crisis and promote environmental justice. \\n\\nWe’ll build a national network of 500,000 electric vehicle charging stations, begin to replace poisonous lead pipes—so every child—and every American—has clean water to drink at home and at school, provide affordable high-speed internet for every American—urban, suburban, rural, and tribal communities. \\n\\n4,000 projects have already been announced. \\n\\nAnd tonight, I’m announcing that this year we will start fixing over 65,000 miles of highway and 1,500 bridges in disrepair. \\n\\nWhen we use taxpayer dollars to rebuild America – we are going to Buy American: buy American products to support American jobs. \\n\\nThe federal government spends about $600 Billion a year to keep the country safe and secure. \\n\\nThere’s been a law on the books for almost a century \\nto make sure taxpayers’ dollars support American jobs and businesses. \\n\\nEvery Administration says they’ll do it, but we are actually doing it. \\n\\nWe will buy American to make sure everything from the deck of an aircraft carrier to the steel on highway guardrails are made in America. \\n\\nBut to compete for the best jobs of the future, we also need to level the playing field with China and other competitors. \\n\\nThat’s why it is so important to pass the Bipartisan Innovation Act sitting in Congress that will make record investments in emerging technologies and American manufacturing. \\n\\nLet me give you one example of why it’s so important to pass it. \\n\\nIf you travel 20 miles east of Columbus, Ohio, you’ll find 1,000 empty acres of land. \\n\\nIt won’t look like much, but if you stop and look closely, you’ll see a “Field of dreams,” the ground on which America’s future will be built. \\n\\nThis is where Intel, the American company that helped build Silicon Valley, is going to build its $20 billion semiconductor “mega site”. \\n\\nUp to eight state-of-the-art factories in one place. 10,000 new good-paying jobs. \\n\\nSome of the most sophisticated manufacturing in the world to make computer chips the size of a fingertip that power the world and our everyday lives. \\n\\nSmartphones. The Internet. Technology we have yet to invent. \\n\\nBut that’s just the beginning. \\n\\nIntel’s CEO, Pat Gelsinger, who is here tonight, told me they are ready to increase their investment from  \\n$20 billion to $100 billion. \\n\\nThat would be one of the biggest investments in manufacturing in American history. \\n\\nAnd all they’re waiting for is for you to pass this bill. \\n\\nSo let’s not wait any longer. Send it to my desk. I’ll sign it.  \\n\\nAnd we will really take off. \\n\\nAnd Intel is not alone. \\n\\nThere’s something happening in America. \\n\\nJust look around and you’ll see an amazing story. \\n\\nThe rebirth of the pride that comes from stamping products “Made In America.” The revitalization of American manufacturing.   \\n\\nCompanies are choosing to build new factories here, when just a few years ago, they would have built them overseas. \\n\\nThat’s what is happening. Ford is investing $11 billion to build electric vehicles, creating 11,000 jobs across the country. \\n\\nGM is making the largest investment in its history—$7 billion to build electric vehicles, creating 4,000 jobs in Michigan. \\n\\nAll told, we created 369,000 new manufacturing jobs in America just last year. \\n\\nPowered by people I’ve met like JoJo Burgess, from generations of union steelworkers from Pittsburgh, who’s here with us tonight. \\n\\nAs Ohio Senator Sherrod Brown says, “It’s time to bury the label “Rust Belt.” \\n\\nIt’s time. \\n\\nBut with all the bright spots in our economy, record job growth and higher wages, too many families are struggling to keep up with the bills.  \\n\\nInflation is robbing them of the gains they might otherwise feel. \\n\\nI get it. That’s why my top priority is getting prices under control. \\n\\nLook, our economy roared back faster than most predicted, but the pandemic meant that businesses had a hard time hiring enough workers to keep up production in their factories. \\n\\nThe pandemic also disrupted global supply chains. \\n\\nWhen factories close, it takes longer to make goods and get them from the warehouse to the store, and prices go up. \\n\\nLook at cars. \\n\\nLast year, there weren’t enough semiconductors to make all the cars that people wanted to buy. \\n\\nAnd guess what, prices of automobiles went up. \\n\\nSo—we have a choice. \\n\\nOne way to fight inflation is to drive down wages and make Americans poorer.  \\n\\nI have a better plan to fight inflation. \\n\\nLower your costs, not your wages. \\n\\nMake more cars and semiconductors in America. \\n\\nMore infrastructure and innovation in America. \\n\\nMore goods moving faster and cheaper in America. \\n\\nMore jobs where you can earn a good living in America. \\n\\nAnd instead of relying on foreign supply chains, let’s make it in America. \\n\\nEconomists call it “increasing the productive capacity of our economy.” \\n\\nI call it building a better America. \\n\\nMy plan to fight inflation will lower your costs and lower the deficit. \\n\\n17 Nobel laureates in economics say my plan will ease long-term inflationary pressures. Top business leaders and most Americans support my plan. And here’s the plan: \\n\\nFirst – cut the cost of prescription drugs. Just look at insulin. One in ten Americans has diabetes. In Virginia, I met a 13-year-old boy named Joshua Davis.  \\n\\nHe and his Dad both have Type 1 diabetes, which means they need insulin every day. Insulin costs about $10 a vial to make.  \\n\\nBut drug companies charge families like Joshua and his Dad up to 30 times more. I spoke with Joshua’s mom. \\n\\nImagine what it’s like to look at your child who needs insulin and have no idea how you’re going to pay for it.  \\n\\nWhat it does to your dignity, your ability to look your child in the eye, to be the parent you expect to be. \\n\\nJoshua is here with us tonight. Yesterday was his birthday. Happy birthday, buddy.  \\n\\nFor Joshua, and for the 200,000 other young people with Type 1 diabetes, let’s cap the cost of insulin at $35 a month so everyone can afford it.  \\n\\nDrug companies will still do very well. And while we’re at it let Medicare negotiate lower prices for prescription drugs, like the VA already does. \\n\\nLook, the American Rescue Plan is helping millions of families on Affordable Care Act plans save $2,400 a year on their health care premiums. Let’s close the coverage gap and make those savings permanent. \\n\\nSecond – cut energy costs for families an average of $500 a year by combatting climate change.  \\n\\nLet’s provide investments and tax credits to weatherize your homes and businesses to be energy efficient and you get a tax credit; double America’s clean energy production in solar, wind, and so much more;  lower the price of electric vehicles, saving you another $80 a month because you’ll never have to pay at the gas pump again. \\n\\nThird – cut the cost of child care. Many families pay up to $14,000 a year for child care per child.  \\n\\nMiddle-class and working families shouldn’t have to pay more than 7% of their income for care of young children.  \\n\\nMy plan will cut the cost in half for most families and help parents, including millions of women, who left the workforce during the pandemic because they couldn’t afford child care, to be able to get back to work. \\n\\nMy plan doesn’t stop there. It also includes home and long-term care. More affordable housing. And Pre-K for every 3- and 4-year-old.  \\n\\nAll of these will lower costs. \\n\\nAnd under my plan, nobody earning less than $400,000 a year will pay an additional penny in new taxes. Nobody.  \\n\\nThe one thing all Americans agree on is that the tax system is not fair. We have to fix it.  \\n\\nI’m not looking to punish anyone. But let’s make sure corporations and the wealthiest Americans start paying their fair share. \\n\\nJust last year, 55 Fortune 500 corporations earned $40 billion in profits and paid zero dollars in federal income tax.  \\n\\nThat’s simply not fair. That’s why I’ve proposed a 15% minimum tax rate for corporations. \\n\\nWe got more than 130 countries to agree on a global minimum tax rate so companies can’t get out of paying their taxes at home by shipping jobs and factories overseas. \\n\\nThat’s why I’ve proposed closing loopholes so the very wealthy don’t pay a lower tax rate than a teacher or a firefighter.  \\n\\nSo that’s my plan. It will grow the economy and lower costs for families. \\n\\nSo what are we waiting for? Let’s get this done. And while you’re at it, confirm my nominees to the Federal Reserve, which plays a critical role in fighting inflation.  \\n\\nMy plan will not only lower costs to give families a fair shot, it will lower the deficit. \\n\\nThe previous Administration not only ballooned the deficit with tax cuts for the very wealthy and corporations, it undermined the watchdogs whose job was to keep pandemic relief funds from being wasted. \\n\\nBut in my administration, the watchdogs have been welcomed back. \\n\\nWe’re going after the criminals who stole billions in relief money meant for small businesses and millions of Americans.  \\n\\nAnd tonight, I’m announcing that the Justice Department will name a chief prosecutor for pandemic fraud. \\n\\nBy the end of this year, the deficit will be down to less than half what it was before I took office.  \\n\\nThe only president ever to cut the deficit by more than one trillion dollars in a single year. \\n\\nLowering your costs also means demanding more competition. \\n\\nI’m a capitalist, but capitalism without competition isn’t capitalism. \\n\\nIt’s exploitation—and it drives up prices. \\n\\nWhen corporations don’t have to compete, their profits go up, your prices go up, and small businesses and family farmers and ranchers go under. \\n\\nWe see it happening with ocean carriers moving goods in and out of America. \\n\\nDuring the pandemic, these foreign-owned companies raised prices by as much as 1,000% and made record profits. \\n\\nTonight, I’m announcing a crackdown on these companies overcharging American businesses and consumers. \\n\\nAnd as Wall Street firms take over more nursing homes, quality in those homes has gone down and costs have gone up.  \\n\\nThat ends on my watch. \\n\\nMedicare is going to set higher standards for nursing homes and make sure your loved ones get the care they deserve and expect. \\n\\nWe’ll also cut costs and keep the economy going strong by giving workers a fair shot, provide more training and apprenticeships, hire them based on their skills not degrees. \\n\\nLet’s pass the Paycheck Fairness Act and paid leave.  \\n\\nRaise the minimum wage to $15 an hour and extend the Child Tax Credit, so no one has to raise a family in poverty. \\n\\nLet’s increase Pell Grants and increase our historic support of HBCUs, and invest in what Jill—our First Lady who teaches full-time—calls America’s best-kept secret: community colleges. \\n\\nAnd let’s pass the PRO Act when a majority of workers want to form a union—they shouldn’t be stopped.  \\n\\nWhen we invest in our workers, when we build the economy from the bottom up and the middle out together, we can do something we haven’t done in a long time: build a better America. \\n\\nFor more than two years, COVID-19 has impacted every decision in our lives and the life of the nation. \\n\\nAnd I know you’re tired, frustrated, and exhausted. \\n\\nBut I also know this. \\n\\nBecause of the progress we’ve made, because of your resilience and the tools we have, tonight I can say  \\nwe are moving forward safely, back to more normal routines.  \\n\\nWe’ve reached a new moment in the fight against COVID-19, with severe cases down to a level not seen since last July.  \\n\\nJust a few days ago, the Centers for Disease Control and Prevention—the CDC—issued new mask guidelines. \\n\\nUnder these new guidelines, most Americans in most of the country can now be mask free.   \\n\\nAnd based on the projections, more of the country will reach that point across the next couple of weeks. \\n\\nThanks to the progress we have made this past year, COVID-19 need no longer control our lives.  \\n\\nI know some are talking about “living with COVID-19”. Tonight – I say that we will never just accept living with COVID-19. \\n\\nWe will continue to combat the virus as we do other diseases. And because this is a virus that mutates and spreads, we will stay on guard. \\n\\nHere are four common sense steps as we move forward safely.  \\n\\nFirst, stay protected with vaccines and treatments. We know how incredibly effective vaccines are. If you’re vaccinated and boosted you have the highest degree of protection. \\n\\nWe will never give up on vaccinating more Americans. Now, I know parents with kids under 5 are eager to see a vaccine authorized for their children. \\n\\nThe scientists are working hard to get that done and we’ll be ready with plenty of vaccines when they do. \\n\\nWe’re also ready with anti-viral treatments. If you get COVID-19, the Pfizer pill reduces your chances of ending up in the hospital by 90%.  \\n\\nWe’ve ordered more of these pills than anyone in the world. And Pfizer is working overtime to get us 1 Million pills this month and more than double that next month.  \\n\\nAnd we’re launching the “Test to Treat” initiative so people can get tested at a pharmacy, and if they’re positive, receive antiviral pills on the spot at no cost.  \\n\\nIf you’re immunocompromised or have some other vulnerability, we have treatments and free high-quality masks. \\n\\nWe’re leaving no one behind or ignoring anyone’s needs as we move forward. \\n\\nAnd on testing, we have made hundreds of millions of tests available for you to order for free.   \\n\\nEven if you already ordered free tests tonight, I am announcing that you can order more from covidtests.gov starting next week. \\n\\nSecond – we must prepare for new variants. Over the past year, we’ve gotten much better at detecting new variants. \\n\\nIf necessary, we’ll be able to deploy new vaccines within 100 days instead of many more months or years.  \\n\\nAnd, if Congress provides the funds we need, we’ll have new stockpiles of tests, masks, and pills ready if needed. \\n\\nI cannot promise a new variant won’t come. But I can promise you we’ll do everything within our power to be ready if it does.  \\n\\nThird – we can end the shutdown of schools and businesses. We have the tools we need. \\n\\nIt’s time for Americans to get back to work and fill our great downtowns again.  People working from home can feel safe to begin to return to the office.   \\n\\nWe’re doing that here in the federal government. The vast majority of federal workers will once again work in person. \\n\\nOur schools are open. Let’s keep it that way. Our kids need to be in school. \\n\\nAnd with 75% of adult Americans fully vaccinated and hospitalizations down by 77%, most Americans can remove their masks, return to work, stay in the classroom, and move forward safely. \\n\\nWe achieved this because we provided free vaccines, treatments, tests, and masks. \\n\\nOf course, continuing this costs money. \\n\\nI will soon send Congress a request. \\n\\nThe vast majority of Americans have used these tools and may want to again, so I expect Congress to pass it quickly.   \\n\\nFourth, we will continue vaccinating the world.     \\n\\nWe’ve sent 475 Million vaccine doses to 112 countries, more than any other nation. \\n\\nAnd we won’t stop. \\n\\nWe have lost so much to COVID-19. Time with one another. And worst of all, so much loss of life. \\n\\nLet’s use this moment to reset. Let’s stop looking at COVID-19 as a partisan dividing line and see it for what it is: A God-awful disease.  \\n\\nLet’s stop seeing each other as enemies, and start seeing each other for who we really are: Fellow Americans.  \\n\\nWe can’t change how divided we’ve been. But we can change how we move forward—on COVID-19 and other issues we must face together. \\n\\nI recently visited the New York City Police Department days after the funerals of Officer Wilbert Mora and his partner, Officer Jason Rivera. \\n\\nThey were responding to a 9-1-1 call when a man shot and killed them with a stolen gun. \\n\\nOfficer Mora was 27 years old. \\n\\nOfficer Rivera was 22. \\n\\nBoth Dominican Americans who’d grown up on the same streets they later chose to patrol as police officers. \\n\\nI spoke with their families and told them that we are forever in debt for their sacrifice, and we will carry on their mission to restore the trust and safety every community deserves. \\n\\nI’ve worked on these issues a long time. \\n\\nI know what works: Investing in crime prevention and community police officers who’ll walk the beat, who’ll know the neighborhood, and who can restore trust and safety. \\n\\nSo let’s not abandon our streets. Or choose between safety and equal justice. \\n\\nLet’s come together to protect our communities, restore trust, and hold law enforcement accountable. \\n\\nThat’s why the Justice Department required body cameras, banned chokeholds, and restricted no-knock warrants for its officers. \\n\\nThat’s why the American Rescue Plan provided $350 Billion that cities, states, and counties can use to hire more police and invest in proven strategies like community violence interruption—trusted messengers breaking the cycle of violence and trauma and giving young people hope.  \\n\\nWe should all agree: The answer is not to Defund the police. The answer is to FUND the police with the resources and training they need to protect our communities. \\n\\nI ask Democrats and Republicans alike: Pass my budget and keep our neighborhoods safe.  \\n\\nAnd I will keep doing everything in my power to crack down on gun trafficking and ghost guns you can buy online and make at home—they have no serial numbers and can’t be traced. \\n\\nAnd I ask Congress to pass proven measures to reduce gun violence. Pass universal background checks. Why should anyone on a terrorist list be able to purchase a weapon? \\n\\nBan assault weapons and high-capacity magazines. \\n\\nRepeal the liability shield that makes gun manufacturers the only industry in America that can’t be sued. \\n\\nThese laws don’t infringe on the Second Amendment. They save lives. \\n\\nThe most fundamental right in America is the right to vote – and to have it counted. And it’s under assault. \\n\\nIn state after state, new laws have been passed, not only to suppress the vote, but to subvert entire elections. \\n\\nWe cannot let this happen. \\n\\nTonight. I call on the Senate to: Pass the Freedom to Vote Act. Pass the John Lewis Voting Rights Act. And while you’re at it, pass the Disclose Act so Americans can know who is funding our elections. \\n\\nTonight, I’d like to honor someone who has dedicated his life to serve this country: Justice Stephen Breyer—an Army veteran, Constitutional scholar, and retiring Justice of the United States Supreme Court. Justice Breyer, thank you for your service. \\n\\nOne of the most serious constitutional responsibilities a President has is nominating someone to serve on the United States Supreme Court. \\n\\nAnd I did that 4 days ago, when I nominated Circuit Court of Appeals Judge Ketanji Brown Jackson. One of our nation’s top legal minds, who will continue Justice Breyer’s legacy of excellence. \\n\\nA former top litigator in private practice. A former federal public defender. And from a family of public school educators and police officers. A consensus builder. Since she’s been nominated, she’s received a broad range of support—from the Fraternal Order of Police to former judges appointed by Democrats and Republicans. \\n\\nAnd if we are to advance liberty and justice, we need to secure the Border and fix the immigration system. \\n\\nWe can do both. At our border, we’ve installed new technology like cutting-edge scanners to better detect drug smuggling.  \\n\\nWe’ve set up joint patrols with Mexico and Guatemala to catch more human traffickers.  \\n\\nWe’re putting in place dedicated immigration judges so families fleeing persecution and violence can have their cases heard faster. \\n\\nWe’re securing commitments and supporting partners in South and Central America to host more refugees and secure their own borders. \\n\\nWe can do all this while keeping lit the torch of liberty that has led generations of immigrants to this land—my forefathers and so many of yours. \\n\\nProvide a pathway to citizenship for Dreamers, those on temporary status, farm workers, and essential workers. \\n\\nRevise our laws so businesses have the workers they need and families don’t wait decades to reunite. \\n\\nIt’s not only the right thing to do—it’s the economically smart thing to do. \\n\\nThat’s why immigration reform is supported by everyone from labor unions to religious leaders to the U.S. Chamber of Commerce. \\n\\nLet’s get it done once and for all. \\n\\nAdvancing liberty and justice also requires protecting the rights of women. \\n\\nThe constitutional right affirmed in Roe v. Wade—standing precedent for half a century—is under attack as never before. \\n\\nIf we want to go forward—not backward—we must protect access to health care. Preserve a woman’s right to choose. And let’s continue to advance maternal health care in America. \\n\\nAnd for our LGBTQ+ Americans, let’s finally get the bipartisan Equality Act to my desk. The onslaught of state laws targeting transgender Americans and their families is wrong. \\n\\nAs I said last year, especially to our younger transgender Americans, I will always have your back as your President, so you can be yourself and reach your God-given potential. \\n\\nWhile it often appears that we never agree, that isn’t true. I signed 80 bipartisan bills into law last year. From preventing government shutdowns to protecting Asian-Americans from still-too-common hate crimes to reforming military justice. \\n\\nAnd soon, we’ll strengthen the Violence Against Women Act that I first wrote three decades ago. It is important for us to show the nation that we can come together and do big things. \\n\\nSo tonight I’m offering a Unity Agenda for the Nation. Four big things we can do together.  \\n\\nFirst, beat the opioid epidemic. \\n\\nThere is so much we can do. Increase funding for prevention, treatment, harm reduction, and recovery.  \\n\\nGet rid of outdated rules that stop doctors from prescribing treatments. And stop the flow of illicit drugs by working with state and local law enforcement to go after traffickers. \\n\\nIf you’re suffering from addiction, know you are not alone. I believe in recovery, and I celebrate the 23 million Americans in recovery. \\n\\nSecond, let’s take on mental health. Especially among our children, whose lives and education have been turned upside down.  \\n\\nThe American Rescue Plan gave schools money to hire teachers and help students make up for lost learning.  \\n\\nI urge every parent to make sure your school does just that. And we can all play a part—sign up to be a tutor or a mentor. \\n\\nChildren were also struggling before the pandemic. Bullying, violence, trauma, and the harms of social media. \\n\\nAs Frances Haugen, who is here with us tonight, has shown, we must hold social media platforms accountable for the national experiment they’re conducting on our children for profit. \\n\\nIt’s time to strengthen privacy protections, ban targeted advertising to children, demand tech companies stop collecting personal data on our children. \\n\\nAnd let’s get all Americans the mental health services they need. More people they can turn to for help, and full parity between physical and mental health care. \\n\\nThird, support our veterans. \\n\\nVeterans are the best of us. \\n\\nI’ve always believed that we have a sacred obligation to equip all those we send to war and care for them and their families when they come home. \\n\\nMy administration is providing assistance with job training and housing, and now helping lower-income veterans get VA care debt-free.  \\n\\nOur troops in Iraq and Afghanistan faced many dangers. \\n\\nOne was stationed at bases and breathing in toxic smoke from “burn pits” that incinerated wastes of war—medical and hazard material, jet fuel, and more. \\n\\nWhen they came home, many of the world’s fittest and best trained warriors were never the same. \\n\\nHeadaches. Numbness. Dizziness. \\n\\nA cancer that would put them in a flag-draped coffin. \\n\\nI know. \\n\\nOne of those soldiers was my son Major Beau Biden. \\n\\nWe don’t know for sure if a burn pit was the cause of his brain cancer, or the diseases of so many of our troops. \\n\\nBut I’m committed to finding out everything we can. \\n\\nCommitted to military families like Danielle Robinson from Ohio. \\n\\nThe widow of Sergeant First Class Heath Robinson.  \\n\\nHe was born a soldier. Army National Guard. Combat medic in Kosovo and Iraq. \\n\\nStationed near Baghdad, just yards from burn pits the size of football fields. \\n\\nHeath’s widow Danielle is here with us tonight. They loved going to Ohio State football games. He loved building Legos with their daughter. \\n\\nBut cancer from prolonged exposure to burn pits ravaged Heath’s lungs and body. \\n\\nDanielle says Heath was a fighter to the very end. \\n\\nHe didn’t know how to stop fighting, and neither did she. \\n\\nThrough her pain she found purpose to demand we do better. \\n\\nTonight, Danielle—we are. \\n\\nThe VA is pioneering new ways of linking toxic exposures to diseases, already helping more veterans get benefits. \\n\\nAnd tonight, I’m announcing we’re expanding eligibility to veterans suffering from nine respiratory cancers. \\n\\nI’m also calling on Congress: pass a law to make sure veterans devastated by toxic exposures in Iraq and Afghanistan finally get the benefits and comprehensive health care they deserve. \\n\\nAnd fourth, let’s end cancer as we know it. \\n\\nThis is personal to me and Jill, to Kamala, and to so many of you. \\n\\nCancer is the #2 cause of death in America–second only to heart disease. \\n\\nLast month, I announced our plan to supercharge  \\nthe Cancer Moonshot that President Obama asked me to lead six years ago. \\n\\nOur goal is to cut the cancer death rate by at least 50% over the next 25 years, turn more cancers from death sentences into treatable diseases.  \\n\\nMore support for patients and families. \\n\\nTo get there, I call on Congress to fund ARPA-H, the Advanced Research Projects Agency for Health. \\n\\nIt’s based on DARPA—the Defense Department project that led to the Internet, GPS, and so much more.  \\n\\nARPA-H will have a singular purpose—to drive breakthroughs in cancer, Alzheimer’s, diabetes, and more. \\n\\nA unity agenda for the nation. \\n\\nWe can do this. \\n\\nMy fellow Americans—tonight , we have gathered in a sacred space—the citadel of our democracy. \\n\\nIn this Capitol, generation after generation, Americans have debated great questions amid great strife, and have done great things. \\n\\nWe have fought for freedom, expanded liberty, defeated totalitarianism and terror. \\n\\nAnd built the strongest, freest, and most prosperous nation the world has ever known. \\n\\nNow is the hour. \\n\\nOur moment of responsibility. \\n\\nOur test of resolve and conscience, of history itself. \\n\\nIt is in this moment that our character is formed. Our purpose is found. Our future is forged. \\n\\nWell I know this nation.  \\n\\nWe will meet the test. \\n\\nTo protect freedom and liberty, to expand fairness and opportunity. \\n\\nWe will save democracy. \\n\\nAs hard as these times have been, I am more optimistic about America today than I have been my whole life. \\n\\nBecause I see the future that is within our grasp. \\n\\nBecause I know there is simply nothing beyond our capacity. \\n\\nWe are the only nation on Earth that has always turned every crisis we have faced into an opportunity. \\n\\nThe only nation that can be defined by a single word: possibilities. \\n\\nSo on this night, in our 245th year as a nation, I have come to report on the State of the Union. \\n\\nAnd my report is this: the State of the Union is strong—because you, the American people, are strong. \\n\\nWe are stronger today than we were a year ago. \\n\\nAnd we will be stronger a year from now than we are today. \\n\\nNow is our moment to meet and overcome the challenges of our time. \\n\\nAnd we will, as one people. \\n\\nOne America. \\n\\nThe United States of America. \\n\\nMay God bless you all. May God protect our troops.')]"]}, "metadata": {}, "execution_count": 9}]}, {"cell_type": "code", "source": ["# This text splitter is used to create the child documents\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "child_splitter = RecursiveCharacterTextSplitter(chunk_size=400)"], "metadata": {"id": "sLtZhaDnFUDL"}, "execution_count": 19, "outputs": []}, {"cell_type": "code", "source": ["from langchain.storage import InMemoryStore\n", "from langchain_chroma import Chroma"], "metadata": {"id": "l_x04zdrowdD"}, "execution_count": 15, "outputs": []}, {"cell_type": "markdown", "source": ["**Dataset size:**  Larger datasets generally benefit from more powerful models like MPNet.\n", "\n", "**Computational resources:**  If you have limited resources, BGE Small En or MiniLM might be better options.\n", "\n", "**Task complexity:**  For complex tasks like question answering or text summarization, MPNet is often preferred.\n", "\n", "**Embedding dimensionality:**  Different models produce embeddings of varying dimensions.Choose based on downstream task requirements.\n", "\n", "**Performance vs. efficiency trade-off:** Decide if you prioritize high accuracy or faster processing\n", "\n", "#####Experimentation is key. Try different models and evaluate their performance on your specific task and dataset to find the best fit.\n", "\n", "MTEB: Massive Text Embedding Benchmark\n", "\n", "MPNET: Masked and Permuted Pre-training for Language Understanding.\n", "\n", "BGE(BAAI general embedding)\n", "BAAI: https://huggingface.co/BAAI\n", "\n", "https://huggingface.co/sentence-transformers\n", "\n", "https://huggingface.co/spaces/mteb/leaderboard\n", "\n", "https://huggingface.co/blog/mteb\n", "\n", "#### The all-mpnet-base-v2 model provides the best quality, while all-MiniLM-L6-v2 is 5 times faster and still offers good quality."], "metadata": {"id": "WtPMrD6nog-P"}}, {"cell_type": "code", "source": ["'''# specify embedding model (using huggingface sentence transformer)\n", "from langchain.embeddings import HuggingFaceEmbeddings\n", "embedding_model_name = \"sentence-transformers/all-mpnet-base-v2\"\n", "model_kwargs = {\"device\": \"cuda\"}\n", "embeddings = HuggingFaceEmbeddings(\n", "  model_name=embedding_model_name,\n", "  model_kwargs=model_kwargs\n", ")'''"], "metadata": {"id": "o1O5hxRCHevP"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["import os\n", "from google.colab import userdata\n", "\n", "GOOGLE_API_KEY = userdata.get('GOOGLE_API_KEY')\n", "os.environ[\"GOOGLE_API_KEY\"] = GOOGLE_API_KEY\n", "\n", "from langchain_google_genai import GoogleGenerativeAIEmbeddings\n", "gemini_embeddings = GoogleGenerativeAIEmbeddings(model=\"models/embedding-001\")"], "metadata": {"id": "C5NFA0cMHF9u"}, "execution_count": 13, "outputs": []}, {"cell_type": "code", "source": ["vectorstore = Chroma(\n", "    collection_name=\"full_documents\", embedding_function=gemini_embeddings\n", ")"], "metadata": {"id": "wHnoDNOaE8nS"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["store = InMemoryStore()"], "metadata": {"id": "4YRLmyMmE8p-"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": ["from langchain.retrievers import ParentDocumentRetriever\n", "retriever = ParentDocumentRetriever(\n", "    vectorstore=vectorstore,\n", "    docstore=store,\n", "    child_splitter=child_splitter,\n", ")"], "metadata": {"id": "z0TRe_yxE8sg"}, "execution_count": 20, "outputs": []}, {"cell_type": "code", "source": ["retriever.add_documents(docs, ids=None)"], "metadata": {"id": "3yQp2lzZFOoP"}, "execution_count": 21, "outputs": []}, {"cell_type": "code", "source": ["list(store.yield_keys())"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "GXrbdq3vpyQs", "outputId": "80ac07d5-e38f-4337-9c97-74ea085012a3"}, "execution_count": 22, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['8f613b54-cd3d-4611-8eee-7a043c52c336',\n", " 'a50a25ca-5f8e-4d89-9ada-f9bfa56b3156']"]}, "metadata": {}, "execution_count": 22}]}, {"cell_type": "code", "source": ["retrieved_docs= retriever.invoke(\"What did the president say about <PERSON><PERSON><PERSON>\")"], "metadata": {"id": "nOwg2zSnp6E7"}, "execution_count": 24, "outputs": []}, {"cell_type": "code", "source": ["print(retrieved_docs[0].page_content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Xo0wX26Ap6Hc", "outputId": "5171b01e-b1c6-4cef-cf24-3511cea9b60b"}, "execution_count": 25, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Madam Speaker, <PERSON>am Vice President, our First Lady and Second Gentleman. Members of Congress and the Cabinet. Justices of the Supreme Court. My fellow Americans.  \n", "\n", "Last year COVID-19 kept us apart. This year we are finally together again. \n", "\n", "Tonight, we meet as Democrats Republicans and Independents. But most importantly as Americans. \n", "\n", "With a duty to one another to the American people to the Constitution. \n", "\n", "And with an unwavering resolve that freedom will always triumph over tyranny. \n", "\n", "Six days ago, Russia’s <PERSON> sought to shake the foundations of the free world thinking he could make it bend to his menacing ways. But he badly miscalculated. \n", "\n", "He thought he could roll into Ukraine and the world would roll over. Instead he met a wall of strength he never imagined. \n", "\n", "He met the Ukrainian people. \n", "\n", "From President <PERSON><PERSON><PERSON><PERSON> to every Ukrainian, their fearlessness, their courage, their determination, inspires the world. \n", "\n", "Groups of citizens blocking tanks with their bodies. Everyone from students to retirees teachers turned soldiers defending their homeland. \n", "\n", "In this struggle as President <PERSON><PERSON><PERSON><PERSON> said in his speech to the European Parliament “Light will win over darkness.” The Ukrainian Ambassador to the United States is here tonight. \n", "\n", "Let each of us here tonight in this Chamber send an unmistakable signal to Ukraine and to the world. \n", "\n", "Please rise if you are able and show that, Yes, we the United States of America stand with the Ukrainian people. \n", "\n", "Throughout our history we’ve learned this lesson when dictators do not pay a price for their aggression they cause more chaos.   \n", "\n", "They keep moving.   \n", "\n", "And the costs and the threats to America and the world keep rising.   \n", "\n", "That’s why the NATO Alliance was created to secure peace and stability in Europe after World War 2. \n", "\n", "The United States is a member along with 29 other nations. \n", "\n", "It matters. American diplomacy matters. American resolve matters. \n", "\n", "<PERSON>’s latest attack on Ukraine was premeditated and unprovoked. \n", "\n", "He rejected repeated efforts at diplomacy. \n", "\n", "He thought the West and NATO wouldn’t respond. And he thought he could divide us at home. Putin was wrong. We were ready.  Here is what we did.   \n", "\n", "We prepared extensively and carefully. \n", "\n", "We spent months building a coalition of other freedom-loving nations from Europe and the Americas to Asia and Africa to confront <PERSON>. \n", "\n", "I spent countless hours unifying our European allies. We shared with the world in advance what we knew <PERSON> was planning and precisely how he would try to falsely justify his aggression.  \n", "\n", "We countered Russia’s lies with truth.   \n", "\n", "And now that he has acted the free world is holding him accountable. \n", "\n", "Along with twenty-seven members of the European Union including France, Germany, Italy, as well as countries like the United Kingdom, Canada, Japan, Korea, Australia, New Zealand, and many others, even Switzerland. \n", "\n", "We are inflicting pain on Russia and supporting the people of Ukraine. <PERSON> is now isolated from the world more than ever. \n", "\n", "Together with our allies –we are right now enforcing powerful economic sanctions. \n", "\n", "We are cutting off Russia’s largest banks from the international financial system.  \n", "\n", "Preventing Russia’s central bank from defending the Russian Ruble making <PERSON>’s $630 Billion “war fund” worthless.   \n", "\n", "We are choking off Russia’s access to technology that will sap its economic strength and weaken its military for years to come.  \n", "\n", "Tonight I say to the Russian oligarchs and corrupt leaders who have bilked billions of dollars off this violent regime no more. \n", "\n", "The U.S. Department of Justice is assembling a dedicated task force to go after the crimes of Russian oligarchs.  \n", "\n", "We are joining with our European allies to find and seize your yachts your luxury apartments your private jets. We are coming for your ill-begotten gains. \n", "\n", "And tonight I am announcing that we will join our allies in closing off American air space to all Russian flights – further isolating Russia – and adding an additional squeeze –on their economy. The Ruble has lost 30% of its value. \n", "\n", "The Russian stock market has lost 40% of its value and trading remains suspended. Russia’s economy is reeling and Putin alone is to blame. \n", "\n", "Together with our allies we are providing support to the Ukrainians in their fight for freedom. Military assistance. Economic assistance. Humanitarian assistance. \n", "\n", "We are giving more than $1 Billion in direct assistance to Ukraine. \n", "\n", "And we will continue to aid the Ukrainian people as they defend their country and to help ease their suffering.  \n", "\n", "Let me be clear, our forces are not engaged and will not engage in conflict with Russian forces in Ukraine.  \n", "\n", "Our forces are not going to Europe to fight in Ukraine, but to defend our NATO Allies – in the event that <PERSON> decides to keep moving west.  \n", "\n", "For that purpose we’ve mobilized American ground forces, air squadrons, and ship deployments to protect NATO countries including Poland, Romania, Latvia, Lithuania, and Estonia. \n", "\n", "As I have made crystal clear the United States and our Allies will defend every inch of territory of NATO countries with the full force of our collective power.  \n", "\n", "And we remain clear-eyed. The Ukrainians are fighting back with pure courage. But the next few days weeks, months, will be hard on them.  \n", "\n", "<PERSON> has unleashed violence and chaos.  But while he may make gains on the battlefield – he will pay a continuing high price over the long run. \n", "\n", "And a proud Ukrainian people, who have known 30 years  of independence, have repeatedly shown that they will not tolerate anyone who tries to take their country backwards.  \n", "\n", "To all Americans, I will be honest with you, as I’ve always promised. A Russian dictator, invading a foreign country, has costs around the world. \n", "\n", "And I’m taking robust action to make sure the pain of our sanctions  is targeted at Russia’s economy. And I will use every tool at our disposal to protect American businesses and consumers. \n", "\n", "Tonight, I can announce that the United States has worked with 30 other countries to release 60 Million barrels of oil from reserves around the world.  \n", "\n", "America will lead that effort, releasing 30 Million barrels from our own Strategic Petroleum Reserve. And we stand ready to do more if necessary, unified with our allies.  \n", "\n", "These steps will help blunt gas prices here at home. And I know the news about what’s happening can seem alarming. \n", "\n", "But I want you to know that we are going to be okay. \n", "\n", "When the history of this era is written <PERSON>’s war on Ukraine will have left Russia weaker and the rest of the world stronger. \n", "\n", "While it shouldn’t have taken something so terrible for people around the world to see what’s at stake now everyone sees it clearly. \n", "\n", "We see the unity among leaders of nations and a more unified Europe a more unified West. And we see unity among the people who are gathering in cities in large crowds around the world even in Russia to demonstrate their support for Ukraine.  \n", "\n", "In the battle between democracy and autocracy, democracies are rising to the moment, and the world is clearly choosing the side of peace and security. \n", "\n", "This is a real test. It’s going to take time. So let us continue to draw inspiration from the iron will of the Ukrainian people. \n", "\n", "To our fellow Ukrainian Americans who forge a deep bond that connects our two nations we stand with you. \n", "\n", "<PERSON> may circle Kyiv with tanks, but he will never gain the hearts and souls of the Ukrainian people. \n", "\n", "He will never extinguish their love of freedom. He will never weaken the resolve of the free world. \n", "\n", "We meet tonight in an America that has lived through two of the hardest years this nation has ever faced. \n", "\n", "The pandemic has been punishing. \n", "\n", "And so many families are living paycheck to paycheck, struggling to keep up with the rising cost of food, gas, housing, and so much more. \n", "\n", "I understand. \n", "\n", "I remember when my Dad had to leave our home in Scranton, Pennsylvania to find work. I grew up in a family where if the price of food went up, you felt it. \n", "\n", "That’s why one of the first things I did as President was fight to pass the American Rescue Plan.  \n", "\n", "Because people were hurting. We needed to act, and we did. \n", "\n", "Few pieces of legislation have done more in a critical moment in our history to lift us out of crisis. \n", "\n", "It fueled our efforts to vaccinate the nation and combat COVID-19. It delivered immediate economic relief for tens of millions of Americans.  \n", "\n", "Helped put food on their table, keep a roof over their heads, and cut the cost of health insurance. \n", "\n", "And as my Dad used to say, it gave people a little breathing room. \n", "\n", "And unlike the $2 Trillion tax cut passed in the previous administration that benefitted the top 1% of Americans, the American Rescue Plan helped working people—and left no one behind. \n", "\n", "And it worked. It created jobs. Lots of jobs. \n", "\n", "In fact—our economy created over 6.5 Million new jobs just last year, more jobs created in one year  \n", "than ever before in the history of America. \n", "\n", "Our economy grew at a rate of 5.7% last year, the strongest growth in nearly 40 years, the first step in bringing fundamental change to an economy that hasn’t worked for the working people of this nation for too long.  \n", "\n", "For the past 40 years we were told that if we gave tax breaks to those at the very top, the benefits would trickle down to everyone else. \n", "\n", "But that trickle-down theory led to weaker economic growth, lower wages, bigger deficits, and the widest gap between those at the top and everyone else in nearly a century. \n", "\n", "Vice President <PERSON> and <PERSON> ran for office with a new economic vision for America. \n", "\n", "Invest in America. Educate Americans. Grow the workforce. Build the economy from the bottom up  \n", "and the middle out, not from the top down.  \n", "\n", "Because we know that when the middle class grows, the poor have a ladder up and the wealthy do very well. \n", "\n", "America used to have the best roads, bridges, and airports on Earth. \n", "\n", "Now our infrastructure is ranked 13th in the world. \n", "\n", "We won’t be able to compete for the jobs of the 21st Century if we don’t fix that. \n", "\n", "That’s why it was so important to pass the Bipartisan Infrastructure Law—the most sweeping investment to rebuild America in history. \n", "\n", "This was a bipartisan effort, and I want to thank the members of both parties who worked to make it happen. \n", "\n", "We’re done talking about infrastructure weeks. \n", "\n", "We’re going to have an infrastructure decade. \n", "\n", "It is going to transform America and put us on a path to win the economic competition of the 21st Century that we face with the rest of the world—particularly with China.  \n", "\n", "As I’ve told <PERSON>, it is never a good bet to bet against the American people. \n", "\n", "We’ll create good jobs for millions of Americans, modernizing roads, airports, ports, and waterways all across America. \n", "\n", "And we’ll do it all to withstand the devastating effects of the climate crisis and promote environmental justice. \n", "\n", "We’ll build a national network of 500,000 electric vehicle charging stations, begin to replace poisonous lead pipes—so every child—and every American—has clean water to drink at home and at school, provide affordable high-speed internet for every American—urban, suburban, rural, and tribal communities. \n", "\n", "4,000 projects have already been announced. \n", "\n", "And tonight, I’m announcing that this year we will start fixing over 65,000 miles of highway and 1,500 bridges in disrepair. \n", "\n", "When we use taxpayer dollars to rebuild America – we are going to Buy American: buy American products to support American jobs. \n", "\n", "The federal government spends about $600 Billion a year to keep the country safe and secure. \n", "\n", "There’s been a law on the books for almost a century \n", "to make sure taxpayers’ dollars support American jobs and businesses. \n", "\n", "Every Administration says they’ll do it, but we are actually doing it. \n", "\n", "We will buy American to make sure everything from the deck of an aircraft carrier to the steel on highway guardrails are made in America. \n", "\n", "But to compete for the best jobs of the future, we also need to level the playing field with China and other competitors. \n", "\n", "That’s why it is so important to pass the Bipartisan Innovation Act sitting in Congress that will make record investments in emerging technologies and American manufacturing. \n", "\n", "Let me give you one example of why it’s so important to pass it. \n", "\n", "If you travel 20 miles east of Columbus, Ohio, you’ll find 1,000 empty acres of land. \n", "\n", "It won’t look like much, but if you stop and look closely, you’ll see a “Field of dreams,” the ground on which America’s future will be built. \n", "\n", "This is where Intel, the American company that helped build Silicon Valley, is going to build its $20 billion semiconductor “mega site”. \n", "\n", "Up to eight state-of-the-art factories in one place. 10,000 new good-paying jobs. \n", "\n", "Some of the most sophisticated manufacturing in the world to make computer chips the size of a fingertip that power the world and our everyday lives. \n", "\n", "Smartphones. The Internet. Technology we have yet to invent. \n", "\n", "But that’s just the beginning. \n", "\n", "Intel’s CEO, <PERSON>, who is here tonight, told me they are ready to increase their investment from  \n", "$20 billion to $100 billion. \n", "\n", "That would be one of the biggest investments in manufacturing in American history. \n", "\n", "And all they’re waiting for is for you to pass this bill. \n", "\n", "So let’s not wait any longer. Send it to my desk. I’ll sign it.  \n", "\n", "And we will really take off. \n", "\n", "And Intel is not alone. \n", "\n", "There’s something happening in America. \n", "\n", "Just look around and you’ll see an amazing story. \n", "\n", "The rebirth of the pride that comes from stamping products “Made In America.” The revitalization of American manufacturing.   \n", "\n", "Companies are choosing to build new factories here, when just a few years ago, they would have built them overseas. \n", "\n", "That’s what is happening. Ford is investing $11 billion to build electric vehicles, creating 11,000 jobs across the country. \n", "\n", "GM is making the largest investment in its history—$7 billion to build electric vehicles, creating 4,000 jobs in Michigan. \n", "\n", "All told, we created 369,000 new manufacturing jobs in America just last year. \n", "\n", "Powered by people I’ve met like <PERSON><PERSON><PERSON>, from generations of union steelworkers from Pittsburgh, who’s here with us tonight. \n", "\n", "As Ohio Senator <PERSON><PERSON><PERSON> says, “It’s time to bury the label “Rust Belt.” \n", "\n", "It’s time. \n", "\n", "But with all the bright spots in our economy, record job growth and higher wages, too many families are struggling to keep up with the bills.  \n", "\n", "Inflation is robbing them of the gains they might otherwise feel. \n", "\n", "I get it. That’s why my top priority is getting prices under control. \n", "\n", "Look, our economy roared back faster than most predicted, but the pandemic meant that businesses had a hard time hiring enough workers to keep up production in their factories. \n", "\n", "The pandemic also disrupted global supply chains. \n", "\n", "When factories close, it takes longer to make goods and get them from the warehouse to the store, and prices go up. \n", "\n", "Look at cars. \n", "\n", "Last year, there weren’t enough semiconductors to make all the cars that people wanted to buy. \n", "\n", "And guess what, prices of automobiles went up. \n", "\n", "So—we have a choice. \n", "\n", "One way to fight inflation is to drive down wages and make Americans poorer.  \n", "\n", "I have a better plan to fight inflation. \n", "\n", "Lower your costs, not your wages. \n", "\n", "Make more cars and semiconductors in America. \n", "\n", "More infrastructure and innovation in America. \n", "\n", "More goods moving faster and cheaper in America. \n", "\n", "More jobs where you can earn a good living in America. \n", "\n", "And instead of relying on foreign supply chains, let’s make it in America. \n", "\n", "Economists call it “increasing the productive capacity of our economy.” \n", "\n", "I call it building a better America. \n", "\n", "My plan to fight inflation will lower your costs and lower the deficit. \n", "\n", "17 Nobel laureates in economics say my plan will ease long-term inflationary pressures. Top business leaders and most Americans support my plan. And here’s the plan: \n", "\n", "First – cut the cost of prescription drugs. Just look at insulin. One in ten Americans has diabetes. In Virginia, I met a 13-year-old boy named <PERSON>.  \n", "\n", "He and his Dad both have Type 1 diabetes, which means they need insulin every day. Insulin costs about $10 a vial to make.  \n", "\n", "But drug companies charge families like <PERSON> and his Dad up to 30 times more. I spoke with <PERSON>’s mom. \n", "\n", "Imagine what it’s like to look at your child who needs insulin and have no idea how you’re going to pay for it.  \n", "\n", "What it does to your dignity, your ability to look your child in the eye, to be the parent you expect to be. \n", "\n", "<PERSON> is here with us tonight. Yesterday was his birthday. Happy birthday, buddy.  \n", "\n", "For <PERSON>, and for the 200,000 other young people with Type 1 diabetes, let’s cap the cost of insulin at $35 a month so everyone can afford it.  \n", "\n", "Drug companies will still do very well. And while we’re at it let Medicare negotiate lower prices for prescription drugs, like the VA already does. \n", "\n", "Look, the American Rescue Plan is helping millions of families on Affordable Care Act plans save $2,400 a year on their health care premiums. Let’s close the coverage gap and make those savings permanent. \n", "\n", "Second – cut energy costs for families an average of $500 a year by combatting climate change.  \n", "\n", "Let’s provide investments and tax credits to weatherize your homes and businesses to be energy efficient and you get a tax credit; double America’s clean energy production in solar, wind, and so much more;  lower the price of electric vehicles, saving you another $80 a month because you’ll never have to pay at the gas pump again. \n", "\n", "Third – cut the cost of child care. Many families pay up to $14,000 a year for child care per child.  \n", "\n", "Middle-class and working families shouldn’t have to pay more than 7% of their income for care of young children.  \n", "\n", "My plan will cut the cost in half for most families and help parents, including millions of women, who left the workforce during the pandemic because they couldn’t afford child care, to be able to get back to work. \n", "\n", "My plan doesn’t stop there. It also includes home and long-term care. More affordable housing. And Pre-K for every 3- and 4-year-old.  \n", "\n", "All of these will lower costs. \n", "\n", "And under my plan, nobody earning less than $400,000 a year will pay an additional penny in new taxes. Nobody.  \n", "\n", "The one thing all Americans agree on is that the tax system is not fair. We have to fix it.  \n", "\n", "I’m not looking to punish anyone. But let’s make sure corporations and the wealthiest Americans start paying their fair share. \n", "\n", "Just last year, 55 Fortune 500 corporations earned $40 billion in profits and paid zero dollars in federal income tax.  \n", "\n", "That’s simply not fair. That’s why I’ve proposed a 15% minimum tax rate for corporations. \n", "\n", "We got more than 130 countries to agree on a global minimum tax rate so companies can’t get out of paying their taxes at home by shipping jobs and factories overseas. \n", "\n", "That’s why I’ve proposed closing loopholes so the very wealthy don’t pay a lower tax rate than a teacher or a firefighter.  \n", "\n", "So that’s my plan. It will grow the economy and lower costs for families. \n", "\n", "So what are we waiting for? Let’s get this done. And while you’re at it, confirm my nominees to the Federal Reserve, which plays a critical role in fighting inflation.  \n", "\n", "My plan will not only lower costs to give families a fair shot, it will lower the deficit. \n", "\n", "The previous Administration not only ballooned the deficit with tax cuts for the very wealthy and corporations, it undermined the watchdogs whose job was to keep pandemic relief funds from being wasted. \n", "\n", "But in my administration, the watchdogs have been welcomed back. \n", "\n", "We’re going after the criminals who stole billions in relief money meant for small businesses and millions of Americans.  \n", "\n", "And tonight, I’m announcing that the Justice Department will name a chief prosecutor for pandemic fraud. \n", "\n", "By the end of this year, the deficit will be down to less than half what it was before I took office.  \n", "\n", "The only president ever to cut the deficit by more than one trillion dollars in a single year. \n", "\n", "Lowering your costs also means demanding more competition. \n", "\n", "I’m a capitalist, but capitalism without competition isn’t capitalism. \n", "\n", "It’s exploitation—and it drives up prices. \n", "\n", "When corporations don’t have to compete, their profits go up, your prices go up, and small businesses and family farmers and ranchers go under. \n", "\n", "We see it happening with ocean carriers moving goods in and out of America. \n", "\n", "During the pandemic, these foreign-owned companies raised prices by as much as 1,000% and made record profits. \n", "\n", "Tonight, I’m announcing a crackdown on these companies overcharging American businesses and consumers. \n", "\n", "And as Wall Street firms take over more nursing homes, quality in those homes has gone down and costs have gone up.  \n", "\n", "That ends on my watch. \n", "\n", "Medicare is going to set higher standards for nursing homes and make sure your loved ones get the care they deserve and expect. \n", "\n", "We’ll also cut costs and keep the economy going strong by giving workers a fair shot, provide more training and apprenticeships, hire them based on their skills not degrees. \n", "\n", "Let’s pass the Paycheck Fairness Act and paid leave.  \n", "\n", "Raise the minimum wage to $15 an hour and extend the Child Tax Credit, so no one has to raise a family in poverty. \n", "\n", "Let’s increase Pell Grants and increase our historic support of HBCUs, and invest in what <PERSON>—our First Lady who teaches full-time—calls America’s best-kept secret: community colleges. \n", "\n", "And let’s pass the PRO Act when a majority of workers want to form a union—they shouldn’t be stopped.  \n", "\n", "When we invest in our workers, when we build the economy from the bottom up and the middle out together, we can do something we haven’t done in a long time: build a better America. \n", "\n", "For more than two years, COVID-19 has impacted every decision in our lives and the life of the nation. \n", "\n", "And I know you’re tired, frustrated, and exhausted. \n", "\n", "But I also know this. \n", "\n", "Because of the progress we’ve made, because of your resilience and the tools we have, tonight I can say  \n", "we are moving forward safely, back to more normal routines.  \n", "\n", "We’ve reached a new moment in the fight against COVID-19, with severe cases down to a level not seen since last July.  \n", "\n", "Just a few days ago, the Centers for Disease Control and Prevention—the CDC—issued new mask guidelines. \n", "\n", "Under these new guidelines, most Americans in most of the country can now be mask free.   \n", "\n", "And based on the projections, more of the country will reach that point across the next couple of weeks. \n", "\n", "Thanks to the progress we have made this past year, COVID-19 need no longer control our lives.  \n", "\n", "I know some are talking about “living with COVID-19”. Tonight – I say that we will never just accept living with COVID-19. \n", "\n", "We will continue to combat the virus as we do other diseases. And because this is a virus that mutates and spreads, we will stay on guard. \n", "\n", "Here are four common sense steps as we move forward safely.  \n", "\n", "First, stay protected with vaccines and treatments. We know how incredibly effective vaccines are. If you’re vaccinated and boosted you have the highest degree of protection. \n", "\n", "We will never give up on vaccinating more Americans. Now, I know parents with kids under 5 are eager to see a vaccine authorized for their children. \n", "\n", "The scientists are working hard to get that done and we’ll be ready with plenty of vaccines when they do. \n", "\n", "We’re also ready with anti-viral treatments. If you get COVID-19, the Pfizer pill reduces your chances of ending up in the hospital by 90%.  \n", "\n", "We’ve ordered more of these pills than anyone in the world. And <PERSON><PERSON><PERSON> is working overtime to get us 1 Million pills this month and more than double that next month.  \n", "\n", "And we’re launching the “Test to Treat” initiative so people can get tested at a pharmacy, and if they’re positive, receive antiviral pills on the spot at no cost.  \n", "\n", "If you’re immunocompromised or have some other vulnerability, we have treatments and free high-quality masks. \n", "\n", "We’re leaving no one behind or ignoring anyone’s needs as we move forward. \n", "\n", "And on testing, we have made hundreds of millions of tests available for you to order for free.   \n", "\n", "Even if you already ordered free tests tonight, I am announcing that you can order more from covidtests.gov starting next week. \n", "\n", "Second – we must prepare for new variants. Over the past year, we’ve gotten much better at detecting new variants. \n", "\n", "If necessary, we’ll be able to deploy new vaccines within 100 days instead of many more months or years.  \n", "\n", "And, if Congress provides the funds we need, we’ll have new stockpiles of tests, masks, and pills ready if needed. \n", "\n", "I cannot promise a new variant won’t come. But I can promise you we’ll do everything within our power to be ready if it does.  \n", "\n", "Third – we can end the shutdown of schools and businesses. We have the tools we need. \n", "\n", "It’s time for Americans to get back to work and fill our great downtowns again.  People working from home can feel safe to begin to return to the office.   \n", "\n", "We’re doing that here in the federal government. The vast majority of federal workers will once again work in person. \n", "\n", "Our schools are open. Let’s keep it that way. Our kids need to be in school. \n", "\n", "And with 75% of adult Americans fully vaccinated and hospitalizations down by 77%, most Americans can remove their masks, return to work, stay in the classroom, and move forward safely. \n", "\n", "We achieved this because we provided free vaccines, treatments, tests, and masks. \n", "\n", "Of course, continuing this costs money. \n", "\n", "I will soon send Congress a request. \n", "\n", "The vast majority of Americans have used these tools and may want to again, so I expect Congress to pass it quickly.   \n", "\n", "Fourth, we will continue vaccinating the world.     \n", "\n", "We’ve sent 475 Million vaccine doses to 112 countries, more than any other nation. \n", "\n", "And we won’t stop. \n", "\n", "We have lost so much to COVID-19. Time with one another. And worst of all, so much loss of life. \n", "\n", "Let’s use this moment to reset. Let’s stop looking at COVID-19 as a partisan dividing line and see it for what it is: A God-awful disease.  \n", "\n", "Let’s stop seeing each other as enemies, and start seeing each other for who we really are: Fellow Americans.  \n", "\n", "We can’t change how divided we’ve been. But we can change how we move forward—on COVID-19 and other issues we must face together. \n", "\n", "I recently visited the New York City Police Department days after the funerals of Officer <PERSON><PERSON><PERSON> and his partner, Officer <PERSON>. \n", "\n", "They were responding to a 9-1-1 call when a man shot and killed them with a stolen gun. \n", "\n", "Officer <PERSON><PERSON> was 27 years old. \n", "\n", "Officer <PERSON> was 22. \n", "\n", "Both Dominican Americans who’d grown up on the same streets they later chose to patrol as police officers. \n", "\n", "I spoke with their families and told them that we are forever in debt for their sacrifice, and we will carry on their mission to restore the trust and safety every community deserves. \n", "\n", "I’ve worked on these issues a long time. \n", "\n", "I know what works: Investing in crime prevention and community police officers who’ll walk the beat, who’ll know the neighborhood, and who can restore trust and safety. \n", "\n", "So let’s not abandon our streets. Or choose between safety and equal justice. \n", "\n", "Let’s come together to protect our communities, restore trust, and hold law enforcement accountable. \n", "\n", "That’s why the Justice Department required body cameras, banned chokeholds, and restricted no-knock warrants for its officers. \n", "\n", "That’s why the American Rescue Plan provided $350 Billion that cities, states, and counties can use to hire more police and invest in proven strategies like community violence interruption—trusted messengers breaking the cycle of violence and trauma and giving young people hope.  \n", "\n", "We should all agree: The answer is not to Defund the police. The answer is to FUND the police with the resources and training they need to protect our communities. \n", "\n", "I ask Democrats and Republicans alike: Pass my budget and keep our neighborhoods safe.  \n", "\n", "And I will keep doing everything in my power to crack down on gun trafficking and ghost guns you can buy online and make at home—they have no serial numbers and can’t be traced. \n", "\n", "And I ask Congress to pass proven measures to reduce gun violence. Pass universal background checks. Why should anyone on a terrorist list be able to purchase a weapon? \n", "\n", "Ban assault weapons and high-capacity magazines. \n", "\n", "Repeal the liability shield that makes gun manufacturers the only industry in America that can’t be sued. \n", "\n", "These laws don’t infringe on the Second Amendment. They save lives. \n", "\n", "The most fundamental right in America is the right to vote – and to have it counted. And it’s under assault. \n", "\n", "In state after state, new laws have been passed, not only to suppress the vote, but to subvert entire elections. \n", "\n", "We cannot let this happen. \n", "\n", "Tonight. I call on the Senate to: Pass the Freedom to Vote Act. Pass the John Lewis Voting Rights Act. And while you’re at it, pass the Disclose Act so Americans can know who is funding our elections. \n", "\n", "Tonight, I’d like to honor someone who has dedicated his life to serve this country: Justice <PERSON>—an Army veteran, Constitutional scholar, and retiring Justice of the United States Supreme Court. Justice <PERSON>, thank you for your service. \n", "\n", "One of the most serious constitutional responsibilities a President has is nominating someone to serve on the United States Supreme Court. \n", "\n", "And I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>. One of our nation’s top legal minds, who will continue <PERSON>’s legacy of excellence. \n", "\n", "A former top litigator in private practice. A former federal public defender. And from a family of public school educators and police officers. A consensus builder. Since she’s been nominated, she’s received a broad range of support—from the Fraternal Order of Police to former judges appointed by Democrats and Republicans. \n", "\n", "And if we are to advance liberty and justice, we need to secure the Border and fix the immigration system. \n", "\n", "We can do both. At our border, we’ve installed new technology like cutting-edge scanners to better detect drug smuggling.  \n", "\n", "We’ve set up joint patrols with Mexico and Guatemala to catch more human traffickers.  \n", "\n", "We’re putting in place dedicated immigration judges so families fleeing persecution and violence can have their cases heard faster. \n", "\n", "We’re securing commitments and supporting partners in South and Central America to host more refugees and secure their own borders. \n", "\n", "We can do all this while keeping lit the torch of liberty that has led generations of immigrants to this land—my forefathers and so many of yours. \n", "\n", "Provide a pathway to citizenship for Dream<PERSON>, those on temporary status, farm workers, and essential workers. \n", "\n", "Revise our laws so businesses have the workers they need and families don’t wait decades to reunite. \n", "\n", "It’s not only the right thing to do—it’s the economically smart thing to do. \n", "\n", "That’s why immigration reform is supported by everyone from labor unions to religious leaders to the U.S. Chamber of Commerce. \n", "\n", "Let’s get it done once and for all. \n", "\n", "Advancing liberty and justice also requires protecting the rights of women. \n", "\n", "The constitutional right affirmed in <PERSON> v<PERSON>—standing precedent for half a century—is under attack as never before. \n", "\n", "If we want to go forward—not backward—we must protect access to health care. Preserve a woman’s right to choose. And let’s continue to advance maternal health care in America. \n", "\n", "And for our LGBTQ+ Americans, let’s finally get the bipartisan Equality Act to my desk. The onslaught of state laws targeting transgender Americans and their families is wrong. \n", "\n", "As I said last year, especially to our younger transgender Americans, I will always have your back as your President, so you can be yourself and reach your God-given potential. \n", "\n", "While it often appears that we never agree, that isn’t true. I signed 80 bipartisan bills into law last year. From preventing government shutdowns to protecting Asian-Americans from still-too-common hate crimes to reforming military justice. \n", "\n", "And soon, we’ll strengthen the Violence Against Women Act that I first wrote three decades ago. It is important for us to show the nation that we can come together and do big things. \n", "\n", "So tonight I’m offering a Unity Agenda for the Nation. Four big things we can do together.  \n", "\n", "First, beat the opioid epidemic. \n", "\n", "There is so much we can do. Increase funding for prevention, treatment, harm reduction, and recovery.  \n", "\n", "Get rid of outdated rules that stop doctors from prescribing treatments. And stop the flow of illicit drugs by working with state and local law enforcement to go after traffickers. \n", "\n", "If you’re suffering from addiction, know you are not alone. I believe in recovery, and I celebrate the 23 million Americans in recovery. \n", "\n", "Second, let’s take on mental health. Especially among our children, whose lives and education have been turned upside down.  \n", "\n", "The American Rescue Plan gave schools money to hire teachers and help students make up for lost learning.  \n", "\n", "I urge every parent to make sure your school does just that. And we can all play a part—sign up to be a tutor or a mentor. \n", "\n", "Children were also struggling before the pandemic. Bullying, violence, trauma, and the harms of social media. \n", "\n", "As <PERSON>, who is here with us tonight, has shown, we must hold social media platforms accountable for the national experiment they’re conducting on our children for profit. \n", "\n", "It’s time to strengthen privacy protections, ban targeted advertising to children, demand tech companies stop collecting personal data on our children. \n", "\n", "And let’s get all Americans the mental health services they need. More people they can turn to for help, and full parity between physical and mental health care. \n", "\n", "Third, support our veterans. \n", "\n", "Veterans are the best of us. \n", "\n", "I’ve always believed that we have a sacred obligation to equip all those we send to war and care for them and their families when they come home. \n", "\n", "My administration is providing assistance with job training and housing, and now helping lower-income veterans get VA care debt-free.  \n", "\n", "Our troops in Iraq and Afghanistan faced many dangers. \n", "\n", "One was stationed at bases and breathing in toxic smoke from “burn pits” that incinerated wastes of war—medical and hazard material, jet fuel, and more. \n", "\n", "When they came home, many of the world’s fittest and best trained warriors were never the same. \n", "\n", "Headaches. Numbness. Dizziness. \n", "\n", "A cancer that would put them in a flag-draped coffin. \n", "\n", "I know. \n", "\n", "One of those soldiers was my son Major <PERSON>. \n", "\n", "We don’t know for sure if a burn pit was the cause of his brain cancer, or the diseases of so many of our troops. \n", "\n", "But I’m committed to finding out everything we can. \n", "\n", "Committed to military families like <PERSON> from Ohio. \n", "\n", "The widow of Sergeant First Class <PERSON>.  \n", "\n", "He was born a soldier. Army National Guard. Combat medic in Kosovo and Iraq. \n", "\n", "Stationed near Baghdad, just yards from burn pits the size of football fields. \n", "\n", "<PERSON>’s widow <PERSON> is here with us tonight. They loved going to Ohio State football games. He loved building Legos with their daughter. \n", "\n", "But cancer from prolonged exposure to burn pits ravaged <PERSON>’s lungs and body. \n", "\n", "<PERSON> says <PERSON> was a fighter to the very end. \n", "\n", "He didn’t know how to stop fighting, and neither did she. \n", "\n", "Through her pain she found purpose to demand we do better. \n", "\n", "Tonight, <PERSON>—we are. \n", "\n", "The VA is pioneering new ways of linking toxic exposures to diseases, already helping more veterans get benefits. \n", "\n", "And tonight, I’m announcing we’re expanding eligibility to veterans suffering from nine respiratory cancers. \n", "\n", "I’m also calling on Congress: pass a law to make sure veterans devastated by toxic exposures in Iraq and Afghanistan finally get the benefits and comprehensive health care they deserve. \n", "\n", "And fourth, let’s end cancer as we know it. \n", "\n", "This is personal to me and <PERSON>, to <PERSON><PERSON>, and to so many of you. \n", "\n", "Cancer is the #2 cause of death in America–second only to heart disease. \n", "\n", "Last month, I announced our plan to supercharge  \n", "the Cancer Moonshot that President <PERSON> asked me to lead six years ago. \n", "\n", "Our goal is to cut the cancer death rate by at least 50% over the next 25 years, turn more cancers from death sentences into treatable diseases.  \n", "\n", "More support for patients and families. \n", "\n", "To get there, I call on Congress to fund ARPA-H, the Advanced Research Projects Agency for Health. \n", "\n", "It’s based on DARPA—the Defense Department project that led to the Internet, GPS, and so much more.  \n", "\n", "ARPA-H will have a singular purpose—to drive breakthroughs in cancer, Alzheimer’s, diabetes, and more. \n", "\n", "A unity agenda for the nation. \n", "\n", "We can do this. \n", "\n", "My fellow Americans—tonight , we have gathered in a sacred space—the citadel of our democracy. \n", "\n", "In this Capitol, generation after generation, Americans have debated great questions amid great strife, and have done great things. \n", "\n", "We have fought for freedom, expanded liberty, defeated totalitarianism and terror. \n", "\n", "And built the strongest, freest, and most prosperous nation the world has ever known. \n", "\n", "Now is the hour. \n", "\n", "Our moment of responsibility. \n", "\n", "Our test of resolve and conscience, of history itself. \n", "\n", "It is in this moment that our character is formed. Our purpose is found. Our future is forged. \n", "\n", "Well I know this nation.  \n", "\n", "We will meet the test. \n", "\n", "To protect freedom and liberty, to expand fairness and opportunity. \n", "\n", "We will save democracy. \n", "\n", "As hard as these times have been, I am more optimistic about America today than I have been my whole life. \n", "\n", "Because I see the future that is within our grasp. \n", "\n", "Because I know there is simply nothing beyond our capacity. \n", "\n", "We are the only nation on Earth that has always turned every crisis we have faced into an opportunity. \n", "\n", "The only nation that can be defined by a single word: possibilities. \n", "\n", "So on this night, in our 245th year as a nation, I have come to report on the State of the Union. \n", "\n", "And my report is this: the State of the Union is strong—because you, the American people, are strong. \n", "\n", "We are stronger today than we were a year ago. \n", "\n", "And we will be stronger a year from now than we are today. \n", "\n", "Now is our moment to meet and overcome the challenges of our time. \n", "\n", "And we will, as one people. \n", "\n", "One America. \n", "\n", "The United States of America. \n", "\n", "May God bless you all. May God protect our troops.\n"]}]}, {"cell_type": "code", "source": ["print(len(retrieved_docs[0].page_content))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qa5Uakyip6J7", "outputId": "bf19c244-db05-4054-d69c-ddd3c508abca"}, "execution_count": 26, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["38540\n"]}]}, {"cell_type": "code", "source": ["# It should create documents smaller than the parent\n", "child_splitter = RecursiveCharacterTextSplitter(chunk_size=500)"], "metadata": {"id": "UQREOCakp6O8"}, "execution_count": 27, "outputs": []}, {"cell_type": "code", "source": ["# This text splitter is used to create the parent documents\n", "parent_splitter = RecursiveCharacterTextSplitter(chunk_size=2000)"], "metadata": {"id": "G6L0oYSXp6Rz"}, "execution_count": 28, "outputs": []}, {"cell_type": "code", "source": ["# The storage layer for the parent documents\n", "store1 = InMemoryStore()"], "metadata": {"id": "-hKUDmJrJLes"}, "execution_count": 29, "outputs": []}, {"cell_type": "code", "source": ["vectorstore1 = Chroma(\n", "    collection_name=\"full_documents\", embedding_function=gemini_embeddings\n", ")"], "metadata": {"id": "7mrkzvizJToH"}, "execution_count": 30, "outputs": []}, {"cell_type": "code", "source": ["retriever2 = ParentDocumentRetriever(\n", "    vectorstore=vectorstore1,\n", "    docstore=store1,\n", "    child_splitter=child_splitter,\n", "    parent_splitter=parent_splitter,\n", ")"], "metadata": {"id": "3YFmtO5rp6U0"}, "execution_count": 31, "outputs": []}, {"cell_type": "code", "source": ["retriever2.add_documents(docs)"], "metadata": {"id": "bayGpg5pp6XT"}, "execution_count": 32, "outputs": []}, {"cell_type": "code", "source": ["len(list(store1.yield_keys()))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gIUPIp15p6Z7", "outputId": "72e36fa6-9a92-4e53-c69c-b2badac6fb55"}, "execution_count": 34, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["66"]}, "metadata": {}, "execution_count": 34}]}, {"cell_type": "code", "source": ["len(list(store.yield_keys()))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "D4UoqeZ-p6cL", "outputId": "bea06635-b17b-49b1-b09e-af3e58440c91"}, "execution_count": 35, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2"]}, "metadata": {}, "execution_count": 35}]}, {"cell_type": "code", "source": ["retrieved_docs2= retriever2.invoke(\"What did the president say about <PERSON><PERSON><PERSON>\")"], "metadata": {"id": "t5gyMvXhp6ej"}, "execution_count": 36, "outputs": []}, {"cell_type": "code", "source": ["retrieved_docs2"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TEta4Oq4J63m", "outputId": "8fc8834c-ca52-4e56-f41f-6e3d6ea6e54f"}, "execution_count": 38, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(metadata={'source': '/content/data/state_of_the_union.txt'}, page_content='In state after state, new laws have been passed, not only to suppress the vote, but to subvert entire elections. \\n\\nWe cannot let this happen. \\n\\nTonight. I call on the Senate to: Pass the Freedom to Vote Act. Pass the John Lewis Voting Rights Act. And while you’re at it, pass the Disclose Act so Americans can know who is funding our elections. \\n\\nTonight, I’d like to honor someone who has dedicated his life to serve this country: Justice <PERSON>—an Army veteran, Constitutional scholar, and retiring Justice of the United States Supreme Court. <PERSON>, thank you for your service. \\n\\nOne of the most serious constitutional responsibilities a President has is nominating someone to serve on the United States Supreme Court. \\n\\nAnd I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>. One of our nation’s top legal minds, who will continue <PERSON>’s legacy of excellence. \\n\\nA former top litigator in private practice. A former federal public defender. And from a family of public school educators and police officers. A consensus builder. Since she’s been nominated, she’s received a broad range of support—from the Fraternal Order of Police to former judges appointed by Democrats and Republicans. \\n\\nAnd if we are to advance liberty and justice, we need to secure the Border and fix the immigration system. \\n\\nWe can do both. At our border, we’ve installed new technology like cutting-edge scanners to better detect drug smuggling.  \\n\\nWe’ve set up joint patrols with Mexico and Guatemala to catch more human traffickers.  \\n\\nWe’re putting in place dedicated immigration judges so families fleeing persecution and violence can have their cases heard faster. \\n\\nWe’re securing commitments and supporting partners in South and Central America to host more refugees and secure their own borders.'),\n", " Document(metadata={'source': '/content/data/state_of_the_union.txt'}, page_content='We’re securing commitments and supporting partners in South and Central America to host more refugees and secure their own borders. \\n\\nWe can do all this while keeping lit the torch of liberty that has led generations of immigrants to this land—my forefathers and so many of yours. \\n\\nProvide a pathway to citizenship for Dream<PERSON>, those on temporary status, farm workers, and essential workers. \\n\\nRevise our laws so businesses have the workers they need and families don’t wait decades to reunite. \\n\\nIt’s not only the right thing to do—it’s the economically smart thing to do. \\n\\nThat’s why immigration reform is supported by everyone from labor unions to religious leaders to the U.S. Chamber of Commerce. \\n\\nLet’s get it done once and for all. \\n\\nAdvancing liberty and justice also requires protecting the rights of women. \\n\\nThe constitutional right affirmed in <PERSON> v. <PERSON>—standing precedent for half a century—is under attack as never before. \\n\\nIf we want to go forward—not backward—we must protect access to health care. Preserve a woman’s right to choose. And let’s continue to advance maternal health care in America. \\n\\nAnd for our LGBTQ+ Americans, let’s finally get the bipartisan Equality Act to my desk. The onslaught of state laws targeting transgender Americans and their families is wrong. \\n\\nAs I said last year, especially to our younger transgender Americans, I will always have your back as your President, so you can be yourself and reach your God-given potential. \\n\\nWhile it often appears that we never agree, that isn’t true. I signed 80 bipartisan bills into law last year. From preventing government shutdowns to protecting Asian-Americans from still-too-common hate crimes to reforming military justice. \\n\\nAnd soon, we’ll strengthen the Violence Against Women Act that I first wrote three decades ago. It is important for us to show the nation that we can come together and do big things.')]"]}, "metadata": {}, "execution_count": 38}]}, {"cell_type": "code", "source": ["len(retrieved_docs2[0].page_content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "45c3gP25JzpT", "outputId": "f20a5cc1-be08-4c0f-8107-43e9d2520c3b"}, "execution_count": 37, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["1849"]}, "metadata": {}, "execution_count": 37}]}, {"cell_type": "markdown", "source": ["# Data Generation"], "metadata": {"id": "Dh5TRjUup7iE"}}, {"cell_type": "code", "source": ["from langchain_google_genai import ChatGoogleGenerativeAI\n", "llm = ChatGoogleGenerativeAI(model=\"gemini-1.5-pro\")\n", "\n", "result = llm.invoke(\"Write a ballad about <PERSON><PERSON><PERSON><PERSON>\")\n", "print(result.content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aSj_UeLtp-KK", "outputId": "3873f356-d05b-4b9b-e753-036bd0c27080"}, "execution_count": 39, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The coder toiled, 'midst screens aglow,\n", "With datasets vast, a tangled flow.\n", "His tools were sharp, his mind was keen,\n", "But still, the knowledge gap was seen.\n", "\n", "He dreamt of chains, not forged of steel,\n", "But links of thought, that could reveal,\n", "The secrets hidden, deep inside,\n", "The data's heart, where truths reside.\n", "\n", "Then, like a dawn, <PERSON><PERSON><PERSON><PERSON> took flight,\n", "A beacon bright, in coding's night.\n", "With <PERSON>'s grace, it wove its spell,\n", "Connecting words, where stories dwell.\n", "\n", "No more would data stand alone,\n", "In silos deep, on servers prone.\n", "<PERSON><PERSON><PERSON><PERSON> would bridge, with logic's art,\n", "Unleashing wisdom, from the start.\n", "\n", "It chained the models, one by one,\n", "From GP<PERSON>'s prose, to tasks well done.\n", "It queried, summarized, and more,\n", "Unlocking treasures, unexplored.\n", "\n", "The coder smiled, his work complete,\n", "A symphony of code, so neat.\n", "<PERSON><PERSON><PERSON><PERSON>, his ally, strong and true,\n", "Had made the impossible, come to view.\n", "\n", "So sing a song, of code so bright,\n", "That banishes the data's night.\n", "<PERSON><PERSON><PERSON><PERSON>, we hail your noble quest,\n", "To weave the words, and find the best. \n", "\n"]}]}, {"cell_type": "code", "source": ["from langchain.chains import RetrievalQA\n", "from langchain.llms import OpenAI\n", "\n", "qa = RetrievalQA.from_chain_type(llm=llm,\n", "                                 chain_type=\"stuff\",\n", "                                 retriever=retriever2)\n", "\n", "query = \"What did the president say about <PERSON><PERSON><PERSON>\""], "metadata": {"id": "Q_bsstEYKbn5"}, "execution_count": 40, "outputs": []}, {"cell_type": "code", "source": ["qa.run(query)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 146}, "id": "2exNRRDfKqzc", "outputId": "5e234b59-ce5c-4ff9-d494-d54cde805f39"}, "execution_count": 41, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/langchain_core/_api/deprecation.py:139: LangChainDeprecationWarning: The method `Chain.run` was deprecated in langchain 0.1.0 and will be removed in 0.3.0. Use invoke instead.\n", "  warn_deprecated(\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'The president stated that he nominated <PERSON><PERSON><PERSON> to the United States Supreme Court 4 days prior to this speech. He called her \"one of our nation\\'s top legal minds\" and that she would \"continue <PERSON>\\'s legacy of excellence.\" He described her past experience as a former top litigator, a former federal public defender, and someone who comes from a family of public school educators and police officers. He also called her a \"consensus builder\" who has received a broad range of support. \\n'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 41}]}, {"cell_type": "code", "source": [], "metadata": {"id": "YWshwI6IKtEp"}, "execution_count": null, "outputs": []}]}