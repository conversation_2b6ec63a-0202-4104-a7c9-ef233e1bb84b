{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"id": "pqj11-jHlJHf"}, "outputs": [], "source": ["import gensim\n", "from gensim.models import Word2Vec, KeyedVectors"]}, {"cell_type": "code", "source": ["from google.colab import drive\n", "drive.mount('/content/drive/')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Vmu4aFIClMd3", "outputId": "47fdec6c-120e-4577-9fae-49b42dc98126"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive/\n"]}]}, {"cell_type": "code", "source": ["model = KeyedVectors.load_word2vec_format('/content/drive/MyDrive/GoogleNews/GoogleNews-vectors-negative300.bin.gz',binary=True,limit=500000)"], "metadata": {"id": "VKejw6rtlSDd"}, "execution_count": 4, "outputs": []}, {"cell_type": "code", "source": ["model['human']"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Hw3NBqBRlpXT", "outputId": "4a3e488b-615f-4f29-87c2-072e499453fb"}, "execution_count": 5, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([ 5.59082031e-02,  9.22851562e-02,  1.07910156e-01,  2.83203125e-01,\n", "       -2.43164062e-01,  1.90429688e-02,  4.08203125e-01, -3.17382812e-02,\n", "       -4.78515625e-02,  6.34765625e-02, -9.32617188e-02, -4.46777344e-02,\n", "       -2.41210938e-01, -1.58203125e-01, -5.83496094e-02,  2.51953125e-01,\n", "       -3.24707031e-02,  1.00097656e-01, -4.56542969e-02,  1.35742188e-01,\n", "       -2.07031250e-01, -3.73046875e-01,  4.39453125e-02,  4.24804688e-02,\n", "        6.93359375e-02, -2.42187500e-01, -2.75390625e-01,  1.95312500e-01,\n", "        2.26562500e-01, -1.90429688e-01, -2.35351562e-01, -5.56640625e-02,\n", "       -1.25000000e-01, -8.78906250e-02, -2.33398438e-01,  9.61914062e-02,\n", "       -4.83398438e-02,  4.54101562e-02,  9.81445312e-02,  5.76171875e-02,\n", "       -4.17480469e-02,  2.02148438e-01, -9.03320312e-02,  2.75390625e-01,\n", "       -6.34765625e-02,  4.93164062e-02,  2.92968750e-02,  2.57812500e-01,\n", "        1.32812500e-01,  7.42187500e-02,  6.64062500e-02, -1.37695312e-01,\n", "       -1.73828125e-01,  1.89453125e-01, -1.88476562e-01, -4.34570312e-02,\n", "        2.11181641e-02, -3.02734375e-01,  1.76757812e-01,  3.24218750e-01,\n", "       -1.27563477e-02,  1.47094727e-02,  1.88476562e-01,  9.61914062e-02,\n", "       -8.15429688e-02, -6.64062500e-02,  9.71679688e-02,  2.29492188e-01,\n", "       -2.94921875e-01, -1.09375000e-01,  6.54296875e-02, -7.42187500e-02,\n", "       -3.41796875e-03, -6.88476562e-02, -9.86328125e-02, -1.13769531e-01,\n", "        1.03027344e-01,  1.39770508e-02, -7.08007812e-02, -1.81884766e-02,\n", "        6.49414062e-02, -3.27148438e-02,  1.04980469e-01, -7.22656250e-02,\n", "       -1.67236328e-02, -6.20117188e-02, -2.82287598e-03,  1.61132812e-01,\n", "       -5.27343750e-02,  1.30859375e-01,  1.46484375e-01,  2.81982422e-02,\n", "       -5.54199219e-02,  5.06591797e-03, -3.93066406e-02, -2.33154297e-02,\n", "        1.16699219e-01,  9.52148438e-02,  9.08203125e-02, -6.22558594e-02,\n", "       -1.24023438e-01,  2.44140625e-02,  1.90429688e-01, -5.49316406e-02,\n", "        1.68945312e-01, -1.94091797e-02, -6.88476562e-02,  4.32128906e-02,\n", "        1.00097656e-01,  1.70898438e-02, -1.87500000e-01, -5.43212891e-03,\n", "        4.46777344e-02,  1.46484375e-01, -1.19018555e-03,  8.74023438e-02,\n", "       -8.74023438e-02, -2.23388672e-02,  1.57226562e-01,  1.48437500e-01,\n", "       -1.76757812e-01, -2.47070312e-01, -2.71484375e-01,  9.13085938e-02,\n", "       -2.71484375e-01, -4.19921875e-02,  1.51367188e-01, -9.88769531e-03,\n", "        2.14843750e-01,  3.86047363e-03, -1.08886719e-01, -1.00585938e-01,\n", "       -3.80859375e-01,  8.00781250e-02,  8.83789062e-02, -2.06054688e-01,\n", "        5.49316406e-02,  6.88476562e-02,  1.40625000e-01, -1.43554688e-01,\n", "        9.61914062e-02, -3.46679688e-02, -1.42578125e-01,  2.44140625e-02,\n", "       -6.29882812e-02,  1.03027344e-01, -3.12500000e-02,  2.57812500e-01,\n", "        2.60009766e-02, -4.95605469e-02,  6.68945312e-02, -5.88378906e-02,\n", "       -1.56250000e-01,  3.10546875e-01, -3.27148438e-02, -1.75781250e-01,\n", "       -5.73730469e-02, -3.41796875e-01,  2.03125000e-01, -1.30859375e-01,\n", "       -1.01074219e-01, -3.10546875e-01,  2.44140625e-03,  6.05468750e-02,\n", "       -1.84570312e-01,  1.33056641e-02,  7.32421875e-02, -1.00097656e-01,\n", "        1.89208984e-02, -8.44726562e-02,  4.02832031e-02,  1.11328125e-01,\n", "        1.07421875e-01,  5.17578125e-02,  3.00781250e-01,  3.88183594e-02,\n", "       -1.23046875e-01, -5.29785156e-02, -1.79687500e-01, -2.18750000e-01,\n", "       -7.66601562e-02,  1.08886719e-01,  1.25976562e-01, -2.92968750e-01,\n", "       -2.77343750e-01,  1.91650391e-02, -1.92382812e-01, -3.68652344e-02,\n", "        1.68457031e-02,  3.16406250e-01, -1.59179688e-01,  1.20605469e-01,\n", "       -1.60156250e-01,  1.04492188e-01, -1.49414062e-01, -6.73828125e-02,\n", "        1.05468750e-01, -1.80664062e-01, -7.47070312e-02, -2.01171875e-01,\n", "        1.47460938e-01,  1.66015625e-02, -1.40625000e-01, -2.79541016e-02,\n", "       -1.60156250e-01, -4.24804688e-02, -1.20605469e-01, -7.08007812e-02,\n", "       -3.51562500e-01, -2.61718750e-01, -3.49609375e-01,  3.08990479e-04,\n", "       -1.45507812e-01, -8.93554688e-02, -3.49121094e-02,  6.83593750e-02,\n", "        2.49023438e-01, -7.91015625e-02, -2.99072266e-02,  3.73535156e-02,\n", "        2.39257812e-01,  9.08203125e-02,  1.87500000e-01, -2.81250000e-01,\n", "        5.49316406e-02, -1.47460938e-01,  8.25195312e-02,  7.08007812e-02,\n", "        8.10546875e-02,  8.74023438e-02,  3.59375000e-01,  1.09863281e-01,\n", "        8.31604004e-04,  5.83496094e-02, -1.46484375e-01, -2.01171875e-01,\n", "       -3.54003906e-02,  1.13281250e-01,  9.03320312e-02, -1.16210938e-01,\n", "        2.98828125e-01, -4.85229492e-03, -4.22363281e-02, -1.48437500e-01,\n", "        1.33789062e-01, -3.83300781e-02,  6.34765625e-02,  1.75781250e-01,\n", "       -6.34765625e-02, -1.57226562e-01,  1.79443359e-02,  6.62231445e-03,\n", "       -1.74560547e-02,  5.71289062e-02,  3.73535156e-02, -4.58984375e-01,\n", "       -2.58789062e-02,  1.14746094e-01,  4.76074219e-02,  3.90625000e-02,\n", "        8.74023438e-02, -7.27539062e-02, -1.14257812e-01,  5.71289062e-02,\n", "       -1.17797852e-02,  3.83300781e-02,  6.63757324e-04, -1.22070312e-03,\n", "       -1.38671875e-01,  1.10351562e-01, -1.01074219e-01,  6.98242188e-02,\n", "       -1.66015625e-01, -2.00195312e-01, -1.25000000e-01, -6.93359375e-02,\n", "       -1.54296875e-01,  1.09863281e-01,  3.27148438e-02,  4.83398438e-02,\n", "       -9.42382812e-02,  8.98437500e-02,  1.14746094e-01, -4.66308594e-02,\n", "       -6.29882812e-02,  1.50146484e-02, -1.55273438e-01, -1.20605469e-01,\n", "        2.23632812e-01,  1.99218750e-01, -2.11914062e-01, -6.54296875e-02,\n", "       -3.12500000e-01,  8.74023438e-02, -2.22656250e-01,  1.23046875e-01,\n", "        4.76074219e-03, -7.12890625e-02,  1.62109375e-01,  9.13085938e-02],\n", "      dtype=float32)"]}, "metadata": {}, "execution_count": 5}]}, {"cell_type": "code", "source": ["model['human'].shape"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "BK2udz1blfmV", "outputId": "174f35ce-0698-414e-f95b-22491cda0c08"}, "execution_count": 6, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(300,)"]}, "metadata": {}, "execution_count": 6}]}, {"cell_type": "code", "source": ["model['man']"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gsGZdVeklzjN", "outputId": "9b8fe463-a770-4a97-ef0c-952a9f4d524a"}, "execution_count": 7, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([ 0.32617188,  0.13085938,  0.03466797, -0.08300781,  0.08984375,\n", "       -0.04125977, -0.19824219,  0.00689697,  0.14355469,  0.0019455 ,\n", "        0.02880859, -0.25      , -0.08398438, -0.15136719, -0.10205078,\n", "        0.04077148, -0.09765625,  0.05932617,  0.02978516, -0.10058594,\n", "       -0.13085938,  0.001297  ,  0.02612305, -0.27148438,  0.06396484,\n", "       -0.19140625, -0.078125  ,  0.25976562,  0.375     , -0.04541016,\n", "        0.16210938,  0.13671875, -0.06396484, -0.02062988, -0.09667969,\n", "        0.25390625,  0.24804688, -0.12695312,  0.07177734,  0.3203125 ,\n", "        0.03149414, -0.03857422,  0.21191406, -0.00811768,  0.22265625,\n", "       -0.13476562, -0.07617188,  0.01049805, -0.05175781,  0.03808594,\n", "       -0.13378906,  0.125     ,  0.0559082 , -0.18261719,  0.08154297,\n", "       -0.08447266, -0.07763672, -0.04345703,  0.08105469, -0.01092529,\n", "        0.17480469,  0.30664062, -0.04321289, -0.01416016,  0.09082031,\n", "       -0.00927734, -0.03442383, -0.11523438,  0.12451172, -0.0246582 ,\n", "        0.08544922,  0.14355469, -0.27734375,  0.03662109, -0.11035156,\n", "        0.13085938, -0.01721191, -0.08056641, -0.00708008, -0.02954102,\n", "        0.30078125, -0.09033203,  0.03149414, -0.18652344, -0.11181641,\n", "        0.10253906, -0.25976562, -0.02209473,  0.16796875, -0.05322266,\n", "       -0.14550781, -0.01049805, -0.03039551, -0.03857422,  0.11523438,\n", "       -0.0062561 , -0.13964844,  0.08007812,  0.06103516, -0.15332031,\n", "       -0.11132812, -0.14160156,  0.19824219, -0.06933594,  0.29296875,\n", "       -0.16015625,  0.20898438,  0.00041771,  0.01831055, -0.20214844,\n", "        0.04760742,  0.05810547, -0.0123291 , -0.01989746, -0.00364685,\n", "       -0.0135498 , -0.08251953, -0.03149414,  0.00717163,  0.20117188,\n", "        0.08300781, -0.0480957 , -0.26367188, -0.09667969, -0.22558594,\n", "       -0.09667969,  0.06494141, -0.02502441,  0.08496094,  0.03198242,\n", "       -0.07568359, -0.25390625, -0.11669922, -0.01446533, -0.16015625,\n", "       -0.00701904, -0.05712891,  0.02807617, -0.09179688,  0.25195312,\n", "        0.24121094,  0.06640625,  0.12988281,  0.17089844, -0.13671875,\n", "        0.1875    , -0.10009766, -0.04199219, -0.12011719,  0.00524902,\n", "        0.15625   , -0.203125  , -0.07128906, -0.06103516,  0.01635742,\n", "        0.18261719,  0.03588867, -0.04248047,  0.16796875, -0.15039062,\n", "       -0.16992188,  0.01831055,  0.27734375, -0.01269531, -0.0390625 ,\n", "       -0.15429688,  0.18457031, -0.07910156,  0.09033203, -0.02709961,\n", "        0.08251953,  0.06738281, -0.16113281, -0.19628906, -0.15234375,\n", "       -0.04711914,  0.04760742,  0.05908203, -0.16894531, -0.14941406,\n", "        0.12988281,  0.04321289,  0.02624512, -0.1796875 , -0.19628906,\n", "        0.06445312,  0.08935547,  0.1640625 , -0.03808594, -0.09814453,\n", "       -0.01483154,  0.1875    ,  0.12792969,  0.22753906,  0.01818848,\n", "       -0.07958984, -0.11376953, -0.06933594, -0.15527344, -0.08105469,\n", "       -0.09277344, -0.11328125, -0.15136719, -0.08007812, -0.05126953,\n", "       -0.15332031,  0.11669922,  0.06835938,  0.0324707 , -0.33984375,\n", "       -0.08154297, -0.08349609,  0.04003906,  0.04907227, -0.24121094,\n", "       -0.13476562, -0.05932617,  0.12158203, -0.34179688,  0.16503906,\n", "        0.06176758, -0.18164062,  0.20117188, -0.07714844,  0.1640625 ,\n", "        0.00402832,  0.30273438, -0.10009766, -0.13671875, -0.05957031,\n", "        0.0625    , -0.21289062, -0.06542969,  0.1796875 , -0.07763672,\n", "       -0.01928711, -0.15039062, -0.00106049,  0.03417969,  0.03344727,\n", "        0.19335938,  0.01965332, -0.19921875, -0.10644531,  0.01525879,\n", "        0.00927734,  0.01416016, -0.02392578,  0.05883789,  0.02368164,\n", "        0.125     ,  0.04760742, -0.05566406,  0.11572266,  0.14746094,\n", "        0.1015625 , -0.07128906, -0.07714844, -0.12597656,  0.0291748 ,\n", "        0.09521484, -0.12402344, -0.109375  , -0.12890625,  0.16308594,\n", "        0.28320312, -0.03149414,  0.12304688, -0.23242188, -0.09375   ,\n", "       -0.12988281,  0.0135498 , -0.03881836, -0.08251953,  0.00897217,\n", "        0.16308594,  0.10546875, -0.13867188, -0.16503906, -0.03857422,\n", "        0.10839844, -0.10498047,  0.06396484,  0.38867188, -0.05981445,\n", "       -0.0612793 , -0.10449219, -0.16796875,  0.07177734,  0.13964844,\n", "        0.15527344, -0.03125   , -0.20214844, -0.12988281, -0.10058594,\n", "       -0.06396484, -0.08349609, -0.30273438, -0.08007812,  0.02099609],\n", "      dtype=float32)"]}, "metadata": {}, "execution_count": 7}]}, {"cell_type": "code", "source": ["model.most_similar('man')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cgOrETj0l4Tt", "outputId": "ad90072b-bf34-41c7-dcea-14e001de72ec"}, "execution_count": 8, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[('woman', 0.7664012908935547),\n", " ('boy', 0.6824871301651001),\n", " ('teenager', 0.6586930155754089),\n", " ('teenage_girl', 0.6147903203964233),\n", " ('girl', 0.5921714305877686),\n", " ('robber', 0.5585119128227234),\n", " ('<PERSON><PERSON>_suspect', 0.5584409832954407),\n", " ('teen_ager', 0.5549196600914001),\n", " ('men', 0.5489763021469116),\n", " ('guy', 0.5420035123825073)]"]}, "metadata": {}, "execution_count": 8}]}, {"cell_type": "code", "source": ["model.most_similar('keyboard')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "U3KHS6ryl8CN", "outputId": "6b6744ff-0314-48b6-8940-b73cb2686be4"}, "execution_count": 9, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[('keyboards', 0.7883305549621582),\n", " ('Keyboard', 0.7165083289146423),\n", " ('touchpad', 0.7082577347755432),\n", " ('trackpad', 0.7039602994918823),\n", " ('keypad', 0.6944352984428406),\n", " ('qwerty_keyboard', 0.6921881437301636),\n", " ('stylus', 0.6857424378395081),\n", " ('Qwerty_keyboard', 0.6761177182197571),\n", " ('scroll_wheel', 0.6746445894241333),\n", " ('onscreen_keyboard', 0.6673976182937622)]"]}, "metadata": {}, "execution_count": 9}]}, {"cell_type": "code", "source": ["model.most_similar('cricket')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "A9Eg2YBPl_i1", "outputId": "6bb6f5e6-4229-4519-cead-f11d3104f5e5"}, "execution_count": 10, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[('cricketing', 0.8372225761413574),\n", " ('cricketers', 0.8165745735168457),\n", " ('Test_cricket', 0.8094819188117981),\n", " ('Twenty##_cricket', 0.8068488240242004),\n", " ('Twenty##', 0.7624265551567078),\n", " ('Cricket', 0.75413978099823),\n", " ('cricketer', 0.7372578382492065),\n", " ('twenty##', 0.7316356897354126),\n", " ('T##_cricket', 0.7304614186286926),\n", " ('West_Indies_cricket', 0.6987985968589783)]"]}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "code", "source": ["model.similarity('man','woman')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aBrKjxzwmDft", "outputId": "96cfcd71-821a-4a13-b6cd-d2c3b62e9073"}, "execution_count": 11, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0.76640123"]}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "code", "source": ["model.similarity('man','python')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "EdT-Ff-rmJul", "outputId": "6ba0586c-5334-4e50-cfe8-b384188a8d21"}, "execution_count": 12, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0.2097966"]}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "code", "source": ["model.doesnt_match(['JAVA','python','elephant'])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 36}, "id": "rQaPIaeWmMjt", "outputId": "5de8529f-2f6e-4e9f-8216-f12f8604a91f"}, "execution_count": 15, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'JAVA'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 15}]}, {"cell_type": "code", "source": ["x = model['king'] - model['man'] + model['woman']"], "metadata": {"id": "aepXq6PMmWD1"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["model.most_similar([x])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Vrsp1kEkmh8l", "outputId": "45b52f51-f93e-4521-f0c7-748c4f0135e7"}, "execution_count": 18, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[('king', 0.8449392318725586),\n", " ('queen', 0.7300517559051514),\n", " ('monarch', 0.645466148853302),\n", " ('princess', 0.6156251430511475),\n", " ('crown_prince', 0.5818676352500916),\n", " ('prince', 0.5777117609977722),\n", " ('kings', 0.5613663792610168),\n", " ('sultan', 0.5376775860786438),\n", " ('queens', 0.5289887189865112),\n", " ('ruler', 0.5247419476509094)]"]}, "metadata": {}, "execution_count": 18}]}, {"cell_type": "code", "source": ["model['king'][0:50]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "52o-W5-kmiTF", "outputId": "08907d80-a7b6-44a0-da25-c19adc8915c8"}, "execution_count": 19, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([ 0.12597656,  0.02978516,  0.00860596,  0.13964844, -0.02563477,\n", "       -0.03613281,  0.11181641, -0.19824219,  0.05126953,  0.36328125,\n", "       -0.2421875 , -0.30273438, -0.17773438, -0.02490234, -0.16796875,\n", "       -0.16992188,  0.03466797,  0.00521851,  0.04638672,  0.12890625,\n", "        0.13671875,  0.11279297,  0.05957031,  0.13671875,  0.10107422,\n", "       -0.17675781, -0.25195312,  0.05981445,  0.34179688, -0.03112793,\n", "        0.10449219,  0.06176758,  0.12451172,  0.40039062, -0.32226562,\n", "        0.08398438,  0.0390625 ,  0.00585938,  0.0703125 ,  0.17285156,\n", "        0.13867188, -0.23144531,  0.28320312,  0.14257812,  0.34179688,\n", "       -0.02392578, -0.10986328,  0.03320312, -0.0546875 ,  0.01531982],\n", "      dtype=float32)"]}, "metadata": {}, "execution_count": 19}]}, {"cell_type": "code", "source": ["import seaborn as sns\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "# king, queen, woman, girl, boy, man, water\n", "words = ['queen', 'woman', 'girl', 'boy', 'man', 'king', 'water']\n", "embeddings = np.array([\n", "    model['queen'][0:10],\n", "    model['woman'][0:10],\n", "    model['girl'][0:10],\n", "    model['boy'][0:10],\n", "    model['man'][0:10],\n", "    model['king'][0:10],\n", "    model['water'][0:10],\n", "                       ])\n", "\n", "df = pd.DataFrame(embeddings, index=words)"], "metadata": {"id": "DEowTdygoWiW"}, "execution_count": 20, "outputs": []}, {"cell_type": "code", "source": ["# Create a heatmap\n", "plt.figure(figsize=(18,16))\n", "heatmap = sns.heatmap(df, cmap='crest')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "VC8yro9mob29", "outputId": "e7c5d537-f02a-4348-9c52-0934efd6765c"}, "execution_count": 21, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1800x1600 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": [], "metadata": {"id": "EIVJcix_odY1"}, "execution_count": null, "outputs": []}]}