{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders import PyPDFLoader\n", "from langchain_community.document_loaders import PyPDFDirectoryLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_community.vectorstores import FAISS\n", "\n", "from langchain_community.embeddings import HuggingFaceBgeEmbeddings\n", "from langchain.prompts import PromptTemplate\n", "\n", "from langchain.chains import RetrievalQA\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(page_content='Health Insurance Coverage Status and Type \\nby Geography: 2021 and 2022\\nAmerican Community Survey Briefs\\nACSBR-015Issued September 2023Douglas <PERSON> and Breauna Branch\\nINTRODUCTION\\nDemographic shifts as well as economic and govern-\\nment policy changes can affect people’s access to health coverage. For example, between 2021 and 2022, the labor market continued to improve, which may have affected private coverage in the United States \\nduring that time.\\n1 Public policy changes included \\nthe renewal of the Public Health Emergency, which \\nallowed Medicaid enrollees to remain covered under the Continuous Enrollment Provision.\\n2 The American \\nRescue Plan (ARP) enhanced Marketplace premium subsidies for those with incomes above 400 percent of the poverty level as well as for unemployed people.\\n3', metadata={'source': 'us_census\\\\acsbr-015.pdf', 'page': 0})"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["## Read the ppdfs from the folder\n", "loader=PyPDFDirectoryLoader(\"./us_census\")\n", "\n", "documents=loader.load()\n", "\n", "text_splitter=RecursiveCharacterTextSplitter(chunk_size=1000,chunk_overlap=200)\n", "\n", "final_documents=text_splitter.split_documents(documents)\n", "final_documents[0]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["316"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["len(final_documents)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["e:\\Langchain\\venv\\lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["## Embedding Using Huggingface\n", "huggingface_embeddings=HuggingFaceBgeEmbeddings(\n", "    model_name=\"BAAI/bge-small-en-v1.5\",      #sentence-transformers/all-MiniLM-l6-v2\n", "    model_kwargs={'device':'cpu'},\n", "    encode_kwargs={'normalize_embeddings':True}\n", "\n", ")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[-8.46568644e-02 -1.19099217e-02 -3.37892585e-02  2.94559337e-02\n", "  5.19159958e-02  5.73839322e-02 -4.10017520e-02  2.74268016e-02\n", " -1.05128214e-01 -1.58056132e-02  7.94858634e-02  5.64318486e-02\n", " -1.31765157e-02 -3.41543928e-02  5.81604475e-03  4.72547710e-02\n", " -1.30746774e-02  3.12988879e-03 -3.44225690e-02  3.08406260e-02\n", " -4.09086421e-02  3.52737904e-02 -2.43761651e-02 -4.35831733e-02\n", "  2.41503324e-02  1.31986588e-02 -4.84452769e-03  1.92347374e-02\n", " -5.43912649e-02 -1.42735064e-01  5.15528210e-03  2.93115843e-02\n", " -5.60810715e-02 -8.53536371e-03  3.14141326e-02  2.76736189e-02\n", " -2.06188373e-02  8.24231505e-02  4.15425114e-02  5.79654835e-02\n", " -3.71587239e-02  6.26157876e-03 -2.41390076e-02 -5.61792636e-03\n", " -2.51715183e-02  5.04967198e-03 -2.52801236e-02 -2.91945064e-03\n", " -8.24046135e-03 -5.69604561e-02  2.30822619e-02 -5.54219959e-03\n", "  5.11555411e-02  6.09937944e-02  6.49766475e-02 -5.38514033e-02\n", "  2.19109710e-02 -2.54194364e-02 -4.49223518e-02  4.22459245e-02\n", "  4.75252084e-02  7.23217207e-04 -2.61084497e-01  9.30173397e-02\n", "  1.13597196e-02  4.90668938e-02 -1.06287086e-02 -8.08727555e-03\n", " -1.53562622e-02 -5.33785969e-02 -6.89967200e-02  4.75178175e-02\n", " -5.68596013e-02  9.38643236e-03  4.24065925e-02  2.54346598e-02\n", "  9.67096724e-03  7.90799968e-03  2.25160886e-02  1.91007671e-03\n", "  3.06091923e-02  2.43992303e-02 -1.34115480e-02 -4.77401279e-02\n", "  4.89939749e-02 -9.49416459e-02  5.62894158e-02 -4.76260781e-02\n", "  2.81447303e-02 -2.54329499e-02 -3.84951308e-02  1.00940112e-02\n", "  1.90582985e-04  3.36625241e-02  1.00181838e-02  2.83524077e-02\n", " -2.68963701e-03 -6.96359901e-03 -3.54914516e-02  3.42758894e-01\n", " -1.94496289e-02  1.43988123e-02 -5.68810571e-03  1.71480775e-02\n", " -2.88607483e-03 -5.81653193e-02  6.35178352e-04  5.17297909e-03\n", "  2.06331387e-02  1.65708251e-02  2.15096809e-02 -2.38796007e-02\n", "  2.89275143e-02  4.67319153e-02 -3.56104821e-02 -1.05078742e-02\n", "  3.70704755e-02  1.57502498e-02  9.43095461e-02 -2.50715371e-02\n", " -9.55965184e-03  1.78565886e-02 -9.41779092e-03 -4.57858741e-02\n", "  1.82930417e-02  5.81431836e-02  4.94311154e-02  1.46350682e-01\n", "  2.16057692e-02 -3.92896198e-02  1.03241250e-01 -3.48299928e-02\n", " -6.61871349e-03  7.07991235e-03  9.26990295e-04  4.49867966e-03\n", " -2.89777480e-02  4.02419232e-02 -5.23192994e-03  4.59961966e-02\n", "  4.23976406e-03 -4.83790739e-03 -3.23238224e-03 -1.41072914e-01\n", " -3.76811475e-02  1.83623895e-01 -2.96609867e-02  4.90660444e-02\n", "  3.90551575e-02 -1.57757346e-02 -3.86351012e-02  4.65630889e-02\n", " -2.43486036e-02  3.57695147e-02 -3.54947336e-02  2.36265883e-02\n", " -3.41984764e-04  3.11703496e-02 -2.39356644e-02 -5.94757982e-02\n", "  6.06259257e-02 -3.81902158e-02 -7.04255328e-02  1.42479865e-02\n", "  3.34432051e-02 -3.85255218e-02 -1.71951596e-02 -7.12288395e-02\n", "  2.64976211e-02  1.09495688e-02  1.32650323e-02  3.89527939e-02\n", "  1.60355382e-02 -3.17630395e-02  1.02013692e-01  2.92911958e-02\n", " -2.29205769e-02 -8.38053040e-03 -1.72173064e-02 -6.78820610e-02\n", "  5.39418403e-03 -2.32346989e-02 -6.07407168e-02 -3.86575758e-02\n", " -1.54306367e-02 -3.84982936e-02 -5.02867699e-02  5.04235253e-02\n", "  4.94898260e-02 -1.41083300e-02 -2.98144598e-03  9.76440133e-05\n", " -6.59190416e-02  3.01006734e-02 -5.46592870e-04 -1.64787639e-02\n", " -5.21614477e-02 -3.30222957e-03  4.75748219e-02 -3.40808518e-02\n", " -2.98659913e-02  2.75014956e-02  5.90203749e-03 -2.64040008e-03\n", " -1.61242671e-02  2.05222610e-02  1.21105220e-02 -5.49782291e-02\n", "  5.10389581e-02 -7.92088546e-03  7.25206453e-03  3.51751335e-02\n", "  3.66276912e-02  5.67682087e-04  2.60788649e-02  2.50971057e-02\n", "  1.14481300e-02 -2.54925024e-02  1.96417440e-02  2.84220409e-02\n", "  2.82554235e-02  6.57489598e-02  9.26554129e-02 -2.68629670e-01\n", " -8.90553114e-04  3.16917268e-03  5.08358562e-03 -6.42101169e-02\n", " -4.56614904e-02 -4.62259948e-02  3.60924639e-02  8.29056371e-03\n", "  8.92349407e-02  5.68022132e-02  6.91062305e-03 -1.08684311e-02\n", "  9.36060175e-02  1.03680165e-02 -8.60929266e-02  1.77331921e-02\n", " -2.00802702e-02 -1.85124874e-02  5.62404632e-04 -9.38337017e-03\n", "  7.76061229e-03 -5.37273772e-02 -2.30028406e-02  7.48890713e-02\n", " -1.29693234e-02  6.53716922e-02 -4.24983352e-02 -7.10293651e-02\n", " -1.56803615e-02 -6.23028576e-02  5.36034741e-02 -6.53212238e-03\n", " -1.15985490e-01  6.70967922e-02  1.93367060e-02 -6.67827874e-02\n", " -2.01754435e-03 -6.27636909e-02 -2.95005478e-02 -2.71986630e-02\n", "  4.49796803e-02 -6.61587194e-02  2.13751234e-02 -2.94077471e-02\n", " -5.71503267e-02  4.05282490e-02  7.11039603e-02 -6.80165365e-02\n", "  2.11908873e-02  1.30515285e-02 -2.91152820e-02 -2.25581583e-02\n", " -1.60188414e-02  3.20554040e-02 -5.89460693e-02 -2.97131632e-02\n", "  3.42681594e-02 -1.58376191e-02 -9.31771472e-03  3.59834097e-02\n", "  3.65337380e-03  4.73319739e-02 -1.06235184e-02 -8.69734772e-03\n", " -4.38009948e-02  5.94554143e-03 -2.41493657e-02 -7.79940188e-02\n", "  1.46542490e-02  1.05613861e-02  5.45365028e-02 -3.17896828e-02\n", " -1.26762642e-02  7.92561378e-03 -1.38133150e-02  5.01396768e-02\n", " -7.28576025e-03 -5.23702893e-03 -5.32640517e-02  4.78208959e-02\n", " -5.38353659e-02  1.11437477e-02  3.96674089e-02 -1.93496253e-02\n", "  9.94823873e-03 -3.53479455e-03  3.58561263e-03 -9.61500779e-03\n", "  2.15323791e-02 -1.82350334e-02 -2.15188656e-02 -1.38835954e-02\n", " -1.76698975e-02  3.38015234e-04 -3.84854589e-04 -2.25800529e-01\n", "  4.51243371e-02  1.53376386e-02 -1.76966917e-02 -1.42525993e-02\n", " -7.00285891e-03 -3.13724913e-02  2.13672244e-03 -9.28345323e-03\n", " -1.66986901e-02  4.66264114e-02  7.71809518e-02  1.26696959e-01\n", " -1.83595419e-02 -1.39637077e-02 -1.23306655e-03  5.93339056e-02\n", " -1.37463736e-03  1.98233537e-02 -2.92635858e-02  4.96656746e-02\n", " -6.07207343e-02  1.53544754e-01 -4.67309207e-02  1.97028890e-02\n", " -7.67833516e-02 -7.73231685e-03  3.71618718e-02 -3.00591048e-02\n", "  8.30263086e-03  2.06259061e-02  1.97466253e-03  3.39764208e-02\n", " -1.70869529e-02  4.84796055e-02  1.20781595e-02  1.24999117e-02\n", "  5.61724417e-02  9.88546945e-03  2.13879198e-02 -4.25293520e-02\n", " -1.94036588e-02  2.47837696e-02  1.37260715e-02  6.41119629e-02\n", " -2.84481011e-02 -4.64116633e-02 -5.36255538e-02 -6.95093986e-05\n", "  6.45710081e-02 -4.32005967e-04 -1.32471016e-02  5.85135119e-03\n", "  1.48595814e-02 -5.41847497e-02 -2.02038661e-02 -5.98262921e-02\n", "  3.67029347e-02  1.43319997e-03 -8.64463206e-03  2.90671345e-02\n", "  4.38365377e-02 -7.64942840e-02  1.55717917e-02  6.65831119e-02]\n", "(384,)\n"]}], "source": ["import  numpy as np\n", "print(np.array(huggingface_embeddings.embed_query(final_documents[0].page_content)))\n", "print(np.array(huggingface_embeddings.embed_query(final_documents[0].page_content)).shape)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["## VectorStore Creation\n", "vectorstore=FAISS.from_documents(final_documents[:120],huggingface_embeddings)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2 U.S. Census Bureau\n", "WHAT IS HEALTH INSURANCE COVERAGE?\n", "This brief presents state-level estimates of health insurance coverage \n", "using data from the American Community Survey (ACS). The  \n", "U.S. Census Bureau conducts the ACS throughout the year; the \n", "survey asks respondents to report their coverage at the time of \n", "interview. The resulting measure of health insurance coverage, \n", "therefore, reflects an annual average of current comprehensive \n", "health insurance coverage status.* This uninsured rate measures a \n", "different concept than the measure based on the Current Population \n", "Survey Annual Social and Economic Supplement (CPS ASEC). \n", "For reporting purposes, the ACS broadly classifies health insurance \n", "coverage as private insurance or public insurance. The ACS defines \n", "private health insurance as a plan provided through an employer \n", "or a union, coverage purchased directly by an individual from an \n", "insurance company or through an exchange (such as healthcare.\n"]}], "source": ["## Query using Similarity Search\n", "query=\"WHAT IS HEALTH INSURANCE COVERAGE?\"\n", "relevant_docments=vectorstore.similarity_search(query)\n", "\n", "print(relevant_docments[0].page_content)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tags=['FAISS', 'HuggingFaceBgeEmbeddings'] vectorstore=<langchain_community.vectorstores.faiss.FAISS object at 0x00000159F4860C10> search_kwargs={'k': 3}\n"]}], "source": ["retriever=vectorstore.as_retriever(search_type=\"similarity\",search_kwargs={\"k\":3})\n", "print(retriever)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['HUGGINGFACEHUB_API_TOKEN']=\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["The Hugging Face Hub is an platform with over 350k models, 75k datasets, and 150k demo apps (Spaces), all open source and publicly available, in an online platform where people can easily collaborate and build ML together."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["e:\\Langchain\\venv\\lib\\site-packages\\langchain_core\\_api\\deprecation.py:117: LangChainDeprecationWarning: The class `langchain_community.llms.huggingface_hub.HuggingFaceHub` was deprecated in langchain-community 0.0.21 and will be removed in 0.2.0. Use HuggingFaceEndpoint instead.\n", "  warn_deprecated(\n"]}, {"data": {"text/plain": ["'What is the health insurance coverage?\\n\\nThe health insurance coverage is a contract between the insurer and the insured. The insurer agrees to pay the insured for the medical expenses incurred by the insured. The insured agrees to pay the premium to the insurer.\\n\\nWhat is the health insurance coverage?\\n\\nThe health insurance coverage is a contract between the insurer and the insured. The insurer agrees to pay the insured for the medical expenses incurred by the insured.'"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_community.llms import HuggingFaceHub\n", "\n", "hf=HuggingFaceHub(\n", "    repo_id=\"mistralai/Mistral-7B-v0.1\",\n", "    model_kwargs={\"temperature\":0.1,\"max_length\":500}\n", "\n", ")\n", "query=\"What is the health insurance coverage?\"\n", "hf.invoke(query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Hugging Face models can be run locally through the HuggingFacePipeline class.\n", "from langchain_community.llms.huggingface_pipeline import HuggingFacePipeline\n", "\n", "hf = HuggingFacePipeline.from_model_id(\n", "    model_id=\"mistralai/Mistral-7B-v0.1\",\n", "    task=\"text-generation\",\n", "    pipeline_kwargs={\"temperature\": 0, \"max_new_tokens\": 300}\n", ")\n", "\n", "llm = hf \n", "llm.invoke(query)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["prompt_template=\"\"\"\n", "Use the following piece of context to answer the question asked.\n", "Please try to provide the answer only based on the context\n", "\n", "{context}\n", "Question:{question}\n", "\n", "Helpful Answers:\n", " \"\"\""]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["prompt=PromptTemplate(template=prompt_template,input_variables=[\"context\",\"question\"])"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["retrievalQA=RetrievalQA.from_chain_type(\n", "    llm=hf,\n", "    chain_type=\"stuff\",\n", "    retriever=retriever,\n", "    return_source_documents=True,\n", "    chain_type_kwargs={\"prompt\":prompt}\n", ")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["query=\"\"\"DIFFERENCES IN THE\n", "UNINSURED RATE BY STATE\n", "IN 2022\"\"\""]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Use the following piece of context to answer the question asked.\n", "Please try to provide the answer only based on the context\n", "\n", "comparison of ACS and CPS ASEC measures \n", "of health insurance coverage, refer to < www.\n", "census.gov/topics/health/health-insurance/\n", "guidance.html >.\n", "9 Respondents may have more than one \n", "health insurance coverage type at the time \n", "of interview. As a result, adding the total \n", "number of people with private coverage and \n", "the total number with public coverage will \n", "sum to more than the total number with any \n", "coverage.• From 2021 to 2022, nine states \n", "reported increases in private \n", "coverage, while seven reported \n", "decreases (Appendix Table B-2). \n", "DIFFERENCES IN THE \n", "UNINSURED RATE BY STATE \n", "IN 2022\n", "In 2022, uninsured rates at the \n", "time of interview ranged across \n", "states from a low of 2.4 percent \n", "in Massachusetts to a high of 16.6 \n", "percent in Texas, compared to the \n", "national rate of 8.0 percent.10 Ten \n", "of the 15 states with uninsured \n", "10 The uninsured rates in the District \n", "of Columbia and Massachusetts were not \n", "statistically different.rates above the national aver -\n", "\n", "percent (Appendix Table B-5). \n", "Medicaid coverage accounted \n", "for a portion of that difference. \n", "Medicaid coverage was 22.7 per -\n", "cent in the group of states that \n", "expanded Medicaid eligibility and \n", "18.0 percent in the group of nonex -\n", "pansion states.\n", "CHANGES IN THE UNINSURED \n", "RATE BY STATE FROM 2021 \n", "TO 2022\n", "From 2021 to 2022, uninsured rates \n", "decreased across 27 states, while \n", "only Maine had an increase. The \n", "uninsured rate in Maine increased \n", "from 5.7 percent to 6.6 percent, \n", "although it remained below the \n", "national average. Maine’s uninsured \n", "rate was still below 8.0 percent, \n", "21 <PERSON> and Breauna Branch, \n", "“Health Insurance Coverage Status and Type \n", "by Geography: 2019 and 2021,” 2022, < www.\n", "census.gov/content/dam/Census/library/\n", "publications/2022/acs/acsbr-013.pdf >.\n", "\n", "library/publications/2022/acs/acsbr-013.pdf >.\n", "39 In 2022, the private coverage rates were \n", "not statistically different in North Dakota and \n", "Utah.Figure /five.tab/period.tab\n", "Percentage of Uninsured People for the /two.tab/five.tab Most Populous Metropolitan \n", "Areas/colon.tab /two.tab/zero.tab/two.tab/one.tab and /two.tab/zero.tab/two.tab/two.tab\n", "(Civilian, noninstitutionalized population) /uni00A0\n", "* Denotes a statistically signiﬁcant change between 2021 and 2022 at the 90 percent conﬁdence level.\n", "Note: For information on conﬁdentiality protection, sampling error, nonsampling error, and deﬁnitions in the American Community\n", "Survey, refer to <https://www2.census.gov/programs-surveys/acs/tech_docs/accuracy/ACS_Accuracy_of_Data_2022.pdf>.\n", "Source: U.S. Census Bureau, 2021 and 2022 American Community Survey, 1-year estimates. Boston-Cambridge-Newton/comma.tab MA-NH\n", "San Francisco-Oakland-Berkeley/comma.tab CA\n", "*Detroit-Warren-Dearborn/comma.tab MI\n", "Question:DIFFERENCES IN THE\n", "UNINSURED RATE BY STATE\n", "IN 2022\n", "\n", "Helpful Answers:\n", " 1.\n", " 2.\n", " 3.\n", " 4.\n", " 5.\n", " 6.\n", " 7.\n", " 8.\n", " 9.\n", " 10.\n", " 11.\n", " 12.\n", " 13.\n", " 14.\n", " 15.\n", " 16.\n", " 17.\n", " 18.\n", " 19.\n", " 20.\n", " 21.\n", " 22.\n", "\n"]}], "source": ["# Call the QA chain with our query.\n", "result = retrievalQA.invoke({\"query\": query})\n", "print(result['result'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 2}