{"name": "@university/types", "version": "1.0.0", "description": "Shared TypeScript types for the university system", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist"}, "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"typescript": "^5.3.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}