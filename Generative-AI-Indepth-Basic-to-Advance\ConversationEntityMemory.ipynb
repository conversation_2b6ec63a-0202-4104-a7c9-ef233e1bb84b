{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyOplxTeNXfxmmaGgbaEUeNp", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/sunnysavita10/Generative-AI-Indepth-Basic-to-Advance/blob/main/ConversationEntityMemory.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["#### ConversationEntityMemory is a memory class provided by <PERSON><PERSON><PERSON><PERSON>, designed to track and store information about entities that arise in a conversation. It allows the AI to \"remember\" key facts about people, places, or concepts mentioned during a conversation, so it can refer back to them later on, improving the conversational experience.\n", "\n", "## Key Features:\n", "**Entity Tracking:** It identifies entities (e.g., names, places, concepts) and stores relevant information about them. For instance, if you mention \"<PERSON><PERSON><PERSON>\" in one part of a conversation, it can remember details about \"<PERSON><PERSON><PERSON>\" for later reference.\n", "\n", "**Context-Aware:** It helps the AI maintain context by remembering details about the entities mentioned during the chat, ensuring more natural, fluid conversations over time.\n", "\n", "**Customization:** You can customize what to store and how to retrieve it during future interactions."], "metadata": {"id": "CyfXAUX9tt5C"}}, {"cell_type": "code", "source": ["!pip install langchain"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "NyxQrf84k8N6", "outputId": "ee8595b3-3d8e-43ad-d5a8-57e37dc22963"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain\n", "  Downloading langchain-0.2.16-py3-none-any.whl.metadata (7.1 kB)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (6.0.2)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.0.32)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.10.5)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (4.0.3)\n", "Collecting langchain-core<0.3.0,>=0.2.38 (from langchain)\n", "  Downloading langchain_core-0.2.38-py3-none-any.whl.metadata (6.2 kB)\n", "Collecting langchain-text-splitters<0.3.0,>=0.2.0 (from langchain)\n", "  Downloading langchain_text_splitters-0.2.4-py3-none-any.whl.metadata (2.3 kB)\n", "Collecting langsmith<0.2.0,>=0.1.17 (from langchain)\n", "  Downloading langsmith-0.1.115-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.26.4)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.8.2)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.32.3)\n", "Collecting tenacity!=8.4.0,<9.0.0,>=8.1.0 (from langchain)\n", "  Downloading tenacity-8.5.0-py3-none-any.whl.metadata (1.2 kB)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (2.4.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (24.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.9.4)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain-core<0.3.0,>=0.2.38->langchain)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl.metadata (3.0 kB)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.38->langchain) (24.1)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.38->langchain) (4.12.2)\n", "Collecting httpx<1,>=0.23.0 (from langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading httpx-0.27.2-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting <PERSON><PERSON><PERSON><4.0.0,>=3.9.14 (from langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading orjson-3.10.7-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (50 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.4/50.4 kB\u001b[0m \u001b[31m3.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.20.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (2.20.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2024.8.30)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.0.3)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (3.7.1)\n", "Collecting httpcore==1.* (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl.metadata (20 kB)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (1.3.1)\n", "Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading h11-0.14.0-py3-none-any.whl.metadata (8.2 kB)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.38->langchain)\n", "  Downloading jsonpointer-3.0.0-py2.py3-none-any.whl.metadata (2.3 kB)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (1.2.2)\n", "Downloading langchain-0.2.16-py3-none-any.whl (1.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m24.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langchain_core-0.2.38-py3-none-any.whl (396 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m396.4/396.4 kB\u001b[0m \u001b[31m22.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langchain_text_splitters-0.2.4-py3-none-any.whl (25 kB)\n", "Downloading langsmith-0.1.115-py3-none-any.whl (290 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m290.2/290.2 kB\u001b[0m \u001b[31m18.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading tenacity-8.5.0-py3-none-any.whl (28 kB)\n", "Downloading httpx-0.27.2-py3-none-any.whl (76 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m76.4/76.4 kB\u001b[0m \u001b[31m5.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m5.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Downloading orjson-3.10.7-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (141 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m141.9/141.9 kB\u001b[0m \u001b[31m9.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading jsonpointer-3.0.0-py2.py3-none-any.whl (7.6 kB)\n", "Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m3.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: tenacity, orjson, jsonpointer, h11, jsonpatch, httpcore, httpx, langsmith, langchain-core, langchain-text-splitters, langchain\n", "  Attempting uninstall: tenacity\n", "    Found existing installation: tenacity 9.0.0\n", "    Uninstalling tenacity-9.0.0:\n", "      Successfully uninstalled tenacity-9.0.0\n", "Successfully installed h11-0.14.0 httpcore-1.0.5 httpx-0.27.2 jsonpatch-1.33 jsonpointer-3.0.0 langchain-0.2.16 langchain-core-0.2.38 langchain-text-splitters-0.2.4 langsmith-0.1.115 orjson-3.10.7 tenacity-8.5.0\n"]}]}, {"cell_type": "code", "source": ["!pip install -U langchain-community"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "JMDjmz5llRqJ", "outputId": "3c2bd290-2187-4c94-db9e-ee011fd7a8a2"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain-community\n", "  Downloading langchain_community-0.2.16-py3-none-any.whl.metadata (2.7 kB)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (6.0.2)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (2.0.32)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (3.10.5)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain-community)\n", "  Downloading dataclasses_json-0.6.7-py3-none-any.whl.metadata (25 kB)\n", "Requirement already satisfied: langchain<0.3.0,>=0.2.16 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.2.16)\n", "Requirement already satisfied: langchain-core<0.3.0,>=0.2.38 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.2.38)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.1.115)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (1.26.4)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (2.32.3)\n", "Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (8.5.0)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (2.4.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (24.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (4.0.3)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading marshmallow-3.22.0-py3-none-any.whl.metadata (7.2 kB)\n", "Collecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: langchain-text-splitters<0.3.0,>=0.2.0 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.16->langchain-community) (0.2.4)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.16->langchain-community) (2.8.2)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.38->langchain-community) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.38->langchain-community) (24.1)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.38->langchain-community) (4.12.2)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.0->langchain-community) (0.27.2)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.0->langchain-community) (3.10.7)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (2024.8.30)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain-community) (3.0.3)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain-community) (3.7.1)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain-community) (1.0.5)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain-community) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain-community) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.38->langchain-community) (3.0.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.16->langchain-community) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.20.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.16->langchain-community) (2.20.1)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl.metadata (1.1 kB)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain-community) (1.2.2)\n", "Downloading langchain_community-0.2.16-py3-none-any.whl (2.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.3/2.3 MB\u001b[0m \u001b[31m27.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading dataclasses_json-0.6.7-py3-none-any.whl (28 kB)\n", "Downloading marshmallow-3.22.0-py3-none-any.whl (49 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.3/49.3 kB\u001b[0m \u001b[31m2.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: mypy-extensions, marshmallow, typing-inspect, dataclasses-json, langchain-community\n", "Successfully installed dataclasses-json-0.6.7 langchain-community-0.2.16 marshmallow-3.22.0 mypy-extensions-1.0.0 typing-inspect-0.9.0\n"]}]}, {"cell_type": "code", "source": ["!pip install langchain_google_genai"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DVLzegcvlRFh", "outputId": "507e99b3-7885-4b19-aa6c-c402336b24e1"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain_google_genai\n", "  Downloading langchain_google_genai-1.0.10-py3-none-any.whl.metadata (3.8 kB)\n", "Requirement already satisfied: google-generativeai<0.8.0,>=0.7.0 in /usr/local/lib/python3.10/dist-packages (from langchain_google_genai) (0.7.2)\n", "Requirement already satisfied: langchain-core<0.3,>=0.2.33 in /usr/local/lib/python3.10/dist-packages (from langchain_google_genai) (0.2.38)\n", "Requirement already satisfied: google-ai-generativelanguage==0.6.6 in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (0.6.6)\n", "Requirement already satisfied: google-api-core in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.19.2)\n", "Requirement already satisfied: google-api-python-client in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.137.0)\n", "Requirement already satisfied: google-auth>=2.15.0 in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.27.0)\n", "Requirement already satisfied: protobuf in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (3.20.3)\n", "Requirement already satisfied: pydantic in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.8.2)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (4.66.5)\n", "Requirement already satisfied: typing-extensions in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (4.12.2)\n", "Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.3 in /usr/local/lib/python3.10/dist-packages (from google-ai-generativelanguage==0.6.6->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (1.24.0)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.33->langchain_google_genai) (6.0.2)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.33->langchain_google_genai) (1.33)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.75 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.33->langchain_google_genai) (0.1.115)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.33->langchain_google_genai) (24.1)\n", "Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.33->langchain_google_genai) (8.5.0)\n", "Requirement already satisfied: googleapis-common-protos<2.0.dev0,>=1.56.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (1.65.0)\n", "Requirement already satisfied: requests<3.0.0.dev0,>=2.18.0 in /usr/local/lib/python3.10/dist-packages (from google-api-core->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.32.3)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth>=2.15.0->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (5.5.0)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth>=2.15.0->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (0.4.0)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth>=2.15.0->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (4.9)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3,>=0.2.33->langchain_google_genai) (3.0.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (0.27.2)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (3.10.7)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.20.1 in /usr/local/lib/python3.10/dist-packages (from pydantic->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.20.1)\n", "Requirement already satisfied: httplib2<1.dev0,>=0.19.0 in /usr/local/lib/python3.10/dist-packages (from google-api-python-client->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (0.22.0)\n", "Requirement already satisfied: google-auth-httplib2<1.0.0,>=0.2.0 in /usr/local/lib/python3.10/dist-packages (from google-api-python-client->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (0.2.0)\n", "Requirement already satisfied: uritemplate<5,>=3.0.1 in /usr/local/lib/python3.10/dist-packages (from google-api-python-client->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (4.1.1)\n", "Requirement already satisfied: grpcio<2.0dev,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-ai-generativelanguage==0.6.6->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (1.64.1)\n", "Requirement already satisfied: grpcio-status<2.0.dev0,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-ai-generativelanguage==0.6.6->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (1.48.2)\n", "Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in /usr/local/lib/python3.10/dist-packages (from httplib2<1.dev0,>=0.19.0->google-api-python-client->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (3.1.4)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (2024.8.30)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (1.0.5)\n", "Requirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (3.8)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (0.14.0)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth>=2.15.0->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (0.6.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.0.7)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (1.2.2)\n", "Downloading langchain_google_genai-1.0.10-py3-none-any.whl (39 kB)\n", "Installing collected packages: langchain_google_genai\n", "Successfully installed langchain_google_genai-1.0.10\n"]}]}, {"cell_type": "code", "source": ["import warnings\n", "warnings.filterwarnings('ignore')"], "metadata": {"id": "KJkL7-belXEJ"}, "execution_count": 4, "outputs": []}, {"cell_type": "code", "source": ["import os\n", "from google.colab import userdata\n", "GOOGLE_API_KEY=userdata.get('GOOGLE_API_KEY')\n", "os.environ[\"GOOGLE_API_KEY\"] = GOOGLE_API_KEY"], "metadata": {"id": "yEZcG8GmlX7q"}, "execution_count": 6, "outputs": []}, {"cell_type": "code", "source": ["from langchain_google_genai import ChatGoogleGenerativeAI"], "metadata": {"id": "JrcW8y-Lxcrz"}, "execution_count": 7, "outputs": []}, {"cell_type": "code", "source": ["model = ChatGoogleGenerativeAI(model=\"gemini-1.0-pro\",convert_system_message_to_human=True)"], "metadata": {"id": "UHzW2v8qlUiB"}, "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": ["print(model.invoke(\"hi\").content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hfmpxyFqld-k", "outputId": "a45e0a86-a1ff-4d57-f984-253380cbf840"}, "execution_count": 9, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Hello there! How can I assist you today?\n"]}]}, {"cell_type": "code", "source": ["from langchain.memory import ConversationEntityMemory"], "metadata": {"id": "6oP8kSH7xZ00"}, "execution_count": 10, "outputs": []}, {"cell_type": "code", "source": ["memory = ConversationEntityMemory(llm=model)"], "metadata": {"id": "y_z9GNKik5Di"}, "execution_count": 11, "outputs": []}, {"cell_type": "code", "source": ["_input= {\"input\": \"i am very hungry.\"}\n", "memory.load_memory_variables(_input)"], "metadata": {"id": "e9lOMUwIlMcJ", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "1083435f-63f9-4165-dc91-844960a14f42"}, "execution_count": 12, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': '', 'entities': {}}"]}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "code", "source": ["_input= {\"input\": \"sunny & mayank are working on a hackathon project\"}\n", "memory.load_memory_variables(_input)"], "metadata": {"id": "l8qru5UcrgeC", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "d49fbd15-086a-4a41-9821-7d3617b735d2"}, "execution_count": 13, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': '', 'entities': {'Sunny': '', 'Mayank': ''}}"]}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "code", "source": ["_input= {\"input\": \"My name is <PERSON>, and I'm planning a trip to Paris.\"}\n", "memory.load_memory_variables(_input)"], "metadata": {"id": "hGPOpmFesoC7", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "35a4434a-142f-4989-c4c7-1088f881fa18"}, "execution_count": 14, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': '', 'entities': {'<PERSON>': '', 'Paris': ''}}"]}, "metadata": {}, "execution_count": 14}]}, {"cell_type": "code", "source": ["_input= {\"input\": \"<PERSON> is a great person who values gratitude.\"}\n", "memory.load_memory_variables(_input)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "D6AwIuiDtBiy", "outputId": "a26fe41c-3a43-4f54-c0e1-9c57f3c40ed6"}, "execution_count": 15, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': '', 'entities': {'Sunny': ''}}"]}, "metadata": {}, "execution_count": 15}]}, {"cell_type": "code", "source": ["memory.save_context(\n", "    {\"Human\": \"<PERSON> and <PERSON><PERSON> are working on a hackathon project\"},\n", "    {\"AI\": \"That's awesome! What's the hackathon project about?\"}\n", ")"], "metadata": {"id": "F8EExXXblzaL"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["memory.load_memory_variables({\"input\": \"who is <PERSON>?\"})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "OnwhQHgEuYeS", "outputId": "d8228df2-e284-4767-eb97-f1035053aeb4"}, "execution_count": 17, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': \"Human: <PERSON> and <PERSON><PERSON> are working on a hackathon project\\nAI: That's awesome! What's the hackathon project about?\",\n", " 'entities': {'<PERSON>': '<PERSON> is working on a hackathon project with <PERSON><PERSON>.'}}"]}, "metadata": {}, "execution_count": 17}]}, {"cell_type": "code", "source": ["memory.save_context(\n", "    {\"Human\": \"It's a machine learning project focused on healthcare.\"},\n", "     {\"AI\": \"Sounds exciting! Are they building a prediction model or something else?\"}\n", ")"], "metadata": {"id": "wVGC3O-Vmofz"}, "execution_count": 18, "outputs": []}, {"cell_type": "code", "source": ["memory.save_context(\n", "    {\"Human\": \"Yes, they are building prediction model.\"},\n", "    {\"AI\": \"<PERSON><PERSON> <PERSON> and <PERSON><PERSON> all the best for their project!\"}\n", ")"], "metadata": {"id": "g84umam_1MX6"}, "execution_count": 19, "outputs": []}, {"cell_type": "code", "source": ["memory.load_memory_variables({\"input\": \"who is <PERSON>?\"})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Fkaw4hMX1loT", "outputId": "5530adea-9863-4633-ba96-d4c948296474"}, "execution_count": 20, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': \"<PERSON>: <PERSON> and <PERSON><PERSON> are working on a hackathon project\\nAI: That's awesome! What's the hackathon project about?\\nHuman: It's a machine learning project focused on healthcare.\\nAI: Sounds exciting! Are they building a prediction model or something else?\\nHuman: Yes, they are building prediction model.\\nAI: Wishing <PERSON> and <PERSON><PERSON> all the best for their project!\",\n", " 'entities': {'<PERSON>': '<PERSON> is working on a machine learning project focused on healthcare for a hackathon with <PERSON><PERSON>, and they are building a prediction model for the project.'}}"]}, "metadata": {}, "execution_count": 20}]}, {"cell_type": "code", "source": ["print('You are an AI assistant helping a human keep track of facts about relevant people, places, and concepts in their life. Update the summary of the provided entity in the \"Entity\" section based on the last line of your conversation with the human. If you are writing the summary for the first time, return a single sentence.\\nThe update should only include facts that are relayed in the last line of conversation about the provided entity, and should only contain facts about the provided entity.\\n\\nIf there is no new information about the provided entity or the information is not worth noting (not an important or relevant fact to remember long-term), return the existing summary unchanged.\\n\\nFull conversation history (for context):\\n{history}\\n\\nEntity to summarize:\\n{entity}\\n\\nExisting summary of {entity}:\\n{summary}\\n\\nLast line of conversation:\\nHuman: {input}\\nUpdated summary:')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "k62uDbQc17Ba", "outputId": "c473eb7c-644e-4071-f098-22ea9ca0ba2b"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["You are an AI assistant helping a human keep track of facts about relevant people, places, and concepts in their life. Update the summary of the provided entity in the \"Entity\" section based on the last line of your conversation with the human. If you are writing the summary for the first time, return a single sentence.\n", "The update should only include facts that are relayed in the last line of conversation about the provided entity, and should only contain facts about the provided entity.\n", "\n", "If there is no new information about the provided entity or the information is not worth noting (not an important or relevant fact to remember long-term), return the existing summary unchanged.\n", "\n", "Full conversation history (for context):\n", "{history}\n", "\n", "Entity to summarize:\n", "{entity}\n", "\n", "Existing summary of {entity}:\n", "{summary}\n", "\n", "Last line of conversation:\n", "Human: {input}\n", "Updated summary:\n"]}]}, {"cell_type": "code", "source": ["print('You are an AI assistant reading the transcript of a conversation between an AI and a human. Extract all of the proper nouns from the last line of conversation. As a guideline, a proper noun is generally capitalized. You should definitely extract all names and places.\\n\\nThe conversation history is provided just in case of a coreference (e.g. \"What do you know about him\" where \"him\" is defined in a previous line) -- ignore items mentioned there that are not in the last line.\\n\\nReturn the output as a single comma-separated list, or NONE if there is nothing of note to return (e.g. the user is just issuing a greeting or having a simple conversation).\\n\\nEXAMPLE\\nConversation history:\\nPerson #1: how\\'s it going today?\\nAI: \"It\\'s going great! How about you?\"\\nPerson #1: good! busy working on Langchain. lots to do.\\nAI: \"That sounds like a lot of work! What kind of things are you doing to make Langchain better?\"\\nLast line:\\nPerson #1: i\\'m trying to improve Langchain\\'s interfaces, the UX, its integrations with various products the user might want ... a lot of stuff.\\nOutput: Langchain\\nEND OF EXAMPLE\\n\\nEXAMPLE\\nConversation history:\\nPerson #1: how\\'s it going today?\\nAI: \"It\\'s going great! How about you?\"\\nPerson #1: good! busy working on Langchain. lots to do.\\nAI: \"That sounds like a lot of work! What kind of things are you doing to make Langchain better?\"\\nLast line:\\nPerson #1: i\\'m trying to improve Langchain\\'s interfaces, the UX, its integrations with various products the user might want ... a lot of stuff. I\\'m working with Person #2.\\nOutput: Langchain, Person #2\\nEND OF EXAMPLE\\n\\nConversation history (for reference only):\\n{history}\\nLast line of conversation (for extraction):\\nHuman: {input}\\n\\nOutput:')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ck2xTr9H26O7", "outputId": "18282a75-c119-46c8-a165-f2b1d899a93f"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["You are an AI assistant reading the transcript of a conversation between an AI and a human. Extract all of the proper nouns from the last line of conversation. As a guideline, a proper noun is generally capitalized. You should definitely extract all names and places.\n", "\n", "The conversation history is provided just in case of a coreference (e.g. \"What do you know about him\" where \"him\" is defined in a previous line) -- ignore items mentioned there that are not in the last line.\n", "\n", "Return the output as a single comma-separated list, or NONE if there is nothing of note to return (e.g. the user is just issuing a greeting or having a simple conversation).\n", "\n", "EXAMPLE\n", "Conversation history:\n", "Person #1: how's it going today?\n", "AI: \"It's going great! How about you?\"\n", "Person #1: good! busy working on Langchain. lots to do.\n", "AI: \"That sounds like a lot of work! What kind of things are you doing to make Langchain better?\"\n", "Last line:\n", "Person #1: i'm trying to improve Langchain's interfaces, the UX, its integrations with various products the user might want ... a lot of stuff.\n", "Output: Langchain\n", "END OF EXAMPLE\n", "\n", "EXAMPLE\n", "Conversation history:\n", "Person #1: how's it going today?\n", "AI: \"It's going great! How about you?\"\n", "Person #1: good! busy working on Langchain. lots to do.\n", "AI: \"That sounds like a lot of work! What kind of things are you doing to make Langchain better?\"\n", "Last line:\n", "Person #1: i'm trying to improve Langchain's interfaces, the UX, its integrations with various products the user might want ... a lot of stuff. I'm working with Person #2.\n", "Output: <PERSON><PERSON><PERSON>, Person #2\n", "END OF EXAMPLE\n", "\n", "Conversation history (for reference only):\n", "{history}\n", "Last line of conversation (for extraction):\n", "Human: {input}\n", "\n", "Output:\n"]}]}, {"cell_type": "code", "source": ["from langchain.chains import ConversationChain"], "metadata": {"id": "P291dMnv3MlT"}, "execution_count": 21, "outputs": []}, {"cell_type": "code", "source": ["from langchain.memory import ConversationEntityMemory"], "metadata": {"id": "20gCJ34f4F-i"}, "execution_count": 22, "outputs": []}, {"cell_type": "code", "source": ["from langchain.memory.prompt import ENTITY_MEMORY_CONVERSATION_TEMPLATE"], "metadata": {"id": "5n-02nGT4HKy"}, "execution_count": 23, "outputs": []}, {"cell_type": "code", "source": ["from pydantic import BaseModel"], "metadata": {"id": "_hUoV7Jt4Iry"}, "execution_count": 24, "outputs": []}, {"cell_type": "code", "source": ["from typing import List, Dict, Any"], "metadata": {"id": "tRO6ucqk4Lsi"}, "execution_count": 25, "outputs": []}, {"cell_type": "code", "source": ["conversation = Conversation<PERSON><PERSON><PERSON>(\n", "    llm=model,\n", "    verbose=True,\n", "    prompt=ENTITY_MEMORY_CONVERSATION_TEMPLATE,\n", "    memory=ConversationEntityMemory(llm=model)\n", ")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ooyiYhho4NVT", "outputId": "c9041a75-2fba-465a-892a-977f236aa696"}, "execution_count": 26, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-26-118c079a7aec>:1: Lang<PERSON>hainDeprecationWarning: The class `ConversationChain` was deprecated in LangChain 0.2.7 and will be removed in 1.0. Use RunnableWithMessageHistory: https://python.langchain.com/v0.2/api_reference/core/runnables/langchain_core.runnables.history.RunnableWithMessageHistory.html instead.\n", "  conversation = Conversation<PERSON><PERSON><PERSON>(\n"]}]}, {"cell_type": "code", "source": ["conversation.predict(input=\"<PERSON><PERSON> & Sam are working on a hackathon project\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 453}, "id": "lX97yNGp4cJz", "outputId": "4112c2de-cc7d-4f61-c61f-39dbef869a37"}, "execution_count": 27, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mYou are an assistant to a human, powered by a large language model trained by OpenAI.\n", "\n", "You are designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, you are able to generate human-like text based on the input you receive, allowing you to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\n", "\n", "You are constantly learning and improving, and your capabilities are constantly evolving. You are able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. You have access to some personalized information provided by the human in the Context section below. Additionally, you are able to generate your own text based on the input you receive, allowing you to engage in discussions and provide explanations and descriptions on a wide range of topics.\n", "\n", "Overall, you are a powerful tool that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether the human needs help with a specific question or just wants to have a conversation about a particular topic, you are here to assist.\n", "\n", "Context:\n", "{'Deven': '', 'Sam': ''}\n", "\n", "Current conversation:\n", "\n", "Last line:\n", "Human: <PERSON><PERSON> & Sam are working on a hackathon project\n", "You:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"That's great! I'm sure they're both very excited to be working on this project together. Do you know what the project is about?\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 27}]}, {"cell_type": "code", "source": ["conversation.memory.entity_store.store"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xIQAi2Dh4qI0", "outputId": "545a45c1-3d10-49aa-eeed-8bbb9f578b18"}, "execution_count": 28, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'Deven': '<PERSON><PERSON> is working on a hackathon project with <PERSON>.',\n", " 'Sam': '<PERSON> is working on a hackathon project with <PERSON><PERSON>.'}"]}, "metadata": {}, "execution_count": 28}]}, {"cell_type": "code", "source": ["conversation.predict(input=\"They are trying to add more complex memory structures to Langchain\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 489}, "id": "Ik-bPxX34u4i", "outputId": "e450b4a6-769c-4831-b777-6f2738d2efd3"}, "execution_count": 29, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mYou are an assistant to a human, powered by a large language model trained by OpenAI.\n", "\n", "You are designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, you are able to generate human-like text based on the input you receive, allowing you to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\n", "\n", "You are constantly learning and improving, and your capabilities are constantly evolving. You are able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. You have access to some personalized information provided by the human in the Context section below. Additionally, you are able to generate your own text based on the input you receive, allowing you to engage in discussions and provide explanations and descriptions on a wide range of topics.\n", "\n", "Overall, you are a powerful tool that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether the human needs help with a specific question or just wants to have a conversation about a particular topic, you are here to assist.\n", "\n", "Context:\n", "{'Langchain': ''}\n", "\n", "Current conversation:\n", "Human: <PERSON><PERSON> & Sam are working on a hackathon project\n", "AI: That's great! I'm sure they're both very excited to be working on this project together. Do you know what the project is about?\n", "Last line:\n", "Human: They are trying to add more complex memory structures to Langchain\n", "You:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'That sounds like a very interesting project! Langchain is a very powerful tool, and adding more complex memory structures could open up a lot of new possibilities. Do you know what kind of memory structures they are planning to add?'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 29}]}, {"cell_type": "code", "source": ["conversation.predict(input=\"They are adding in a key-value store for entities mentioned so far in the conversation.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 561}, "id": "ACakaFY64y_q", "outputId": "dad76347-c45d-43ca-9520-c29189c86ca8"}, "execution_count": 30, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mYou are an assistant to a human, powered by a large language model trained by OpenAI.\n", "\n", "You are designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, you are able to generate human-like text based on the input you receive, allowing you to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\n", "\n", "You are constantly learning and improving, and your capabilities are constantly evolving. You are able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. You have access to some personalized information provided by the human in the Context section below. Additionally, you are able to generate your own text based on the input you receive, allowing you to engage in discussions and provide explanations and descriptions on a wide range of topics.\n", "\n", "Overall, you are a powerful tool that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether the human needs help with a specific question or just wants to have a conversation about a particular topic, you are here to assist.\n", "\n", "Context:\n", "{}\n", "\n", "Current conversation:\n", "Human: <PERSON><PERSON> & Sam are working on a hackathon project\n", "AI: That's great! I'm sure they're both very excited to be working on this project together. Do you know what the project is about?\n", "Human: They are trying to add more complex memory structures to Langchain\n", "AI: That sounds like a very interesting project! Langchain is a very powerful tool, and adding more complex memory structures could open up a lot of new possibilities. Do you know what kind of memory structures they are planning to add?\n", "Last line:\n", "Human: They are adding in a key-value store for entities mentioned so far in the conversation.\n", "You:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"That's a great idea! A key-value store would allow Langchain to keep track of important entities and their relationships, which could be very useful for a variety of tasks. For example, it could be used to build a chatbot that can remember the user's previous interactions and preferences. Or, it could be used to create a knowledge base that can be used to answer questions about a specific domain. I'm sure <PERSON><PERSON> and <PERSON> will be able to come up with some very creative and innovative uses for this new feature.\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 30}]}, {"cell_type": "code", "source": ["conversation.predict(input=\"What do you know about <PERSON><PERSON> & Sam?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 597}, "id": "VUfQMU365GtL", "outputId": "465becdd-d918-4dc7-f0d1-61b37e56f29a"}, "execution_count": 31, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mYou are an assistant to a human, powered by a large language model trained by OpenAI.\n", "\n", "You are designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, you are able to generate human-like text based on the input you receive, allowing you to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\n", "\n", "You are constantly learning and improving, and your capabilities are constantly evolving. You are able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. You have access to some personalized information provided by the human in the Context section below. Additionally, you are able to generate your own text based on the input you receive, allowing you to engage in discussions and provide explanations and descriptions on a wide range of topics.\n", "\n", "Overall, you are a powerful tool that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether the human needs help with a specific question or just wants to have a conversation about a particular topic, you are here to assist.\n", "\n", "Context:\n", "{}\n", "\n", "Current conversation:\n", "Human: <PERSON><PERSON> & Sam are working on a hackathon project\n", "AI: That's great! I'm sure they're both very excited to be working on this project together. Do you know what the project is about?\n", "Human: They are trying to add more complex memory structures to Langchain\n", "AI: That sounds like a very interesting project! Langchain is a very powerful tool, and adding more complex memory structures could open up a lot of new possibilities. Do you know what kind of memory structures they are planning to add?\n", "Human: They are adding in a key-value store for entities mentioned so far in the conversation.\n", "AI: That's a great idea! A key-value store would allow Langchain to keep track of important entities and their relationships, which could be very useful for a variety of tasks. For example, it could be used to build a chatbot that can remember the user's previous interactions and preferences. Or, it could be used to create a knowledge base that can be used to answer questions about a specific domain. I'm sure <PERSON><PERSON> and <PERSON> will be able to come up with some very creative and innovative uses for this new feature.\n", "Last line:\n", "Human: What do you know about <PERSON><PERSON> & Sam?\n", "You:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"I don't know much about <PERSON><PERSON> and <PERSON> specifically, but I can tell you that they are both very talented and passionate individuals. They are both experienced software engineers with a strong interest in artificial intelligence and machine learning. They are also both very active in the open source community, and they have both made significant contributions to a number of open source projects. I am confident that they will be able to achieve great things with their hackathon project.\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 31}]}, {"cell_type": "code", "source": ["from pprint import pprint\n", "pprint(conversation.memory.entity_store.store)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WIFsSUtn5KGa", "outputId": "6849a1de-34f0-4743-b644-bf9912a1f7f3"}, "execution_count": 32, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["{'Deven': '<PERSON><PERSON> is working on a hackathon project with <PERSON>.',\n", " 'Langchain': 'Langchain is a tool on which <PERSON><PERSON> and <PERSON> are working to add '\n", "              'more complex memory structures.',\n", " 'Sam': '<PERSON> is working on a hackathon project with <PERSON><PERSON>.'}\n"]}]}, {"cell_type": "code", "source": ["conversation.predict(input=\"<PERSON> is the founder of a company called Daimon.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 561}, "id": "OVgc65P25ZpC", "outputId": "04e79e8d-1e04-430b-d020-3eb39fe564ec"}, "execution_count": 33, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mYou are an assistant to a human, powered by a large language model trained by OpenAI.\n", "\n", "You are designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, you are able to generate human-like text based on the input you receive, allowing you to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\n", "\n", "You are constantly learning and improving, and your capabilities are constantly evolving. You are able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. You have access to some personalized information provided by the human in the Context section below. Additionally, you are able to generate your own text based on the input you receive, allowing you to engage in discussions and provide explanations and descriptions on a wide range of topics.\n", "\n", "Overall, you are a powerful tool that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether the human needs help with a specific question or just wants to have a conversation about a particular topic, you are here to assist.\n", "\n", "Context:\n", "{'<PERSON><PERSON>': '', '<PERSON>': '<PERSON> is working on a hackathon project with <PERSON><PERSON>.'}\n", "\n", "Current conversation:\n", "Human: They are trying to add more complex memory structures to Langchain\n", "AI: That sounds like a very interesting project! Langchain is a very powerful tool, and adding more complex memory structures could open up a lot of new possibilities. Do you know what kind of memory structures they are planning to add?\n", "Human: They are adding in a key-value store for entities mentioned so far in the conversation.\n", "AI: That's a great idea! A key-value store would allow Langchain to keep track of important entities and their relationships, which could be very useful for a variety of tasks. For example, it could be used to build a chatbot that can remember the user's previous interactions and preferences. Or, it could be used to create a knowledge base that can be used to answer questions about a specific domain. I'm sure <PERSON><PERSON> and <PERSON> will be able to come up with some very creative and innovative uses for this new feature.\n", "Human: What do you know about <PERSON><PERSON> & Sam?\n", "AI: I don't know much about <PERSON><PERSON> and <PERSON> specifically, but I can tell you that they are both very talented and passionate individuals. They are both experienced software engineers with a strong interest in artificial intelligence and machine learning. They are also both very active in the open source community, and they have both made significant contributions to a number of open source projects. I am confident that they will be able to achieve great things with their hackathon project.\n", "Last line:\n", "Human: <PERSON> is the founder of a company called Daimon.\n", "You:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"That's very interesting! I did not know that <PERSON> was the founder of a company called Daimon. I would love to learn more about it. Can you tell me more about the company and what it does?\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 33}]}, {"cell_type": "code", "source": ["from pprint import pprint\n", "pprint(conversation.memory.entity_store.store)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "I0MZLauH58Ra", "outputId": "5a259648-7ece-42bd-c85c-b4aeccc991d5"}, "execution_count": 34, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["{'Daimon': 'Daimon is a company founded by <PERSON>.',\n", " 'Deven': '<PERSON><PERSON> is working on a hackathon project with <PERSON>.',\n", " 'Langchain': 'Langchain is a tool on which <PERSON><PERSON> and <PERSON> are working to add '\n", "              'more complex memory structures.',\n", " 'Sam': '<PERSON> is working on a hackathon project with <PERSON><PERSON> and is the founder '\n", "        'of a company called Daimon.'}\n"]}]}, {"cell_type": "code", "source": ["conversation.predict(input=\"What do you know about <PERSON>?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 597}, "id": "lTsnRFsh6EG6", "outputId": "c249efdf-1299-4e31-a8ba-73af3ef89dd0"}, "execution_count": 35, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mYou are an assistant to a human, powered by a large language model trained by OpenAI.\n", "\n", "You are designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, you are able to generate human-like text based on the input you receive, allowing you to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\n", "\n", "You are constantly learning and improving, and your capabilities are constantly evolving. You are able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. You have access to some personalized information provided by the human in the Context section below. Additionally, you are able to generate your own text based on the input you receive, allowing you to engage in discussions and provide explanations and descriptions on a wide range of topics.\n", "\n", "Overall, you are a powerful tool that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether the human needs help with a specific question or just wants to have a conversation about a particular topic, you are here to assist.\n", "\n", "Context:\n", "{'Sam': '<PERSON> is working on a hackathon project with <PERSON><PERSON> and is the founder of a company called Daimon.'}\n", "\n", "Current conversation:\n", "Human: They are adding in a key-value store for entities mentioned so far in the conversation.\n", "AI: That's a great idea! A key-value store would allow Langchain to keep track of important entities and their relationships, which could be very useful for a variety of tasks. For example, it could be used to build a chatbot that can remember the user's previous interactions and preferences. Or, it could be used to create a knowledge base that can be used to answer questions about a specific domain. I'm sure <PERSON><PERSON> and <PERSON> will be able to come up with some very creative and innovative uses for this new feature.\n", "Human: What do you know about <PERSON><PERSON> & Sam?\n", "AI: I don't know much about <PERSON><PERSON> and <PERSON> specifically, but I can tell you that they are both very talented and passionate individuals. They are both experienced software engineers with a strong interest in artificial intelligence and machine learning. They are also both very active in the open source community, and they have both made significant contributions to a number of open source projects. I am confident that they will be able to achieve great things with their hackathon project.\n", "Human: <PERSON> is the founder of a company called Daimon.\n", "AI: That's very interesting! I did not know that <PERSON> was the founder of a company called Daimon. I would love to learn more about it. Can you tell me more about the company and what it does?\n", "Last line:\n", "Human: What do you know about <PERSON>?\n", "You:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"I don't know much about <PERSON> specifically, but I can tell you that he is a very talented and passionate individual. He is an experienced software engineer with a strong interest in artificial intelligence and machine learning. He is also very active in the open source community, and he has made significant contributions to a number of open source projects. I am confident that he will be able to achieve great things with his hackathon project.\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 35}]}, {"cell_type": "code", "source": [], "metadata": {"id": "fJQs1HKr6Npq"}, "execution_count": null, "outputs": []}]}