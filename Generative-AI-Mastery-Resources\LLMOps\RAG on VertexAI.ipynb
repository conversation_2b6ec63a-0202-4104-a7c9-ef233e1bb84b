{"cells": [{"cell_type": "code", "id": "vBG29zWhwWnCbzlDyJPBWIN6", "metadata": {"tags": [], "id": "vBG29zWhwWnCbzlDyJPBWIN6", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1727326200874, "user_tz": -360, "elapsed": 4, "user": {"displayName": "", "userId": ""}}, "outputId": "d1e9b6f4-f253-4d6c-c913-8e06c7fec6c1"}, "source": ["print(\"ok\")"], "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["ok\n"]}]}, {"cell_type": "code", "source": ["# Install some required packages\n", "\n", "!pip install pypdf2\n", "!pip install google-cloud-aiplatform\n", "!pip install google-cloud-storage"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "IJS27zX0qhwo", "executionInfo": {"status": "ok", "timestamp": 1727326220032, "user_tz": -360, "elapsed": 11970, "user": {"displayName": "", "userId": ""}}, "outputId": "25ae6f71-1bd0-41a4-8efe-4e436b3ea451"}, "id": "IJS27zX0qhwo", "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting pypdf2\n", "  Downloading pypdf2-3.0.1-py3-none-any.whl.metadata (6.8 kB)\n", "Downloading pypdf2-3.0.1-py3-none-any.whl (232 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m232.6/232.6 kB\u001b[0m \u001b[31m1.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: pypdf2\n", "Successfully installed pypdf2-3.0.1\n", "Requirement already satisfied: google-cloud-aiplatform in /usr/local/lib/python3.10/dist-packages (1.65.0)\n", "Requirement already satisfied: google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (2.19.2)\n", "Requirement already satisfied: google-auth<3.0.0dev,>=2.14.1 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (2.27.0)\n", "Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.3 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (1.24.0)\n", "Requirement already satisfied: protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<6.0.0dev,>=3.20.2 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (3.20.3)\n", "Requirement already satisfied: packaging>=14.3 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (24.1)\n", "Requirement already satisfied: google-cloud-storage<3.0.0dev,>=1.32.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (2.8.0)\n", "Requirement already satisfied: google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (3.25.0)\n", "Requirement already satisfied: google-cloud-resource-manager<3.0.0dev,>=1.3.3 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (1.12.5)\n", "Requirement already satisfied: shapely<3.0.0dev in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (2.0.6)\n", "Requirement already satisfied: pydantic<3 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (1.10.18)\n", "Requirement already satisfied: docstring-parser<1 in /usr/local/lib/python3.10/dist-packages (from google-cloud-aiplatform) (0.16)\n", "Requirement already satisfied: googleapis-common-protos<2.0.dev0,>=1.56.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (1.65.0)\n", "Requirement already satisfied: requests<3.0.0.dev0,>=2.18.0 in /usr/local/lib/python3.10/dist-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (2.32.3)\n", "Requirement already satisfied: grpcio<2.0dev,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (1.64.1)\n", "Requirement already satisfied: grpcio-status<2.0.dev0,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (1.48.2)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0.0dev,>=2.14.1->google-cloud-aiplatform) (5.5.0)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0.0dev,>=2.14.1->google-cloud-aiplatform) (0.4.0)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0.0dev,>=2.14.1->google-cloud-aiplatform) (4.9)\n", "Requirement already satisfied: google-cloud-core<3.0.0dev,>=1.6.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform) (2.4.1)\n", "Requirement already satisfied: google-resumable-media<3.0dev,>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform) (2.7.2)\n", "Requirement already satisfied: python-dateutil<3.0dev,>=2.7.2 in /usr/local/lib/python3.10/dist-packages (from google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform) (2.8.2)\n", "Requirement already satisfied: grpc-google-iam-v1<1.0.0dev,>=0.12.4 in /usr/local/lib/python3.10/dist-packages (from google-cloud-resource-manager<3.0.0dev,>=1.3.3->google-cloud-aiplatform) (0.13.1)\n", "Requirement already satisfied: typing-extensions>=4.2.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3->google-cloud-aiplatform) (4.12.2)\n", "Requirement already satisfied: numpy<3,>=1.14 in /usr/local/lib/python3.10/dist-packages (from shapely<3.0.0dev->google-cloud-aiplatform) (1.26.4)\n", "Requirement already satisfied: google-crc32c<2.0dev,>=1.0 in /usr/local/lib/python3.10/dist-packages (from google-resumable-media<3.0dev,>=0.6.0->google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform) (1.6.0)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth<3.0.0dev,>=2.14.1->google-cloud-aiplatform) (0.6.0)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil<3.0dev,>=2.7.2->google-cloud-bigquery!=3.20.0,<4.0.0dev,>=1.15.0->google-cloud-aiplatform) (1.16.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0dev,>=1.34.1->google-cloud-aiplatform) (2024.8.30)\n", "Requirement already satisfied: google-cloud-storage in /usr/local/lib/python3.10/dist-packages (2.8.0)\n", "Requirement already satisfied: google-auth<3.0dev,>=1.25.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-storage) (2.27.0)\n", "Requirement already satisfied: google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5 in /usr/local/lib/python3.10/dist-packages (from google-cloud-storage) (2.19.2)\n", "Requirement already satisfied: google-cloud-core<3.0dev,>=2.3.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-storage) (2.4.1)\n", "Requirement already satisfied: google-resumable-media>=2.3.2 in /usr/local/lib/python3.10/dist-packages (from google-cloud-storage) (2.7.2)\n", "Requirement already satisfied: requests<3.0.0dev,>=2.18.0 in /usr/local/lib/python3.10/dist-packages (from google-cloud-storage) (2.32.3)\n", "Requirement already satisfied: googleapis-common-protos<2.0.dev0,>=1.56.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5->google-cloud-storage) (1.65.0)\n", "Requirement already satisfied: protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<6.0.0.dev0,>=3.19.5 in /usr/local/lib/python3.10/dist-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5->google-cloud-storage) (3.20.3)\n", "Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.3 in /usr/local/lib/python3.10/dist-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0dev,>=1.31.5->google-cloud-storage) (1.24.0)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0dev,>=1.25.0->google-cloud-storage) (5.5.0)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0dev,>=1.25.0->google-cloud-storage) (0.4.0)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth<3.0dev,>=1.25.0->google-cloud-storage) (4.9)\n", "Requirement already satisfied: google-crc32c<2.0dev,>=1.0 in /usr/local/lib/python3.10/dist-packages (from google-resumable-media>=2.3.2->google-cloud-storage) (1.6.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0dev,>=2.18.0->google-cloud-storage) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0dev,>=2.18.0->google-cloud-storage) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0dev,>=2.18.0->google-cloud-storage) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0dev,>=2.18.0->google-cloud-storage) (2024.8.30)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth<3.0dev,>=1.25.0->google-cloud-storage) (0.6.0)\n"]}]}, {"cell_type": "code", "source": ["from google.cloud import storage\n", "from vertexai.language_models import TextEmbeddingModel\n", "from google.cloud import aiplatform\n", "import PyPDF2\n", "\n", "import re\n", "import os\n", "import random\n", "import json\n", "import uuid\n"], "metadata": {"id": "_i2lh3_CqjvH", "executionInfo": {"status": "ok", "timestamp": 1727326227949, "user_tz": -360, "elapsed": 3988, "user": {"displayName": "", "userId": ""}}}, "id": "_i2lh3_CqjvH", "execution_count": 3, "outputs": []}, {"cell_type": "code", "source": ["%ls"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "JDZd-aoTqnYu", "executionInfo": {"status": "ok", "timestamp": 1727326255825, "user_tz": -360, "elapsed": 3, "user": {"displayName": "", "userId": ""}}, "outputId": "11fdb336-c149-4397-a458-9cdceedc3b0c"}, "id": "JDZd-aoTqnYu", "execution_count": 4, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["stats.pdf\n"]}]}, {"cell_type": "code", "source": ["# Initialize some variable\n", "\n", "# project=\"your_GCP_project_id\"\n", "location=\"us-central1\"\n", "\n", "pdf_path=\"stats.pdf\"\n", "bucket_name = \"stats-content2024\"\n", "embed_file_path = \"stats_embeddings.json\"\n", "sentence_file_path = \"stats_sentences.json\"\n", "index_name=\"stats_index\"\n"], "metadata": {"id": "7fjg2m8LqvOe", "executionInfo": {"status": "ok", "timestamp": 1727326353363, "user_tz": -360, "elapsed": 2, "user": {"displayName": "", "userId": ""}}}, "id": "7fjg2m8LqvOe", "execution_count": 12, "outputs": []}, {"cell_type": "code", "source": ["# helper\n", "\n", "def extract_sentences_from_pdf(pdf_path):\n", "    with open(pdf_path, 'rb') as file:\n", "        reader = PyPDF2.PdfReader(file)\n", "        text = \"\"\n", "        for page in reader.pages:\n", "            if page.extract_text() is not None:\n", "                text += page.extract_text() + \" \"\n", "    sentences = [sentence.strip() for sentence in text.split('. ') if sentence.strip()]\n", "    return sentences"], "metadata": {"id": "oj4QAgy3qyi9", "executionInfo": {"status": "ok", "timestamp": 1727326356241, "user_tz": -360, "elapsed": 3, "user": {"displayName": "", "userId": ""}}}, "id": "oj4QAgy3qyi9", "execution_count": 13, "outputs": []}, {"cell_type": "code", "source": ["def generate_text_embeddings(sentences) -> list:\n", "  # aiplatform.init(project=project,location=location)\n", "  model = TextEmbeddingModel.from_pretrained(\"textembedding-gecko@001\")\n", "  embeddings = model.get_embeddings(sentences)\n", "  vectors = [embedding.values for embedding in embeddings]\n", "  return vectors"], "metadata": {"id": "WiZ-nSVVq03V", "executionInfo": {"status": "ok", "timestamp": 1727326356965, "user_tz": -360, "elapsed": 2, "user": {"displayName": "", "userId": ""}}}, "id": "WiZ-nSVVq03V", "execution_count": 14, "outputs": []}, {"cell_type": "code", "source": ["def generate_and_save_embeddings(pdf_path, sentence_file_path, embed_file_path):\n", "    def clean_text(text):\n", "        cleaned_text = re.sub(r'\\u2022', '', text)  # Remove bullet points\n", "        cleaned_text = re.sub(r'\\s+', ' ', cleaned_text).strip()  # Remove extra whitespaces and strip\n", "        return cleaned_text\n", "\n", "    sentences = extract_sentences_from_pdf(pdf_path)\n", "    if sentences:\n", "        embeddings = generate_text_embeddings(sentences)\n", "\n", "        with open(embed_file_path, 'w') as embed_file, open(sentence_file_path, 'w') as sentence_file:\n", "            for sentence, embedding in zip(sentences, embeddings):\n", "                cleaned_sentence = clean_text(sentence)\n", "                id = str(uuid.uuid4())\n", "\n", "                embed_item = {\"id\": id, \"embedding\": embedding}\n", "                sentence_item = {\"id\": id, \"sentence\": cleaned_sentence}\n", "\n", "                json.dump(sentence_item, sentence_file)\n", "                sentence_file.write('\\n')\n", "                json.dump(embed_item, embed_file)\n", "                embed_file.write('\\n')"], "metadata": {"id": "sCcPWMSVq2j1", "executionInfo": {"status": "ok", "timestamp": 1727326356965, "user_tz": -360, "elapsed": 1, "user": {"displayName": "", "userId": ""}}}, "id": "sCcPWMSVq2j1", "execution_count": 15, "outputs": []}, {"cell_type": "code", "source": ["def upload_file(bucket_name,file_path):\n", "    storage_client = storage.Client()\n", "    bucket = storage_client.create_bucket(bucket_name,location=location)\n", "    blob = bucket.blob(file_path)\n", "    blob.upload_from_filename(file_path)"], "metadata": {"id": "eBmAHQPJq5Ju", "executionInfo": {"status": "ok", "timestamp": 1727326358432, "user_tz": -360, "elapsed": 1, "user": {"displayName": "", "userId": ""}}}, "id": "eBmAHQPJq5Ju", "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["def create_vector_index(bucket_name, index_name):\n", "    lakeside_index = aiplatform.MatchingEngineIndex.create_tree_ah_index(\n", "    display_name = index_name,\n", "    contents_delta_uri = \"gs://\"+bucket_name,\n", "    dimensions = 768,\n", "    approximate_neighbors_count = 10,\n", "    )\n", "\n", "    lakeside_index_endpoint = aiplatform.MatchingEngineIndexEndpoint.create(\n", "    display_name = index_name,\n", "    public_endpoint_enabled = True\n", "    )\n", "\n", "    lakeside_index_endpoint.deploy_index(\n", "    index = lakeside_index, deployed_index_id = index_name\n", "    )"], "metadata": {"id": "7I-aCNaaq6aG", "executionInfo": {"status": "ok", "timestamp": 1727326359171, "user_tz": -360, "elapsed": 2, "user": {"displayName": "", "userId": ""}}}, "id": "7I-aCNaaq6aG", "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": ["generate_and_save_embeddings(pdf_path,sentence_file_path,embed_file_path)\n", "upload_file(bucket_name,sentence_file_path)"], "metadata": {"id": "8etUF5CEq8Mm", "executionInfo": {"status": "ok", "timestamp": 1727326360690, "user_tz": -360, "elapsed": 1520, "user": {"displayName": "", "userId": ""}}}, "id": "8etUF5CEq8Mm", "execution_count": 18, "outputs": []}, {"cell_type": "code", "source": ["create_vector_index(bucket_name, index_name)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "itPg6OEzq-kO", "executionInfo": {"status": "ok", "timestamp": 1727328100291, "user_tz": -360, "elapsed": 1703164, "user": {"displayName": "", "userId": ""}}, "outputId": "ab4211db-ecd6-4085-bef2-f71359d0eb9e"}, "id": "itPg6OEzq-kO", "execution_count": 19, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["INFO:google.cloud.aiplatform.matching_engine.matching_engine_index:Creating MatchingEngineIndex\n", "INFO:google.cloud.aiplatform.matching_engine.matching_engine_index:Create MatchingEngineIndex backing LRO: projects/509917135302/locations/us-central1/indexes/5993635795099975680/operations/6412509215151095808\n", "INFO:google.cloud.aiplatform.matching_engine.matching_engine_index:MatchingEngineIndex created. Resource name: projects/509917135302/locations/us-central1/indexes/5993635795099975680\n", "INFO:google.cloud.aiplatform.matching_engine.matching_engine_index:To use this MatchingEngineIndex in another session:\n", "INFO:google.cloud.aiplatform.matching_engine.matching_engine_index:index = aiplatform.MatchingEngineIndex('projects/509917135302/locations/us-central1/indexes/5993635795099975680')\n", "INFO:google.cloud.aiplatform.matching_engine.matching_engine_index_endpoint:Creating MatchingEngineIndexEndpoint\n", "INFO:google.cloud.aiplatform.matching_engine.matching_engine_index_endpoint:Create MatchingEngineIndexEndpoint backing LRO: projects/509917135302/locations/us-central1/indexEndpoints/1376179539650019328/operations/8628948734887067648\n", "INFO:google.cloud.aiplatform.matching_engine.matching_engine_index_endpoint:MatchingEngineIndexEndpoint created. Resource name: projects/509917135302/locations/us-central1/indexEndpoints/1376179539650019328\n", "INFO:google.cloud.aiplatform.matching_engine.matching_engine_index_endpoint:To use this MatchingEngineIndexEndpoint in another session:\n", "INFO:google.cloud.aiplatform.matching_engine.matching_engine_index_endpoint:index_endpoint = aiplatform.MatchingEngineIndexEndpoint('projects/509917135302/locations/us-central1/indexEndpoints/1376179539650019328')\n", "INFO:google.cloud.aiplatform.matching_engine.matching_engine_index_endpoint:Deploying index MatchingEngineIndexEndpoint index_endpoint: projects/509917135302/locations/us-central1/indexEndpoints/1376179539650019328\n", "INFO:google.cloud.aiplatform.matching_engine.matching_engine_index_endpoint:Deploy index MatchingEngineIndexEndpoint index_endpoint backing LRO: projects/509917135302/locations/us-central1/indexEndpoints/1376179539650019328/operations/2385165248375029760\n", "INFO:google.cloud.aiplatform.matching_engine.matching_engine_index_endpoint:MatchingEngineIndexEndpoint index_endpoint Deployed index. Resource name: projects/509917135302/locations/us-central1/indexEndpoints/1376179539650019328\n"]}]}, {"cell_type": "code", "source": ["from vertexai.language_models import TextEmbeddingModel\n", "from google.cloud import aiplatform\n", "import vertexai\n", "from vertexai.preview.generative_models import GenerativeModel, Part\n", "import json\n", "import os"], "metadata": {"id": "iURiCr76rRue", "executionInfo": {"status": "ok", "timestamp": 1727328143465, "user_tz": -360, "elapsed": 2, "user": {"displayName": "", "userId": ""}}}, "id": "iURiCr76rRue", "execution_count": 20, "outputs": []}, {"cell_type": "code", "source": ["# project=”YOUR_GCP_PROJECT”\n", "location=\"us-central1\"\n", "sentence_file_path = \"stats_sentences.json\"\n", "index_name=\"stats_index\" #Get this from the console or the previous step"], "metadata": {"id": "4JpYpYlAx8Du", "executionInfo": {"status": "ok", "timestamp": 1727328155023, "user_tz": -360, "elapsed": 1, "user": {"displayName": "", "userId": ""}}}, "id": "4JpYpYlAx8Du", "execution_count": 21, "outputs": []}, {"cell_type": "code", "source": ["# aiplatform.init(project=project,location=location)\n", "# vertexai.init()\n", "model = GenerativeModel(\"gemini-pro\")\n", "lakeside_index_ep = aiplatform.MatchingEngineIndexEndpoint(index_endpoint_name=\"1376179539650019328\")"], "metadata": {"id": "TWP8r_VIx-tu", "executionInfo": {"status": "ok", "timestamp": 1727328576029, "user_tz": -360, "elapsed": 751, "user": {"displayName": "", "userId": ""}}}, "id": "TWP8r_VIx-tu", "execution_count": 25, "outputs": []}, {"cell_type": "code", "source": ["def generate_text_embeddings(sentences) -> list:\n", "    model = TextEmbeddingModel.from_pretrained(\"textembedding-gecko@001\")\n", "    embeddings = model.get_embeddings(sentences)\n", "    vectors = [embedding.values for embedding in embeddings]\n", "    return vectors\n", "\n", "\n", "def generate_context(ids,data):\n", "    concatenated_names = ''\n", "    for id in ids:\n", "        for entry in data:\n", "            if entry['id'] == id:\n", "                concatenated_names += entry['sentence'] + \"\\n\"\n", "    return concatenated_names.strip()\n", "\n", "\n", "\n", "def load_file(sentence_file_path):\n", "  data = []\n", "  with open(sentence_file_path,'r') as f:\n", "    for line in f:\n", "      entry = json.loads(line)\n", "      data.append(entry)\n", "  return data\n", "\n", "\n", "\n"], "metadata": {"id": "y5CpZY01yKum", "executionInfo": {"status": "ok", "timestamp": 1727328597001, "user_tz": -360, "elapsed": 2, "user": {"displayName": "", "userId": ""}}}, "id": "y5CpZY01yKum", "execution_count": 26, "outputs": []}, {"cell_type": "code", "source": ["data=load_file(sentence_file_path)\n", "data"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6aCnXfywzqxO", "executionInfo": {"status": "ok", "timestamp": 1727328602867, "user_tz": -360, "elapsed": 4, "user": {"displayName": "", "userId": ""}}, "outputId": "b7ae595b-8d4f-400a-b283-15eca0ec9f7d"}, "id": "6aCnXfywzqxO", "execution_count": 27, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[{'id': 'd587cca8-5563-4321-9a6f-1128ab760880',\n", "  'sentence': 'Importance and the use of correlation in Statistics Introduction Correlation is a statistical measure that expresses the extent to which two variables are linearly related. It is a common tool for describing simple relationships without making a statement about cause and effect. Correlation coefficients range from -1 to 1, with a value of 0 indicating no linear relationship between the two variables, a value of 1 indicating a perfect positive linear relationship, and a value of -1 indicating a perfect negative linear relationship. Correlation is important in statistics because it can be used to 1. Identify relationships between variables: Correlation can be used to identify whether there is a relationship between two variables, and if so, whether the relationship is positive or negative. This information can be useful for understanding the relationships between different factors in a complex system. 2. Make predictions: If there is a strong correlation between two variables, it is possible to use the value of one variable to predict the value of the other variable. This can be useful for making predictions in a variety of fields, such as business, finance, and medicine. 3. Develop causal models: Correlation can be used as a starting point for developing causal models, which are models that describe how changes in one variable cause changes in other variables. Causal models can be used to make more accurate predictions and to develop interventions to change the values of specific variables'},\n", " {'id': 'ef43e9e4-a127-406a-970f-1b4e1842875a',\n", "  'sentence': 'Correlation is used in a wide variety of fields including 1. Business: Correlation can be used to identify relationships between different business variables, such as sales, advertising spending, and customer satisfaction. This information can be used to make better business decisions, such as how to allocate marketing resources. 2. Finance: Correlation can be used to identify relationships between different financial assets, such as stocks, bonds, and commodities. This information can be used to build diversified portfolios that reduce risk. 3. Medicine: Correlation can be used to identify relationships between different medical variables, such as risk factors for diseases and the effectiveness of treatments. This information can be used to improve the prevention, diagnosis, and treatment of diseases. 4. Psychology: Correlation can be used to identify relationships between different psychological variables, such as personality traits, cognitive abilities, and mental disorders. This information can be used to develop better psychological assessments and treatments. How correlation is used in the real world 1. A marketing manager might use correlation to identify the relationship between advertising spending and sales. This information could be used to decide how much money to allocate to advertising. 2. A financial analyst might use correlation to identify the relationship between the returns of different stocks. This information could be used to build a portfolio of stocks that is diversified and has a lower overall risk. 3. A medical researcher might use correlation to identify the relationship between smoking and lung cancer. This information could be used to develop public health campaigns to discourage smoking. 4. A psychologist might use correlation to identify the relationship between anxiety and depression. This information could be used to develop more effective treatments for anxiety and depression. Conclusion It is important to note that correlation does not equal causation. Just because two variables are correlated does not mean that one variable causes the other . For example, there is a strong correlation between ice cream sales and shark attacks. However , this does not mean that eating ice cream causes shark attacks. Instead, there is likely a third variable, such as hot weather , that causes both ice cream sales and shark attacks to increase. Overall, correlation is a powerful statistical tool that can be used to identify relationships between variables, make predictions, and develop causal models. It is used in a wide variety of fields to make better decisions and improve outcomes'}]"]}, "metadata": {}, "execution_count": 27}]}, {"cell_type": "code", "source": ["query=[\"what is correlation?\"]\n", "\n", "qry_emb=generate_text_embeddings(query)"], "metadata": {"id": "kJjWdNaHzsQE", "executionInfo": {"status": "ok", "timestamp": 1727330380465, "user_tz": -360, "elapsed": 701, "user": {"displayName": "", "userId": ""}}}, "id": "kJjWdNaHzsQE", "execution_count": 38, "outputs": []}, {"cell_type": "code", "source": ["# qry_emb"], "metadata": {"id": "e36FrIBmzxWP", "executionInfo": {"status": "ok", "timestamp": 1727330336392, "user_tz": -360, "elapsed": 1, "user": {"displayName": "", "userId": ""}}}, "id": "e36FrIBmzxWP", "execution_count": 35, "outputs": []}, {"cell_type": "code", "source": ["response = lakeside_index_ep.find_neighbors(\n", "    deployed_index_id = index_name,\n", "    queries = [qry_emb[0]],\n", "    num_neighbors = 10\n", ")"], "metadata": {"id": "7oUeWFzezyMG", "executionInfo": {"status": "ok", "timestamp": 1727330385419, "user_tz": -360, "elapsed": 721, "user": {"displayName": "", "userId": ""}}}, "id": "7oUeWFzezyMG", "execution_count": 39, "outputs": []}, {"cell_type": "code", "source": ["matching_ids = [neighbor.id for sublist in response for neighbor in sublist]\n", "\n", "context = generate_context(matching_ids,data)\n", "prompt=f\"Based on the context delimited in backticks, answer the query. ```{context}``` {query}\"\n", "\n", "chat = model.start_chat(history=[])\n", "response = chat.send_message(prompt)\n", "print(response.text)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "iY4Gk2SBz7Cg", "executionInfo": {"status": "ok", "timestamp": 1727330393753, "user_tz": -360, "elapsed": 4748, "user": {"displayName": "", "userId": ""}}, "outputId": "60060dd9-c5ec-40bf-cb34-49822a06dabc"}, "id": "iY4Gk2SBz7Cg", "execution_count": 40, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Correlation is a statistical measure that indicates the extent to which two variables are related. It can range from -1 to 1, where:\n", "\n", "* **-1**: Perfect negative correlation. As one variable increases, the other variable decreases proportionally.\n", "* **0**: No correlation. There is no relationship between the two variables.\n", "* **+1**: Perfect positive correlation. As one variable increases, the other variable also increases proportionally.\n", "\n", "Correlation does not imply causation, meaning that just because two variables are correlated does not mean that one causes the other. Other factors may be responsible for the relationship between the variables.\n", "\n", "Correlation is commonly used in research to identify relationships between variables and to make predictions. However, it is important to note that correlation is not always a reliable indicator of causation.\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "Pgue5OOnz-dG"}, "id": "Pgue5OOnz-dG", "execution_count": null, "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}, "colab": {"provenance": [], "name": "cloud (Sep 26, 2024, 10:48:35 AM)"}}, "nbformat": 4, "nbformat_minor": 5}