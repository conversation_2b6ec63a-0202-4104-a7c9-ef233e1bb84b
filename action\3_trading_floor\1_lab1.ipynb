{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## WELCOME TO THE FINALE - PROJECT 3\n", "\n", "And\n", "\n", "# WELCOME TO THE **M**ODEL **C**ONTEXT **P**ROTOCOL!\n", "\n", "And welcome back to OpenAI Agents SDK\n", "\n", "# Please consider this next hour to be a 'teaser'\n", "\n", "We will fly by a lot of functionality; gain intuition, come back later\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/stop.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">To Windows PC people - known issue with MCP servers on PCs</h2>\n", "            <span style=\"color:#ff7800;\">I have unpleasant news. There's a problem running MCP Servers on Windows PCs; Mac and Linux is fine. This is a known issue as of May 4th, 2025. I asked o3 with Deep Research to try to find workarounds; it <a href=\"https://chatgpt.com/share/6817bbc3-3d0c-8012-9b51-631842470628\">confirmed the issue</a> and confirmed the workaround.<br/><br/>\n", "            The workaround is a bit of a bore. It is to take advantage of \"WSL\", the Microsoft approach for running Linux on your PC. You'll need to carry out more setup instructions! But it's quick, and several students have confirmed that this works perfectly for them, then the Week 6 MCP labs work. Plus, WSL is actually a great way to build software on your Windows PC.<br/>\n", "            The WSL Setup instructions are in the Setup folder, <a href=\"../setup/SETUP-WSL.md\">in the file called SETUP-WSL.md here</a>. I do hope this only holds you up briefly - you should be back up and running quickly. Oh the joys of working with bleeding-edge technology!<br/><br/>\n", "            With many thanks to students <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and several others, for helping me work on it and confirming the fix.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# The imports\n", "\n", "from dotenv import load_dotenv\n", "from agents import Agent, Runner, trace\n", "from agents.mcp import MCPServerStdio\n", "import os\n", "from IPython.display import Markdown, display\n", "load_dotenv(override=True)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You may need to install node if you don't have it already.\n", "\n", "https://chatgpt.com/share/68103af2-e2dc-8012-b259-bc135a23273b"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Let's use MCP in OpenAI Agents SDK\n", "\n", "Fetch MCP tools: runs a headless browser controlled by <PERSON><PERSON>\n", "\n", "Filesystem MCP tools: can read and write from a chosen directory on your computer\n", "\n", "Let's put them together to carry out an important task."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# A python based MCP server\n", "\n", "fetch_params = {\"command\": \"uvx\", \"args\": [\"mcp-server-fetch\"]}\n", "\n", "async with MCPServerStdio(params=fetch_params, client_session_timeout_seconds=60) as server:\n", "    fetch_tools = await server.list_tools()\n", "\n", "fetch_tools"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# A Javacript based MCP server\n", "\n", "playwright_params = {\"command\": \"npx\",\"args\": [ \"@playwright/mcp@latest\"]}\n", "\n", "async with MCPServerStdio(params=playwright_params, client_session_timeout_seconds=60) as server:\n", "    playwright_tools = await server.list_tools()\n", "\n", "playwright_tools"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# A JavaScript based MCP server\n", "\n", "sandbox_path = os.path.abspath(os.path.join(os.getcwd(), \"sandbox\"))\n", "files_params = {\"command\": \"npx\", \"args\": [\"-y\", \"@modelcontextprotocol/server-filesystem\", sandbox_path]}\n", "\n", "async with MCPServerStdio(params=files_params) as server:\n", "    file_tools = await server.list_tools()\n", "\n", "file_tools"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["instructions = \"\"\"\n", "You browse the internet to accomplish your instructions.\n", "Be persistent until you have solved your assignment,\n", "trying different options and sites as needed.\n", "\"\"\"\n", "\n", "\n", "async with MCPServerStdio(params=playwright_params, client_session_timeout_seconds=60) as playwright_mcp_server:\n", "    async with MCPServerStdio(params=files_params, client_session_timeout_seconds=10) as files_mcp_server:\n", "        agent = Agent(\n", "            name=\"investigator\", \n", "            instructions=instructions, \n", "            model=\"gpt-4o-mini\",\n", "            mcp_servers=[playwright_mcp_server, files_mcp_server]\n", "            )\n", "        with trace(\"investigate\"):\n", "            result = await <PERSON>.run(agent, \"Find a great recipe for Banoffee Pie, then summarize it in markdown to banoffee.md\")\n", "            display(Markdown(result.final_output))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}