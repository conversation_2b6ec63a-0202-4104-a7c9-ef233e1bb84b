{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyMAIeL7BnRH5Lu0OSgsgW4c", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/sunnysavita10/Generative-AI-Indepth-Basic-to-Advance/blob/main/Langchain_memory_classes.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ObdXCxM7c9uG", "outputId": "0ef24b8f-e978-4b26-81d5-eea1461629f3"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain\n", "  Downloading langchain-0.2.16-py3-none-any.whl.metadata (7.1 kB)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (6.0.2)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.0.32)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.10.5)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (4.0.3)\n", "Collecting langchain-core<0.3.0,>=0.2.38 (from langchain)\n", "  Downloading langchain_core-0.2.38-py3-none-any.whl.metadata (6.2 kB)\n", "Collecting langchain-text-splitters<0.3.0,>=0.2.0 (from langchain)\n", "  Downloading langchain_text_splitters-0.2.4-py3-none-any.whl.metadata (2.3 kB)\n", "Collecting langsmith<0.2.0,>=0.1.17 (from langchain)\n", "  Downloading langsmith-0.1.111-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.26.4)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.8.2)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.32.3)\n", "Collecting tenacity!=8.4.0,<9.0.0,>=8.1.0 (from langchain)\n", "  Downloading tenacity-8.5.0-py3-none-any.whl.metadata (1.2 kB)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (2.4.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (24.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.9.4)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain-core<0.3.0,>=0.2.38->langchain)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl.metadata (3.0 kB)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.38->langchain) (24.1)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.38->langchain) (4.12.2)\n", "Collecting httpx<1,>=0.23.0 (from langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading httpx-0.27.2-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting <PERSON><PERSON><PERSON><4.0.0,>=3.9.14 (from langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading orjson-3.10.7-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (50 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.4/50.4 kB\u001b[0m \u001b[31m710.6 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.20.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (2.20.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2024.8.30)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.0.3)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (3.7.1)\n", "Collecting httpcore==1.* (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl.metadata (20 kB)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (1.3.1)\n", "Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading h11-0.14.0-py3-none-any.whl.metadata (8.2 kB)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.38->langchain)\n", "  Downloading jsonpointer-3.0.0-py2.py3-none-any.whl.metadata (2.3 kB)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (1.2.2)\n", "Downloading langchain-0.2.16-py3-none-any.whl (1.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m15.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langchain_core-0.2.38-py3-none-any.whl (396 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m396.4/396.4 kB\u001b[0m \u001b[31m23.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langchain_text_splitters-0.2.4-py3-none-any.whl (25 kB)\n", "Downloading langsmith-0.1.111-py3-none-any.whl (288 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m288.5/288.5 kB\u001b[0m \u001b[31m20.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading tenacity-8.5.0-py3-none-any.whl (28 kB)\n", "Downloading httpx-0.27.2-py3-none-any.whl (76 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m76.4/76.4 kB\u001b[0m \u001b[31m5.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m6.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Downloading orjson-3.10.7-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (141 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m141.9/141.9 kB\u001b[0m \u001b[31m10.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading jsonpointer-3.0.0-py2.py3-none-any.whl (7.6 kB)\n", "Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m4.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: tenacity, orjson, jsonpointer, h11, jsonpatch, httpcore, httpx, langsmith, langchain-core, langchain-text-splitters, langchain\n", "  Attempting uninstall: tenacity\n", "    Found existing installation: tenacity 9.0.0\n", "    Uninstalling tenacity-9.0.0:\n", "      Successfully uninstalled tenacity-9.0.0\n", "Successfully installed h11-0.14.0 httpcore-1.0.5 httpx-0.27.2 jsonpatch-1.33 jsonpointer-3.0.0 langchain-0.2.16 langchain-core-0.2.38 langchain-text-splitters-0.2.4 langsmith-0.1.111 orjson-3.10.7 tenacity-8.5.0\n"]}], "source": ["!pip install langchain"]}, {"cell_type": "code", "source": ["!pip install -U langchain-community"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aKUkmjG7obXp", "outputId": "5bbcb0d6-1397-45a8-e3c9-a0b1cc35deeb"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain-community\n", "  Downloading langchain_community-0.2.16-py3-none-any.whl.metadata (2.7 kB)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (6.0.2)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (2.0.32)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (3.10.5)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain-community)\n", "  Downloading dataclasses_json-0.6.7-py3-none-any.whl.metadata (25 kB)\n", "Requirement already satisfied: langchain<0.3.0,>=0.2.16 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.2.16)\n", "Requirement already satisfied: langchain-core<0.3.0,>=0.2.38 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.2.38)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.1.111)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (1.26.4)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (2.32.3)\n", "Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (8.5.0)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (2.4.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (24.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (4.0.3)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading marshmallow-3.22.0-py3-none-any.whl.metadata (7.2 kB)\n", "Collecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: langchain-text-splitters<0.3.0,>=0.2.0 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.16->langchain-community) (0.2.4)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.16->langchain-community) (2.8.2)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.38->langchain-community) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.38->langchain-community) (24.1)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.38->langchain-community) (4.12.2)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.0->langchain-community) (0.27.2)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.0->langchain-community) (3.10.7)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (2024.8.30)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain-community) (3.0.3)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain-community) (3.7.1)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain-community) (1.0.5)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain-community) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain-community) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.38->langchain-community) (3.0.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.16->langchain-community) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.20.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.16->langchain-community) (2.20.1)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl.metadata (1.1 kB)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain-community) (1.2.2)\n", "Downloading langchain_community-0.2.16-py3-none-any.whl (2.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.3/2.3 MB\u001b[0m \u001b[31m25.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading dataclasses_json-0.6.7-py3-none-any.whl (28 kB)\n", "Downloading marshmallow-3.22.0-py3-none-any.whl (49 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.3/49.3 kB\u001b[0m \u001b[31m3.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: mypy-extensions, marshmallow, typing-inspect, dataclasses-json, langchain-community\n", "Successfully installed dataclasses-json-0.6.7 langchain-community-0.2.16 marshmallow-3.22.0 mypy-extensions-1.0.0 typing-inspect-0.9.0\n"]}]}, {"cell_type": "code", "source": ["!pip install langchain_google_genai"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hF9ONG-Xpiwy", "outputId": "13099f6b-f643-46a3-f3fd-bee676630e3c"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain_google_genai\n", "  Downloading langchain_google_genai-1.0.10-py3-none-any.whl.metadata (3.8 kB)\n", "Requirement already satisfied: google-generativeai<0.8.0,>=0.7.0 in /usr/local/lib/python3.10/dist-packages (from langchain_google_genai) (0.7.2)\n", "Requirement already satisfied: langchain-core<0.3,>=0.2.33 in /usr/local/lib/python3.10/dist-packages (from langchain_google_genai) (0.2.38)\n", "Requirement already satisfied: google-ai-generativelanguage==0.6.6 in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (0.6.6)\n", "Requirement already satisfied: google-api-core in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.19.2)\n", "Requirement already satisfied: google-api-python-client in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.137.0)\n", "Requirement already satisfied: google-auth>=2.15.0 in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.27.0)\n", "Requirement already satisfied: protobuf in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (3.20.3)\n", "Requirement already satisfied: pydantic in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.8.2)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (4.66.5)\n", "Requirement already satisfied: typing-extensions in /usr/local/lib/python3.10/dist-packages (from google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (4.12.2)\n", "Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.3 in /usr/local/lib/python3.10/dist-packages (from google-ai-generativelanguage==0.6.6->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (1.24.0)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.33->langchain_google_genai) (6.0.2)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.33->langchain_google_genai) (1.33)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.75 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.33->langchain_google_genai) (0.1.111)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.33->langchain_google_genai) (24.1)\n", "Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.33->langchain_google_genai) (8.5.0)\n", "Requirement already satisfied: googleapis-common-protos<2.0.dev0,>=1.56.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (1.65.0)\n", "Requirement already satisfied: requests<3.0.0.dev0,>=2.18.0 in /usr/local/lib/python3.10/dist-packages (from google-api-core->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.32.3)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth>=2.15.0->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (5.5.0)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth>=2.15.0->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (0.4.0)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth>=2.15.0->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (4.9)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3,>=0.2.33->langchain_google_genai) (3.0.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (0.27.2)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (3.10.7)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.20.1 in /usr/local/lib/python3.10/dist-packages (from pydantic->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.20.1)\n", "Requirement already satisfied: httplib2<1.dev0,>=0.19.0 in /usr/local/lib/python3.10/dist-packages (from google-api-python-client->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (0.22.0)\n", "Requirement already satisfied: google-auth-httplib2<1.0.0,>=0.2.0 in /usr/local/lib/python3.10/dist-packages (from google-api-python-client->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (0.2.0)\n", "Requirement already satisfied: uritemplate<5,>=3.0.1 in /usr/local/lib/python3.10/dist-packages (from google-api-python-client->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (4.1.1)\n", "Requirement already satisfied: grpcio<2.0dev,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-ai-generativelanguage==0.6.6->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (1.64.1)\n", "Requirement already satisfied: grpcio-status<2.0.dev0,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-ai-generativelanguage==0.6.6->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (1.48.2)\n", "Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in /usr/local/lib/python3.10/dist-packages (from httplib2<1.dev0,>=0.19.0->google-api-python-client->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (3.1.4)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (2024.8.30)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (1.0.5)\n", "Requirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (3.8)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (0.14.0)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth>=2.15.0->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (0.6.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core->google-generativeai<0.8.0,>=0.7.0->langchain_google_genai) (2.0.7)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.33->langchain_google_genai) (1.2.2)\n", "Downloading langchain_google_genai-1.0.10-py3-none-any.whl (39 kB)\n", "Installing collected packages: langchain_google_genai\n", "Successfully installed langchain_google_genai-1.0.10\n"]}]}, {"cell_type": "code", "source": ["import warnings\n", "warnings.filterwarnings('ignore')"], "metadata": {"id": "sT-M-IbGO_fX"}, "execution_count": 10, "outputs": []}, {"cell_type": "code", "source": ["import os\n", "from google.colab import userdata\n", "GOOGLE_API_KEY=userdata.get('GOOGLE_API_KEY')\n", "os.environ[\"GOOGLE_API_KEY\"] = GOOGLE_API_KEY"], "metadata": {"id": "1qH4CJp-puOR"}, "execution_count": 11, "outputs": []}, {"cell_type": "code", "source": ["from langchain_google_genai import ChatGoogleGenerativeAI\n", "model = ChatGoogleGenerativeAI(model=\"gemini-1.0-pro\",convert_system_message_to_human=True)"], "metadata": {"id": "Y82qy4YTpmGx"}, "execution_count": 12, "outputs": []}, {"cell_type": "code", "source": ["print(model.invoke(\"hi\").content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mUQPzgTMpnVg", "outputId": "bfb0c6df-6a4c-49fd-fa49-6c8327b20e6a"}, "execution_count": 13, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Hello there! How can I assist you today?\n"]}]}, {"cell_type": "code", "source": ["print(model.invoke(\"hi, how are you please tell me?\").content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AmitzKzGoo6e", "outputId": "7c8307a1-70f1-450d-e61e-ca7e1be332f6"}, "execution_count": 14, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["I am well, thank you for asking. I am a large language model, trained by Google. I do not have personal experiences or feelings, but I am designed to be informative and helpful. How can I assist you today?\n"]}]}, {"cell_type": "code", "source": ["from langchain.memory import ConversationBufferMemory"], "metadata": {"id": "3zgleD2norLX"}, "execution_count": 15, "outputs": []}, {"cell_type": "code", "source": ["memory = ConversationBufferMemory()"], "metadata": {"id": "iK0mnJnho1WH"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["memory.save_context({\"input\": \"Hi\"},\n", "                    {\"output\": \"What's up\"})"], "metadata": {"id": "h2px3_AAo5wX"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": ["memory.load_memory_variables({})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "c_nviz7-o-Lg", "outputId": "e9f3fa5c-f755-4f11-e78f-cd51b4a5774f"}, "execution_count": 18, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': \"Human: Hi\\nAI: What's up\"}"]}, "metadata": {}, "execution_count": 18}]}, {"cell_type": "code", "source": ["memory2 = ConversationBufferMemory(return_messages=True)"], "metadata": {"id": "qtz58I3fpIRv"}, "execution_count": 19, "outputs": []}, {"cell_type": "code", "source": ["memory2.save_context({\"input\": \"Hi\"},\n", "                    {\"output\": \"What's up\"})"], "metadata": {"id": "kIYkuLPspQx_"}, "execution_count": 20, "outputs": []}, {"cell_type": "code", "source": ["memory2.load_memory_variables({})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "BweTLFgYpUVR", "outputId": "bbca8008-2435-4213-b274-80af5f360b7d"}, "execution_count": 21, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': [HumanMessage(content='Hi'), AIMessage(content=\"What's up\")]}"]}, "metadata": {}, "execution_count": 21}]}, {"cell_type": "code", "source": [], "metadata": {"id": "MATKudmrQRdl"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["import os"], "metadata": {"id": "D8mXubm9pZXp"}, "execution_count": 22, "outputs": []}, {"cell_type": "code", "source": ["from langchain.chains import ConversationChain"], "metadata": {"id": "22KrSvL-peAo"}, "execution_count": 23, "outputs": []}, {"cell_type": "code", "source": ["conversation = ConversationChain(llm=model,verbose=True,memory=ConversationBufferMemory())"], "metadata": {"id": "Ie3Onspvp6cP"}, "execution_count": 24, "outputs": []}, {"cell_type": "code", "source": ["conversation.predict(input=\"Hi there!\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 263}, "id": "FtXYKCiQqJSX", "outputId": "2546e8c6-aa8d-4444-8b58-b1e8789e85e1"}, "execution_count": 25, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "\n", "Human: Hi there!\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"Hello! It's nice to meet you.\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 25}]}, {"cell_type": "code", "source": ["conversation.predict(input=\"Nothing much! Just tell me how do a conversation with an AI.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 385}, "id": "T84Xm_07qLlQ", "outputId": "a85b0f33-db4c-467b-c196-2a43aab04f30"}, "execution_count": 26, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "Human: Hi there!\n", "AI: Hello! It's nice to meet you.\n", "Human: Nothing much! Just tell me how do a conversation with an AI.\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"1. **Be clear and concise.** AI can't understand vague or ambiguous language.\\n2. **Use specific examples.** This will help the AI to understand what you're talking about.\\n3. **Be patient.** AI is still under development and it may take some time for it to understand you.\\n4. **Have fun!** Conversations with AI can be a lot of fun. Just relax and be yourself.\\n\\nHere are some tips for having a conversation with an AI:\\n\\n* **Start with a greeting.** This will help to establish a rapport with the AI.\\n* **Ask open-ended questions.** This will give the AI more opportunities to respond.\\n* **Follow up on the AI's responses.** This will show that you're interested in what it has to say.\\n* **Be patient.** AI is still under development and it may take some time for it to understand you.\\n* **Have fun!** Conversations with AI can be a lot of fun. Just relax and be yourself.\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 26}]}, {"cell_type": "code", "source": ["conversation.predict(input=\"how many tips are there can you mention in the numbers\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 524}, "id": "0ckLlsGiqdSH", "outputId": "35d77981-8eb9-40a4-fbbf-7c3bb3b42943"}, "execution_count": 27, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "Human: Hi there!\n", "AI: Hello! It's nice to meet you.\n", "Human: Nothing much! Just tell me how do a conversation with an AI.\n", "AI: 1. **Be clear and concise.** AI can't understand vague or ambiguous language.\n", "2. **Use specific examples.** This will help the AI to understand what you're talking about.\n", "3. **Be patient.** AI is still under development and it may take some time for it to understand you.\n", "4. **Have fun!** Conversations with AI can be a lot of fun. Just relax and be yourself.\n", "\n", "Here are some tips for having a conversation with an AI:\n", "\n", "* **Start with a greeting.** This will help to establish a rapport with the AI.\n", "* **Ask open-ended questions.** This will give the AI more opportunities to respond.\n", "* **Follow up on the AI's responses.** This will show that you're interested in what it has to say.\n", "* **Be patient.** AI is still under development and it may take some time for it to understand you.\n", "* **Have fun!** Conversations with AI can be a lot of fun. Just relax and be yourself.\n", "Human: how many tips are there can you mention in the numbers\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'There are 5 tips mentioned in the numbers:\\n\\n1. Be clear and concise.\\n2. Use specific examples.\\n3. Be patient.\\n4. Have fun!\\n\\n'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 27}]}, {"cell_type": "code", "source": ["conversation.memory.chat_memory.messages"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "by5vIxH2CS3Z", "outputId": "57a704bd-dcb1-44ca-87e7-b47329006f16"}, "execution_count": 28, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[HumanMessage(content='Hi there!'),\n", " AIMessage(content=\"Hello! It's nice to meet you.\"),\n", " HumanMessage(content='Nothing much! Just tell me how do a conversation with an AI.'),\n", " AIMessage(content=\"1. **Be clear and concise.** AI can't understand vague or ambiguous language.\\n2. **Use specific examples.** This will help the AI to understand what you're talking about.\\n3. **Be patient.** AI is still under development and it may take some time for it to understand you.\\n4. **Have fun!** Conversations with AI can be a lot of fun. Just relax and be yourself.\\n\\nHere are some tips for having a conversation with an AI:\\n\\n* **Start with a greeting.** This will help to establish a rapport with the AI.\\n* **Ask open-ended questions.** This will give the AI more opportunities to respond.\\n* **Follow up on the AI's responses.** This will show that you're interested in what it has to say.\\n* **Be patient.** AI is still under development and it may take some time for it to understand you.\\n* **Have fun!** Conversations with AI can be a lot of fun. Just relax and be yourself.\"),\n", " HumanMessage(content='how many tips are there can you mention in the numbers'),\n", " AIMessage(content='There are 5 tips mentioned in the numbers:\\n\\n1. Be clear and concise.\\n2. Use specific examples.\\n3. Be patient.\\n4. Have fun!\\n\\n')]"]}, "metadata": {}, "execution_count": 28}]}, {"cell_type": "code", "source": ["conversation.predict(input=\"can you give me the 3rd tip?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 662}, "id": "rmlGoT6IQsKV", "outputId": "5f27894a-2875-4864-cab2-6b434bad8152"}, "execution_count": 29, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "Human: Hi there!\n", "AI: Hello! It's nice to meet you.\n", "Human: Nothing much! Just tell me how do a conversation with an AI.\n", "AI: 1. **Be clear and concise.** AI can't understand vague or ambiguous language.\n", "2. **Use specific examples.** This will help the AI to understand what you're talking about.\n", "3. **Be patient.** AI is still under development and it may take some time for it to understand you.\n", "4. **Have fun!** Conversations with AI can be a lot of fun. Just relax and be yourself.\n", "\n", "Here are some tips for having a conversation with an AI:\n", "\n", "* **Start with a greeting.** This will help to establish a rapport with the AI.\n", "* **Ask open-ended questions.** This will give the AI more opportunities to respond.\n", "* **Follow up on the AI's responses.** This will show that you're interested in what it has to say.\n", "* **Be patient.** AI is still under development and it may take some time for it to understand you.\n", "* **Have fun!** Conversations with AI can be a lot of fun. Just relax and be yourself.\n", "Human: how many tips are there can you mention in the numbers\n", "AI: There are 5 tips mentioned in the numbers:\n", "\n", "1. Be clear and concise.\n", "2. Use specific examples.\n", "3. Be patient.\n", "4. Have fun!\n", "\n", "\n", "Human: can you give me the 3rd tip?\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'The 3rd tip is: **Be patient.**'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 29}]}, {"cell_type": "markdown", "source": ["# ConversationBufferWindowMemory"], "metadata": {"id": "ayGsHEv9qqNa"}}, {"cell_type": "code", "source": ["from langchain.memory import ConversationBufferWindowMemory"], "metadata": {"id": "M5MfA3coql_o"}, "execution_count": 30, "outputs": []}, {"cell_type": "code", "source": ["window_memory = ConversationBufferWindowMemory(k=1)"], "metadata": {"id": "WADVyAaIq12p"}, "execution_count": 31, "outputs": []}, {"cell_type": "code", "source": ["window_memory.save_context(\n", "    {\"input\": \"Hi\"},\n", "    {\"output\": \"What's up\"}\n", ")"], "metadata": {"id": "afJtNTWrq9Z_"}, "execution_count": 32, "outputs": []}, {"cell_type": "code", "source": ["window_memory.save_context(\n", "    {\"input\": \"Not much, just hanging\"},\n", "    {\"output\": \"Cool\"}\n", ")"], "metadata": {"id": "ymZ7WpfYrR7_"}, "execution_count": 33, "outputs": []}, {"cell_type": "code", "source": ["window_memory.load_memory_variables({})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "PUmMWst6rZdH", "outputId": "50b89e06-ec98-494b-bddc-00272b452b22"}, "execution_count": 34, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': 'Human: Not much, just hanging\\nAI: Cool'}"]}, "metadata": {}, "execution_count": 34}]}, {"cell_type": "code", "source": ["window_memory = ConversationBufferWindowMemory( k=2, return_messages=True)"], "metadata": {"id": "92JAkps_rcHo"}, "execution_count": 35, "outputs": []}, {"cell_type": "code", "source": ["window_memory.save_context(\n", "    {\"input\": \"Hi\"},\n", "    {\"output\": \"What's up\"}\n", ")\n", "window_memory.save_context(\n", "    {\"input\": \"Not much, just hanging\"},\n", "    {\"output\": \"Cool\"}\n", ")\n", "window_memory.save_context(\n", "    {\"input\": \"ok thanks \"},\n", "    {\"output\": \"great thankyou\"}\n", ")\n"], "metadata": {"id": "4I5696VUr4H6"}, "execution_count": 36, "outputs": []}, {"cell_type": "code", "source": ["window_memory.load_memory_variables({})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lTz6A1ZUr-3Q", "outputId": "a21e9c92-a192-46bf-971f-1693bd33ba5f"}, "execution_count": 37, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': [HumanMessage(content='Not much, just hanging'),\n", "  AIMessage(content='Cool'),\n", "  HumanMessage(content='ok thanks '),\n", "  AIMessage(content='great thankyou')]}"]}, "metadata": {}, "execution_count": 37}]}, {"cell_type": "code", "source": ["conversation_window = ConversationChain(\n", "    llm=model,\n", "    memory=window_memory,\n", "    verbose=True\n", ")"], "metadata": {"id": "gSLZAyaCsBSw"}, "execution_count": 41, "outputs": []}, {"cell_type": "code", "source": ["conversation_window.predict(input=\"Hi, what's up?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 263}, "id": "c57Fr0kasY3g", "outputId": "7071ffae-b81c-4e18-d73f-793d65068188"}, "execution_count": 42, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "[HumanMessage(content=\"Hi, what's up?\"), AIMessage(content='Not much, just hanging.'), HumanMessage(content='how we can talk with AI'), AIMessage(content='There are many ways to communicate with AI. One way is through natural language processing (NLP), which allows AI to understand and respond to human language. Another way is through machine learning, which allows AI to learn from data and improve its performance over time.')]\n", "Human: Hi, what's up?\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'Not much, just hanging.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 42}]}, {"cell_type": "code", "source": ["conversation_window.predict(input=\"how we can talk with AI give me 5 points\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 315}, "id": "Cb_2zJAgshtv", "outputId": "60b35011-526d-4dde-8aa1-243df0640938"}, "execution_count": 43, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "[HumanMessage(content='how we can talk with AI'), AIMessage(content='There are many ways to communicate with AI. One way is through natural language processing (NLP), which allows AI to understand and respond to human language. Another way is through machine learning, which allows AI to learn from data and improve its performance over time.'), HumanMessage(content=\"Hi, what's up?\"), AIMessage(content='Not much, just hanging.')]\n", "Human: how we can talk with AI give me 5 points\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'1. **Natural language processing (NLP)**: This allows AI to understand and respond to human language.\\n2. **Machine learning**: This allows AI to learn from data and improve its performance over time.\\n3. **Speech recognition**: This allows AI to convert spoken words into text.\\n4. **Computer vision**: This allows AI to \"see\" and interpret images.\\n5. **Robotics**: This allows AI to control physical devices, such as robots.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 43}]}, {"cell_type": "code", "source": ["conversation_window.predict(input=\"what is a allows AI to 'see' and 'interpret' images?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 263}, "id": "cqjuwol7soep", "outputId": "d3bfead1-8854-4e21-b787-7b4e4ecfff14"}, "execution_count": 47, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "[HumanMessage(content='can you tell me how many tips you genearte in the previous to previous message?'), AIMessage(content='5'), HumanMessage(content='what was the fifth number tips?'), AIMessage(content='I do not have that information in context.')]\n", "Human: what is a allows AI to 'see' and 'interpret' images?\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'Convolutional Neural Network'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 47}]}, {"cell_type": "code", "source": ["conversation_window.predict(input=\"can you tell me how many tips you genearte in the previous to previous message?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 263}, "id": "v5bzPXTys_mw", "outputId": "a0ac905b-2e4f-4851-f425-18238ff600d2"}, "execution_count": 48, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "[HumanMessage(content='what was the fifth number tips?'), AIMessage(content='I do not have that information in context.'), HumanMessage(content=\"what is a allows AI to 'see' and 'interpret' images?\"), AIMessage(content='Convolutional Neural Network')]\n", "Human: can you tell me how many tips you genearte in the previous to previous message?\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'I do not have that information in context.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 48}]}, {"cell_type": "code", "source": ["conversation.predict(input=\"what was the fifth number tips?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 263}, "id": "Dq4X_PVYtJnx", "outputId": "23ac5f3c-c779-464a-ad0a-4f0b248c446b"}, "execution_count": 46, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "[HumanMessage(content=\"what is a allows AI to 'see' and 'interpret' images?\"), AIMessage(content='Computer vision'), HumanMessage(content='can you tell me how many tips you genearte in the previous to previous message?'), AIMessage(content='5')]\n", "Human: what was the fifth number tips?\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'I do not have that information in context.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 46}]}, {"cell_type": "code", "source": ["from langchain.memory import ConversationEntityMemory"], "metadata": {"id": "11Sj-AcdvN-S"}, "execution_count": 146, "outputs": []}, {"cell_type": "code", "source": ["memory = ConversationEntityMemory(llm=model)"], "metadata": {"id": "gx7UfR2b55Wy"}, "execution_count": 147, "outputs": []}, {"cell_type": "code", "source": ["_input = {\"input\": \"<PERSON><PERSON> & Sam are working on a hackathon project\"}"], "metadata": {"id": "h7H3aQ5t6Esy"}, "execution_count": 148, "outputs": []}, {"cell_type": "code", "source": ["memory.load_memory_variables(_input)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "prEkB6V16IHr", "outputId": "a5b093a9-b51e-4469-e212-f873dd0650eb"}, "execution_count": 149, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': '', 'entities': {'Deven': '', 'Sam': ''}}"]}, "metadata": {}, "execution_count": 149}]}, {"cell_type": "code", "source": ["memory.save_context(\n", "    _input,\n", "    {\"output\": \" That sounds like a great project! What kind of project are they working on?\"}\n", ")"], "metadata": {"id": "iEJTouF96LH7"}, "execution_count": 150, "outputs": []}, {"cell_type": "code", "source": ["memory.load_memory_variables({\"input\": 'who is <PERSON>'})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vaefDOzq6mmS", "outputId": "395b1892-ca17-424a-9af4-c1dc1ca9ca6b"}, "execution_count": 151, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': 'Human: <PERSON><PERSON> & Sam are working on a hackathon project\\nAI:  That sounds like a great project! What kind of project are they working on?',\n", " 'entities': {'Sam': '<PERSON> is working on a hackathon project with <PERSON><PERSON>.'}}"]}, "metadata": {}, "execution_count": 151}]}, {"cell_type": "code", "source": ["memory = ConversationEntityMemory(llm=model, return_messages=True)"], "metadata": {"id": "9eHY8f8A7E0K"}, "execution_count": 152, "outputs": []}, {"cell_type": "code", "source": ["memory"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fKCuHE6K8Dvy", "outputId": "fb4bd10c-c3ea-4c5e-9406-2a9ceb9e02ec"}, "execution_count": 153, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["ConversationEntityMemory(return_messages=True, llm=ChatGoogleGenerativeAI(model='models/gemini-1.0-pro', client=<google.ai.generativelanguage_v1beta.services.generative_service.client.GenerativeServiceClient object at 0x7bf6bc68d1e0>, async_client=<google.ai.generativelanguage_v1beta.services.generative_service.async_client.GenerativeServiceAsyncClient object at 0x7bf6bc4aed40>, default_metadata=(), convert_system_message_to_human=True))"]}, "metadata": {}, "execution_count": 153}]}, {"cell_type": "code", "source": ["_input = {\"input\": \"<PERSON><PERSON> & Sam are working on a hackathon project\"}"], "metadata": {"id": "PzBxI_6J8FCc"}, "execution_count": 154, "outputs": []}, {"cell_type": "code", "source": ["memory.load_memory_variables(_input)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jZj9GaBH8Hej", "outputId": "e877af0f-cee9-4304-8a49-bb4a47510480"}, "execution_count": 155, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': [], 'entities': {'Deven': '', 'Sam': ''}}"]}, "metadata": {}, "execution_count": 155}]}, {"cell_type": "code", "source": ["memory.save_context(\n", "    _input,\n", "    {\"output\": \" That sounds like a great project! What kind of project are they working on?\"}\n", ")"], "metadata": {"id": "r0Sa4O9C8Otz"}, "execution_count": 156, "outputs": []}, {"cell_type": "code", "source": ["memory.load_memory_variables({\"input\": 'who is <PERSON>'})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yLBPaeWU8SUL", "outputId": "9ac3bf92-ff52-48b1-d3ad-20c0c249a9d6"}, "execution_count": 157, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'history': [HumanMessage(content='<PERSON><PERSON> & Sam are working on a hackathon project'),\n", "  AIMessage(content=' That sounds like a great project! What kind of project are they working on?')],\n", " 'entities': {'Sam': '<PERSON> is working on a hackathon project with <PERSON><PERSON>.'}}"]}, "metadata": {}, "execution_count": 157}]}, {"cell_type": "code", "source": ["from langchain.chains import ConversationChain"], "metadata": {"id": "25sJTv-J8cRb"}, "execution_count": 158, "outputs": []}, {"cell_type": "code", "source": ["from langchain.memory import ConversationEntityMemory"], "metadata": {"id": "tcLlxQe4_MBI"}, "execution_count": 159, "outputs": []}, {"cell_type": "code", "source": ["from langchain.memory.prompt import ENTITY_MEMORY_CONVERSATION_TEMPLATE"], "metadata": {"id": "UybNNJCA_N2X"}, "execution_count": 160, "outputs": []}, {"cell_type": "code", "source": ["from pydantic import BaseModel"], "metadata": {"id": "g9-ALiFc_SQ8"}, "execution_count": 86, "outputs": []}, {"cell_type": "code", "source": ["from typing import List, Dict, Any"], "metadata": {"id": "7kQLI2Tk_U2C"}, "execution_count": 87, "outputs": []}, {"cell_type": "code", "source": ["conversation = Conversation<PERSON><PERSON><PERSON>(\n", "    llm=model,\n", "    verbose=True,\n", "    prompt=ENTITY_MEMORY_CONVERSATION_TEMPLATE,\n", "    memory=ConversationEntityMemory(llm=model)\n", ")"], "metadata": {"id": "KGqFtiST_Wf6"}, "execution_count": 88, "outputs": []}, {"cell_type": "code", "source": ["conversation.predict(input=\"<PERSON><PERSON> & Sam are working on a hackathon project\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 437}, "id": "I1i6FFEE_u_D", "outputId": "79197e71-b89e-467f-b0d1-5be7e015bba6"}, "execution_count": 89, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mYou are an assistant to a human, powered by a large language model trained by OpenAI.\n", "\n", "You are designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, you are able to generate human-like text based on the input you receive, allowing you to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\n", "\n", "You are constantly learning and improving, and your capabilities are constantly evolving. You are able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. You have access to some personalized information provided by the human in the Context section below. Additionally, you are able to generate your own text based on the input you receive, allowing you to engage in discussions and provide explanations and descriptions on a wide range of topics.\n", "\n", "Overall, you are a powerful tool that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether the human needs help with a specific question or just wants to have a conversation about a particular topic, you are here to assist.\n", "\n", "Context:\n", "{'Deven': '', 'Sam': ''}\n", "\n", "Current conversation:\n", "\n", "Last line:\n", "Human: <PERSON><PERSON> & Sam are working on a hackathon project\n", "You:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"That's great! What kind of project are they working on?\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 89}]}, {"cell_type": "code", "source": ["conversation.memory.entity_store.store"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QDdJUJjd_4Ol", "outputId": "1a1c9bd1-e334-45cb-d370-d6bd1d08f948"}, "execution_count": 90, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'Deven': '<PERSON><PERSON> is working on a hackathon project with <PERSON>.',\n", " 'Sam': '<PERSON> is working on a hackathon project with <PERSON><PERSON>.'}"]}, "metadata": {}, "execution_count": 90}]}, {"cell_type": "code", "source": ["conversation.predict(input=\"They are trying to add more complex memory structures to Langchain\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 472}, "id": "7_3ELrYJAB57", "outputId": "4c0b9050-b80e-4682-c4d0-063cda7cee79"}, "execution_count": 91, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mYou are an assistant to a human, powered by a large language model trained by OpenAI.\n", "\n", "You are designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, you are able to generate human-like text based on the input you receive, allowing you to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\n", "\n", "You are constantly learning and improving, and your capabilities are constantly evolving. You are able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. You have access to some personalized information provided by the human in the Context section below. Additionally, you are able to generate your own text based on the input you receive, allowing you to engage in discussions and provide explanations and descriptions on a wide range of topics.\n", "\n", "Overall, you are a powerful tool that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether the human needs help with a specific question or just wants to have a conversation about a particular topic, you are here to assist.\n", "\n", "Context:\n", "{'Langchain': ''}\n", "\n", "Current conversation:\n", "Human: <PERSON><PERSON> & Sam are working on a hackathon project\n", "AI: That's great! What kind of project are they working on?\n", "Last line:\n", "Human: They are trying to add more complex memory structures to Langchain\n", "You:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"That's an interesting project! Langchain is a very powerful language model, and adding more complex memory structures could significantly enhance its capabilities. What specific types of memory structures are they considering adding?\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 91}]}, {"cell_type": "code", "source": ["conversation.predict(input=\"They are adding in a key-value store for entities mentioned so far in the conversation.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 524}, "id": "FAToSArZAGJy", "outputId": "f458234e-cb17-4706-82d7-db05aee12afe"}, "execution_count": 92, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mYou are an assistant to a human, powered by a large language model trained by OpenAI.\n", "\n", "You are designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, you are able to generate human-like text based on the input you receive, allowing you to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\n", "\n", "You are constantly learning and improving, and your capabilities are constantly evolving. You are able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. You have access to some personalized information provided by the human in the Context section below. Additionally, you are able to generate your own text based on the input you receive, allowing you to engage in discussions and provide explanations and descriptions on a wide range of topics.\n", "\n", "Overall, you are a powerful tool that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether the human needs help with a specific question or just wants to have a conversation about a particular topic, you are here to assist.\n", "\n", "Context:\n", "{}\n", "\n", "Current conversation:\n", "Human: <PERSON><PERSON> & Sam are working on a hackathon project\n", "AI: That's great! What kind of project are they working on?\n", "Human: They are trying to add more complex memory structures to Langchain\n", "AI: That's an interesting project! Langchain is a very powerful language model, and adding more complex memory structures could significantly enhance its capabilities. What specific types of memory structures are they considering adding?\n", "Last line:\n", "Human: They are adding in a key-value store for entities mentioned so far in the conversation.\n", "You:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"That's a great idea! A key-value store would allow Langchain to keep track of important entities and their attributes, which could be very useful for tasks such as question answering and dialogue generation. How are they planning to integrate the key-value store with the existing Langchain architecture?\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 92}]}, {"cell_type": "code", "source": ["conversation.predict(input=\"What do you know about <PERSON><PERSON> & Sam?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 576}, "id": "JMqcPvC3AJEC", "outputId": "b3991c52-de54-4436-abfe-1b9640203e9c"}, "execution_count": 93, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mYou are an assistant to a human, powered by a large language model trained by OpenAI.\n", "\n", "You are designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, you are able to generate human-like text based on the input you receive, allowing you to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\n", "\n", "You are constantly learning and improving, and your capabilities are constantly evolving. You are able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. You have access to some personalized information provided by the human in the Context section below. Additionally, you are able to generate your own text based on the input you receive, allowing you to engage in discussions and provide explanations and descriptions on a wide range of topics.\n", "\n", "Overall, you are a powerful tool that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether the human needs help with a specific question or just wants to have a conversation about a particular topic, you are here to assist.\n", "\n", "Context:\n", "{'<PERSON>en': '<PERSON><PERSON> is working on a hackathon project with <PERSON>.', '<PERSON>': '<PERSON> is working on a hackathon project with <PERSON><PERSON>.'}\n", "\n", "Current conversation:\n", "Human: <PERSON><PERSON> & Sam are working on a hackathon project\n", "AI: That's great! What kind of project are they working on?\n", "Human: They are trying to add more complex memory structures to Langchain\n", "AI: That's an interesting project! Langchain is a very powerful language model, and adding more complex memory structures could significantly enhance its capabilities. What specific types of memory structures are they considering adding?\n", "Human: They are adding in a key-value store for entities mentioned so far in the conversation.\n", "AI: That's a great idea! A key-value store would allow Langchain to keep track of important entities and their attributes, which could be very useful for tasks such as question answering and dialogue generation. How are they planning to integrate the key-value store with the existing Langchain architecture?\n", "Last line:\n", "Human: What do you know about <PERSON><PERSON> & Sam?\n", "You:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'I know that <PERSON><PERSON> and <PERSON> are working on a hackathon project together. They are trying to add more complex memory structures to Langchain, a powerful language model. Specifically, they are adding in a key-value store for entities mentioned so far in the conversation. I think this is a great idea, as it would allow Langchain to keep track of important entities and their attributes, which could be very useful for tasks such as question answering and dialogue generation.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 93}]}, {"cell_type": "code", "source": ["from pprint import pprint\n", "pprint(conversation.memory.entity_store.store)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dJkuNNOZAKri", "outputId": "9c448519-a611-49e0-c50e-79ce374ce544"}, "execution_count": 94, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["{'<PERSON>en': '<PERSON><PERSON> is working on a hackathon project with <PERSON>, where they are '\n", "          'trying to add more complex memory structures to Langchain, '\n", "          'specifically a key-value store for entities mentioned so far in the '\n", "          'conversation.',\n", " 'Langchain': '<PERSON><PERSON><PERSON> is a powerful language model that <PERSON><PERSON> and <PERSON> are '\n", "              'working on enhancing by adding more complex memory structures.',\n", " 'Sam': '<PERSON> is working on a hackathon project with <PERSON><PERSON>, specifically adding '\n", "        'a key-value store to Langchain.'}\n"]}]}, {"cell_type": "code", "source": ["conversation.predict(input=\"<PERSON> is the founder of a company called Daimon.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 541}, "id": "sI7tSPB7AN8b", "outputId": "6a7660a5-c92a-4488-a25e-97bfe401086f"}, "execution_count": 95, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mYou are an assistant to a human, powered by a large language model trained by OpenAI.\n", "\n", "You are designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, you are able to generate human-like text based on the input you receive, allowing you to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\n", "\n", "You are constantly learning and improving, and your capabilities are constantly evolving. You are able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. You have access to some personalized information provided by the human in the Context section below. Additionally, you are able to generate your own text based on the input you receive, allowing you to engage in discussions and provide explanations and descriptions on a wide range of topics.\n", "\n", "Overall, you are a powerful tool that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether the human needs help with a specific question or just wants to have a conversation about a particular topic, you are here to assist.\n", "\n", "Context:\n", "{'<PERSON><PERSON>': '', '<PERSON>': '<PERSON> is working on a hackathon project with <PERSON><PERSON>, specifically adding a key-value store to Langchain.'}\n", "\n", "Current conversation:\n", "Human: They are trying to add more complex memory structures to Langchain\n", "AI: That's an interesting project! Langchain is a very powerful language model, and adding more complex memory structures could significantly enhance its capabilities. What specific types of memory structures are they considering adding?\n", "Human: They are adding in a key-value store for entities mentioned so far in the conversation.\n", "AI: That's a great idea! A key-value store would allow Langchain to keep track of important entities and their attributes, which could be very useful for tasks such as question answering and dialogue generation. How are they planning to integrate the key-value store with the existing Langchain architecture?\n", "Human: What do you know about <PERSON><PERSON> & Sam?\n", "AI: I know that <PERSON><PERSON> and <PERSON> are working on a hackathon project together. They are trying to add more complex memory structures to Langchain, a powerful language model. Specifically, they are adding in a key-value store for entities mentioned so far in the conversation. I think this is a great idea, as it would allow Langchain to keep track of important entities and their attributes, which could be very useful for tasks such as question answering and dialogue generation.\n", "Last line:\n", "Human: <PERSON> is the founder of a company called Daimon.\n", "You:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"That's interesting! I'm not familiar with <PERSON><PERSON>, but I'm always interested in hearing about new companies. What does <PERSON><PERSON> do?\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 95}]}, {"cell_type": "code", "source": ["from pprint import pprint\n", "pprint(conversation.memory.entity_store.store)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6Mqxyr1mAQyk", "outputId": "9878d6ab-8e25-4092-fe9b-a6c95c6def64"}, "execution_count": 96, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["{'Daimon': 'Daimon is a company founded by <PERSON>.',\n", " 'Deven': '<PERSON><PERSON> is working on a hackathon project with <PERSON>, where they are '\n", "          'trying to add more complex memory structures to Langchain, '\n", "          'specifically a key-value store for entities mentioned so far in the '\n", "          'conversation.',\n", " 'Langchain': '<PERSON><PERSON><PERSON> is a powerful language model that <PERSON><PERSON> and <PERSON> are '\n", "              'working on enhancing by adding more complex memory structures.',\n", " 'Sam': '<PERSON> is working on a hackathon project with <PERSON><PERSON>, specifically adding '\n", "        'a key-value store to Langchain. <PERSON> is also the founder of a company '\n", "        'called <PERSON><PERSON>.'}\n"]}]}, {"cell_type": "code", "source": ["conversation.predict(input=\"What do you know about <PERSON>?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 541}, "id": "yKVKMXI_ATYk", "outputId": "22bbbbd9-92bd-4ac6-e8a3-61d4b3cb2e3a"}, "execution_count": 97, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mYou are an assistant to a human, powered by a large language model trained by OpenAI.\n", "\n", "You are designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, you are able to generate human-like text based on the input you receive, allowing you to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\n", "\n", "You are constantly learning and improving, and your capabilities are constantly evolving. You are able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. You have access to some personalized information provided by the human in the Context section below. Additionally, you are able to generate your own text based on the input you receive, allowing you to engage in discussions and provide explanations and descriptions on a wide range of topics.\n", "\n", "Overall, you are a powerful tool that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether the human needs help with a specific question or just wants to have a conversation about a particular topic, you are here to assist.\n", "\n", "Context:\n", "{'Sam': '<PERSON> is working on a hackathon project with Deven, specifically adding a key-value store to Langchain. <PERSON> is also the founder of a company called Daimon.'}\n", "\n", "Current conversation:\n", "Human: They are adding in a key-value store for entities mentioned so far in the conversation.\n", "AI: That's a great idea! A key-value store would allow Langchain to keep track of important entities and their attributes, which could be very useful for tasks such as question answering and dialogue generation. How are they planning to integrate the key-value store with the existing Langchain architecture?\n", "Human: What do you know about <PERSON><PERSON> & Sam?\n", "AI: I know that <PERSON><PERSON> and <PERSON> are working on a hackathon project together. They are trying to add more complex memory structures to Langchain, a powerful language model. Specifically, they are adding in a key-value store for entities mentioned so far in the conversation. I think this is a great idea, as it would allow Langchain to keep track of important entities and their attributes, which could be very useful for tasks such as question answering and dialogue generation.\n", "Human: <PERSON> is the founder of a company called Daimon.\n", "AI: That's interesting! I'm not familiar with <PERSON><PERSON>, but I'm always interested in hearing about new companies. What does <PERSON><PERSON> do?\n", "Last line:\n", "Human: What do you know about <PERSON>?\n", "You:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'I know that <PERSON> is working on a hackathon project with <PERSON><PERSON>, specifically adding a key-value store to Langchain. I also know that <PERSON> is the founder of a company called Daimon.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 97}]}, {"cell_type": "code", "source": [], "metadata": {"id": "1XfVuH3CAWGj"}, "execution_count": null, "outputs": []}]}