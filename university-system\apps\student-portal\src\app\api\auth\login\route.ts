import { NextRequest, NextResponse } from 'next/server';

// Temporary types until we set up the shared packages
interface LoginCredentials {
  email: string;
  password: string;
}

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: LoginCredentials = await request.json();
    const { email, password } = body;

    if (!email || !password) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Email and password are required'
      }, { status: 400 });
    }

    // Mock authentication for demo purposes
    // In production, this would use Supabase authentication
    if (email === '<EMAIL>' && password === 'password') {
      return NextResponse.json<ApiResponse>({
        success: true,
        data: {
          user: {
            id: '1',
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe',
            role: 'student'
          }
        },
        message: 'Login successful'
      });
    } else {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Invalid credentials. Use <EMAIL> / password for demo.'
      }, { status: 401 });
    }

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
