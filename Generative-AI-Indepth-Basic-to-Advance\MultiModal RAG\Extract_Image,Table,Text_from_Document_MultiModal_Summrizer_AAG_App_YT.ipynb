{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"29fef475a16a499b95bd7495e2388663": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_8e3eae6d05294e9e8a28d11b2e4b0ae2", "IPY_MODEL_035c1f9016ec4219a3d9c5b39e4066e7", "IPY_MODEL_07a7635319024098af3c7e313c0cd1d2"], "layout": "IPY_MODEL_79991327fb3e4b25b963d317e85d43f8"}}, "8e3eae6d05294e9e8a28d11b2e4b0ae2": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_910aa943220c43a697ca2156df61c4d0", "placeholder": "​", "style": "IPY_MODEL_9f34dbbd331e4dd7ae9a6d69334efa87", "value": "yolox_l0.05.onnx: 100%"}}, "035c1f9016ec4219a3d9c5b39e4066e7": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f81683846b9e4d4d9c0a2ca0fcf78386", "max": 216625723, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_63956298d47b4f81aaecbd73a38874ac", "value": 216625723}}, "07a7635319024098af3c7e313c0cd1d2": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c266c91b3a6e4950aade4d8641db71d1", "placeholder": "​", "style": "IPY_MODEL_5fa906e23e804770ba4ca39b43db0e89", "value": " 217M/217M [00:02&lt;00:00, 107MB/s]"}}, "79991327fb3e4b25b963d317e85d43f8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "910aa943220c43a697ca2156df61c4d0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9f34dbbd331e4dd7ae9a6d69334efa87": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f81683846b9e4d4d9c0a2ca0fcf78386": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "63956298d47b4f81aaecbd73a38874ac": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c266c91b3a6e4950aade4d8641db71d1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5fa906e23e804770ba4ca39b43db0e89": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "cells": [{"cell_type": "markdown", "source": ["# Realtime multimodal Usecase | Extract Image,Table,Text from Document | MultiModal Summrizer| RAG App"], "metadata": {"id": "AUeScs9rB6Nk"}}, {"cell_type": "code", "source": ["! pip install \"unstructured[all-docs]\" pillow pydantic lxml matplotlib"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "M7BsV2KiVRm2", "outputId": "c5e60d31-aa52-4de2-bb12-ab97c1ea0ecc"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting unstructured[all-docs]\n", "  Downloading unstructured-0.13.4-py3-none-any.whl (1.9 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.9/1.9 MB\u001b[0m \u001b[31m10.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pillow in /usr/local/lib/python3.10/dist-packages (9.4.0)\n", "Requirement already satisfied: pydantic in /usr/local/lib/python3.10/dist-packages (2.7.0)\n", "Requirement already satisfied: lxml in /usr/local/lib/python3.10/dist-packages (4.9.4)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.10/dist-packages (3.7.1)\n", "Requirement already satisfied: chardet in /usr/local/lib/python3.10/dist-packages (from unstructured[all-docs]) (5.2.0)\n", "Collecting filetype (from unstructured[all-docs])\n", "  Downloading filetype-1.2.0-py2.py3-none-any.whl (19 kB)\n", "Collecting python-magic (from unstructured[all-docs])\n", "  Downloading python_magic-0.4.27-py2.py3-none-any.whl (13 kB)\n", "Requirement already satisfied: nltk in /usr/local/lib/python3.10/dist-packages (from unstructured[all-docs]) (3.8.1)\n", "Requirement already satisfied: tabulate in /usr/local/lib/python3.10/dist-packages (from unstructured[all-docs]) (0.9.0)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from unstructured[all-docs]) (2.31.0)\n", "Requirement already satisfied: beautifulsoup4 in /usr/local/lib/python3.10/dist-packages (from unstructured[all-docs]) (4.12.3)\n", "Collecting emoji (from unstructured[all-docs])\n", "  Downloading emoji-2.11.1-py2.py3-none-any.whl (433 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m433.8/433.8 kB\u001b[0m \u001b[31m12.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting dataclasses-json (from unstructured[all-docs])\n", "  Downloading dataclasses_json-0.6.4-py3-none-any.whl (28 kB)\n", "Collecting python-iso639 (from unstructured[all-docs])\n", "  Downloading python_iso639-2024.4.27-py3-none-any.whl (274 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m274.7/274.7 kB\u001b[0m \u001b[31m13.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langdetect (from unstructured[all-docs])\n", "  Downloading langdetect-1.0.9.tar.gz (981 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m981.5/981.5 kB\u001b[0m \u001b[31m17.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from unstructured[all-docs]) (1.25.2)\n", "Collecting rapidfuzz (from unstructured[all-docs])\n", "  Downloading rapidfuzz-3.8.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.4 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.4/3.4 MB\u001b[0m \u001b[31m21.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting backoff (from unstructured[all-docs])\n", "  Downloading backoff-2.2.1-py3-none-any.whl (15 kB)\n", "Requirement already satisfied: typing-extensions in /usr/local/lib/python3.10/dist-packages (from unstructured[all-docs]) (4.11.0)\n", "Collecting unstructured-client (from unstructured[all-docs])\n", "  Downloading unstructured_client-0.22.0-py3-none-any.whl (28 kB)\n", "Requirement already satisfied: wrapt in /usr/local/lib/python3.10/dist-packages (from unstructured[all-docs]) (1.14.1)\n", "Collecting onnx (from unstructured[all-docs])\n", "  Downloading onnx-1.16.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (15.9 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m15.9/15.9 MB\u001b[0m \u001b[31m32.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting python-docx (from unstructured[all-docs])\n", "  Downloading python_docx-1.1.0-py3-none-any.whl (239 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m239.6/239.6 kB\u001b[0m \u001b[31m14.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting unstructured-inference==0.7.28 (from unstructured[all-docs])\n", "  Downloading unstructured_inference-0.7.28-py3-none-any.whl (59 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m59.4/59.4 kB\u001b[0m \u001b[31m6.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting msg-parser (from unstructured[all-docs])\n", "  Downloading msg_parser-1.2.0-py2.py3-none-any.whl (101 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m101.8/101.8 kB\u001b[0m \u001b[31m9.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: openpyxl in /usr/local/lib/python3.10/dist-packages (from unstructured[all-docs]) (3.1.2)\n", "Requirement already satisfied: markdown in /usr/local/lib/python3.10/dist-packages (from unstructured[all-docs]) (3.6)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from unstructured[all-docs]) (2.0.3)\n", "Collecting pypandoc (from unstructured[all-docs])\n", "  Downloading pypandoc-1.13-py3-none-any.whl (21 kB)\n", "Collecting pdf2image (from unstructured[all-docs])\n", "  Downloading pdf2image-1.17.0-py3-none-any.whl (11 kB)\n", "Collecting pdfminer.six (from unstructured[all-docs])\n", "  Downloading pdfminer.six-20231228-py3-none-any.whl (5.6 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.6/5.6 MB\u001b[0m \u001b[31m52.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting google-cloud-vision (from unstructured[all-docs])\n", "  Downloading google_cloud_vision-3.7.2-py2.py3-none-any.whl (459 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m459.6/459.6 kB\u001b[0m \u001b[31m31.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: xlrd in /usr/local/lib/python3.10/dist-packages (from unstructured[all-docs]) (2.0.1)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from unstructured[all-docs]) (3.3)\n", "Collecting unstructured.pytesseract>=0.3.12 (from unstructured[all-docs])\n", "  Downloading unstructured.pytesseract-0.3.12-py3-none-any.whl (14 kB)\n", "Collecting pypdf (from unstructured[all-docs])\n", "  Downloading pypdf-4.2.0-py3-none-any.whl (290 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m290.4/290.4 kB\u001b[0m \u001b[31m22.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting pikepdf (from unstructured[all-docs])\n", "  Downloading pikepdf-8.15.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.4 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.4/2.4 MB\u001b[0m \u001b[31m64.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting pillow-heif (from unstructured[all-docs])\n", "  Downloading pillow_heif-0.16.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (7.5 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7.5/7.5 MB\u001b[0m \u001b[31m62.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting python-pptx<=0.6.23 (from unstructured[all-docs])\n", "  Downloading python_pptx-0.6.23-py3-none-any.whl (471 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m471.6/471.6 kB\u001b[0m \u001b[31m38.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting layoutparser[layoutmodels,tesseract] (from unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Downloading layoutparser-0.3.4-py3-none-any.whl (19.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m19.2/19.2 MB\u001b[0m \u001b[31m42.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting python-multipart (from unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Downloading python_multipart-0.0.9-py3-none-any.whl (22 kB)\n", "Requirement already satisfied: huggingface-hub in /usr/local/lib/python3.10/dist-packages (from unstructured-inference==0.7.28->unstructured[all-docs]) (0.20.3)\n", "Requirement already satisfied: opencv-python!=******** in /usr/local/lib/python3.10/dist-packages (from unstructured-inference==0.7.28->unstructured[all-docs]) (********)\n", "Collecting onnxruntime>=1.17.0 (from unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Downloading onnxruntime-1.17.3-cp310-cp310-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (6.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.8/6.8 MB\u001b[0m \u001b[31m91.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: transformers>=4.25.1 in /usr/local/lib/python3.10/dist-packages (from unstructured-inference==0.7.28->unstructured[all-docs]) (4.40.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.1 in /usr/local/lib/python3.10/dist-packages (from pydantic) (2.18.1)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (1.2.1)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (4.51.0)\n", "Requirement already satisfied: kiwisolver>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (1.4.5)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (24.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (3.1.2)\n", "Requirement already satisfied: python-dateutil>=2.7 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (2.8.2)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.7->matplotlib) (1.16.0)\n", "Collecting XlsxWriter>=0.5.7 (from python-pptx<=0.6.23->unstructured[all-docs])\n", "  Downloading XlsxWriter-3.2.0-py3-none-any.whl (159 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m159.9/159.9 kB\u001b[0m \u001b[31m17.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.10/dist-packages (from beautifulsoup4->unstructured[all-docs]) (2.5)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json->unstructured[all-docs])\n", "  Downloading marshmallow-3.21.1-py3-none-any.whl (49 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.4/49.4 kB\u001b[0m \u001b[31m6.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting typing-inspect<1,>=0.4.0 (from dataclasses-json->unstructured[all-docs])\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Requirement already satisfied: google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1 in /usr/local/lib/python3.10/dist-packages (from google-cloud-vision->unstructured[all-docs]) (2.11.1)\n", "Requirement already satisfied: google-auth!=2.24.0,!=2.25.0,<3.0.0dev,>=2.14.1 in /usr/local/lib/python3.10/dist-packages (from google-cloud-vision->unstructured[all-docs]) (2.27.0)\n", "Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.3 in /usr/local/lib/python3.10/dist-packages (from google-cloud-vision->unstructured[all-docs]) (1.23.0)\n", "Requirement already satisfied: protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.19.5 in /usr/local/lib/python3.10/dist-packages (from google-cloud-vision->unstructured[all-docs]) (3.20.3)\n", "Collecting olefile>=0.46 (from msg-parser->unstructured[all-docs])\n", "  Downloading olefile-0.47-py2.py3-none-any.whl (114 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m114.6/114.6 kB\u001b[0m \u001b[31m13.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured[all-docs]) (8.1.7)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured[all-docs]) (1.4.0)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured[all-docs]) (2023.12.25)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured[all-docs]) (4.66.2)\n", "Requirement already satisfied: et-xmlfile in /usr/local/lib/python3.10/dist-packages (from openpyxl->unstructured[all-docs]) (1.1.0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->unstructured[all-docs]) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas->unstructured[all-docs]) (2024.1)\n", "Requirement already satisfied: charset-normalizer>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from pdfminer.six->unstructured[all-docs]) (3.3.2)\n", "Requirement already satisfied: cryptography>=36.0.0 in /usr/local/lib/python3.10/dist-packages (from pdfminer.six->unstructured[all-docs]) (42.0.5)\n", "Collecting pillow\n", "  Downloading pillow-10.3.0-cp310-cp310-manylinux_2_28_x86_64.whl (4.5 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.5/4.5 MB\u001b[0m \u001b[31m60.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting Deprecated (from pikepdf->unstructured[all-docs])\n", "  Downloading Deprecated-1.2.14-py2.py3-none-any.whl (9.6 kB)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured[all-docs]) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured[all-docs]) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured[all-docs]) (2024.2.2)\n", "Collecting deepdiff>=6.0 (from unstructured-client->unstructured[all-docs])\n", "  Downloading deepdiff-7.0.1-py3-none-any.whl (80 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m80.8/80.8 kB\u001b[0m \u001b[31m9.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting jsonpath-python>=1.0.6 (from unstructured-client->unstructured[all-docs])\n", "  Downloading jsonpath_python-1.0.6-py3-none-any.whl (7.6 kB)\n", "Collecting mypy-extensions>=1.0.0 (from unstructured-client->unstructured[all-docs])\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Requirement already satisfied: cffi>=1.12 in /usr/local/lib/python3.10/dist-packages (from cryptography>=36.0.0->pdfminer.six->unstructured[all-docs]) (1.16.0)\n", "Collecting ordered-set<4.2.0,>=4.1.0 (from deepdiff>=6.0->unstructured-client->unstructured[all-docs])\n", "  Downloading ordered_set-4.1.0-py3-none-any.whl (7.6 kB)\n", "Requirement already satisfied: googleapis-common-protos<2.0.dev0,>=1.56.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-cloud-vision->unstructured[all-docs]) (1.63.0)\n", "Requirement already satisfied: grpcio<2.0dev,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-cloud-vision->unstructured[all-docs]) (1.62.2)\n", "Requirement already satisfied: grpcio-status<2.0.dev0,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-cloud-vision->unstructured[all-docs]) (1.48.2)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0dev,>=2.14.1->google-cloud-vision->unstructured[all-docs]) (5.3.3)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0dev,>=2.14.1->google-cloud-vision->unstructured[all-docs]) (0.4.0)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0dev,>=2.14.1->google-cloud-vision->unstructured[all-docs]) (4.9)\n", "Collecting coloredlogs (from onnxruntime>=1.17.0->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Downloading coloredlogs-15.0.1-py2.py3-none-any.whl (46 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.0/46.0 kB\u001b[0m \u001b[31m5.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: flatbuffers in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.17.0->unstructured-inference==0.7.28->unstructured[all-docs]) (24.3.25)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.17.0->unstructured-inference==0.7.28->unstructured[all-docs]) (1.12)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from transformers>=4.25.1->unstructured-inference==0.7.28->unstructured[all-docs]) (3.13.4)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from transformers>=4.25.1->unstructured-inference==0.7.28->unstructured[all-docs]) (6.0.1)\n", "Requirement already satisfied: tokenizers<0.20,>=0.19 in /usr/local/lib/python3.10/dist-packages (from transformers>=4.25.1->unstructured-inference==0.7.28->unstructured[all-docs]) (0.19.1)\n", "Requirement already satisfied: safetensors>=0.4.1 in /usr/local/lib/python3.10/dist-packages (from transformers>=4.25.1->unstructured-inference==0.7.28->unstructured[all-docs]) (0.4.3)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub->unstructured-inference==0.7.28->unstructured[all-docs]) (2023.6.0)\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.10/dist-packages (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs]) (1.11.4)\n", "Collecting iopath (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Downloading iopath-0.1.10.tar.gz (42 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m42.2/42.2 kB\u001b[0m \u001b[31m4.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Collecting pdfplumber (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Downloading pdfplumber-0.11.0-py3-none-any.whl (56 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.4/56.4 kB\u001b[0m \u001b[31m6.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: torch in /usr/local/lib/python3.10/dist-packages (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs]) (2.2.1+cu121)\n", "Requirement already satisfied: torchvision in /usr/local/lib/python3.10/dist-packages (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs]) (0.17.1+cu121)\n", "Collecting effdet (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Downloading effdet-0.4.1-py3-none-any.whl (112 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m112.5/112.5 kB\u001b[0m \u001b[31m12.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting pytesseract (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Downloading pytesseract-0.3.10-py3-none-any.whl (14 kB)\n", "Requirement already satisfied: pycparser in /usr/local/lib/python3.10/dist-packages (from cffi>=1.12->cryptography>=36.0.0->pdfminer.six->unstructured[all-docs]) (2.22)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth!=2.24.0,!=2.25.0,<3.0.0dev,>=2.14.1->google-cloud-vision->unstructured[all-docs]) (0.6.0)\n", "Collecting humanfriendly>=9.1 (from coloredlogs->onnxruntime>=1.17.0->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Downloading humanfriendly-10.0-py2.py3-none-any.whl (86 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.8/86.8 kB\u001b[0m \u001b[31m9.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting timm>=0.9.2 (from effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Downloading timm-0.9.16-py3-none-any.whl (2.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.2/2.2 MB\u001b[0m \u001b[31m47.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pycocotools>=2.0.2 in /usr/local/lib/python3.10/dist-packages (from effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs]) (2.0.7)\n", "Collecting omegaconf>=2.0 (from effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Downloading omegaconf-2.3.0-py3-none-any.whl (79 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m79.5/79.5 kB\u001b[0m \u001b[31m7.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs]) (3.1.3)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.1.105 (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Using cached nvidia_cuda_nvrtc_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (23.7 MB)\n", "Collecting nvidia-cuda-runtime-cu12==12.1.105 (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Using cached nvidia_cuda_runtime_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (823 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.1.105 (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Using cached nvidia_cuda_cupti_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (14.1 MB)\n", "Collecting nvidia-cudnn-cu12==******** (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Using cached nvidia_cudnn_cu12-********-py3-none-manylinux1_x86_64.whl (731.7 MB)\n", "Collecting nvidia-cublas-cu12==******** (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Using cached nvidia_cublas_cu12-********-py3-none-manylinux1_x86_64.whl (410.6 MB)\n", "Collecting nvidia-cufft-cu12==********* (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Using cached nvidia_cufft_cu12-*********-py3-none-manylinux1_x86_64.whl (121.6 MB)\n", "Collecting nvidia-curand-cu12==********** (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Using cached nvidia_curand_cu12-**********-py3-none-manylinux1_x86_64.whl (56.5 MB)\n", "Collecting nvidia-cusolver-cu12==********** (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Using cached nvidia_cusolver_cu12-**********-py3-none-manylinux1_x86_64.whl (124.2 MB)\n", "Collecting nvidia-cusparse-cu12==********** (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Using cached nvidia_cusparse_cu12-**********-py3-none-manylinux1_x86_64.whl (196.0 MB)\n", "Collecting nvidia-nccl-cu12==2.19.3 (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Using cached nvidia_nccl_cu12-2.19.3-py3-none-manylinux1_x86_64.whl (166.0 MB)\n", "Collecting nvidia-nvtx-cu12==12.1.105 (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Using cached nvidia_nvtx_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (99 kB)\n", "Requirement already satisfied: triton==2.2.0 in /usr/local/lib/python3.10/dist-packages (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs]) (2.2.0)\n", "Collecting nvidia-nvjitlink-cu12 (from nvidia-cusolver-cu12==**********->torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Using cached nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n", "Collecting portalocker (from iopath->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Downloading portalocker-2.8.2-py3-none-any.whl (17 kB)\n", "Collecting pypdfium2>=4.18.0 (from pdfplumber->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Downloading pypdfium2-4.29.0-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.8/2.8 MB\u001b[0m \u001b[31m74.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->onnxruntime>=1.17.0->unstructured-inference==0.7.28->unstructured[all-docs]) (1.3.0)\n", "Collecting antlr4-python3-runtime==4.9.* (from omegaconf>=2.0->effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs])\n", "  Downloading antlr4-python3-runtime-4.9.3.tar.gz (117 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m117.0/117.0 kB\u001b[0m \u001b[31m13.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.28->unstructured[all-docs]) (2.1.5)\n", "Building wheels for collected packages: langdetect, iopath, antlr4-python3-runtime\n", "  Building wheel for langdetect (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for langdetect: filename=langdetect-1.0.9-py3-none-any.whl size=993227 sha256=39c9a5aa38e5132c1bd0ce322ddf54e2f5465129208ceb8b9393c38caeb9c4a3\n", "  Stored in directory: /root/.cache/pip/wheels/95/03/7d/59ea870c70ce4e5a370638b5462a7711ab78fba2f655d05106\n", "  Building wheel for iopath (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for iopath: filename=iopath-0.1.10-py3-none-any.whl size=31532 sha256=5ba0ad22e4e71fe3e9c673a33131a812079c9fd0039dc9f0d661119b3976b36e\n", "  Stored in directory: /root/.cache/pip/wheels/9a/a3/b6/ac0fcd1b4ed5cfeb3db92e6a0e476cfd48ed0df92b91080c1d\n", "  Building wheel for antlr4-python3-runtime (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for antlr4-python3-runtime: filename=antlr4_python3_runtime-4.9.3-py3-none-any.whl size=144554 sha256=6084bfdf0a694434c4e8f3a1be601b61f1309c7ce5ae1f86605cd9a68bf67efd\n", "  Stored in directory: /root/.cache/pip/wheels/12/93/dd/1f6a127edc45659556564c5730f6d4e300888f4bca2d4c5a88\n", "Successfully built langdetect iopath antlr4-python3-runtime\n", "Installing collected packages: filetype, antlr4-python3-runtime, XlsxWriter, rapidfuzz, python-multipart, python-magic, python-iso639, python-docx, pypdfium2, pypdf, pypandoc, portalocker, pillow, ordered-set, onnx, omegaconf, olefile, nvidia-nvtx-cu12, nvidia-nvjitlink-cu12, nvidia-nccl-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, mypy-extensions, marshmallow, langdetect, jsonpath-python, humanfriendly, emoji, Deprecated, backoff, unstructured.pytesseract, typing-inspect, python-pptx, pytesseract, pillow-heif, pikepdf, pdf2image, nvidia-cusparse-cu12, nvidia-cudnn-cu12, msg-parser, iopath, deepdiff, coloredlogs, pdfminer.six, onnxruntime, nvidia-cusolver-cu12, dataclasses-json, unstructured-client, pdfplumber, unstructured, layoutparser, google-cloud-vision, timm, effdet, unstructured-inference\n", "  Attempting uninstall: pillow\n", "    Found existing installation: Pillow 9.4.0\n", "    Uninstalling Pillow-9.4.0:\n", "      Successfully uninstalled Pillow-9.4.0\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "imageio 2.31.6 requires pillow<10.1.0,>=8.3.2, but you have pillow 10.3.0 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed Deprecated-1.2.14 XlsxWriter-3.2.0 antlr4-python3-runtime-4.9.3 backoff-2.2.1 coloredlogs-15.0.1 dataclasses-json-0.6.4 deepdiff-7.0.1 effdet-0.4.1 emoji-2.11.1 filetype-1.2.0 google-cloud-vision-3.7.2 humanfriendly-10.0 iopath-0.1.10 jsonpath-python-1.0.6 langdetect-1.0.9 layoutparser-0.3.4 marshmallow-3.21.1 msg-parser-1.2.0 mypy-extensions-1.0.0 nvidia-cublas-cu12-******** nvidia-cuda-cupti-cu12-12.1.105 nvidia-cuda-nvrtc-cu12-12.1.105 nvidia-cuda-runtime-cu12-12.1.105 nvidia-cudnn-cu12-******** nvidia-cufft-cu12-********* nvidia-curand-cu12-********** nvidia-cusolver-cu12-********** nvidia-cusparse-cu12-********** nvidia-nccl-cu12-2.19.3 nvidia-nvjitlink-cu12-12.4.127 nvidia-nvtx-cu12-12.1.105 olefile-0.47 omegaconf-2.3.0 onnx-1.16.0 onnxruntime-1.17.3 ordered-set-4.1.0 pdf2image-1.17.0 pdfminer.six-20231228 pdfplumber-0.11.0 pikepdf-8.15.1 pillow-10.3.0 pillow-heif-0.16.0 portalocker-2.8.2 pypandoc-1.13 pypdf-4.2.0 pypdfium2-4.29.0 pytesseract-0.3.10 python-docx-1.1.0 python-iso639-2024.4.27 python-magic-0.4.27 python-multipart-0.0.9 python-pptx-0.6.23 rapidfuzz-3.8.1 timm-0.9.16 typing-inspect-0.9.0 unstructured-0.13.4 unstructured-client-0.22.0 unstructured-inference-0.7.28 unstructured.pytesseract-0.3.12\n"]}, {"output_type": "display_data", "data": {"application/vnd.colab-display-data+json": {"pip_warning": {"packages": ["PIL", "google", "pydevd_plugins"]}, "id": "a1c0a5a372b148e5a29da4aeb0f5cdb2"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["!sudo apt-get update"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9ERiIOhfeWeJ", "outputId": "252a51f6-1cca-45bb-e57b-eb9694182b04"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\r0% [Working]\r            \rGet:1 http://security.ubuntu.com/ubuntu jammy-security InRelease [110 kB]\n", "\r0% [Waiting for headers] [1 InRelease 14.2 kB/110 kB 13%] [Waiting for headers]\r                                                                               \rHit:2 http://archive.ubuntu.com/ubuntu jammy InRelease\n", "\r                                                                               \rGet:3 https://cloud.r-project.org/bin/linux/ubuntu jammy-cran40/ InRelease [3,626 B]\n", "\r0% [1 InRelease 80.8 kB/110 kB 73%] [Connected to ppa.launchpadcontent.net (185\r                                                                               \rGet:4 http://archive.ubuntu.com/ubuntu jammy-updates InRelease [119 kB]\n", "Hit:5 https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64  InRelease\n", "Get:6 http://archive.ubuntu.com/ubuntu jammy-backports InRelease [109 kB]\n", "Hit:7 https://ppa.launchpadcontent.net/c2d4u.team/c2d4u4.0+/ubuntu jammy InRelease\n", "Get:8 https://ppa.launchpadcontent.net/deadsnakes/ppa/ubuntu jammy InRelease [18.1 kB]\n", "Get:9 https://cloud.r-project.org/bin/linux/ubuntu jammy-cran40/ Packages [52.9 kB]\n", "Get:10 https://ppa.launchpadcontent.net/graphics-drivers/ppa/ubuntu jammy InRelease [24.3 kB]\n", "Hit:11 https://ppa.launchpadcontent.net/ubuntugis/ppa/ubuntu jammy InRelease\n", "Get:12 http://security.ubuntu.com/ubuntu jammy-security/main amd64 Packages [1,755 kB]\n", "Get:13 http://archive.ubuntu.com/ubuntu jammy-updates/universe amd64 Packages [1,370 kB]\n", "Get:14 http://security.ubuntu.com/ubuntu jammy-security/universe amd64 Packages [1,077 kB]\n", "Get:15 http://archive.ubuntu.com/ubuntu jammy-updates/main amd64 Packages [2,035 kB]\n", "Get:16 https://ppa.launchpadcontent.net/deadsnakes/ppa/ubuntu jammy/main amd64 Packages [27.8 kB]\n", "Get:17 https://ppa.launchpadcontent.net/graphics-drivers/ppa/ubuntu jammy/main amd64 Packages [44.2 kB]\n", "Fetched 6,746 kB in 2s (3,230 kB/s)\n", "Reading package lists... Done\n"]}]}, {"cell_type": "code", "source": ["!sudo apt-get install poppler-utils"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Mu97I46AefNj", "outputId": "50c79707-1615-4dea-f239-e63e12bedfb9"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Reading package lists... Done\n", "Building dependency tree... Done\n", "Reading state information... Done\n", "The following NEW packages will be installed:\n", "  poppler-utils\n", "0 upgraded, 1 newly installed, 0 to remove and 50 not upgraded.\n", "Need to get 186 kB of archives.\n", "After this operation, 696 kB of additional disk space will be used.\n", "Get:1 http://archive.ubuntu.com/ubuntu jammy-updates/main amd64 poppler-utils amd64 22.02.0-2ubuntu0.3 [186 kB]\n", "Fetched 186 kB in 0s (1,210 kB/s)\n", "debconf: unable to initialize frontend: Dialog\n", "debconf: (No usable dialog-like program is installed, so the dialog based frontend cannot be used. at /usr/share/perl5/Debconf/FrontEnd/Dialog.pm line 78, <> line 1.)\n", "debconf: falling back to frontend: Readline\n", "debconf: unable to initialize frontend: Readline\n", "debconf: (This frontend requires a controlling tty.)\n", "debconf: falling back to frontend: Teletype\n", "dpkg-preconfigure: unable to re-open stdin: \n", "Selecting previously unselected package poppler-utils.\n", "(Reading database ... 121752 files and directories currently installed.)\n", "Preparing to unpack .../poppler-utils_22.02.0-2ubuntu0.3_amd64.deb ...\n", "Unpacking poppler-utils (22.02.0-2ubuntu0.3) ...\n", "Setting up poppler-utils (22.02.0-2ubuntu0.3) ...\n", "Processing triggers for man-db (2.10.2-1) ...\n"]}]}, {"cell_type": "code", "source": ["!sudo apt-get install libleptonica-dev tesseract-ocr libtesseract-dev python3-pil tesseract-ocr-eng tesseract-ocr-script-latn"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "BmntZhzTejwH", "outputId": "9c0c5201-fb25-4fe7-9234-c78f4<PERSON><PERSON><PERSON>"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Reading package lists... Done\n", "Building dependency tree... Done\n", "Reading state information... Done\n", "The following additional packages will be installed:\n", "  libarchive-dev libimagequant0 libraqm0 mailcap mime-support python3-olefile\n", "  tesseract-ocr-osd\n", "Suggested packages:\n", "  python-pil-doc\n", "The following NEW packages will be installed:\n", "  libarchive-dev libimagequant0 libleptonica-dev libraqm0 libtesseract-dev\n", "  mailcap mime-support python3-olefile python3-pil tesseract-ocr\n", "  tesseract-ocr-eng tesseract-ocr-osd tesseract-ocr-script-latn\n", "0 upgraded, 13 newly installed, 0 to remove and 50 not upgraded.\n", "Need to get 40.0 MB of archives.\n", "After this operation, 123 MB of additional disk space will be used.\n", "Get:1 http://archive.ubuntu.com/ubuntu jammy/main amd64 libarchive-dev amd64 3.6.0-1ubuntu1 [581 kB]\n", "Get:2 http://archive.ubuntu.com/ubuntu jammy/main amd64 libimagequant0 amd64 2.17.0-1 [34.6 kB]\n", "Get:3 http://archive.ubuntu.com/ubuntu jammy/universe amd64 libleptonica-dev amd64 1.82.0-3build1 [1,562 kB]\n", "Get:4 http://archive.ubuntu.com/ubuntu jammy/main amd64 libraqm0 amd64 0.7.0-4ubuntu1 [11.7 kB]\n", "Get:5 http://archive.ubuntu.com/ubuntu jammy/universe amd64 libtesseract-dev amd64 4.1.1-2.1build1 [1,600 kB]\n", "Get:6 http://archive.ubuntu.com/ubuntu jammy/main amd64 mailcap all 3.70+nmu1ubuntu1 [23.8 kB]\n", "Get:7 http://archive.ubuntu.com/ubuntu jammy/main amd64 mime-support all 3.66 [3,696 B]\n", "Get:8 http://archive.ubuntu.com/ubuntu jammy/main amd64 python3-olefile all 0.46-3 [33.8 kB]\n", "Get:9 http://archive.ubuntu.com/ubuntu jammy-updates/main amd64 python3-pil amd64 9.0.1-1ubuntu0.3 [419 kB]\n", "Get:10 http://archive.ubuntu.com/ubuntu jammy/universe amd64 tesseract-ocr-eng all 1:4.00~git30-7274cfa-1.1 [1,591 kB]\n", "Get:11 http://archive.ubuntu.com/ubuntu jammy/universe amd64 tesseract-ocr-osd all 1:4.00~git30-7274cfa-1.1 [2,990 kB]\n", "Get:12 http://archive.ubuntu.com/ubuntu jammy/universe amd64 tesseract-ocr amd64 4.1.1-2.1build1 [236 kB]\n", "Get:13 http://archive.ubuntu.com/ubuntu jammy/universe amd64 tesseract-ocr-script-latn all 1:4.00~git30-7274cfa-1.1 [30.9 MB]\n", "Fetched 40.0 MB in 1s (58.6 MB/s)\n", "debconf: unable to initialize frontend: Dialog\n", "debconf: (No usable dialog-like program is installed, so the dialog based frontend cannot be used. at /usr/share/perl5/Debconf/FrontEnd/Dialog.pm line 78, <> line 13.)\n", "debconf: falling back to frontend: Readline\n", "debconf: unable to initialize frontend: Readline\n", "debconf: (This frontend requires a controlling tty.)\n", "debconf: falling back to frontend: Teletype\n", "dpkg-preconfigure: unable to re-open stdin: \n", "Selecting previously unselected package libarchive-dev:amd64.\n", "(Reading database ... 121782 files and directories currently installed.)\n", "Preparing to unpack .../00-libarchive-dev_3.6.0-1ubuntu1_amd64.deb ...\n", "Unpacking libarchive-dev:amd64 (3.6.0-1ubuntu1) ...\n", "Selecting previously unselected package libimagequant0:amd64.\n", "Preparing to unpack .../01-libimagequant0_2.17.0-1_amd64.deb ...\n", "Unpacking libimagequant0:amd64 (2.17.0-1) ...\n", "Selecting previously unselected package libleptonica-dev.\n", "Preparing to unpack .../02-libleptonica-dev_1.82.0-3build1_amd64.deb ...\n", "Unpacking libleptonica-dev (1.82.0-3build1) ...\n", "Selecting previously unselected package libraqm0:amd64.\n", "Preparing to unpack .../03-libraqm0_0.7.0-4ubuntu1_amd64.deb ...\n", "Unpacking libraqm0:amd64 (0.7.0-4ubuntu1) ...\n", "Selecting previously unselected package libtesseract-dev:amd64.\n", "Preparing to unpack .../04-libtesseract-dev_4.1.1-2.1build1_amd64.deb ...\n", "Unpacking libtesseract-dev:amd64 (4.1.1-2.1build1) ...\n", "Selecting previously unselected package mailcap.\n", "Preparing to unpack .../05-mailcap_3.70+nmu1ubuntu1_all.deb ...\n", "Unpacking mailcap (3.70+nmu1ubuntu1) ...\n", "Selecting previously unselected package mime-support.\n", "Preparing to unpack .../06-mime-support_3.66_all.deb ...\n", "Unpacking mime-support (3.66) ...\n", "Selecting previously unselected package python3-olefile.\n", "Preparing to unpack .../07-python3-olefile_0.46-3_all.deb ...\n", "Unpacking python3-ole<PERSON>le (0.46-3) ...\n", "Selecting previously unselected package python3-pil:amd64.\n", "Preparing to unpack .../08-python3-pil_9.0.1-1ubuntu0.3_amd64.deb ...\n", "Unpacking python3-pil:amd64 (9.0.1-1ubuntu0.3) ...\n", "Selecting previously unselected package tesseract-ocr-eng.\n", "Preparing to unpack .../09-tesseract-ocr-eng_1%3a4.00~git30-7274cfa-1.1_all.deb ...\n", "Unpacking tesseract-ocr-eng (1:4.00~git30-7274cfa-1.1) ...\n", "Selecting previously unselected package tesseract-ocr-osd.\n", "Preparing to unpack .../10-tesseract-ocr-osd_1%3a4.00~git30-7274cfa-1.1_all.deb ...\n", "Unpacking tesseract-ocr-osd (1:4.00~git30-7274cfa-1.1) ...\n", "Selecting previously unselected package tesseract-ocr.\n", "Preparing to unpack .../11-tesseract-ocr_4.1.1-2.1build1_amd64.deb ...\n", "Unpacking tesseract-ocr (4.1.1-2.1build1) ...\n", "Selecting previously unselected package tesseract-ocr-script-latn.\n", "Preparing to unpack .../12-tesseract-ocr-script-latn_1%3a4.00~git30-7274cfa-1.1_all.deb ...\n", "Unpacking tesseract-ocr-script-latn (1:4.00~git30-7274cfa-1.1) ...\n", "Setting up tesseract-ocr-script-latn (1:4.00~git30-7274cfa-1.1) ...\n", "Setting up python3-olefile (0.46-3) ...\n", "Setting up tesseract-ocr-eng (1:4.00~git30-7274cfa-1.1) ...\n", "Setting up libleptonica-dev (1.82.0-3build1) ...\n", "Setting up libraqm0:amd64 (0.7.0-4ubuntu1) ...\n", "Setting up libimagequant0:amd64 (2.17.0-1) ...\n", "Setting up libarchive-dev:amd64 (3.6.0-1ubuntu1) ...\n", "Setting up tesseract-ocr-osd (1:4.00~git30-7274cfa-1.1) ...\n", "Setting up mailcap (3.70+nmu1ubuntu1) ...\n", "Setting up libtesseract-dev:amd64 (4.1.1-2.1build1) ...\n", "Setting up mime-support (3.66) ...\n", "Setting up python3-pil:amd64 (9.0.1-1ubuntu0.3) ...\n", "Setting up tesseract-ocr (4.1.1-2.1build1) ...\n", "Processing triggers for man-db (2.10.2-1) ...\n", "Processing triggers for libc-bin (2.35-0ubuntu3.4) ...\n", "/sbin/ldconfig.real: /usr/local/lib/libtbbbind_2_5.so.3 is not a symbolic link\n", "\n", "/sbin/ldconfig.real: /usr/local/lib/libtbbmalloc_proxy.so.2 is not a symbolic link\n", "\n", "/sbin/ldconfig.real: /usr/local/lib/libtbb.so.12 is not a symbolic link\n", "\n", "/sbin/ldconfig.real: /usr/local/lib/libtbbbind_2_0.so.3 is not a symbolic link\n", "\n", "/sbin/ldconfig.real: /usr/local/lib/libtbbbind.so.3 is not a symbolic link\n", "\n", "/sbin/ldconfig.real: /usr/local/lib/libtbbmalloc.so.2 is not a symbolic link\n", "\n"]}]}, {"cell_type": "code", "source": ["!pip install unstructured-pytesseract\n", "!pip install tesseract-ocr"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "u7Rrt88Nex_u", "outputId": "93b098d1-0045-4ca5-8a53-a6fd0d653491"}, "execution_count": 4, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: unstructured-pytesseract in /usr/local/lib/python3.10/dist-packages (0.3.12)\n", "Requirement already satisfied: packaging>=21.3 in /usr/local/lib/python3.10/dist-packages (from unstructured-pytesseract) (24.0)\n", "Requirement already satisfied: Pillow>=8.0.0 in /usr/local/lib/python3.10/dist-packages (from unstructured-pytesseract) (10.3.0)\n", "Collecting tesseract-ocr\n", "  Downloading tesseract-ocr-0.0.1.tar.gz (33 kB)\n", "  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: cython in /usr/local/lib/python3.10/dist-packages (from tesseract-ocr) (3.0.10)\n", "Building wheels for collected packages: tesseract-ocr\n", "  Building wheel for tesseract-ocr (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for tesseract-ocr: filename=tesseract_ocr-0.0.1-cp310-cp310-linux_x86_64.whl size=169757 sha256=97a1d9e6448fc3794cb6ad72398626e744d07f4162d013c9d97da54a6c6de10a\n", "  Stored in directory: /root/.cache/pip/wheels/bb/fd/f3/5c231ecbbb80a1fe33204ff3021d99b54ef6daf6f8099311b8\n", "Successfully built tesseract-ocr\n", "Installing collected packages: tesseract-ocr\n", "Successfully installed tesseract-ocr-0.0.1\n"]}]}, {"cell_type": "code", "source": ["from unstructured.partition.pdf import partition_pdf"], "metadata": {"id": "GiVgnFmee7M7"}, "execution_count": 5, "outputs": []}, {"cell_type": "code", "source": ["raw_pdf_elements=partition_pdf(\n", "    filename=\"/content/data/cj.pdf\",\n", "    strategy=\"hi_res\",\n", "    extract_images_in_pdf=True,\n", "    extract_image_block_types=[\"Image\", \"Table\"],\n", "    extract_image_block_to_payload=False,\n", "    extract_image_block_output_dir=\"extracted_data\"\n", "  )"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 177, "referenced_widgets": ["29fef475a16a499b95bd7495e2388663", "8e3eae6d05294e9e8a28d11b2e4b0ae2", "035c1f9016ec4219a3d9c5b39e4066e7", "07a7635319024098af3c7e313c0cd1d2", "79991327fb3e4b25b963d317e85d43f8", "910aa943220c43a697ca2156df61c4d0", "9f34dbbd331e4dd7ae9a6d69334efa87", "f81683846b9e4d4d9c0a2ca0fcf78386", "63956298d47b4f81aaecbd73a38874ac", "c266c91b3a6e4950aade4d8641db71d1", "5fa906e23e804770ba4ca39b43db0e89"]}, "id": "j9uoVggzfWRI", "outputId": "b11f8430-e6ee-44a4-93db-cb78193351dc"}, "execution_count": 6, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["[nltk_data] Downloading package punkt to /root/nltk_data...\n", "[nltk_data]   Unzipping tokenizers/punkt.zip.\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /root/nltk_data...\n", "[nltk_data]   Unzipping taggers/averaged_perceptron_tagger.zip.\n", "WARNING:unstructured:This function will be deprecated in a future release and `unstructured` will simply use the DEFAULT_MODEL from `unstructured_inference.model.base` to set default model name\n"]}, {"output_type": "display_data", "data": {"text/plain": ["yolox_l0.05.onnx:   0%|          | 0.00/217M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "29fef475a16a499b95bd7495e2388663"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["raw_pdf_elements"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "U4h0uSdEhIo6", "outputId": "4c3e283a-22b5-4656-e87b-c6d6b97a8bb0"}, "execution_count": 7, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[<unstructured.documents.elements.Header at 0x7ad20f037400>,\n", " <unstructured.documents.elements.Header at 0x7ad20f0374c0>,\n", " <unstructured.documents.elements.Title at 0x7ad20061c610>,\n", " <unstructured.documents.elements.Image at 0x7ad20061fe20>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20061c100>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20061c040>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20061f970>,\n", " <unstructured.documents.elements.Text at 0x7ad20061c4c0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20061d930>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200726500>,\n", " <unstructured.documents.elements.Table at 0x7ad20061fe80>,\n", " <unstructured.documents.elements.Title at 0x7ad20f932860>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20061df60>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20061e500>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20061e170>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20061e110>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2007a0100>,\n", " <unstructured.documents.elements.Footer at 0x7ad20061c5b0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20061c220>,\n", " <unstructured.documents.elements.Footer at 0x7ad20061c5e0>,\n", " <unstructured.documents.elements.Header at 0x7ad20061f730>,\n", " <unstructured.documents.elements.Header at 0x7ad20061c370>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20061fbe0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20061fa60>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20061d870>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f047df0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2007a04c0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f046c50>,\n", " <unstructured.documents.elements.Title at 0x7ad2007a03d0>,\n", " <unstructured.documents.elements.Footer at 0x7ad20f046200>,\n", " <unstructured.documents.elements.Footer at 0x7ad20f0471f0>,\n", " <unstructured.documents.elements.Header at 0x7ad20f0699f0>,\n", " <unstructured.documents.elements.Header at 0x7ad20f068610>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f06a3b0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20f069270>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20f06a440>,\n", " <unstructured.documents.elements.Title at 0x7ad2007a0ee0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f06b880>,\n", " <unstructured.documents.elements.Title at 0x7ad2007a2a70>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2005d6c80>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2007a0310>,\n", " <unstructured.documents.elements.Footer at 0x7ad2005d6230>,\n", " <unstructured.documents.elements.Footer at 0x7ad2005d60e0>,\n", " <unstructured.documents.elements.Header at 0x7ad2005d6590>,\n", " <unstructured.documents.elements.Header at 0x7ad2005d4430>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2005d4610>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2005d6770>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2007a0190>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2005d57b0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2007a1150>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2005d4580>,\n", " <unstructured.documents.elements.Title at 0x7ad2007a1120>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20061f400>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2007a0910>,\n", " <unstructured.documents.elements.Footer at 0x7ad20f06a980>,\n", " <unstructured.documents.elements.Footer at 0x7ad20061d9c0>,\n", " <unstructured.documents.elements.Header at 0x7ad20f024880>,\n", " <unstructured.documents.elements.Header at 0x7ad2005d4dc0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2005d6290>,\n", " <unstructured.documents.elements.Title at 0x7ad2007a08e0>,\n", " <unstructured.documents.elements.Image at 0x7ad2005d4c70>,\n", " <unstructured.documents.elements.Title at 0x7ad2007a1000>,\n", " <unstructured.documents.elements.Footer at 0x7ad2005d4be0>,\n", " <unstructured.documents.elements.Footer at 0x7ad2005d6c50>,\n", " <unstructured.documents.elements.Header at 0x7ad2005d6560>,\n", " <unstructured.documents.elements.Header at 0x7ad2005d4a90>,\n", " <unstructured.documents.elements.Image at 0x7ad2005d4310>,\n", " <unstructured.documents.elements.Title at 0x7ad2007a0880>,\n", " <unstructured.documents.elements.Footer at 0x7ad2005d70d0>,\n", " <unstructured.documents.elements.Footer at 0x7ad2005d4bb0>,\n", " <unstructured.documents.elements.Header at 0x7ad2005d77f0>,\n", " <unstructured.documents.elements.Header at 0x7ad2005d5750>,\n", " <unstructured.documents.elements.Image at 0x7ad2005d5360>,\n", " <unstructured.documents.elements.Title at 0x7ad2007a10c0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2005d6a70>,\n", " <unstructured.documents.elements.Title at 0x7ad2007a0c40>,\n", " <unstructured.documents.elements.Footer at 0x7ad2005d65f0>,\n", " <unstructured.documents.elements.Footer at 0x7ad2005d4910>,\n", " <unstructured.documents.elements.Header at 0x7ad2005d4760>,\n", " <unstructured.documents.elements.Header at 0x7ad2005d7a60>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2005d5570>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2005d59c0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2005d4490>,\n", " <unstructured.documents.elements.Text at 0x7ad200756f80>,\n", " <unstructured.documents.elements.Image at 0x7ad2005d50f0>,\n", " <unstructured.documents.elements.Footer at 0x7ad2006b4a30>,\n", " <unstructured.documents.elements.Footer at 0x7ad2005d79a0>,\n", " <unstructured.documents.elements.Header at 0x7ad2006b4970>,\n", " <unstructured.documents.elements.Header at 0x7ad2006b4100>,\n", " <unstructured.documents.elements.Image at 0x7ad2006b40d0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2006b4cd0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2007547c0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2006b4b20>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2006b44c0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2006b4730>,\n", " <unstructured.documents.elements.Title at 0x7ad200757c40>,\n", " <unstructured.documents.elements.Footer at 0x7ad2006b4370>,\n", " <unstructured.documents.elements.Footer at 0x7ad2006b5540>,\n", " <unstructured.documents.elements.Header at 0x7ad2006b5450>,\n", " <unstructured.documents.elements.Header at 0x7ad2006b5630>,\n", " <unstructured.documents.elements.Image at 0x7ad2006b56c0>,\n", " <unstructured.documents.elements.Footer at 0x7ad2006b59f0>,\n", " <unstructured.documents.elements.Footer at 0x7ad2006b5a20>,\n", " <unstructured.documents.elements.Header at 0x7ad2006b5ae0>,\n", " <unstructured.documents.elements.Header at 0x7ad2006b5c90>,\n", " <unstructured.documents.elements.Image at 0x7ad2006b6080>,\n", " <unstructured.documents.elements.Title at 0x7ad200756ec0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2006b6050>,\n", " <unstructured.documents.elements.Title at 0x7ad200757430>,\n", " <unstructured.documents.elements.Footer at 0x7ad2006b6590>,\n", " <unstructured.documents.elements.Footer at 0x7ad2006b6800>,\n", " <unstructured.documents.elements.Header at 0x7ad2006b66e0>,\n", " <unstructured.documents.elements.Header at 0x7ad2006b6950>,\n", " <unstructured.documents.elements.Image at 0x7ad2006b6c80>,\n", " <unstructured.documents.elements.Footer at 0x7ad2006b6d10>,\n", " <unstructured.documents.elements.Footer at 0x7ad2006b6bf0>,\n", " <unstructured.documents.elements.Header at 0x7ad2006b6f80>,\n", " <unstructured.documents.elements.Header at 0x7ad2006b6fe0>,\n", " <unstructured.documents.elements.Image at 0x7ad2006b7130>,\n", " <unstructured.documents.elements.Title at 0x7ad200756740>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2006b73a0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200756950>,\n", " <unstructured.documents.elements.Footer at 0x7ad2006b7790>,\n", " <unstructured.documents.elements.Footer at 0x7ad2006b7670>,\n", " <unstructured.documents.elements.Header at 0x7ad2006b7ac0>,\n", " <unstructured.documents.elements.Header at 0x7ad2006b7940>,\n", " <unstructured.documents.elements.Image at 0x7ad2006b7bb0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200756a70>,\n", " <unstructured.documents.elements.Footer at 0x7ad2006b6380>,\n", " <unstructured.documents.elements.Footer at 0x7ad2006b7d00>,\n", " <unstructured.documents.elements.Header at 0x7ad2006b60e0>,\n", " <unstructured.documents.elements.Header at 0x7ad2006b51b0>,\n", " <unstructured.documents.elements.Image at 0x7ad2006b50c0>,\n", " <unstructured.documents.elements.Title at 0x7ad2006b4f40>,\n", " <unstructured.documents.elements.Title at 0x7ad200757d60>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2006d9930>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200757970>,\n", " <unstructured.documents.elements.Footer at 0x7ad2006d98d0>,\n", " <unstructured.documents.elements.Footer at 0x7ad2006da890>,\n", " <unstructured.documents.elements.Header at 0x7ad2006daef0>,\n", " <unstructured.documents.elements.Header at 0x7ad2006d9780>,\n", " <unstructured.documents.elements.Image at 0x7ad2006d8df0>,\n", " <unstructured.documents.elements.Title at 0x7ad200756ad0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2006d9ea0>,\n", " <unstructured.documents.elements.Title at 0x7ad200757490>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2006da3e0>,\n", " <unstructured.documents.elements.Title at 0x7ad2007542e0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2006da230>,\n", " <unstructured.documents.elements.Title at 0x7ad200754af0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2006da8f0>,\n", " <unstructured.documents.elements.Title at 0x7ad200756e60>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2006da680>,\n", " <unstructured.documents.elements.Title at 0x7ad200754a00>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2006da4a0>,\n", " <unstructured.documents.elements.Title at 0x7ad200757d30>,\n", " <unstructured.documents.elements.Footer at 0x7ad20070f550>,\n", " <unstructured.documents.elements.Footer at 0x7ad20070f520>,\n", " <unstructured.documents.elements.Header at 0x7ad20070eef0>,\n", " <unstructured.documents.elements.Header at 0x7ad20070de10>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20070e0e0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20070e470>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20070dfc0>,\n", " <unstructured.documents.elements.Title at 0x7ad2007574f0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20070dae0>,\n", " <unstructured.documents.elements.Title at 0x7ad200756830>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20070e050>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20070dbd0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2007543d0>,\n", " <unstructured.documents.elements.Footer at 0x7ad20070c310>,\n", " <unstructured.documents.elements.Footer at 0x7ad20070df30>,\n", " <unstructured.documents.elements.Header at 0x7ad20070c070>,\n", " <unstructured.documents.elements.Header at 0x7ad20070c4c0>,\n", " <unstructured.documents.elements.Image at 0x7ad20070c2e0>,\n", " <unstructured.documents.elements.Image at 0x7ad20070c6a0>,\n", " <unstructured.documents.elements.Footer at 0x7ad20070c760>,\n", " <unstructured.documents.elements.Footer at 0x7ad20070c550>,\n", " <unstructured.documents.elements.Header at 0x7ad20070ca90>,\n", " <unstructured.documents.elements.Header at 0x7ad20070cc10>,\n", " <unstructured.documents.elements.Image at 0x7ad20070cbb0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200757a30>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20070cdf0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200756f20>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20070d180>,\n", " <unstructured.documents.elements.Footer at 0x7ad20070d4b0>,\n", " <unstructured.documents.elements.Footer at 0x7ad20070d450>,\n", " <unstructured.documents.elements.Header at 0x7ad20070d540>,\n", " <unstructured.documents.elements.Header at 0x7ad20070d810>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20070d840>,\n", " <unstructured.documents.elements.Text at 0x7ad200754760>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200757be0>,\n", " <unstructured.documents.elements.Title at 0x7ad2007568c0>,\n", " <unstructured.documents.elements.Image at 0x7ad20067b7f0>,\n", " <unstructured.documents.elements.Title at 0x7ad2007576a0>,\n", " <unstructured.documents.elements.Image at 0x7ad200679f60>,\n", " <unstructured.documents.elements.Image at 0x7ad20067b940>,\n", " <unstructured.documents.elements.Image at 0x7ad20067bee0>,\n", " <unstructured.documents.elements.Image at 0x7ad20067bdc0>,\n", " <unstructured.documents.elements.Title at 0x7ad2007569e0>,\n", " <unstructured.documents.elements.Title at 0x7ad200757010>,\n", " <unstructured.documents.elements.Image at 0x7ad20067a050>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200755fc0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20067a230>,\n", " <unstructured.documents.elements.Image at 0x7ad200679f30>,\n", " <unstructured.documents.elements.Title at 0x7ad200757340>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200756980>,\n", " <unstructured.documents.elements.Title at 0x7ad200757fd0>,\n", " <unstructured.documents.elements.Title at 0x7ad2007567d0>,\n", " <unstructured.documents.elements.Text at 0x7ad200754b20>,\n", " <unstructured.documents.elements.Title at 0x7ad2007565c0>,\n", " <unstructured.documents.elements.Title at 0x7ad200757cd0>,\n", " <unstructured.documents.elements.Title at 0x7ad200757e20>,\n", " <unstructured.documents.elements.Image at 0x7ad20067a8f0>,\n", " <unstructured.documents.elements.Title at 0x7ad2007577c0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200756470>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20067b130>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20067b160>,\n", " <unstructured.documents.elements.Footer at 0x7ad20067b070>,\n", " <unstructured.documents.elements.Footer at 0x7ad20067ad70>,\n", " <unstructured.documents.elements.Header at 0x7ad2007a0ac0>,\n", " <unstructured.documents.elements.Footer at 0x7ad2007a05b0>,\n", " <unstructured.documents.elements.Header at 0x7ad2007a0af0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2007a0a30>,\n", " <unstructured.documents.elements.Footer at 0x7ad2007a0e50>]"]}, "metadata": {}, "execution_count": 7}]}, {"cell_type": "code", "source": ["Header=[]\n", "Footer=[]\n", "Title=[]\n", "NarrativeText=[]\n", "Text=[]\n", "ListItem=[]\n", "\n", "\n", "for element in raw_pdf_elements:\n", "  if \"unstructured.documents.elements.Header\" in str(type(element)):\n", "            Header.append(str(element))\n", "  elif \"unstructured.documents.elements.Footer\" in str(type(element)):\n", "            Footer.append(str(element))\n", "  elif \"unstructured.documents.elements.Title\" in str(type(element)):\n", "            Title.append(str(element))\n", "  elif \"unstructured.documents.elements.NarrativeText\" in str(type(element)):\n", "            NarrativeText.append(str(element))\n", "  elif \"unstructured.documents.elements.Text\" in str(type(element)):\n", "            Text.append(str(element))\n", "  elif \"unstructured.documents.elements.ListItem\" in str(type(element)):\n", "            ListItem.append(str(element))\n", "\n"], "metadata": {"id": "0udLgeRzkWzo"}, "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": ["NarrativeText"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "U3Qjtbxfkslh", "outputId": "5921899e-adfc-45a8-c798-3b30c4e3c580"}, "execution_count": 9, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['JAMIN BALL',\n", " 'NOV 10, 2023',\n", " '35',\n", " 'Every week I’ll provide updates on the latest trends in cloud soware companies. Follow along to',\n", " 'stay up to date!',\n", " 'OpenAI had their big developer day this week, and I wanted to call out two key announcements (and trends): increasing context windows and decreasing costs.',\n", " 'When I think about the monetization of AI (and which “layers” monetize rst) I’ve always thought it would follow the below order, with each layer lagging the one that comes before it.',\n", " 'Share',\n", " 'We’ve clearly well underway of the rst 3 layers monetizing. Just starting the fourth layer, with the h layer showing up in some pockets, but not really widespread monetization (and I should clarify - scalable monetization). The caveat is important - I’ve heard of a well known company that had an oshore team handling lots of manual customer work (ie responses). And this “product” had a ~50% gross margin. When they started using large language models from OpenAI, the gross margin on the same product went to -100%! (yes, that’s negative 100%). While',\n", " 'the product was “monetizing” I wouldn’t count it as scalable monetization.',\n", " 'We haven’t quite yet cracked AI used in production in widespread fashion. There are many limiters here - data security and compliance are big ones. But even more important right now is cost. At the end of the day, these large language models are quite expensive! And as a vendor using them, you can either pass through the costs to your end customer (to maintain your gross margins), or eat the costs and lower your gross margins (because the customer isn’t willing to pay the incremental cost for incremental functionality brought about by AI), and hope the model providers lower their costs in the future. It seems like every company has been experimenting. Saying things like “just build out all the AI functionality now and then we’ll evaluate if customers will pay for it.” Now that we’re getting through this initial wave of experimentation and AI buildout, there’s quite a bit of sticker shock when the OpenAI bills come due! People are looking to build in model portability to enable them to switch to lower cost models (or open',\n", " 'This brings me back to the initial point - the two announcements from OpenAI I want to highlight here.',\n", " 'The cost decrease is very meaningful - it’s lowers the barrier to experiment with AI, and also lowers the barrier for these AI functionalities to be pushed into production (because vendors don’t have to increase price nearly as much). Also - <PERSON> <PERSON><PERSON> pointed out on Twitter / X, as context windows increase the need for task / domain-specic models (or ne-tuned models) decreases. The counter argument to this is will we be able to nd enough high quality long context training data. Either way - it’s clear these models are becoming cheaper and more eective, which is an exciting future for AI! I think we’re about to see an explosion of good business model AI applications in the near future. 2024 will be the year of AI applications!',\n", " 'This week soware stocks shot up on Tuesday, largely a result of Datadog’s quarterly earnings. Datadog in particular was up ~30%. So what happened? They made a number of comments about',\n", " 'optimizations easing up, and the worst being behind us. Here are some quotes:',\n", " \"than they've been for the past year.”\",\n", " 'existing customers improving in Q3 relative to Q2.”',\n", " 'Datadog was one of the rst companies to really highlight an improving macro environment. And even more important, they called out a great month of October (rst month of Q4 for them). So how do we contrast their positive commentary, with largely neutral commentary from the rest of the soware universe? Most likely Datadog is seeing trends more unique to their own business. As the market puts a greater emphasis on bundled platforms today vs point solutions, they appear to be an incremental winner of market share. Best of breed platforms (with more of a usage based model) will recover rst (in terms of revenue growth recovery). Datadog appears to be in that bucket and recovering rst. This doesn’t mean the rest of the soware universe will follow suite. There will be many “pretenders” who never recover and nd themselves bundled',\n", " 'into oblivion. However, the positive commentary from Datadog is the rst sign that we’re',\n", " 'starting to turn a corner. So while the rest of the soware universe may not be at that corner today, we’re starting to see the light at the end of the tunnel.',\n", " 'SaaS businesses are generally valued on a multiple of their revenue - in most cases the projected revenue for the next 12 months. Revenue multiples are a shorthand valuation framework. Given most soware companies are not protable, or not generating meaningful FCF, it’s the only metric to compare the entire industry against. Even a DCF is riddled with long term assumptions. The promise of SaaS is that growth in the early years leads to prots in the mature years. Multiples shown below are calculated by taking the Enterprise Value (market cap + debt - cash) / NTM revenue.',\n", " 'Bucketed by Growth. In the buckets below I consider high growth >30% projected NTM growth,',\n", " 'mid growth 15%-30% and low growth <15%',\n", " 'The below chart shows the EV / NTM revenue multiple divided by NTM consensus growth expectations. So a company trading at 20x NTM revenue that is projected to grow 100% would be trading at 0.2x. The goal of this graph is to show how relatively cheap / expensive each stock is',\n", " 'The line chart shows the median of all companies with a FCF multiple >0x and <100x. I created',\n", " 'this subset to show companies where FCF is a relevant valuation metric.',\n", " 'Companies with negative NTM FCF are not listed on the chart',\n", " 'How correlated is growth to valuation multiple?',\n", " 'How correlated is growth to valuation multiple?',\n", " 'Rule of 40 shows rev growth + FCF margin (both LTM and NTM for growth + margins). FCF calculated as Cash Flow from Operations - Capital Expenditures',\n", " 'GM Adjusted Payback is calculated as: (Previous Q S&M) / (Net New ARR in Q x Gross Margin) x 12 . It shows the number of months it takes for a SaaS business to payback their fully burdened CAC on a gross prot basis. Most public companies don’t report net new ARR, so I’m taking an implied ARR metric (quarterly subscription revenue x 4). Net new ARR is simply the ARR of the current quarter, minus the ARR of the previous quarter. Companies that do not disclose',\n", " 'subscription rev have been le out of the analysis and are listed as NA.',\n", " 'Sources used in this post include Bloomberg, Pitchbook and company lings',\n", " 'The information presented in this newsletter is the opinion of the author and does not necessarily reect the view of any other person or entity, including Altimeter Capital Management, LP (\"Altimeter\"). The information provided is believed to be from reliable sources but no liability is accepted for any inaccuracies. This is for information purposes and should not be construed as an investment recommendation. Past performance is no guarantee of future performance. Altimeter is an investment adviser registered with the U.S. Securities and',\n", " 'Exchange Commission. Registration does not imply a certain level of skill or training.',\n", " 'This post and the information presented are intended for informational purposes only. The views expressed herein are the author’s alone and do not constitute an oer to sell, or a recommendation to purchase, or a solicitation of an oer to buy, any security, nor a recommendation for any investment product or service. While certain information contained herein has been obtained from sources believed to be reliable, neither the author nor any of his employers or their aliates have independently veried this information, and its accuracy and completeness cannot be guaranteed. Accordingly, no representation or warranty, express or',\n", " 'implied, is made as to, and no reliance should be placed on, the fairness, accuracy, timeliness or completeness of this information. The author and all employers and their aliated persons assume no liability for this information and no obligation to update the information or analysis contained herein in the future.',\n", " 'Type your email...',\n", " 'Write a comment...',\n", " 'Write a comment...',\n", " 'Thank you for your interesting thoughts regarding the monetization layers of AI. Really inspiring!',\n", " 'Good stuff as always. Your point about the reduced costs per token is interesting and hopefully',\n", " 'allows companies to experiment with AI more in their workflows',\n", " 'LIKE REPLY SHARE',\n", " '© 2023 Jamin Ball ∙ Privacy ∙ Terms ∙ Collection notice Substack is the home for great writing']"]}, "metadata": {}, "execution_count": 9}]}, {"cell_type": "code", "source": ["ListItem"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "eXxg4HwvkwtB", "outputId": "c4d9a015-e841-47d7-bb84-267aad8fa743"}, "execution_count": 10, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['1. Raw silicon (chips like Nvidia bought in large quantities to build out infra to service upcoming demand).',\n", " '2. Model providers (OpenAI, Anthropic, etc as companies start building out Al).',\n", " '2. Model providers (OpenAI, Anthropic, etc as companies start building out AI).',\n", " '3. Hyperscalers (AWS, Azure, GCP as companies look for cloud GPUs who aren’t building out their own data centers)',\n", " '4. Infra (Data layer, orchestration, monitoring, ops, etc)',\n", " '5. Durable Applications',\n", " '1. Context length: Context window of GPT 4 Turbo went from 8k tokens to 128k tokens (think of this as ~300 pages of text worth of input). This means what you can put into a prompt just went up dramatically',\n", " '2. Costs decreasing: GPT 4 Turbo is 3x cheaper for input tokens (think of this as roughly the length of the prompt) and 2x cheaper for output tokens. This equates to $0.01 per 1k input tokens, and $0.03 per 1k output tokens. On a blended basis, GPT 4 Turbo is roughly 2.5-3x',\n", " \"“It looks like we've hit an inection point. It looks like there's a lot less overhang now in terms of what needs to be optimized or could be optimized by customers. It looks like also optimization is less intense and less widespread across the customer base.”\",\n", " '“We had a very healthy start to Q4 in October...the trends we see in early Q4 are stronger',\n", " '“As we look at our overall customer activity, we continue to see customers optimizing but with less impact than we experienced in Q2, contributing to our usage growth with',\n", " '“As a reminder, last quarter, we discussed a cohort of customers who began optimizing about a year ago and we said that they appear to stabilize their users growth at the end of Q2. That trend has held for the past several months with that cohorts usage remaining',\n", " 'Overall Median: 5.0x',\n", " 'Top 5 Median: 14.5x',\n", " '© 10Y: 4.6%',\n", " 'High Growth Median: 11.8x',\n", " 'Mid Growth Median: 7.4x',\n", " 'e Low Growth Median: 3.9x',\n", " 'e Median NTM growth rate: 15%',\n", " 'e¢ Median LTM growth rate: 21%',\n", " 'e Median Gross Margin: 75%',\n", " 'e¢ Median Operating Margin (18%)',\n", " 'e Median FCF Margin: 8%',\n", " '© Median Net Retention: 114%',\n", " 'Median CAC Payback: 35 months',\n", " 'Median S&M % Revenue: 42%',\n", " 'e Median R&D % Revenue: 26%',\n", " 'Median G&A % Revenue: 17%']"]}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "code", "source": ["img=[]\n", "for element in raw_pdf_elements:\n", "  if \"unstructured.documents.elements.Image\" in str(type(element)):\n", "            img.append(str(element))"], "metadata": {"id": "QRsiXMKEkqjl"}, "execution_count": 11, "outputs": []}, {"cell_type": "code", "source": ["img[2]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 144}, "id": "-idYwDsQlCYy", "outputId": "1bb9af7d-4f9d-4950-e7db-908b826b43f2"}, "execution_count": 12, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'EV/NTM EV/2024 EV/NTM NTMRev Gross Operating FCF % in Top 10 Company Rev Rev FCF Growth Margin Margin Margin Multiple LTM 1 Snowflake 15.5x 13.4x 55x 27% 66% (41%) 25% 100% 2 MongoDB 14.6x 12.9x 133x 17% 74% (18%) 3% 67% 3 Palantir 14.5x 13.9x 58x 19% 80% 2% 22% 47% 4 Cloudflare 13.4x 12.6x 153x 28% 76% (16%) 8% 100% 5 Datadog 13.1x 12.4x 52x 19% 80% (5%) 25% 99% 6 CrowdStrike 12.5x 11.1x 37x 31% 74% (6%) 30% 31% 7 Adobe 12.3x 11.9x 30x 12% 88% 34% 40% 26% 8 ServiceNow 12.2x 11.6x 38x 21% 79% 8% 28% 70% 9 Samsara 11.8x 10.5x 393x 31% 72% (34%) (3%) 72% 10 Zscaler 11.8x 10.5x 48x 27% 78% (14%) 21% 36% Overall Median = 50x. 48x 33.7 18% = 75% = (16%) 8% Clouded Judgement @jaminball ALTIMETER'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "code", "source": ["raw_pdf_elements2=partition_pdf(\n", "    filename=\"/content/data2/Retrieval-Augmented-Generation-for-NLP.pdf\",\n", "    strategy=\"hi_res\",\n", "    extract_images_in_pdf=True,\n", "    extract_image_block_types=[\"Image\", \"Table\"],\n", "    extract_image_block_to_payload=False,\n", "    extract_image_block_output_dir=\"extracted_data2\"\n", "  )"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "wxEKH9xWk9SP", "outputId": "68e30c33-ab8f-43f1-cf55-a62d5e577f82"}, "execution_count": 13, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["WARNING:unstructured:This function will be deprecated in a future release and `unstructured` will simply use the DEFAULT_MODEL from `unstructured_inference.model.base` to set default model name\n"]}]}, {"cell_type": "code", "source": ["raw_pdf_elements2"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fk8hSSbZlBhM", "outputId": "54ba4415-dff1-448b-8afc-d55f101e6828"}, "execution_count": 14, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[<unstructured.documents.elements.Text at 0x7ad2007a3a90>,\n", " <unstructured.documents.elements.Header at 0x7ad20f068640>,\n", " <unstructured.documents.elements.Text at 0x7ad2007a22f0>,\n", " <unstructured.documents.elements.Title at 0x7ad200276290>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200275f30>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200275ed0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f068730>,\n", " <unstructured.documents.elements.Title at 0x7ad2007a0670>,\n", " <unstructured.documents.elements.Title at 0x7ad20f06a590>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f068f10>,\n", " <unstructured.documents.elements.Title at 0x7ad2001dd6c0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2001dd2a0>,\n", " <unstructured.documents.elements.Image at 0x7ad2001ddbd0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2001de170>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2001df2b0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2001df550>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2001dd900>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2001dda20>,\n", " <unstructured.documents.elements.Title at 0x7ad2001ddf00>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2001de3e0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2001df4c0>,\n", " <unstructured.documents.elements.Footer at 0x7ad2001de080>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2001df400>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2001df310>,\n", " <unstructured.documents.elements.Title at 0x7ad2001dd7e0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2001df5b0>,\n", " <unstructured.documents.elements.Formula at 0x7ad2007a29b0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200229c60>,\n", " <unstructured.documents.elements.Formula at 0x7ad200228550>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200229e10>,\n", " <unstructured.documents.elements.Title at 0x7ad2002296c0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200229f60>,\n", " <unstructured.documents.elements.Title at 0x7ad2007a3df0>,\n", " <unstructured.documents.elements.Formula at 0x7ad20022b100>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20022b4f0>,\n", " <unstructured.documents.elements.Title at 0x7ad2002287c0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20022a950>,\n", " <unstructured.documents.elements.Title at 0x7ad200229b10>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200229480>,\n", " <unstructured.documents.elements.Footer at 0x7ad20022a7a0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200228580>,\n", " <unstructured.documents.elements.Title at 0x7ad200229150>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2007a3f10>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200228910>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200229e70>,\n", " <unstructured.documents.elements.Title at 0x7ad200228d60>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200228df0>,\n", " <unstructured.documents.elements.Title at 0x7ad20022b580>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200228220>,\n", " <unstructured.documents.elements.Title at 0x7ad2002299f0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2002286a0>,\n", " <unstructured.documents.elements.Text at 0x7ad2007a23e0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad2002295d0>,\n", " <unstructured.documents.elements.Title at 0x7ad20022a410>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200228070>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f044790>,\n", " <unstructured.documents.elements.Title at 0x7ad20f0458d0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f047ac0>,\n", " <unstructured.documents.elements.Title at 0x7ad20f047670>,\n", " <unstructured.documents.elements.Title at 0x7ad20f047be0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f0471c0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f047790>,\n", " <unstructured.documents.elements.Footer at 0x7ad20f0443a0>,\n", " <unstructured.documents.elements.FigureCaption at 0x7ad20f047b20>,\n", " <unstructured.documents.elements.Title at 0x7ad2007a3c40>,\n", " <unstructured.documents.elements.Title at 0x7ad20022b970>,\n", " <unstructured.documents.elements.Title at 0x7ad20022ba90>,\n", " <unstructured.documents.elements.Table at 0x7ad20f044cd0>,\n", " <unstructured.documents.elements.Title at 0x7ad20022bdf0>,\n", " <unstructured.documents.elements.Text at 0x7ad2007a2200>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f0457e0>,\n", " <unstructured.documents.elements.Title at 0x7ad20f044d90>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f047940>,\n", " <unstructured.documents.elements.Title at 0x7ad20f044fa0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f045510>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f044ca0>,\n", " <unstructured.documents.elements.Title at 0x7ad20f044df0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f044340>,\n", " <unstructured.documents.elements.Text at 0x7ad2007a3ee0>,\n", " <unstructured.documents.elements.Image at 0x7ad20f0476a0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f046d40>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f046b60>,\n", " <unstructured.documents.elements.Table at 0x7ad20f045300>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f045810>,\n", " <unstructured.documents.elements.Title at 0x7ad20f0467a0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f0468f0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f045ea0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f0460e0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20f047100>,\n", " <unstructured.documents.elements.Footer at 0x7ad1ebe35420>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad1ebe34a00>,\n", " <unstructured.documents.elements.Title at 0x7ad20022bd90>,\n", " <unstructured.documents.elements.Table at 0x7ad1ebe35540>,\n", " <unstructured.documents.elements.FigureCaption at 0x7ad1ebe36d70>,\n", " <unstructured.documents.elements.Table at 0x7ad1ebe35b10>,\n", " <unstructured.documents.elements.Title at 0x7ad20022bd00>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad1ebe35d20>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad1ebe35e40>,\n", " <unstructured.documents.elements.Image at 0x7ad1ebe36e60>,\n", " <unstructured.documents.elements.FigureCaption at 0x7ad1ebe36950>,\n", " <unstructured.documents.elements.Title at 0x7ad1ebe37ac0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad1ebe349a0>,\n", " <unstructured.documents.elements.Footer at 0x7ad1ebe36e90>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad1ebe34a90>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad1ebe364d0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad1ebe34e20>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad1ebe35000>,\n", " <unstructured.documents.elements.Title at 0x7ad1ebe34d90>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad1ebe34d30>,\n", " <unstructured.documents.elements.Text at 0x7ad2007a2b90>,\n", " <unstructured.documents.elements.Title at 0x7ad1ebe37820>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad1ebe377f0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad1ebe379d0>,\n", " <unstructured.documents.elements.Title at 0x7ad1ebe351e0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad1ebe376a0>,\n", " <unstructured.documents.elements.Title at 0x7ad1ebe34400>,\n", " <unstructured.documents.elements.ListItem at 0x7ad1ebe34190>,\n", " <unstructured.documents.elements.ListItem at 0x7ad1ebe346d0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad1ebe37160>,\n", " <unstructured.documents.elements.ListItem at 0x7ad1ebe34820>,\n", " <unstructured.documents.elements.ListItem at 0x7ad1ebe37220>,\n", " <unstructured.documents.elements.ListItem at 0x7ad1ebe34b80>,\n", " <unstructured.documents.elements.Footer at 0x7ad1ebe342b0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad1ebe343a0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad1ebe373a0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad1ebe372b0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad1ebe367a0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad1ebe36fe0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad1ebe37460>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2007527a0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2007508b0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200752950>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200751240>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200750f40>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200751000>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200752350>,\n", " <unstructured.documents.elements.Footer at 0x7ad200752920>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200752f20>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200751750>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200750be0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200134130>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200135c30>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200134df0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20036d3f0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20036eb00>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20036ed70>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20036e9e0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20036fcd0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20036ecb0>,\n", " <unstructured.documents.elements.Footer at 0x7ad20036fd30>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20036f490>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20036ea40>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20036e320>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200442230>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2004429e0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200441cc0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200442380>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200443a30>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200441ea0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2004404f0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2004432e0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200442d70>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200442560>,\n", " <unstructured.documents.elements.Footer at 0x7ad200440880>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200440970>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200440700>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200442170>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2004436a0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200443610>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200441180>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200441420>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200442d10>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2004428f0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200441db0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200441600>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200442f20>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2004415a0>,\n", " <unstructured.documents.elements.Text at 0x7ad2007a3c10>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200440ca0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200440730>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200441240>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200443f10>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200443b50>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200440fa0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200440fd0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200440c10>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200339960>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2003381c0>,\n", " <unstructured.documents.elements.Footer at 0x7ad2003393f0>,\n", " <unstructured.documents.elements.ListItem at 0x7ad200339060>,\n", " <unstructured.documents.elements.ListItem at 0x7ad20033b700>,\n", " <unstructured.documents.elements.ListItem at 0x7ad2003385e0>,\n", " <unstructured.documents.elements.Header at 0x7ad200338370>,\n", " <unstructured.documents.elements.Title at 0x7ad20033bd30>,\n", " <unstructured.documents.elements.Title at 0x7ad200338070>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200338850>,\n", " <unstructured.documents.elements.Title at 0x7ad20033b370>,\n", " <unstructured.documents.elements.Image at 0x7ad20033bb80>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200339030>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200338c10>,\n", " <unstructured.documents.elements.Title at 0x7ad200338ee0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200338ac0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200339630>,\n", " <unstructured.documents.elements.Footer at 0x7ad2003397b0>,\n", " <unstructured.documents.elements.Title at 0x7ad200338220>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200339270>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200339480>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200339330>,\n", " <unstructured.documents.elements.Title at 0x7ad200339b70>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20033a560>,\n", " <unstructured.documents.elements.Title at 0x7ad20033a9e0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20033a980>,\n", " <unstructured.documents.elements.Title at 0x7ad20033acb0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20033ab90>,\n", " <unstructured.documents.elements.Footer at 0x7ad20033ad70>,\n", " <unstructured.documents.elements.FigureCaption at 0x7ad20033aef0>,\n", " <unstructured.documents.elements.Table at 0x7ad20033b7f0>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20033ba90>,\n", " <unstructured.documents.elements.Title at 0x7ad20033af20>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad20033ba00>,\n", " <unstructured.documents.elements.Title at 0x7ad20033b970>,\n", " <unstructured.documents.elements.NarrativeText at 0x7ad200339bd0>,\n", " <unstructured.documents.elements.Header at 0x7ad20033baf0>]"]}, "metadata": {}, "execution_count": 14}]}, {"cell_type": "code", "source": ["img=[]\n", "for element in raw_pdf_elements2:\n", "  if \"unstructured.documents.elements.Image\" in str(type(element)):\n", "            img.append(str(element))"], "metadata": {"id": "K-NxyHd2mc_n"}, "execution_count": 15, "outputs": []}, {"cell_type": "code", "source": ["img"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZyheLpbZnDnm", "outputId": "562685ff-c5c5-4c5f-a719-d74050a6cb42"}, "execution_count": 16, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['@--- ee ee ee ee ee ee ee ee ee ee eee The middle ear includes End-to-End Backprop through q and pe the tympanic cavity and the three ossicles. (y) Define \"middle ear\" (x) Question Answering: Question Query Retriever py Document Generator pg “fnower Generation Index. (Non-Parametric) (Parametric) d(z) supports (y) <PERSON> was born in Hawaii. (x) q(x) Fact Verification: Fact Query Fact Verification: Label Generation The Divine This 14th century work Comedy (x) is divided into 3 Jeopardy Question Generation: Answer Query sections: \"Inferno\", \"purgatorio\" & \"Paradiso\" @) Question Generation',\n", " 'Document 1: his works are considered classics of American Doc 1 | | literature ... His wartime experiences formed the basis for his novel poe 2 | | “A Farewell to Arms” (1929) ... Doc 3 Document 2: ... artists of the 1920s “Lost Generation” expatriate Doe 4 community. His debut novel, \"The Sun Also Rises”, was published °° in 1926. Doc 5 & , es ee £ te os & ss . TES eS',\n", " 'Bee TT % 80 Porm Sa SRS nana ga g / Z fr = 70 2 | / 3 RAG TORRE 2 nf |! g <= RAG-Tok B-1 Ba Ze H=- RAGSeq RL a ; 3 Zs == RAG-Seq BA Q 50 > 50 ZO — reactor | & 3 soft === RAGSeq | Z 40 2 4s rr rr rr nr) rr nr K Retrieved Docs K Retrieved Docs K Retrieved Docs',\n", " 'View full instructions Which sentence is more factually true? View tool guide Select an option Subject : He<PERSON>way eI Note: Some questions are Sentence Ais more 1 control questions. We require Sentence A : \"The Sun Also Rises\" is a novel by this author of \"A true good accuracy on our control Farewell to Arms\" Sentence Bis more 2 questions to accept true responses. Sentence B : This author of \"The Sun Also Rises\" was born in Both sentences are 8 Havana, Cuba, the son of Spanish immigrants ‘rue Indicate which one of the P a following sentences is more Both sentences are factually true with respect to completely untrue the subject. Using the internet to check whether the sentences are true is encouraged.']"]}, "metadata": {}, "execution_count": 16}]}, {"cell_type": "code", "source": ["tab=[]\n", "for element in raw_pdf_elements2:\n", "  if \"unstructured.documents.elements.Table\" in str(type(element)):\n", "            tab.append(str(element))"], "metadata": {"id": "EJXbI-qnmiqh"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": ["tab[0]"], "metadata": {"id": "ggmzHxN_nGbQ", "outputId": "de7e1928-1052-4bf5-caf7-2c2db4b17bbb", "colab": {"base_uri": "https://localhost:8080/", "height": 90}}, "execution_count": 18, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Closed Book T5-11B [52] 34.5 T5-11B+SSM[52] 36.6 - - /50.1 37.4 /60.5 44.7 - - Model B-1 Label Acc. Open Book REALM [20] DPR [26] 40.4 / 41.5 57.9/ - - - 40.7 46.8 41.1 50.6 SotA BART - - 15.1 19.7 49.8* 49.9* 38.2 41.6 76.8 64.0 81.1 RAG-Token RAG-Seq. 44.1 55.2/66.1 45.5 50.0 44.5 56.8/68.0 45.2 52.2 RAG-Tok. 17.3 22.2 RAG-Seq. 14.7 21.4 40.1 40.8 41.5 44.2 72.5 89.5'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 18}]}, {"cell_type": "code", "source": ["NarrativeText=[]\n", "for element in raw_pdf_elements2:\n", "  if \"unstructured.documents.elements.NarrativeText\" in str(type(element)):\n", "            NarrativeText.append(str(element))"], "metadata": {"id": "Q_HPBYJUmki8"}, "execution_count": 19, "outputs": []}, {"cell_type": "code", "source": ["ListItem=[]\n", "for element in raw_pdf_elements2:\n", "  if \"unstructured.documents.elements.ListItem\" in str(type(element)):\n", "            ListItem.append(str(element))"], "metadata": {"id": "BEQ3vkYjmnER"}, "execution_count": 20, "outputs": []}, {"cell_type": "code", "source": ["NarrativeText\n"], "metadata": {"id": "NqS75kwYnF84", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "d2e26583-21b4-4f68-b2ed-cf5057e22574"}, "execution_count": 21, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[\"<PERSON>'t, <PERSON>*,\",\n", " '<PERSON><PERSON>†, <PERSON><PERSON><PERSON>†, <PERSON>†, <PERSON><PERSON>†, <PERSON>†,',\n", " '<PERSON>†, <PERSON><PERSON><PERSON><PERSON>†, <PERSON>†‡, <PERSON>†‡, <PERSON><PERSON><PERSON>†',\n", " 'Large pre-trained language models have been shown to store factual knowledge in their parameters, and achieve state-of-the-art results when ﬁne-tuned on down- stream NLP tasks. However, their ability to access and precisely manipulate knowl- edge is still limited, and hence on knowledge-intensive tasks, their performance lags behind task-speciﬁc architectures. Additionally, providing provenance for their decisions and updating their world knowledge remain open research problems. Pre- trained models with a differentiable access mechanism to explicit non-parametric memory have so far been only investigated for extractive downstream tasks. We explore a general-purpose ﬁne-tuning recipe for retrieval-augmented generation (RAG) — models which combine pre-trained parametric and non-parametric mem- ory for language generation. We introduce RAG models where the parametric memory is a pre-trained seq2seq model and the non-parametric memory is a dense vector index of Wikipedia, accessed with a pre-trained neural retriever. We com- pare two RAG formulations, one which conditions on the same retrieved passages across the whole generated sequence, and another which can use different passages per token. We ﬁne-tune and evaluate our models on a wide range of knowledge- intensive NLP tasks and set the state of the art on three open domain QA tasks, outperforming parametric seq2seq models and task-speciﬁc retrieve-and-extract architectures. For language generation tasks, we ﬁnd that RAG models generate more speciﬁc, diverse and factual language than a state-of-the-art parametric-only seq2seq baseline.',\n", " 'Pre-trained neural language models have been shown to learn a substantial amount of in-depth knowl- edge from data [47]. They can do so without any access to an external memory, as a parameterized implicit knowledge base [51, 52]. While this development is exciting, such models do have down- sides: They cannot easily expand or revise their memory, can’t straightforwardly provide insight into their predictions, and may produce “hallucinations” [38]. Hybrid models that combine parametric memory with non-parametric (i.e., retrieval-based) memories [20, 26, 48] can address some of these issues because knowledge can be directly revised and expanded, and accessed knowledge can be inspected and interpreted. REALM [20] and ORQA [31], two recently introduced models that combine masked language models [8] with a differentiable retriever, have shown promising results,',\n", " 'Figure 1: Overview of our approach. We combine a pre-trained retriever (Query Encoder + Document Index) with a pre-trained seq2seq model (Generator) and ﬁne-tune end-to-end. For query x, we use Maximum Inner Product Search (MIPS) to ﬁnd the top-K documents zi. For ﬁnal prediction y, we treat z as a latent variable and marginalize over seq2seq predictions given different documents.',\n", " 'but have only explored open-domain extractive question answering. Here, we bring hybrid parametric and non-parametric memory to the “workhorse of NLP,” i.e. sequence-to-sequence (seq2seq) models.',\n", " 'We endow pre-trained, parametric-memory generation models with a non-parametric memory through a general-purpose ﬁne-tuning approach which we refer to as retrieval-augmented generation (RAG). We build RAG models where the parametric memory is a pre-trained seq2seq transformer, and the non-parametric memory is a dense vector index of Wikipedia, accessed with a pre-trained neural retriever. We combine these components in a probabilistic model trained end-to-end (Fig. 1). The retriever (Dense Passage Retriever [26], henceforth DPR) provides latent documents conditioned on the input, and the seq2seq model (BART [32]) then conditions on these latent documents together with the input to generate the output. We marginalize the latent documents with a top-K approximation, either on a per-output basis (assuming the same document is responsible for all tokens) or a per-token basis (where different documents are responsible for different tokens). Like T5 [51] or BART, RAG can be ﬁne-tuned on any seq2seq task, whereby both the generator and retriever are jointly learned.',\n", " 'There has been extensive previous work proposing architectures to enrich systems with non-parametric memory which are trained from scratch for speciﬁc tasks, e.g. memory networks [64, 55], stack- augmented networks [25] and memory layers [30]. In contrast, we explore a setting where both parametric and non-parametric memory components are pre-trained and pre-loaded with extensive knowledge. Crucially, by using pre-trained access mechanisms, the ability to access knowledge is present without additional training.',\n", " 'Our results highlight the beneﬁts of combining parametric and non-parametric memory with genera- tion for knowledge-intensive tasks—tasks that humans could not reasonably be expected to perform without access to an external knowledge source. Our RAG models achieve state-of-the-art results on open Natural Questions [29], WebQuestions [3] and CuratedTrec [2] and strongly outperform recent approaches that use specialised pre-training objectives on TriviaQA [24]. Despite these being extractive tasks, we ﬁnd that unconstrained generation outperforms previous extractive approaches. For knowledge-intensive generation, we experiment with MS-MARCO [1] and Jeopardy question generation, and we ﬁnd that our models generate responses that are more factual, speciﬁc, and diverse than a BART baseline. For FEVER [56] fact veriﬁcation, we achieve results within 4.3% of state-of-the-art pipeline models which use strong retrieval supervision. Finally, we demonstrate that the non-parametric memory can be replaced to update the models’ knowledge as the world changes.1',\n", " 'We explore RAG models, which use the input sequence x to retrieve text documents z and use them as additional context when generating the target sequence y. As shown in Figure 1, our models leverage two components: (i) a retriever pη(z|x) with parameters η that returns (top-K truncated) distributions over text passages given a query x and (ii) a generator pθ(yi|x, z, y1:i−1) parametrized',\n", " '1Code to run experiments with RAG has been open-sourced as part of the HuggingFace Transform- ers Library [66] and can be found at https://github.com/huggingface/transformers/blob/master/ examples/rag/. An interactive demo of RAG models can be found at https://huggingface.co/rag/',\n", " 'by θ that generates a current token based on a context of the previous i − 1 tokens y1:i−1, the original input x and a retrieved passage z.',\n", " 'To train the retriever and generator end-to-end, we treat the retrieved document as a latent variable. We propose two models that marginalize over the latent documents in different ways to produce a distribution over generated text. In one approach, RAG-Sequence, the model uses the same document to predict each target token. The second approach, RAG-Token, can predict each target token based on a different document. In the following, we formally introduce both models and then describe the pη and pθ components, as well as the training and decoding procedure.',\n", " 'RAG-Sequence Model The RAG-Sequence model uses the same retrieved document to generate the complete sequence. Technically, it treats the retrieved document as a single latent variable that is marginalized to get the seq2seq probability p(y|x) via a top-K approximation. Concretely, the top K documents are retrieved using the retriever, and the generator produces the output sequence probability for each document, which are then marginalized,',\n", " 'RAG-Token Model In the RAG-Token model we can draw a different latent document for each target token and marginalize accordingly. This allows the generator to choose content from several documents when producing an answer. Concretely, the top K documents are retrieved using the retriever, and then the generator produces a distribution for the next output token for each document, before marginalizing, and repeating the process with the following output token, Formally, we deﬁne:',\n", " 'Finally, we note that RAG can be used for sequence classiﬁcation tasks by considering the target class as a target sequence of length one, in which case RAG-Sequence and RAG-Token are equivalent.',\n", " 'The retrieval component pη(z|x) is based on DPR [26]. DPR follows a bi-encoder architecture:',\n", " 'where d(z) is a dense representation of a document produced by a BERTBASE document encoder [8], and q(x) a query representation produced by a query encoder, also based on BERTBASE. Calculating top-k(pη(·|x)), the list of k documents z with highest prior probability pη(z|x), is a Maximum Inner Product Search (MIPS) problem, which can be approximately solved in sub-linear time [23]. We use a pre-trained bi-encoder from DPR to initialize our retriever and to build the document index. This retriever was trained to retrieve documents which contain answers to TriviaQA [24] questions and Natural Questions [29]. We refer to the document index as the non-parametric memory.',\n", " 'The generator component pθ(yi|x, z, y1:i−1) could be modelled using any encoder-decoder. We use BART-large [32], a pre-trained seq2seq transformer [58] with 400M parameters. To combine the input x with the retrieved content z when generating from BART, we simply concatenate them. BART was pre-trained using a denoising objective and a variety of different noising functions. It has obtained state-of-the-art results on a diverse set of generation tasks and outperforms comparably-sized T5 models [32]. We refer to the BART generator parameters θ as the parametric memory henceforth.',\n", " 'We jointly train the retriever and generator components without any direct supervision on what document should be retrieved. Given a ﬁne-tuning training corpus of input/output pairs (xj, yj), we',\n", " 'minimize the negative marginal log-likelihood of each target, ar — log p(y;|a;) using stochastic gradient descent with <PERSON> [28]. Updating the document encoder BERT, during training is costly as it requires the document index to be periodically updated as REALM does during pre-training [20]. We do not find this step necessary for strong performance, and keep the document encoder (and index) fixed, only fine-tuning the query encoder BERT, and the BART generator.',\n", " 'At test time, RAG-Sequence and RAG-Token require different ways to approximate arg maxy p(y|x).',\n", " 'RAG-Token The RAG-Token model can be seen as a standard, autoregressive seq2seq genera- tor with transition probability: pj (yi|z, yii—1) = zetop-k(p(-|2)) Pn (zil@)po(yil@, Zi, Y1e—1) To decode, we can plug Po(yi |x, y1i—1) into a standard beam decoder.',\n", " 'RAG-Sequence For RAG-Sequence, the likelihood p(y|x) does not break into a conventional per- token likelihood, hence we cannot solve it with a single beam search. Instead, we run beam search for each document z, scoring each hypothesis using pθ(yi|x, z, y1:i−1). This yields a set of hypotheses Y , some of which may not have appeared in the beams of all documents. To estimate the probability of an hypothesis y we run an additional forward pass for each document z for which y does not appear in the beam, multiply generator probability with pη(z|x) and then sum the probabilities across beams for the marginals. We refer to this decoding procedure as “Thorough Decoding.” For longer output sequences, |Y | can become large, requiring many forward passes. For more efﬁcient decoding, we can make a further approximation that pθ(y|x, zi) ≈ 0 where y was not generated during beam search from x, zi. This avoids the need to run additional forward passes once the candidate set Y has been generated. We refer to this decoding procedure as “Fast Decoding.”',\n", " 'We experiment with RAG in a wide range of knowledge-intensive tasks. For all experiments, we use a single Wikipedia dump for our non-parametric knowledge source. Following <PERSON> et al. [31] and <PERSON><PERSON><PERSON><PERSON> et al. [26], we use the December 2018 dump. Each Wikipedia article is split into disjoint 100-word chunks, to make a total of 21M documents. We use the document encoder to compute an embedding for each document, and build a single MIPS index using FAISS [23] with a Hierarchical Navigable Small World approximation for fast retrieval [37]. During training, we retrieve the top k documents for each query. We consider k ∈ {5, 10} for training and set k for test time using dev data. We now discuss experimental details for each task.',\n", " 'Open-domain question answering (QA) is an important real-world application and common testbed for knowledge-intensive tasks [20]. We treat questions and answers as input-output text pairs (x, y) and train RAG by directly minimizing the negative log-likelihood of answers. We compare RAG to the popular extractive QA paradigm [5, 7, 31, 26], where answers are extracted spans from retrieved documents, relying primarily on non-parametric knowledge. We also compare to “Closed-Book QA” approaches [52], which, like RAG, generate answers, but which do not exploit retrieval, instead relying purely on parametric knowledge. We consider four popular open-domain QA datasets: Natural Questions (NQ) [29], TriviaQA (TQA) [24]. WebQuestions (WQ) [3] and CuratedTrec (CT) [2]. As CT and WQ are small, we follow DPR [26] by initializing CT and WQ models with our NQ RAG model. We use the same train/dev/test splits as prior work [31, 26] and report Exact Match (EM) scores. For TQA, to compare with T5 [52], we also evaluate on the TQA Wiki test set.',\n", " 'RAG models can go beyond simple extractive QA and answer questions with free-form, abstractive text generation. To test RAG’s natural language generation (NLG) in a knowledge-intensive setting, we use the MSMARCO NLG task v2.1 [43]. The task consists of questions, ten gold passages retrieved from a search engine for each question, and a full sentence answer annotated from the retrieved passages. We do not use the supplied passages, only the questions and answers, to treat',\n", " 'MSMARCO as an open-domain abstractive QA task. MSMARCO has some questions that cannot be answered in a way that matches the reference answer without access to the gold passages, such as “What is the weather in Volcano, CA?” so performance will be lower without using gold passages. We also note that some MSMARCO questions cannot be answered using Wikipedia alone. Here, RAG can rely on parametric knowledge to generate reasonable responses.',\n", " 'To evaluate RAG’s generation abilities in a non-QA setting, we study open-domain question gen- eration. Rather than use questions from standard open-domain QA tasks, which typically consist of short, simple questions, we propose the more demanding task of generating Jeopardy questions. Jeopardy is an unusual format that consists of trying to guess an entity from a fact about that entity. For example, “The World Cup” is the answer to the question “In 1986 Mexico scored as the ﬁrst country to host this international sports competition twice.” As Jeopardy questions are precise, factual statements, generating Jeopardy questions conditioned on their answer entities constitutes a challenging knowledge-intensive generation task.',\n", " 'We use the splits from SearchQA [10], with 100K train, 14K dev, and 27K test examples. As this is a new task, we train a BART model for comparison. Following [67], we evaluate using the SQuAD-tuned Q-BLEU-1 metric [42]. Q-BLEU is a variant of BLEU with a higher weight for matching entities and has higher correlation with human judgment for question generation than standard metrics. We also perform two human evaluations, one to assess generation factuality, and one for speciﬁcity. We deﬁne factuality as whether a statement can be corroborated by trusted external sources, and speciﬁcity as high mutual dependence between the input and output [33]. We follow best practice and use pairwise comparative evaluation [34]. Evaluators are shown an answer and two generated questions, one from BART and one from RAG. They are then asked to pick one of four options—quuestion A is better, question B is better, both are good, or neither is good.',\n", " 'FEVER [56] requires classifying whether a natural language claim is supported or refuted by Wikipedia, or whether there is not enough information to decide. The task requires retrieving evidence from Wikipedia relating to the claim and then reasoning over this evidence to classify whether the claim is true, false, or unveriﬁable from Wikipedia alone. FEVER is a retrieval problem coupled with an challenging entailment reasoning task. It also provides an appropriate testbed for exploring the RAG models’ ability to handle classiﬁcation rather than generation. We map FEVER class labels (supports, refutes, or not enough info) to single output tokens and directly train with claim-class pairs. Crucially, unlike most other approaches to FEVER, we do not use supervision on retrieved evidence. In many real-world applications, retrieval supervision signals aren’t available, and models that do not require such supervision will be applicable to a wider range of tasks. We explore two variants: the standard 3-way classiﬁcation task (supports/refutes/not enough info) and the 2-way (supports/refutes) task studied in Thorne and Vlachos [57]. In both cases we report label accuracy.',\n", " 'Table 1 shows results for RAG along with state-of-the-art models. On all four open-domain QA tasks, RAG sets a new state of the art (only on the T5-comparable split for TQA). RAG combines the generation ﬂexibility of the “closed-book” (parametric only) approaches and the performance of \"open-book\" retrieval-based approaches. Unlike REALM and T5+SSM, RAG enjoys strong results without expensive, specialized “salient span masking” pre-training [20]. It is worth noting that RAG’s retriever is initialized using DPR’s retriever, which uses retrieval supervision on Natural Questions and TriviaQA. RAG compares favourably to the DPR QA system, which uses a BERT-based “cross- encoder” to re-rank documents, along with an extractive reader. RA<PERSON> demonstrates that neither a re-ranker nor extractive reader is necessary for state-of-the-art performance.',\n", " 'There are several advantages to generating answers even when it is possible to extract them. Docu- ments with clues about the answer but do not contain the answer verbatim can still contribute towards a correct answer being generated, which is not possible with standard extractive approaches, leading',\n", " 'to more effective marginalization over documents. Furthermore, RAG can generate correct answers even when the correct answer is not in any retrieved document, achieving 11.8% accuracy in such cases for NQ, where an extractive model would score 0%.',\n", " 'As shown in Table 2, RAG-Sequence outperforms BART on Open MS-MARCO NLG by 2.6 Bleu points and 2.6 Rouge-L points. RAG approaches state-of-the-art model performance, which is impressive given that (i) those models access gold passages with speciﬁc information required to generate the reference answer , (ii) many questions are unanswerable without the gold passages, and (iii) not all questions are answerable from Wikipedia alone. Table 3 shows some generated answers from our models. Qualitatively, we ﬁnd that RAG models hallucinate less and generate factually correct text more often than BART. Later, we also show that RAG generations are more diverse than BART generations (see §4.5).',\n", " 'Table 2 shows that RAG-Token performs better than RAG-Sequence on Jeopardy question generation, with both models outperforming BART on Q-BLEU-1. 4 shows human evaluation results, over 452 pairs of generations from BART and RAG-Token. Evaluators indicated that BART was more factual than RAG in only 7.1% of cases, while RAG was more factual in 42.7% of cases, and both RAG and BART were factual in a further 17% of cases, clearly demonstrating the effectiveness of RAG on the task over a state-of-the-art generation model. Evaluators also ﬁnd RAG generations to be more speciﬁc by a large margin. Table 3 shows typical generations from each model.',\n", " 'Jeopardy questions often contain two separate pieces of information, and RAG-Token may perform best because it can generate responses that combine content from several documents. Figure 2 shows an example. When generating “Sun”, the posterior is high for document 2 which mentions “The Sun Also Rises”. Similarly, document 1 dominates the posterior when “A Farewell to Arms” is generated. Intriguingly, after the ﬁrst token of each book is generated, the document posterior ﬂattens. This observation suggests that the generator can complete the titles without depending on speciﬁc documents. In other words, the model’s parametric knowledge is sufﬁcient to complete the titles. We ﬁnd evidence for this hypothesis by feeding the BART-only baseline with the partial decoding \"The Sun. BART completes the generation \"The Sun Also Rises\" is a novel by this author of \"The Sun Also Rises\" indicating the title \"The Sun Also Rises\" is stored in BART’s parameters. Similarly, BART will complete the partial decoding \"The Sun Also Rises\" is a novel by this author of \"A with \"The Sun Also Rises\" is a novel by this author of \"A Farewell to Arms\". This example shows how parametric and non-parametric memories work together—the non-parametric component helps to guide the generation, drawing out speciﬁc knowledge stored in the parametric memory.',\n", " 'Table 2 shows our results on FEVER. For 3-way classiﬁcation, RAG scores are within 4.3% of state-of-the-art models, which are complex pipeline systems with domain-speciﬁc architectures and substantial engineering, trained using intermediate retrieval supervision, which RAG does not require.',\n", " 'Figure 2: RAG-Token document posterior p(zi|x, yi, y−i) for each generated token for input “Hem- ingway\" for Jeopardy generation with 5 retrieved documents. The posterior for document 1 is high when generating “A Farewell to Arms\" and for document 2 when generating “The Sun Also Rises\".',\n", " 'Table 3: Examples from generation tasks. RAG models generate more speciﬁc and factually accurate responses. ‘?’ indicates factually incorrect responses, * indicates partially correct responses.',\n", " 'For 2-way classiﬁcation, we compare against <PERSON> and <PERSON><PERSON><PERSON><PERSON> [57], who train RoBERTa [35] to classify the claim as true or false given the gold evidence sentence. RAG achieves an accuracy within 2.7% of this model, despite being supplied with only the claim and retrieving its own evidence. We also analyze whether documents retrieved by RAG correspond to documents annotated as gold evidence in FEVER. We calculate the overlap in article titles between the top k documents retrieved by RAG and gold evidence annotations. We ﬁnd that the top retrieved document is from a gold article in 71% of cases, and a gold article is present in the top 10 retrieved articles in 90% of cases.',\n", " 'Generation Diversity Section 4.3 shows that RAG models are more factual and speciﬁc than BART for Jeopardy question generation. Following recent work on diversity-promoting decoding [33, 59, 39], we also investigate generation diversity by calculating the ratio of distinct ngrams to total ngrams generated by different models. Table 5 shows that RAG-Sequence’s generations are more diverse than RAG-Token’s, and both are signiﬁcantly more diverse than BART without needing any diversity-promoting decoding.',\n", " 'Retrieval Ablations A key feature of RAG is learning to retrieve relevant information for the task. To assess the effectiveness of the retrieval mechanism, we run ablations where we freeze the retriever during training. As shown in Table 6, learned retrieval improves results for all tasks.',\n", " 'We compare RAG’s dense retriever to a word overlap-based BM25 retriever [53]. Here, we replace RAG’s retriever with a ﬁxed BM25 system, and use BM25 retrieval scores as logits when calculating p(z|x). Table 6 shows the results. For FEVER, BM25 performs best, perhaps since FEVER claims are heavily entity-centric and thus well-suited for word overlap-based retrieval. Differentiable retrieval improves results on all other tasks, especially for Open-Domain QA, where it is crucial.',\n", " 'Index hot-swapping An advantage of non-parametric memory models like RAG is that knowledge can be easily updated at test time. Parametric-only models like T5 or BART need further training to update their behavior as the world changes. To demonstrate, we build an index using the DrQA [5] Wikipedia dump from December 2016 and compare outputs from RAG using this index to the newer index from our main results (December 2018). We prepare a list of 82 world leaders who had changed',\n", " 'Table 4: Human assessments for the Jeopardy Question Generation Task.',\n", " 'between these dates and use a template “Who is {position}?” (e.g. “Who is the President of Peru?”) to query our NQ RAG model with each index. RAG answers 70% correctly using the 2016 index for 2016 world leaders and 68% using the 2018 index for 2018 world leaders. Accuracy with mismatched indices is low (12% with the 2018 index and 2016 leaders, 4% with the 2016 index and 2018 leaders). This shows we can update RAG’s world knowledge by simply replacing its non-parametric memory.',\n", " 'Effect of Retrieving more documents Models are trained with either 5 or 10 retrieved latent documents, and we do not observe signiﬁcant differences in performance between them. We have the ﬂexibility to adjust the number of retrieved documents at test time, which can affect performance and runtime. Figure 3 (left) shows that retrieving more documents at test time monotonically improves Open-domain QA results for RAG-Sequence, but performance peaks for RAG-Token at 10 retrieved documents. Figure 3 (right) shows that retrieving more documents leads to higher Rouge-L for RAG-Token at the expense of Bleu-1, but the effect is less pronounced for RAG-Sequence.',\n", " 'Single-Task Retrieval Prior work has shown that retrieval improves performance across a variety of NLP tasks when considered in isolation. Such tasks include open-domain question answering [5, 29], fact checking [56], fact completion [48], long-form question answering [12], Wikipedia article generation [36], dialogue [41, 65, 9, 13], translation [17], and language modeling [19, 27]. Our work uniﬁes previous successes in incorporating retrieval into individual tasks, showing that a single retrieval-based architecture is capable of achieving strong performance across several tasks.',\n", " 'General-Purpose Architectures for NLP Prior work on general-purpose architectures for NLP tasks has shown great success without the use of retrieval. A single, pre-trained language model has been shown to achieve strong performance on various classiﬁcation tasks in the GLUE bench- marks [60, 61] after ﬁne-tuning [49, 8]. GPT-2 [50] later showed that a single, left-to-right, pre-trained language model could achieve strong performance across both discriminative and generative tasks. For further improvement, BART [32] and T5 [51, 52] propose a single, pre-trained encoder-decoder model that leverages bi-directional attention to achieve stronger performance on discriminative and generative tasks. Our work aims to expand the space of possible tasks with a single, uniﬁed architecture, by learning a retrieval module to augment pre-trained, generative language models.',\n", " 'Learned Retrieval There is signiﬁcant work on learning to retrieve documents in information retrieval, more recently with pre-trained, neural language models [44, 26] similar to ours. Some work optimizes the retrieval module to aid in a speciﬁc, downstream task such as question answering, using search [46], reinforcement learning [6, 63, 62], or a latent variable approach [31, 20] as in our work. These successes leverage different retrieval-based architectures and optimization techniques to achieve strong performance on a single task, while we show that a single retrieval-based architecture can be ﬁne-tuned for strong performance on a variety of tasks.',\n", " 'Memory-based Architectures Our document index can be seen as a large external memory for neural networks to attend to, analogous to memory networks [64, 55]. Concurrent work [14] learns to retrieve a trained embedding for each entity in the input, rather than to retrieve raw text as in our work. Other work improves the ability of dialog models to generate factual text by attending over fact embeddings [15, 13]. A key feature of our memory is that it is comprised of raw text rather distributed representations, which makes the memory both (i) human-readable, lending a form of interpretability to our model, and (ii) human-writable, enabling us to dynamically update the model’s memory by editing the document index. This approach has also been used in knowledge-intensive dialog, where generators have been conditioned on retrieved text directly, albeit obtained via TF-IDF rather than end-to-end learnt retrieval [9].',\n", " 'Retrieve-and-Edit approaches Our method shares some similarities with retrieve-and-edit style approaches, where a similar training input-output pair is retrieved for a given input, and then edited to provide a ﬁnal output. These approaches have proved successful in a number of domains including Machine Translation [18, 22] and Semantic Parsing [21]. Our approach does have several differences, including less of emphasis on lightly editing a retrieved item, but on aggregating content from several pieces of retrieved content, as well as learning latent retrieval, and retrieving evidence documents rather than related training pairs. This said, RAG techniques may work well in these settings, and could represent promising future work.',\n", " 'In this work, we presented hybrid generation models with access to parametric and non-parametric memory. We showed that our RAG models obtain state of the art results on open-domain QA. We found that people prefer RAG’s generation over purely parametric BART, ﬁnding RAG more factual and speciﬁc. We conducted an thorough investigation of the learned retrieval component, validating its effectiveness, and we illustrated how the retrieval index can be hot-swapped to update the model without requiring any retraining. In future work, it may be fruitful to investigate if the two components can be jointly pre-trained from scratch, either with a denoising objective similar to BART or some another objective. Our work opens up new research directions on how parametric and non-parametric memories interact and how to most effectively combine them, showing promise in being applied to a wide variety of NLP tasks.',\n", " 'This work offers several positive societal beneﬁts over previous work: the fact that it is more strongly grounded in real factual knowledge (in this case Wikipedia) makes it “hallucinate” less with generations that are more factual, and offers more control and interpretability. RAG could be employed in a wide variety of scenarios with direct beneﬁt to society, for example by endowing it with a medical index and asking it open-domain questions on that topic, or by helping people be more effective at their jobs.',\n", " 'With these advantages also come potential downsides: Wikipedia, or any potential external knowledge source, will probably never be entirely factual and completely devoid of bias. Since RAG can be employed as a language model, similar concerns as for GPT-2 [50] are valid here, although arguably to a lesser extent, including that it might be used to generate abuse, faked or misleading content in the news or on social media; to impersonate others; or to automate the production of spam/phishing content [54]. Advanced language models may also lead to the automation of various jobs in the coming decades [16]. In order to mitigate these risks, AI systems could be employed to ﬁght against misleading content and automated spam/phishing.',\n", " 'The authors would like to thank the reviewers for their thoughtful and constructive feedback on this paper, as well as HuggingFace for their help in open-sourcing code to run RAG models. The authors would also like to thank <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> for productive discussions and advice. EP thanks supports from the NSF Graduate Research Fellowship. PL is supported by the FAIR PhD program.',\n", " 'for Computational Linguistics, pages 6086–6096, Florence, Italy, July 2019. Association for Computational Linguistics. doi: 10.18653/v1/P19-1612. URL https://www.aclweb.org/ anthology/P19-1612.',\n", " 'approaches 2016 co-located with the 30th Annual Conference on Neural Information Processing Systems (NIPS 2016), Barcelona, Spain, December 9, 2016, volume 1773 of CEUR Workshop Proceedings. CEUR-WS.org, 2016. URL http://ceur-ws.org/Vol-1773/CoCoNIPS_ 2016_paper9.pdf.',\n", " 'For Open-domain QA we report test numbers using 15 retrieved documents for RAG-Token models. For RAG-Sequence models, we report test results using 50 retrieved documents, and we use the Thorough Decoding approach since answers are generally short. We use greedy decoding for QA as we did not ﬁnd beam search improved results. For Open-MSMarco and Jeopardy question generation, we report test numbers using ten retrieved documents for both RAG-Token and RAG-Sequence, and we also train a BART-large model as a baseline. We use a beam size of four, and use the Fast Decoding approach for RAG-Sequence models, as Thorough Decoding did not improve performance.',\n", " 'Figure 4: Annotation interface for human evaluation of factuality. A pop-out for detailed instructions and a worked example appear when clicking \"view tool guide\".',\n", " 'Figure 4 shows the user interface for human evaluation. To avoid any biases for screen position, which model corresponded to sentence A and sentence B was randomly selected for each example. Annotators were encouraged to research the topic using the internet, and were given detailed instruc- tions and worked examples in a full instructions tab. We included some gold sentences in order to assess the accuracy of the annotators. Two annotators did not perform well on these examples and their annotations were removed from the results.',\n", " 'We train all RAG models and BART baselines using Fairseq [45].2 We train with mixed precision ﬂoating point arithmetic [40], distributing training across 8, 32GB NVIDIA V100 GPUs, though training and inference can be run on one GPU. We ﬁnd that doing Maximum Inner Product Search with FAISS is sufﬁciently fast on CPU, so we store document index vectors on CPU, requiring ∼ 100 GB of CPU memory for all of Wikipedia. After submission, We have ported our code to HuggingFace Transformers [66]3, which achieves equivalent performance to the previous version but is a cleaner and easier to use implementation. This version is also open-sourced. We also compress the document index using FAISS’s compression tools, reducing the CPU memory requirement to 36GB. Scripts to run experiments with RAG can be found at https://github.com/huggingface/transformers/ blob/master/examples/rag/README.md and an interactive demo of a RAG model can be found at https://huggingface.co/rag/',\n", " '2https://github.com/pytorch/fairseq 3https://github.com/huggingface/transformers',\n", " 'For open-domain QA, multiple answer annotations are often available for a given question. These answer annotations are exploited by extractive models during training as typically all the answer annotations are used to ﬁnd matches within documents when preparing training data. For RAG, we also make use of multiple annotation examples for Natural Questions and WebQuestions by training the model with each (q, a) pair separately, leading to a small increase in accuracy. For TriviaQA, there are often many valid answers to a given question, some of which are not suitable training targets, such as emoji or spelling variants. For TriviaQA, we ﬁlter out answer candidates if they do not occur in top 1000 documents for the query.',\n", " 'CuratedTrec preprocessing The answers for CuratedTrec are given in the form of regular expres- sions, which has been suggested as a reason why it is unsuitable for answer-generation models [20]. To overcome this, we use a pre-processing step where we ﬁrst retrieve the top 1000 documents for each query, and use the answer that most frequently matches the regex pattern as the supervision target. If no matches are found, we resort to a simple heuristic: generate all possible permutations for each regex, replacing non-deterministic symbols in the regex nested tree structure with a whitespace.',\n", " 'TriviaQA Evaluation setups The open-domain QA community customarily uses public develop- ment datasets as test datasets, as test data for QA datasets is often restricted and dedicated to reading compehension purposes. We report our results using the datasets splits used in DPR [26], which are consistent with common practice in Open-domain QA. For TriviaQA, this test dataset is the public TriviaQA Web Development split. <PERSON> et al. [52] used the TriviaQA ofﬁcial Wikipedia test set instead. <PERSON><PERSON><PERSON><PERSON> et al. [14] follow this convention in order to compare with <PERSON> et al. [52] (See appendix of [14]). We report results on both test sets to enable fair comparison to both approaches. We ﬁnd that our performance is much higher using the ofﬁcial Wiki test set, rather than the more conventional open-domain test set, which we attribute to the ofﬁcial Wiki test set questions being simpler to answer from Wikipedia.',\n", " 'For FEVER classiﬁcation, we follow the practice from [32], and ﬁrst re-generate the claim, and then classify using the representation of the ﬁnal hidden state, before ﬁnally marginalizing across documents to obtain the class probabilities. The FEVER task traditionally has two sub-tasks. The ﬁrst is to classify the claim as either \"Supported\", \"Refuted\" or \"Not Enough Info\", which is the task we explore in the main paper. FEVER’s other sub-task involves extracting sentences from Wikipedia as evidence supporting the classiﬁcation prediction. As FEVER uses a different Wikipedia dump to us, directly tackling this task is not straightforward. We hope to address this in future work.',\n", " 'We experimented with adding \"Null document\" mechanism to RAG, similar to REALM [20] in order to model cases where no useful information could be retrieved for a given input. Here, if k documents were retrieved, we would additionally \"retrieve\" an empty document and predict a logit for the null document, before marginalizing over k + 1 predictions. We explored modelling this null document logit by learning (i) a document embedding for the null document, (ii) a static learnt bias term, or (iii) a neural network to predict the logit. We did not ﬁnd that these improved performance, so in the interests of simplicity, we omit them. For Open MS-MARCO, where useful retrieved documents cannot always be retrieved, we observe that the model learns to always retrieve a particular set of documents for questions that are less likely to beneﬁt from retrieval, suggesting that null document mechanisms may not be necessary for RAG.',\n", " 'Our RAG models contain the trainable parameters for the BERT-base query and document encoder of DPR, with 110M parameters each (although we do not train the document encoder ourselves) and 406M trainable parameters from BART-large, 406M parameters, making a total of 626M trainable',\n", " 'parameters. The best performing \"closed-book\" (parametric only) open-domain QA model is T5-11B with 11 Billion trainable parameters. The T5 model with the closest number of parameters to our models is T5-large (770M parameters), which achieves a score of 28.9 EM on Natural Questions [52], substantially below the 44.5 that RAG-Sequence achieves, indicating that hybrid parametric/non- parametric models require far fewer trainable parameters for strong open-domain QA performance. The non-parametric memory index does not consist of trainable parameters, but does consists of 21M 728 dimensional vectors, consisting of 15.3B values. These can be easily be stored at 8-bit ﬂoating point precision to manage memory and disk footprints.',\n", " 'In preliminary experiments, we observed that for some tasks such as story generation [11], the retrieval component would “collapse” and learn to retrieve the same documents regardless of the input. In these cases, once retrieval had collapsed, the generator would learn to ignore the documents, and the RAG model would perform equivalently to BART. The collapse could be due to a less-explicit requirement for factual knowledge in some tasks, or the longer target sequences, which could result in less informative gradients for the retriever. <PERSON> et al. [46] also found spurious retrieval results when optimizing a retrieval component in order to improve performance on downstream tasks.',\n", " 'The number of training, development and test datapoints in each of our datasets is shown in Table 7.']"]}, "metadata": {}, "execution_count": 21}]}, {"cell_type": "code", "source": ["ListItem"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AiKLLrmfv_D_", "outputId": "ef57d10a-4839-49e0-fa19-4deb17e0ebeb"}, "execution_count": 22, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['[1] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. MS MARCO: A Human Generated MAchine Reading COmprehension Dataset. arXiv:1611.09268 [cs], November 2016. URL http: //arxiv.org/abs/1611.09268. arXiv: 1611.09268.',\n", " '[2] <PERSON><PERSON> and <PERSON>y. Modeling of the question answering task in the yodaqa system. In International Conference of the Cross-Language Evaluation Forum for European Languages, pages 222–228. Springer, 2015. URL https://link.springer.com/chapter/10.1007% 2F978-3-319-24027-5_20.',\n", " '[3] <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Seman<PERSON> Parsing on Freebase from Question-Answer Pairs. In Proceedings of the 2013 Conference on Empirical Methods in Natural Language Processing, pages 1533–1544, Seattle, Washington, USA, October 2013. Association for Computational Linguistics. URL http://www.aclweb.org/anthology/ D13-1160.',\n", " '[4] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Palm: Pre-training an autoencod- ing&autoregressive language model for context-conditioned generation. ArXiv, abs/2004.07159, 2020. URL https://arxiv.org/abs/2004.07159.',\n", " '[5] <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Reading Wikipedia to Answer Open-Domain Questions. In Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers), pages 1870–1879, Vancouver, Canada, July 2017. Association for Computational Linguistics. doi: 10.18653/v1/P17-1171. URL https://www.aclweb.org/anthology/P17-1171.',\n", " '[6] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Coarse-to-ﬁne question answering for long documents. In Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers), pages 209–220, Vancouver, Canada, July 2017. Association for Computational Linguistics. doi: 10.18653/v1/P17-1020. URL https://www.aclweb.org/anthology/P17-1020.',\n", " '[7] <PERSON> and <PERSON>. Simple and Effective Multi-Paragraph Reading Compre- hension. arXiv:1710.10723 [cs], October 2017. URL http://arxiv.org/abs/1710.10723. arXiv: 1710.10723.',\n", " '[8] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding. In Proceedings of the 2019 Con- ference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies, Volume 1 (Long and Short Papers), pages 4171–4186, Minneapolis, Minnesota, June 2019. Association for Computational Linguistics. doi: 10.18653/v1/N19-1423. URL https://www.aclweb.org/anthology/N19-1423.',\n", " '[9] <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Wiz- ard of wikipedia: Knowledge-powered conversational agents. In International Conference on Learning Representations, 2019. URL https://openreview.net/forum?id=r1l73iRqKm.',\n", " '[10] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. SearchQA: A New Q&A Dataset Augmented with Context from a Search Engine. arXiv:1704.05179 [cs], April 2017. URL http://arxiv.org/abs/1704.05179. arXiv: 1704.05179.',\n", " '[11] <PERSON>, <PERSON>, and <PERSON><PERSON>. Hierarchical neural story generation. In Proceed- ings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers), pages 889–898, Melbourne, Australia, July 2018. Association for Computational Linguistics. doi: 10.18653/v1/P18-1082. URL https://www.aclweb.org/anthology/ P18-1082.',\n", " '[12] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. ELI5: Long form question answering. In Proceedings of the 57th Annual Meeting of the Association for Computational Linguistics, pages 3558–3567, Florence, Italy, July 2019. Association for Computational Linguistics. doi: 10.18653/v1/P19-1346. URL https://www.aclweb.org/ anthology/P19-1346.',\n", " '[13] <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Augmenting transformers with KNN-based composite memory, 2020. URL https://openreview.net/forum?id= H1gx1CNKPH.',\n", " '[14] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Entities as experts: Sparse memory access with entity supervision. ArXiv, abs/2004.07202, 2020. URL https://arxiv.org/abs/2004.07202.',\n", " '[15] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. A knowledge-grounded neural conversation model. In AAAI Conference on Artiﬁcial Intelligence, 2018. URL https://www.aaai.org/ocs/index.php/ AAAI/AAAI18/paper/view/16710.',\n", " '[16] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. When will AI exceed human performance? evidence from AI experts. CoRR, abs/1705.08807, 2017. URL http://arxiv.org/abs/1705.08807.',\n", " '[17] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Search engine guided neural In AAAI Conference on Artiﬁcial Intelligence, 2018. URL https: machine translation. //www.aaai.org/ocs/index.php/AAAI/AAAI18/paper/view/17282.',\n", " '[18] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Search engine guided neural machine translation. In 32nd AAAI Conference on Artiﬁcial Intelligence, AAAI 2018, 32nd AAAI Conference on Artiﬁcial Intelligence, AAAI 2018, pages 5133–5140. AAAI press, 2018. 32nd AAAI Conference on Artiﬁcial Intelligence, AAAI 2018 ; Conference date: 02-02-2018 Through 07-02-2018.',\n", " '[19] <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Generating sentences by editing prototypes. Transactions of the Association for Computational Linguistics, 6:437–450, 2018. doi: 10.1162/tacl_a_00030. URL https://www.aclweb.org/anthology/Q18-1031.',\n", " '[20] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. REALM: Retrieval-augmented language model pre-training. ArXiv, abs/2002.08909, 2020. URL https: //arxiv.org/abs/2002.08909.',\n", " 'A <PERSON> <PERSON>, retrieve-and-edit <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, ed- itors, Advances pages 10052– 10062. Curran Associates, URL http://papers.nips.cc/paper/ 8209-a-retrieve-and-edit-framework-for-predicting-structured-outputs. pdf.',\n", " '[22] <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Simple and effective retrieve- edit-rerank text generation. In Proceedings of the 58th Annual Meeting of the Association for Computational Linguistics, pages 2532–2538, Online, July 2020. Association for Computa- tional Linguistics. doi: 10.18653/v1/2020.acl-main.228. URL https://www.aclweb.org/ anthology/2020.acl-main.228.',\n", " '[23] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Billion-scale similarity search with gpus. arXiv preprint arXiv:1702.08734, 2017. URL https://arxiv.org/abs/1702.08734.',\n", " '[24] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. TriviaQA: A Large Scale Distantly Supervised Challenge Dataset for Reading Comprehension. In Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers), pages 1601–1611, Vancouver, Canada, July 2017. Association for Computational Linguistics. doi: 10.18653/v1/P17-1147. URL https://www.aclweb.org/anthology/P17-1147.',\n", " 'Inferring algorithmic patterns with stack- the 28th International Conference on augmented recurrent nets. Neural Information Processing Systems - Volume 1, NIPS’15, page 190–198, Cam- bridge, MA, USA, 2015. MIT Press. URL https://papers.nips.cc/paper/ 5857-inferring-algorithmic-patterns-with-stack-augmented-recurrent-nets.',\n", " '[26] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Dense passage retrieval for open-domain question answering. arXiv preprint arXiv:2004.04906, 2020. URL https://arxiv.org/abs/2004.04906.',\n", " '[27] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Generaliza- tion through memorization: Nearest neighbor language models. In International Conference on Learning Representations, 2020. URL https://openreview.net/forum?id=HklBjCEKvH.',\n", " '[28] <PERSON><PERSON><PERSON> and <PERSON>: A method for stochastic optimization. In <PERSON><PERSON><PERSON> and <PERSON><PERSON>, editors, 3rd International Conference on Learning Representations, ICLR 2015, San Diego, CA, USA, May 7-9, 2015, Conference Track Proceedings, 2015. URL http://arxiv.org/abs/1412.6980.',\n", " '[29] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Natural Questions: a Benchmark for Ques- the Association of Computational Lin- tion Answering Research. guistics, 2019. URL https://tomkwiat.users.x20web.corp.google.com/papers/ natural-questions/main-1455-kwiatkowski.pdf.',\n", " '[30] <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Large memory layers with product keys. In <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural In- formation Processing Systems 32, pages 8548–8559. Curran Associates, Inc., 2019. URL http: //papers.nips.cc/paper/9061-large-memory-layers-with-product-keys.pdf.',\n", " '[31] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Latent retrieval for weakly supervised open domain question answering. In Proceedings of the 57th Annual Meeting of the Association',\n", " '[32] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. BART: Denoising sequence-to-sequence pre-training for natural language generation, translation, and comprehension. arXiv preprint arXiv:1910.13461, 2019. URL https://arxiv.org/abs/1910.13461.',\n", " '[33] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A diversity-promoting objective function for neural conversation models. In Proceedings of the 2016 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies, pages 110–119, San Diego, California, June 2016. Association for Computational Linguistics. doi: 10.18653/v1/N16-1014. URL https://www.aclweb.org/anthology/ N16-1014.',\n", " '[34] <PERSON>, <PERSON>, and <PERSON>. Acute-eval: Improved dialogue evaluation with optimized questions and multi-turn comparisons. ArXiv, abs/1909.03087, 2019. URL https://arxiv.org/abs/1909.03087.',\n", " '[35] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Robust neural machine translation with joint textual and phonetic embedding. In Proceedings of the 57th Annual Meeting of the Association for Computational Linguistics, pages 3044–3049, Florence, Italy, July 2019. Association for Computational Linguistics. doi: 10.18653/v1/P19-1291. URL https://www.aclweb.org/anthology/P19-1291.',\n", " '[36] <PERSON>*, <PERSON>*, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Generating wikipedia by summarizing long sequences. In International Conference on Learning Representations, 2018. URL https://openreview.net/forum? id=Hyg0vbWC-.',\n", " '[37] <PERSON><PERSON> and <PERSON><PERSON> <PERSON><PERSON>. Efﬁcient and robust approximate nearest neighbor search using hierarchical navigable small world graphs. IEEE Transactions on Pattern Analysis and Machine Intelligence, 42:824–836, 2016. URL https://arxiv.org/abs/1603.09320.',\n", " '[38] <PERSON>. The next decade in ai: four steps towards robust artiﬁcial intelligence. arXiv preprint arXiv:2002.06177, 2020. URL https://arxiv.org/abs/2002.06177.',\n", " '[39] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. How decoding strategies affect the arXiv preprint arXiv:1911.03587, 2019. URL https: veriﬁability of generated text. //arxiv.org/abs/1911.03587.',\n", " '[40] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Mixed precision training. In ICLR, 2018. URL https://openreview.net/forum?id=r1gs9JgRZ.',\n", " '[41] <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Towards exploit- ing background knowledge for building conversation systems. In Proceedings of the 2018 Conference on Empirical Methods in Natural Language Processing, pages 2322–2332, Brus- sels, Belgium, October-November 2018. Association for Computational Linguistics. doi: 10.18653/v1/D18-1255. URL https://www.aclweb.org/anthology/D18-1255.',\n", " '[42] <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Towards a better metric for evaluating question generation systems. In Proceedings of the 2018 Conference on Empirical Methods in Natural Language Processing, pages 3950–3959, Brussels, Belgium, October-November 2018. Association for Computational Linguistics. doi: 10.18653/v1/D18-1429. URL https://www.aclweb.org/ anthology/D18-1429.',\n", " '[43] <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. MS MARCO: A human generated machine reading comprehension dataset. In <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> <PERSON><PERSON>, and <PERSON>, editors, Proceedings of the Workshop on Cognitive Computation: Integrating neural and symbolic',\n", " '[44] <PERSON> and <PERSON><PERSON><PERSON><PERSON>. Passage re-ranking with BERT. arXiv preprint arXiv:1901.04085, 2019. URL https://arxiv.org/abs/1901.04085.',\n", " '[45] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. fairseq: A fast, extensible toolkit for sequence modeling. In Proceedings of the 2019 Conference of the North American Chapter of the Association for Computational Linguistics (Demonstrations), pages 48–53, Minneapolis, Minnesota, June 2019. Association for Computational Linguistics. doi: 10.18653/v1/N19-4009. URL https://www.aclweb. org/anthology/N19-4009.',\n", " '[46] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Finding generalizable evidence by learning to convince q&a models. In Proceedings of the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th International Joint Conference on Natural Language Processing (EMNLP-IJCNLP), pages 2402–2411, Hong Kong, China, November 2019. Association for Computational Linguistics. doi: 10.18653/v1/D19-1244. URL https://www.aclweb.org/anthology/D19-1244.',\n", " '[47] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Language models as knowledge bases? In Proceedings of the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th International Joint Conference on Natural Language Processing (EMNLP-IJCNLP), pages 2463–2473, Hong Kong, China, November 2019. Association for Computational Linguistics. doi: 10.18653/v1/ D19-1250. URL https://www.aclweb.org/anthology/D19-1250.',\n", " '[48] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. How context affects language models’ factual predictions. In Automated Knowledge Base Construction, 2020. URL https://openreview.net/forum? id=025X0zPfn.',\n", " '[49] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. proving Language Understanding https://s3-us-west-2.amazonaws.com/openai-assets/research-covers/ language-unsupervised/language_understanding_paper.pdf. by Generative Pre-Training, 2018. Im- URL',\n", " '[50] <PERSON>, Sutskever. https://d4mucfpksywv.cloudfront.net/better-language-models/language_ models_are_unsupervised_multitask_learners.pdf.',\n", " '[51] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Exploring the limits of transfer learning with a uniﬁed text-to-text transformer. arXiv e-prints, 2019. URL https://arxiv.org/abs/1910.10683.',\n", " '[52] <PERSON>, <PERSON>, and <PERSON><PERSON>. How much knowledge can you pack into the parameters of a language model? arXiv e-prints, 2020. URL https://arxiv.org/abs/ 2002.08910.',\n", " '[53] <PERSON> and <PERSON>. The probabilistic relevance framework: Bm25 and beyond. Found. Trends Inf. Retr., 3(4):333–389, April 2009. ISSN 1554-0669. doi: 10.1561/ 1500000019. URL https://doi.org/10.1561/1500000019.',\n", " '[54] <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Release strategies and the social impacts of language models. ArXiv, abs/1908.09203, 2019.',\n", " '[55] <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. End-to-end memory net- works. In <PERSON>. <PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural Information Processing Systems 28, pages 2440–2448. Curran Associates, Inc., 2015. URL http://papers.nips.cc/paper/5846-end-to-end-memory-networks.pdf.',\n", " '[56] <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. FEVER: a large-scale dataset for fact extraction and VERiﬁcation. In Proceedings of the 2018 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies, Volume 1 (Long Papers), pages 809–819, New Orleans, Louisiana, June 2018. Association for Computational Linguistics. doi: 10.18653/v1/N18-1074. URL https://www.aclweb.org/anthology/N18-1074.',\n", " '[57] <PERSON> and <PERSON>. Avoiding catastrophic forgetting in mitigating model biases in sentence-pair classiﬁcation with elastic weight consolidation. ArXiv, abs/2004.14366, 2020. URL https://arxiv.org/abs/2004.14366.',\n", " '[58] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Attention is all you need. In <PERSON><PERSON>, U. V. <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural Information Processing Systems 30, pages 5998–6008. Curran Associates, Inc., 2017. URL http://papers.nips.cc/paper/7181-attention-is-all-you-need.pdf.',\n", " '[59] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Diverse beam search for improved description of complex scenes. AAAI Conference on Artiﬁcial Intelligence, 2018. URL https://www.aaai.org/ocs/index. php/AAAI/AAAI18/paper/view/17329.',\n", " '[60] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. GLUE: A multi-task benchmark and analysis platform for natural language understanding. In Proceedings of the 2018 EMNLP Workshop BlackboxNLP: Analyzing and Interpreting Neural Networks for NLP, pages 353–355, Brussels, Belgium, November 2018. Association for Computational Linguistics. doi: 10.18653/v1/W18-5446. URL https://www.aclweb.org/ anthology/W18-5446.',\n", " '[61] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. SuperGLUE: A Stickier Benchmark for General- Purpose Language Understanding Systems. In <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, F<PERSON> <PERSON>\\\\textquotesing<PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural Information Processing Systems 32, pages 3261–3275. Curran Associates, Inc., 2019. URL https:// arxiv.org/abs/1905.00537.',\n", " '[62] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. R3: Reinforced ranker-reader for open-domain question answering. In <PERSON> and <PERSON><PERSON>, editors, Proceedings of the Thirty-Second AAAI Conference on Artiﬁcial Intelligence, (AAAI-18), the 30th innovative Applications of Artiﬁcial Intelligence (IAAI-18), and the 8th AAAI Symposium on Educational Advances in Artiﬁcial Intelligence (EAAI-18), New Orleans, Louisiana, USA, February 2-7, 2018, pages 5981–5988. AAAI Press, 2018. URL https://www.aaai.org/ocs/index. php/AAAI/AAAI18/paper/view/16712.',\n", " '[63] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Evidence aggregation for answer re- ranking in open-domain question answering. In ICLR, 2018. URL https://openreview. net/forum?id=rJl3yM-Ab.',\n", " '[64] <PERSON>, <PERSON><PERSON>, and <PERSON>. Memory networks. In <PERSON><PERSON><PERSON> and <PERSON><PERSON>, editors, 3rd International Conference on Learning Representations, ICLR 2015, San Diego, CA, USA, May 7-9, 2015, Conference Track Proceedings, 2015. URL http://arxiv.org/abs/1410.3916.',\n", " '[65] <PERSON>, <PERSON>, and <PERSON>. Retrieve and reﬁne: Improved sequence generation models for dialogue. In Proceedings of the 2018 EMNLP Workshop SCAI: The 2nd International Workshop on Search-Oriented Conversational AI, pages 87–92, Brussels, Belgium, October 2018. Association for Computational Linguistics. doi: 10.18653/v1/W18-5713. URL https://www.aclweb.org/anthology/W18-5713.',\n", " '[66] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON> Huggingface’s transformers: State-of-the-art natural language processing. ArXiv, abs/1910.03771, 2019.',\n", " '[67] <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Addressing semantic drift in question generation for semi- supervised question answering. In Proceedings of the 2019 Conference on Empirical Meth- ods in Natural Language Processing and the 9th International Joint Conference on Natural Language Processing (EMNLP-IJCNLP), pages 2495–2509, Hong Kong, China, Novem- ber 2019. Association for Computational Linguistics. doi: 10.18653/v1/D19-1253. URL https://www.aclweb.org/anthology/D19-1253.',\n", " '[68] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Reasoning over semantic-level graph for fact checking. ArXiv, abs/1909.03745, 2019. URL https://arxiv.org/abs/1909.03745.']"]}, "metadata": {}, "execution_count": 22}]}, {"cell_type": "code", "source": ["!pip install langchain_core"], "metadata": {"id": "-_OkfkQH3Y2s", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "f8669c3e-6aa9-4806-a74a-fa28c98ab7fc"}, "execution_count": 23, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain_core\n", "  Downloading langchain_core-0.1.46-py3-none-any.whl (299 kB)\n", "\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/299.3 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[91m━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[90m╺\u001b[0m\u001b[90m━━━━━━━━━━━━━\u001b[0m \u001b[32m194.6/299.3 kB\u001b[0m \u001b[31m5.6 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m299.3/299.3 kB\u001b[0m \u001b[31m5.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain_core) (6.0.1)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain_core)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Collecting langsmith<0.2.0,>=0.1.0 (from langchain_core)\n", "  Downloading langsmith-0.1.51-py3-none-any.whl (115 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m116.0/116.0 kB\u001b[0m \u001b[31m11.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting packaging<24.0,>=23.2 (from langchain_core)\n", "  Downloading packaging-23.2-py3-none-any.whl (53 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m53.0/53.0 kB\u001b[0m \u001b[31m5.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain_core) (2.7.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain_core) (8.2.3)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain_core)\n", "  Downloading jsonpointer-2.4-py2.py3-none-any.whl (7.8 kB)\n", "Collecting or<PERSON><PERSON><4.0.0,>=3.9.14 (from langsmith<0.2.0,>=0.1.0->langchain_core)\n", "  Downloading orjson-3.10.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (141 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m141.1/141.1 kB\u001b[0m \u001b[31m8.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.0->langchain_core) (2.31.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain_core) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain_core) (2.18.1)\n", "Requirement already satisfied: typing-extensions>=4.6.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain_core) (4.11.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langsmith<0.2.0,>=0.1.0->langchain_core) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langsmith<0.2.0,>=0.1.0->langchain_core) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langsmith<0.2.0,>=0.1.0->langchain_core) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langsmith<0.2.0,>=0.1.0->langchain_core) (2024.2.2)\n", "Installing collected packages: packaging, orjson, jsonpointer, jsonpatch, langsmith, langchain_core\n", "  Attempting uninstall: packaging\n", "    Found existing installation: packaging 24.0\n", "    Uninstalling packaging-24.0:\n", "      Successfully uninstalled packaging-24.0\n", "Successfully installed jsonpatch-1.33 jsonpointer-2.4 langchain_core-0.1.46 langsmith-0.1.51 orjson-3.10.1 packaging-23.2\n"]}]}, {"cell_type": "code", "source": ["!pip install langchain_openai"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "rbRMkWGOefZm", "outputId": "33bff2ab-55a8-4730-b7ac-0d5671f5c7f3"}, "execution_count": 24, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain_openai\n", "  Downloading langchain_openai-0.1.4-py3-none-any.whl (33 kB)\n", "Requirement already satisfied: langchain-core<0.2.0,>=0.1.46 in /usr/local/lib/python3.10/dist-packages (from langchain_openai) (0.1.46)\n", "Collecting openai<2.0.0,>=1.10.0 (from langchain_openai)\n", "  Downloading openai-1.23.6-py3-none-any.whl (311 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m311.6/311.6 kB\u001b[0m \u001b[31m9.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting tiktoken<1,>=0.5.2 (from langchain_openai)\n", "  Downloading tiktoken-0.6.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.8/1.8 MB\u001b[0m \u001b[31m41.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.46->langchain_openai) (6.0.1)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.46->langchain_openai) (1.33)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.46->langchain_openai) (0.1.51)\n", "Requirement already satisfied: packaging<24.0,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.46->langchain_openai) (23.2)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.46->langchain_openai) (2.7.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.2.0,>=0.1.46->langchain_openai) (8.2.3)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.10.0->langchain_openai) (3.7.1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai<2.0.0,>=1.10.0->langchain_openai) (1.7.0)\n", "Collecting httpx<1,>=0.23.0 (from openai<2.0.0,>=1.10.0->langchain_openai)\n", "  Downloading httpx-0.27.0-py3-none-any.whl (75 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m7.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.10.0->langchain_openai) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.10.0->langchain_openai) (4.66.2)\n", "Requirement already satisfied: typing-extensions<5,>=4.7 in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.10.0->langchain_openai) (4.11.0)\n", "Requirement already satisfied: regex>=2022.1.18 in /usr/local/lib/python3.10/dist-packages (from tiktoken<1,>=0.5.2->langchain_openai) (2023.12.25)\n", "Requirement already satisfied: requests>=2.26.0 in /usr/local/lib/python3.10/dist-packages (from tiktoken<1,>=0.5.2->langchain_openai) (2.31.0)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.10.0->langchain_openai) (3.7)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.10.0->langchain_openai) (1.2.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.10.0->langchain_openai) (2024.2.2)\n", "Collecting httpcore==1.* (from httpx<1,>=0.23.0->openai<2.0.0,>=1.10.0->langchain_openai)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m8.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->openai<2.0.0,>=1.10.0->langchain_openai)\n", "  Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m6.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.2.0,>=0.1.46->langchain_openai) (2.4)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.0->langchain-core<0.2.0,>=0.1.46->langchain_openai) (3.10.1)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain-core<0.2.0,>=0.1.46->langchain_openai) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain-core<0.2.0,>=0.1.46->langchain_openai) (2.18.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken<1,>=0.5.2->langchain_openai) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken<1,>=0.5.2->langchain_openai) (2.0.7)\n", "Installing collected packages: h11, tiktoken, httpcore, httpx, openai, langchain_openai\n", "Successfully installed h11-0.14.0 httpcore-1.0.5 httpx-0.27.0 langchain_openai-0.1.4 openai-1.23.6 tiktoken-0.6.0\n"]}]}, {"cell_type": "code", "source": ["len(tab)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "V5f6LEkWeh_e", "outputId": "48a085c8-aa6b-4cb1-e2c9-306d15e07c99"}, "execution_count": 28, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["5"]}, "metadata": {}, "execution_count": 28}]}, {"cell_type": "code", "source": ["tab[0]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 90}, "id": "0xbJ4cytfpsN", "outputId": "48daacff-62f6-4cc4-c16f-23b740384fb6"}, "execution_count": 34, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Closed Book T5-11B [52] 34.5 T5-11B+SSM[52] 36.6 - - /50.1 37.4 /60.5 44.7 - - Model B-1 Label Acc. Open Book REALM [20] DPR [26] 40.4 / 41.5 57.9/ - - - 40.7 46.8 41.1 50.6 SotA BART - - 15.1 19.7 49.8* 49.9* 38.2 41.6 76.8 64.0 81.1 RAG-Token RAG-Seq. 44.1 55.2/66.1 45.5 50.0 44.5 56.8/68.0 45.2 52.2 RAG-Tok. 17.3 22.2 RAG-Seq. 14.7 21.4 40.1 40.8 41.5 44.2 72.5 89.5'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 34}]}, {"cell_type": "code", "source": ["len(img)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "CXMVS1PWezj3", "outputId": "86746ab0-cdc2-4b4e-c7a4-0cec5cd7d3dd"}, "execution_count": 27, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["4"]}, "metadata": {}, "execution_count": 27}]}, {"cell_type": "code", "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI"], "metadata": {"id": "kpjw423Ye0ju"}, "execution_count": 29, "outputs": []}, {"cell_type": "code", "source": ["# Prompt\n", "prompt_text = \"\"\"You are an assistant tasked with summarizing tables for retrieval. \\\n", "    These summaries will be embedded and used to retrieve the raw table elements. \\\n", "    Give a concise summary of the table that is well optimized for retrieval. Table {element} \"\"\""], "metadata": {"id": "_bbRvggrfFUG"}, "execution_count": 31, "outputs": []}, {"cell_type": "code", "source": ["prompt = ChatPromptTemplate.from_template(prompt_text)"], "metadata": {"id": "JM-4CppSfJnd"}, "execution_count": 32, "outputs": []}, {"cell_type": "code", "source": ["import os\n", "from google.colab import userdata\n", "OPENAI_API_TOKEN=userdata.get('OPENAI_API_KEY')\n", "os.environ[\"OPENAI_API_KEY\"] = OPENAI_API_TOKEN"], "metadata": {"id": "kEAfJG7_fXDu"}, "execution_count": 33, "outputs": []}, {"cell_type": "code", "source": ["# Text summary chain\n", "model = ChatOpenAI(temperature=0, model=\"gpt-4\")"], "metadata": {"id": "1mHzuhFAfdUn"}, "execution_count": 35, "outputs": []}, {"cell_type": "code", "source": ["summarize_chain = {\"element\": lambda x: x} | prompt | model | StrOutputParser()"], "metadata": {"id": "Vi5h9tpPftEu"}, "execution_count": 36, "outputs": []}, {"cell_type": "code", "source": ["table_summaries = []"], "metadata": {"id": "2nqNsUzyf03Q"}, "execution_count": 38, "outputs": []}, {"cell_type": "code", "source": ["table_summaries=summarize_chain.batch(tab,{\"max_concurrency\": 5})"], "metadata": {"id": "ybpy889Hf4GI"}, "execution_count": 39, "outputs": []}, {"cell_type": "code", "source": ["tab[0]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 90}, "id": "t114EKUfgLKF", "outputId": "b18ed036-b1a8-4be3-893b-697091210e3d"}, "execution_count": 40, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Closed Book T5-11B [52] 34.5 T5-11B+SSM[52] 36.6 - - /50.1 37.4 /60.5 44.7 - - Model B-1 Label Acc. Open Book REALM [20] DPR [26] 40.4 / 41.5 57.9/ - - - 40.7 46.8 41.1 50.6 SotA BART - - 15.1 19.7 49.8* 49.9* 38.2 41.6 76.8 64.0 81.1 RAG-Token RAG-Seq. 44.1 55.2/66.1 45.5 50.0 44.5 56.8/68.0 45.2 52.2 RAG-Tok. 17.3 22.2 RAG-Seq. 14.7 21.4 40.1 40.8 41.5 44.2 72.5 89.5'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 40}]}, {"cell_type": "code", "source": ["table_summaries[0]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 108}, "id": "aAslSPlZgOAu", "outputId": "7b252d46-e209-4d92-c062-986c1bf3817a"}, "execution_count": 41, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["\"The table presents the performance of various models on a task, measured by accuracy. Models include Closed Book T5-11B, T5-11B+SSM, Open Book REALM, DPR, SotA BART, RAG-Token, RAG-Seq, and RAG-Tok. The table also includes different versions or configurations of these models. Performance metrics vary, with some models achieving over 80% accuracy. The table provides a comprehensive comparison of these models' performance, allowing for easy identification of the most effective models.\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 41}]}, {"cell_type": "code", "source": ["img[0]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 126}, "id": "Xdif_KSTgQ9G", "outputId": "a067dfff-f894-4f52-f280-a8b129b1eb43"}, "execution_count": 43, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'@--- ee ee ee ee ee ee ee ee ee ee eee The middle ear includes End-to-End Backprop through q and pe the tympanic cavity and the three ossicles. (y) Define \"middle ear\" (x) Question Answering: Question Query Retriever py Document Generator pg “fnower Generation Index. (Non-Parametric) (Parametric) d(z) supports (y) <PERSON> was born in Hawaii. (x) q(x) Fact Verification: Fact Query Fact Verification: Label Generation The Divine This 14th century work Comedy (x) is divided into 3 Jeopardy Question Generation: Answer Query sections: \"Inferno\", \"purgatorio\" & \"Paradiso\" @) Question Generation'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 43}]}, {"cell_type": "code", "source": ["import base64\n", "import os\n", "from langchain_core.messages import HumanMessage"], "metadata": {"id": "clS9oDdqgcqn"}, "execution_count": 44, "outputs": []}, {"cell_type": "code", "source": ["def encode_image(image_path):\n", "    \"\"\"Getting the base64 string\"\"\"\n", "    with open(image_path, \"rb\") as image_file:\n", "        return base64.b64encode(image_file.read()).decode(\"utf-8\")"], "metadata": {"id": "mKR7JUAEgixP"}, "execution_count": 45, "outputs": []}, {"cell_type": "code", "source": ["def image_summarize(img_base64, prompt):\n", "    \"\"\"Make image summary\"\"\"\n", "\n", "\n", "    chat = ChatOpenAI(model=\"gpt-4-vision-preview\", max_tokens=1024)\n", "\n", "    msg = chat.invoke(\n", "        [\n", "            HumanMessage(\n", "                content=[\n", "                    {\"type\": \"text\", \"text\": prompt},\n", "\n", "                     {\n", "                        \"type\": \"image_url\",\n", "                        \"image_url\": {\"url\": f\"data:image/jpeg;base64,{img_base64}\"},\n", "                    },\n", "                ]\n", "            )\n", "        ]\n", "    )\n", "    return msg.content"], "metadata": {"id": "nqWb87Hbgn8g"}, "execution_count": 46, "outputs": []}, {"cell_type": "code", "source": ["def generate_img_summaries(path):\n", "    \"\"\"\n", "    Generate summaries and base64 encoded strings for images\n", "    path: Path to list of .jpg files extracted by Unstructured\n", "    \"\"\"\n", "\n", "    # Store base64 encoded images\n", "    img_base64_list = []\n", "\n", "    # Store image summaries\n", "    image_summaries = []\n", "\n", "    # Prompt\n", "    prompt = \"\"\"You are an assistant tasked with summarizing images for retrieval. \\\n", "    These summaries will be embedded and used to retrieve the raw image. \\\n", "    Give a concise summary of the image that is well optimized for retrieval.\"\"\"\n", "\n", "\n", "    base64_image = encode_image(path)\n", "    img_base64_list.append(base64_image)\n", "    image_summaries.append(image_summarize(base64_image, prompt))\n", "\n", "    return img_base64_list, image_summaries"], "metadata": {"id": "M2chds0kg16e"}, "execution_count": 47, "outputs": []}, {"cell_type": "code", "source": ["fpath=\"/content/extracted_data2/figure-17-4.jpg\""], "metadata": {"id": "ebVqRi8fhLM4"}, "execution_count": 48, "outputs": []}, {"cell_type": "code", "source": ["img_base64_list,image_summaries=generate_img_summaries(fpath)"], "metadata": {"id": "zQbpbrRbhRhO"}, "execution_count": 49, "outputs": []}, {"cell_type": "code", "source": ["image_summaries[0]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 126}, "id": "GTtDwQ0chbf-", "outputId": "9da9597f-47f6-4ed9-da69-1cf3c66df5b5"}, "execution_count": 51, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'The image displays a screenshot of a quiz or assessment interface with a question asking to identify which of two given sentences is more factually true about <PERSON><PERSON><PERSON>. The first sentence (A) states that \"The Sun Also Rises\" is a novel by the author of \"A Farewell to Arms,\" while the second sentence (B) claims that the author of \"The Sun Also Rises\" was born in Havana, Cuba, the son of Spanish immigrants. There are four options to select the most accurate sentence, ranging from either sentence being more true, both being true, or both being completely untrue.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 51}]}, {"cell_type": "code", "source": [], "metadata": {"id": "HA5izJnzhgB3"}, "execution_count": null, "outputs": []}]}