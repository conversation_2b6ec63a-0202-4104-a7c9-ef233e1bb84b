{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Lab 2\n", "\n", "We're about to create and use our own MCP Server and MCP Client!\n", "\n", "It's pretty simple, but it's not super-simple. The excitment around MCP is about how easy it is to share and use other MCP Servers - making our own does involve a bit of work.\n", "\n", "## First, looking at `accounts.py`\n", "\n", "Let's review some python code made mostly by a hard-working Engineering Team."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from dotenv import load_dotenv\n", "from agents import Agent, Runner, trace\n", "from agents.mcp import MCPServerStdio\n", "from IPython.display import display, Markdown\n", "\n", "load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from accounts import Account"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["Account(name='ed', balance=6730.************, strategy='Monitor market closely for opportunities based on volatility and news.', holdings={'DIS': 20, 'AMZN': 6}, transactions=[20 shares of DIS at 105.************** each., 3 shares of AMZN at 193.5864 each., 3 shares of AMZN at 193.5864 each.], portfolio_value_time_series=[('2025-05-08 19:54:56', 10000.0), ('2025-05-08 19:55:04', 10000.0), ('2025-05-08 19:55:57', 9995.792), ('2025-05-08 19:57:26', 9991.792), ('2025-05-09 22:30:24', 10010.2328), ('2025-05-09 22:30:26', 10010.2328), ('2025-05-09 22:33:16', 10010.2328), ('2025-05-10 21:28:53', 10009.0736), ('2025-05-10 21:28:58', 10009.0736), ('2025-05-10 21:31:11', 10009.0736)])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["account = Account.get(\"Ed\")\n", "account"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Completed. Latest details:\\n{\"name\": \"ed\", \"balance\": 6536.*************, \"strategy\": \"Monitor market closely for opportunities based on volatility and news.\", \"holdings\": {\"DIS\": 20, \"AMZN\": 7}, \"transactions\": [{\"symbol\": \"DIS\", \"quantity\": 20, \"price\": 105.**************, \"timestamp\": \"2025-05-08 19:55:57\", \"rationale\": \"Positive market performance and strong day change of 3.04%.\"}, {\"symbol\": \"AMZN\", \"quantity\": 3, \"price\": 193.5864, \"timestamp\": \"2025-05-09 22:30:24\", \"rationale\": \"Because this bookstore website looks promising\"}, {\"symbol\": \"AMZN\", \"quantity\": 3, \"price\": 193.5864, \"timestamp\": \"2025-05-10 21:28:53\", \"rationale\": \"Because this bookstore website looks promising\"}, {\"symbol\": \"AMZN\", \"quantity\": 1, \"price\": 193.5864, \"timestamp\": \"2025-05-11 13:09:24\", \"rationale\": \"Because this bookstore website looks promising\"}], \"portfolio_value_time_series\": [[\"2025-05-08 19:54:56\", 10000.0], [\"2025-05-08 19:55:04\", 10000.0], [\"2025-05-08 19:55:57\", 9995.792], [\"2025-05-08 19:57:26\", 9991.792], [\"2025-05-09 22:30:24\", 10010.2328], [\"2025-05-09 22:30:26\", 10010.2328], [\"2025-05-09 22:33:16\", 10010.2328], [\"2025-05-10 21:28:53\", 10009.0736], [\"2025-05-10 21:28:58\", 10009.0736], [\"2025-05-10 21:31:11\", 10009.0736], [\"2025-05-11 13:09:24\", 10008.************]], \"total_portfolio_value\": 10008.************, \"total_profit_loss\": 8.***************}'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["account.buy_shares(\"AMZN\", 1, \"Because this bookstore website looks promising\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["'{\"name\": \"ed\", \"balance\": 6536.*************, \"strategy\": \"Monitor market closely for opportunities based on volatility and news.\", \"holdings\": {\"DIS\": 20, \"AMZN\": 7}, \"transactions\": [{\"symbol\": \"DIS\", \"quantity\": 20, \"price\": 105.**************, \"timestamp\": \"2025-05-08 19:55:57\", \"rationale\": \"Positive market performance and strong day change of 3.04%.\"}, {\"symbol\": \"AMZN\", \"quantity\": 3, \"price\": 193.5864, \"timestamp\": \"2025-05-09 22:30:24\", \"rationale\": \"Because this bookstore website looks promising\"}, {\"symbol\": \"AMZN\", \"quantity\": 3, \"price\": 193.5864, \"timestamp\": \"2025-05-10 21:28:53\", \"rationale\": \"Because this bookstore website looks promising\"}, {\"symbol\": \"AMZN\", \"quantity\": 1, \"price\": 193.5864, \"timestamp\": \"2025-05-11 13:09:24\", \"rationale\": \"Because this bookstore website looks promising\"}], \"portfolio_value_time_series\": [[\"2025-05-08 19:54:56\", 10000.0], [\"2025-05-08 19:55:04\", 10000.0], [\"2025-05-08 19:55:57\", 9995.792], [\"2025-05-08 19:57:26\", 9991.792], [\"2025-05-09 22:30:24\", 10010.2328], [\"2025-05-09 22:30:26\", 10010.2328], [\"2025-05-09 22:33:16\", 10010.2328], [\"2025-05-10 21:28:53\", 10009.0736], [\"2025-05-10 21:28:58\", 10009.0736], [\"2025-05-10 21:31:11\", 10009.0736], [\"2025-05-11 13:09:24\", 10008.************], [\"2025-05-11 13:09:28\", 10008.************]], \"total_portfolio_value\": 10008.************, \"total_profit_loss\": 8.***************}'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["account.report()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'symbol': 'DIS',\n", "  'quantity': 20,\n", "  'price': 105.**************,\n", "  'timestamp': '2025-05-08 19:55:57',\n", "  'rationale': 'Positive market performance and strong day change of 3.04%.'},\n", " {'symbol': 'AMZN',\n", "  'quantity': 3,\n", "  'price': 193.5864,\n", "  'timestamp': '2025-05-09 22:30:24',\n", "  'rationale': 'Because this bookstore website looks promising'},\n", " {'symbol': 'AMZN',\n", "  'quantity': 3,\n", "  'price': 193.5864,\n", "  'timestamp': '2025-05-10 21:28:53',\n", "  'rationale': 'Because this bookstore website looks promising'},\n", " {'symbol': 'AMZN',\n", "  'quantity': 1,\n", "  'price': 193.5864,\n", "  'timestamp': '2025-05-11 13:09:24',\n", "  'rationale': 'Because this bookstore website looks promising'}]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["account.list_transactions()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Now we make an MCP server: `accounts_server.py`"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Tool(name='get_balance', description='Get the cash balance of the given account name.\\n\\n    Args:\\n        name: The name of the account holder\\n    ', inputSchema={'properties': {'name': {'title': 'Name', 'type': 'string'}}, 'required': ['name'], 'title': 'get_balanceArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='get_holdings', description='Get the holdings of the given account name.\\n\\n    Args:\\n        name: The name of the account holder\\n    ', inputSchema={'properties': {'name': {'title': 'Name', 'type': 'string'}}, 'required': ['name'], 'title': 'get_holdingsArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='buy_shares', description=\"Buy shares of a stock.\\n\\n    Args:\\n        name: The name of the account holder\\n        symbol: The symbol of the stock\\n        quantity: The quantity of shares to buy\\n        rationale: The rationale for the purchase and fit with the account's strategy\\n    \", inputSchema={'properties': {'name': {'title': 'Name', 'type': 'string'}, 'symbol': {'title': 'Symbol', 'type': 'string'}, 'quantity': {'title': 'Quantity', 'type': 'integer'}, 'rationale': {'title': 'Rationale', 'type': 'string'}}, 'required': ['name', 'symbol', 'quantity', 'rationale'], 'title': 'buy_sharesArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='sell_shares', description=\"Sell shares of a stock.\\n\\n    Args:\\n        name: The name of the account holder\\n        symbol: The symbol of the stock\\n        quantity: The quantity of shares to sell\\n        rationale: The rationale for the sale and fit with the account's strategy\\n    \", inputSchema={'properties': {'name': {'title': 'Name', 'type': 'string'}, 'symbol': {'title': 'Symbol', 'type': 'string'}, 'quantity': {'title': 'Quantity', 'type': 'integer'}, 'rationale': {'title': 'Rationale', 'type': 'string'}}, 'required': ['name', 'symbol', 'quantity', 'rationale'], 'title': 'sell_sharesArguments', 'type': 'object'}, annotations=None),\n", " Tool(name='change_strategy', description='At your discretion, if you choose to, call this to change your investment strategy for the future.\\n\\n    Args:\\n        name: The name of the account holder\\n        strategy: The new strategy for the account\\n    ', inputSchema={'properties': {'name': {'title': 'Name', 'type': 'string'}, 'strategy': {'title': 'Strategy', 'type': 'string'}}, 'required': ['name', 'strategy'], 'title': 'change_strategyArguments', 'type': 'object'}, annotations=None)]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Now let's use our accounts server as an MCP server\n", "\n", "params = {\"command\": \"uv\", \"args\": [\"run\", \"accounts_server.py\"]}\n", "async with MCPServerStdio(params=params) as server:\n", "    mcp_tools = await server.list_tools()\n", "\n", "mcp_tools"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["instructions = \"You are able to manage an account for a client, and answer questions about the account.\"\n", "request = \"My name is <PERSON> and my account is under the name <PERSON>. What's my balance and my holdings?\"\n", "model = \"gpt-4o-mini\""]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/markdown": ["Your account balance is **$6,536.69**.\n", "\n", "As for your holdings, you have:\n", "- **20 shares** of Disney (DIS)\n", "- **7 shares** of Amazon (AMZN)"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "\n", "async with MCPServerStdio(params=params) as mcp_server:\n", "    agent = Agent(name=\"account_manager\", instructions=instructions, model=model, mcp_servers=[mcp_server])\n", "    with trace(\"account_manager\"):\n", "        result = await Runner.run(agent, request)\n", "    display(Markdown(result.final_output))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["OpenAI Agents SDK takes care of the MCP Client, but we can also make one ourselves\n", "\n", "## See `accounts_client.py` for our MCP Client"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"name\": \"ed\", \"balance\": 6536.*************, \"strategy\": \"Monitor market closely for opportunities based on volatility and news.\", \"holdings\": {\"DIS\": 20, \"AMZN\": 7}, \"transactions\": [{\"symbol\": \"DIS\", \"quantity\": 20, \"price\": 105.**************, \"timestamp\": \"2025-05-08 19:55:57\", \"rationale\": \"Positive market performance and strong day change of 3.04%.\"}, {\"symbol\": \"AMZN\", \"quantity\": 3, \"price\": 193.5864, \"timestamp\": \"2025-05-09 22:30:24\", \"rationale\": \"Because this bookstore website looks promising\"}, {\"symbol\": \"AMZN\", \"quantity\": 3, \"price\": 193.5864, \"timestamp\": \"2025-05-10 21:28:53\", \"rationale\": \"Because this bookstore website looks promising\"}, {\"symbol\": \"AMZN\", \"quantity\": 1, \"price\": 193.5864, \"timestamp\": \"2025-05-11 13:09:24\", \"rationale\": \"Because this bookstore website looks promising\"}], \"portfolio_value_time_series\": [[\"2025-05-08 19:54:56\", 10000.0], [\"2025-05-08 19:55:04\", 10000.0], [\"2025-05-08 19:55:57\", 9995.792], [\"2025-05-08 19:57:26\", 9991.792], [\"2025-05-09 22:30:24\", 10010.2328], [\"2025-05-09 22:30:26\", 10010.2328], [\"2025-05-09 22:33:16\", 10010.2328], [\"2025-05-10 21:28:53\", 10009.0736], [\"2025-05-10 21:28:58\", 10009.0736], [\"2025-05-10 21:31:11\", 10009.0736], [\"2025-05-11 13:09:24\", 10008.************], [\"2025-05-11 13:09:28\", 10008.************], [\"2025-05-11 13:11:42\", 10008.************]], \"total_portfolio_value\": 10008.************, \"total_profit_loss\": 8.***************}\n"]}], "source": ["from accounts_client import read_accounts_resource\n", "\n", "context = await read_accounts_resource(\"ed\")\n", "print(context)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## And that's how to create MCP Servers and Clients!"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}