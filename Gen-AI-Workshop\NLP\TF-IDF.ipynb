{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.metrics import classification_report"], "metadata": {"id": "-0nT22CCRoGZ"}, "execution_count": 30, "outputs": []}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "CIHzkvqvOvUT"}, "outputs": [], "source": ["from sklearn.feature_extraction.text import TfidfVectorizer\n", "\n", "corpus = [\n", "    \"Thor eating pizza, <PERSON> is eating pizza, <PERSON><PERSON> ate pizza already\",\n", "    \"Apple is announcing new iphone tomorrow\",\n", "    \"Tesla is announcing new model-3 tomorrow\",\n", "    \"Google is announcing new pixel-6 tomorrow\",\n", "    \"Microsoft is announcing new surface tomorrow\",\n", "    \"Amazon is announcing new eco-dot tomorrow\",\n", "    \"I am eating biryani and you are eating grapes\"\n", "]"]}, {"cell_type": "code", "source": ["v = TfidfVectorizer()\n", "v.fit(corpus)\n", "transform_output = v.transform(corpus)"], "metadata": {"id": "OlPRPQDGQFfM"}, "execution_count": 2, "outputs": []}, {"cell_type": "code", "source": ["print(v.vocabulary_)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aPRgFXT2QTV7", "outputId": "c87504ea-bc90-438c-f3d8-a4778a8d9e8c"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["{'thor': 25, 'eating': 10, 'pizza': 22, 'loki': 17, 'is': 16, 'ironman': 15, 'ate': 7, 'already': 0, 'apple': 5, 'announcing': 4, 'new': 20, 'iphone': 14, 'tomorrow': 26, 'tesla': 24, 'model': 19, 'google': 12, 'pixel': 21, 'microsoft': 18, 'surface': 23, 'amazon': 2, 'eco': 11, 'dot': 9, 'am': 1, 'biryani': 8, 'and': 3, 'you': 27, 'are': 6, 'grapes': 13}\n"]}]}, {"cell_type": "code", "source": ["i = v.vocabulary_.get('thor')\n", "v.idf_[i]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Ca2WedHPQu9m", "outputId": "cac9afef-9531-469b-8827-dd482c0897a0"}, "execution_count": 11, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2.386294361119891"]}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "code", "source": ["# Print the idf of each word\n", "\n", "all_feature_names = v.get_feature_names_out()\n", "\n", "for word in all_feature_names:\n", "\n", "  indx = v.vocabulary_.get(word)\n", "\n", "  #get the score\n", "  idf_score = v.idf_[indx]\n", "\n", "  print(f\"{word}: {idf_score}\")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "G3dfIaq1QWdD", "outputId": "b7390878-4feb-4db3-b000-67278b3cd2f9"}, "execution_count": 12, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["already: 2.386294361119891\n", "am: 2.386294361119891\n", "amazon: 2.386294361119891\n", "and: 2.386294361119891\n", "announcing: 1.2876820724517808\n", "apple: 2.386294361119891\n", "are: 2.386294361119891\n", "ate: 2.386294361119891\n", "biryani: 2.386294361119891\n", "dot: 2.386294361119891\n", "eating: 1.9808292530117262\n", "eco: 2.386294361119891\n", "google: 2.386294361119891\n", "grapes: 2.386294361119891\n", "iphone: 2.386294361119891\n", "ironman: 2.386294361119891\n", "is: 1.1335313926245225\n", "loki: 2.386294361119891\n", "microsoft: 2.386294361119891\n", "model: 2.386294361119891\n", "new: 1.2876820724517808\n", "pixel: 2.386294361119891\n", "pizza: 2.386294361119891\n", "surface: 2.386294361119891\n", "tesla: 2.386294361119891\n", "thor: 2.386294361119891\n", "tomorrow: 1.2876820724517808\n", "you: 2.386294361119891\n"]}]}, {"cell_type": "code", "source": ["# Print the transformed output from tf-idf\n", "print(transform_output.toarray())"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2okEmDN1RAUT", "outputId": "30a40761-ade8-4b26-bb99-48d5905b9067"}, "execution_count": 13, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[[0.24266547 0.         0.         0.         0.         0.\n", "  0.         0.24266547 0.         0.         0.40286636 0.\n", "  0.         0.         0.         0.24266547 0.11527033 0.24266547\n", "  0.         0.         0.         0.         0.72799642 0.\n", "  0.         0.24266547 0.         0.        ]\n", " [0.         0.         0.         0.         0.30652086 0.5680354\n", "  0.         0.         0.         0.         0.         0.\n", "  0.         0.         0.5680354  0.         0.26982671 0.\n", "  0.         0.         0.30652086 0.         0.         0.\n", "  0.         0.         0.30652086 0.        ]\n", " [0.         0.         0.         0.         0.30652086 0.\n", "  0.         0.         0.         0.         0.         0.\n", "  0.         0.         0.         0.         0.26982671 0.\n", "  0.         0.5680354  0.30652086 0.         0.         0.\n", "  0.5680354  0.         0.30652086 0.        ]\n", " [0.         0.         0.         0.         0.30652086 0.\n", "  0.         0.         0.         0.         0.         0.\n", "  0.5680354  0.         0.         0.         0.26982671 0.\n", "  0.         0.         0.30652086 0.5680354  0.         0.\n", "  0.         0.         0.30652086 0.        ]\n", " [0.         0.         0.         0.         0.30652086 0.\n", "  0.         0.         0.         0.         0.         0.\n", "  0.         0.         0.         0.         0.26982671 0.\n", "  0.5680354  0.         0.30652086 0.         0.         0.5680354\n", "  0.         0.         0.30652086 0.        ]\n", " [0.         0.         0.49391316 0.         0.26652333 0.\n", "  0.         0.         0.         0.49391316 0.         0.49391316\n", "  0.         0.         0.         0.         0.23461736 0.\n", "  0.         0.         0.26652333 0.         0.         0.\n", "  0.         0.         0.26652333 0.        ]\n", " [0.         0.33794257 0.         0.33794257 0.         0.\n", "  0.33794257 0.         0.33794257 0.         0.56104271 0.\n", "  0.         0.33794257 0.         0.         0.         0.\n", "  0.         0.         0.         0.         0.         0.\n", "  0.         0.         0.         0.33794257]]\n"]}]}, {"cell_type": "markdown", "source": ["#### **Custom Use case**"], "metadata": {"id": "JJFpTUM_T7LH"}}, {"cell_type": "markdown", "source": ["- E-commerce data\n", "- 4 labels: Household, Electronics, Clothing & Books\n", "- Task is to create a classification model that can predict a given description of a product and classify them as one of the labels using TfIdf vectorization technique"], "metadata": {"id": "9YpUIaWHT9tI"}}, {"cell_type": "code", "source": ["df = pd.read_csv('/content/Ecommerce_data.csv')"], "metadata": {"id": "PzrjnLajRQGL"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["df.head(5)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "fbGNnocRRmgE", "outputId": "0b6f0b76-c405-46b5-cce3-a0ee724461db"}, "execution_count": 17, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                                                Text                   label\n", "0  Urban Ladder Eisner Low Back Study-Office Comp...               Household\n", "1  Contrast living Wooden Decorative Box,Painted ...               Household\n", "2  IO Crest SY-PCI40010 PCI RAID Host Controller ...             Electronics\n", "3  ISAKAA Baby Socks from Just Born to 8 Years- P...  Clothing & Accessories\n", "4  Indira Designer Women's Art Mysore Silk Saree ...  Clothing & Accessories"], "text/html": ["\n", "  <div id=\"df-efa9fa11-3c7c-4f9c-9b31-2686cc0f10b5\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Text</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Urban Ladder Eisner Low Back Study-Office Comp...</td>\n", "      <td>Household</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Contrast living Wooden Decorative Box,Painted ...</td>\n", "      <td>Household</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>IO Crest SY-PCI40010 PCI RAID Host Controller ...</td>\n", "      <td>Electronics</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ISAKAA Baby Socks from Just Born to 8 Years- P...</td>\n", "      <td>Clothing &amp; Accessories</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Indira Designer Women's Art Mysore Silk Saree ...</td>\n", "      <td>Clothing &amp; Accessories</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-efa9fa11-3c7c-4f9c-9b31-2686cc0f10b5')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-efa9fa11-3c7c-4f9c-9b31-2686cc0f10b5 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-efa9fa11-3c7c-4f9c-9b31-2686cc0f10b5');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-eb32ece9-d9fb-4843-8544-ae186de81a4e\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-eb32ece9-d9fb-4843-8544-ae186de81a4e')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-eb32ece9-d9fb-4843-8544-ae186de81a4e button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 24000,\n  \"fields\": [\n    {\n      \"column\": \"Text\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 13834,\n        \"samples\": [\n          \"Deco Home Duvet Cover Set 100% Cotton, 7 pc Set, 1 Duvet Cover, 2 Sham Covers, 1 Flat Sheet, 3 Cushion Covers, Breathable, Comfortable, (<PERSON> Size, French Blue) Color Name:French Blue   Add vibrancy to your Bed-Room with fantastic French Blue Color Bedding!! The Deco Window Bedding are giving you a great chance to redesign your Room. Dark or enlivening colours like French Blue is used to get the attention towards the Bed for their scenic view. The interiors get a boost with the color as it complements any colour furniture well.Wash Care: Dry Clean\",\n          \"BANKARON KE LIYE LEKHANKAN AVM VIT JAIIB\",\n          \"DriftingWood Rich Walnut Solid Wooden Dressers and Chests of Drawers for Bedroom Round Shape - 4 Drawers and Storage Color:Walnut - 4 Drawers   A little bit modern and a little bit classic. The DriftingWood family blends warm wood tones and transitional lines with a lightweight structure and sleek wood hardware. The 4 drawer chest is a versatile piece that can be used all over the home, from the living room (use it for media, photo albums or anything else) to the bedroom (it makes a great dresser). Refer to the images for dimension details. Indoor use only.Assembly : We encourage self assembly of products to help customers better & completely understand ther product they buy.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"label\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 4,\n        \"samples\": [\n          \"Electronics\",\n          \"Books\",\n          \"Household\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 17}]}, {"cell_type": "code", "source": ["df.label.value_counts()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "17wP-XtDRsyE", "outputId": "56422d75-c832-4906-8fbc-2a752529efbc"}, "execution_count": 18, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Household                 6000\n", "Electronics               6000\n", "Clothing & Accessories    6000\n", "Books                     6000\n", "Name: label, dtype: int64"]}, "metadata": {}, "execution_count": 18}]}, {"cell_type": "code", "source": ["df.shape"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "iM7hcpXrRvlT", "outputId": "04faa3e4-c559-45cf-b83d-880ac8a0b78e"}, "execution_count": 19, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(24000, 2)"]}, "metadata": {}, "execution_count": 19}]}, {"cell_type": "code", "source": ["df['label_num'] = df['label'].map({\n", "    'Household': 0,\n", "    'Electronics': 1,\n", "    'Clothing & Accessories': 2,\n", "    'Books': 3\n", "})"], "metadata": {"id": "Y7mtKO4CRzKL"}, "execution_count": 22, "outputs": []}, {"cell_type": "code", "source": ["df.head(5)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "uQUIFf3eSMcM", "outputId": "fd349c7b-775b-4d3e-82bc-d970ae7b5ad5"}, "execution_count": 23, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                                                Text                   label  \\\n", "0  Urban Ladder Eisner Low Back Study-Office Comp...               Household   \n", "1  Contrast living Wooden Decorative Box,Painted ...               Household   \n", "2  IO Crest SY-PCI40010 PCI RAID Host Controller ...             Electronics   \n", "3  ISAKAA Baby Socks from Just Born to 8 Years- P...  Clothing & Accessories   \n", "4  Indira Designer Women's Art Mysore Silk Saree ...  Clothing & Accessories   \n", "\n", "   label_num  \n", "0          0  \n", "1          0  \n", "2          1  \n", "3          2  \n", "4          2  "], "text/html": ["\n", "  <div id=\"df-add1d577-3a14-43d6-b799-9c716b2db61c\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Text</th>\n", "      <th>label</th>\n", "      <th>label_num</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Urban Ladder Eisner Low Back Study-Office Comp...</td>\n", "      <td>Household</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Contrast living Wooden Decorative Box,Painted ...</td>\n", "      <td>Household</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>IO Crest SY-PCI40010 PCI RAID Host Controller ...</td>\n", "      <td>Electronics</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ISAKAA Baby Socks from Just Born to 8 Years- P...</td>\n", "      <td>Clothing &amp; Accessories</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Indira Designer Women's Art Mysore Silk Saree ...</td>\n", "      <td>Clothing &amp; Accessories</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-add1d577-3a14-43d6-b799-9c716b2db61c')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-add1d577-3a14-43d6-b799-9c716b2db61c button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-add1d577-3a14-43d6-b799-9c716b2db61c');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-1abb10bc-2e49-470f-be46-b721ac5bed08\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-1abb10bc-2e49-470f-be46-b721ac5bed08')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-1abb10bc-2e49-470f-be46-b721ac5bed08 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 24000,\n  \"fields\": [\n    {\n      \"column\": \"Text\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 13834,\n        \"samples\": [\n          \"Deco Home Duvet Cover Set 100% Cotton, 7 pc Set, 1 Duvet Cover, 2 Sham Covers, 1 Flat Sheet, 3 Cushion Covers, Breathable, Comfortable, (<PERSON> Size, French Blue) Color Name:French Blue   Add vibrancy to your Bed-Room with fantastic French Blue Color Bedding!! The Deco Window Bedding are giving you a great chance to redesign your Room. Dark or enlivening colours like French Blue is used to get the attention towards the Bed for their scenic view. The interiors get a boost with the color as it complements any colour furniture well.Wash Care: Dry Clean\",\n          \"BANKARON KE LIYE LEKHANKAN AVM VIT JAIIB\",\n          \"DriftingWood Rich Walnut Solid Wooden Dressers and Chests of Drawers for Bedroom Round Shape - 4 Drawers and Storage Color:Walnut - 4 Drawers   A little bit modern and a little bit classic. The DriftingWood family blends warm wood tones and transitional lines with a lightweight structure and sleek wood hardware. The 4 drawer chest is a versatile piece that can be used all over the home, from the living room (use it for media, photo albums or anything else) to the bedroom (it makes a great dresser). Refer to the images for dimension details. Indoor use only.Assembly : We encourage self assembly of products to help customers better & completely understand ther product they buy.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"label\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 4,\n        \"samples\": [\n          \"Electronics\",\n          \"Books\",\n          \"Household\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"label_num\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 0,\n        \"max\": 3,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          1,\n          3,\n          0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 23}]}, {"cell_type": "markdown", "source": ["#### **Train Test Split**"], "metadata": {"id": "S7nCzz9NSeWG"}}, {"cell_type": "code", "source": ["from sklearn.model_selection import train_test_split\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(df.Text, df.label_num, test_size=0.2)"], "metadata": {"id": "4Xm3I3RCSb7j"}, "execution_count": 24, "outputs": []}, {"cell_type": "code", "source": ["len(X_train)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Q2hCR_4xSsVL", "outputId": "d12e889d-38ae-4f1f-fd61-61882505f783"}, "execution_count": 25, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["19200"]}, "metadata": {}, "execution_count": 25}]}, {"cell_type": "code", "source": ["len(X_test)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mFYVQFUJStpq", "outputId": "4a8f9e6a-449f-4d7b-8d53-e8d835b25692"}, "execution_count": 26, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["4800"]}, "metadata": {}, "execution_count": 26}]}, {"cell_type": "markdown", "source": ["#### **Tfidf Vectorizer**"], "metadata": {"id": "uRdS0VtNTKtZ"}}, {"cell_type": "code", "source": ["tf = TfidfVectorizer()\n", "\n", "X_train_tf = tf.fit_transform(X_train)\n", "X_test_tf = tf.transform(X_test)"], "metadata": {"id": "lFsg2xaoTKZ3"}, "execution_count": 28, "outputs": []}, {"cell_type": "markdown", "source": ["#### **Classification Model**"], "metadata": {"id": "ESpP13GcSx4Z"}}, {"cell_type": "code", "source": ["clf = DecisionTreeClassifier()\n", "clf.fit(X_train_tf,y_train)\n", "\n", "y_pred = clf.predict(X_test_tf)"], "metadata": {"id": "mBW35bXPSu8i"}, "execution_count": 29, "outputs": []}, {"cell_type": "code", "source": ["print(classification_report(y_test, y_pred))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "KxeArJpgTW0a", "outputId": "d13d3671-8af2-4ce1-9084-73544e258eb9"}, "execution_count": 31, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.91      0.94      0.92      1213\n", "           1       0.95      0.93      0.94      1162\n", "           2       0.97      0.95      0.96      1239\n", "           3       0.96      0.96      0.96      1186\n", "\n", "    accuracy                           0.94      4800\n", "   macro avg       0.95      0.94      0.95      4800\n", "weighted avg       0.95      0.94      0.95      4800\n", "\n"]}]}, {"cell_type": "markdown", "source": ["#### **Testing on a new data**"], "metadata": {"id": "tBhBkTqUTk-8"}}, {"cell_type": "code", "source": ["#msg = [\"Indira Designer Women's Art Mysore Silk Saree With Blouse Piece (Star-Red) This Saree Is Of Art Mysore Silk & Comes With Blouse Piece.\"]\n", "msg = [\"<PERSON><PERSON><PERSON><PERSON>'s designer women art saree silk blouse piece, saree with pipili chandua work\"]\n", "msg_tf = tf.transform(msg)\n", "\n", "clf.predict(msg_tf)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sjYce95aS99j", "outputId": "b7c700bf-11ea-4ff0-826e-830b43263e40"}, "execution_count": 35, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([2])"]}, "metadata": {}, "execution_count": 35}]}, {"cell_type": "code", "source": [], "metadata": {"id": "nBVbCZIzTs6T"}, "execution_count": null, "outputs": []}]}