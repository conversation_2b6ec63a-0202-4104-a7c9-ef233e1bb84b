{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["# **MultiModal RAG App for Video Processing With LlamaIndex and LanceDB**"], "metadata": {"id": "Yn8jv85EiZn_"}}, {"cell_type": "markdown", "source": ["### 1. llamaindex framework\n", "### 2. Lancedb Vector DataBase\n", "### 3. LLM MultiModAl GPT-4V or Google-gemini-pro-vision\n", "\n", "\n", "# **Steps Need to follow:**\n", "#### 1. Download video from YouTube, process and store it.\n", "\n", "#### 2. Build Multi-Modal index and vector store for both texts and images.\n", "\n", "#### 3. Retrieve relevant images and context, use both to augment the prompt.\n", "\n", "#### 4. Using GPT4V for reasoning the correlations between the input query and augmented data and generating final response."], "metadata": {"id": "5ZHVe_qkiYkg"}}, {"cell_type": "code", "source": ["%pip install llama-index-vector-stores-lancedb\n", "%pip install llama-index-multi-modal-llms-openai\n", "%pip install llama-index-embeddings-clip\n", "%pip install git+https://github.com/openai/CLIP.git\n", "!pip install llama-index-readers-file"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "sY9xSK0SihIG", "outputId": "22e8e2d4-aa21-4706-8660-76cb79a39caa"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting llama-index-vector-stores-lancedb\n", "  Downloading llama_index_vector_stores_lancedb-0.1.3-py3-none-any.whl (4.1 kB)\n", "Collecting lancedb<0.6.0,>=0.5.1 (from llama-index-vector-stores-lancedb)\n", "  Downloading lancedb-0.5.7-py3-none-any.whl (115 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m115.1/115.1 kB\u001b[0m \u001b[31m2.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting llama-index-core<0.11.0,>=0.10.1 (from llama-index-vector-stores-lancedb)\n", "  Downloading llama_index_core-0.10.30-py3-none-any.whl (15.4 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m15.4/15.4 MB\u001b[0m \u001b[31m25.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting deprecation (from lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb)\n", "  Downloading deprecation-2.1.0-py2.py3-none-any.whl (11 kB)\n", "Collecting pylance==0.9.18 (from lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb)\n", "  Downloading pylance-0.9.18-cp38-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (21.6 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.6/21.6 MB\u001b[0m \u001b[31m42.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting ratelimiter~=1.0 (from lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb)\n", "  Downloading ratelimiter-1.2.0.post0-py3-none-any.whl (6.6 kB)\n", "Collecting retry>=0.9.2 (from lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb)\n", "  Downloading retry-0.9.2-py2.py3-none-any.whl (8.0 kB)\n", "Requirement already satisfied: tqdm>=4.27.0 in /usr/local/lib/python3.10/dist-packages (from lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb) (4.66.2)\n", "Requirement already satisfied: pydantic>=1.10 in /usr/local/lib/python3.10/dist-packages (from lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb) (2.7.0)\n", "Requirement already satisfied: attrs>=21.3.0 in /usr/local/lib/python3.10/dist-packages (from lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb) (23.2.0)\n", "Collecting semver>=3.0 (from lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb)\n", "  Downloading semver-3.0.2-py3-none-any.whl (17 kB)\n", "Requirement already satisfied: cachetools in /usr/local/lib/python3.10/dist-packages (from lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb) (5.3.3)\n", "Requirement already satisfied: pyyaml>=6.0 in /usr/local/lib/python3.10/dist-packages (from lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb) (6.0.1)\n", "Requirement already satisfied: click>=8.1.7 in /usr/local/lib/python3.10/dist-packages (from lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb) (8.1.7)\n", "Requirement already satisfied: requests>=2.31.0 in /usr/local/lib/python3.10/dist-packages (from lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb) (2.31.0)\n", "Collecting overrides>=0.7 (from lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb)\n", "  Downloading overrides-7.7.0-py3-none-any.whl (17 kB)\n", "Requirement already satisfied: pyarrow>=12 in /usr/local/lib/python3.10/dist-packages (from pylance==0.9.18->lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb) (14.0.2)\n", "Requirement already satisfied: numpy>=1.22 in /usr/local/lib/python3.10/dist-packages (from pylance==0.9.18->lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb) (1.25.2)\n", "Requirement already satisfied: SQLAlchemy[asyncio]>=1.4.49 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (2.0.29)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.6 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (3.9.5)\n", "Collecting dataclasses-json (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb)\n", "  Downloading dataclasses_json-0.6.4-py3-none-any.whl (28 kB)\n", "Collecting deprecated>=******* (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb)\n", "  Downloading Deprecated-1.2.14-py2.py3-none-any.whl (9.6 kB)\n", "Collecting <PERSON><PERSON><PERSON><2.0.0,>=1.0.8 (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb)\n", "  Downloading dirtyjson-1.0.8-py3-none-any.whl (25 kB)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (2023.6.0)\n", "Collecting httpx (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb)\n", "  Downloading httpx-0.27.0-py3-none-any.whl (75 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m9.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting llamaindex-py-client<0.2.0,>=0.1.18 (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb)\n", "  Downloading llamaindex_py_client-0.1.18-py3-none-any.whl (136 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m136.1/136.1 kB\u001b[0m \u001b[31m15.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: nest-asyncio<2.0.0,>=1.5.8 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (1.6.0)\n", "Requirement already satisfied: networkx>=3.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (3.3)\n", "Requirement already satisfied: nltk<4.0.0,>=3.8.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (3.8.1)\n", "Collecting openai>=1.1.0 (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb)\n", "  Downloading openai-1.23.2-py3-none-any.whl (311 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m311.2/311.2 kB\u001b[0m \u001b[31m28.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (2.0.3)\n", "Requirement already satisfied: pillow>=9.0.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (9.4.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.2.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (8.2.3)\n", "Collecting tiktoken>=0.3.3 (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb)\n", "  Downloading tiktoken-0.6.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.8/1.8 MB\u001b[0m \u001b[31m59.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: typing-extensions>=4.5.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (4.11.0)\n", "Collecting typing-inspect>=0.8.0 (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Requirement already satisfied: wrapt in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (1.14.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (1.3.1)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (4.0.3)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (2024.2.2)\n", "Collecting httpcore==1.* (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m9.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (3.7)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (1.3.1)\n", "Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb)\n", "  Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m7.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (1.4.0)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (2023.12.25)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai>=1.1.0->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (1.7.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.1 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb) (2.18.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb) (2.0.7)\n", "Requirement already satisfied: decorator>=3.4.2 in /usr/local/lib/python3.10/dist-packages (from retry>=0.9.2->lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb) (4.4.2)\n", "Collecting py<2.0.0,>=1.4.26 (from retry>=0.9.2->lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb)\n", "  Downloading py-1.11.0-py2.py3-none-any.whl (98 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m98.7/98.7 kB\u001b[0m \u001b[31m12.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy[asyncio]>=1.4.49->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (3.0.3)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect>=0.8.0->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb)\n", "  Downloading marshmallow-3.21.1-py3-none-any.whl (49 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.4/49.4 kB\u001b[0m \u001b[31m5.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: packaging in /usr/local/lib/python3.10/dist-packages (from deprecation->lancedb<0.6.0,>=0.5.1->llama-index-vector-stores-lancedb) (24.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (2024.1)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (1.2.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.8.2->pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-vector-stores-lancedb) (1.16.0)\n", "Installing collected packages: ratelimiter, dirtyjson, semver, py, overrides, mypy-extensions, marshmallow, h11, deprecation, deprecated, typing-inspect, tiktoken, retry, pylance, httpcore, lancedb, httpx, dataclasses-json, openai, llamaindex-py-client, llama-index-core, llama-index-vector-stores-lancedb\n", "Successfully installed dataclasses-json-0.6.4 deprecated-1.2.14 deprecation-2.1.0 dirtyjson-1.0.8 h11-0.14.0 httpcore-1.0.5 httpx-0.27.0 lancedb-0.5.7 llama-index-core-0.10.30 llama-index-vector-stores-lancedb-0.1.3 llamaindex-py-client-0.1.18 marshmallow-3.21.1 mypy-extensions-1.0.0 openai-1.23.2 overrides-7.7.0 py-1.11.0 pylance-0.9.18 ratelimiter-1.2.0.post0 retry-0.9.2 semver-3.0.2 tiktoken-0.6.0 typing-inspect-0.9.0\n", "Collecting llama-index-multi-modal-llms-openai\n", "  Downloading llama_index_multi_modal_llms_openai-0.1.5-py3-none-any.whl (5.8 kB)\n", "Requirement already satisfied: llama-index-core<0.11.0,>=0.10.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-multi-modal-llms-openai) (0.10.30)\n", "Collecting llama-index-llms-openai<0.2.0,>=0.1.1 (from llama-index-multi-modal-llms-openai)\n", "  Downloading llama_index_llms_openai-0.1.16-py3-none-any.whl (10 kB)\n", "Requirement already satisfied: PyYAML>=6.0.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy[asyncio]>=1.4.49 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (2.0.29)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.6 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (3.9.5)\n", "Requirement already satisfied: dataclasses-json in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (0.6.4)\n", "Requirement already satisfied: deprecated>=******* in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (1.2.14)\n", "Requirement already satisfied: <PERSON><PERSON><PERSON><2.0.0,>=1.0.8 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (1.0.8)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (2023.6.0)\n", "Requirement already satisfied: httpx in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (0.27.0)\n", "Requirement already satisfied: llamaindex-py-client<0.2.0,>=0.1.18 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (0.1.18)\n", "Requirement already satisfied: nest-asyncio<2.0.0,>=1.5.8 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (1.6.0)\n", "Requirement already satisfied: networkx>=3.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (3.3)\n", "Requirement already satisfied: nltk<4.0.0,>=3.8.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (3.8.1)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (1.25.2)\n", "Requirement already satisfied: openai>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (1.23.2)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (2.0.3)\n", "Requirement already satisfied: pillow>=9.0.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (9.4.0)\n", "Requirement already satisfied: requests>=2.31.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.2.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (8.2.3)\n", "Requirement already satisfied: tiktoken>=0.3.3 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (0.6.0)\n", "Requirement already satisfied: tqdm<5.0.0,>=4.66.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (4.66.2)\n", "Requirement already satisfied: typing-extensions>=4.5.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (4.11.0)\n", "Requirement already satisfied: typing-inspect>=0.8.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (0.9.0)\n", "Requirement already satisfied: wrapt in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (1.14.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (4.0.3)\n", "Requirement already satisfied: pydantic>=1.10 in /usr/local/lib/python3.10/dist-packages (from llamaindex-py-client<0.2.0,>=0.1.18->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (2.7.0)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (2024.2.2)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (1.0.5)\n", "Requirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (3.7)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (0.14.0)\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (8.1.7)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (1.4.0)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (2023.12.25)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai>=1.1.0->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (1.7.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (2.0.7)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy[asyncio]>=1.4.49->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (3.0.3)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from typing-inspect>=0.8.0->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (1.0.0)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (3.21.1)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (2024.1)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (1.2.1)\n", "Requirement already satisfied: packaging>=17.0 in /usr/local/lib/python3.10/dist-packages (from marshmallow<4.0.0,>=3.18.0->dataclasses-json->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (24.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->llamaindex-py-client<0.2.0,>=0.1.18->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.1 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->llamaindex-py-client<0.2.0,>=0.1.18->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (2.18.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.8.2->pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-multi-modal-llms-openai) (1.16.0)\n", "Installing collected packages: llama-index-llms-openai, llama-index-multi-modal-llms-openai\n", "Successfully installed llama-index-llms-openai-0.1.16 llama-index-multi-modal-llms-openai-0.1.5\n", "Collecting llama-index-embeddings-clip\n", "  Downloading llama_index_embeddings_clip-0.1.5-py3-none-any.whl (3.2 kB)\n", "Collecting ftfy<7.0.0,>=6.1.3 (from llama-index-embeddings-clip)\n", "  Downloading ftfy-6.2.0-py3-none-any.whl (54 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m54.4/54.4 kB\u001b[0m \u001b[31m1.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: llama-index-core<0.11.0,>=0.10.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-embeddings-clip) (0.10.30)\n", "Collecting pillow<11.0.0,>=10.2.0 (from llama-index-embeddings-clip)\n", "  Downloading pillow-10.3.0-cp310-cp310-manylinux_2_28_x86_64.whl (4.5 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.5/4.5 MB\u001b[0m \u001b[31m38.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: torch<3.0.0,>=2.1.2 in /usr/local/lib/python3.10/dist-packages (from llama-index-embeddings-clip) (2.2.1+cu121)\n", "Requirement already satisfied: torchvision<0.18.0,>=0.17.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-embeddings-clip) (0.17.1+cu121)\n", "Requirement already satisfied: wcwidth<0.3.0,>=0.2.12 in /usr/local/lib/python3.10/dist-packages (from ftfy<7.0.0,>=6.1.3->llama-index-embeddings-clip) (0.2.13)\n", "Requirement already satisfied: PyYAML>=6.0.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy[asyncio]>=1.4.49 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (2.0.29)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.6 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (3.9.5)\n", "Requirement already satisfied: dataclasses-json in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (0.6.4)\n", "Requirement already satisfied: deprecated>=******* in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (1.2.14)\n", "Requirement already satisfied: <PERSON><PERSON><PERSON><2.0.0,>=1.0.8 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (1.0.8)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (2023.6.0)\n", "Requirement already satisfied: httpx in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (0.27.0)\n", "Requirement already satisfied: llamaindex-py-client<0.2.0,>=0.1.18 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (0.1.18)\n", "Requirement already satisfied: nest-asyncio<2.0.0,>=1.5.8 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (1.6.0)\n", "Requirement already satisfied: networkx>=3.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (3.3)\n", "Requirement already satisfied: nltk<4.0.0,>=3.8.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (3.8.1)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (1.25.2)\n", "Requirement already satisfied: openai>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (1.23.2)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (2.0.3)\n", "Requirement already satisfied: requests>=2.31.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.2.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (8.2.3)\n", "Requirement already satisfied: tiktoken>=0.3.3 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (0.6.0)\n", "Requirement already satisfied: tqdm<5.0.0,>=4.66.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (4.66.2)\n", "Requirement already satisfied: typing-extensions>=4.5.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (4.11.0)\n", "Requirement already satisfied: typing-inspect>=0.8.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (0.9.0)\n", "Requirement already satisfied: wrapt in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (1.14.1)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-clip) (3.13.4)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-clip) (1.12)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-clip) (3.1.3)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.1.105 (from torch<3.0.0,>=2.1.2->llama-index-embeddings-clip)\n", "  Using cached nvidia_cuda_nvrtc_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (23.7 MB)\n", "Collecting nvidia-cuda-runtime-cu12==12.1.105 (from torch<3.0.0,>=2.1.2->llama-index-embeddings-clip)\n", "  Using cached nvidia_cuda_runtime_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (823 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.1.105 (from torch<3.0.0,>=2.1.2->llama-index-embeddings-clip)\n", "  Using cached nvidia_cuda_cupti_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (14.1 MB)\n", "Collecting nvidia-cudnn-cu12==******** (from torch<3.0.0,>=2.1.2->llama-index-embeddings-clip)\n", "  Using cached nvidia_cudnn_cu12-********-py3-none-manylinux1_x86_64.whl (731.7 MB)\n", "Collecting nvidia-cublas-cu12==******** (from torch<3.0.0,>=2.1.2->llama-index-embeddings-clip)\n", "  Using cached nvidia_cublas_cu12-********-py3-none-manylinux1_x86_64.whl (410.6 MB)\n", "Collecting nvidia-cufft-cu12==********* (from torch<3.0.0,>=2.1.2->llama-index-embeddings-clip)\n", "  Using cached nvidia_cufft_cu12-*********-py3-none-manylinux1_x86_64.whl (121.6 MB)\n", "Collecting nvidia-curand-cu12==********** (from torch<3.0.0,>=2.1.2->llama-index-embeddings-clip)\n", "  Using cached nvidia_curand_cu12-**********-py3-none-manylinux1_x86_64.whl (56.5 MB)\n", "Collecting nvidia-cusolver-cu12==********** (from torch<3.0.0,>=2.1.2->llama-index-embeddings-clip)\n", "  Using cached nvidia_cusolver_cu12-**********-py3-none-manylinux1_x86_64.whl (124.2 MB)\n", "Collecting nvidia-cusparse-cu12==********** (from torch<3.0.0,>=2.1.2->llama-index-embeddings-clip)\n", "  Using cached nvidia_cusparse_cu12-**********-py3-none-manylinux1_x86_64.whl (196.0 MB)\n", "Collecting nvidia-nccl-cu12==2.19.3 (from torch<3.0.0,>=2.1.2->llama-index-embeddings-clip)\n", "  Using cached nvidia_nccl_cu12-2.19.3-py3-none-manylinux1_x86_64.whl (166.0 MB)\n", "Collecting nvidia-nvtx-cu12==12.1.105 (from torch<3.0.0,>=2.1.2->llama-index-embeddings-clip)\n", "  Using cached nvidia_nvtx_cu12-12.1.105-py3-none-manylinux1_x86_64.whl (99 kB)\n", "Requirement already satisfied: triton==2.2.0 in /usr/local/lib/python3.10/dist-packages (from torch<3.0.0,>=2.1.2->llama-index-embeddings-clip) (2.2.0)\n", "Collecting nvidia-nvjitlink-cu12 (from nvidia-cusolver-cu12==**********->torch<3.0.0,>=2.1.2->llama-index-embeddings-clip)\n", "  Using cached nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (4.0.3)\n", "Requirement already satisfied: pydantic>=1.10 in /usr/local/lib/python3.10/dist-packages (from llamaindex-py-client<0.2.0,>=0.1.18->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (2.7.0)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (2024.2.2)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (1.0.5)\n", "Requirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (3.7)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (0.14.0)\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (8.1.7)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (1.4.0)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (2023.12.25)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai>=1.1.0->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (1.7.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (2.0.7)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy[asyncio]>=1.4.49->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (3.0.3)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from typing-inspect>=0.8.0->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (1.0.0)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (3.21.1)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch<3.0.0,>=2.1.2->llama-index-embeddings-clip) (2.1.5)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (2024.1)\n", "Requirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->torch<3.0.0,>=2.1.2->llama-index-embeddings-clip) (1.3.0)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (1.2.1)\n", "Requirement already satisfied: packaging>=17.0 in /usr/local/lib/python3.10/dist-packages (from marshmallow<4.0.0,>=3.18.0->dataclasses-json->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (24.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->llamaindex-py-client<0.2.0,>=0.1.18->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.1 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->llamaindex-py-client<0.2.0,>=0.1.18->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (2.18.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.8.2->pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-embeddings-clip) (1.16.0)\n", "Installing collected packages: pillow, nvidia-nvtx-cu12, nvidia-nvjitlink-cu12, nvidia-nccl-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, ftfy, nvidia-cusparse-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12, llama-index-embeddings-clip\n", "  Attempting uninstall: pillow\n", "    Found existing installation: Pillow 9.4.0\n", "    Uninstalling Pillow-9.4.0:\n", "      Successfully uninstalled Pillow-9.4.0\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "imageio 2.31.6 requires pillow<10.1.0,>=8.3.2, but you have pillow 10.3.0 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed ftfy-6.2.0 llama-index-embeddings-clip-0.1.5 nvidia-cublas-cu12-******** nvidia-cuda-cupti-cu12-12.1.105 nvidia-cuda-nvrtc-cu12-12.1.105 nvidia-cuda-runtime-cu12-12.1.105 nvidia-cudnn-cu12-******** nvidia-cufft-cu12-********* nvidia-curand-cu12-********** nvidia-cusolver-cu12-********** nvidia-cusparse-cu12-********** nvidia-nccl-cu12-2.19.3 nvidia-nvjitlink-cu12-12.4.127 nvidia-nvtx-cu12-12.1.105 pillow-10.3.0\n"]}, {"output_type": "display_data", "data": {"application/vnd.colab-display-data+json": {"pip_warning": {"packages": ["PIL"]}, "id": "4b0c9e9fdb32483381501f71fbeed4ce"}}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Collecting git+https://github.com/openai/CLIP.git\n", "  Cloning https://github.com/openai/CLIP.git to /tmp/pip-req-build-i2bmz3mr\n", "  Running command git clone --filter=blob:none --quiet https://github.com/openai/CLIP.git /tmp/pip-req-build-i2bmz3mr\n", "  Resolved https://github.com/openai/CLIP.git to commit a1d071733d7111c9c014f024669f959182114e33\n", "  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: ftfy in /usr/local/lib/python3.10/dist-packages (from clip==1.0) (6.2.0)\n", "Requirement already satisfied: regex in /usr/local/lib/python3.10/dist-packages (from clip==1.0) (2023.12.25)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from clip==1.0) (4.66.2)\n", "Requirement already satisfied: torch in /usr/local/lib/python3.10/dist-packages (from clip==1.0) (2.2.1+cu121)\n", "Requirement already satisfied: torchvision in /usr/local/lib/python3.10/dist-packages (from clip==1.0) (0.17.1+cu121)\n", "Requirement already satisfied: wcwidth<0.3.0,>=0.2.12 in /usr/local/lib/python3.10/dist-packages (from ftfy->clip==1.0) (0.2.13)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (3.13.4)\n", "Requirement already satisfied: typing-extensions>=4.8.0 in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (4.11.0)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (1.12)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (3.3)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (3.1.3)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (2023.6.0)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (12.1.105)\n", "Requirement already satisfied: nvidia-cudnn-cu12==******** in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (********)\n", "Requirement already satisfied: nvidia-cublas-cu12==******** in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (********)\n", "Requirement already satisfied: nvidia-cufft-cu12==********* in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (*********)\n", "Requirement already satisfied: nvidia-curand-cu12==********** in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (**********)\n", "Requirement already satisfied: nvidia-cusolver-cu12==********** in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (**********)\n", "Requirement already satisfied: nvidia-cusparse-cu12==********** in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (**********)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.19.3 in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (2.19.3)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (12.1.105)\n", "Requirement already satisfied: triton==2.2.0 in /usr/local/lib/python3.10/dist-packages (from torch->clip==1.0) (2.2.0)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12 in /usr/local/lib/python3.10/dist-packages (from nvidia-cusolver-cu12==**********->torch->clip==1.0) (12.4.127)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from torchvision->clip==1.0) (1.25.2)\n", "Requirement already satisfied: pillow!=8.3.*,>=5.3.0 in /usr/local/lib/python3.10/dist-packages (from torchvision->clip==1.0) (10.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch->clip==1.0) (2.1.5)\n", "Requirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->torch->clip==1.0) (1.3.0)\n", "Building wheels for collected packages: clip\n", "  Building wheel for clip (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for clip: filename=clip-1.0-py3-none-any.whl size=1369499 sha256=0dec45dfb1bd27829c86245f9e5d0e3e15b5aa3fa618dc4c8906052d77c880dd\n", "  Stored in directory: /tmp/pip-ephem-wheel-cache-txu2nl_n/wheels/da/2b/4c/d6691fa9597aac8bb85d2ac13b112deb897d5b50f5ad9a37e4\n", "Successfully built clip\n", "Installing collected packages: clip\n", "Successfully installed clip-1.0\n", "Collecting llama-index-readers-file\n", "  Downloading llama_index_readers_file-0.1.19-py3-none-any.whl (36 kB)\n", "Requirement already satisfied: beautifulsoup4<5.0.0,>=4.12.3 in /usr/local/lib/python3.10/dist-packages (from llama-index-readers-file) (4.12.3)\n", "Requirement already satisfied: llama-index-core<0.11.0,>=0.10.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-readers-file) (0.10.30)\n", "Collecting pypdf<5.0.0,>=4.0.1 (from llama-index-readers-file)\n", "  Downloading pypdf-4.2.0-py3-none-any.whl (290 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m290.4/290.4 kB\u001b[0m \u001b[31m7.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting striprtf<0.0.27,>=0.0.26 (from llama-index-readers-file)\n", "  Downloading striprtf-0.0.26-py3-none-any.whl (6.9 kB)\n", "Requirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.10/dist-packages (from beautifulsoup4<5.0.0,>=4.12.3->llama-index-readers-file) (2.5)\n", "Requirement already satisfied: PyYAML>=6.0.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy[asyncio]>=1.4.49 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (2.0.29)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.6 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (3.9.5)\n", "Requirement already satisfied: dataclasses-json in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (0.6.4)\n", "Requirement already satisfied: deprecated>=******* in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (1.2.14)\n", "Requirement already satisfied: <PERSON><PERSON><PERSON><2.0.0,>=1.0.8 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (1.0.8)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (2023.6.0)\n", "Requirement already satisfied: httpx in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (0.27.0)\n", "Requirement already satisfied: llamaindex-py-client<0.2.0,>=0.1.18 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (0.1.18)\n", "Requirement already satisfied: nest-asyncio<2.0.0,>=1.5.8 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (1.6.0)\n", "Requirement already satisfied: networkx>=3.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (3.3)\n", "Requirement already satisfied: nltk<4.0.0,>=3.8.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (3.8.1)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (1.25.2)\n", "Requirement already satisfied: openai>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (1.23.2)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (2.0.3)\n", "Requirement already satisfied: pillow>=9.0.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (10.3.0)\n", "Requirement already satisfied: requests>=2.31.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.2.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (8.2.3)\n", "Requirement already satisfied: tiktoken>=0.3.3 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (0.6.0)\n", "Requirement already satisfied: tqdm<5.0.0,>=4.66.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (4.66.2)\n", "Requirement already satisfied: typing-extensions>=4.5.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (4.11.0)\n", "Requirement already satisfied: typing-inspect>=0.8.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (0.9.0)\n", "Requirement already satisfied: wrapt in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (1.14.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (4.0.3)\n", "Requirement already satisfied: pydantic>=1.10 in /usr/local/lib/python3.10/dist-packages (from llamaindex-py-client<0.2.0,>=0.1.18->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (2.7.0)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (2024.2.2)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (1.0.5)\n", "Requirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (3.7)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (0.14.0)\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (8.1.7)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (1.4.0)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (2023.12.25)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai>=1.1.0->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (1.7.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (2.0.7)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy[asyncio]>=1.4.49->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (3.0.3)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from typing-inspect>=0.8.0->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (1.0.0)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (3.21.1)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (2024.1)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (1.2.1)\n", "Requirement already satisfied: packaging>=17.0 in /usr/local/lib/python3.10/dist-packages (from marshmallow<4.0.0,>=3.18.0->dataclasses-json->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (24.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->llamaindex-py-client<0.2.0,>=0.1.18->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.1 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->llamaindex-py-client<0.2.0,>=0.1.18->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (2.18.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.8.2->pandas->llama-index-core<0.11.0,>=0.10.1->llama-index-readers-file) (1.16.0)\n", "Installing collected packages: striprtf, pypdf, llama-index-readers-file\n", "Successfully installed llama-index-readers-file-0.1.19 pypdf-4.2.0 striprtf-0.0.26\n"]}]}, {"cell_type": "code", "source": ["%pip install llama_index\n", "%pip install -U openai-whisper"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qNZ4yrIMpa9S", "outputId": "cad849f2-6b95-4e56-b3c1-da66eb4a89bc"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting llama_index\n", "  Downloading llama_index-0.10.30-py3-none-any.whl (6.9 kB)\n", "Collecting llama-index-agent-openai<0.3.0,>=0.1.4 (from llama_index)\n", "  Downloading llama_index_agent_openai-0.2.3-py3-none-any.whl (13 kB)\n", "Collecting llama-index-cli<0.2.0,>=0.1.2 (from llama_index)\n", "  Downloading llama_index_cli-0.1.12-py3-none-any.whl (26 kB)\n", "Requirement already satisfied: llama-index-core<0.11.0,>=0.10.30 in /usr/local/lib/python3.10/dist-packages (from llama_index) (0.10.30)\n", "Collecting llama-index-embeddings-openai<0.2.0,>=0.1.5 (from llama_index)\n", "  Downloading llama_index_embeddings_openai-0.1.8-py3-none-any.whl (6.0 kB)\n", "Collecting llama-index-indices-managed-llama-cloud<0.2.0,>=0.1.2 (from llama_index)\n", "  Downloading llama_index_indices_managed_llama_cloud-0.1.5-py3-none-any.whl (6.7 kB)\n", "Collecting llama-index-legacy<0.10.0,>=0.9.48 (from llama_index)\n", "  Downloading llama_index_legacy-0.9.48-py3-none-any.whl (2.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m25.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: llama-index-llms-openai<0.2.0,>=0.1.13 in /usr/local/lib/python3.10/dist-packages (from llama_index) (0.1.16)\n", "Requirement already satisfied: llama-index-multi-modal-llms-openai<0.2.0,>=0.1.3 in /usr/local/lib/python3.10/dist-packages (from llama_index) (0.1.5)\n", "Collecting llama-index-program-openai<0.2.0,>=0.1.3 (from llama_index)\n", "  Downloading llama_index_program_openai-0.1.6-py3-none-any.whl (5.2 kB)\n", "Collecting llama-index-question-gen-openai<0.2.0,>=0.1.2 (from llama_index)\n", "  Downloading llama_index_question_gen_openai-0.1.3-py3-none-any.whl (2.9 kB)\n", "Requirement already satisfied: llama-index-readers-file<0.2.0,>=0.1.4 in /usr/local/lib/python3.10/dist-packages (from llama_index) (0.1.19)\n", "Collecting llama-index-readers-llama-parse<0.2.0,>=0.1.2 (from llama_index)\n", "  Downloading llama_index_readers_llama_parse-0.1.4-py3-none-any.whl (2.5 kB)\n", "Requirement already satisfied: openai>=1.14.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-agent-openai<0.3.0,>=0.1.4->llama_index) (1.23.2)\n", "Requirement already satisfied: PyYAML>=6.0.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy[asyncio]>=1.4.49 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (2.0.29)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.6 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (3.9.5)\n", "Requirement already satisfied: dataclasses-json in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (0.6.4)\n", "Requirement already satisfied: deprecated>=******* in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (1.2.14)\n", "Requirement already satisfied: <PERSON><PERSON><PERSON><2.0.0,>=1.0.8 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (1.0.8)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (2023.6.0)\n", "Requirement already satisfied: httpx in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (0.27.0)\n", "Requirement already satisfied: llamaindex-py-client<0.2.0,>=0.1.18 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (0.1.18)\n", "Requirement already satisfied: nest-asyncio<2.0.0,>=1.5.8 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (1.6.0)\n", "Requirement already satisfied: networkx>=3.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (3.3)\n", "Requirement already satisfied: nltk<4.0.0,>=3.8.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (3.8.1)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (1.25.2)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (2.0.3)\n", "Requirement already satisfied: pillow>=9.0.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (10.3.0)\n", "Requirement already satisfied: requests>=2.31.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.2.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (8.2.3)\n", "Requirement already satisfied: tiktoken>=0.3.3 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (0.6.0)\n", "Requirement already satisfied: tqdm<5.0.0,>=4.66.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (4.66.2)\n", "Requirement already satisfied: typing-extensions>=4.5.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (4.11.0)\n", "Requirement already satisfied: typing-inspect>=0.8.0 in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (0.9.0)\n", "Requirement already satisfied: wrapt in /usr/local/lib/python3.10/dist-packages (from llama-index-core<0.11.0,>=0.10.30->llama_index) (1.14.1)\n", "Requirement already satisfied: beautifulsoup4<5.0.0,>=4.12.3 in /usr/local/lib/python3.10/dist-packages (from llama-index-readers-file<0.2.0,>=0.1.4->llama_index) (4.12.3)\n", "Requirement already satisfied: pypdf<5.0.0,>=4.0.1 in /usr/local/lib/python3.10/dist-packages (from llama-index-readers-file<0.2.0,>=0.1.4->llama_index) (4.2.0)\n", "Requirement already satisfied: striprtf<0.0.27,>=0.0.26 in /usr/local/lib/python3.10/dist-packages (from llama-index-readers-file<0.2.0,>=0.1.4->llama_index) (0.0.26)\n", "Collecting llama-parse<0.5.0,>=0.4.0 (from llama-index-readers-llama-parse<0.2.0,>=0.1.2->llama_index)\n", "  Downloading llama_parse-0.4.1-py3-none-any.whl (7.3 kB)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.30->llama_index) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.30->llama_index) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.30->llama_index) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.30->llama_index) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.30->llama_index) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.6->llama-index-core<0.11.0,>=0.10.30->llama_index) (4.0.3)\n", "Requirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.10/dist-packages (from beautifulsoup4<5.0.0,>=4.12.3->llama-index-readers-file<0.2.0,>=0.1.4->llama_index) (2.5)\n", "Requirement already satisfied: pydantic>=1.10 in /usr/local/lib/python3.10/dist-packages (from llamaindex-py-client<0.2.0,>=0.1.18->llama-index-core<0.11.0,>=0.10.30->llama_index) (2.7.0)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.30->llama_index) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.30->llama_index) (2024.2.2)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.30->llama_index) (1.0.5)\n", "Requirement already satisfied: idna in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.30->llama_index) (3.7)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx->llama-index-core<0.11.0,>=0.10.30->llama_index) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx->llama-index-core<0.11.0,>=0.10.30->llama_index) (0.14.0)\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.30->llama_index) (8.1.7)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.30->llama_index) (1.4.0)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.10/dist-packages (from nltk<4.0.0,>=3.8.1->llama-index-core<0.11.0,>=0.10.30->llama_index) (2023.12.25)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai>=1.14.0->llama-index-agent-openai<0.3.0,>=0.1.4->llama_index) (1.7.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->llama-index-core<0.11.0,>=0.10.30->llama_index) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->llama-index-core<0.11.0,>=0.10.30->llama_index) (2.0.7)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy[asyncio]>=1.4.49->llama-index-core<0.11.0,>=0.10.30->llama_index) (3.0.3)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from typing-inspect>=0.8.0->llama-index-core<0.11.0,>=0.10.30->llama_index) (1.0.0)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->llama-index-core<0.11.0,>=0.10.30->llama_index) (3.21.1)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.30->llama_index) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.30->llama_index) (2023.4)\n", "Requirement already satisfied: tzdata>=2022.1 in /usr/local/lib/python3.10/dist-packages (from pandas->llama-index-core<0.11.0,>=0.10.30->llama_index) (2024.1)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx->llama-index-core<0.11.0,>=0.10.30->llama_index) (1.2.1)\n", "Requirement already satisfied: packaging>=17.0 in /usr/local/lib/python3.10/dist-packages (from marshmallow<4.0.0,>=3.18.0->dataclasses-json->llama-index-core<0.11.0,>=0.10.30->llama_index) (24.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->llamaindex-py-client<0.2.0,>=0.1.18->llama-index-core<0.11.0,>=0.10.30->llama_index) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.1 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->llamaindex-py-client<0.2.0,>=0.1.18->llama-index-core<0.11.0,>=0.10.30->llama_index) (2.18.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.8.2->pandas->llama-index-core<0.11.0,>=0.10.30->llama_index) (1.16.0)\n", "Installing collected packages: llama-index-legacy, llama-parse, llama-index-indices-managed-llama-cloud, llama-index-embeddings-openai, llama-index-readers-llama-parse, llama-index-cli, llama-index-agent-openai, llama-index-program-openai, llama-index-question-gen-openai, llama_index\n", "Successfully installed llama-index-agent-openai-0.2.3 llama-index-cli-0.1.12 llama-index-embeddings-openai-0.1.8 llama-index-indices-managed-llama-cloud-0.1.5 llama-index-legacy-0.9.48 llama-index-program-openai-0.1.6 llama-index-question-gen-openai-0.1.3 llama-index-readers-llama-parse-0.1.4 llama-parse-0.4.1 llama_index-0.10.30\n", "Collecting openai-whisper\n", "  Downloading openai-whisper-20231117.tar.gz (798 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m798.6/798.6 kB\u001b[0m \u001b[31m5.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: triton<3,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from openai-whisper) (2.2.0)\n", "Requirement already satisfied: numba in /usr/local/lib/python3.10/dist-packages (from openai-whisper) (0.58.1)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from openai-whisper) (1.25.2)\n", "Requirement already satisfied: torch in /usr/local/lib/python3.10/dist-packages (from openai-whisper) (2.2.1+cu121)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from openai-whisper) (4.66.2)\n", "Requirement already satisfied: more-itertools in /usr/local/lib/python3.10/dist-packages (from openai-whisper) (10.1.0)\n", "Requirement already satisfied: tiktoken in /usr/local/lib/python3.10/dist-packages (from openai-whisper) (0.6.0)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from triton<3,>=2.0.0->openai-whisper) (3.13.4)\n", "Requirement already satisfied: llvmlite<0.42,>=0.41.0dev0 in /usr/local/lib/python3.10/dist-packages (from numba->openai-whisper) (0.41.1)\n", "Requirement already satisfied: regex>=2022.1.18 in /usr/local/lib/python3.10/dist-packages (from tiktoken->openai-whisper) (2023.12.25)\n", "Requirement already satisfied: requests>=2.26.0 in /usr/local/lib/python3.10/dist-packages (from tiktoken->openai-whisper) (2.31.0)\n", "Requirement already satisfied: typing-extensions>=4.8.0 in /usr/local/lib/python3.10/dist-packages (from torch->openai-whisper) (4.11.0)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch->openai-whisper) (1.12)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from torch->openai-whisper) (3.3)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch->openai-whisper) (3.1.3)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.10/dist-packages (from torch->openai-whisper) (2023.6.0)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch->openai-whisper) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch->openai-whisper) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch->openai-whisper) (12.1.105)\n", "Requirement already satisfied: nvidia-cudnn-cu12==******** in /usr/local/lib/python3.10/dist-packages (from torch->openai-whisper) (********)\n", "Requirement already satisfied: nvidia-cublas-cu12==******** in /usr/local/lib/python3.10/dist-packages (from torch->openai-whisper) (********)\n", "Requirement already satisfied: nvidia-cufft-cu12==********* in /usr/local/lib/python3.10/dist-packages (from torch->openai-whisper) (*********)\n", "Requirement already satisfied: nvidia-curand-cu12==********** in /usr/local/lib/python3.10/dist-packages (from torch->openai-whisper) (**********)\n", "Requirement already satisfied: nvidia-cusolver-cu12==********** in /usr/local/lib/python3.10/dist-packages (from torch->openai-whisper) (**********)\n", "Requirement already satisfied: nvidia-cusparse-cu12==********** in /usr/local/lib/python3.10/dist-packages (from torch->openai-whisper) (**********)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.19.3 in /usr/local/lib/python3.10/dist-packages (from torch->openai-whisper) (2.19.3)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch->openai-whisper) (12.1.105)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12 in /usr/local/lib/python3.10/dist-packages (from nvidia-cusolver-cu12==**********->torch->openai-whisper) (12.4.127)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken->openai-whisper) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken->openai-whisper) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken->openai-whisper) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken->openai-whisper) (2024.2.2)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch->openai-whisper) (2.1.5)\n", "Requirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->torch->openai-whisper) (1.3.0)\n", "Building wheels for collected packages: openai-whisper\n", "  Building wheel for openai-whisper (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for openai-whisper: filename=openai_whisper-20231117-py3-none-any.whl size=801358 sha256=397b83347b361a46eb32640b2e64cf144eb463a09d29cad06af5c2a446e7b7fe\n", "  Stored in directory: /root/.cache/pip/wheels/d0/85/e1/9361b4cbea7dd4b7f6702fa4c3afc94877952eeb2b62f45f56\n", "Successfully built openai-whisper\n", "Installing collected packages: openai-whisper\n", "Successfully installed openai-whisper-20231117\n"]}]}, {"cell_type": "code", "source": ["%pip install lancedb\n", "%pip install moviepy\n", "%pip install pytube\n", "%pip install pydub\n", "%pip install SpeechRecognition\n", "%pip install ffmpeg-python\n", "%pip install soundfile\n", "%pip install torch torchvision\n", "%pip install matplotlib scikit-image\n", "%pip install ftfy regex tqdm"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "Y6xmSWjkppBJ", "outputId": "637cab2b-a090-47e5-b4e3-b88428ef65c2"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: lancedb in /usr/local/lib/python3.10/dist-packages (0.5.7)\n", "Requirement already satisfied: deprecation in /usr/local/lib/python3.10/dist-packages (from lancedb) (2.1.0)\n", "Requirement already satisfied: pylance==0.9.18 in /usr/local/lib/python3.10/dist-packages (from lancedb) (0.9.18)\n", "Requirement already satisfied: ratelimiter~=1.0 in /usr/local/lib/python3.10/dist-packages (from lancedb) (1.2.0.post0)\n", "Requirement already satisfied: retry>=0.9.2 in /usr/local/lib/python3.10/dist-packages (from lancedb) (0.9.2)\n", "Requirement already satisfied: tqdm>=4.27.0 in /usr/local/lib/python3.10/dist-packages (from lancedb) (4.66.2)\n", "Requirement already satisfied: pydantic>=1.10 in /usr/local/lib/python3.10/dist-packages (from lancedb) (2.7.0)\n", "Requirement already satisfied: attrs>=21.3.0 in /usr/local/lib/python3.10/dist-packages (from lancedb) (23.2.0)\n", "Requirement already satisfied: semver>=3.0 in /usr/local/lib/python3.10/dist-packages (from lancedb) (3.0.2)\n", "Requirement already satisfied: cachetools in /usr/local/lib/python3.10/dist-packages (from lancedb) (5.3.3)\n", "Requirement already satisfied: pyyaml>=6.0 in /usr/local/lib/python3.10/dist-packages (from lancedb) (6.0.1)\n", "Requirement already satisfied: click>=8.1.7 in /usr/local/lib/python3.10/dist-packages (from lancedb) (8.1.7)\n", "Requirement already satisfied: requests>=2.31.0 in /usr/local/lib/python3.10/dist-packages (from lancedb) (2.31.0)\n", "Requirement already satisfied: overrides>=0.7 in /usr/local/lib/python3.10/dist-packages (from lancedb) (7.7.0)\n", "Requirement already satisfied: pyarrow>=12 in /usr/local/lib/python3.10/dist-packages (from pylance==0.9.18->lancedb) (14.0.2)\n", "Requirement already satisfied: numpy>=1.22 in /usr/local/lib/python3.10/dist-packages (from pylance==0.9.18->lancedb) (1.25.2)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->lancedb) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.18.1 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->lancedb) (2.18.1)\n", "Requirement already satisfied: typing-extensions>=4.6.1 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.10->lancedb) (4.11.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->lancedb) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->lancedb) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->lancedb) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests>=2.31.0->lancedb) (2024.2.2)\n", "Requirement already satisfied: decorator>=3.4.2 in /usr/local/lib/python3.10/dist-packages (from retry>=0.9.2->lancedb) (4.4.2)\n", "Requirement already satisfied: py<2.0.0,>=1.4.26 in /usr/local/lib/python3.10/dist-packages (from retry>=0.9.2->lancedb) (1.11.0)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.10/dist-packages (from deprecation->lancedb) (24.0)\n", "Requirement already satisfied: moviepy in /usr/local/lib/python3.10/dist-packages (1.0.3)\n", "Requirement already satisfied: decorator<5.0,>=4.0.2 in /usr/local/lib/python3.10/dist-packages (from moviepy) (4.4.2)\n", "Requirement already satisfied: tqdm<5.0,>=4.11.2 in /usr/local/lib/python3.10/dist-packages (from moviepy) (4.66.2)\n", "Requirement already satisfied: requests<3.0,>=2.8.1 in /usr/local/lib/python3.10/dist-packages (from moviepy) (2.31.0)\n", "Requirement already satisfied: proglog<=1.0.0 in /usr/local/lib/python3.10/dist-packages (from moviepy) (0.1.10)\n", "Requirement already satisfied: numpy>=1.17.3 in /usr/local/lib/python3.10/dist-packages (from moviepy) (1.25.2)\n", "Requirement already satisfied: imageio<3.0,>=2.5 in /usr/local/lib/python3.10/dist-packages (from moviepy) (2.31.6)\n", "Requirement already satisfied: imageio-ffmpeg>=0.2.0 in /usr/local/lib/python3.10/dist-packages (from moviepy) (0.4.9)\n", "Collecting pillow<10.1.0,>=8.3.2 (from imageio<3.0,>=2.5->moviepy)\n", "  Downloading Pillow-10.0.1-cp310-cp310-manylinux_2_28_x86_64.whl (3.6 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.6/3.6 MB\u001b[0m \u001b[31m8.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: setuptools in /usr/local/lib/python3.10/dist-packages (from imageio-ffmpeg>=0.2.0->moviepy) (67.7.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0,>=2.8.1->moviepy) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3.0,>=2.8.1->moviepy) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3.0,>=2.8.1->moviepy) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3.0,>=2.8.1->moviepy) (2024.2.2)\n", "Installing collected packages: pillow\n", "  Attempting uninstall: pillow\n", "    Found existing installation: pillow 10.3.0\n", "    Uninstalling pillow-10.3.0:\n", "      Successfully uninstalled pillow-10.3.0\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "llama-index-embeddings-clip 0.1.5 requires pillow<11.0.0,>=10.2.0, but you have pillow 10.0.1 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed pillow-10.0.1\n"]}, {"output_type": "display_data", "data": {"application/vnd.colab-display-data+json": {"pip_warning": {"packages": ["PIL"]}, "id": "f74407c854394029baf918b916b01d7d"}}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Collecting pytube\n", "  Downloading pytube-15.0.0-py3-none-any.whl (57 kB)\n", "\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/57.6 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m57.6/57.6 kB\u001b[0m \u001b[31m1.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: pytube\n", "Successfully installed pytube-15.0.0\n", "Collecting pydub\n", "  Downloading pydub-0.25.1-py2.py3-none-any.whl (32 kB)\n", "Installing collected packages: pydub\n", "Successfully installed pydub-0.25.1\n", "Collecting SpeechRecognition\n", "  Downloading SpeechRecognition-3.10.3-py2.py3-none-any.whl (32.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m32.8/32.8 MB\u001b[0m \u001b[31m40.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: requests>=2.26.0 in /usr/local/lib/python3.10/dist-packages (from SpeechRecognition) (2.31.0)\n", "Requirement already satisfied: typing-extensions in /usr/local/lib/python3.10/dist-packages (from SpeechRecognition) (4.11.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->SpeechRecognition) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->SpeechRecognition) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->SpeechRecognition) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->SpeechRecognition) (2024.2.2)\n", "Installing collected packages: SpeechRecognition\n", "Successfully installed SpeechRecognition-3.10.3\n", "Collecting ffmpeg-python\n", "  Downloading ffmpeg_python-0.2.0-py3-none-any.whl (25 kB)\n", "Requirement already satisfied: future in /usr/local/lib/python3.10/dist-packages (from ffmpeg-python) (0.18.3)\n", "Installing collected packages: ffmpeg-python\n", "Successfully installed ffmpeg-python-0.2.0\n", "Requirement already satisfied: soundfile in /usr/local/lib/python3.10/dist-packages (0.12.1)\n", "Requirement already satisfied: cffi>=1.0 in /usr/local/lib/python3.10/dist-packages (from soundfile) (1.16.0)\n", "Requirement already satisfied: pycparser in /usr/local/lib/python3.10/dist-packages (from cffi>=1.0->soundfile) (2.22)\n", "Requirement already satisfied: torch in /usr/local/lib/python3.10/dist-packages (2.2.1+cu121)\n", "Requirement already satisfied: torchvision in /usr/local/lib/python3.10/dist-packages (0.17.1+cu121)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from torch) (3.13.4)\n", "Requirement already satisfied: typing-extensions>=4.8.0 in /usr/local/lib/python3.10/dist-packages (from torch) (4.11.0)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch) (1.12)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from torch) (3.3)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch) (3.1.3)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.10/dist-packages (from torch) (2023.6.0)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch) (12.1.105)\n", "Requirement already satisfied: nvidia-cudnn-cu12==******** in /usr/local/lib/python3.10/dist-packages (from torch) (********)\n", "Requirement already satisfied: nvidia-cublas-cu12==******** in /usr/local/lib/python3.10/dist-packages (from torch) (********)\n", "Requirement already satisfied: nvidia-cufft-cu12==********* in /usr/local/lib/python3.10/dist-packages (from torch) (*********)\n", "Requirement already satisfied: nvidia-curand-cu12==********** in /usr/local/lib/python3.10/dist-packages (from torch) (**********)\n", "Requirement already satisfied: nvidia-cusolver-cu12==********** in /usr/local/lib/python3.10/dist-packages (from torch) (**********)\n", "Requirement already satisfied: nvidia-cusparse-cu12==********** in /usr/local/lib/python3.10/dist-packages (from torch) (**********)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.19.3 in /usr/local/lib/python3.10/dist-packages (from torch) (2.19.3)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.1.105 in /usr/local/lib/python3.10/dist-packages (from torch) (12.1.105)\n", "Requirement already satisfied: triton==2.2.0 in /usr/local/lib/python3.10/dist-packages (from torch) (2.2.0)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12 in /usr/local/lib/python3.10/dist-packages (from nvidia-cusolver-cu12==**********->torch) (12.4.127)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from torchvision) (1.25.2)\n", "Requirement already satisfied: pillow!=8.3.*,>=5.3.0 in /usr/local/lib/python3.10/dist-packages (from torchvision) (10.0.1)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch) (2.1.5)\n", "Requirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->torch) (1.3.0)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.10/dist-packages (3.7.1)\n", "Requirement already satisfied: scikit-image in /usr/local/lib/python3.10/dist-packages (0.19.3)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (1.2.1)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (4.51.0)\n", "Requirement already satisfied: kiwisolver>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (1.4.5)\n", "Requirement already satisfied: numpy>=1.20 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (1.25.2)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (24.0)\n", "Requirement already satisfied: pillow>=6.2.0 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (10.0.1)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (3.1.2)\n", "Requirement already satisfied: python-dateutil>=2.7 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (2.8.2)\n", "Requirement already satisfied: scipy>=1.4.1 in /usr/local/lib/python3.10/dist-packages (from scikit-image) (1.11.4)\n", "Requirement already satisfied: networkx>=2.2 in /usr/local/lib/python3.10/dist-packages (from scikit-image) (3.3)\n", "Requirement already satisfied: imageio>=2.4.1 in /usr/local/lib/python3.10/dist-packages (from scikit-image) (2.31.6)\n", "Requirement already satisfied: tifffile>=2019.7.26 in /usr/local/lib/python3.10/dist-packages (from scikit-image) (2024.4.18)\n", "Requirement already satisfied: PyWavelets>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from scikit-image) (1.6.0)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.7->matplotlib) (1.16.0)\n", "Requirement already satisfied: ftfy in /usr/local/lib/python3.10/dist-packages (6.2.0)\n", "Requirement already satisfied: regex in /usr/local/lib/python3.10/dist-packages (2023.12.25)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (4.66.2)\n", "Requirement already satisfied: wcwidth<0.3.0,>=0.2.12 in /usr/local/lib/python3.10/dist-packages (from ftfy) (0.2.13)\n"]}]}, {"cell_type": "markdown", "source": ["ffmpeg-library enables you to use FFmpeg in Python to manipulate various media files for different purposes like building comprehensive multimedia applications, preprocessing media files.\n", "\n", "MoviePy is a Python library for video editing, enabling cutting, concatenations, title insertions, video compositing, and effects like animations or color grading.\n", "\n", "Pytube is a Python library used for downloading videos from YouTube. It supports downloading in various formats, resolutions, and also direct audio extraction.\n", "\n", "\n", "Pydub is a Python library for audio manipulation, enabling easy loading,\n", "editing, and exporting of audio files in various formats with minimal code.\n", "\n", "The SpeechRecognition library in Python allows you to convert spoken language into text using various engines and APIs, such as Google Speech Recognition, IBM Speech to Text, etc.\n", "\n", "\n", "SoundFile is a Python library for reading from and writing to audio files, supporting many formats through the libsndfile library, ideal for high-quality audio processing.\n", "\n", "FTFY (Fix Text For You) is a Python library that fixes broken Unicode text and mojibake (garbled text due to encoding issues), making text legible again.\n", "\n", "OpenAI Whisper is a robust, multilingual speech recognition model developed by OpenAI. It converts speech into text and supports various languages with high accuracy.\n", "\n", "pprint is a Python module that provides a capability to \"pretty-print\" complex data structures in a well-formatted and more readable way than the basic print function."], "metadata": {"id": "tMlqUqibp0ji"}}, {"cell_type": "code", "source": ["from moviepy.editor import VideoFileClip\n", "from pathlib import Path\n", "import speech_recognition as sr\n", "from pytube import YouTube\n", "from pprint import pprint\n", "from PIL import Image\n", "import matplotlib.pyplot as plt"], "metadata": {"id": "igmrjXU6pwhu"}, "execution_count": 4, "outputs": []}, {"cell_type": "code", "source": ["import os\n", "from google.colab import userdata\n", "OPENAI_API_TOKEN=userdata.get('OPENAI_API_KEY')\n", "os.environ[\"OPENAI_API_KEY\"] = OPENAI_API_TOKEN"], "metadata": {"id": "ukX3ASTKqNDw"}, "execution_count": 5, "outputs": []}, {"cell_type": "code", "source": ["import os\n", "print(os.getcwd())"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TjxaH7FwqRGQ", "outputId": "ebaf140c-15b1-40db-e50f-69d441bb9aa1"}, "execution_count": 9, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["/content\n"]}]}, {"cell_type": "code", "source": ["video_url=\"https://youtu.be/3dhcmeOTZ_Q\""], "metadata": {"id": "0dA6Lv4Hqp26"}, "execution_count": 10, "outputs": []}, {"cell_type": "code", "source": ["output_video_path = \"/content/video_data/\""], "metadata": {"id": "0TzZx3dbqrwq"}, "execution_count": 11, "outputs": []}, {"cell_type": "code", "source": ["# from the video i am going to collect images,audio,text\n", "output_folder = \"/content/mixed_data/\"\n", "output_audio_path = \"/content/mixed_data/output_audio.wav\""], "metadata": {"id": "bTx05t7bqcFv"}, "execution_count": 13, "outputs": []}, {"cell_type": "code", "source": ["!mkdir mixed_data"], "metadata": {"id": "PTzo50Y6qtmA"}, "execution_count": 14, "outputs": []}, {"cell_type": "code", "source": ["filepath=output_video_path + \"input_vid.mp4\"\n", "print(filepath)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "n9SkpcGgq--g", "outputId": "4fc30558-b6f9-493b-8a5a-403d6f1e10ae"}, "execution_count": 15, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["/content/video_data/input_vid.mp4\n"]}]}, {"cell_type": "code", "source": ["from pytube import YouTube\n", "def download_video(url,output_path):\n", "  yt = YouTube(url)\n", "  metadata = {\"Author\": yt.author, \"Title\": yt.title, \"Views\": yt.views}\n", "\n", "  yt.streams.get_highest_resolution().download(\n", "        output_path=output_path, filename=\"input_vid.mp4\"\n", "    )\n", "  return metadata"], "metadata": {"id": "dwfB_9uhrB2F"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["from moviepy.editor import VideoFileClip\n", "def video_to_images(video_path,output_folder):\n", "  clip=VideoFileClip(video_path)\n", "  clip.write_images_sequence(\n", "      os.path.join(output_folder,\"frame%04d.png\"),fps=0.2\n", "  )"], "metadata": {"id": "-lOX4wuBr8N6"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": ["def video_to_audio(video_path,output_audio_path):\n", "  clip=VideoFileClip(video_path)\n", "  audio=clip.audio\n", "  audio.write_audiofile(output_audio_path)"], "metadata": {"id": "0HPUIQSFsMkh"}, "execution_count": 18, "outputs": []}, {"cell_type": "code", "source": ["def audio_to_text(audio_path):\n", "  recognizer=sr.Recognizer()\n", "  audio=sr.AudioFile(audio_path)\n", "\n", "  with audio as source:\n", "    audio_data=recognizer.record(source)\n", "\n", "    try:\n", "\n", "      #recognize the speech\n", "      text = recognizer.recognize_whisper(audio_data)\n", "\n", "    except sr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:\n", "      print(\"Speech recognition could not understand the audio.\")\n", "  return text"], "metadata": {"id": "_p39w53ZsRb5"}, "execution_count": 19, "outputs": []}, {"cell_type": "code", "source": ["video_url"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "RZnTqV_fslb2", "outputId": "e043f6f5-f3d3-4031-dae2-e260004fcb02"}, "execution_count": 20, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'https://youtu.be/3dhcmeOTZ_Q'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 20}]}, {"cell_type": "code", "source": ["output_video_path"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "KU1B6rEGsnVt", "outputId": "0bf343b3-b008-491f-c705-1d2d363476aa"}, "execution_count": 21, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'/content/video_data/'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 21}]}, {"cell_type": "code", "source": ["metadata_vid = download_video(video_url, output_video_path)"], "metadata": {"id": "RblUwfbJshSJ"}, "execution_count": 22, "outputs": []}, {"cell_type": "code", "source": ["metadata_vid"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gwZJpGH8ssiM", "outputId": "3051ee60-9337-43ec-8698-9c4ccf6d9e6b"}, "execution_count": 23, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'Author': '3-Minute Data Science',\n", " 'Title': 'Linear Regression in 3 Minutes',\n", " 'Views': 7286}"]}, "metadata": {}, "execution_count": 23}]}, {"cell_type": "code", "source": ["video_to_images(filepath,output_folder)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "JbtVwXvgsqD8", "outputId": "5e51c2e6-cbea-4ad8-e76e-c444eb027bfc"}, "execution_count": 24, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Moviepy - Writing frames /content/mixed_data/frame%04d.png.\n"]}, {"output_type": "stream", "name": "stderr", "text": ["                                                            "]}, {"output_type": "stream", "name": "stdout", "text": ["Moviepy - Done writing frames /content/mixed_data/frame%04d.png.\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\r"]}]}, {"cell_type": "code", "source": ["filepath"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "XeIiBcBXs9fu", "outputId": "2dc35766-49b3-43e5-fc58-3128d316c97e"}, "execution_count": 25, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'/content/video_data/input_vid.mp4'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 25}]}, {"cell_type": "code", "source": ["output_audio_path"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "hhf-ckBDtAAe", "outputId": "4a97db0f-737a-440a-d555-e11f2135e538"}, "execution_count": 26, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'/content/mixed_data/output_audio.wav'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 26}]}, {"cell_type": "code", "source": ["video_to_audio(filepath,output_audio_path)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lhfIDRJLswtx", "outputId": "4d3e1a7e-c431-4590-ceb3-bbd4d680db13"}, "execution_count": 27, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["MoviePy - Writing audio in /content/mixed_data/output_audio.wav\n"]}, {"output_type": "stream", "name": "stderr", "text": ["                                                                      "]}, {"output_type": "stream", "name": "stdout", "text": ["MoviePy - Done.\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\r"]}]}, {"cell_type": "code", "source": ["text_data=audio_to_text(output_audio_path)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "GGIEgadAtCaq", "outputId": "49523efa-e6e1-4d56-9fcd-f25c4b377f10"}, "execution_count": 28, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["100%|████████████████████████████████████████| 139M/139M [00:01<00:00, 133MiB/s]\n"]}]}, {"cell_type": "code", "source": ["text_data"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 162}, "id": "TCEnFoCPtHq8", "outputId": "df721248-ec0e-4747-c6bc-34f254354d27"}, "execution_count": 29, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["\" Lanyard regression is a statistical technique for modeling the relationship between an output variable and one or more input variables. In layman's terms, think of it as fitting a line through some data points as shown here, so you can make predictions on unknown data, assuming there is a linear relationship between the variables. You might be familiar with the linear function y equals mx plus b, where y is the output variable, also called the dependent variable. You may also see expressed as f of x, the function of the input variable. x on the other hand, would serve as the input variable, also called the independent variable. It's likely you'll see the coefficients m and b expressed as beta 1 and beta 0 respectively. So what do the m and b coefficients do? The m or beta 1 coefficient controls the slope of the line. The b or the beta 0 controls the intercept of the line. In machine learning, we also know it as the bias. These two coefficients are what we are solving for in linear regression. We can also extend to multiple input variables, so x1, x2, x3, with beta 1, beta 2, and beta 3, and so on, acting as slopes for each of those variables. In these higher dimensions, you would visualize the linear regression as a hyperplane. So how do we fit the line to these points? Well, you'll notice that there's these differences between the points and the line, these little red segments, these are called residuals. They are the differences between the data points and the predictions the line would produce. Take each of these residuals and square them. These are the squared errors, and notice that the large of the residuals are, the more amplified area of the squares are. If we total the areas of all of these squares for a given line, we will get the sum of the squared error, and this is known as our loss function. We need to find the beta 0 and beta 1 coefficients that will minimize that sum of squared error. The coefficients can be solved with a variety of techniques ranging from matrix decomposition to gradient descent, which is depicted right here. Thankfully, a lot of libraries are available to do this for us, and we will deep dive into these topics in other videos. To validate a linear regression, there are a number of techniques. Machine learning practitioners will often take a third of the data and put it into the test data set. The remaining two thirds will become the training data set. The training data set will then be used to fit the regression line. The test data set will then be used to validate the regression line. This is done to make sure that the regression performs well on data it has not seen before. The tricks used to evaluate the linear regression vary from the R square, standard error of the estimate, prediction intervals, as well as statistical significance. These are topics we will cover in future videos. If you enjoyed this video, please like and subscribe. Look at my two O'Reilly books, Essential Math for Data Science, and getting started with SQL. Chapter five of Essential Math for Data Science actually covers linear regression and much more depth. If you want live instruction, I also do teach on the O'Reilly platform. Promotional link below. I teach classes including machine learning from scratch, probability, and SQL. Comment on what topics you would like to see next, and I will see you again on 3 Minute Data Science.\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 29}]}, {"cell_type": "code", "source": ["with open(output_folder + \"output_text.txt\", \"w\") as file:\n", "        file.write(text_data)\n", "print(\"Text data saved to file\")\n", "file.close()\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "EgEEv89ptdHp", "outputId": "d9ceef12-1119-4595-d077-925034701218"}, "execution_count": 30, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Text data saved to file\n"]}]}, {"cell_type": "code", "source": ["os.remove(output_audio_path)\n", "print(\"Audio file removed\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zse424_3tl9a", "outputId": "240eb1ac-2384-4ef4-e11b-3f31e8af11a5"}, "execution_count": 31, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Audio file removed\n"]}]}, {"cell_type": "code", "source": ["#process the video\n", "#image\n", "#text"], "metadata": {"id": "7KB6YBHJuCLt"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from llama_index.core.indices import MultiModalVectorStoreIndex\n", "from llama_index.core import SimpleDirectoryReader\n", "from llama_index.core import StorageContext"], "metadata": {"id": "vj4OUtZluIGG"}, "execution_count": 32, "outputs": []}, {"cell_type": "code", "source": ["from llama_index.vector_stores.lancedb import LanceDBVectorStore"], "metadata": {"id": "kBBDEuXUutl5"}, "execution_count": 33, "outputs": []}, {"cell_type": "code", "source": ["text_store=LanceDBVectorStore(uri=\"lancedb\",table_name=\"text_collection\")\n", "image_store=LanceDBVectorStore(uri=\"lancedb\",table_name=\"image_collection\")"], "metadata": {"id": "xUv_t8vMuxYK"}, "execution_count": 34, "outputs": []}, {"cell_type": "code", "source": ["storage_context=StorageContext.from_defaults(vector_store=text_store,image_store=image_store)"], "metadata": {"id": "Ua0JXObmvRYN"}, "execution_count": 35, "outputs": []}, {"cell_type": "code", "source": ["output_folder"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "KCicDH2WvZvQ", "outputId": "8a22d126-c409-49fe-e68b-bde9f5edd5d0"}, "execution_count": 36, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'/content/mixed_data/'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 36}]}, {"cell_type": "code", "source": ["documents=SimpleDirectoryReader(output_folder).load_data()"], "metadata": {"id": "_B-UYzwtvXKq"}, "execution_count": 37, "outputs": []}, {"cell_type": "code", "source": ["index = MultiModalVectorStoreIndex.from_documents(documents,storage_context=storage_context)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "BbaU5Noqvdyk", "outputId": "6ea2eefc-b4fb-4eb6-f93b-17044a47b3fc"}, "execution_count": 38, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["100%|███████████████████████████████████████| 338M/338M [00:05<00:00, 69.7MiB/s]\n"]}]}, {"cell_type": "code", "source": ["retriever_engine=index.as_retriever(similarity_top_k=1, image_similarity_top_k=5)"], "metadata": {"id": "v5vZLg_-vm2o"}, "execution_count": 56, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "BQ2viUQuvv8K"}, "execution_count": 40, "outputs": []}, {"cell_type": "code", "source": ["from llama_index.core.response.notebook_utils import display_source_node\n", "from llama_index.core.schema import ImageNode"], "metadata": {"id": "3hTtlvjav2fw"}, "execution_count": 57, "outputs": []}, {"cell_type": "code", "source": ["def retrieve(retriever_engine, query_str):\n", "    retrieval_results = retriever_engine.retrieve(query_str)\n", "\n", "    retrieved_image = []\n", "    retrieved_text = []\n", "    for res_node in retrieval_results:\n", "        if isinstance(res_node.node, ImageNode):\n", "            retrieved_image.append(res_node.node.metadata[\"file_path\"])\n", "        else:\n", "            display_source_node(res_node, source_length=200)\n", "            retrieved_text.append(res_node.text)\n", "\n", "    return retrieved_image, retrieved_text"], "metadata": {"id": "3c5AB1KWv3yv"}, "execution_count": 58, "outputs": []}, {"cell_type": "code", "source": ["query=\"can you tell me what is linear regression? explain equation of the multiple linear regression?\""], "metadata": {"id": "LZHW-10jwEla"}, "execution_count": 84, "outputs": []}, {"cell_type": "code", "source": ["img,text=retrieve(retriever_engine,query)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 98}, "id": "byH2Aq95wK1B", "outputId": "048af396-0548-4ae2-ccb6-cfc00751a975"}, "execution_count": 85, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Markdown object>"], "text/markdown": "**Node ID:** aec6ceb6-129d-43e5-b1c1-921016db203f<br>**Similarity:** 0.7475849986076355<br>**Text:** Lanyard regression is a statistical technique for modeling the relationship between an output variable and one or more input variables. In layman's terms, think of it as fitting a line through some...<br>"}, "metadata": {}}]}, {"cell_type": "code", "source": ["import matplotlib.pyplot as plt\n", "def plot_images(images_path):\n", "  images_shown = 0\n", "  plt.figure(figsize=(16, 9))\n", "  for img_path in images_path:\n", "        if os.path.isfile(img_path):\n", "            image = Image.open(img_path)\n", "\n", "            plt.subplot(2, 3, images_shown + 1)\n", "            plt.imshow(image)\n", "            plt.xticks([])\n", "            plt.yticks([])\n", "\n", "            images_shown += 1\n", "            if images_shown >= 5:\n", "                break"], "metadata": {"id": "HnCjdTmnwSVJ"}, "execution_count": 86, "outputs": []}, {"cell_type": "code", "source": ["plot_images(img)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 532}, "id": "ovyDQLk-wkcS", "outputId": "12fd36b6-c601-4f0b-e306-69d441fb3b31"}, "execution_count": 87, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1600x900 with 5 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["qa_tmpl_str=(\n", "    \"Based on the provided information, including relevant images and retrieved context from the video, \\\n", "    accurately and precisely answer the query without any additional prior knowledge.\\n\"\n", "\n", "    \"---------------------\\n\"\n", "    \"Context: {context_str}\\n\"\n", "    \"Metadata for video: {metadata_str} \\n\"\n", "\n", "    \"---------------------\\n\"\n", "    \"Query: {query_str}\\n\"\n", "    \"Answer: \"\n", ")"], "metadata": {"id": "97bGd7wcyKTZ"}, "execution_count": 64, "outputs": []}, {"cell_type": "code", "source": ["img"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fYVgHILHy5-X", "outputId": "81c6e61e-9acb-4942-87bb-06dea9d0f0aa"}, "execution_count": 67, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['/content/mixed_data/frame0003.png',\n", " '/content/mixed_data/frame0040.png',\n", " '/content/mixed_data/frame0044.png',\n", " '/content/mixed_data/frame0042.png',\n", " '/content/mixed_data/frame0045.png']"]}, "metadata": {}, "execution_count": 67}]}, {"cell_type": "code", "source": ["import json\n", "metadata_str=json.dumps(metadata_vid)"], "metadata": {"id": "P-oEG3Q2zC0F"}, "execution_count": 68, "outputs": []}, {"cell_type": "code", "source": ["query_str=\"can you tell me what is linear regression and equation of linear regression?\""], "metadata": {"id": "MrgFXHJIy_XU"}, "execution_count": 70, "outputs": []}, {"cell_type": "code", "source": ["context_str = \"\".join(text)"], "metadata": {"id": "6VC4mg79yuMZ"}, "execution_count": 66, "outputs": []}, {"cell_type": "code", "source": ["image_documents = SimpleDirectoryReader( input_files=img).load_data()"], "metadata": {"id": "tNyOcu2fywnO"}, "execution_count": 71, "outputs": []}, {"cell_type": "code", "source": ["from llama_index.multi_modal_llms.openai import OpenAIMultiModal"], "metadata": {"id": "IUGGglIMwtHB"}, "execution_count": 51, "outputs": []}, {"cell_type": "code", "source": ["openai_mm_llm = OpenAIMultiModal(model=\"gpt-4-vision-preview\", api_key=OPENAI_API_TOKEN, max_new_tokens=1500)"], "metadata": {"id": "qRrclz5dxlaj"}, "execution_count": 63, "outputs": []}, {"cell_type": "code", "source": ["result=openai_mm_llm.complete(\n", "    prompt=qa_tmpl_str.format(\n", "        query_str=query_str,metadata_str=metadata_str\n", "    ),\n", "    image_documents=image_documents,\n", ")"], "metadata": {"id": "UdzMuacuyWMR"}, "execution_count": 77, "outputs": []}, {"cell_type": "code", "source": ["pprint(result.text)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2PGjzok8zKDS", "outputId": "039f8566-0095-4095-bd9c-1e261e2d359c"}, "execution_count": 78, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["('Linear regression is a statistical method used to model the relationship '\n", " 'between a dependent variable and one or more independent variables. The '\n", " 'method assumes that the relationship between the variables is linear, '\n", " 'meaning that it can be represented by a straight line.\\n'\n", " '\\n'\n", " 'The equation of a simple linear regression, which involves a single '\n", " 'independent variable, is:\\n'\n", " '\\n'\n", " '\\\\[ y = \\\\beta_0 + \\\\beta_1x + \\\\epsilon \\\\]\\n'\n", " '\\n'\n", " 'Here, \\\\( y \\\\) is the dependent variable, \\\\( x \\\\) is the independent '\n", " 'variable, \\\\( \\\\beta_0 \\\\) is the y-intercept, \\\\( \\\\beta_1 \\\\) is the slope '\n", " 'of the line (which represents the effect of the independent variable on the '\n", " 'dependent variable), and \\\\( \\\\epsilon \\\\) represents the error term, which '\n", " 'accounts for the variability in \\\\( y \\\\) that cannot be explained by \\\\( x '\n", " '\\\\).\\n'\n", " '\\n'\n", " 'In multiple linear regression, where there are multiple independent '\n", " 'variables, the equation is extended to:\\n'\n", " '\\n'\n", " '\\\\[ y = \\\\beta_0 + \\\\beta_1x_1 + \\\\beta_2x_2 + ... + \\\\beta_nx_n + \\\\epsilon '\n", " '\\\\]\\n'\n", " '\\n'\n", " 'Each \\\\( x_i \\\\) represents a different independent variable, and each \\\\( '\n", " '\\\\beta_i \\\\) represents the partial effect of that independent variable on '\n", " 'the dependent variable \\\\( y \\\\).')\n"]}]}, {"cell_type": "code", "source": ["qa_tmpl_str=(\n", "    \"Based on the provided information, including relevant images and retrieved context from the video, \\\n", "    accurately and precisely answer the query without any additional prior knowledge.\\n\"\n", "\n", "    \"---------------------\\n\"\n", "    \"Metadata for video: {metadata_str} \\n\"\n", "\n", "    \"---------------------\\n\"\n", "    \"Query: {query_str}\\n\"\n", "    \"Answer: \"\n", ")"], "metadata": {"id": "t8Y8VyRbzYuB"}, "execution_count": 76, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "X24-KE-UznCS"}, "execution_count": null, "outputs": []}]}