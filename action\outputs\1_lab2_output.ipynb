{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Deep Research\n", "\n", "One of the classic cross-business Agentic use cases! This is huge."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/business.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#00bfff;\">Commercial implications</h2>\n", "            <span style=\"color:#00bfff;\">A Deep Research agent is broadly applicable to any business area, and to your own day-to-day activities. You can make use of this yourself!\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Imports\n", "\n", "from agents import Agent, WebSearchTool, trace, Runner, function_tool\n", "from agents.model_settings import ModelSettings\n", "from pydantic import BaseModel\n", "from dotenv import load_dotenv\n", "import asyncio\n", "import os\n", "from IPython.display import display, Markdown\n", "from pprint import pprint\n", "import requests\n", "load_dotenv(override=True)\n", "\n", "# Constants\n", "\n", "pushover_user = os.getenv(\"PUSHOVER_USER\")\n", "pushover_token = os.getenv(\"PUSHOVER_TOKEN\")\n", "pushover_url = \"https://api.pushover.net/1/messages.json\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## OpenAI Hosted Tools\n", "\n", "OpenAI Agents SDK includes the following hosted tools:\n", "\n", "The `WebSearchTool` lets an agent search the web.  \n", "The `FileSearchTool` allows retrieving information from your OpenAI Vector Stores.  \n", "The `ComputerTool` allows automating computer use tasks like taking screenshots and clicking.\n", "\n", "### Important note - API charge of WebSearchTool\n", "\n", "This is costing me 2.5 cents per call for OpenAI WebSearchTool. That can add up to $2-$3 for the next 2 labs. We'll use low cost Search tools with other platforms, so feel free to skip running this if the cost is a concern.\n", "\n", "Costs are here: https://platform.openai.com/docs/pricing#web-search"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## We will be making 4 Agents:\n", "\n", "1. Search Agent - searches online given a search term using an OpenAI hosted tool\n", "2. Planner Agent - given a query from the user, come up with searches\n", "3. Report Agent - make a report on results\n", "4. <PERSON>ush Agent - send a notification to the user's phone with a summary\n", "\n", "## Our First Agent: Search Agent\n", "\n", "Given a Search term, search for it on the internet and summarize results."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["INSTRUCTIONS = \"You are a research assistant. Given a search term, you search the web for that term and \\\n", "produce a concise summary of the results. The summary must 2-3 paragraphs and less than 300 \\\n", "words. Capture the main points. Write succintly, no need to have complete sentences or good \\\n", "grammar. This will be consumed by someone synthesizing a report, so it's vital you capture the \\\n", "essence and ignore any fluff. Do not include any additional commentary other than the summary itself.\"\n", "\n", "search_agent = Agent(\n", "    name=\"Search agent\",\n", "    instructions=INSTRUCTIONS,\n", "    tools=[WebSearchTool(search_context_size=\"low\")],\n", "    model=\"gpt-4.1-mini\",\n", "    model_settings=ModelSettings(tool_choice=\"required\"),\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/markdown": ["As of May 2025, several AI agent frameworks have gained prominence for their capabilities in developing sophisticated, multi-agent systems:\n", "\n", "**LangChain**: A versatile framework for building applications powered by large language models (LLMs), LangChain offers modular components for complex workflows, context management, and multi-step tasks. It integrates seamlessly with various LLMs and external tools, making it suitable for conversational AI, research tools, and document analysis. ([curotec.com](https://www.curotec.com/insights/top-ai-agent-frameworks/?utm_source=openai))\n", "\n", "**LangGraph**: An extension of LangChain, LangGraph focuses on stateful, multi-agent applications, providing tools for agent coordination, visual graph-based workflows, and advanced error handling. It's ideal for applications requiring dependency management and logical flow across multi-step tasks, such as storytelling, multi-step chatbots, and strategic planning tools. ([curotec.com](https://www.curotec.com/insights/top-ai-agent-frameworks/?utm_source=openai))\n", "\n", "**CrewAI**: Designed for orchestrating role-playing AI agents, CrewAI enables the creation of specialized agents that collaborate on complex tasks. It features role-based agent architecture, dynamic task planning, and inter-agent communication protocols, making it well-suited for virtual product development teams and complex, multi-step workflows. ([chatbase.co](https://www.chatbase.co/blog/ai-agent-frameworks?utm_source=openai))\n", "\n", "These frameworks are recognized for their flexibility, scalability, and active community support, facilitating the development of advanced AI agent systems across various domains. "], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["message = \"What are the most popular and successful AI Agent frameworks in May 2025\"\n", "\n", "with trace(\"Search\"):\n", "    result = await Runner.run(search_agent, message)\n", "\n", "display(Markdown(result.final_output))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Take a look at the trace\n", "\n", "https://platform.openai.com/traces"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Our Second Agent: Planner Agent\n", "\n", "Given a query, come up with 5 ideas for web searches that could be run.\n", "\n", "Use Structured Outputs as our way to ensure the Agent provides what we need."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# See note above about cost of WebSearchTool\n", "\n", "HOW_MANY_SEARCHES = 5\n", "\n", "INSTRUCTIONS = f\"You are a helpful research assistant. Given a query, come up with a set of web searches \\\n", "to perform to best answer the query. Output {HOW_MANY_SEARCHES} terms to query for.\"\n", "\n", "# We use Pydantic objects to describe the Schema of the output\n", "\n", "class WebSearchItem(BaseModel):\n", "    reason: str\n", "    \"Your reasoning for why this search is important to the query.\"\n", "\n", "    query: str\n", "    \"The search term to use for the web search.\"\n", "\n", "\n", "class WebSearchPlan(BaseModel):\n", "    searches: list[WebSearchItem]\n", "    \"\"\"A list of web searches to perform to best answer the query.\"\"\"\n", "\n", "# We pass in the Pydantic object to ensure the output follows the schema\n", "\n", "planner_agent = Agent(\n", "    name=\"PlannerAgent\",\n", "    instructions=INSTRUCTIONS,\n", "    model=\"gpt-4.1-mini\",\n", "    output_type=WebSearchPlan,\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WebSearchPlan(searches=[WebSearchItem(reason='Identify currently leading AI agent frameworks as of 2025', query='most popular AI agent frameworks 2025'), WebSearchItem(reason='Find success metrics and user adoption statistics for AI agent frameworks in 2025', query='successful AI agent frameworks user adoption 2025'), WebSearchItem(reason='Review expert analysis and comparisons of AI agent frameworks in 2025', query='best AI agent frameworks expert reviews 2025'), WebSearchItem(reason='Explore recent innovations and updates in AI agent frameworks by May 2025', query='latest AI agent framework innovations May 2025'), WebSearchItem(reason='Check developer forums and communities for popular AI agent frameworks discussion 2025', query='AI agent frameworks developer community popularity 2025')])\n"]}], "source": ["\n", "message = \"What are the most popular and successful AI Agent frameworks in May 2025\"\n", "\n", "with trace(\"Search\"):\n", "    result = await Runner.run(planner_agent, message)\n", "    pprint(result.final_output)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Our Third Agent: Writer Agent\n", "\n", "Take the results of internet searches and make a report"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["INSTRUCTIONS = (\n", "    \"You are a senior researcher tasked with writing a cohesive report for a research query. \"\n", "    \"You will be provided with the original query, and some initial research done by a research assistant.\\n\"\n", "    \"You should first come up with an outline for the report that describes the structure and \"\n", "    \"flow of the report. Then, generate the report and return that as your final output.\\n\"\n", "    \"The final output should be in markdown format, and it should be lengthy and detailed. Aim \"\n", "    \"for 5-10 pages of content, at least 1000 words.\"\n", ")\n", "\n", "\n", "class ReportData(BaseModel):\n", "    short_summary: str\n", "    \"\"\"A short 2-3 sentence summary of the findings.\"\"\"\n", "\n", "    markdown_report: str\n", "    \"\"\"The final report\"\"\"\n", "\n", "    follow_up_questions: list[str]\n", "    \"\"\"Suggested topics to research further\"\"\"\n", "\n", "\n", "writer_agent = Agent(\n", "    name=\"WriterAgent\",\n", "    instructions=INSTRUCTIONS,\n", "    model=\"gpt-4o-mini\",\n", "    output_type=ReportData,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Our Fourth Agent: push notification\n", "\n", "Just to show how easy it is to make a tool!\n", "\n", "I'm using a nifty product called PushOver - to set this up yourself, visit https://pushover.net"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["@function_tool\n", "def push(message: str):\n", "    \"\"\"Send a push notification with this brief message\"\"\"\n", "    payload = {\"user\": pushover_user, \"token\": pushover_token, \"message\": message}\n", "    requests.post(pushover_url, data=payload)\n", "    return {\"status\": \"success\"}"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["FunctionTool(name='push', description='Send a push notification with this brief message', params_json_schema={'properties': {'message': {'title': 'Message', 'type': 'string'}}, 'required': ['message'], 'title': 'push_args', 'type': 'object', 'additionalProperties': False}, on_invoke_tool=<function function_tool.<locals>._create_function_tool.<locals>._on_invoke_tool at 0x112e9f9c0>, strict_json_schema=True)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["push"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["INSTRUCTIONS = \"\"\"You are a member of a research team and will be provided with a short summary of a report.\n", "When you receive the report summary, you send a push notification to the user using your tool, informing them that research is complete,\n", "and including the report summary you receive\"\"\"\n", "\n", "\n", "push_agent = Agent(\n", "    name=\"Push agent\",\n", "    instructions=INSTRUCTIONS,\n", "    tools=[push],\n", "    model=\"gpt-4.1-mini\",\n", "    model_settings=ModelSettings(tool_choice=\"required\")\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### The next 3 functions will plan and execute the search, using planner_agent and search_agent"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["async def plan_searches(query: str):\n", "    \"\"\" Use the planner_agent to plan which searches to run for the query \"\"\"\n", "    print(\"Planning searches...\")\n", "    result = await Runner.run(planner_agent, f\"Query: {query}\")\n", "    print(f\"Will perform {len(result.final_output.searches)} searches\")\n", "    return result.final_output\n", "\n", "async def perform_searches(search_plan: WebSearchPlan):\n", "    \"\"\" Call search() for each item in the search plan \"\"\"\n", "    print(\"Searching...\")\n", "    tasks = [asyncio.create_task(search(item)) for item in search_plan.searches]\n", "    results = await asyncio.gather(*tasks)\n", "    print(\"Finished searching\")\n", "    return results\n", "\n", "async def search(item: WebSearchItem):\n", "    \"\"\" Use the search agent to run a web search for each item in the search plan \"\"\"\n", "    input = f\"Search term: {item.query}\\nReason for searching: {item.reason}\"\n", "    result = await Runner.run(search_agent, input)\n", "    return result.final_output"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### The next 2 functions write a report and send a push notification"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["async def write_report(query: str, search_results: list[str]):\n", "    \"\"\" Use the writer agent to write a report based on the search results\"\"\"\n", "    print(\"Thinking about report...\")\n", "    input = f\"Original query: {query}\\nSummarized search results: {search_results}\"\n", "    result = await Runner.run(writer_agent, input)\n", "    print(\"Finished writing report\")\n", "    return result.final_output\n", "\n", "async def send_push(report: ReportData):\n", "    \"\"\" Use the push agent to send a notification to the user \"\"\"\n", "    print(\"Pushing...\")\n", "    result = await Runner.run(push_agent, report.short_summary)\n", "    print(\"Push sent\")\n", "    return report"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Showtime!"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting research...\n", "Planning searches...\n", "<PERSON> perform 5 searches\n", "Searching...\n", "Finished searching\n", "Thinking about report...\n", "Finished writing report\n", "Pushing...\n", "<PERSON><PERSON> sent\n", "Hooray!\n"]}, {"data": {"text/markdown": ["# AI Agent Frameworks: A Comprehensive Assessment (May 2025)\n", "\n", "## Table of Contents\n", "\n", "1. [Introduction](#introduction)  \n", "2. [Methodology](#methodology)  \n", "3. [Leading AI Agent Frameworks](#leading-ai-agent-frameworks)  \n", "     3.1. [<PERSON><PERSON><PERSON><PERSON>](#langchain)  \n", "     3.2. [<PERSON><PERSON><PERSON><PERSON>](#langgraph)  \n", "     3.3. [CrewAI](#crewai)  \n", "     3.4. [Microsoft's AutoGen](#microsofts-autogen)  \n", "     3.5. [AI2Agent](#ai2agent)  \n", "     3.6. [Other Notable Frameworks](#other-notable-frameworks)  \n", "4. [Use Cases and Applications](#use-cases-and-applications)  \n", "5. [Challenges and Considerations](#challenges-and-considerations)  \n", "6. [Future Trends](#future-trends)  \n", "7. [Conclusion](#conclusion)  \n", "8. [References](#references)  \n", "\n", "## Introduction  \n", "The rapid evolution of artificial intelligence (AI) has led to the emergence of advanced frameworks for developing AI agents capable of sophisticated tasks. By May 2025, many frameworks have proven their efficiency and popularity in diverse applications, underscoring their strategic significance in enterprise settings. This report delves into the leading AI agent frameworks dominating the landscape in May 2025, highlighting their unique features and applicability across various use cases.\n", "\n", "## Methodology  \n", "This report synthesizes insights from multiple reputable sources, including industry analyses, developer surveys, and expert commentary, to provide a cohesive overview of the AI agent frameworks currently shaping the market. A review of documented experiences from developers and organizations aids in understanding practical applications and assessing framework performance.\n", "\n", "## Leading AI Agent Frameworks  \n", "As of May 2025, the following frameworks are recognized for their capabilities and adoption across various sectors:\n", "\n", "### <PERSON><PERSON><PERSON><PERSON>  \n", "LangChain is a versatile open-source framework designed for building applications powered by large language models (LLMs). It simplifies complex workflows by providing tools for context management, workflow creation, and integration with multiple LLMs such as OpenAI and Hugging Face. It is particularly well-suited for applications in conversational AI, research tools, and document analysis. However, it has been noted that LangChain can be somewhat complex for newcomers, requiring a steep learning curve to master its many features.  \n", "\n", "**Key Features:**  \n", "- Modular development capabilities  \n", "- Integration with leading LLMs  \n", "- Context management tools  \n", "\n", "### LangGraph  \n", "An extension of LangChain, LangGraph focuses on multi-agent systems that can collaborate and adapt to different tasks. It enhances LangChain’s functionalities by providing coordination tools for multiple agents, a visual graph-based workflow, and robust error-handling capabilities. LangGraph is typically utilized for applications such as storytelling, multi-step chatbots, and strategic planning tools.  \n", "\n", "**Key Features:**  \n", "- Support for stateful multi-agent applications  \n", "- Advanced error handling  \n", "- Visual workflow design  \n", "\n", "### CrewAI  \n", "CrewAI is a framework aimed at orchestrating role-based AI agents, allowing developers to create teams of specialized agents to tackle complex tasks. It features a role-based architecture which facilitates dynamic task planning and enhances inter-agent communication. CrewAI is particularly adept at managing intricate, multi-step workflows. Future updates are expected to introduce more sophisticated orchestration strategies.  \n", "\n", "**Key Features:**  \n", "- Role-based agent assignments  \n", "- Inter-agent communication protocols  \n", "- Dynamic task management  \n", "\n", "### Microsoft’s AutoGen  \n", "AutoGen is a multi-agent conversational system designed to support both autonomous and human-in-the-loop workflows. It features asynchronous messaging capabilities and event-driven interactions, making it suitable for tasks that require real-time concurrency and dynamic dialogue management. However, there are reported limitations in its applicability for specific tasks like compiling C source code and extracting data from complex files.  \n", "\n", "**Key Features:**  \n", "- Asynchronous messaging  \n", "- Human-in-the-loop support  \n", "- Event-driven interaction capabilities  \n", "\n", "### AI2Agent  \n", "AI2Agent is aimed at automating the deployment of AI projects, allowing for guideline-driven execution and self-adaptive debugging. Experiments indicate that AI2Agent can significantly reduce deployment times while improving success rates across various AI deployment cases. This framework is particularly valuable for teams looking to streamline their project initiation and execution phases in AI deployments.  \n", "\n", "**Key Features:**  \n", "- Automation of deployment processes  \n", "- Self-adaptive debugging mechanisms  \n", "- Enhanced project execution efficiency  \n", "\n", "### Other Notable Frameworks  \n", "- **OpenAI Swarm**: Focuses on lightweight and modular multi-agent systems designed for autonomous collaboration.  \n", "- **Hugging Face Transformers Agents**: Facilitates the development, testing, and deployment of agents handling complex natural language tasks, leveraging advanced machine learning models.\n", "- **Rasa**: An open-source solution ideal for creating customized conversational AI systems for industry-specific applications.\n", "- **JADE**: Tailored for distributed systems, especially in IoT environments, providing robust communication protocols for complex system management.\n", "\n", "## Use Cases and Applications  \n", "The versatility of these frameworks enables their application across numerous industries and scenarios.  \n", "- **Customer Support**: Automated agents handle inquiries and provide support without human intervention.  \n", "- **Content Creation**: Frameworks like LangChain assist in generating insights and drafting documents.  \n", "- **Project Management**: CrewAI allows project teams to collaborate effectively by assigning roles and managing tasks dynamically.  \n", "- **Research Tools**: Leveraging LLMs, automated tools streamline the research process, enhancing accuracy and reducing time expenditure.  \n", "\n", "## Challenges and Considerations  \n", "While the frameworks offer substantial benefits, several challenges warrant attention:\n", "- **Complexity for Beginners**: Many frameworks, particularly LangChain, may pose a steep learning curve for novice users.\n", "- **Deployment Constraints**: Certain frameworks like AutoGen may not be universally suitable for all application requirements.\n", "- **Performance Variability**: The effectiveness of frameworks can vary significantly based on project requirements, necessitating careful selection.\n", "\n", "## Future Trends  \n", "As AI technologies continue to break new ground, several trends are likely to shape the future of AI agent frameworks:\n", "- **Increased Modularity**: Expect a rise in modular frameworks that allow developers to customize functionalities extensively.\n", "- **Broader Integration Capabilities**: Future frameworks will likely integrate seamlessly with a broader range of tools and platforms in enterprise settings.\n", "- **Enhanced Collaboration Features**: Advancements in inter-agent communication are anticipated, reinforcing collaboration both among agents and between humans and machines.\n", "\n", "## Conclusion  \n", "The AI agent framework landscape as of May 2025 is characterized by diversity and sophistication, with various frameworks tailored to meet specific enterprise needs. Understanding the strengths and weaknesses of each framework is essential for organizations looking to harness AI agents effectively. As developments continue, staying abreast of these changes will be crucial for leveraging the full potential of AI in highly competitive sectors.\n", "\n", "## References  \n", "1<PERSON> <PERSON><PERSON><PERSON>. \"Top AI Agent Frameworks: Insights from May 2025.\"  \n", "2. AI21. \"Evaluating AI Agent Frameworks for Enterprise Use.\"\n", "3. Turing. \"A Comprehensive Review of AI Agent Frameworks.\"\n", "4. <PERSON><PERSON>. \"The State of AI Agents: Survey Results of 2025.\"\n", "5. <PERSON><PERSON><PERSON>. \"Top AI Agent Frameworks to Watch in 2025.\"  \n", "6. Medium. \"Best AI Agent Frameworks for Builders in 2025.\"  \n", "\n", "---  \n", "This report provides a holistic view of the AI agent framework landscape, appropriate for organizations seeking to understand current trends and make informed decisions regarding their AI implementations."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["query =\"What are the most popular and successful AI Agent frameworks in May 2025\"\n", "\n", "with trace(\"Research trace\"):\n", "    print(\"Starting research...\")\n", "    search_plan = await plan_searches(query)\n", "    search_results = await perform_searches(search_plan)\n", "    report = await write_report(query, search_results)\n", "    await send_push(report)  \n", "    print(\"Hooray!\")\n", "display(Markdown(report.markdown_report))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### As always, take a look at the trace\n", "\n", "https://platform.openai.com/traces"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}