{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4", "authorship_tag": "ABX9TyMIuVjJKqR/9fsypmYd/Dng", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU", "widgets": {"application/vnd.jupyter.widget-state+json": {"e8b6acc77d8f4d208b74b4f1d05144e5": {"model_module": "yfiles-jupyter-graphs", "model_name": "GraphModel", "model_module_version": "^1.6.2", "state": {"_context_pane_mapping": [{"id": "Neighborhood", "title": "Neighborhood"}, {"id": "Data", "title": "Data"}, {"id": "Search", "title": "Search"}, {"id": "About", "title": "About"}], "_data_importer": "neo4j", "_directed": true, "_dom_classes": [], "_edges": [{"id": 1152922604118474800, "start": 1, "end": 2, "properties": {"label": "RULED"}, "label": "RULED", "color": "#9C27B0", "thickness_factor": 1, "directed": true}, {"id": 1155174403932160000, "start": 1, "end": 3, "properties": {"label": "RULED"}, "label": "RULED", "color": "#9C27B0", "thickness_factor": 1, "directed": true}, {"id": 1152923703630102500, "start": 1, "end": 4, "properties": {"label": "BELONGS_TO"}, "label": "BELONGS_TO", "color": "#2196F3", "thickness_factor": 1, "directed": true}, {"id": 1152924803141730300, "start": 1, "end": 5, "properties": {"label": "CHILD_OF"}, "label": "CHILD_OF", "color": "#4CAF50", "thickness_factor": 1, "directed": true}, {"id": 1155176602955415600, "start": 1, "end": 6, "properties": {"label": "CHILD_OF"}, "label": "CHILD_OF", "color": "#4CAF50", "thickness_factor": 1, "directed": true}, {"id": 1152925902653358000, "start": 1, "end": 7, "properties": {"label": "HALF-SISTER_OF"}, "label": "HALF-SISTER_OF", "color": "#F44336", "thickness_factor": 1, "directed": true}, {"id": 1155177702467043300, "start": 1, "end": 9, "properties": {"label": "HALF-SISTER_OF"}, "label": "HALF-SISTER_OF", "color": "#F44336", "thickness_factor": 1, "directed": true}, {"id": 1152927002164985900, "start": 1, "end": 8, "properties": {"label": "IGNORED_CLAIMS_OF"}, "label": "IGNORED_CLAIMS_OF", "color": "#607D8B", "thickness_factor": 1, "directed": true}, {"id": 1152928101676613600, "start": 1, "end": 10, "properties": {"label": "DEPENDS_ON"}, "label": "DEPENDS_ON", "color": "#673AB7", "thickness_factor": 1, "directed": true}, {"id": 1155179901490299000, "start": 1, "end": 16, "properties": {"label": "DEPENDS_ON"}, "label": "DEPENDS_ON", "color": "#673AB7", "thickness_factor": 1, "directed": true}, {"id": 1152929201188241400, "start": 1, "end": 11, "properties": {"label": "CREATED_TITLE"}, "label": "CREATED_TITLE", "color": "#CDDC39", "thickness_factor": 1, "directed": true}, {"id": 1152930300699869200, "start": 1, "end": 12, "properties": {"label": "SUCCEEDED_BY"}, "label": "SUCCEEDED_BY", "color": "#9E9E9E", "thickness_factor": 1, "directed": true}, {"id": 1152931400211497000, "start": 1, "end": 13, "properties": {"label": "MOTHER_OF"}, "label": "MOTHER_OF", "color": "#9C27B0", "thickness_factor": 1, "directed": true}, {"id": 1152932499723124700, "start": 1, "end": 14, "properties": {"label": "MANOEUVRING_BETWEEN"}, "label": "MANOEUVRING_BETWEEN", "color": "#2196F3", "thickness_factor": 1, "directed": true}, {"id": 1155184299536810000, "start": 1, "end": 15, "properties": {"label": "MANOEUVRING_BETWEEN"}, "label": "MANOEUVRING_BETWEEN", "color": "#2196F3", "thickness_factor": 1, "directed": true}, {"id": 6917560913478287000, "start": 1, "end": 12, "properties": {"label": "SUCCESSOR"}, "label": "SUCCESSOR", "color": "#4CAF50", "thickness_factor": 1, "directed": true}, {"id": 1157456990071423000, "start": 5, "end": 1, "properties": {"label": "SUCCESSOR"}, "label": "SUCCESSOR", "color": "#4CAF50", "thickness_factor": 1, "directed": true}, {"id": 1152953390444052500, "start": 5, "end": 7, "properties": {"label": "SUCCESSOR"}, "label": "SUCCESSOR", "color": "#4CAF50", "thickness_factor": 1, "directed": true}, {"id": 1155205190257737700, "start": 5, "end": 54, "properties": {"label": "SUCCESSOR"}, "label": "SUCCESSOR", "color": "#4CAF50", "thickness_factor": 1, "directed": true}, {"id": 1159708789885108200, "start": 5, "end": 55, "properties": {"label": "SUCCESSOR"}, "label": "SUCCESSOR", "color": "#4CAF50", "thickness_factor": 1, "directed": true}, {"id": 1161960589698793500, "start": 5, "end": 56, "properties": {"label": "SUCCESSOR"}, "label": "SUCCESSOR", "color": "#4CAF50", "thickness_factor": 1, "directed": true}, {"id": 1164212389512478700, "start": 5, "end": 57, "properties": {"label": "SUCCESSOR"}, "label": "SUCCESSOR", "color": "#4CAF50", "thickness_factor": 1, "directed": true}, {"id": 1166464189326164000, "start": 5, "end": 58, "properties": {"label": "SUCCESSOR"}, "label": "SUCCESSOR", "color": "#4CAF50", "thickness_factor": 1, "directed": true}, {"id": 1155197493676343300, "start": 13, "end": 63, "properties": {"label": "PARENT"}, "label": "PARENT", "color": "#F44336", "thickness_factor": 1, "directed": true}, {"id": 1152945693862658000, "start": 13, "end": 65, "properties": {"label": "PARENT"}, "label": "PARENT", "color": "#F44336", "thickness_factor": 1, "directed": true}, {"id": 1152953390444052500, "start": 13, "end": 18, "properties": {"label": "SUCCESSOR"}, "label": "SUCCESSOR", "color": "#4CAF50", "thickness_factor": 1, "directed": true}, {"id": 1159688998675808300, "start": 18, "end": 3, "properties": {"label": "WAR"}, "label": "WAR", "color": "#607D8B", "thickness_factor": 1, "directed": true}, {"id": 1157437198862123000, "start": 18, "end": 14, "properties": {"label": "WAR"}, "label": "WAR", "color": "#607D8B", "thickness_factor": 1, "directed": true}, {"id": 1152933599234752500, "start": 18, "end": 15, "properties": {"label": "WAR"}, "label": "WAR", "color": "#607D8B", "thickness_factor": 1, "directed": true}, {"id": 1155185399048437800, "start": 18, "end": 19, "properties": {"label": "WAR"}, "label": "WAR", "color": "#607D8B", "thickness_factor": 1, "directed": true}, {"id": 1152934698746380300, "start": 18, "end": 20, "properties": {"label": "INFLUENCED"}, "label": "INFLUENCED", "color": "#673AB7", "thickness_factor": 1, "directed": true}, {"id": 1155186498560065500, "start": 18, "end": 21, "properties": {"label": "INFLUENCED"}, "label": "INFLUENCED", "color": "#673AB7", "thickness_factor": 1, "directed": true}, {"id": 1157438298373750800, "start": 18, "end": 22, "properties": {"label": "INFLUENCED"}, "label": "INFLUENCED", "color": "#673AB7", "thickness_factor": 1, "directed": true}, {"id": 1159690098187436000, "start": 18, "end": 23, "properties": {"label": "INFLUENCED"}, "label": "INFLUENCED", "color": "#673AB7", "thickness_factor": 1, "directed": true}, {"id": 1152935798258008000, "start": 18, "end": 24, "properties": {"label": "DEFEATED"}, "label": "DEFEATED", "color": "#CDDC39", "thickness_factor": 1, "directed": true}, {"id": 1152945693862658000, "start": 18, "end": 43, "properties": {"label": "PARENT"}, "label": "PARENT", "color": "#F44336", "thickness_factor": 1, "directed": true}, {"id": 1155197493676343300, "start": 18, "end": 44, "properties": {"label": "PARENT"}, "label": "PARENT", "color": "#F44336", "thickness_factor": 1, "directed": true}, {"id": 1152948992397541400, "start": 18, "end": 47, "properties": {"label": "RECEIVED_TITLE"}, "label": "RECEIVED_TITLE", "color": "#9E9E9E", "thickness_factor": 1, "directed": true}, {"id": 1152936897769635800, "start": 26, "end": 27, "properties": {"label": "HELD_TITLE"}, "label": "HELD_TITLE", "color": "#9C27B0", "thickness_factor": 1, "directed": true}, {"id": 1152937997281263600, "start": 26, "end": 28, "properties": {"label": "DAUGHTER_OF"}, "label": "DAUGHTER_OF", "color": "#2196F3", "thickness_factor": 1, "directed": true}, {"id": 1155189797094948900, "start": 26, "end": 29, "properties": {"label": "DAUGHTER_OF"}, "label": "DAUGHTER_OF", "color": "#2196F3", "thickness_factor": 1, "directed": true}, {"id": 1152939096792891400, "start": 26, "end": 30, "properties": {"label": "RELATED_TO"}, "label": "RELATED_TO", "color": "#4CAF50", "thickness_factor": 1, "directed": true}, {"id": 1155190896606576600, "start": 26, "end": 31, "properties": {"label": "RELATED_TO"}, "label": "RELATED_TO", "color": "#4CAF50", "thickness_factor": 1, "directed": true}, {"id": 1157442696420262000, "start": 26, "end": 32, "properties": {"label": "RELATED_TO"}, "label": "RELATED_TO", "color": "#4CAF50", "thickness_factor": 1, "directed": true}, {"id": 1152940196304519200, "start": 26, "end": 33, "properties": {"label": "DECLARED_AS_HEIR"}, "label": "DECLARED_AS_HEIR", "color": "#F44336", "thickness_factor": 1, "directed": true}, {"id": 1152941295816147000, "start": 26, "end": 34, "properties": {"label": "SUPPORTED_FOUNDATION_OF_UNIVERSITY_OF_MOSCOW"}, "label": "SUPPORTED_FOUNDATION_OF_UNIVERSITY_OF_MOSCOW", "color": "#607D8B", "thickness_factor": 1, "directed": true}, {"id": 1152942395327774700, "start": 26, "end": 35, "properties": {"label": "ENCOURAGED_FOUNDATION_OF_IMPERIAL_ACADEMY_OF_ARTS"}, "label": "ENCOURAGED_FOUNDATION_OF_IMPERIAL_ACADEMY_OF_ARTS", "color": "#673AB7", "thickness_factor": 1, "directed": true}, {"id": 1152943494839402500, "start": 26, "end": 36, "properties": {"label": "SUPPORTED_ARCHITECTURAL_PROJECTS_BY"}, "label": "SUPPORTED_ARCHITECTURAL_PROJECTS_BY", "color": "#CDDC39", "thickness_factor": 1, "directed": true}, {"id": 1152944594351030300, "start": 26, "end": 37, "properties": {"label": "SOLVED_EUROPEAN_CONFLICT_WITH"}, "label": "SOLVED_EUROPEAN_CONFLICT_WITH", "color": "#9E9E9E", "thickness_factor": 1, "directed": true}, {"id": 6924308616337949000, "start": 31, "end": 43, "properties": {"label": "PARENT"}, "label": "PARENT", "color": "#F44336", "thickness_factor": 1, "directed": true}], "_graph_layout": {}, "_highlight": [], "_license": {}, "_model_module": "yfiles-jupyter-graphs", "_model_module_version": "^1.6.2", "_model_name": "GraphModel", "_neighborhood": {}, "_nodes": [{"id": 1, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 2, "properties": {"id": "England", "label": "Country:__Entity__"}, "color": "#4CAF50", "styles": {}, "label": "England", "scale_factor": 1, "type": "#4CAF50", "size": [55, 55], "position": [0, 0]}, {"id": 3, "properties": {"id": "Ireland", "label": "Country:__Entity__"}, "color": "#4CAF50", "styles": {}, "label": "Ireland", "scale_factor": 1, "type": "#4CAF50", "size": [55, 55], "position": [0, 0]}, {"id": 4, "properties": {"id": "House Of Tudor", "label": "Royal family:__En<PERSON>ty__"}, "color": "#F44336", "styles": {}, "label": "House Of Tudor", "scale_factor": 1, "type": "#F44336", "size": [55, 55], "position": [0, 0]}, {"id": 5, "properties": {"id": "<PERSON>iii", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>iii", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 6, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 7, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 9, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 8, "properties": {"id": "Lady <PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "Lady <PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 10, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 16, "properties": {"id": "Sir <PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "Sir <PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 11, "properties": {"id": "<PERSON>", "label": "Title:__Entity__"}, "color": "#607D8B", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#607D8B", "size": [55, 55], "position": [0, 0]}, {"id": 12, "properties": {"id": "<PERSON> Of Scotland", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON> Of Scotland", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 13, "properties": {"id": "<PERSON>, Queen Of Scots", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>, Queen Of Scots", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 14, "properties": {"id": "France", "label": "Country:__Entity__"}, "color": "#4CAF50", "styles": {}, "label": "France", "scale_factor": 1, "type": "#4CAF50", "size": [55, 55], "position": [0, 0]}, {"id": 15, "properties": {"id": "Spain", "label": "Country:__Entity__"}, "color": "#4CAF50", "styles": {}, "label": "Spain", "scale_factor": 1, "type": "#4CAF50", "size": [55, 55], "position": [0, 0]}, {"id": 54, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 55, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 56, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 57, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 58, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 63, "properties": {"id": "<PERSON><PERSON><PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON><PERSON><PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 65, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 18, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 19, "properties": {"id": "Netherlands", "label": "Country:__Entity__"}, "color": "#4CAF50", "styles": {}, "label": "Netherlands", "scale_factor": 1, "type": "#4CAF50", "size": [55, 55], "position": [0, 0]}, {"id": 20, "properties": {"id": "<PERSON>", "label": "Playwright:__En<PERSON>ty__"}, "color": "#673AB7", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#673AB7", "size": [55, 55], "position": [0, 0]}, {"id": 21, "properties": {"id": "<PERSON>", "label": "Playwright:__En<PERSON>ty__"}, "color": "#673AB7", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#673AB7", "size": [55, 55], "position": [0, 0]}, {"id": 22, "properties": {"id": "<PERSON>", "label": "Maritime adventurer:__En<PERSON>ty__"}, "color": "#CDDC39", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#CDDC39", "size": [55, 55], "position": [0, 0]}, {"id": 23, "properties": {"id": "<PERSON>", "label": "Maritime adventurer:__En<PERSON>ty__"}, "color": "#CDDC39", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#CDDC39", "size": [55, 55], "position": [0, 0]}, {"id": 24, "properties": {"id": "Spanish Armada", "label": "Event:__Entity__"}, "color": "#9E9E9E", "styles": {}, "label": "Spanish Armada", "scale_factor": 1, "type": "#9E9E9E", "size": [55, 55], "position": [0, 0]}, {"id": 43, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 44, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 47, "properties": {"id": "<PERSON><PERSON><PERSON>", "label": "Title:__Entity__"}, "color": "#607D8B", "styles": {}, "label": "<PERSON><PERSON><PERSON>", "scale_factor": 1, "type": "#607D8B", "size": [55, 55], "position": [0, 0]}, {"id": 26, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 27, "properties": {"id": "Empress Of Russia", "label": "Title:__Entity__"}, "color": "#607D8B", "styles": {}, "label": "Empress Of Russia", "scale_factor": 1, "type": "#607D8B", "size": [55, 55], "position": [0, 0]}, {"id": 28, "properties": {"id": "Tsar <PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "Tsar <PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 29, "properties": {"id": "Catherine I Of Russia", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "Catherine I Of Russia", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 30, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 31, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 32, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 33, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 34, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 35, "properties": {"id": "<PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 36, "properties": {"id": "<PERSON><PERSON><PERSON><PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON><PERSON><PERSON><PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}, {"id": 37, "properties": {"id": "<PERSON><PERSON><PERSON><PERSON>", "label": "Person:__Entity__"}, "color": "#2196F3", "styles": {}, "label": "<PERSON><PERSON><PERSON><PERSON>", "scale_factor": 1, "type": "#2196F3", "size": [55, 55], "position": [0, 0]}], "_overview": {"enabled": null, "overview_set": false}, "_selected_graph": [[], []], "_sidebar": {"enabled": true, "start_with": ""}, "_view_count": null, "_view_module": "yfiles-jupyter-graphs", "_view_module_version": "^1.6.2", "_view_name": "GraphView", "layout": "IPY_MODEL_5caa1675fb9b47e89ceeab4a5aabb705"}}, "5caa1675fb9b47e89ceeab4a5aabb705": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": "800px", "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "100%"}}}}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/sunnysavita10/Indepth-GENAI/blob/main/RAG_With_Knowledge_graph(Neo4j).ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# langchain-core\n", "\n", "contains simple, core abstractions that have emerged as a standard, as well as LangChain Expression Language as a way to compose these components together. This package is now at version 0.1 and all breaking changes will be accompanied by a minor version bump.\n", "\n", "# langchain-community\n", "contains all third party integrations. We will work with partners on splitting key integrations out into standalone packages over the next month.\n", "\n", "# langchain\n", "contains higher-level and use-case specific chains, agents, and retrieval algorithms that are at the core of your application's cognitive architecture. We are targeting a launch of a stable 0.1 release for langchain in early January.#"], "metadata": {"id": "xB3OyiU14byv"}}, {"cell_type": "code", "source": ["%pip install --upgrade --quiet  langchain langchain-community langchain-openai langchain-experimental neo4j wikipedia tiktoken yfiles_jupyter_graphs"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "usWcdmOr7GAH", "outputId": "dcbfc75b-28d2-4a52-db53-83c63a862798"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/973.7 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[91m━━━━━━━━\u001b[0m\u001b[90m╺\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m204.8/973.7 kB\u001b[0m \u001b[31m6.2 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m973.7/973.7 kB\u001b[0m \u001b[31m14.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.1/2.1 MB\u001b[0m \u001b[31m53.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m199.5/199.5 kB\u001b[0m \u001b[31m19.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m203.0/203.0 kB\u001b[0m \u001b[31m20.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n", "  Installing backend dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m53.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m15.3/15.3 MB\u001b[0m \u001b[31m56.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m308.5/308.5 kB\u001b[0m \u001b[31m37.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m121.4/121.4 kB\u001b[0m \u001b[31m17.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m320.6/320.6 kB\u001b[0m \u001b[31m28.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.3/49.3 kB\u001b[0m \u001b[31m5.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m53.0/53.0 kB\u001b[0m \u001b[31m5.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m142.5/142.5 kB\u001b[0m \u001b[31m21.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m11.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m11.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m8.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.6/1.6 MB\u001b[0m \u001b[31m87.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Building wheel for neo4j (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "  Building wheel for wikipedia (setup.py) ... \u001b[?25l\u001b[?25hdone\n"]}]}, {"cell_type": "code", "source": ["from langchain_core.runnables import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON>,\n", "    RunnablePassthrough,\n", ")"], "metadata": {"id": "q8EzdaTJFTbx"}, "execution_count": 2, "outputs": []}, {"cell_type": "code", "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.prompts.prompt import PromptTemplate"], "metadata": {"id": "vKkxxyasFWPh"}, "execution_count": 3, "outputs": []}, {"cell_type": "code", "source": ["from google.colab import userdata\n", "OPENAI_API_KEY=userdata.get('OPENAI_API_KEY')"], "metadata": {"id": "SksHz3Q356JQ"}, "execution_count": 4, "outputs": []}, {"cell_type": "code", "source": ["from typing import Tuple, List, Optional"], "metadata": {"id": "GCEgNy7LFXS4"}, "execution_count": 4, "outputs": []}, {"cell_type": "code", "source": ["from langchain_core.messages import AIMessage, HumanMessage\n", "from langchain_core.output_parsers import StrOutputParser"], "metadata": {"id": "ymZquwggFaNr"}, "execution_count": 5, "outputs": []}, {"cell_type": "code", "source": ["from langchain_core.runnables import ConfigurableField"], "metadata": {"id": "nitfT-ktFaQQ"}, "execution_count": 6, "outputs": []}, {"cell_type": "code", "source": ["from yfiles_jupyter_graphs import GraphWidget\n", "from neo4j import GraphDatabase\n"], "metadata": {"id": "fzOPupw0FaSy"}, "execution_count": 7, "outputs": []}, {"cell_type": "code", "source": ["import os"], "metadata": {"id": "g6kjt1HkFaVZ"}, "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": ["try:\n", "  import google.colab\n", "  from google.colab import output\n", "  output.enable_custom_widget_manager()\n", "except:\n", "  pass"], "metadata": {"id": "IR5TLMjpFhE-"}, "execution_count": 10, "outputs": []}, {"cell_type": "code", "source": ["from langchain_community.vectorstores import Neo4jVector"], "metadata": {"id": "pSgOwI9SFhHr"}, "execution_count": 9, "outputs": []}, {"cell_type": "code", "source": ["from google.colab import userdata\n", "OPENAI_API_KEY=userdata.get('OPENAI_API_KEY')"], "metadata": {"id": "lyOvwiijFlQF"}, "execution_count": 11, "outputs": []}, {"cell_type": "code", "source": ["NEO4J_URI=\"neo4j+s://7b0ac3fd.databases.neo4j.io\"\n", "NEO4J_USERNAME=\"neo4j\"\n", "NEO4J_PASSWORD=\"al6q_y6NWn8e98YXHElSBED010quYdte4FaNxL-hESg\""], "metadata": {"id": "YiKFX23n4tl3"}, "execution_count": 12, "outputs": []}, {"cell_type": "code", "source": ["os.environ[\"OPENAI_API_KEY\"] = OPENAI_API_KEY\n", "os.environ[\"NEO4J_URI\"] = NEO4J_URI\n", "os.environ[\"NEO4J_USERNAME\"] = NEO4J_USERNAME\n", "os.environ[\"NEO4J_PASSWORD\"] = NEO4J_PASSWORD"], "metadata": {"id": "gHiqiwau7Tat"}, "execution_count": 13, "outputs": []}, {"cell_type": "code", "source": ["from langchain_community.graphs import Neo4jGraph"], "metadata": {"id": "zpIPagYu6BAp"}, "execution_count": 14, "outputs": []}, {"cell_type": "code", "source": ["graph = Neo4jGraph()"], "metadata": {"id": "0xzi4bRD6Bx9"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["from langchain.document_loaders import WikipediaLoader\n", "raw_documents = WikipediaLoader(query=\"Elizabeth I\").load()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XKqtMDVY6WwW", "outputId": "c432ea29-28d4-4501-e01e-dececbc2d748"}, "execution_count": 17, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/wikipedia/wikipedia.py:389: GuessedAtParserWarning: No parser was explicitly specified, so I'm using the best available HTML parser for this system (\"lxml\"). This usually isn't a problem, but if you run this code on another system, or in a different virtual environment, it may use a different parser and behave differently.\n", "\n", "The code that caused this warning is on line 389 of the file /usr/local/lib/python3.10/dist-packages/wikipedia/wikipedia.py. To get rid of this warning, pass the additional argument 'features=\"lxml\"' to the BeautifulSoup constructor.\n", "\n", "  lis = BeautifulSoup(html).find_all('li')\n"]}]}, {"cell_type": "code", "source": ["len(raw_documents)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ACWeDt0O7yc2", "outputId": "14415b49-96a8-4c23-e8f0-c962afbe1135"}, "execution_count": 18, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["23"]}, "metadata": {}, "execution_count": 18}]}, {"cell_type": "code", "source": ["raw_documents[:3]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "skFy3n30732l", "outputId": "95a859ac-436a-4de9-e1fa-146aa92c07d0"}, "execution_count": 19, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='<PERSON> (7 September 1533 – 24 March 1603) was Queen of England and Ireland from 17 November 1558 until her death in 1603. She was the last monarch of the House of Tudor.\\n<PERSON><PERSON><PERSON><PERSON> was the only surviving child of <PERSON> and <PERSON>, his second wife, who was executed when <PERSON> was two years old. <PERSON>'s marriage to <PERSON> was annulled, and <PERSON> was declared illegitimate. <PERSON> restored her to the line of succession when she was 10, via the Third Succession Act 1543. After <PERSON>'s death in 1547, <PERSON>'s younger half-brother <PERSON> ruled until his own death in 1553, bequeathing the crown to a Protestant cousin, <PERSON> <PERSON>, and ignoring the claims of his two half-sisters, the Catholic <PERSON> and the younger <PERSON>, in spite of statutes to the contrary. <PERSON>\\'s will was set aside within weeks of his death and <PERSON> became queen, deposing and executing <PERSON>. During <PERSON>'s reign, <PERSON> was imprisoned for nearly a year on suspicion of supporting Protestant rebels.\\nUpon her half-sister\\'s death in 1558, <PERSON> succeeded to the throne and set out to rule by good counsel. She depended heavily on a group of trusted advisers led by <PERSON>, whom she created <PERSON>. One of her first actions as queen was the establishment of an English Protestant church, of which she became the supreme governor. This era, later named the Elizabethan Religious Settlement, would evolve into the Church of England. It was expected that <PERSON> would marry and produce an heir; however, despite numerous courtships, she never did, and because of this she is sometimes referred to as the \"Virgin Queen\". She was eventually succeeded by her first cousin twice removed, <PERSON> of Scotland, the son of <PERSON>, Queen of Scots.\\nIn government, <PERSON> was more moderate than her father and siblings had been. One of her mottoes was video et taceo (\"I see and keep silent\"). In religion, she was relatively tolerant and avoided systematic persecution. After the pope declared her illegitimate in 1570, which in theory released English Catholics from allegiance to her, several conspiracies threatened her life, all of which were defeated with the help of her ministers\\' secret service, run by Sir <PERSON> Walsingham. Elizabeth was cautious in foreign affairs, manoeuvring between the major powers of France and Spain. She half-heartedly supported a number of ineffective, poorly resourced military campaigns in the Netherlands, France, and Ireland. By the mid-1580s, England could no longer avoid war with Spain.\\nAs she grew older, Elizabeth became celebrated for her virginity. A cult of personality grew around her which was celebrated in the portraits, pageants, and literature of the day. Elizabeth\\'s reign became known as the Elizabethan era. The period is famous for the flourishing of English drama, led by playwrights such as William Shakespeare and Christopher Marlowe, the prowess of English maritime adventurers, such as Francis Drake and Walter Raleigh, and for the defeat of the Spanish Armada. Some historians depict Elizabeth as a short-tempered, sometimes indecisive ruler, who enjoyed more than her fair share of luck. Towards the end of her reign, a series of economic and military problems weakened her popularity. Elizabeth is acknowledged as a charismatic performer (\"Gloriana\") and a dogged survivor (\"Good Queen Bess\") in an era when government was ramshackle and limited, and when monarchs in neighbouring countries faced internal problems that jeopardised their thrones. After the short, disastrous reigns of her half-siblings, her 44 years on the throne provided welcome stability for the kingdom and helped to forge a sense of national identity.\\n\\n\\n== Early life ==\\n\\nElizabeth was born at Greenwich Palace on 7 September 1533 and was named after her grandmothers, Elizabeth of York and Lady Elizabeth Howard. She was the second child of Henry VIII of England born in wedlock to survive infancy. Her mother was Henry\\'s second wife, Anne Bol', metadata={'title': 'Elizabeth I', 'summary': 'Elizabeth I (7 September 1533 – 24 March 1603) was Queen of England and Ireland from 17 November 1558 until her death in 1603. She was the last monarch of the House of Tudor.\\nElizabeth was the only surviving child of Henry VIII and Anne Boleyn, his second wife, who was executed when Elizabeth was two years old. Anne\\'s marriage to Henry was annulled, and Elizabeth was declared illegitimate. Henry restored her to the line of succession when she was 10, via the Third Succession Act 1543. After Henry\\'s death in 1547, Elizabeth\\'s younger half-brother Edward VI ruled until his own death in 1553, bequeathing the crown to a Protestant cousin, Lady Jane Grey, and ignoring the claims of his two half-sisters, the Catholic Mary and the younger Elizabeth, in spite of statutes to the contrary. Edward\\'s will was set aside within weeks of his death and Mary became queen, deposing and executing Jane. During Mary\\'s reign, Elizabeth was imprisoned for nearly a year on suspicion of supporting Protestant rebels.\\nUpon her half-sister\\'s death in 1558, Elizabeth succeeded to the throne and set out to rule by good counsel. She depended heavily on a group of trusted advisers led by William Cecil, whom she created Baron Burghley. One of her first actions as queen was the establishment of an English Protestant church, of which she became the supreme governor. This era, later named the Elizabethan Religious Settlement, would evolve into the Church of England. It was expected that Elizabeth would marry and produce an heir; however, despite numerous courtships, she never did, and because of this she is sometimes referred to as the \"Virgin Queen\". She was eventually succeeded by her first cousin twice removed, James VI of Scotland, the son of Mary, Queen of Scots.\\nIn government, Elizabeth was more moderate than her father and siblings had been. One of her mottoes was video et taceo (\"I see and keep silent\"). In religion, she was relatively tolerant and avoided systematic persecution. After the pope declared her illegitimate in 1570, which in theory released English Catholics from allegiance to her, several conspiracies threatened her life, all of which were defeated with the help of her ministers\\' secret service, run by Sir Francis Walsingham. Elizabeth was cautious in foreign affairs, manoeuvring between the major powers of France and Spain. She half-heartedly supported a number of ineffective, poorly resourced military campaigns in the Netherlands, France, and Ireland. By the mid-1580s, England could no longer avoid war with Spain.\\nAs she grew older, Elizabeth became celebrated for her virginity. A cult of personality grew around her which was celebrated in the portraits, pageants, and literature of the day. Elizabeth\\'s reign became known as the Elizabethan era. The period is famous for the flourishing of English drama, led by playwrights such as William Shakespeare and Christopher Marlowe, the prowess of English maritime adventurers, such as Francis Drake and Walter Raleigh, and for the defeat of the Spanish Armada. Some historians depict Elizabeth as a short-tempered, sometimes indecisive ruler, who enjoyed more than her fair share of luck. Towards the end of her reign, a series of economic and military problems weakened her popularity. Elizabeth is acknowledged as a charismatic performer (\"Gloriana\") and a dogged survivor (\"Good Queen Bess\") in an era when government was ramshackle and limited, and when monarchs in neighbouring countries faced internal problems that jeopardised their thrones. After the short, disastrous reigns of her half-siblings, her 44 years on the throne provided welcome stability for the kingdom and helped to forge a sense of national identity.', 'source': 'https://en.wikipedia.org/wiki/Elizabeth_I'}),\n", " Document(page_content='<PERSON> or <PERSON><PERSON><PERSON> (Russian: Елизаве́т<PERSON> Петро́вна; 29 December [O.S. 18 December] 1709 – 5 January [O.S. 25 December] 1762) reigned as Empress of Russia from 1741 until her death in 1762. She remains one of the most popular Russian monarchs because of her decision not to execute a single person during her reign, her numerous construction projects, and her strong opposition to Prussian policies.\\nThe second-eldest daughter of Tsar <PERSON> (r.\\u20091682–1725), <PERSON> lived through the confused successions of her father\\'s descendants following her half-brother <PERSON>'s death in 1718. The throne first passed to her mother <PERSON> of Russia (r.\\u20091725–1727), then to her nephew <PERSON>, who died in 1730 and was succeeded by <PERSON>'s first cousin <PERSON> (r.\\u20091730–1740). After the brief rule of <PERSON><PERSON>'s infant great-nephew, <PERSON>, <PERSON> seized the throne with the military\\'s support and declared her own nephew, the future <PERSON>, her heir.\\nDuring her reign <PERSON> continued the policies of her father and brought about a remarkable Age of Enlightenment in Russia. Her domestic policies allowed the nobles to gain dominance in local government while shortening their terms of service to the state. She encouraged <PERSON>'s foundation of the University of Moscow, the highest-ranking Russian educational institution. Her court became one of the most splendid in all Europe, especially regarding architecture: she modernised Russia\\'s roads, encouraged <PERSON>'s foundation of the Imperial Academy of Arts, and financed grandiose Baroque projects of her favourite architect, Bartolomeo Rastrelli, particularly in Peterhof Palace. The Winter Palace and the Smolny Cathedral in Saint Petersburg are among the chief monuments of her reign.\\nElizabeth led the Russian Empire during the two major European conflicts of her time: the War of Austrian Succession (1740–1748) and the Seven Years\\' War (1756–1763). She and diplomat Aleksey Bestuzhev-Ryumin solved the first event by forming an alliance with Austria and France, but indirectly caused the second. Russian troops enjoyed several victories against Prussia and briefly occupied Berlin, but when Frederick the Great was finally considering surrender in January 1762, the Russian Empress died. She was the last agnatic member of the House of Romanov to reign over the Russian Empire.\\n\\n\\n== Early life ==\\n\\n\\n=== Childhood and teenage years ===\\n\\nElizabeth was born at Kolomenskoye, near Moscow, Russia, on 18 December 1709 (O.S.). Her parents were Peter the Great, Tsar of Russia and Catherine. Catherine was the daughter of Samuel Skowroński, a subject of Grand Duchy of Lithuania. Although no documentary record exists, her parents were said to have married secretly at the Cathedral of the Holy Trinity in Saint Petersburg at some point between 23 October and 1 December 1707. Their official marriage was at Saint Isaac\\'s Cathedral in Saint Petersburg on 9 February 1712. On this day, the two children previously born to the couple (Anna and Elizabeth) were legitimised by their father and given the title of Tsarevna (\"princess\") on 6 March 1711. Of the twelve children born to Peter and Catherine (five sons and seven daughters), only the sisters survived to adulthood. They had one older surviving sibling, crown prince Alexei Petrovich, who was Peter\\'s son by his first wife, noblewoman Eudoxia Lopukhina.\\nAs a child, Elizabeth was the favourite of her father, whom she resembled both physically and temperamentally. Even though he adored his daughter, Peter did not devote time or attention to her education; having both a son and grandson from his first marriage to a noblewoman, he did not anticipate that a daughter born to his former maid might one day inherit the Russian throne, which had until that point never been occupied by a woman; as such, it was left to Catherine to raise the girls, a task met with considerable difficulty due to her own lack of education. Despite this, Elizabeth was', metadata={'title': 'Elizabeth of Russia', 'summary': \"Elizabeth or Elizaveta Petrovna (Russian: Елизаве́та Петро́вна; 29 December [O.S. 18 December] 1709 – 5 January [O.S. 25 December] 1762) reigned as Empress of Russia from 1741 until her death in 1762. She remains one of the most popular Russian monarchs because of her decision not to execute a single person during her reign, her numerous construction projects, and her strong opposition to Prussian policies.\\nThe second-eldest daughter of Tsar Peter the Great (r.\\u20091682–1725), Elizabeth lived through the confused successions of her father's descendants following her half-brother Alexei's death in 1718. The throne first passed to her mother Catherine I of Russia (r.\\u20091725–1727), then to her nephew Peter II, who died in 1730 and was succeeded by Elizabeth's first cousin Anna (r.\\u20091730–1740). After the brief rule of Anna's infant great-nephew, Ivan VI, Elizabeth seized the throne with the military's support and declared her own nephew, the future Peter III, her heir.\\nDuring her reign Elizabeth continued the policies of her father and brought about a remarkable Age of Enlightenment in Russia. Her domestic policies allowed the nobles to gain dominance in local government while shortening their terms of service to the state. She encouraged Mikhail Lomonosov's foundation of the University of Moscow, the highest-ranking Russian educational institution. Her court became one of the most splendid in all Europe, especially regarding architecture: she modernised Russia's roads, encouraged Ivan Shuvalov's foundation of the Imperial Academy of Arts, and financed grandiose Baroque projects of her favourite architect, Bartolomeo Rastrelli, particularly in Peterhof Palace. The Winter Palace and the Smolny Cathedral in Saint Petersburg are among the chief monuments of her reign.\\nElizabeth led the Russian Empire during the two major European conflicts of her time: the War of Austrian Succession (1740–1748) and the Seven Years' War (1756–1763). She and diplomat Aleksey Bestuzhev-Ryumin solved the first event by forming an alliance with Austria and France, but indirectly caused the second. Russian troops enjoyed several victories against Prussia and briefly occupied Berlin, but when Frederick the Great was finally considering surrender in January 1762, the Russian Empress died. She was the last agnatic member of the House of Romanov to reign over the Russian Empire.\", 'source': 'https://en.wikipedia.org/wiki/Elizabeth_of_Russia'}),\n", " Document(page_content='The succession to the childless <PERSON> I was an open question from her accession in 1558 to her death in 1603, when the crown passed to <PERSON> of Scotland. While the accession of <PERSON> went smoothly, the succession had been the subject of much debate for decades. In some scholarly views, it was a major political factor of the entire reign, even if not so voiced. Separate aspects have acquired their own nomenclature: the \"Norfolk conspiracy\", <PERSON>\\'s \"Elizabethan exclusion crisis\", and the \"Secret Correspondence\".\\nThe topics of debate remained obscured by uncertainty.\\n<PERSON><PERSON><PERSON><PERSON> I avoided establishing the order of succession in any form, presumably because she feared for her own life once a successor was named.  She was also concerned with England forming a productive relationship with Scotland, whose Catholic and Presbyterian strongholds were resistant to female leadership. Catholic women who would be submissive to the <PERSON> and not to English constitutional law were rejected.\\n<PERSON><PERSON><PERSON>\\'s will had named one male and seven females living at his death in 1547 as the line of succession: (1) his son <PERSON>, (2) <PERSON>, (3) <PERSON>, (4) <PERSON>, (5) <PERSON>, (6) <PERSON>, and (7) <PERSON>. <PERSON> had outlived all of them.\\nA number of authorities considered that the legal position hinged on documents such as the statute De natis ultra mare of <PERSON>, and the will of <PERSON>. There were different opinions about the application of these documents. Political, religious and military matters came to predominate later in <PERSON>'s reign, in the context of the Anglo-Spanish War.\\n\\n\\n== Cognatic descent from <PERSON> <PERSON> ==\\nDescent from the two daughters of <PERSON> <PERSON> who reached adulthood, <PERSON> and <PERSON>, was the first and main issue in the succession. \\n\\n\\n=== <PERSON> claim ===\\nMary I of England had died without managing to have her preferred successor and first cousin, <PERSON> <PERSON>, <PERSON> of <PERSON>, nominated by parliament. <PERSON> <PERSON> was a daughter of <PERSON> <PERSON>, and lived to 1578, but became a marginal figure in discussions of the succession to <PERSON> <PERSON>, who at no point clarified the dynastic issues of the <PERSON> line. When in 1565 Margaret Douglas\\'s elder son Henry Stuart, Lord Darnley, married Mary, Queen of Scots, the \"Lennox claim\" was generally regarded as consolidated into the \"Stuart claim\".\\n\\n\\n=== Stuart claimants ===\\nJames VI was the son of two grandchildren of Margaret Tudor. Arbella Stuart, the most serious other contender by the late 16th century, was the daughter of Margaret Douglas, Countess of Lennox\\'s younger son Charles Stuart, 1st Earl of Lennox.\\nJames VI\\'s mother, Mary, Queen of Scots, was considered a plausible successor to the English throne. At the beginning of Elizabeth\\'s reign she sent ambassadors to England when a parliament was summoned, anticipating a role for parliament in settling the succession in her favour. Mary was a Roman Catholic, and her proximity to the succession was a factor in plotting, making her position a political problem for the English government, eventually resolved by judicial means. She was executed in 1587. In that year Mary\\'s son James reached the age of twenty-one, while Arbella was only twelve.\\n\\n\\n=== Suffolk claimants ===\\nWhile the Stuart line of James and Arbella would have had political support, by 1600 the descendants of Mary Tudor were theoretically relevant, and on legal grounds could not be discounted. Frances Grey, Duchess of Suffolk, and Eleanor Clifford, Countess of Cumberland, both had children who were in the line of succession. Frances and Eleanor were Mary Tudor\\'s daughters by her second husband, Charles Brandon, 1st Duke of Suffolk. Frances married Henry Grey, 1st Duke of Suffolk, and they had three daughters, Lady Jane Grey (1537–1554), Lady Catherine Grey (1540–1568), and Lady Mary Grey (1545–1578). Of these, the two youngest lived into Queen Elizabeth\\'s reign.\\nCatherine\\'s first marriage to the youthful', metadata={'title': 'Succession to Elizabeth I', 'summary': 'The succession to the childless Elizabeth I was an open question from her accession in 1558 to her death in 1603, when the crown passed to James VI of Scotland. While the accession of James went smoothly, the succession had been the subject of much debate for decades. In some scholarly views, it was a major political factor of the entire reign, even if not so voiced. Separate aspects have acquired their own nomenclature: the \"Norfolk conspiracy\", Patrick Collinson\\'s \"Elizabethan exclusion crisis\", and the \"Secret Correspondence\".\\nThe topics of debate remained obscured by uncertainty.\\nElizabeth I avoided establishing the order of succession in any form, presumably because she feared for her own life once a successor was named.  She was also concerned with England forming a productive relationship with Scotland, whose Catholic and Presbyterian strongholds were resistant to female leadership. Catholic women who would be submissive to the Pope and not to English constitutional law were rejected.\\nHenry VIII\\'s will had named one male and seven females living at his death in 1547 as the line of succession: (1) his son Edward VI, (2) Mary I, (3) Elizabeth I, (4) Jane Grey, (5) Katherine Grey, (6) Mary Grey, and (7) Margaret Clifford. Elizabeth had outlived all of them.\\nA number of authorities considered that the legal position hinged on documents such as the statute De natis ultra mare of Edward III, and the will of Henry VIII. There were different opinions about the application of these documents. Political, religious and military matters came to predominate later in Elizabeth\\'s reign, in the context of the Anglo-Spanish War.', 'source': 'https://en.wikipedia.org/wiki/Succession_to_Elizabeth_I'})]"]}, "metadata": {}, "execution_count": 19}]}, {"cell_type": "code", "source": ["from langchain.text_splitter import TokenTextSplitter\n", "text_splitter = TokenTextSplitter(chunk_size=512, chunk_overlap=24)\n", "documents = text_splitter.split_documents(raw_documents[:3])"], "metadata": {"id": "5ChZ008I6paW"}, "execution_count": 20, "outputs": []}, {"cell_type": "code", "source": ["from langchain_openai import ChatOpenAI\n", "llm=ChatOpenAI(temperature=0, model_name=\"gpt-3.5-turbo-0125\")"], "metadata": {"id": "IMh_IpRb78rs"}, "execution_count": 21, "outputs": []}, {"cell_type": "code", "source": ["from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "llm_transformer = LLMGraphTransformer(llm=llm)"], "metadata": {"id": "Mer51fZA9pa1"}, "execution_count": 22, "outputs": []}, {"cell_type": "code", "source": ["graph_documents = llm_transformer.convert_to_graph_documents(documents)"], "metadata": {"id": "pZP64uFM9vLk"}, "execution_count": 23, "outputs": []}, {"cell_type": "code", "source": ["graph_documents"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3Nwjd5yR92VE", "outputId": "1c707732-3f56-4228-be36-5e376a481aac"}, "execution_count": 24, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[GraphDocument(nodes=[Node(id='<PERSON>', type='Person'), Node(id='England', type='Country'), Node(id='Ireland', type='Country'), Node(id='House Of Tudor', type='Royal family'), Node(id='<PERSON> Viii', type='Person'), Node(id='<PERSON>', type='Person'), Node(id='<PERSON> Vi', type='Person'), <PERSON>de(id='Lady <PERSON>', type='Person'), <PERSON>de(id='Mary', type='Person'), <PERSON>de(id='<PERSON>', type='Person'), Node(id='<PERSON>', type='Title'), Node(id='<PERSON> Vi Of Scotland', type='Person'), Node(id='<PERSON>, Queen <PERSON>', type='Person'), Node(id='France', type='Country'), Node(id='Spain', type='Country'), Node(id='Sir <PERSON>', type='Person')], relationships=[Relationship(source=Node(id='<PERSON>', type='Person'), target=Node(id='England', type='Country'), type='RULED'), Relationship(source=Node(id='<PERSON>', type='Person'), target=Node(id='Ireland', type='Country'), type='RULED'), Relationship(source=Node(id='Elizabeth I', type='Person'), target=Node(id='House Of Tudor', type='Royal family'), type='BELONGS_TO'), Relationship(source=Node(id='Elizabeth I', type='Person'), target=Node(id='Henry Viii', type='Person'), type='CHILD_OF'), Relationship(source=Node(id='Elizabeth I', type='Person'), target=Node(id='Anne Boleyn', type='Person'), type='CHILD_OF'), Relationship(source=Node(id='Elizabeth I', type='Person'), target=Node(id='Edward Vi', type='Person'), type='HALF-SISTER_OF'), Relationship(source=Node(id='Elizabeth I', type='Person'), target=Node(id='Lady Jane Grey', type='Person'), type='IGNORED_CLAIMS_OF'), Relationship(source=Node(id='Elizabeth I', type='Person'), target=Node(id='Mary', type='Person'), type='HALF-SISTER_OF'), Relationship(source=Node(id='Elizabeth I', type='Person'), target=Node(id='William Cecil', type='Person'), type='DEPENDS_ON'), Relationship(source=Node(id='Elizabeth I', type='Person'), target=Node(id='Baron Burghley', type='Title'), type='CREATED_TITLE'), Relationship(source=Node(id='Elizabeth I', type='Person'), target=Node(id='James Vi Of Scotland', type='Person'), type='SUCCEEDED_BY'), Relationship(source=Node(id='Elizabeth I', type='Person'), target=Node(id='Mary, Queen Of Scots', type='Person'), type='MOTHER_OF'), Relationship(source=Node(id='Elizabeth I', type='Person'), target=Node(id='France', type='Country'), type='MANOEUVRING_BETWEEN'), Relationship(source=Node(id='Elizabeth I', type='Person'), target=Node(id='Spain', type='Country'), type='MANOEUVRING_BETWEEN'), Relationship(source=Node(id='Elizabeth I', type='Person'), target=Node(id='Sir Francis Walsingham', type='Person'), type='DEPENDS_ON')], source=Document(page_content='Elizabeth I (7 September 1533 – 24 March 1603) was Queen of England and Ireland from 17 November 1558 until her death in 1603. She was the last monarch of the House of Tudor.\\nElizabeth was the only surviving child of Henry VIII and Anne Boleyn, his second wife, who was executed when Elizabeth was two years old. Anne\\'s marriage to Henry was annulled, and Elizabeth was declared illegitimate. Henry restored her to the line of succession when she was 10, via the Third Succession Act 1543. After Henry\\'s death in 1547, Elizabeth\\'s younger half-brother Edward VI ruled until his own death in 1553, bequeathing the crown to a Protestant cousin, Lady Jane Grey, and ignoring the claims of his two half-sisters, the Catholic Mary and the younger Elizabeth, in spite of statutes to the contrary. Edward\\'s will was set aside within weeks of his death and Mary became queen, deposing and executing Jane. During Mary\\'s reign, Elizabeth was imprisoned for nearly a year on suspicion of supporting Protestant rebels.\\nUpon her half-sister\\'s death in 1558, Elizabeth succeeded to the throne and set out to rule by good counsel. She depended heavily on a group of trusted advisers led by William Cecil, whom she created Baron Burghley. One of her first actions as queen was the establishment of an English Protestant church, of which she became the supreme governor. This era, later named the Elizabethan Religious Settlement, would evolve into the Church of England. It was expected that Elizabeth would marry and produce an heir; however, despite numerous courtships, she never did, and because of this she is sometimes referred to as the \"Virgin Queen\". She was eventually succeeded by her first cousin twice removed, James VI of Scotland, the son of Mary, Queen of Scots.\\nIn government, Elizabeth was more moderate than her father and siblings had been. One of her mottoes was video et taceo (\"I see and keep silent\"). In religion, she was relatively tolerant and avoided systematic persecution. After the pope declared her illegitimate in 1570, which in theory released English Catholics from allegiance to her, several conspiracies threatened her life, all of which were defeated with the help of her ministers\\' secret service, run by Sir Francis Walsingham. Elizabeth was cautious in foreign affairs, manoeuvring between the major powers of France and Spain. She half-heartedly supported a number of ineffective, poorly resourced military campaigns in the Netherlands, France,', metadata={'title': 'Elizabeth I', 'summary': 'Elizabeth I (7 September 1533 – 24 March 1603) was Queen of England and Ireland from 17 November 1558 until her death in 1603. She was the last monarch of the House of Tudor.\\nElizabeth was the only surviving child of Henry VIII and Anne Boleyn, his second wife, who was executed when Elizabeth was two years old. Anne\\'s marriage to Henry was annulled, and Elizabeth was declared illegitimate. Henry restored her to the line of succession when she was 10, via the Third Succession Act 1543. After Henry\\'s death in 1547, Elizabeth\\'s younger half-brother Edward VI ruled until his own death in 1553, bequeathing the crown to a Protestant cousin, Lady Jane Grey, and ignoring the claims of his two half-sisters, the Catholic Mary and the younger Elizabeth, in spite of statutes to the contrary. Edward\\'s will was set aside within weeks of his death and Mary became queen, deposing and executing Jane. During Mary\\'s reign, Elizabeth was imprisoned for nearly a year on suspicion of supporting Protestant rebels.\\nUpon her half-sister\\'s death in 1558, Elizabeth succeeded to the throne and set out to rule by good counsel. She depended heavily on a group of trusted advisers led by William Cecil, whom she created Baron Burghley. One of her first actions as queen was the establishment of an English Protestant church, of which she became the supreme governor. This era, later named the Elizabethan Religious Settlement, would evolve into the Church of England. It was expected that Elizabeth would marry and produce an heir; however, despite numerous courtships, she never did, and because of this she is sometimes referred to as the \"Virgin Queen\". She was eventually succeeded by her first cousin twice removed, James VI of Scotland, the son of Mary, Queen of Scots.\\nIn government, Elizabeth was more moderate than her father and siblings had been. One of her mottoes was video et taceo (\"I see and keep silent\"). In religion, she was relatively tolerant and avoided systematic persecution. After the pope declared her illegitimate in 1570, which in theory released English Catholics from allegiance to her, several conspiracies threatened her life, all of which were defeated with the help of her ministers\\' secret service, run by Sir Francis Walsingham. Elizabeth was cautious in foreign affairs, manoeuvring between the major powers of France and Spain. She half-heartedly supported a number of ineffective, poorly resourced military campaigns in the Netherlands, France, and Ireland. By the mid-1580s, England could no longer avoid war with Spain.\\nAs she grew older, Elizabeth became celebrated for her virginity. A cult of personality grew around her which was celebrated in the portraits, pageants, and literature of the day. Elizabeth\\'s reign became known as the Elizabethan era. The period is famous for the flourishing of English drama, led by playwrights such as William Shakespeare and Christopher Marlowe, the prowess of English maritime adventurers, such as Francis Drake and Walter Raleigh, and for the defeat of the Spanish Armada. Some historians depict Elizabeth as a short-tempered, sometimes indecisive ruler, who enjoyed more than her fair share of luck. Towards the end of her reign, a series of economic and military problems weakened her popularity. Elizabeth is acknowledged as a charismatic performer (\"Gloriana\") and a dogged survivor (\"Good Queen Bess\") in an era when government was ramshackle and limited, and when monarchs in neighbouring countries faced internal problems that jeopardised their thrones. After the short, disastrous reigns of her half-siblings, her 44 years on the throne provided welcome stability for the kingdom and helped to forge a sense of national identity.', 'source': 'https://en.wikipedia.org/wiki/Elizabeth_I'})),\n", " GraphDocument(nodes=[Node(id='Elizabeth', type='Person'), Node(id='Spain', type='Country'), Node(id='Netherlands', type='Country'), Node(id='France', type='Country'), Node(id='Ireland', type='Country'), Node(id='<PERSON>', type='Playwright'), Node(id='<PERSON>', type='Playwright'), Node(id='<PERSON>', type='Maritime adventurer'), Node(id='Walter Raleigh', type='Maritime adventurer'), Node(id='Spanish Armada', type='Event')], relationships=[Relationship(source=Node(id='Elizabeth', type='Person'), target=Node(id='Spain', type='Country'), type='WAR'), Relationship(source=Node(id='Elizabeth', type='Person'), target=Node(id='Netherlands', type='Country'), type='WAR'), Relationship(source=Node(id='Elizabeth', type='Person'), target=Node(id='France', type='Country'), type='WAR'), Relationship(source=Node(id='Elizabeth', type='Person'), target=Node(id='Ireland', type='Country'), type='WAR'), Relationship(source=Node(id='Elizabeth', type='Person'), target=Node(id='<PERSON>', type='Playwright'), type='INFLUENCED'), Relationship(source=Node(id='Elizabeth', type='Person'), target=Node(id='Christopher Marlowe', type='Playwright'), type='INFLUENCED'), Relationship(source=Node(id='Elizabeth', type='Person'), target=Node(id='Francis Drake', type='Maritime adventurer'), type='INFLUENCED'), Relationship(source=Node(id='Elizabeth', type='Person'), target=Node(id='Walter Raleigh', type='Maritime adventurer'), type='INFLUENCED'), Relationship(source=Node(id='Elizabeth', type='Person'), target=Node(id='Spanish Armada', type='Event'), type='DEFEATED')], source=Document(page_content=' and Spain. She half-heartedly supported a number of ineffective, poorly resourced military campaigns in the Netherlands, France, and Ireland. By the mid-1580s, England could no longer avoid war with Spain.\\nAs she grew older, Elizabeth became celebrated for her virginity. A cult of personality grew around her which was celebrated in the portraits, pageants, and literature of the day. Elizabeth\\'s reign became known as the Elizabethan era. The period is famous for the flourishing of English drama, led by playwrights such as William Shakespeare and Christopher Marlowe, the prowess of English maritime adventurers, such as Francis Drake and Walter Raleigh, and for the defeat of the Spanish Armada. Some historians depict Elizabeth as a short-tempered, sometimes indecisive ruler, who enjoyed more than her fair share of luck. Towards the end of her reign, a series of economic and military problems weakened her popularity. Elizabeth is acknowledged as a charismatic performer (\"Gloriana\") and a dogged survivor (\"Good Queen Bess\") in an era when government was ramshackle and limited, and when monarchs in neighbouring countries faced internal problems that jeopardised their thrones. After the short, disastrous reigns of her half-siblings, her 44 years on the throne provided welcome stability for the kingdom and helped to forge a sense of national identity.\\n\\n\\n== Early life ==\\n\\nElizabeth was born at Greenwich Palace on 7 September 1533 and was named after her grandmothers, Elizabeth of York and Lady Elizabeth Howard. She was the second child of Henry VIII of England born in wedlock to survive infancy. Her mother was Henry\\'s second wife, Anne Bol', metadata={'title': 'Elizabeth I', 'summary': 'Elizabeth I (7 September 1533 – 24 March 1603) was Queen of England and Ireland from 17 November 1558 until her death in 1603. She was the last monarch of the House of Tudor.\\nElizabeth was the only surviving child of Henry VIII and Anne Boleyn, his second wife, who was executed when Elizabeth was two years old. Anne\\'s marriage to Henry was annulled, and Elizabeth was declared illegitimate. Henry restored her to the line of succession when she was 10, via the Third Succession Act 1543. After Henry\\'s death in 1547, Elizabeth\\'s younger half-brother Edward VI ruled until his own death in 1553, bequeathing the crown to a Protestant cousin, Lady Jane Grey, and ignoring the claims of his two half-sisters, the Catholic Mary and the younger Elizabeth, in spite of statutes to the contrary. Edward\\'s will was set aside within weeks of his death and Mary became queen, deposing and executing Jane. During Mary\\'s reign, Elizabeth was imprisoned for nearly a year on suspicion of supporting Protestant rebels.\\nUpon her half-sister\\'s death in 1558, Elizabeth succeeded to the throne and set out to rule by good counsel. She depended heavily on a group of trusted advisers led by William Cecil, whom she created Baron Burghley. One of her first actions as queen was the establishment of an English Protestant church, of which she became the supreme governor. This era, later named the Elizabethan Religious Settlement, would evolve into the Church of England. It was expected that Elizabeth would marry and produce an heir; however, despite numerous courtships, she never did, and because of this she is sometimes referred to as the \"Virgin Queen\". She was eventually succeeded by her first cousin twice removed, James VI of Scotland, the son of Mary, Queen of Scots.\\nIn government, Elizabeth was more moderate than her father and siblings had been. One of her mottoes was video et taceo (\"I see and keep silent\"). In religion, she was relatively tolerant and avoided systematic persecution. After the pope declared her illegitimate in 1570, which in theory released English Catholics from allegiance to her, several conspiracies threatened her life, all of which were defeated with the help of her ministers\\' secret service, run by Sir Francis Walsingham. Elizabeth was cautious in foreign affairs, manoeuvring between the major powers of France and Spain. She half-heartedly supported a number of ineffective, poorly resourced military campaigns in the Netherlands, France, and Ireland. By the mid-1580s, England could no longer avoid war with Spain.\\nAs she grew older, Elizabeth became celebrated for her virginity. A cult of personality grew around her which was celebrated in the portraits, pageants, and literature of the day. Elizabeth\\'s reign became known as the Elizabethan era. The period is famous for the flourishing of English drama, led by playwrights such as William Shakespeare and Christopher Marlowe, the prowess of English maritime adventurers, such as Francis Drake and Walter Raleigh, and for the defeat of the Spanish Armada. Some historians depict Elizabeth as a short-tempered, sometimes indecisive ruler, who enjoyed more than her fair share of luck. Towards the end of her reign, a series of economic and military problems weakened her popularity. Elizabeth is acknowledged as a charismatic performer (\"Gloriana\") and a dogged survivor (\"Good Queen Bess\") in an era when government was ramshackle and limited, and when monarchs in neighbouring countries faced internal problems that jeopardised their thrones. After the short, disastrous reigns of her half-siblings, her 44 years on the throne provided welcome stability for the kingdom and helped to forge a sense of national identity.', 'source': 'https://en.wikipedia.org/wiki/Elizabeth_I'})),\n", " GraphDocument(nodes=[Node(id='<PERSON>', type='Person'), <PERSON>de(id='Empress <PERSON>', type='Title'), <PERSON>de(id='Tsar <PERSON>', type='Person'), <PERSON>de(id='<PERSON>', type='Person'), <PERSON>de(id='<PERSON>', type='Person'), <PERSON>de(id='<PERSON>', type='Person'), <PERSON>de(id='Ivan Vi', type='Person'), <PERSON><PERSON>(id='<PERSON>', type='Person'), <PERSON><PERSON>(id='<PERSON>', type='Person'), <PERSON>de(id='<PERSON>', type='Person'), <PERSON>de(id='<PERSON><PERSON><PERSON><PERSON>', type='Person'), Node(id='<PERSON><PERSON><PERSON>', type='Person'), Node(id='<PERSON>', type='Person')], relationships=[Relationship(source=Node(id='<PERSON>', type='Person'), target=Node(id='Empress Of Russia', type='Title'), type='HELD_TITLE'), Relationship(source=Node(id='<PERSON>', type='Person'), target=Node(id='Tsar <PERSON>', type='Person'), type='DAUGHTER_OF'), Relationship(source=Node(id='<PERSON>', type='Person'), target=Node(id='Catherine I Of Russia', type='Person'), type='DAUGHTER_OF'), Relationship(source=Node(id='Elizabeth Petrovna', type='Person'), target=Node(id='Peter Ii', type='Person'), type='RELATED_TO'), Relationship(source=Node(id='Elizabeth Petrovna', type='Person'), target=Node(id='Anna', type='Person'), type='RELATED_TO'), Relationship(source=Node(id='Elizabeth Petrovna', type='Person'), target=Node(id='Ivan Vi', type='Person'), type='RELATED_TO'), Relationship(source=Node(id='Elizabeth Petrovna', type='Person'), target=Node(id='Peter Iii', type='Person'), type='DECLARED_AS_HEIR'), Relationship(source=Node(id='Elizabeth Petrovna', type='Person'), target=Node(id='Mikhail Lomonosov', type='Person'), type='SUPPORTED_FOUNDATION_OF_UNIVERSITY_OF_MOSCOW'), Relationship(source=Node(id='Elizabeth Petrovna', type='Person'), target=Node(id='Ivan Shuvalov', type='Person'), type='ENCOURAGED_FOUNDATION_OF_IMPERIAL_ACADEMY_OF_ARTS'), Relationship(source=Node(id='Elizabeth Petrovna', type='Person'), target=Node(id='Bartolomeo Rastrelli', type='Person'), type='SUPPORTED_ARCHITECTURAL_PROJECTS_BY'), Relationship(source=Node(id='Elizabeth Petrovna', type='Person'), target=Node(id='Aleksey Bestuzhev-Ryumin', type='Person'), type='SOLVED_EUROPEAN_CONFLICT_WITH')], source=Document(page_content=\"Elizabeth or Elizaveta Petrovna (Russian: Елизаве́та Петро́вна; 29 December [O.S. 18 December] 1709 – 5 January [O.S. 25 December] 1762) reigned as Empress of Russia from 1741 until her death in 1762. She remains one of the most popular Russian monarchs because of her decision not to execute a single person during her reign, her numerous construction projects, and her strong opposition to Prussian policies.\\nThe second-eldest daughter of Tsar Peter the Great (r.\\u20091682–1725), Elizabeth lived through the confused successions of her father's descendants following her half-brother Alexei's death in 1718. The throne first passed to her mother Catherine I of Russia (r.\\u20091725–1727), then to her nephew Peter II, who died in 1730 and was succeeded by Elizabeth's first cousin Anna (r.\\u20091730–1740). After the brief rule of Anna's infant great-nephew, Ivan VI, Elizabeth seized the throne with the military's support and declared her own nephew, the future Peter III, her heir.\\nDuring her reign Elizabeth continued the policies of her father and brought about a remarkable Age of Enlightenment in Russia. Her domestic policies allowed the nobles to gain dominance in local government while shortening their terms of service to the state. She encouraged Mikhail Lomonosov's foundation of the University of Moscow, the highest-ranking Russian educational institution. Her court became one of the most splendid in all Europe, especially regarding architecture: she modernised Russia's roads, encouraged Ivan Shuvalov's foundation of the Imperial Academy of Arts, and financed grandiose Baroque projects of her favourite architect, Bartolomeo Rastrelli, particularly in Peterhof Palace. The Winter Palace and the Smolny Cathedral in Saint Petersburg are among the chief monuments of her reign.\\nElizabeth led the Russian Empire during the two major European conflicts of her time: the War of Austrian Succession (1740–1748) and the Seven Years' War (1756–1763). She and diplomat Aleksey Bestuzhev-Ryumin solved the first event by forming an alliance with Austria and France, but indirectly caused the second. Russian troops enjoyed several victories against Prussia and briefly occupied Berlin, but when Frederick the Great was finally considering surrender\", metadata={'title': 'Elizabeth of Russia', 'summary': \"Elizabeth or Elizaveta Petrovna (Russian: Елизаве́та Петро́вна; 29 December [O.S. 18 December] 1709 – 5 January [O.S. 25 December] 1762) reigned as Empress of Russia from 1741 until her death in 1762. She remains one of the most popular Russian monarchs because of her decision not to execute a single person during her reign, her numerous construction projects, and her strong opposition to Prussian policies.\\nThe second-eldest daughter of Tsar Peter the Great (r.\\u20091682–1725), Elizabeth lived through the confused successions of her father's descendants following her half-brother Alexei's death in 1718. The throne first passed to her mother Catherine I of Russia (r.\\u20091725–1727), then to her nephew Peter II, who died in 1730 and was succeeded by Elizabeth's first cousin Anna (r.\\u20091730–1740). After the brief rule of Anna's infant great-nephew, Ivan VI, Elizabeth seized the throne with the military's support and declared her own nephew, the future Peter III, her heir.\\nDuring her reign Elizabeth continued the policies of her father and brought about a remarkable Age of Enlightenment in Russia. Her domestic policies allowed the nobles to gain dominance in local government while shortening their terms of service to the state. She encouraged Mikhail Lomonosov's foundation of the University of Moscow, the highest-ranking Russian educational institution. Her court became one of the most splendid in all Europe, especially regarding architecture: she modernised Russia's roads, encouraged Ivan Shuvalov's foundation of the Imperial Academy of Arts, and financed grandiose Baroque projects of her favourite architect, Bartolomeo Rastrelli, particularly in Peterhof Palace. The Winter Palace and the Smolny Cathedral in Saint Petersburg are among the chief monuments of her reign.\\nElizabeth led the Russian Empire during the two major European conflicts of her time: the War of Austrian Succession (1740–1748) and the Seven Years' War (1756–1763). She and diplomat Aleksey Bestuzhev-Ryumin solved the first event by forming an alliance with Austria and France, but indirectly caused the second. Russian troops enjoyed several victories against Prussia and briefly occupied Berlin, but when Frederick the Great was finally considering surrender in January 1762, the Russian Empress died. She was the last agnatic member of the House of Romanov to reign over the Russian Empire.\", 'source': 'https://en.wikipedia.org/wiki/Elizabeth_of_Russia'})),\n", " GraphDocument(nodes=[Node(id='Elizabeth', type='Person'), Node(id='Russian Empire', type='Country'), Node(id='<PERSON>', type='Person'), Node(id='Russian Empress', type='Person'), Node(id='House Of Romanov', type='Family'), Node(id='<PERSON>', type='Person'), Node(id='<PERSON>', type='Person'), Node(id='<PERSON>', type='Person'), <PERSON><PERSON>(id='Grand Duchy Of Lithuania', type='Country'), <PERSON>de(id='Anna', type='Person'), Node(id='<PERSON><PERSON>na', type='Title'), Node(id='<PERSON>', type='Person'), Node(id='<PERSON><PERSON>xia <PERSON>', type='Person')], relationships=[Relationship(source=Node(id='Elizabeth', type='Person'), target=Node(id='Peter The Great', type='Person'), type='PARENT'), Relationship(source=Node(id='Elizabeth', type='Person'), target=Node(id='Catherine', type='Person'), type='PARENT'), Relationship(source=Node(id='<PERSON>', type='Person'), target=Node(id='<PERSON>', type='Person'), type='SPOUSE'), Relationship(source=Node(id='Peter The Great', type='Person'), target=Node(id='Anna', type='Person'), type='PARENT'), Relationship(source=Node(id='Peter The Great', type='Person'), target=Node(id='Elizabeth', type='Person'), type='PARENT'), Relationship(source=Node(id='Peter The Great', type='Person'), target=Node(id='Alexei Petrovich', type='Person'), type='PARENT'), Relationship(source=Node(id='Peter The Great', type='Person'), target=Node(id='Eudoxia Lopukhina', type='Person'), type='SPOUSE'), Relationship(source=Node(id='Catherine', type='Person'), target=Node(id='Samuel Skowroński', type='Person'), type='PARENT'), Relationship(source=Node(id='Catherine', type='Person'), target=Node(id='Grand Duchy Of Lithuania', type='Country'), type='BIRTHPLACE'), Relationship(source=Node(id='Anna', type='Person'), target=Node(id='Peter The Great', type='Person'), type='PARENT'), Relationship(source=Node(id='Elizabeth', type='Person'), target=Node(id='Tsarevna', type='Title'), type='RECEIVED_TITLE'), Relationship(source=Node(id='Peter The Great', type='Person'), target=Node(id='Tsarevna', type='Title'), type='PARENT_GRANTED_TITLE'), Relationship(source=Node(id='Peter The Great', type='Person'), target=Node(id='Catherine', type='Person'), type='MARRIAGE'), Relationship(source=Node(id='Peter The Great', type='Person'), target=Node(id='Catherine', type='Person'), type='OFFICIAL_MARRIAGE')], source=Document(page_content=' second. Russian troops enjoyed several victories against Prussia and briefly occupied Berlin, but when Frederick the Great was finally considering surrender in January 1762, the Russian Empress died. She was the last agnatic member of the House of Romanov to reign over the Russian Empire.\\n\\n\\n== Early life ==\\n\\n\\n=== Childhood and teenage years ===\\n\\nElizabeth was born at Kolomenskoye, near Moscow, Russia, on 18 December 1709 (O.S.). Her parents were Peter the Great, Tsar of Russia and Catherine. Catherine was the daughter of Samuel Skowroński, a subject of Grand Duchy of Lithuania. Although no documentary record exists, her parents were said to have married secretly at the Cathedral of the Holy Trinity in Saint Petersburg at some point between 23 October and 1 December 1707. Their official marriage was at Saint Isaac\\'s Cathedral in Saint Petersburg on 9 February 1712. On this day, the two children previously born to the couple (Anna and Elizabeth) were legitimised by their father and given the title of Tsarevna (\"princess\") on 6 March 1711. Of the twelve children born to Peter and Catherine (five sons and seven daughters), only the sisters survived to adulthood. They had one older surviving sibling, crown prince Alexei Petrovich, who was Peter\\'s son by his first wife, noblewoman Eudoxia Lopukhina.\\nAs a child, Elizabeth was the favourite of her father, whom she resembled both physically and temperamentally. Even though he adored his daughter, Peter did not devote time or attention to her education; having both a son and grandson from his first marriage to a noblewoman, he did not anticipate that a daughter born to his former maid might one day inherit the Russian throne, which had until that point never been occupied by a woman; as such, it was left to Catherine to raise the girls, a task met with considerable difficulty due to her own lack of education. Despite this, Elizabeth was', metadata={'title': 'Elizabeth of Russia', 'summary': \"Elizabeth or Elizaveta Petrovna (Russian: Елизаве́та Петро́вна; 29 December [O.S. 18 December] 1709 – 5 January [O.S. 25 December] 1762) reigned as Empress of Russia from 1741 until her death in 1762. She remains one of the most popular Russian monarchs because of her decision not to execute a single person during her reign, her numerous construction projects, and her strong opposition to Prussian policies.\\nThe second-eldest daughter of Tsar Peter the Great (r.\\u20091682–1725), Elizabeth lived through the confused successions of her father's descendants following her half-brother Alexei's death in 1718. The throne first passed to her mother Catherine I of Russia (r.\\u20091725–1727), then to her nephew Peter II, who died in 1730 and was succeeded by Elizabeth's first cousin Anna (r.\\u20091730–1740). After the brief rule of Anna's infant great-nephew, Ivan VI, Elizabeth seized the throne with the military's support and declared her own nephew, the future Peter III, her heir.\\nDuring her reign Elizabeth continued the policies of her father and brought about a remarkable Age of Enlightenment in Russia. Her domestic policies allowed the nobles to gain dominance in local government while shortening their terms of service to the state. She encouraged Mikhail Lomonosov's foundation of the University of Moscow, the highest-ranking Russian educational institution. Her court became one of the most splendid in all Europe, especially regarding architecture: she modernised Russia's roads, encouraged Ivan Shuvalov's foundation of the Imperial Academy of Arts, and financed grandiose Baroque projects of her favourite architect, Bartolomeo Rastrelli, particularly in Peterhof Palace. The Winter Palace and the Smolny Cathedral in Saint Petersburg are among the chief monuments of her reign.\\nElizabeth led the Russian Empire during the two major European conflicts of her time: the War of Austrian Succession (1740–1748) and the Seven Years' War (1756–1763). She and diplomat Aleksey Bestuzhev-Ryumin solved the first event by forming an alliance with Austria and France, but indirectly caused the second. Russian troops enjoyed several victories against Prussia and briefly occupied Berlin, but when Frederick the Great was finally considering surrender in January 1762, the Russian Empress died. She was the last agnatic member of the House of Romanov to reign over the Russian Empire.\", 'source': 'https://en.wikipedia.org/wiki/Elizabeth_of_Russia'})),\n", " GraphDocument(nodes=[Node(id='<PERSON>', type='Person'), Node(id='<PERSON>i Of Scotland', type='Person'), Node(id='Norfolk Conspiracy', type='Concept'), Node(id=\"<PERSON>'S Elizabethan Exclusion Crisis\", type='Concept'), Node(id='Secret Correspondence', type='Concept'), Node(id='Henry Viii', type='Person'), <PERSON>de(id='Edward Vi', type='Person'), <PERSON><PERSON>(id='<PERSON> I', type='Person'), <PERSON>de(id='<PERSON>', type='Person'), <PERSON>de(id='<PERSON>', type='Person'), <PERSON>de(id='<PERSON>', type='Person'), Node(id='<PERSON>', type='Person'), <PERSON>de(id='<PERSON>', type='Person'), Node(id='<PERSON>, Lord Dar<PERSON>ley', type='Person'), <PERSON>de(id='<PERSON>, Queen Of Scots', type='Person')], relationships=[Relationship(source=Node(id='<PERSON>', type='Person'), target=Node(id='<PERSON>i Of Scotland', type='Person'), type='SUCCESSOR'), Relationship(source=Node(id='Henry Viii', type='Person'), target=Node(id='Edward Vi', type='Person'), type='SUCCESSOR'), Relationship(source=Node(id='Henry Viii', type='Person'), target=Node(id='Mary I', type='Person'), type='SUCCESSOR'), Relationship(source=Node(id='Henry Viii', type='Person'), target=Node(id='Elizabeth I', type='Person'), type='SUCCESSOR'), Relationship(source=Node(id='Henry Viii', type='Person'), target=Node(id='Jane Grey', type='Person'), type='SUCCESSOR'), Relationship(source=Node(id='Henry Viii', type='Person'), target=Node(id='Katherine Grey', type='Person'), type='SUCCESSOR'), Relationship(source=Node(id='Henry Viii', type='Person'), target=Node(id='Mary Grey', type='Person'), type='SUCCESSOR'), Relationship(source=Node(id='Henry Viii', type='Person'), target=Node(id='Margaret Clifford', type='Person'), type='SUCCESSOR'), Relationship(source=Node(id='Mary I', type='Person'), target=Node(id='Margaret Douglas', type='Person'), type='PREFERRED_SUCCESSOR'), Relationship(source=Node(id='Margaret Douglas', type='Person'), target=Node(id='Henry Stuart, Lord Darnley', type='Person'), type='FAMILY_RELATION'), Relationship(source=Node(id='Henry Stuart, Lord Darnley', type='Person'), target=Node(id='Mary, Queen Of Scots', type='Person'), type='MARRIAGE')], source=Document(page_content='The succession to the childless Elizabeth I was an open question from her accession in 1558 to her death in 1603, when the crown passed to James VI of Scotland. While the accession of James went smoothly, the succession had been the subject of much debate for decades. In some scholarly views, it was a major political factor of the entire reign, even if not so voiced. Separate aspects have acquired their own nomenclature: the \"Norfolk conspiracy\", Patrick Collinson\\'s \"Elizabethan exclusion crisis\", and the \"Secret Correspondence\".\\nThe topics of debate remained obscured by uncertainty.\\nElizabeth I avoided establishing the order of succession in any form, presumably because she feared for her own life once a successor was named.  She was also concerned with England forming a productive relationship with Scotland, whose Catholic and Presbyterian strongholds were resistant to female leadership. Catholic women who would be submissive to the Pope and not to English constitutional law were rejected.\\nHenry VIII\\'s will had named one male and seven females living at his death in 1547 as the line of succession: (1) his son Edward VI, (2) Mary I, (3) Elizabeth I, (4) Jane Grey, (5) Katherine Grey, (6) Mary Grey, and (7) Margaret Clifford. Elizabeth had outlived all of them.\\nA number of authorities considered that the legal position hinged on documents such as the statute De natis ultra mare of Edward III, and the will of Henry VIII. There were different opinions about the application of these documents. Political, religious and military matters came to predominate later in Elizabeth\\'s reign, in the context of the Anglo-Spanish War.\\n\\n\\n== Cognatic descent from Henry VII ==\\nDescent from the two daughters of Henry VII who reached adulthood, Margaret and Mary, was the first and main issue in the succession. \\n\\n\\n=== Lennox claim ===\\nMary I of England had died without managing to have her preferred successor and first cousin, Margaret Douglas, Countess of Lennox, nominated by parliament. Margaret Douglas was a daughter of Margaret Tudor, and lived to 1578, but became a marginal figure in discussions of the succession to Elizabeth I, who at no point clarified the dynastic issues of the Tudor line. When in 1565 Margaret Douglas\\'s elder son Henry Stuart, Lord Darnley, married Mary, Queen of Scots, the \"Lennox claim\" was generally regarded as consolidated into the \"Stuart claim\".', metadata={'title': 'Succession to Elizabeth I', 'summary': 'The succession to the childless Elizabeth I was an open question from her accession in 1558 to her death in 1603, when the crown passed to James VI of Scotland. While the accession of James went smoothly, the succession had been the subject of much debate for decades. In some scholarly views, it was a major political factor of the entire reign, even if not so voiced. Separate aspects have acquired their own nomenclature: the \"Norfolk conspiracy\", Patrick Collinson\\'s \"Elizabethan exclusion crisis\", and the \"Secret Correspondence\".\\nThe topics of debate remained obscured by uncertainty.\\nElizabeth I avoided establishing the order of succession in any form, presumably because she feared for her own life once a successor was named.  She was also concerned with England forming a productive relationship with Scotland, whose Catholic and Presbyterian strongholds were resistant to female leadership. Catholic women who would be submissive to the Pope and not to English constitutional law were rejected.\\nHenry VIII\\'s will had named one male and seven females living at his death in 1547 as the line of succession: (1) his son Edward VI, (2) Mary I, (3) Elizabeth I, (4) Jane Grey, (5) Katherine Grey, (6) Mary Grey, and (7) Margaret Clifford. Elizabeth had outlived all of them.\\nA number of authorities considered that the legal position hinged on documents such as the statute De natis ultra mare of Edward III, and the will of Henry VIII. There were different opinions about the application of these documents. Political, religious and military matters came to predominate later in Elizabeth\\'s reign, in the context of the Anglo-Spanish War.', 'source': 'https://en.wikipedia.org/wiki/Succession_to_Elizabeth_I'})),\n", " GraphDocument(nodes=[Node(id='<PERSON>', type='Person'), Node(id='Arb<PERSON> Stuart', type='Person'), Node(id='<PERSON>', type='Person'), Node(id='<PERSON>', type='Person'), Node(id='<PERSON> Vi', type='Person'), Node(id='<PERSON>, Queen <PERSON>', type='Person'), Node(id='<PERSON>', type='Person'), Node(id='<PERSON>', type='Person'), <PERSON>de(id='<PERSON>', type='Person'), Node(id='<PERSON>', type='Person'), Node(id='<PERSON>', type='Person'), Node(id='<PERSON>', type='Person'), Node(id='<PERSON>', type='Person'), Node(id='<PERSON>', type='Person')], relationships=[Relationship(source=Node(id='<PERSON>', type='Person'), target=Node(id='<PERSON> Vi', type='Person'), type='GRANDPARENT'), Relationship(source=Node(id='<PERSON>', type='Person'), target=Node(id='Arb<PERSON> Stuart', type='Person'), type='GRANDPARENT'), Relationship(source=Node(id='<PERSON>', type='Person'), target=Node(id='Charles Stuart', type='Person'), type='PARENT'), Relationship(source=Node(id='Charles Stuart', type='Person'), target=Node(id='Arbella Stuart', type='Person'), type='PARENT'), Relationship(source=Node(id='Mary, Queen Of Scots', type='Person'), target=Node(id='James Vi', type='Person'), type='PARENT'), Relationship(source=Node(id='Mary, Queen Of Scots', type='Person'), target=Node(id='Arbella Stuart', type='Person'), type='PARENT'), Relationship(source=Node(id='Mary, Queen Of Scots', type='Person'), target=Node(id='Elizabeth', type='Person'), type='SUCCESSOR'), Relationship(source=Node(id='Frances Grey', type='Person'), target=Node(id='Lady Jane Grey', type='Person'), type='PARENT'), Relationship(source=Node(id='Frances Grey', type='Person'), target=Node(id='Lady Catherine Grey', type='Person'), type='PARENT'), Relationship(source=Node(id='Frances Grey', type='Person'), target=Node(id='Lady Mary Grey', type='Person'), type='PARENT'), Relationship(source=Node(id='Eleanor Clifford', type='Person'), target=Node(id='Lady Jane Grey', type='Person'), type='PARENT'), Relationship(source=Node(id='Eleanor Clifford', type='Person'), target=Node(id='Lady Catherine Grey', type='Person'), type='PARENT'), Relationship(source=Node(id='Eleanor Clifford', type='Person'), target=Node(id='Lady Mary Grey', type='Person'), type='PARENT'), Relationship(source=Node(id='Charles Brandon', type='Person'), target=Node(id='Frances Grey', type='Person'), type='SPOUSE'), Relationship(source=Node(id='Charles Brandon', type='Person'), target=Node(id='Eleanor Clifford', type='Person'), type='SPOUSE'), Relationship(source=Node(id='Henry Grey', type='Person'), target=Node(id='Frances Grey', type='Person'), type='SPOUSE')], source=Document(page_content=', Queen of Scots, the \"Lennox claim\" was generally regarded as consolidated into the \"Stuart claim\".\\n\\n\\n=== Stuart claimants ===\\nJames VI was the son of two grandchildren of Margaret Tudor. Arbella Stuart, the most serious other contender by the late 16th century, was the daughter of Margaret Douglas, Countess of Lennox\\'s younger son Charles Stuart, 1st Earl of Lennox.\\nJames VI\\'s mother, Mary, Queen of Scots, was considered a plausible successor to the English throne. At the beginning of Elizabeth\\'s reign she sent ambassadors to England when a parliament was summoned, anticipating a role for parliament in settling the succession in her favour. Mary was a Roman Catholic, and her proximity to the succession was a factor in plotting, making her position a political problem for the English government, eventually resolved by judicial means. She was executed in 1587. In that year Mary\\'s son James reached the age of twenty-one, while Arbella was only twelve.\\n\\n\\n=== Suffolk claimants ===\\nWhile the Stuart line of James and Arbella would have had political support, by 1600 the descendants of Mary Tudor were theoretically relevant, and on legal grounds could not be discounted. Frances Grey, Duchess of Suffolk, and Eleanor Clifford, Countess of Cumberland, both had children who were in the line of succession. Frances and Eleanor were Mary Tudor\\'s daughters by her second husband, Charles Brandon, 1st Duke of Suffolk. Frances married Henry Grey, 1st Duke of Suffolk, and they had three daughters, Lady Jane Grey (1537–1554), Lady Catherine Grey (1540–1568), and Lady Mary Grey (1545–1578). Of these, the two youngest lived into Queen Elizabeth\\'s reign.\\nCatherine\\'s first marriage to the youthful', metadata={'title': 'Succession to Elizabeth I', 'summary': 'The succession to the childless Elizabeth I was an open question from her accession in 1558 to her death in 1603, when the crown passed to James VI of Scotland. While the accession of James went smoothly, the succession had been the subject of much debate for decades. In some scholarly views, it was a major political factor of the entire reign, even if not so voiced. Separate aspects have acquired their own nomenclature: the \"Norfolk conspiracy\", Patrick Collinson\\'s \"Elizabethan exclusion crisis\", and the \"Secret Correspondence\".\\nThe topics of debate remained obscured by uncertainty.\\nElizabeth I avoided establishing the order of succession in any form, presumably because she feared for her own life once a successor was named.  She was also concerned with England forming a productive relationship with Scotland, whose Catholic and Presbyterian strongholds were resistant to female leadership. Catholic women who would be submissive to the Pope and not to English constitutional law were rejected.\\nHenry VIII\\'s will had named one male and seven females living at his death in 1547 as the line of succession: (1) his son Edward VI, (2) Mary I, (3) Elizabeth I, (4) Jane Grey, (5) Katherine Grey, (6) Mary Grey, and (7) Margaret Clifford. Elizabeth had outlived all of them.\\nA number of authorities considered that the legal position hinged on documents such as the statute De natis ultra mare of Edward III, and the will of Henry VIII. There were different opinions about the application of these documents. Political, religious and military matters came to predominate later in Elizabeth\\'s reign, in the context of the Anglo-Spanish War.', 'source': 'https://en.wikipedia.org/wiki/Succession_to_Elizabeth_I'}))]"]}, "metadata": {}, "execution_count": 24}]}, {"cell_type": "code", "source": ["graph.add_graph_documents(\n", "    graph_documents,\n", "    baseEntityLabel=True,\n", "    include_source=True\n", ")"], "metadata": {"id": "ib_g3U1d97th"}, "execution_count": 25, "outputs": []}, {"cell_type": "code", "source": ["# directly show the graph resulting from the given Cypher query\n", "default_cypher = \"MATCH (s)-[r:!MENTIONS]->(t) RETURN s,r,t LIMIT 50\""], "metadata": {"id": "rC-4O5FQ99yH"}, "execution_count": 26, "outputs": []}, {"cell_type": "code", "source": ["from yfiles_jupyter_graphs import GraphWidget\n", "from neo4j import GraphDatabase"], "metadata": {"id": "K-91BluK_62t"}, "execution_count": 25, "outputs": []}, {"cell_type": "code", "source": ["try:\n", "  import google.colab\n", "  from google.colab import output\n", "  output.enable_custom_widget_manager()\n", "except:\n", "  pass"], "metadata": {"id": "djVL6Gh4_4sV"}, "execution_count": 26, "outputs": []}, {"cell_type": "code", "source": ["def showGraph(cypher: str = default_cypher):\n", "    # create a neo4j session to run queries\n", "    driver = GraphDatabase.driver(\n", "        uri = os.environ[\"NEO4J_URI\"],\n", "        auth = (os.environ[\"NEO4J_USERNAME\"],\n", "                os.environ[\"NEO4J_PASSWORD\"]))\n", "    session = driver.session()\n", "    widget = GraphWidget(graph = session.run(cypher).graph())\n", "    widget.node_label_mapping = 'id'\n", "    display(widget)\n", "    return widget"], "metadata": {"id": "0Ll2WNnO-Ahf"}, "execution_count": 27, "outputs": []}, {"cell_type": "code", "source": ["showGraph()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["e8b6acc77d8f4d208b74b4f1d05144e5", "5caa1675fb9b47e89ceeab4a5aabb705"]}, "id": "kz-O4c0k-C_4", "outputId": "9d9fa858-6d4b-45cb-bc6c-9e39297ffbef"}, "execution_count": 28, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["GraphWidget(layout=Layout(height='800px', width='100%'))"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "e8b6acc77d8f4d208b74b4f1d05144e5"}}, "metadata": {"application/vnd.jupyter.widget-view+json": {"colab": {"custom_widget_manager": {"url": "https://ssl.gstatic.com/colaboratory-static/widgets/colab-cdn-widget-manager/2b70e893a8ba7c0f/manager.min.js"}}}}}, {"output_type": "display_data", "data": {"text/plain": ["GraphWidget(layout=Layout(height='800px', width='100%'))"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "e8b6acc77d8f4d208b74b4f1d05144e5"}}, "metadata": {"application/vnd.jupyter.widget-view+json": {"colab": {"custom_widget_manager": {"url": "https://ssl.gstatic.com/colaboratory-static/widgets/colab-cdn-widget-manager/2b70e893a8ba7c0f/manager.min.js"}}}}}]}, {"cell_type": "code", "source": ["from typing import Tuple, List, Optional"], "metadata": {"id": "zHSkb7LeBghn"}, "execution_count": 29, "outputs": []}, {"cell_type": "code", "source": ["from langchain_community.vectorstores import Neo4jVector"], "metadata": {"id": "TuDVi4vHBjXP"}, "execution_count": 49, "outputs": []}, {"cell_type": "code", "source": ["from langchain_openai import OpenAIEmbeddings\n", "vector_index = Neo4jVector.from_existing_graph(\n", "    OpenAIEmbeddings(),\n", "    search_type=\"hybrid\",\n", "    node_label=\"Document\",\n", "    text_node_properties=[\"text\"],\n", "    embedding_node_property=\"embedding\"\n", ")"], "metadata": {"id": "M_JloAimBlcK"}, "execution_count": 29, "outputs": []}, {"cell_type": "code", "source": ["graph.query(\"CREATE FULLTEXT INDEX entity IF NOT EXISTS FOR (e:__Entity__) ON EACH [e.id]\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "e0EXdSStG-Oe", "outputId": "d8c21f17-913c-4af0-acdb-1a9eb49dbba7"}, "execution_count": 31, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[]"]}, "metadata": {}, "execution_count": 31}]}, {"cell_type": "code", "source": ["from langchain_core.pydantic_v1 import BaseModel, Field\n", "# Extract entities from text\n", "class Entities(BaseModel):\n", "    \"\"\"Identifying information about entities.\"\"\"\n", "\n", "    names: List[str] = Field(\n", "        ...,\n", "        description=\"All the person, organization, or business entities that \"\n", "        \"appear in the text\",\n", "    )\n"], "metadata": {"id": "qksArGKrAvie"}, "execution_count": 30, "outputs": []}, {"cell_type": "code", "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.prompts.prompt import PromptTemplate"], "metadata": {"id": "Mx6sfpgRBrs-"}, "execution_count": 34, "outputs": []}, {"cell_type": "code", "source": ["prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are extracting organization and person entities from the text.\",\n", "        ),\n", "        (\n", "            \"human\",\n", "            \"Use the given format to extract information from the following \"\n", "            \"input: {question}\",\n", "        ),\n", "    ]\n", ")"], "metadata": {"id": "xUobRC1wAx-_"}, "execution_count": 32, "outputs": []}, {"cell_type": "code", "source": ["entity_chain = prompt | llm.with_structured_output(Entities)"], "metadata": {"id": "KGR6ocjkA0I_"}, "execution_count": 33, "outputs": []}, {"cell_type": "code", "source": ["entity_chain.invoke({\"question\": \"Where was <PERSON> born?\"}).names"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xPLkIEmkA2R2", "outputId": "fecf9433-32c4-4203-ad94-ca1c56ee60ee"}, "execution_count": 34, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['<PERSON>']"]}, "metadata": {}, "execution_count": 34}]}, {"cell_type": "code", "source": ["from langchain_community.vectorstores.neo4j_vector import remove_lucene_chars"], "metadata": {"id": "RpbOzL5BA6hW"}, "execution_count": 35, "outputs": []}, {"cell_type": "code", "source": ["def generate_full_text_query(input: str) -> str:\n", "    full_text_query = \"\"\n", "    words = [el for el in remove_lucene_chars(input).split() if el]\n", "    for word in words[:-1]:\n", "        full_text_query += f\" {word}~2 AND\"\n", "    full_text_query += f\" {words[-1]}~2\"\n", "    return full_text_query.strip()\n"], "metadata": {"id": "7gHCkvGKA86t"}, "execution_count": 36, "outputs": []}, {"cell_type": "code", "source": ["# Fulltext index query\n", "def structured_retriever(question: str) -> str:\n", "    result = \"\"\n", "    entities = entity_chain.invoke({\"question\": question})\n", "    for entity in entities.names:\n", "        response = graph.query(\n", "            \"\"\"CALL db.index.fulltext.queryNodes('entity', $query, {limit:2})\n", "            YIELD node,score\n", "            CALL {\n", "              WITH node\n", "              MATCH (node)-[r:!MENTIONS]->(neighbor)\n", "              RETURN node.id + ' - ' + type(r) + ' -> ' + neighbor.id AS output\n", "              UNION ALL\n", "              WITH node\n", "              MATCH (node)<-[r:!MENTIONS]-(neighbor)\n", "              RETURN neighbor.id + ' - ' + type(r) + ' -> ' +  node.id AS output\n", "            }\n", "            RETURN output LIMIT 50\n", "            \"\"\",\n", "            {\"query\": generate_full_text_query(entity)},\n", "        )\n", "        result += \"\\n\".join([el['output'] for el in response])\n", "    return result"], "metadata": {"id": "kjPkmFJbA_lv"}, "execution_count": 37, "outputs": []}, {"cell_type": "code", "source": ["print(structured_retriever(\"Who is <PERSON>?\"))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "nIla9QpzBA8u", "outputId": "c521c295-5964-45bd-9ce3-29c65ad3f823"}, "execution_count": 38, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<PERSON> I - RULED -> England\n", "<PERSON> I - RULED -> Ireland\n", "<PERSON> I - BELONGS_TO -> House Of Tudor\n", "<PERSON> I - CHILD_OF -> <PERSON> Viii\n", "<PERSON> - CHILD_OF -> <PERSON>\n", "<PERSON> I - HALF-SISTER_OF -> <PERSON>\n", "<PERSON> I - HALF-SISTER_OF -> Mary\n", "<PERSON> - IGNORED_CLAIMS_OF -> Lady <PERSON>\n", "<PERSON> I - DEPENDS_ON -> <PERSON>\n", "<PERSON> I - DEPENDS_ON -> Sir <PERSON>\n", "<PERSON> I - CREATED_TITLE -> <PERSON>\n", "<PERSON> I - SUCCEEDED_BY -> <PERSON> Of Scotland\n", "<PERSON> I - MOTHER_OF -> <PERSON>, Queen Of Scots\n", "<PERSON> I - MANOEUVRING_BETWEEN -> France\n", "<PERSON> I - MANOEUVRING_BETWEEN -> Spain\n", "<PERSON> - SUCCESSOR -> <PERSON> Scotland\n", "<PERSON>iii - SUCCESSOR -> <PERSON>\n"]}]}, {"cell_type": "code", "source": ["def retriever(question: str):\n", "    print(f\"Search query: {question}\")\n", "    structured_data = structured_retriever(question)\n", "    unstructured_data = [el.page_content for el in vector_index.similarity_search(question)]\n", "    final_data = f\"\"\"Structured data:\n", "{structured_data}\n", "Unstructured data:\n", "{\"#Document \". join(unstructured_data)}\n", "    \"\"\"\n", "    return final_data"], "metadata": {"id": "Zo1QoB_iBDfO"}, "execution_count": 39, "outputs": []}, {"cell_type": "code", "source": ["_template = \"\"\"Given the following conversation and a follow up question, rephrase the follow up question to be a standalone question,\n", "in its original language.\n", "Chat History:\n", "{chat_history}\n", "Follow Up Input: {question}\n", "Standalone question:\"\"\""], "metadata": {"id": "nDLnOXBTBFaf"}, "execution_count": 40, "outputs": []}, {"cell_type": "code", "source": ["CONDENSE_QUESTION_PROMPT = PromptTemplate.from_template(_template)"], "metadata": {"id": "hozfZicpBG2G"}, "execution_count": 41, "outputs": []}, {"cell_type": "code", "source": ["def _format_chat_history(chat_history: List[Tuple[str, str]]) -> List:\n", "    buffer = []\n", "    for human, ai in chat_history:\n", "        buffer.append(HumanMessage(content=human))\n", "        buffer.append(AIMessage(content=ai))\n", "    return buffer"], "metadata": {"id": "A9Oi3AEeBIPf"}, "execution_count": 42, "outputs": []}, {"cell_type": "code", "source": ["_search_query = RunnableBranch(\n", "    # If input includes chat_history, we condense it with the follow-up question\n", "    (\n", "        RunnableLambda(lambda x: bool(x.get(\"chat_history\"))).with_config(\n", "            run_name=\"HasChatHistoryCheck\"\n", "        ),  # Condense follow-up question and chat into a standalone_question\n", "        RunnablePassthrough.assign(\n", "            chat_history=lambda x: _format_chat_history(x[\"chat_history\"])\n", "        )\n", "        | CONDENSE_QUESTION_PROMPT\n", "        | ChatOpenAI(temperature=0)\n", "        | StrOutputParser(),\n", "    ),\n", "    # <PERSON><PERSON>, we have no chat history, so just pass through the question\n", "    RunnableLambda(lambda x : x[\"question\"]),\n", ")"], "metadata": {"id": "vXV65bjDBJwO"}, "execution_count": 43, "outputs": []}, {"cell_type": "code", "source": ["template = \"\"\"Answer the question based only on the following context:\n", "{context}\n", "\n", "Question: {question}\n", "Use natural language and be concise.\n", "Answer:\"\"\""], "metadata": {"id": "zuVyoD1iBLgt"}, "execution_count": 44, "outputs": []}, {"cell_type": "code", "source": ["prompt = ChatPromptTemplate.from_template(template)"], "metadata": {"id": "ehex9TRGBM4m"}, "execution_count": 46, "outputs": []}, {"cell_type": "code", "source": ["chain = (\n", "    RunnableParallel(\n", "        {\n", "            \"context\": _search_query | retriever,\n", "            \"question\": <PERSON><PERSON>blePassthrough(),\n", "        }\n", "    )\n", "    | prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")"], "metadata": {"id": "UI6LVwkpBOOA"}, "execution_count": 47, "outputs": []}, {"cell_type": "code", "source": ["chain.invoke({\"question\": \"Which house did <PERSON> I belong to?\"})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 54}, "id": "GZAq-jz3BOrn", "outputId": "d438df41-4a7e-437a-a022-902d8290e4cb"}, "execution_count": 48, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Search query: Which house did <PERSON> I belong to?\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'Elizabeth I belonged to the House of Tudor.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 48}]}, {"cell_type": "code", "source": ["chain.invoke(\n", "    {\n", "        \"question\": \"When was she born?\",\n", "        \"chat_history\": [(\"Which house did <PERSON> I belong to?\", \"House Of Tudor\")],\n", "    }\n", ")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 54}, "id": "b8bO9V_MIBZ5", "outputId": "d5d3cfa2-c4ec-4089-e715-0233e688bf85"}, "execution_count": 49, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Search query: When was <PERSON> I born?\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'<PERSON> was born on 7 September 1533.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 49}]}, {"cell_type": "code", "source": [], "metadata": {"id": "qyIlAGROIUKC"}, "execution_count": null, "outputs": []}]}