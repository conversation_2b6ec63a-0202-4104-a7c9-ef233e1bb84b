{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyNBvnzKXZ1f2uW1KiWpVbJ7", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/sunnysavita10/Generative-AI-Indepth-Basic-to-Advance/blob/main/basic_retrieval_and_contextual_compression_retrieval.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["https://github.com/langchain-ai/langchain/tree/master/libs/langchain/langchain/retrievers/document_compressors"], "metadata": {"id": "pTe8iIaNM4zk"}}, {"cell_type": "markdown", "source": ["https://blog.langchain.dev/improving-document-retrieval-with-contextual-compression/"], "metadata": {"id": "qUKL4xoQLk2l"}}, {"cell_type": "code", "source": ["!pip install langchain_community"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QOY6EbacxP4a", "outputId": "246a240a-add3-4a5b-930c-25904382bedd"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain_community\n", "  Downloading langchain_community-0.2.5-py3-none-any.whl (2.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.2/2.2 MB\u001b[0m \u001b[31m8.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (2.0.30)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (3.9.5)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain_community)\n", "  Downloading dataclasses_json-0.6.7-py3-none-any.whl (28 kB)\n", "Collecting langchain<0.3.0,>=0.2.5 (from langchain_community)\n", "  Downloading langchain-0.2.5-py3-none-any.whl (974 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m974.6/974.6 kB\u001b[0m \u001b[31m20.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-core<0.3.0,>=0.2.7 (from langchain_community)\n", "  Downloading langchain_core-0.2.9-py3-none-any.whl (321 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m321.8/321.8 kB\u001b[0m \u001b[31m20.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langsmith<0.2.0,>=0.1.0 (from langchain_community)\n", "  Downloading langsmith-0.1.81-py3-none-any.whl (127 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.1/127.1 kB\u001b[0m \u001b[31m17.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (1.25.2)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (8.4.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (4.0.3)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain_community)\n", "  Downloading marshmallow-3.21.3-py3-none-any.whl (49 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.2/49.2 kB\u001b[0m \u001b[31m6.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain_community)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Collecting langchain-text-splitters<0.3.0,>=0.2.0 (from langchain<0.3.0,>=0.2.5->langchain_community)\n", "  Downloading langchain_text_splitters-0.2.1-py3-none-any.whl (23 kB)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.5->langchain_community) (2.7.4)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain-core<0.3.0,>=0.2.7->langchain_community)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.7->langchain_community) (24.1)\n", "Collecting or<PERSON><PERSON><4.0.0,>=3.9.14 (from langsmith<0.2.0,>=0.1.0->langchain_community)\n", "  Downloading orjson-3.10.5-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (144 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m145.0/145.0 kB\u001b[0m \u001b[31m11.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain_community) (2024.6.2)\n", "Requirement already satisfied: typing-extensions>=4.6.0 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain_community) (4.12.2)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain_community) (3.0.3)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.7->langchain_community)\n", "  Downloading jsonpointer-3.0.0-py2.py3-none-any.whl (7.6 kB)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.5->langchain_community) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.18.4 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.5->langchain_community) (2.18.4)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain_community)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: orjson, mypy-extensions, marshmallow, jsonpointer, typing-inspect, jsonpatch, langsmith, dataclasses-json, langchain-core, langchain-text-splitters, langchain, langchain_community\n", "Successfully installed dataclasses-json-0.6.7 jsonpatch-1.33 jsonpointer-3.0.0 langchain-0.2.5 langchain-core-0.2.9 langchain-text-splitters-0.2.1 langchain_community-0.2.5 langsmith-0.1.81 marshmallow-3.21.3 mypy-extensions-1.0.0 orjson-3.10.5 typing-inspect-0.9.0\n"]}]}, {"cell_type": "code", "source": ["!pip install langchain_openai"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "atsXMM1OxU-A", "outputId": "974a2892-1770-4532-ee06-64434f6dc517"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain_openai\n", "  Downloading langchain_openai-0.1.8-py3-none-any.whl (38 kB)\n", "Requirement already satisfied: langchain-core<0.3,>=0.2.2 in /usr/local/lib/python3.10/dist-packages (from langchain_openai) (0.2.9)\n", "Collecting openai<2.0.0,>=1.26.0 (from langchain_openai)\n", "  Downloading openai-1.35.3-py3-none-any.whl (327 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m327.4/327.4 kB\u001b[0m \u001b[31m5.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting tiktoken<1,>=0.7 (from langchain_openai)\n", "  Downloading tiktoken-0.7.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.1 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m2.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (6.0.1)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (1.33)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.75 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (0.1.81)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (24.1)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (2.7.4)\n", "Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (8.4.1)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.26.0->langchain_openai) (3.7.1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai<2.0.0,>=1.26.0->langchain_openai) (1.7.0)\n", "Collecting httpx<1,>=0.23.0 (from openai<2.0.0,>=1.26.0->langchain_openai)\n", "  Downloading httpx-0.27.0-py3-none-any.whl (75 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m8.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.26.0->langchain_openai) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.26.0->langchain_openai) (4.66.4)\n", "Requirement already satisfied: typing-extensions<5,>=4.7 in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.26.0->langchain_openai) (4.12.2)\n", "Requirement already satisfied: regex>=2022.1.18 in /usr/local/lib/python3.10/dist-packages (from tiktoken<1,>=0.7->langchain_openai) (2024.5.15)\n", "Requirement already satisfied: requests>=2.26.0 in /usr/local/lib/python3.10/dist-packages (from tiktoken<1,>=0.7->langchain_openai) (2.31.0)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.26.0->langchain_openai) (3.7)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.26.0->langchain_openai) (1.2.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.26.0->langchain_openai) (2024.6.2)\n", "Collecting httpcore==1.* (from httpx<1,>=0.23.0->openai<2.0.0,>=1.26.0->langchain_openai)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m8.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->openai<2.0.0,>=1.26.0->langchain_openai)\n", "  Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m5.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3,>=0.2.2->langchain_openai) (3.0.0)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.2->langchain_openai) (3.10.5)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain-core<0.3,>=0.2.2->langchain_openai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.18.4 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain-core<0.3,>=0.2.2->langchain_openai) (2.18.4)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain_openai) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain_openai) (2.0.7)\n", "Installing collected packages: h11, tiktoken, httpcore, httpx, openai, langchain_openai\n", "Successfully installed h11-0.14.0 httpcore-1.0.5 httpx-0.27.0 langchain_openai-0.1.8 openai-1.35.3 tiktoken-0.7.0\n"]}]}, {"cell_type": "code", "source": ["#facebook ai similarity search\n", "!pip install faiss-cpu"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Xl1eTvuKxb3Z", "outputId": "1ae75a8a-1b82-46bf-985c-024b186819d9"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting faiss-cpu\n", "  Downloading faiss_cpu-1.8.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (27.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m27.0/27.0 MB\u001b[0m \u001b[31m41.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from faiss-cpu) (1.25.2)\n", "Installing collected packages: faiss-cpu\n", "Successfully installed faiss-cpu-1.8.0\n"]}]}, {"cell_type": "code", "source": ["from langchain_community.document_loaders import TextLoader\n", "from langchain_community.vectorstores import FAISS\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_text_splitters import CharacterTextSplitter"], "metadata": {"id": "CQS1GvoVxjKg"}, "execution_count": 15, "outputs": []}, {"cell_type": "code", "source": ["documents = TextLoader(\"/content/state_of_the_union.txt\").load()"], "metadata": {"id": "s1qf4CaMxpmY"}, "execution_count": 5, "outputs": []}, {"cell_type": "code", "source": ["text_splitter = CharacterTextSplitter(chunk_size=500, chunk_overlap=100)"], "metadata": {"id": "7x6yIAuDx8FQ"}, "execution_count": 6, "outputs": []}, {"cell_type": "code", "source": ["texts = text_splitter.split_documents(documents)"], "metadata": {"id": "ltWbdNzVyGzY"}, "execution_count": 7, "outputs": []}, {"cell_type": "code", "source": ["texts"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tTmFHn1basDU", "outputId": "56652ee9-323d-4a82-c415-179dbcca00c6"}, "execution_count": 16, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='<PERSON><PERSON> Speaker, <PERSON>am Vice President, our First Lady and Second Gentleman. Members of Congress and the Cabinet. Justices of the Supreme Court. My fellow Americans.  \\n\\nLast year COVID-19 kept us apart. This year we are finally together again. \\n\\nTonight, we meet as Democrats Republicans and Independents. But most importantly as Americans. \\n\\nWith a duty to one another to the American people to the Constitution. \\n\\nAnd with an unwavering resolve that freedom will always triumph over tyranny.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And with an unwavering resolve that freedom will always triumph over tyranny. \\n\\nSix days ago, Russia’s <PERSON> sought to shake the foundations of the free world thinking he could make it bend to his menacing ways. But he badly miscalculated. \\n\\nHe thought he could roll into Ukraine and the world would roll over. Instead he met a wall of strength he never imagined. \\n\\nHe met the Ukrainian people.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='He met the Ukrainian people. \\n\\nFrom President <PERSON><PERSON><PERSON><PERSON> to every Ukrainian, their fearlessness, their courage, their determination, inspires the world. \\n\\nGroups of citizens blocking tanks with their bodies. Everyone from students to retirees teachers turned soldiers defending their homeland. \\n\\nIn this struggle as President <PERSON><PERSON><PERSON><PERSON> said in his speech to the European Parliament “Light will win over darkness.” The Ukrainian Ambassador to the United States is here tonight.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Let each of us here tonight in this Chamber send an unmistakable signal to Ukraine and to the world. \\n\\nPlease rise if you are able and show that, Yes, we the United States of America stand with the Ukrainian people. \\n\\nThroughout our history we’ve learned this lesson when dictators do not pay a price for their aggression they cause more chaos.   \\n\\nThey keep moving.   \\n\\nAnd the costs and the threats to America and the world keep rising.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='They keep moving.   \\n\\nAnd the costs and the threats to America and the world keep rising.   \\n\\nThat’s why the NATO Alliance was created to secure peace and stability in Europe after World War 2. \\n\\nThe United States is a member along with 29 other nations. \\n\\nIt matters. American diplomacy matters. American resolve matters. \\n\\nPutin’s latest attack on Ukraine was premeditated and unprovoked. \\n\\nHe rejected repeated efforts at diplomacy.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='He rejected repeated efforts at diplomacy. \\n\\nHe thought the West and NATO wouldn’t respond. And he thought he could divide us at home. Putin was wrong. We were ready.  Here is what we did.   \\n\\nWe prepared extensively and carefully. \\n\\nWe spent months building a coalition of other freedom-loving nations from Europe and the Americas to Asia and Africa to confront <PERSON>.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='I spent countless hours unifying our European allies. We shared with the world in advance what we knew <PERSON> was planning and precisely how he would try to falsely justify his aggression.  \\n\\nWe countered Russia’s lies with truth.   \\n\\nAnd now that he has acted the free world is holding him accountable.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And now that he has acted the free world is holding him accountable. \\n\\nAlong with twenty-seven members of the European Union including France, Germany, Italy, as well as countries like the United Kingdom, Canada, Japan, Korea, Australia, New Zealand, and many others, even Switzerland. \\n\\nWe are inflicting pain on Russia and supporting the people of Ukraine. <PERSON> is now isolated from the world more than ever. \\n\\nTogether with our allies –we are right now enforcing powerful economic sanctions.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Together with our allies –we are right now enforcing powerful economic sanctions. \\n\\nWe are cutting off Russia’s largest banks from the international financial system.  \\n\\nPreventing Russia’s central bank from defending the Russian Ruble making <PERSON>’s $630 Billion “war fund” worthless.   \\n\\nWe are choking off Russia’s access to technology that will sap its economic strength and weaken its military for years to come.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Tonight I say to the Russian oligarchs and corrupt leaders who have bilked billions of dollars off this violent regime no more. \\n\\nThe U.S. Department of Justice is assembling a dedicated task force to go after the crimes of Russian oligarchs.  \\n\\nWe are joining with our European allies to find and seize your yachts your luxury apartments your private jets. We are coming for your ill-begotten gains.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And tonight I am announcing that we will join our allies in closing off American air space to all Russian flights – further isolating Russia – and adding an additional squeeze –on their economy. The Ruble has lost 30% of its value. \\n\\nThe Russian stock market has lost 40% of its value and trading remains suspended. Russia’s economy is reeling and <PERSON> alone is to blame.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Together with our allies we are providing support to the Ukrainians in their fight for freedom. Military assistance. Economic assistance. Humanitarian assistance. \\n\\nWe are giving more than $1 Billion in direct assistance to Ukraine. \\n\\nAnd we will continue to aid the Ukrainian people as they defend their country and to help ease their suffering.  \\n\\nLet me be clear, our forces are not engaged and will not engage in conflict with Russian forces in Ukraine.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Our forces are not going to Europe to fight in Ukraine, but to defend our NATO Allies – in the event that <PERSON> decides to keep moving west.  \\n\\nFor that purpose we’ve mobilized American ground forces, air squadrons, and ship deployments to protect NATO countries including Poland, Romania, Latvia, Lithuania, and Estonia. \\n\\nAs I have made crystal clear the United States and our Allies will defend every inch of territory of NATO countries with the full force of our collective power.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And we remain clear-eyed. The Ukrainians are fighting back with pure courage. But the next few days weeks, months, will be hard on them.  \\n\\n<PERSON><PERSON><PERSON> has unleashed violence and chaos.  But while he may make gains on the battlefield – he will pay a continuing high price over the long run. \\n\\nAnd a proud Ukrainian people, who have known 30 years  of independence, have repeatedly shown that they will not tolerate anyone who tries to take their country backwards.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='To all Americans, I will be honest with you, as I’ve always promised. A Russian dictator, invading a foreign country, has costs around the world. \\n\\nAnd I’m taking robust action to make sure the pain of our sanctions  is targeted at Russia’s economy. And I will use every tool at our disposal to protect American businesses and consumers. \\n\\nTonight, I can announce that the United States has worked with 30 other countries to release 60 Million barrels of oil from reserves around the world.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='America will lead that effort, releasing 30 Million barrels from our own Strategic Petroleum Reserve. And we stand ready to do more if necessary, unified with our allies.  \\n\\nThese steps will help blunt gas prices here at home. And I know the news about what’s happening can seem alarming. \\n\\nBut I want you to know that we are going to be okay. \\n\\nWhen the history of this era is written <PERSON>’s war on Ukraine will have left Russia weaker and the rest of the world stronger.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='While it shouldn’t have taken something so terrible for people around the world to see what’s at stake now everyone sees it clearly. \\n\\nWe see the unity among leaders of nations and a more unified Europe a more unified West. And we see unity among the people who are gathering in cities in large crowds around the world even in Russia to demonstrate their support for Ukraine.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='In the battle between democracy and autocracy, democracies are rising to the moment, and the world is clearly choosing the side of peace and security. \\n\\nThis is a real test. It’s going to take time. So let us continue to draw inspiration from the iron will of the Ukrainian people. \\n\\nTo our fellow Ukrainian Americans who forge a deep bond that connects our two nations we stand with you. \\n\\n<PERSON><PERSON><PERSON> may circle Kyiv with tanks, but he will never gain the hearts and souls of the Ukrainian people.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='He will never extinguish their love of freedom. He will never weaken the resolve of the free world. \\n\\nWe meet tonight in an America that has lived through two of the hardest years this nation has ever faced. \\n\\nThe pandemic has been punishing. \\n\\nAnd so many families are living paycheck to paycheck, struggling to keep up with the rising cost of food, gas, housing, and so much more. \\n\\nI understand.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='I understand. \\n\\nI remember when my Dad had to leave our home in Scranton, Pennsylvania to find work. I grew up in a family where if the price of food went up, you felt it. \\n\\nThat’s why one of the first things I did as President was fight to pass the American Rescue Plan.  \\n\\nBecause people were hurting. We needed to act, and we did. \\n\\nFew pieces of legislation have done more in a critical moment in our history to lift us out of crisis.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='It fueled our efforts to vaccinate the nation and combat COVID-19. It delivered immediate economic relief for tens of millions of Americans.  \\n\\nHelped put food on their table, keep a roof over their heads, and cut the cost of health insurance. \\n\\nAnd as my Dad used to say, it gave people a little breathing room. \\n\\nAnd unlike the $2 Trillion tax cut passed in the previous administration that benefitted the top 1% of Americans, the American Rescue Plan helped working people—and left no one behind.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And it worked. It created jobs. Lots of jobs. \\n\\nIn fact—our economy created over 6.5 Million new jobs just last year, more jobs created in one year  \\nthan ever before in the history of America. \\n\\nOur economy grew at a rate of 5.7% last year, the strongest growth in nearly 40 years, the first step in bringing fundamental change to an economy that hasn’t worked for the working people of this nation for too long.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='For the past 40 years we were told that if we gave tax breaks to those at the very top, the benefits would trickle down to everyone else. \\n\\nBut that trickle-down theory led to weaker economic growth, lower wages, bigger deficits, and the widest gap between those at the top and everyone else in nearly a century. \\n\\nVice President <PERSON> and I ran for office with a new economic vision for America.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Vice President <PERSON> and I ran for office with a new economic vision for America. \\n\\nInvest in America. Educate Americans. Grow the workforce. Build the economy from the bottom up  \\nand the middle out, not from the top down.  \\n\\nBecause we know that when the middle class grows, the poor have a ladder up and the wealthy do very well. \\n\\nAmerica used to have the best roads, bridges, and airports on Earth. \\n\\nNow our infrastructure is ranked 13th in the world.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Now our infrastructure is ranked 13th in the world. \\n\\nWe won’t be able to compete for the jobs of the 21st Century if we don’t fix that. \\n\\nThat’s why it was so important to pass the Bipartisan Infrastructure Law—the most sweeping investment to rebuild America in history. \\n\\nThis was a bipartisan effort, and I want to thank the members of both parties who worked to make it happen. \\n\\nWe’re done talking about infrastructure weeks. \\n\\nWe’re going to have an infrastructure decade.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We’re done talking about infrastructure weeks. \\n\\nWe’re going to have an infrastructure decade. \\n\\nIt is going to transform America and put us on a path to win the economic competition of the 21st Century that we face with the rest of the world—particularly with China.  \\n\\nAs I’ve told <PERSON> Jinping, it is never a good bet to bet against the American people. \\n\\nWe’ll create good jobs for millions of Americans, modernizing roads, airports, ports, and waterways all across America.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And we’ll do it all to withstand the devastating effects of the climate crisis and promote environmental justice. \\n\\nWe’ll build a national network of 500,000 electric vehicle charging stations, begin to replace poisonous lead pipes—so every child—and every American—has clean water to drink at home and at school, provide affordable high-speed internet for every American—urban, suburban, rural, and tribal communities. \\n\\n4,000 projects have already been announced.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='4,000 projects have already been announced. \\n\\nAnd tonight, I’m announcing that this year we will start fixing over 65,000 miles of highway and 1,500 bridges in disrepair. \\n\\nWhen we use taxpayer dollars to rebuild America – we are going to Buy American: buy American products to support American jobs. \\n\\nThe federal government spends about $600 Billion a year to keep the country safe and secure.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='The federal government spends about $600 Billion a year to keep the country safe and secure. \\n\\nThere’s been a law on the books for almost a century \\nto make sure taxpayers’ dollars support American jobs and businesses. \\n\\nEvery Administration says they’ll do it, but we are actually doing it. \\n\\nWe will buy American to make sure everything from the deck of an aircraft carrier to the steel on highway guardrails are made in America.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='But to compete for the best jobs of the future, we also need to level the playing field with China and other competitors. \\n\\nThat’s why it is so important to pass the Bipartisan Innovation Act sitting in Congress that will make record investments in emerging technologies and American manufacturing. \\n\\nLet me give you one example of why it’s so important to pass it. \\n\\nIf you travel 20 miles east of Columbus, Ohio, you’ll find 1,000 empty acres of land.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='If you travel 20 miles east of Columbus, Ohio, you’ll find 1,000 empty acres of land. \\n\\nIt won’t look like much, but if you stop and look closely, you’ll see a “Field of dreams,” the ground on which America’s future will be built. \\n\\nThis is where Intel, the American company that helped build Silicon Valley, is going to build its $20 billion semiconductor “mega site”. \\n\\nUp to eight state-of-the-art factories in one place. 10,000 new good-paying jobs.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Up to eight state-of-the-art factories in one place. 10,000 new good-paying jobs. \\n\\nSome of the most sophisticated manufacturing in the world to make computer chips the size of a fingertip that power the world and our everyday lives. \\n\\nSmartphones. The Internet. Technology we have yet to invent. \\n\\nBut that’s just the beginning. \\n\\nIntel’s CEO, <PERSON>, who is here tonight, told me they are ready to increase their investment from  \\n$20 billion to $100 billion.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='That would be one of the biggest investments in manufacturing in American history. \\n\\nAnd all they’re waiting for is for you to pass this bill. \\n\\nSo let’s not wait any longer. Send it to my desk. I’ll sign it.  \\n\\nAnd we will really take off. \\n\\nAnd Intel is not alone. \\n\\nThere’s something happening in America. \\n\\nJust look around and you’ll see an amazing story. \\n\\nThe rebirth of the pride that comes from stamping products “Made In America.” The revitalization of American manufacturing.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Companies are choosing to build new factories here, when just a few years ago, they would have built them overseas. \\n\\nThat’s what is happening. Ford is investing $11 billion to build electric vehicles, creating 11,000 jobs across the country. \\n\\nGM is making the largest investment in its history—$7 billion to build electric vehicles, creating 4,000 jobs in Michigan. \\n\\nAll told, we created 369,000 new manufacturing jobs in America just last year.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='All told, we created 369,000 new manufacturing jobs in America just last year. \\n\\nPowered by people I’ve met like <PERSON><PERSON><PERSON>, from generations of union steelworkers from Pittsburgh, who’s here with us tonight. \\n\\nAs Ohio Senator <PERSON><PERSON><PERSON> says, “It’s time to bury the label “Rust Belt.” \\n\\nIt’s time. \\n\\nBut with all the bright spots in our economy, record job growth and higher wages, too many families are struggling to keep up with the bills.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Inflation is robbing them of the gains they might otherwise feel. \\n\\nI get it. That’s why my top priority is getting prices under control. \\n\\nLook, our economy roared back faster than most predicted, but the pandemic meant that businesses had a hard time hiring enough workers to keep up production in their factories. \\n\\nThe pandemic also disrupted global supply chains. \\n\\nWhen factories close, it takes longer to make goods and get them from the warehouse to the store, and prices go up.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Look at cars. \\n\\nLast year, there weren’t enough semiconductors to make all the cars that people wanted to buy. \\n\\nAnd guess what, prices of automobiles went up. \\n\\nSo—we have a choice. \\n\\nOne way to fight inflation is to drive down wages and make Americans poorer.  \\n\\nI have a better plan to fight inflation. \\n\\nLower your costs, not your wages. \\n\\nMake more cars and semiconductors in America. \\n\\nMore infrastructure and innovation in America. \\n\\nMore goods moving faster and cheaper in America.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='More infrastructure and innovation in America. \\n\\nMore goods moving faster and cheaper in America. \\n\\nMore jobs where you can earn a good living in America. \\n\\nAnd instead of relying on foreign supply chains, let’s make it in America. \\n\\nEconomists call it “increasing the productive capacity of our economy.” \\n\\nI call it building a better America. \\n\\nMy plan to fight inflation will lower your costs and lower the deficit.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='My plan to fight inflation will lower your costs and lower the deficit. \\n\\n17 Nobel laureates in economics say my plan will ease long-term inflationary pressures. Top business leaders and most Americans support my plan. And here’s the plan: \\n\\nFirst – cut the cost of prescription drugs. Just look at insulin. One in ten Americans has diabetes. In Virginia, I met a 13-year-old boy named <PERSON>.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='He and his Dad both have Type 1 diabetes, which means they need insulin every day. Insulin costs about $10 a vial to make.  \\n\\nBut drug companies charge families like <PERSON> and his Dad up to 30 times more. I spoke with <PERSON>’s mom. \\n\\nImagine what it’s like to look at your child who needs insulin and have no idea how you’re going to pay for it.  \\n\\nWhat it does to your dignity, your ability to look your child in the eye, to be the parent you expect to be.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='<PERSON> is here with us tonight. Yesterday was his birthday. Happy birthday, buddy.  \\n\\nFor <PERSON>, and for the 200,000 other young people with Type 1 diabetes, let’s cap the cost of insulin at $35 a month so everyone can afford it.  \\n\\nDrug companies will still do very well. And while we’re at it let Medicare negotiate lower prices for prescription drugs, like the VA already does.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Look, the American Rescue Plan is helping millions of families on Affordable Care Act plans save $2,400 a year on their health care premiums. Let’s close the coverage gap and make those savings permanent. \\n\\nSecond – cut energy costs for families an average of $500 a year by combatting climate change.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Second – cut energy costs for families an average of $500 a year by combatting climate change.  \\n\\nLet’s provide investments and tax credits to weatherize your homes and businesses to be energy efficient and you get a tax credit; double America’s clean energy production in solar, wind, and so much more;  lower the price of electric vehicles, saving you another $80 a month because you’ll never have to pay at the gas pump again.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Third – cut the cost of child care. Many families pay up to $14,000 a year for child care per child.  \\n\\nMiddle-class and working families shouldn’t have to pay more than 7% of their income for care of young children.  \\n\\nMy plan will cut the cost in half for most families and help parents, including millions of women, who left the workforce during the pandemic because they couldn’t afford child care, to be able to get back to work.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='My plan doesn’t stop there. It also includes home and long-term care. More affordable housing. And Pre-K for every 3- and 4-year-old.  \\n\\nAll of these will lower costs. \\n\\nAnd under my plan, nobody earning less than $400,000 a year will pay an additional penny in new taxes. Nobody.  \\n\\nThe one thing all Americans agree on is that the tax system is not fair. We have to fix it.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='The one thing all Americans agree on is that the tax system is not fair. We have to fix it.  \\n\\nI’m not looking to punish anyone. But let’s make sure corporations and the wealthiest Americans start paying their fair share. \\n\\nJust last year, 55 Fortune 500 corporations earned $40 billion in profits and paid zero dollars in federal income tax.  \\n\\nThat’s simply not fair. That’s why I’ve proposed a 15% minimum tax rate for corporations.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='That’s simply not fair. That’s why I’ve proposed a 15% minimum tax rate for corporations. \\n\\nWe got more than 130 countries to agree on a global minimum tax rate so companies can’t get out of paying their taxes at home by shipping jobs and factories overseas. \\n\\nThat’s why I’ve proposed closing loopholes so the very wealthy don’t pay a lower tax rate than a teacher or a firefighter.  \\n\\nSo that’s my plan. It will grow the economy and lower costs for families.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='So that’s my plan. It will grow the economy and lower costs for families. \\n\\nSo what are we waiting for? Let’s get this done. And while you’re at it, confirm my nominees to the Federal Reserve, which plays a critical role in fighting inflation.  \\n\\nMy plan will not only lower costs to give families a fair shot, it will lower the deficit.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='My plan will not only lower costs to give families a fair shot, it will lower the deficit. \\n\\nThe previous Administration not only ballooned the deficit with tax cuts for the very wealthy and corporations, it undermined the watchdogs whose job was to keep pandemic relief funds from being wasted. \\n\\nBut in my administration, the watchdogs have been welcomed back. \\n\\nWe’re going after the criminals who stole billions in relief money meant for small businesses and millions of Americans.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And tonight, I’m announcing that the Justice Department will name a chief prosecutor for pandemic fraud. \\n\\nBy the end of this year, the deficit will be down to less than half what it was before I took office.  \\n\\nThe only president ever to cut the deficit by more than one trillion dollars in a single year. \\n\\nLowering your costs also means demanding more competition. \\n\\nI’m a capitalist, but capitalism without competition isn’t capitalism. \\n\\nIt’s exploitation—and it drives up prices.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='It’s exploitation—and it drives up prices. \\n\\nWhen corporations don’t have to compete, their profits go up, your prices go up, and small businesses and family farmers and ranchers go under. \\n\\nWe see it happening with ocean carriers moving goods in and out of America. \\n\\nDuring the pandemic, these foreign-owned companies raised prices by as much as 1,000% and made record profits. \\n\\nTonight, I’m announcing a crackdown on these companies overcharging American businesses and consumers.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And as Wall Street firms take over more nursing homes, quality in those homes has gone down and costs have gone up.  \\n\\nThat ends on my watch. \\n\\nMedicare is going to set higher standards for nursing homes and make sure your loved ones get the care they deserve and expect. \\n\\nWe’ll also cut costs and keep the economy going strong by giving workers a fair shot, provide more training and apprenticeships, hire them based on their skills not degrees.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Let’s pass the Paycheck Fairness Act and paid leave.  \\n\\nRaise the minimum wage to $15 an hour and extend the Child Tax Credit, so no one has to raise a family in poverty. \\n\\nLet’s increase Pell Grants and increase our historic support of HBCUs, and invest in what <PERSON>—our First Lady who teaches full-time—calls America’s best-kept secret: community colleges. \\n\\nAnd let’s pass the PRO Act when a majority of workers want to form a union—they shouldn’t be stopped.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='When we invest in our workers, when we build the economy from the bottom up and the middle out together, we can do something we haven’t done in a long time: build a better America. \\n\\nFor more than two years, COVID-19 has impacted every decision in our lives and the life of the nation. \\n\\nAnd I know you’re tired, frustrated, and exhausted. \\n\\nBut I also know this.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And I know you’re tired, frustrated, and exhausted. \\n\\nBut I also know this. \\n\\nBecause of the progress we’ve made, because of your resilience and the tools we have, tonight I can say  \\nwe are moving forward safely, back to more normal routines.  \\n\\nWe’ve reached a new moment in the fight against COVID-19, with severe cases down to a level not seen since last July.  \\n\\nJust a few days ago, the Centers for Disease Control and Prevention—the CDC—issued new mask guidelines.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Under these new guidelines, most Americans in most of the country can now be mask free.   \\n\\nAnd based on the projections, more of the country will reach that point across the next couple of weeks. \\n\\nThanks to the progress we have made this past year, COVID-19 need no longer control our lives.  \\n\\nI know some are talking about “living with COVID-19”. Tonight – I say that we will never just accept living with COVID-19.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We will continue to combat the virus as we do other diseases. And because this is a virus that mutates and spreads, we will stay on guard. \\n\\nHere are four common sense steps as we move forward safely.  \\n\\nFirst, stay protected with vaccines and treatments. We know how incredibly effective vaccines are. If you’re vaccinated and boosted you have the highest degree of protection.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We will never give up on vaccinating more Americans. Now, I know parents with kids under 5 are eager to see a vaccine authorized for their children. \\n\\nThe scientists are working hard to get that done and we’ll be ready with plenty of vaccines when they do. \\n\\nWe’re also ready with anti-viral treatments. If you get COVID-19, the Pfizer pill reduces your chances of ending up in the hospital by 90%.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We’ve ordered more of these pills than anyone in the world. And <PERSON><PERSON><PERSON> is working overtime to get us 1 Million pills this month and more than double that next month.  \\n\\nAnd we’re launching the “Test to Treat” initiative so people can get tested at a pharmacy, and if they’re positive, receive antiviral pills on the spot at no cost.  \\n\\nIf you’re immunocompromised or have some other vulnerability, we have treatments and free high-quality masks.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We’re leaving no one behind or ignoring anyone’s needs as we move forward. \\n\\nAnd on testing, we have made hundreds of millions of tests available for you to order for free.   \\n\\nEven if you already ordered free tests tonight, I am announcing that you can order more from covidtests.gov starting next week. \\n\\nSecond – we must prepare for new variants. Over the past year, we’ve gotten much better at detecting new variants.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='If necessary, we’ll be able to deploy new vaccines within 100 days instead of many more months or years.  \\n\\nAnd, if Congress provides the funds we need, we’ll have new stockpiles of tests, masks, and pills ready if needed. \\n\\nI cannot promise a new variant won’t come. But I can promise you we’ll do everything within our power to be ready if it does.  \\n\\nThird – we can end the shutdown of schools and businesses. We have the tools we need.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Third – we can end the shutdown of schools and businesses. We have the tools we need. \\n\\nIt’s time for Americans to get back to work and fill our great downtowns again.  People working from home can feel safe to begin to return to the office.   \\n\\nWe’re doing that here in the federal government. The vast majority of federal workers will once again work in person. \\n\\nOur schools are open. Let’s keep it that way. Our kids need to be in school.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Our schools are open. Let’s keep it that way. Our kids need to be in school. \\n\\nAnd with 75% of adult Americans fully vaccinated and hospitalizations down by 77%, most Americans can remove their masks, return to work, stay in the classroom, and move forward safely. \\n\\nWe achieved this because we provided free vaccines, treatments, tests, and masks. \\n\\nOf course, continuing this costs money. \\n\\nI will soon send Congress a request.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Of course, continuing this costs money. \\n\\nI will soon send Congress a request. \\n\\nThe vast majority of Americans have used these tools and may want to again, so I expect Congress to pass it quickly.   \\n\\nFourth, we will continue vaccinating the world.     \\n\\nWe’ve sent 475 Million vaccine doses to 112 countries, more than any other nation. \\n\\nAnd we won’t stop. \\n\\nWe have lost so much to COVID-19. Time with one another. And worst of all, so much loss of life.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We have lost so much to COVID-19. Time with one another. And worst of all, so much loss of life. \\n\\nLet’s use this moment to reset. Let’s stop looking at COVID-19 as a partisan dividing line and see it for what it is: A God-awful disease.  \\n\\nLet’s stop seeing each other as enemies, and start seeing each other for who we really are: Fellow Americans.  \\n\\nWe can’t change how divided we’ve been. But we can change how we move forward—on COVID-19 and other issues we must face together.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='I recently visited the New York City Police Department days after the funerals of Officer <PERSON><PERSON><PERSON> and his partner, Officer <PERSON>. \\n\\nThey were responding to a 9-1-1 call when a man shot and killed them with a stolen gun. \\n\\nOfficer <PERSON> was 27 years old. \\n\\nOfficer <PERSON> was 22. \\n\\nBoth Dominican Americans who’d grown up on the same streets they later chose to patrol as police officers.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='I spoke with their families and told them that we are forever in debt for their sacrifice, and we will carry on their mission to restore the trust and safety every community deserves. \\n\\nI’ve worked on these issues a long time. \\n\\nI know what works: Investing in crime prevention and community police officers who’ll walk the beat, who’ll know the neighborhood, and who can restore trust and safety. \\n\\nSo let’s not abandon our streets. Or choose between safety and equal justice.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='So let’s not abandon our streets. Or choose between safety and equal justice. \\n\\nLet’s come together to protect our communities, restore trust, and hold law enforcement accountable. \\n\\nThat’s why the Justice Department required body cameras, banned chokeholds, and restricted no-knock warrants for its officers.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='That’s why the American Rescue Plan provided $350 Billion that cities, states, and counties can use to hire more police and invest in proven strategies like community violence interruption—trusted messengers breaking the cycle of violence and trauma and giving young people hope.  \\n\\nWe should all agree: The answer is not to Defund the police. The answer is to FUND the police with the resources and training they need to protect our communities.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='I ask Democrats and Republicans alike: Pass my budget and keep our neighborhoods safe.  \\n\\nAnd I will keep doing everything in my power to crack down on gun trafficking and ghost guns you can buy online and make at home—they have no serial numbers and can’t be traced. \\n\\nAnd I ask Congress to pass proven measures to reduce gun violence. Pass universal background checks. Why should anyone on a terrorist list be able to purchase a weapon? \\n\\nBan assault weapons and high-capacity magazines.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Ban assault weapons and high-capacity magazines. \\n\\nRepeal the liability shield that makes gun manufacturers the only industry in America that can’t be sued. \\n\\nThese laws don’t infringe on the Second Amendment. They save lives. \\n\\nThe most fundamental right in America is the right to vote – and to have it counted. And it’s under assault. \\n\\nIn state after state, new laws have been passed, not only to suppress the vote, but to subvert entire elections. \\n\\nWe cannot let this happen.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We cannot let this happen. \\n\\nTonight. I call on the Senate to: Pass the Freedom to Vote Act. Pass the John Lewis Voting Rights Act. And while you’re at it, pass the Disclose Act so Americans can know who is funding our elections. \\n\\nTonight, I’d like to honor someone who has dedicated his life to serve this country: Justice <PERSON>—an Army veteran, Constitutional scholar, and retiring Justice of the United States Supreme Court. Justice <PERSON>, thank you for your service.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='One of the most serious constitutional responsibilities a President has is nominating someone to serve on the United States Supreme Court. \\n\\nAnd I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>. One of our nation’s top legal minds, who will continue <PERSON>’s legacy of excellence.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='A former top litigator in private practice. A former federal public defender. And from a family of public school educators and police officers. A consensus builder. Since she’s been nominated, she’s received a broad range of support—from the Fraternal Order of Police to former judges appointed by Democrats and Republicans. \\n\\nAnd if we are to advance liberty and justice, we need to secure the Border and fix the immigration system.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We can do both. At our border, we’ve installed new technology like cutting-edge scanners to better detect drug smuggling.  \\n\\nWe’ve set up joint patrols with Mexico and Guatemala to catch more human traffickers.  \\n\\nWe’re putting in place dedicated immigration judges so families fleeing persecution and violence can have their cases heard faster. \\n\\nWe’re securing commitments and supporting partners in South and Central America to host more refugees and secure their own borders.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We can do all this while keeping lit the torch of liberty that has led generations of immigrants to this land—my forefathers and so many of yours. \\n\\nProvide a pathway to citizenship for Dreamers, those on temporary status, farm workers, and essential workers. \\n\\nRevise our laws so businesses have the workers they need and families don’t wait decades to reunite. \\n\\nIt’s not only the right thing to do—it’s the economically smart thing to do.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='It’s not only the right thing to do—it’s the economically smart thing to do. \\n\\nThat’s why immigration reform is supported by everyone from labor unions to religious leaders to the U.S. Chamber of Commerce. \\n\\nLet’s get it done once and for all. \\n\\nAdvancing liberty and justice also requires protecting the rights of women. \\n\\nThe constitutional right affirmed in <PERSON> v<PERSON>—standing precedent for half a century—is under attack as never before.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='If we want to go forward—not backward—we must protect access to health care. Preserve a woman’s right to choose. And let’s continue to advance maternal health care in America. \\n\\nAnd for our LGBTQ+ Americans, let’s finally get the bipartisan Equality Act to my desk. The onslaught of state laws targeting transgender Americans and their families is wrong.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='As I said last year, especially to our younger transgender Americans, I will always have your back as your President, so you can be yourself and reach your God-given potential. \\n\\nWhile it often appears that we never agree, that isn’t true. I signed 80 bipartisan bills into law last year. From preventing government shutdowns to protecting Asian-Americans from still-too-common hate crimes to reforming military justice.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='And soon, we’ll strengthen the Violence Against Women Act that I first wrote three decades ago. It is important for us to show the nation that we can come together and do big things. \\n\\nSo tonight I’m offering a Unity Agenda for the Nation. Four big things we can do together.  \\n\\nFirst, beat the opioid epidemic. \\n\\nThere is so much we can do. Increase funding for prevention, treatment, harm reduction, and recovery.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Get rid of outdated rules that stop doctors from prescribing treatments. And stop the flow of illicit drugs by working with state and local law enforcement to go after traffickers. \\n\\nIf you’re suffering from addiction, know you are not alone. I believe in recovery, and I celebrate the 23 million Americans in recovery. \\n\\nSecond, let’s take on mental health. Especially among our children, whose lives and education have been turned upside down.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='The American Rescue Plan gave schools money to hire teachers and help students make up for lost learning.  \\n\\nI urge every parent to make sure your school does just that. And we can all play a part—sign up to be a tutor or a mentor. \\n\\nChildren were also struggling before the pandemic. Bullying, violence, trauma, and the harms of social media.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='As <PERSON>, who is here with us tonight, has shown, we must hold social media platforms accountable for the national experiment they’re conducting on our children for profit. \\n\\nIt’s time to strengthen privacy protections, ban targeted advertising to children, demand tech companies stop collecting personal data on our children. \\n\\nAnd let’s get all Americans the mental health services they need. More people they can turn to for help, and full parity between physical and mental health care.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Third, support our veterans. \\n\\nVeterans are the best of us. \\n\\nI’ve always believed that we have a sacred obligation to equip all those we send to war and care for them and their families when they come home. \\n\\nMy administration is providing assistance with job training and housing, and now helping lower-income veterans get VA care debt-free.  \\n\\nOur troops in Iraq and Afghanistan faced many dangers.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Our troops in Iraq and Afghanistan faced many dangers. \\n\\nOne was stationed at bases and breathing in toxic smoke from “burn pits” that incinerated wastes of war—medical and hazard material, jet fuel, and more. \\n\\nWhen they came home, many of the world’s fittest and best trained warriors were never the same. \\n\\nHeadaches. Numbness. Dizziness. \\n\\nA cancer that would put them in a flag-draped coffin. \\n\\nI know. \\n\\nOne of those soldiers was my son Major <PERSON>.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='I know. \\n\\nOne of those soldiers was my son Major <PERSON>. \\n\\nWe don’t know for sure if a burn pit was the cause of his brain cancer, or the diseases of so many of our troops. \\n\\nBut I’m committed to finding out everything we can. \\n\\nCommitted to military families like <PERSON> from Ohio. \\n\\nThe widow of Sergeant First Class <PERSON>.  \\n\\nHe was born a soldier. Army National Guard. Combat medic in Kosovo and Iraq.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='He was born a soldier. Army National Guard. Combat medic in Kosovo and Iraq. \\n\\nStationed near Baghdad, just yards from burn pits the size of football fields. \\n\\n<PERSON><PERSON><PERSON>’s widow <PERSON> is here with us tonight. They loved going to Ohio State football games. He loved building Legos with their daughter. \\n\\nBut cancer from prolonged exposure to burn pits ravaged <PERSON>’s lungs and body. \\n\\nD<PERSON><PERSON> says <PERSON> was a fighter to the very end. \\n\\n<PERSON>e didn’t know how to stop fighting, and neither did she.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='He didn’t know how to stop fighting, and neither did she. \\n\\nThrough her pain she found purpose to demand we do better. \\n\\nTonight, <PERSON>—we are. \\n\\nThe VA is pioneering new ways of linking toxic exposures to diseases, already helping more veterans get benefits. \\n\\nAnd tonight, I’m announcing we’re expanding eligibility to veterans suffering from nine respiratory cancers.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='I’m also calling on Congress: pass a law to make sure veterans devastated by toxic exposures in Iraq and Afghanistan finally get the benefits and comprehensive health care they deserve. \\n\\nAnd fourth, let’s end cancer as we know it. \\n\\nThis is personal to me and <PERSON>, to <PERSON><PERSON>, and to so many of you. \\n\\nCancer is the #2 cause of death in America–second only to heart disease.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Cancer is the #2 cause of death in America–second only to heart disease. \\n\\nLast month, I announced our plan to supercharge  \\nthe Cancer Moonshot that President <PERSON> asked me to lead six years ago. \\n\\nOur goal is to cut the cancer death rate by at least 50% over the next 25 years, turn more cancers from death sentences into treatable diseases.  \\n\\nMore support for patients and families. \\n\\nTo get there, I call on Congress to fund ARPA-H, the Advanced Research Projects Agency for Health.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='To get there, I call on Congress to fund ARPA-H, the Advanced Research Projects Agency for Health. \\n\\nIt’s based on DARPA—the Defense Department project that led to the Internet, GPS, and so much more.  \\n\\nARPA-H will have a singular purpose—to drive breakthroughs in cancer, Alzheimer’s, diabetes, and more. \\n\\nA unity agenda for the nation. \\n\\nWe can do this. \\n\\nMy fellow Americans—tonight , we have gathered in a sacred space—the citadel of our democracy.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='My fellow Americans—tonight , we have gathered in a sacred space—the citadel of our democracy. \\n\\nIn this Capitol, generation after generation, Americans have debated great questions amid great strife, and have done great things. \\n\\nWe have fought for freedom, expanded liberty, defeated totalitarianism and terror. \\n\\nAnd built the strongest, freest, and most prosperous nation the world has ever known. \\n\\nNow is the hour. \\n\\nOur moment of responsibility.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Now is the hour. \\n\\nOur moment of responsibility. \\n\\nOur test of resolve and conscience, of history itself. \\n\\nIt is in this moment that our character is formed. Our purpose is found. Our future is forged. \\n\\nWell I know this nation.  \\n\\nWe will meet the test. \\n\\nTo protect freedom and liberty, to expand fairness and opportunity. \\n\\nWe will save democracy. \\n\\nAs hard as these times have been, I am more optimistic about America today than I have been my whole life.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Because I see the future that is within our grasp. \\n\\nBecause I know there is simply nothing beyond our capacity. \\n\\nWe are the only nation on Earth that has always turned every crisis we have faced into an opportunity. \\n\\nThe only nation that can be defined by a single word: possibilities. \\n\\nSo on this night, in our 245th year as a nation, I have come to report on the State of the Union. \\n\\nAnd my report is this: the State of the Union is strong—because you, the American people, are strong.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='We are stronger today than we were a year ago. \\n\\nAnd we will be stronger a year from now than we are today. \\n\\nNow is our moment to meet and overcome the challenges of our time. \\n\\nAnd we will, as one people. \\n\\nOne America. \\n\\nThe United States of America. \\n\\nMay God bless you all. May God protect our troops.', metadata={'source': '/content/state_of_the_union.txt'})]"]}, "metadata": {}, "execution_count": 16}]}, {"cell_type": "code", "source": ["from google.colab import userdata\n", "OPENAI_API_KEY=userdata.get('OPENAI_API_KEY')"], "metadata": {"id": "pKjzmrYIyIZg"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": ["import os\n", "os.environ[\"OPENAI_API_KEY\"] = OPENAI_API_KEY"], "metadata": {"id": "TVpbGd5PyKXQ"}, "execution_count": 18, "outputs": []}, {"cell_type": "code", "source": ["retriever = FAISS.from_documents(texts, OpenAIEmbeddings()).as_retriever()"], "metadata": {"id": "pPvs2OcUyL0R"}, "execution_count": 10, "outputs": []}, {"cell_type": "code", "source": ["docs = retriever.invoke(\"What did the president say about <PERSON><PERSON><PERSON>\")"], "metadata": {"id": "KlUX12WNyM-Z"}, "execution_count": 11, "outputs": []}, {"cell_type": "code", "source": ["# Helper function for printing docs\n", "\n", "def pretty_print_docs(docs):\n", "    print(\n", "        f\"\\n{'-' * 100}\\n\".join(\n", "            [f\"Document {i+1}:\\n\\n\" + d.page_content for i, d in enumerate(docs)]\n", "        )\n", "    )"], "metadata": {"id": "MYvh_OXfZf6b"}, "execution_count": 13, "outputs": []}, {"cell_type": "code", "source": ["pretty_print_docs(docs)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cY1HATqwCx7B", "outputId": "1fe9468a-1865-445c-f942-a9c65758e0c1"}, "execution_count": 14, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Document 1:\n", "\n", "One of the most serious constitutional responsibilities a President has is nominating someone to serve on the United States Supreme Court. \n", "\n", "And I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>. One of our nation’s top legal minds, who will continue <PERSON>’s legacy of excellence.\n", "----------------------------------------------------------------------------------------------------\n", "Document 2:\n", "\n", "As I said last year, especially to our younger transgender Americans, I will always have your back as your President, so you can be yourself and reach your God-given potential. \n", "\n", "While it often appears that we never agree, that isn’t true. I signed 80 bipartisan bills into law last year. From preventing government shutdowns to protecting Asian-Americans from still-too-common hate crimes to reforming military justice.\n", "----------------------------------------------------------------------------------------------------\n", "Document 3:\n", "\n", "A former top litigator in private practice. A former federal public defender. And from a family of public school educators and police officers. A consensus builder. Since she’s been nominated, she’s received a broad range of support—from the Fraternal Order of Police to former judges appointed by Democrats and Republicans. \n", "\n", "And if we are to advance liberty and justice, we need to secure the Border and fix the immigration system.\n", "----------------------------------------------------------------------------------------------------\n", "Document 4:\n", "\n", "He met the Ukrainian people. \n", "\n", "From President <PERSON><PERSON><PERSON><PERSON> to every Ukrainian, their fearlessness, their courage, their determination, inspires the world. \n", "\n", "Groups of citizens blocking tanks with their bodies. Everyone from students to retirees teachers turned soldiers defending their homeland. \n", "\n", "In this struggle as President <PERSON><PERSON><PERSON><PERSON> said in his speech to the European Parliament “Light will win over darkness.” The Ukrainian Ambassador to the United States is here tonight.\n"]}]}, {"cell_type": "code", "source": ["docs2 = retriever.invoke(\"What were the top three priorities outlined in the most recent State of the Union address?\")"], "metadata": {"id": "GXqglThB8riU"}, "execution_count": 19, "outputs": []}, {"cell_type": "code", "source": ["pretty_print_docs(docs2)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "S74TsmSNcOnl", "outputId": "4426b97c-6b27-44fa-a254-8af0926e14c6"}, "execution_count": 20, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Document 1:\n", "\n", "Because I see the future that is within our grasp. \n", "\n", "Because I know there is simply nothing beyond our capacity. \n", "\n", "We are the only nation on Earth that has always turned every crisis we have faced into an opportunity. \n", "\n", "The only nation that can be defined by a single word: possibilities. \n", "\n", "So on this night, in our 245th year as a nation, I have come to report on the State of the Union. \n", "\n", "And my report is this: the State of the Union is strong—because you, the American people, are strong.\n", "----------------------------------------------------------------------------------------------------\n", "Document 2:\n", "\n", "Vice President <PERSON> and <PERSON> ran for office with a new economic vision for America. \n", "\n", "Invest in America. Educate Americans. Grow the workforce. Build the economy from the bottom up  \n", "and the middle out, not from the top down.  \n", "\n", "Because we know that when the middle class grows, the poor have a ladder up and the wealthy do very well. \n", "\n", "America used to have the best roads, bridges, and airports on Earth. \n", "\n", "Now our infrastructure is ranked 13th in the world.\n", "----------------------------------------------------------------------------------------------------\n", "Document 3:\n", "\n", "And soon, we’ll strengthen the Violence Against Women Act that I first wrote three decades ago. It is important for us to show the nation that we can come together and do big things. \n", "\n", "So tonight I’m offering a Unity Agenda for the Nation. Four big things we can do together.  \n", "\n", "First, beat the opioid epidemic. \n", "\n", "There is so much we can do. Increase funding for prevention, treatment, harm reduction, and recovery.\n", "----------------------------------------------------------------------------------------------------\n", "Document 4:\n", "\n", "More infrastructure and innovation in America. \n", "\n", "More goods moving faster and cheaper in America. \n", "\n", "More jobs where you can earn a good living in America. \n", "\n", "And instead of relying on foreign supply chains, let’s make it in America. \n", "\n", "Economists call it “increasing the productive capacity of our economy.” \n", "\n", "I call it building a better America. \n", "\n", "My plan to fight inflation will lower your costs and lower the deficit.\n"]}]}, {"cell_type": "code", "source": ["docs3 = retriever.invoke(\"How did the President propose to tackle the issue of climate change?\")\n"], "metadata": {"id": "uW2rdKph9FiW"}, "execution_count": 21, "outputs": []}, {"cell_type": "code", "source": ["pretty_print_docs(docs3)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sP-B8YZGyOhA", "outputId": "cd3bb7ec-1212-4322-c589-2ccb5f14c4bc"}, "execution_count": 22, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Document 1:\n", "\n", "And we’ll do it all to withstand the devastating effects of the climate crisis and promote environmental justice. \n", "\n", "We’ll build a national network of 500,000 electric vehicle charging stations, begin to replace poisonous lead pipes—so every child—and every American—has clean water to drink at home and at school, provide affordable high-speed internet for every American—urban, suburban, rural, and tribal communities. \n", "\n", "4,000 projects have already been announced.\n", "----------------------------------------------------------------------------------------------------\n", "Document 2:\n", "\n", "Second – cut energy costs for families an average of $500 a year by combatting climate change.  \n", "\n", "Let’s provide investments and tax credits to weatherize your homes and businesses to be energy efficient and you get a tax credit; double America’s clean energy production in solar, wind, and so much more;  lower the price of electric vehicles, saving you another $80 a month because you’ll never have to pay at the gas pump again.\n", "----------------------------------------------------------------------------------------------------\n", "Document 3:\n", "\n", "My plan to fight inflation will lower your costs and lower the deficit. \n", "\n", "17 Nobel laureates in economics say my plan will ease long-term inflationary pressures. Top business leaders and most Americans support my plan. And here’s the plan: \n", "\n", "First – cut the cost of prescription drugs. Just look at insulin. One in ten Americans has diabetes. In Virginia, I met a 13-year-old boy named <PERSON>.\n", "----------------------------------------------------------------------------------------------------\n", "Document 4:\n", "\n", "Vice President <PERSON> and <PERSON> ran for office with a new economic vision for America. \n", "\n", "Invest in America. Educate Americans. Grow the workforce. Build the economy from the bottom up  \n", "and the middle out, not from the top down.  \n", "\n", "Because we know that when the middle class grows, the poor have a ladder up and the wealthy do very well. \n", "\n", "America used to have the best roads, bridges, and airports on Earth. \n", "\n", "Now our infrastructure is ranked 13th in the world.\n"]}]}, {"cell_type": "code", "source": ["from langchain_openai import OpenAI"], "metadata": {"id": "D7wlQBDTclX8"}, "execution_count": 24, "outputs": []}, {"cell_type": "code", "source": ["llm=OpenAI(temperature=0)"], "metadata": {"id": "AnY_CqOkZ3I0"}, "execution_count": 25, "outputs": []}, {"cell_type": "code", "source": ["from langchain.chains import RetrievalQA"], "metadata": {"id": "SxyNIxARczyC"}, "execution_count": 29, "outputs": []}, {"cell_type": "code", "source": ["chain = RetrievalQA.from_chain_type(llm=llm, retriever=retriever)"], "metadata": {"id": "ZTnmk8w8Ic75"}, "execution_count": 30, "outputs": []}, {"cell_type": "code", "source": ["query=\"What were the top three priorities outlined in the most recent State of the Union address?\""], "metadata": {"id": "NQxPyJsPZ6qT"}, "execution_count": 31, "outputs": []}, {"cell_type": "code", "source": ["chain.invoke(query)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WnPhNQO8Iimd", "outputId": "087ce491-378b-430a-e58d-f6b47e8d1758"}, "execution_count": 32, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'query': 'What were the top three priorities outlined in the most recent State of the Union address?',\n", " 'result': \" The top three priorities outlined in the most recent State of the Union address were investing in America's infrastructure, addressing the opioid epidemic, and creating more jobs and economic opportunities for Americans.\"}"]}, "metadata": {}, "execution_count": 32}]}, {"cell_type": "code", "source": ["print(chain.invoke(query)['result'])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dR6-pw9DI3Mh", "outputId": "57501773-d922-415a-81ab-01f318b4973c"}, "execution_count": 33, "outputs": [{"output_type": "stream", "name": "stdout", "text": [" The top three priorities outlined in the most recent State of the Union address were investing in America's infrastructure, addressing the opioid epidemic, and creating more jobs and economic opportunities for Americans.\n"]}]}, {"cell_type": "code", "source": ["from langchain.retrievers import ContextualCompressionRetriever\n", "from langchain.retrievers.document_compressors import LLMChainExtractor\n", "from langchain_openai import OpenAI"], "metadata": {"id": "rbPk6dwNyPjA"}, "execution_count": 34, "outputs": []}, {"cell_type": "code", "source": ["compressor = LLMChainExtractor.from_llm(llm)"], "metadata": {"id": "88ew_BD76xaA"}, "execution_count": 35, "outputs": []}, {"cell_type": "code", "source": ["compression_retriever=ContextualCompressionRetriever(base_compressor=compressor, base_retriever=retriever)"], "metadata": {"id": "Z2k9nibH645g"}, "execution_count": 36, "outputs": []}, {"cell_type": "code", "source": ["compressed_docs = compression_retriever.invoke(\"What did the president say about <PERSON><PERSON><PERSON>\")"], "metadata": {"id": "dNXg5XbG7hG5"}, "execution_count": 37, "outputs": []}, {"cell_type": "code", "source": ["compressed_docs"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9k8HzK7WeUCr", "outputId": "d4aa9b4d-09c1-4cb2-e201-1ad07361e8d8"}, "execution_count": 38, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>.', metadata={'source': '/content/state_of_the_union.txt'})]"]}, "metadata": {}, "execution_count": 38}]}, {"cell_type": "code", "source": ["compressed_docs = compression_retriever.invoke(\"What were the top three priorities outlined in the most recent State of the Union address?\")"], "metadata": {"id": "l__guBnv7oTg"}, "execution_count": 39, "outputs": []}, {"cell_type": "code", "source": ["compressed_docs"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2HAq8DSA8gdQ", "outputId": "38e374e4-2d8e-4808-cb63-142e2b11727a"}, "execution_count": 40, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='- So on this night, in our 245th year as a nation, I have come to report on the State of the Union. \\n- And my report is this: the State of the Union is strong—because you, the American people, are strong.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='Invest in America. Educate Americans. Grow the workforce.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='First, beat the opioid epidemic. \\n\\nThere is so much we can do. Increase funding for prevention, treatment, harm reduction, and recovery.', metadata={'source': '/content/state_of_the_union.txt'}),\n", " Document(page_content='More infrastructure and innovation in America. \\n\\nMore goods moving faster and cheaper in America. \\n\\nMore jobs where you can earn a good living in America.', metadata={'source': '/content/state_of_the_union.txt'})]"]}, "metadata": {}, "execution_count": 40}]}, {"cell_type": "code", "source": ["pretty_print_docs(compressed_docs)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Y2fcGeun8h6o", "outputId": "5b8d0f15-8ff0-42cf-eaed-9b23bfb83858"}, "execution_count": 41, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Document 1:\n", "\n", "- So on this night, in our 245th year as a nation, I have come to report on the State of the Union. \n", "- And my report is this: the State of the Union is strong—because you, the American people, are strong.\n", "----------------------------------------------------------------------------------------------------\n", "Document 2:\n", "\n", "Invest in America. Educate Americans. Grow the workforce.\n", "----------------------------------------------------------------------------------------------------\n", "Document 3:\n", "\n", "First, beat the opioid epidemic. \n", "\n", "There is so much we can do. Increase funding for prevention, treatment, harm reduction, and recovery.\n", "----------------------------------------------------------------------------------------------------\n", "Document 4:\n", "\n", "More infrastructure and innovation in America. \n", "\n", "More goods moving faster and cheaper in America. \n", "\n", "More jobs where you can earn a good living in America.\n"]}]}, {"cell_type": "code", "source": ["compressed_docs2 = compression_retriever.invoke(\"How did the President propose to tackle the issue of climate change?\")"], "metadata": {"id": "h91DraTx9Xmn"}, "execution_count": 42, "outputs": []}, {"cell_type": "code", "source": ["pretty_print_docs(compressed_docs2)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7vwNOc-a9UY5", "outputId": "5b5ff4a3-8cb3-44aa-fe32-8dd5c9542224"}, "execution_count": 43, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Document 1:\n", "\n", "We’ll do it all to withstand the devastating effects of the climate crisis and promote environmental justice. We’ll build a national network of 500,000 electric vehicle charging stations, begin to replace poisonous lead pipes—so every child—and every American—has clean water to drink at home and at school.\n", "----------------------------------------------------------------------------------------------------\n", "Document 2:\n", "\n", "combatting climate change\n"]}]}, {"cell_type": "code", "source": ["from langchain.retrievers.document_compressors import LLMChainFilter"], "metadata": {"id": "yy67l-QZ88lR"}, "execution_count": 44, "outputs": []}, {"cell_type": "code", "source": ["filter = LLMChainFilter.from_llm(llm)"], "metadata": {"id": "mfrMzQVvBrm5"}, "execution_count": 45, "outputs": []}, {"cell_type": "code", "source": ["compression_retriever2 = ContextualCompressionRetriever(base_compressor=filter, base_retriever=retriever)"], "metadata": {"id": "Viop2pL7BtZ5"}, "execution_count": 46, "outputs": []}, {"cell_type": "code", "source": ["compressed_docs3 = compression_retriever2.invoke(\"What were the top three priorities outlined in the most recent State of the Union address?\")"], "metadata": {"id": "Cl3JN_lYB2TZ"}, "execution_count": 47, "outputs": []}, {"cell_type": "code", "source": ["pretty_print_docs(compressed_docs3)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "H-K0qV4iCEYp", "outputId": "4afb4e19-de5f-4a35-d7c4-37ce759000b1"}, "execution_count": 48, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Document 1:\n", "\n", "Vice President <PERSON> and <PERSON> ran for office with a new economic vision for America. \n", "\n", "Invest in America. Educate Americans. Grow the workforce. Build the economy from the bottom up  \n", "and the middle out, not from the top down.  \n", "\n", "Because we know that when the middle class grows, the poor have a ladder up and the wealthy do very well. \n", "\n", "America used to have the best roads, bridges, and airports on Earth. \n", "\n", "Now our infrastructure is ranked 13th in the world.\n", "----------------------------------------------------------------------------------------------------\n", "Document 2:\n", "\n", "More infrastructure and innovation in America. \n", "\n", "More goods moving faster and cheaper in America. \n", "\n", "More jobs where you can earn a good living in America. \n", "\n", "And instead of relying on foreign supply chains, let’s make it in America. \n", "\n", "Economists call it “increasing the productive capacity of our economy.” \n", "\n", "I call it building a better America. \n", "\n", "My plan to fight inflation will lower your costs and lower the deficit.\n"]}]}, {"cell_type": "code", "source": ["original_contexts_len = len(\"\\n\\n\".join([d.page_content for i, d in enumerate(docs2)]))"], "metadata": {"id": "qrhbWxSYCIap"}, "execution_count": 49, "outputs": []}, {"cell_type": "code", "source": ["original_contexts_len"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hqz1<PERSON><PERSON>q<PERSON><PERSON>uy", "outputId": "beca2fde-210c-49c1-882e-140f67c1ca22"}, "execution_count": 50, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["1789"]}, "metadata": {}, "execution_count": 50}]}, {"cell_type": "code", "source": ["compressed_contexts_len = len(\"\\n\\n\".join([d.page_content for i, d in enumerate(compressed_docs)]))"], "metadata": {"id": "i3akD7BNCrEY"}, "execution_count": 53, "outputs": []}, {"cell_type": "code", "source": ["compressed_contexts_len"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ES55u8v2DAow", "outputId": "ecdf5b18-40be-4be9-d474-525c65191b00"}, "execution_count": 54, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["557"]}, "metadata": {}, "execution_count": 54}]}, {"cell_type": "code", "source": ["print(\"Original context length:\", original_contexts_len)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MjSbvEhsDSfI", "outputId": "162d47dd-db75-41a6-bb7f-3ec1b4986732"}, "execution_count": 55, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Original context length: 1789\n"]}]}, {"cell_type": "code", "source": ["print(\"Compressed context length:\", compressed_contexts_len)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XsW0VCPHDafl", "outputId": "322168c7-a0d8-4f2c-c7c3-d7c2a7e76bf7"}, "execution_count": 56, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Compressed context length: 557\n"]}]}, {"cell_type": "code", "source": ["print(\"Compressed Ratio:\", f\"{original_contexts_len/(compressed_contexts_len + 1e-5):.2f}x\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MKRrrgTUDBwx", "outputId": "ffdad194-adaf-4ea9-810c-f226d4d0c7d5"}, "execution_count": 57, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Compressed Ratio: 3.21x\n"]}]}, {"cell_type": "code", "source": ["from langchain.retrievers.document_compressors import EmbeddingsFilter"], "metadata": {"id": "nr7_yB-0Dcfy"}, "execution_count": 58, "outputs": []}, {"cell_type": "code", "source": ["from langchain_openai import OpenAIEmbeddings"], "metadata": {"id": "SXZoPZiADuwp"}, "execution_count": 59, "outputs": []}, {"cell_type": "code", "source": ["embeddings = OpenAIEmbeddings()"], "metadata": {"id": "MdVHZfxtDzm6"}, "execution_count": 60, "outputs": []}, {"cell_type": "code", "source": ["embeddings_filter = EmbeddingsFilter(embeddings=embeddings)"], "metadata": {"id": "Zk4XUWnxD1Gy"}, "execution_count": 85, "outputs": []}, {"cell_type": "code", "source": ["compression_retriever3 = ContextualCompressionRetriever(base_compressor=embeddings_filter, base_retriever=retriever)"], "metadata": {"id": "9x3cyCl_D6-R"}, "execution_count": 86, "outputs": []}, {"cell_type": "code", "source": ["compressed_docs4 = compression_retriever3.invoke(\"What were the top three priorities outlined in the most recent State of the Union address?\")"], "metadata": {"id": "7oYTzFlHgqyz"}, "execution_count": 87, "outputs": []}, {"cell_type": "code", "source": ["pretty_print_docs(compressed_docs4)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "G2TzhtGKD8nJ", "outputId": "479b0a4b-44f8-43da-ffe6-a1bd78f60f86"}, "execution_count": 88, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Document 1:\n", "\n", "Because I see the future that is within our grasp. \n", "\n", "Because I know there is simply nothing beyond our capacity. \n", "\n", "We are the only nation on Earth that has always turned every crisis we have faced into an opportunity. \n", "\n", "The only nation that can be defined by a single word: possibilities. \n", "\n", "So on this night, in our 245th year as a nation, I have come to report on the State of the Union. \n", "\n", "And my report is this: the State of the Union is strong—because you, the American people, are strong.\n", "----------------------------------------------------------------------------------------------------\n", "Document 2:\n", "\n", "Vice President <PERSON> and <PERSON> ran for office with a new economic vision for America. \n", "\n", "Invest in America. Educate Americans. Grow the workforce. Build the economy from the bottom up  \n", "and the middle out, not from the top down.  \n", "\n", "Because we know that when the middle class grows, the poor have a ladder up and the wealthy do very well. \n", "\n", "America used to have the best roads, bridges, and airports on Earth. \n", "\n", "Now our infrastructure is ranked 13th in the world.\n", "----------------------------------------------------------------------------------------------------\n", "Document 3:\n", "\n", "And soon, we’ll strengthen the Violence Against Women Act that I first wrote three decades ago. It is important for us to show the nation that we can come together and do big things. \n", "\n", "So tonight I’m offering a Unity Agenda for the Nation. Four big things we can do together.  \n", "\n", "First, beat the opioid epidemic. \n", "\n", "There is so much we can do. Increase funding for prevention, treatment, harm reduction, and recovery.\n", "----------------------------------------------------------------------------------------------------\n", "Document 4:\n", "\n", "More infrastructure and innovation in America. \n", "\n", "More goods moving faster and cheaper in America. \n", "\n", "More jobs where you can earn a good living in America. \n", "\n", "And instead of relying on foreign supply chains, let’s make it in America. \n", "\n", "Economists call it “increasing the productive capacity of our economy.” \n", "\n", "I call it building a better America. \n", "\n", "My plan to fight inflation will lower your costs and lower the deficit.\n"]}]}, {"cell_type": "code", "source": ["print(\"Original context length:\", original_contexts_len)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WLCVDKclD-b5", "outputId": "667bad3f-747b-4bcc-8d93-f64f8396cb8d"}, "execution_count": 89, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Original context length: 1789\n"]}]}, {"cell_type": "code", "source": ["compressed_contexts_len = len(\"\\n\\n\".join([d.page_content for i, d in enumerate(compressed_docs)]))"], "metadata": {"id": "BF7G0XBNEiTR"}, "execution_count": 102, "outputs": []}, {"cell_type": "code", "source": ["print(\"Compressed context length:\", compressed_contexts_len)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lFcGtUV1EU_E", "outputId": "cb40984e-51ca-472b-8513-5aba75d96b4f"}, "execution_count": 103, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Compressed context length: 1779\n"]}]}, {"cell_type": "code", "source": ["print(\"Compressed Ratio:\", f\"{original_contexts_len/(compressed_contexts_len + 1e-5):.2f}x\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "62D7JNojENBJ", "outputId": "546b0513-cf41-4dec-e867-b9833aab47bb"}, "execution_count": 104, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Compressed Ratio: 1.01x\n"]}]}, {"cell_type": "code", "source": ["from langchain.retrievers.document_compressors import DocumentCompressorPipeline\n", "from langchain_community.document_transformers import EmbeddingsRedundantFilter\n", "from langchain_text_splitters import CharacterTextSplitter\n"], "metadata": {"id": "nX-r8QUGEots"}, "execution_count": 93, "outputs": []}, {"cell_type": "code", "source": ["splitter = CharacterTextSplitter(chunk_size=300, chunk_overlap=0, separator=\". \")"], "metadata": {"id": "1rsxdeB5EtHJ"}, "execution_count": 94, "outputs": []}, {"cell_type": "code", "source": ["redundant_filter = EmbeddingsRedundantFilter(embeddings=embeddings)"], "metadata": {"id": "J_7Do4-xFYy5"}, "execution_count": 95, "outputs": []}, {"cell_type": "code", "source": ["relevant_filter = EmbeddingsFilter(embeddings=embeddings, similarity_threshold=0.76)"], "metadata": {"id": "AVaEjtW9Fbi5"}, "execution_count": 97, "outputs": []}, {"cell_type": "code", "source": ["pipeline_compressor = DocumentCompressorPipeline(transformers=[splitter, redundant_filter, relevant_filter])"], "metadata": {"id": "udt5IW7YFeDh"}, "execution_count": 98, "outputs": []}, {"cell_type": "code", "source": ["compression_retriever = ContextualCompressionRetriever(base_compressor=pipeline_compressor, base_retriever=retriever)"], "metadata": {"id": "Wdz30jmyFhjp"}, "execution_count": 99, "outputs": []}, {"cell_type": "code", "source": ["compressed_docs = compression_retriever.invoke(\"What were the top three priorities outlined in the most recent State of the Union address?\")"], "metadata": {"id": "Ix-Gx3IkFlBJ"}, "execution_count": 100, "outputs": []}, {"cell_type": "code", "source": ["pretty_print_docs(compressed_docs)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "M-0-wZOpFtqx", "outputId": "0e9d2cf4-47b8-4600-a5eb-02cc32ab2254"}, "execution_count": 101, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Document 1:\n", "\n", "So on this night, in our 245th year as a nation, I have come to report on the State of the Union. \n", "\n", "And my report is this: the State of the Union is strong—because you, the American people, are strong.\n", "----------------------------------------------------------------------------------------------------\n", "Document 2:\n", "\n", "Vice President <PERSON> and <PERSON> ran for office with a new economic vision for America. \n", "\n", "Invest in America. Educate Americans. Grow the workforce. Build the economy from the bottom up  \n", "and the middle out, not from the top down\n", "----------------------------------------------------------------------------------------------------\n", "Document 3:\n", "\n", "And soon, we’ll strengthen the Violence Against Women Act that I first wrote three decades ago. It is important for us to show the nation that we can come together and do big things. \n", "\n", "So tonight I’m offering a Unity Agenda for the Nation. Four big things we can do together\n", "----------------------------------------------------------------------------------------------------\n", "Document 4:\n", "\n", "More infrastructure and innovation in America. \n", "\n", "More goods moving faster and cheaper in America. \n", "\n", "More jobs where you can earn a good living in America. \n", "\n", "And instead of relying on foreign supply chains, let’s make it in America\n", "----------------------------------------------------------------------------------------------------\n", "Document 5:\n", "\n", "Economists call it “increasing the productive capacity of our economy.” \n", "\n", "I call it building a better America. \n", "\n", "My plan to fight inflation will lower your costs and lower the deficit.\n", "----------------------------------------------------------------------------------------------------\n", "Document 6:\n", "\n", "Because I see the future that is within our grasp. \n", "\n", "Because I know there is simply nothing beyond our capacity. \n", "\n", "We are the only nation on Earth that has always turned every crisis we have faced into an opportunity. \n", "\n", "The only nation that can be defined by a single word: possibilities\n", "----------------------------------------------------------------------------------------------------\n", "Document 7:\n", "\n", "First, beat the opioid epidemic. \n", "\n", "There is so much we can do. Increase funding for prevention, treatment, harm reduction, and recovery.\n", "----------------------------------------------------------------------------------------------------\n", "Document 8:\n", "\n", "Because we know that when the middle class grows, the poor have a ladder up and the wealthy do very well. \n", "\n", "America used to have the best roads, bridges, and airports on Earth. \n", "\n", "Now our infrastructure is ranked 13th in the world.\n"]}]}, {"cell_type": "code", "source": ["from langchain_openai import ChatOpenAI\n", "llm = ChatOpenAI(temperature=0)"], "metadata": {"id": "X0C714p9Fvwp"}, "execution_count": 105, "outputs": []}, {"cell_type": "code", "source": ["from langchain.chains import RetrievalQA"], "metadata": {"id": "dfRixlJZHa9J"}, "execution_count": 106, "outputs": []}, {"cell_type": "code", "source": ["chain = RetrievalQA.from_chain_type(llm=llm, retriever=compression_retriever)"], "metadata": {"id": "A5Mk1nxWHjja"}, "execution_count": 107, "outputs": []}, {"cell_type": "code", "source": ["query=\"What were the top three priorities outlined in the most recent State of the Union address?\""], "metadata": {"id": "I-Hf4k_1H3Q6"}, "execution_count": 108, "outputs": []}, {"cell_type": "code", "source": ["chain.invoke(query)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "nQH1xIP1H1Lx", "outputId": "c3b7c1c1-46b0-4329-8ccf-fb6b5eb2a6f7"}, "execution_count": 109, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'query': 'What were the top three priorities outlined in the most recent State of the Union address?',\n", " 'result': 'The top three priorities outlined in the most recent State of the Union address were:\\n\\n1. Strengthening the Violence Against Women Act\\n2. Implementing a Unity Agenda for the Nation, focusing on infrastructure, innovation, job creation, and domestic manufacturing\\n3. Addressing the opioid epidemic through increased funding for prevention, treatment, harm reduction, and recovery'}"]}, "metadata": {}, "execution_count": 109}]}, {"cell_type": "code", "source": ["print(chain.invoke(query)['result'])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dI5v_c62IJiZ", "outputId": "74e28fb3-bba4-4b27-a528-142c0d2f7a64"}, "execution_count": 110, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The top three priorities outlined in the most recent State of the Union address were:\n", "\n", "1. Strengthening the Violence Against Women Act\n", "2. Implementing a Unity Agenda for the Nation, focusing on infrastructure, innovation, job creation, and domestic manufacturing\n", "3. Addressing the opioid epidemic through increased funding for prevention, treatment, harm reduction, and recovery\n"]}]}, {"cell_type": "markdown", "source": ["The top three priorities outlined in the most recent State of the Union address were:\n", "\n", "1. Beating the opioid epidemic by increasing funding for prevention, treatment, harm reduction, and recovery.\n", "2. Strengthening infrastructure and innovation in America to improve transportation and create more jobs.\n", "3. Promoting domestic production and reducing reliance on foreign supply chains to boost the economy and create more opportunities for Americans."], "metadata": {"id": "kqaJuOidJBJA"}}]}