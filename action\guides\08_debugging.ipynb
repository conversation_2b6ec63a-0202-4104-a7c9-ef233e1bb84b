{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Debugging Survival Guide\n", "\n", "Hitting an exception can sometimes feel quite frustrating, particularly if you're not sure how to approach it. But this is where the best learning happens! Getting to the bottom of hard problems is a great way to learn, and can be quite satisfying.\n", "\n", "But I'm here to help if you can't!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Preliminaries\n", "\n", "Here is a briefing on Except<PERSON>, Stack Traces, Exception Handling and more:\n", "\n", "https://chatgpt.com/share/6806383c-ab38-8012-b21f-61af665bb900"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Formula for debugging\n", "\n", "There is an art and a science to great debugging. The best way to pick it up is by practice! But here are the essentials:\n", "\n", "1. Recreate  \n", "Reproduce your problem, consistently and simply\n", "\n", "2. Reduce  \n", "Simplify your problem down to the simplest possible way to reproduce it. I can't tell you how many people have sent me 100+ lines of code and said \"<PERSON>, this isn't working, please fix it\". That's not how it works! The first thing I would try is to reduce this to the core problem - ideally in 10-20 lines of code or less. In one case, I showed a student how to reproduce it with 2 lines of code!\n", "\n", "The thing is, it's much easier for **you** to do this than anybody else. So while it can be tedious, this is almost always the best way to start. Reduce down to a few lines of code. And by the way, when you do that, you often figure out the problem yourself anyway!\n", "\n", "3. Get help  \n", "Once you've recreated the problem with just a few lines of code, if you can't see what's happening - you should get help! It's worth asking ChatGPT and friends of course; give them the short example and the full stack trace.\n", "\n", "And reach out to me! I'm here to make your learning experience as awesome as possible and if you are stuck, I will help you get unstuck.\n", "\n", "### The best way to work with me for fastest fixes...\n", "\n", "1. Simplify the problem as much as possible, with an easy way to reproduce\n", "2. Include the full stack trace, and a screenshot (ask ChatGPT if you're unsure how to take screenshots)\n", "3. Email <NAME_EMAIL>. Or best of all: if you've used Google Colab before, this is a really terrific way to share an issue, because it will be identical for me to reproduce, and I can fix it and share the fix with you directly.\n", "\n", "I look forward to helping you!\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}