{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyMQn0/iuXCCHW/P3nRyAYov", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/sunnysavita10/Generative-AI-Indepth-Basic-to-Advance/blob/main/self_query_retrieval.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# Basic RAG Flow"], "metadata": {"id": "PBxuDQ4DKddt"}}, {"cell_type": "markdown", "source": ["![image.png](data:image/png;base64,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)"], "metadata": {"id": "AzoZKDjuK6OL"}}, {"cell_type": "markdown", "source": ["## When to use it:\n", "\n", "This is the most basic flow but would be very effective in documents like Pdfs where there is linearity in data and no major interdependency among different parts of documents."], "metadata": {"id": "Vg5iIbnyKgsc"}}, {"cell_type": "markdown", "source": ["## Issue:\n", "\n", "Similarity Search will filter out only top-k similar chunk which is similar to the user query but...\n", "\n", "1. It might not be relevant chunk.\n", "\n", "2. It will give top-k chunk only based on the words present in the query without having knowledge of its dependency on other chunks. This will result in Information loss."], "metadata": {"id": "y49qSaVAKgvE"}}, {"cell_type": "markdown", "source": [], "metadata": {"id": "YntyqWOOKgxc"}}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "9lGCTvc8S7tE", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "76b5a18e-aa89-401b-9098-fe51685e8386"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m974.6/974.6 kB\u001b[0m \u001b[31m19.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m327.4/327.4 kB\u001b[0m \u001b[31m28.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m64.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m232.6/232.6 kB\u001b[0m \u001b[31m26.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m27.0/27.0 MB\u001b[0m \u001b[31m55.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m321.8/321.8 kB\u001b[0m \u001b[31m31.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.4/127.4 kB\u001b[0m \u001b[31m15.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m9.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m9.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m6.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m145.0/145.0 kB\u001b[0m \u001b[31m16.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["!pip -q install langchain openai tiktoken PyPDF2 faiss-cpu"]}, {"cell_type": "code", "source": ["!pip install langchain_openai"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "KIjxALFMDeHi", "outputId": "63bd02fa-d913-4de4-8513-22607e320a1a"}, "execution_count": 15, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain_openai\n", "  Downloading langchain_openai-0.1.9-py3-none-any.whl (40 kB)\n", "\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/40.3 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m40.3/40.3 kB\u001b[0m \u001b[31m1.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: langchain-core<0.3,>=0.2.2 in /usr/local/lib/python3.10/dist-packages (from langchain_openai) (0.2.9)\n", "Requirement already satisfied: openai<2.0.0,>=1.26.0 in /usr/local/lib/python3.10/dist-packages (from langchain_openai) (1.35.3)\n", "Requirement already satisfied: tiktoken<1,>=0.7 in /usr/local/lib/python3.10/dist-packages (from langchain_openai) (0.7.0)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (6.0.1)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (1.33)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.75 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (0.1.82)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (24.1)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (2.7.4)\n", "Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (8.4.1)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.26.0->langchain_openai) (3.7.1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai<2.0.0,>=1.26.0->langchain_openai) (1.7.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.26.0->langchain_openai) (0.27.0)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.26.0->langchain_openai) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.26.0->langchain_openai) (4.66.4)\n", "Requirement already satisfied: typing-extensions<5,>=4.7 in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.26.0->langchain_openai) (4.12.2)\n", "Requirement already satisfied: regex>=2022.1.18 in /usr/local/lib/python3.10/dist-packages (from tiktoken<1,>=0.7->langchain_openai) (2024.5.15)\n", "Requirement already satisfied: requests>=2.26.0 in /usr/local/lib/python3.10/dist-packages (from tiktoken<1,>=0.7->langchain_openai) (2.31.0)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.26.0->langchain_openai) (3.7)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.26.0->langchain_openai) (1.2.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.26.0->langchain_openai) (2024.6.2)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.26.0->langchain_openai) (1.0.5)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai<2.0.0,>=1.26.0->langchain_openai) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3,>=0.2.2->langchain_openai) (3.0.0)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.2->langchain_openai) (3.10.5)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain-core<0.3,>=0.2.2->langchain_openai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.18.4 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain-core<0.3,>=0.2.2->langchain_openai) (2.18.4)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain_openai) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain_openai) (2.0.7)\n", "Installing collected packages: langchain_openai\n", "Successfully installed langchain_openai-0.1.9\n"]}]}, {"cell_type": "code", "source": ["!pip install -U langchain-community"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pAVx9tS6HxXs", "outputId": "0634c8c5-1aff-4b95-c558-7a7bd8310554"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain-community\n", "  Downloading langchain_community-0.2.5-py3-none-any.whl (2.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.2/2.2 MB\u001b[0m \u001b[31m12.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (2.0.31)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (3.9.5)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain-community)\n", "  Downloading dataclasses_json-0.6.7-py3-none-any.whl (28 kB)\n", "Requirement already satisfied: langchain<0.3.0,>=0.2.5 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.2.5)\n", "Requirement already satisfied: langchain-core<0.3.0,>=0.2.7 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.2.9)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.1.82)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (1.25.2)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (8.4.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (4.0.3)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading marshmallow-3.21.3-py3-none-any.whl (49 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.2/49.2 kB\u001b[0m \u001b[31m5.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Requirement already satisfied: langchain-text-splitters<0.3.0,>=0.2.0 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.5->langchain-community) (0.2.1)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.5->langchain-community) (2.7.4)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.7->langchain-community) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.7->langchain-community) (24.1)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.0->langchain-community) (3.10.5)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (2024.6.2)\n", "Requirement already satisfied: typing-extensions>=4.6.0 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain-community) (4.12.2)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain-community) (3.0.3)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.7->langchain-community) (3.0.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.5->langchain-community) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.18.4 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.5->langchain-community) (2.18.4)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: mypy-extensions, marshmallow, typing-inspect, dataclasses-json, langchain-community\n", "Successfully installed dataclasses-json-0.6.7 langchain-community-0.2.5 marshmallow-3.21.3 mypy-extensions-1.0.0 typing-inspect-0.9.0\n"]}]}, {"cell_type": "code", "source": ["!pip install langchain_chroma"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MKZfLjW1DPnH", "outputId": "ec87f1f8-3599-4b6e-b22a-470dc37762cb"}, "execution_count": 13, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain_chroma\n", "  Downloading langchain_chroma-0.1.1-py3-none-any.whl (8.5 kB)\n", "Collecting chromadb<0.6.0,>=0.4.0 (from langchain_chroma)\n", "  Downloading chromadb-0.5.3-py3-none-any.whl (559 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m559.5/559.5 kB\u001b[0m \u001b[31m6.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting fastapi<1,>=0.95.2 (from langchain_chroma)\n", "  Downloading fastapi-0.111.0-py3-none-any.whl (91 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m92.0/92.0 kB\u001b[0m \u001b[31m9.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: langchain-core<0.3,>=0.1.40 in /usr/local/lib/python3.10/dist-packages (from langchain_chroma) (0.2.9)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain_chroma) (1.25.2)\n", "Requirement already satisfied: build>=1.0.3 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.2.1)\n", "Requirement already satisfied: requests>=2.28 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.31.0)\n", "Requirement already satisfied: pydantic>=1.9 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.7.4)\n", "Collecting chroma-hnswlib==0.7.3 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading chroma_hnswlib-0.7.3-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.4 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.4/2.4 MB\u001b[0m \u001b[31m16.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting uvicorn[standard]>=0.18.3 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading uvicorn-0.30.1-py3-none-any.whl (62 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m62.4/62.4 kB\u001b[0m \u001b[31m7.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting posthog>=2.4.0 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading posthog-3.5.0-py2.py3-none-any.whl (41 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m41.3/41.3 kB\u001b[0m \u001b[31m5.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: typing-extensions>=4.5.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (4.12.2)\n", "Collecting onnxruntime>=1.14.1 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading onnxruntime-1.18.0-cp310-cp310-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (6.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.8/6.8 MB\u001b[0m \u001b[31m40.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting opentelemetry-api>=1.2.0 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_api-1.25.0-py3-none-any.whl (59 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m59.9/59.9 kB\u001b[0m \u001b[31m6.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting opentelemetry-exporter-otlp-proto-grpc>=1.2.0 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_exporter_otlp_proto_grpc-1.25.0-py3-none-any.whl (18 kB)\n", "Collecting opentelemetry-instrumentation-fastapi>=0.41b0 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_instrumentation_fastapi-0.46b0-py3-none-any.whl (11 kB)\n", "Collecting opentelemetry-sdk>=1.2.0 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_sdk-1.25.0-py3-none-any.whl (107 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m107.0/107.0 kB\u001b[0m \u001b[31m14.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: tokenizers>=0.13.2 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.19.1)\n", "Collecting pypika>=0.48.9 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading PyPika-0.48.9.tar.gz (67 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.3/67.3 kB\u001b[0m \u001b[31m9.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: tqdm>=4.65.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (4.66.4)\n", "Collecting overrides>=7.3.1 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading overrides-7.7.0-py3-none-any.whl (17 kB)\n", "Requirement already satisfied: importlib-resources in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (6.4.0)\n", "Requirement already satisfied: grpcio>=1.58.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.64.1)\n", "Collecting bcrypt>=4.0.1 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading bcrypt-4.1.3-cp39-abi3-manylinux_2_28_x86_64.whl (283 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m283.7/283.7 kB\u001b[0m \u001b[31m33.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: typer>=0.9.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.12.3)\n", "Collecting kubernetes>=28.1.0 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading kubernetes-30.1.0-py2.py3-none-any.whl (1.7 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.7/1.7 MB\u001b[0m \u001b[31m53.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: tenacity>=8.2.3 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (8.4.1)\n", "Requirement already satisfied: PyYAML>=6.0.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (6.0.1)\n", "Collecting mmh3>=4.0.1 (from chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading mmh3-4.1.0-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (67 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.6/67.6 kB\u001b[0m \u001b[31m9.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: orjson>=3.9.12 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.10.5)\n", "Requirement already satisfied: httpx>=0.27.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.27.0)\n", "Collecting starlette<0.38.0,>=0.37.2 (from fastapi<1,>=0.95.2->langchain_chroma)\n", "  Downloading starlette-0.37.2-py3-none-any.whl (71 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m71.9/71.9 kB\u001b[0m \u001b[31m9.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting fastapi-cli>=0.0.2 (from fastapi<1,>=0.95.2->langchain_chroma)\n", "  Downloading fastapi_cli-0.0.4-py3-none-any.whl (9.5 kB)\n", "Requirement already satisfied: jinja2>=2.11.2 in /usr/local/lib/python3.10/dist-packages (from fastapi<1,>=0.95.2->langchain_chroma) (3.1.4)\n", "Collecting python-multipart>=0.0.7 (from fastapi<1,>=0.95.2->langchain_chroma)\n", "  Downloading python_multipart-0.0.9-py3-none-any.whl (22 kB)\n", "Collecting ujson!=4.0.2,!=4.1.0,!=4.2.0,!=4.3.0,!=5.0.0,!=5.1.0,>=4.0.1 (from fastapi<1,>=0.95.2->langchain_chroma)\n", "  Downloading ujson-5.10.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (53 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m53.6/53.6 kB\u001b[0m \u001b[31m7.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting email_validator>=2.0.0 (from fastapi<1,>=0.95.2->langchain_chroma)\n", "  Downloading email_validator-2.2.0-py3-none-any.whl (33 kB)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.1.40->langchain_chroma) (1.33)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.75 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.1.40->langchain_chroma) (0.1.82)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.1.40->langchain_chroma) (24.1)\n", "Requirement already satisfied: pyproject_hooks in /usr/local/lib/python3.10/dist-packages (from build>=1.0.3->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.1.0)\n", "Requirement already satisfied: tomli>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from build>=1.0.3->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.0.1)\n", "Collecting dnspython>=2.0.0 (from email_validator>=2.0.0->fastapi<1,>=0.95.2->langchain_chroma)\n", "  Downloading dnspython-2.6.1-py3-none-any.whl (307 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m307.7/307.7 kB\u001b[0m \u001b[31m33.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: idna>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from email_validator>=2.0.0->fastapi<1,>=0.95.2->langchain_chroma) (3.7)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2024.6.2)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.0.5)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.14.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2>=2.11.2->fastapi<1,>=0.95.2->langchain_chroma) (2.1.5)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3,>=0.1.40->langchain_chroma) (3.0.0)\n", "Requirement already satisfied: six>=1.9.0 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.16.0)\n", "Requirement already satisfied: python-dateutil>=2.5.3 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.8.2)\n", "Requirement already satisfied: google-auth>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.27.0)\n", "Requirement already satisfied: websocket-client!=0.40.0,!=0.41.*,!=0.42.*,>=0.32.0 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.8.0)\n", "Requirement already satisfied: requests-oauthlib in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.3.1)\n", "Requirement already satisfied: oauthlib>=3.2.2 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.2.2)\n", "Requirement already satisfied: urllib3>=1.24.2 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.0.7)\n", "Collecting coloredlogs (from onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading coloredlogs-15.0.1-py2.py3-none-any.whl (46 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.0/46.0 kB\u001b[0m \u001b[31m5.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: flatbuffers in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma) (24.3.25)\n", "Requirement already satisfied: protobuf in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.20.3)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.12.1)\n", "Collecting deprecated>=1.2.6 (from opentelemetry-api>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading Deprecated-1.2.14-py2.py3-none-any.whl (9.6 kB)\n", "Collecting importlib-metadata<=7.1,>=6.0 (from opentelemetry-api>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading importlib_metadata-7.1.0-py3-none-any.whl (24 kB)\n", "Requirement already satisfied: googleapis-common-protos~=1.52 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.63.1)\n", "Collecting opentelemetry-exporter-otlp-proto-common==1.25.0 (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_exporter_otlp_proto_common-1.25.0-py3-none-any.whl (17 kB)\n", "Collecting opentelemetry-proto==1.25.0 (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_proto-1.25.0-py3-none-any.whl (52 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m52.5/52.5 kB\u001b[0m \u001b[31m6.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting opentelemetry-instrumentation-asgi==0.46b0 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_instrumentation_asgi-0.46b0-py3-none-any.whl (14 kB)\n", "Collecting opentelemetry-instrumentation==0.46b0 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_instrumentation-0.46b0-py3-none-any.whl (29 kB)\n", "Collecting opentelemetry-semantic-conventions==0.46b0 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_semantic_conventions-0.46b0-py3-none-any.whl (130 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m130.5/130.5 kB\u001b[0m \u001b[31m14.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting opentelemetry-util-http==0.46b0 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading opentelemetry_util_http-0.46b0-py3-none-any.whl (6.9 kB)\n", "Requirement already satisfied: setuptools>=16.0 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-instrumentation==0.46b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (67.7.2)\n", "Requirement already satisfied: wrapt<2.0.0,>=1.0.0 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-instrumentation==0.46b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.14.1)\n", "Collecting asgiref~=3.0 (from opentelemetry-instrumentation-asgi==0.46b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading asgiref-3.8.1-py3-none-any.whl (23 kB)\n", "Collecting monotonic>=1.5 (from posthog>=2.4.0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading monotonic-1.6-py2.py3-none-any.whl (8.2 kB)\n", "Collecting backoff>=1.10.0 (from posthog>=2.4.0->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading backoff-2.2.1-py3-none-any.whl (15 kB)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.9->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.18.4 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.9->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.18.4)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.28->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.3.2)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.16.4 in /usr/local/lib/python3.10/dist-packages (from tokenizers>=0.13.2->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.23.4)\n", "Requirement already satisfied: click>=8.0.0 in /usr/local/lib/python3.10/dist-packages (from typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (8.1.7)\n", "Requirement already satisfied: shellingham>=1.3.0 in /usr/local/lib/python3.10/dist-packages (from typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.5.4)\n", "Requirement already satisfied: rich>=10.11.0 in /usr/local/lib/python3.10/dist-packages (from typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (13.7.1)\n", "Collecting httptools>=0.5.0 (from uvicorn[standard]>=0.18.3->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading httptools-0.6.1-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (341 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m341.4/341.4 kB\u001b[0m \u001b[31m34.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting python-dotenv>=0.13 (from uvicorn[standard]>=0.18.3->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading python_dotenv-1.0.1-py3-none-any.whl (19 kB)\n", "Collecting uvloop!=0.15.0,!=0.15.1,>=0.14.0 (from uvicorn[standard]>=0.18.3->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading uvloop-0.19.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.4 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.4/3.4 MB\u001b[0m \u001b[31m70.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting watchfiles>=0.13 (from uvicorn[standard]>=0.18.3->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading watchfiles-0.22.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.2/1.2 MB\u001b[0m \u001b[31m71.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting websockets>=10.4 (from uvicorn[standard]>=0.18.3->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading websockets-12.0-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (130 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m130.2/130.2 kB\u001b[0m \u001b[31m14.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.2.1)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (5.3.3)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.4.0)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (4.9)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.15.3)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2023.6.0)\n", "Requirement already satisfied: zipp>=0.5 in /usr/local/lib/python3.10/dist-packages (from importlib-metadata<=7.1,>=6.0->opentelemetry-api>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.19.2)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /usr/local/lib/python3.10/dist-packages (from rich>=10.11.0->typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /usr/local/lib/python3.10/dist-packages (from rich>=10.11.0->typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.16.1)\n", "Collecting humanfriendly>=9.1 (from coloredlogs->onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma)\n", "  Downloading humanfriendly-10.0-py2.py3-none-any.whl (86 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.8/86.8 kB\u001b[0m \u001b[31m11.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: mpmath<1.4.0,>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from sympy->onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.3.0)\n", "Requirement already satisfied: mdurl~=0.1 in /usr/local/lib/python3.10/dist-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.1.2)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth>=1.0.1->kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.6.0)\n", "Building wheels for collected packages: pypika\n", "  Building wheel for pypika (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for pypika: filename=PyPika-0.48.9-py2.py3-none-any.whl size=53726 sha256=7b95753f9a6b9976202ca639865d2e27b28f69137bf02e39dbe1e5291ce2051e\n", "  Stored in directory: /root/.cache/pip/wheels/e1/26/51/d0bffb3d2fd82256676d7ad3003faea3bd6dddc9577af665f4\n", "Successfully built pypika\n", "Installing collected packages: pypika, monotonic, mmh3, websockets, uvloop, uvicorn, ujson, python-multipart, python-dotenv, overrides, opentelemetry-util-http, opentelemetry-proto, importlib-metadata, humanfriendly, httptools, dnspython, deprecated, chroma-hnswlib, bcrypt, backoff, asgiref, watchfiles, starlette, posthog, opentelemetry-exporter-otlp-proto-common, opentelemetry-api, email_validator, coloredlogs, opentelemetry-semantic-conventions, opentelemetry-instrumentation, onnxruntime, kubernetes, opentelemetry-sdk, opentelemetry-instrumentation-asgi, fastapi-cli, opentelemetry-instrumentation-fastapi, opentelemetry-exporter-otlp-proto-grpc, fastapi, chromadb, langchain_chroma\n", "  Attempting uninstall: importlib-metadata\n", "    Found existing installation: importlib_metadata 7.2.0\n", "    Uninstalling importlib_metadata-7.2.0:\n", "      Successfully uninstalled importlib_metadata-7.2.0\n", "Successfully installed asgiref-3.8.1 backoff-2.2.1 bcrypt-4.1.3 chroma-hnswlib-0.7.3 chromadb-0.5.3 coloredlogs-15.0.1 deprecated-1.2.14 dnspython-2.6.1 email_validator-2.2.0 fastapi-0.111.0 fastapi-cli-0.0.4 httptools-0.6.1 humanfriendly-10.0 importlib-metadata-7.1.0 kubernetes-30.1.0 langchain_chroma-0.1.1 mmh3-4.1.0 monotonic-1.6 onnxruntime-1.18.0 opentelemetry-api-1.25.0 opentelemetry-exporter-otlp-proto-common-1.25.0 opentelemetry-exporter-otlp-proto-grpc-1.25.0 opentelemetry-instrumentation-0.46b0 opentelemetry-instrumentation-asgi-0.46b0 opentelemetry-instrumentation-fastapi-0.46b0 opentelemetry-proto-1.25.0 opentelemetry-sdk-1.25.0 opentelemetry-semantic-conventions-0.46b0 opentelemetry-util-http-0.46b0 overrides-7.7.0 posthog-3.5.0 pypika-0.48.9 python-dotenv-1.0.1 python-multipart-0.0.9 starlette-0.37.2 ujson-5.10.0 uvicorn-0.30.1 uvloop-0.19.0 watchfiles-0.22.0 websockets-12.0\n"]}]}, {"cell_type": "code", "source": ["####if you want to use gemini feel free to use this code.\n", "\n", "'''\n", "%pip install --upgrade --quiet  google-generativeai langchain-google-genai\n", "\n", "import os\n", "from google.colab import userdata\n", "\n", "GOOGLE_API_KEY = userdata.get('GOOGLE_API_KEY')\n", "os.environ[\"GOOGLE_API_KEY\"] = GOOGLE_API_KEY\n", "\n", "from langchain_google_genai import GoogleGenerativeAIEmbeddings\n", "gemini_embeddings = GoogleGenerativeAIEmbeddings(model=\"models/embedding-001\")\n", "\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "llm = ChatGoogleGenerativeAI(model=\"gemini-1.5-pro\")\n", "\n", "result = llm.invoke(\"Write a ballad about <PERSON><PERSON><PERSON><PERSON>\")\n", "print(result.content)\n", "'''"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 87}, "id": "ZQ2jx6K-JxGe", "outputId": "d07f9d7a-5008-4ecd-8bee-b45f837a6caf"}, "execution_count": 3, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'\\n%pip install --upgrade --quiet  google-generativeai langchain-google-genai\\n\\nimport os\\nfrom google.colab import userdata\\n\\nGOOGLE_API_KEY = userdata.get(\\'GOOGLE_API_KEY\\')\\nos.environ[\"GOOGLE_API_KEY\"] = GOOGLE_API_KEY\\n\\nfrom langchain_google_genai import GoogleGenerativeAIEmbeddings\\ngemini_embeddings = GoogleGenerativeAIEmbeddings(model=\"models/embedding-001\")\\n\\nfrom langchain_google_genai import ChatGoogleGenerativeAI\\nllm = ChatGoogleGenerativeAI(model=\"gemini-1.5-pro\")\\n\\nresult = llm.invoke(\"Write a ballad about LangChain\")\\nprint(result.content)\\n'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 3}]}, {"cell_type": "code", "source": ["from google.colab import userdata\n", "OPENAI_API_KEY=userdata.get('OPENAI_API_KEY')"], "metadata": {"id": "XLiHiHPJHnC2"}, "execution_count": 4, "outputs": []}, {"cell_type": "code", "source": ["import os\n", "os.environ[\"OPENAI_API_KEY\"]=OPENAI_API_KEY"], "metadata": {"id": "TgjLGyO2HnFm"}, "execution_count": 5, "outputs": []}, {"cell_type": "code", "source": ["from langchain_chroma import Chroma\n", "from langchain_core.documents import Document\n", "from langchain_openai import OpenAIEmbeddings"], "metadata": {"id": "YNnkfnVzJlP0"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["embedding = OpenAIEmbeddings(openai_api_key=OPENAI_API_KEY)"], "metadata": {"id": "nAioNXP8HnIO"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": ["from langchain_core.documents import Document"], "metadata": {"id": "bkE6PFBfDFCX"}, "execution_count": 18, "outputs": []}, {"cell_type": "code", "source": ["docs = [\n", "    Document(\n", "        page_content=\"A bunch of scientists bring back dinosaurs and mayhem breaks loose\",\n", "        metadata={\"year\": 1993, \"rating\": 7.7, \"genre\": \"science fiction\"},\n", "    ),\n", "    Document(\n", "        page_content=\"<PERSON> gets lost in a dream within a dream within a dream within a ...\",\n", "        metadata={\"year\": 2010, \"director\": \"<PERSON>\", \"rating\": 8.2},\n", "    ),\n", "    Document(\n", "        page_content=\"A psychologist / detective gets lost in a series of dreams within dreams within dreams and Inception reused the idea\",\n", "        metadata={\"year\": 2006, \"director\": \"<PERSON><PERSON>\", \"rating\": 8.6},\n", "    ),\n", "    Document(\n", "        page_content=\"A bunch of normal-sized women are supremely wholesome and some men pine after them\",\n", "        metadata={\"year\": 2019, \"director\": \"<PERSON><PERSON>\", \"rating\": 8.3},\n", "    ),\n", "    Document(\n", "        page_content=\"Toys come alive and have a blast doing so\",\n", "        metadata={\"year\": 1995, \"genre\": \"animated\"},\n", "    ),\n", "    Document(\n", "        page_content=\"A hacker discovers reality is a simulation and leads a rebellion against the machines controlling it.\",\n", "        metadata={\"year\": 1999, \"director\": \"<PERSON>, <PERSON>\", \"rating\": 8.7, \"genre\": \"science fiction\"},\n", "    ),\n", "    Document(\n", "        page_content=\"A young lion prince flees his kingdom only to learn the true meaning of responsibility and bravery.\",\n", "        metadata={\"year\": 1994, \"rating\": 8.5, \"genre\": \"animated\"},\n", "    ),\n", "    Document(\n", "        page_content=\"<PERSON> faces off against the Joker, a criminal mastermind who plunges Gotham into chaos.\",\n", "        metadata={\"year\": 2008, \"director\": \"<PERSON>\", \"rating\": 9.0, \"genre\": \"action\"},\n", "    ),\n", "    Document(\n", "        page_content=\"A team of explorers travel through a wormhole in space in an attempt to ensure humanity's survival.\",\n", "        metadata={\"year\": 2014, \"director\": \"<PERSON>\", \"rating\": 8.6, \"genre\": \"science fiction\"},\n", "    )\n", "]\n"], "metadata": {"id": "XgS5nkYtKsq5"}, "execution_count": 19, "outputs": []}, {"cell_type": "code", "source": ["vectorstore = Chroma.from_documents(docs, embedding)"], "metadata": {"id": "LH_Te8rbO42F"}, "execution_count": 20, "outputs": []}, {"cell_type": "code", "source": ["question1 = \"Which 1994 animated movie has a rating of 8.5?\""], "metadata": {"id": "c2CFZ2TGCiQX"}, "execution_count": 40, "outputs": []}, {"cell_type": "code", "source": ["question2 = \"Which movie features <PERSON> facing off against the Joker and who directed it?\""], "metadata": {"id": "gGfmDywVCi0R"}, "execution_count": 41, "outputs": []}, {"cell_type": "code", "source": ["question3 = \"What genre is the movie 'The Matrix' and who directed it?\""], "metadata": {"id": "aicEDAEPCwWX"}, "execution_count": 42, "outputs": []}, {"cell_type": "code", "source": ["vectorstore.similarity_search(question1)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tIc5K__6QCVb", "outputId": "744ba12f-b431-431d-eadd-2df77791214e"}, "execution_count": 43, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='Toys come alive and have a blast doing so', metadata={'genre': 'animated', 'year': 1995}),\n", " Document(page_content='A young lion prince flees his kingdom only to learn the true meaning of responsibility and bravery.', metadata={'genre': 'animated', 'rating': 8.5, 'year': 1994}),\n", " Document(page_content='A bunch of scientists bring back dinosaurs and mayhem breaks loose', metadata={'genre': 'science fiction', 'rating': 7.7, 'year': 1993}),\n", " Document(page_content=\"A team of explorers travel through a wormhole in space in an attempt to ensure humanity's survival.\", metadata={'director': '<PERSON>', 'genre': 'science fiction', 'rating': 8.6, 'year': 2014})]"]}, "metadata": {}, "execution_count": 43}]}, {"cell_type": "code", "source": ["vectorstore.similarity_search(question2)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "q_rWwadnCbvZ", "outputId": "a934067a-0706-4240-f1d5-4c5441665962"}, "execution_count": 44, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='<PERSON> faces off against the Joker, a criminal mastermind who plunges Gotham into chaos.', metadata={'director': '<PERSON>', 'genre': 'action', 'rating': 9.0, 'year': 2008}),\n", " Document(page_content='A psychologist / detective gets lost in a series of dreams within dreams within dreams and Inception reused the idea', metadata={'director': '<PERSON><PERSON>', 'rating': 8.6, 'year': 2006}),\n", " Document(page_content='<PERSON> gets lost in a dream within a dream within a dream within a ...', metadata={'director': '<PERSON>', 'rating': 8.2, 'year': 2010}),\n", " Document(page_content='A bunch of scientists bring back dinosaurs and mayhem breaks loose', metadata={'genre': 'science fiction', 'rating': 7.7, 'year': 1993})]"]}, "metadata": {}, "execution_count": 44}]}, {"cell_type": "code", "source": ["retriever = vectorstore.as_retriever(search_type=\"similarity\", search_kwargs={\"k\": 3})"], "metadata": {"id": "KZD_6g1PQCaM"}, "execution_count": 25, "outputs": []}, {"cell_type": "code", "source": ["from langchain.chat_models import ChatOpenAI\n", "\n", "from operator import itemgetter\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain.schema.output_parser import StrOutputParser\n", "from langchain.schema.runnable import RunnableLambda, RunnablePassthrough\n", ""], "metadata": {"id": "mqzXhs1dRPfc"}, "execution_count": 26, "outputs": []}, {"cell_type": "code", "source": ["llm = ChatOpenAI(temperature=0.7)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qZBZ3BT7P7dT", "outputId": "18285d61-9386-482d-c762-0615dc17b115"}, "execution_count": 27, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/langchain_core/_api/deprecation.py:139: LangChainDeprecationWarning: The class `ChatOpenAI` was deprecated in LangChain 0.0.10 and will be removed in 0.3.0. An updated version of the class exists in the langchain-openai package and should be used instead. To use it run `pip install -U langchain-openai` and import as `from langchain_openai import ChatOpenAI`.\n", "  warn_deprecated(\n"]}]}, {"cell_type": "code", "source": ["import textwrap\n", "def wrap_text(text, width=90): #preserve_newlines\n", "    # Split the input text into lines based on newline characters\n", "    lines = text.split('\\n')\n", "\n", "    # Wrap each line individually\n", "    wrapped_lines = [textwrap.fill(line, width=width) for line in lines]\n", "\n", "    # Join the wrapped lines back together using newline characters\n", "    wrapped_text = '\\n'.join(wrapped_lines)\n", "\n", "    return wrapped_text"], "metadata": {"id": "03euBVg-O48T"}, "execution_count": 28, "outputs": []}, {"cell_type": "code", "source": ["template = \"\"\"Answer the question based only on the following context:\n", "{context}\n", "\n", "Question: {question}\n", "\"\"\""], "metadata": {"id": "PdJig9FhP0ks"}, "execution_count": 29, "outputs": []}, {"cell_type": "code", "source": ["prompt = ChatPromptTemplate.from_template(template)"], "metadata": {"id": "_e5sau9FP10j"}, "execution_count": 30, "outputs": []}, {"cell_type": "code", "source": ["chain = (\n", "    {\"context\": retriever, \"question\": RunnablePassthrough()}\n", "    | prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")"], "metadata": {"id": "5rTr9oesP2y8"}, "execution_count": 31, "outputs": []}, {"cell_type": "code", "source": ["question1"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "9sMfSKUUKyso", "outputId": "2440d77c-354c-401c-f418-a81c5ebd0074"}, "execution_count": 47, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Which 1994 animated movie has a rating of 8.5?'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 47}]}, {"cell_type": "code", "source": ["text_reply = chain.invoke(question1)"], "metadata": {"id": "DiIBOtGXP4X1"}, "execution_count": 45, "outputs": []}, {"cell_type": "code", "source": ["print(wrap_text(text_reply))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mpRI_8oWHnK7", "outputId": "3db8e016-f627-4d27-cafa-b182c3672a34"}, "execution_count": 46, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The 1994 animated movie with a rating of 8.5 is \"A young lion prince flees his kingdom\n", "only to learn the true meaning of responsibility and bravery.\"\n"]}]}, {"cell_type": "code", "source": ["text_reply = chain.invoke(\"Tell me about the movie which have rating more than 7.\")"], "metadata": {"id": "BtaPThxbK_Mp"}, "execution_count": 50, "outputs": []}, {"cell_type": "code", "source": ["text_reply = chain.invoke(question3)"], "metadata": {"id": "jCS1KSejK4fZ"}, "execution_count": 48, "outputs": []}, {"cell_type": "code", "source": ["\"Tell me about the movie which have rating more than 7.\""], "metadata": {"id": "QATWhyWBRzw7"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(wrap_text(text_reply))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "j-TnWmpuLBLm", "outputId": "e2b81a52-9fac-44a0-9ea8-32b21d28ac12"}, "execution_count": 51, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The movie with a rating more than 7 is \"A young lion prince flees his kingdom only to\n", "learn the true meaning of responsibility and bravery.\"\n"]}]}, {"cell_type": "markdown", "source": ["# Self Query Retrieval"], "metadata": {"id": "td-AtyFpLMns"}}, {"cell_type": "markdown", "source": ["![image.png](data:image/png;base64,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)"], "metadata": {"id": "KwGuQGyrR0DU"}}, {"cell_type": "markdown", "source": ["A self-query retriever is a retrieval system that can analyze a natural language question and use it to query itself. Here's how it works:\n", "\n", "**User Input:** You provide a question in plain English.\n", "Understanding the Question: The retriever uses a large language model (LLM) to understand the intent and meaning behind your question.\n", "\n", "**Building a Structured Query:** The LLM then translates your question into a structured query that a search engine can understand. This structured query might include keywords and filters based on the details you provided in your question.\n", "\n", "**Searching the Datastore:** The retriever uses the structured query to search its underlying datastore, which is typically a vector store.\n", "\n", "**Returning Results:** The retriever retrieves the documents from the datastore that are most relevant to your question.\n", "\n", "We use metadata-filtering to filter out the important chunks.\n", "\n", "When it can be used: It will be effective where you have to search in a small subset of the large document. Suppose you want to know about a particular department type like “Sales” in the whole document. Then you need to add this metadata info of department type in each chunk. And filter accordingly.\n", "\n", "#### Overall, self-query retrieval is a powerful technique that leverages the capabilities of LLMs to achieve a more sophisticated and user-centric approach to information retrieval within LangChain models."], "metadata": {"id": "2dWwE2jfSWQN"}}, {"cell_type": "code", "source": ["!pip install langchain"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QEFgiP7bykm5", "outputId": "c7ad3121-28bb-4984-a192-1be1275e6eed"}, "execution_count": 15, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: langchain in /usr/local/lib/python3.10/dist-packages (0.2.5)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.0.31)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.9.5)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (4.0.3)\n", "Requirement already satisfied: langchain-core<0.3.0,>=0.2.7 in /usr/local/lib/python3.10/dist-packages (from langchain) (0.2.9)\n", "Requirement already satisfied: langchain-text-splitters<0.3.0,>=0.2.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (0.2.1)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.17 in /usr/local/lib/python3.10/dist-packages (from langchain) (0.1.82)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.25.2)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.7.4)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (8.4.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.9.4)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.7->langchain) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.7->langchain) (24.1)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.17->langchain) (3.10.5)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.18.4 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (2.18.4)\n", "Requirement already satisfied: typing-extensions>=4.6.1 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (4.12.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2024.6.2)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.0.3)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.7->langchain) (3.0.0)\n"]}]}, {"cell_type": "code", "source": ["%pip install --upgrade --quiet langchain-chroma"], "metadata": {"id": "VoKRepz1y37B"}, "execution_count": 14, "outputs": []}, {"cell_type": "code", "source": ["!pip install langchain_openai"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "n9YHZpvYzGiA", "outputId": "f8696878-5f84-4471-9f06-4059e546a32a"}, "execution_count": 16, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain_openai\n", "  Downloading langchain_openai-0.1.9-py3-none-any.whl (40 kB)\n", "\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/40.3 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m40.3/40.3 kB\u001b[0m \u001b[31m1.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: langchain-core<0.3,>=0.2.2 in /usr/local/lib/python3.10/dist-packages (from langchain_openai) (0.2.9)\n", "Requirement already satisfied: openai<2.0.0,>=1.26.0 in /usr/local/lib/python3.10/dist-packages (from langchain_openai) (1.35.3)\n", "Requirement already satisfied: tiktoken<1,>=0.7 in /usr/local/lib/python3.10/dist-packages (from langchain_openai) (0.7.0)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (6.0.1)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (1.33)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.75 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (0.1.82)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (24.1)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (2.7.4)\n", "Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.2.2->langchain_openai) (8.4.1)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.26.0->langchain_openai) (3.7.1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai<2.0.0,>=1.26.0->langchain_openai) (1.7.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.26.0->langchain_openai) (0.27.0)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.26.0->langchain_openai) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.26.0->langchain_openai) (4.66.4)\n", "Requirement already satisfied: typing-extensions<5,>=4.7 in /usr/local/lib/python3.10/dist-packages (from openai<2.0.0,>=1.26.0->langchain_openai) (4.12.2)\n", "Requirement already satisfied: regex>=2022.1.18 in /usr/local/lib/python3.10/dist-packages (from tiktoken<1,>=0.7->langchain_openai) (2024.5.15)\n", "Requirement already satisfied: requests>=2.26.0 in /usr/local/lib/python3.10/dist-packages (from tiktoken<1,>=0.7->langchain_openai) (2.31.0)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.26.0->langchain_openai) (3.7)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.26.0->langchain_openai) (1.2.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.26.0->langchain_openai) (2024.6.2)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.26.0->langchain_openai) (1.0.5)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai<2.0.0,>=1.26.0->langchain_openai) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3,>=0.2.2->langchain_openai) (3.0.0)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.75->langchain-core<0.3,>=0.2.2->langchain_openai) (3.10.5)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain-core<0.3,>=0.2.2->langchain_openai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.18.4 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain-core<0.3,>=0.2.2->langchain_openai) (2.18.4)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain_openai) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain_openai) (2.0.7)\n", "Installing collected packages: langchain_openai\n", "Successfully installed langchain_openai-0.1.9\n"]}]}, {"cell_type": "code", "source": ["!pip install langchain_chroma"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FvZIEmOFOi8N", "outputId": "2e55b3f5-9ea4-40bc-81ee-bb9bb7539dd2"}, "execution_count": 17, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: langchain_chroma in /usr/local/lib/python3.10/dist-packages (0.1.1)\n", "Requirement already satisfied: chromadb<0.6.0,>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from langchain_chroma) (0.5.3)\n", "Requirement already satisfied: fastapi<1,>=0.95.2 in /usr/local/lib/python3.10/dist-packages (from langchain_chroma) (0.111.0)\n", "Requirement already satisfied: langchain-core<0.3,>=0.1.40 in /usr/local/lib/python3.10/dist-packages (from langchain_chroma) (0.2.9)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain_chroma) (1.25.2)\n", "Requirement already satisfied: build>=1.0.3 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.2.1)\n", "Requirement already satisfied: requests>=2.28 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.31.0)\n", "Requirement already satisfied: pydantic>=1.9 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.7.4)\n", "Requirement already satisfied: chroma-hnswlib==0.7.3 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.7.3)\n", "Requirement already satisfied: uvicorn[standard]>=0.18.3 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.30.1)\n", "Requirement already satisfied: posthog>=2.4.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.5.0)\n", "Requirement already satisfied: typing-extensions>=4.5.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (4.12.2)\n", "Requirement already satisfied: onnxruntime>=1.14.1 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.18.0)\n", "Requirement already satisfied: opentelemetry-api>=1.2.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.25.0)\n", "Requirement already satisfied: opentelemetry-exporter-otlp-proto-grpc>=1.2.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.25.0)\n", "Requirement already satisfied: opentelemetry-instrumentation-fastapi>=0.41b0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.46b0)\n", "Requirement already satisfied: opentelemetry-sdk>=1.2.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.25.0)\n", "Requirement already satisfied: tokenizers>=0.13.2 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.19.1)\n", "Requirement already satisfied: pypika>=0.48.9 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.48.9)\n", "Requirement already satisfied: tqdm>=4.65.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (4.66.4)\n", "Requirement already satisfied: overrides>=7.3.1 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (7.7.0)\n", "Requirement already satisfied: importlib-resources in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (6.4.0)\n", "Requirement already satisfied: grpcio>=1.58.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.64.1)\n", "Requirement already satisfied: bcrypt>=4.0.1 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (4.1.3)\n", "Requirement already satisfied: typer>=0.9.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.12.3)\n", "Requirement already satisfied: kubernetes>=28.1.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (30.1.0)\n", "Requirement already satisfied: tenacity>=8.2.3 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (8.4.1)\n", "Requirement already satisfied: PyYAML>=6.0.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (6.0.1)\n", "Requirement already satisfied: mmh3>=4.0.1 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (4.1.0)\n", "Requirement already satisfied: orjson>=3.9.12 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.10.5)\n", "Requirement already satisfied: httpx>=0.27.0 in /usr/local/lib/python3.10/dist-packages (from chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.27.0)\n", "Requirement already satisfied: starlette<0.38.0,>=0.37.2 in /usr/local/lib/python3.10/dist-packages (from fastapi<1,>=0.95.2->langchain_chroma) (0.37.2)\n", "Requirement already satisfied: fastapi-cli>=0.0.2 in /usr/local/lib/python3.10/dist-packages (from fastapi<1,>=0.95.2->langchain_chroma) (0.0.4)\n", "Requirement already satisfied: jinja2>=2.11.2 in /usr/local/lib/python3.10/dist-packages (from fastapi<1,>=0.95.2->langchain_chroma) (3.1.4)\n", "Requirement already satisfied: python-multipart>=0.0.7 in /usr/local/lib/python3.10/dist-packages (from fastapi<1,>=0.95.2->langchain_chroma) (0.0.9)\n", "Requirement already satisfied: ujson!=4.0.2,!=4.1.0,!=4.2.0,!=4.3.0,!=5.0.0,!=5.1.0,>=4.0.1 in /usr/local/lib/python3.10/dist-packages (from fastapi<1,>=0.95.2->langchain_chroma) (5.10.0)\n", "Requirement already satisfied: email_validator>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from fastapi<1,>=0.95.2->langchain_chroma) (2.2.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.1.40->langchain_chroma) (1.33)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.75 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.1.40->langchain_chroma) (0.1.82)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3,>=0.1.40->langchain_chroma) (24.1)\n", "Requirement already satisfied: pyproject_hooks in /usr/local/lib/python3.10/dist-packages (from build>=1.0.3->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.1.0)\n", "Requirement already satisfied: tomli>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from build>=1.0.3->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.0.1)\n", "Requirement already satisfied: dnspython>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from email_validator>=2.0.0->fastapi<1,>=0.95.2->langchain_chroma) (2.6.1)\n", "Requirement already satisfied: idna>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from email_validator>=2.0.0->fastapi<1,>=0.95.2->langchain_chroma) (3.7)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2024.6.2)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.0.5)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.14.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2>=2.11.2->fastapi<1,>=0.95.2->langchain_chroma) (2.1.5)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3,>=0.1.40->langchain_chroma) (3.0.0)\n", "Requirement already satisfied: six>=1.9.0 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.16.0)\n", "Requirement already satisfied: python-dateutil>=2.5.3 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.8.2)\n", "Requirement already satisfied: google-auth>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.27.0)\n", "Requirement already satisfied: websocket-client!=0.40.0,!=0.41.*,!=0.42.*,>=0.32.0 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.8.0)\n", "Requirement already satisfied: requests-oauthlib in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.3.1)\n", "Requirement already satisfied: oauthlib>=3.2.2 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.2.2)\n", "Requirement already satisfied: urllib3>=1.24.2 in /usr/local/lib/python3.10/dist-packages (from kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.0.7)\n", "Requirement already satisfied: coloredlogs in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma) (15.0.1)\n", "Requirement already satisfied: flatbuffers in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma) (24.3.25)\n", "Requirement already satisfied: protobuf in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.20.3)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.12.1)\n", "Requirement already satisfied: deprecated>=1.2.6 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-api>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.2.14)\n", "Requirement already satisfied: importlib-metadata<=7.1,>=6.0 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-api>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (7.1.0)\n", "Requirement already satisfied: googleapis-common-protos~=1.52 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.63.1)\n", "Requirement already satisfied: opentelemetry-exporter-otlp-proto-common==1.25.0 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.25.0)\n", "Requirement already satisfied: opentelemetry-proto==1.25.0 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.25.0)\n", "Requirement already satisfied: opentelemetry-instrumentation-asgi==0.46b0 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.46b0)\n", "Requirement already satisfied: opentelemetry-instrumentation==0.46b0 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.46b0)\n", "Requirement already satisfied: opentelemetry-semantic-conventions==0.46b0 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.46b0)\n", "Requirement already satisfied: opentelemetry-util-http==0.46b0 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.46b0)\n", "Requirement already satisfied: setuptools>=16.0 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-instrumentation==0.46b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (67.7.2)\n", "Requirement already satisfied: wrapt<2.0.0,>=1.0.0 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-instrumentation==0.46b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.14.1)\n", "Requirement already satisfied: asgiref~=3.0 in /usr/local/lib/python3.10/dist-packages (from opentelemetry-instrumentation-asgi==0.46b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.8.1)\n", "Requirement already satisfied: monotonic>=1.5 in /usr/local/lib/python3.10/dist-packages (from posthog>=2.4.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.6)\n", "Requirement already satisfied: backoff>=1.10.0 in /usr/local/lib/python3.10/dist-packages (from posthog>=2.4.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.2.1)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.9->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.18.4 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.9->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.18.4)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.28->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.3.2)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.16.4 in /usr/local/lib/python3.10/dist-packages (from tokenizers>=0.13.2->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.23.4)\n", "Requirement already satisfied: click>=8.0.0 in /usr/local/lib/python3.10/dist-packages (from typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (8.1.7)\n", "Requirement already satisfied: shellingham>=1.3.0 in /usr/local/lib/python3.10/dist-packages (from typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.5.4)\n", "Requirement already satisfied: rich>=10.11.0 in /usr/local/lib/python3.10/dist-packages (from typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (13.7.1)\n", "Requirement already satisfied: httptools>=0.5.0 in /usr/local/lib/python3.10/dist-packages (from uvicorn[standard]>=0.18.3->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.6.1)\n", "Requirement already satisfied: python-dotenv>=0.13 in /usr/local/lib/python3.10/dist-packages (from uvicorn[standard]>=0.18.3->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.0.1)\n", "Requirement already satisfied: uvloop!=0.15.0,!=0.15.1,>=0.14.0 in /usr/local/lib/python3.10/dist-packages (from uvicorn[standard]>=0.18.3->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.19.0)\n", "Requirement already satisfied: watchfiles>=0.13 in /usr/local/lib/python3.10/dist-packages (from uvicorn[standard]>=0.18.3->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.22.0)\n", "Requirement already satisfied: websockets>=10.4 in /usr/local/lib/python3.10/dist-packages (from uvicorn[standard]>=0.18.3->chromadb<0.6.0,>=0.4.0->langchain_chroma) (12.0)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx>=0.27.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.2.1)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (5.3.3)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.4.0)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (4.9)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.15.3)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2023.6.0)\n", "Requirement already satisfied: zipp>=0.5 in /usr/local/lib/python3.10/dist-packages (from importlib-metadata<=7.1,>=6.0->opentelemetry-api>=1.2.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.19.2)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /usr/local/lib/python3.10/dist-packages (from rich>=10.11.0->typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /usr/local/lib/python3.10/dist-packages (from rich>=10.11.0->typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (2.16.1)\n", "Requirement already satisfied: humanfriendly>=9.1 in /usr/local/lib/python3.10/dist-packages (from coloredlogs->onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma) (10.0)\n", "Requirement already satisfied: mpmath<1.4.0,>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from sympy->onnxruntime>=1.14.1->chromadb<0.6.0,>=0.4.0->langchain_chroma) (1.3.0)\n", "Requirement already satisfied: mdurl~=0.1 in /usr/local/lib/python3.10/dist-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer>=0.9.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.1.2)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth>=1.0.1->kubernetes>=28.1.0->chromadb<0.6.0,>=0.4.0->langchain_chroma) (0.6.0)\n"]}]}, {"cell_type": "code", "source": ["from langchain_chroma import Chroma\n", "from langchain_core.documents import Document\n", "from langchain_openai import OpenAIEmbeddings"], "metadata": {"id": "aBXs1QqGy8Fq"}, "execution_count": 18, "outputs": []}, {"cell_type": "code", "source": ["docs = [\n", "    Document(\n", "        page_content=\"A bunch of scientists bring back dinosaurs and mayhem breaks loose\",\n", "        metadata={\"year\": 1993, \"rating\": 7.7, \"genre\": \"science fiction\"},\n", "    ),\n", "    Document(\n", "        page_content=\"<PERSON> gets lost in a dream within a dream within a dream within a ...\",\n", "        metadata={\"year\": 2010, \"director\": \"<PERSON>\", \"rating\": 8.2},\n", "    ),\n", "    Document(\n", "        page_content=\"A psychologist / detective gets lost in a series of dreams within dreams within dreams and Inception reused the idea\",\n", "        metadata={\"year\": 2006, \"director\": \"<PERSON><PERSON>\", \"rating\": 8.6},\n", "    ),\n", "    Document(\n", "        page_content=\"A bunch of normal-sized women are supremely wholesome and some men pine after them\",\n", "        metadata={\"year\": 2019, \"director\": \"<PERSON><PERSON>\", \"rating\": 8.3},\n", "    ),\n", "    Document(\n", "        page_content=\"Toys come alive and have a blast doing so\",\n", "        metadata={\"year\": 1995, \"genre\": \"animated\"},\n", "    ),\n", "    Document(\n", "        page_content=\"A hacker discovers reality is a simulation and leads a rebellion against the machines controlling it.\",\n", "        metadata={\"year\": 1999, \"director\": \"<PERSON>, <PERSON>\", \"rating\": 8.7, \"genre\": \"science fiction\"},\n", "    ),\n", "    Document(\n", "        page_content=\"A young lion prince flees his kingdom only to learn the true meaning of responsibility and bravery.\",\n", "        metadata={\"year\": 1994, \"rating\": 8.5, \"genre\": \"animated\"},\n", "    ),\n", "    Document(\n", "        page_content=\"<PERSON> faces off against the Joker, a criminal mastermind who plunges Gotham into chaos.\",\n", "        metadata={\"year\": 2008, \"director\": \"<PERSON>\", \"rating\": 9.0, \"genre\": \"action\"},\n", "    ),\n", "    Document(\n", "        page_content=\"A team of explorers travel through a wormhole in space in an attempt to ensure humanity's survival.\",\n", "        metadata={\"year\": 2014, \"director\": \"<PERSON>\", \"rating\": 8.6, \"genre\": \"science fiction\"},\n", "    )\n", "]\n"], "metadata": {"id": "Hs6gn8S2OY-l"}, "execution_count": 19, "outputs": []}, {"cell_type": "code", "source": ["vectorstore = Chroma.from_documents(docs, embedding())"], "metadata": {"id": "hwTY5PtPzNrS"}, "execution_count": 7, "outputs": []}, {"cell_type": "code", "source": ["from langchain.chains.query_constructor.base import AttributeInfo\n", "from langchain.retrievers.self_query.base import SelfQueryRetriever\n", "from langchain_openai import ChatOpenAI"], "metadata": {"id": "I9ZhQxsXzUig"}, "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": ["metadata_field_info = [\n", "    AttributeInfo(\n", "        name=\"genre\",\n", "        description=\"The genre of the movie. One of ['science fiction', 'comedy', 'drama', 'thriller', 'romance', 'action', 'animated']\",\n", "        type=\"string\",\n", "    ),\n", "    AttributeInfo(\n", "        name=\"year\",\n", "        description=\"The year the movie was released\",\n", "        type=\"integer\",\n", "    ),\n", "    AttributeInfo(\n", "        name=\"director\",\n", "        description=\"The name of the movie director\",\n", "        type=\"string\",\n", "    ),\n", "    AttributeInfo(\n", "        name=\"rating\", description=\"A 1-10 rating for the movie\", type=\"float\"\n", "    ),\n", "]"], "metadata": {"id": "dsywdsapzW_w"}, "execution_count": 9, "outputs": []}, {"cell_type": "code", "source": ["document_content_description = \"Brief summary of a movie\""], "metadata": {"id": "XyF_rSyGzdRJ"}, "execution_count": 10, "outputs": []}, {"cell_type": "code", "source": ["from langchain.chains.query_constructor.base import (\n", "    StructuredQueryOutputParser,\n", "    get_query_constructor_prompt,\n", ")"], "metadata": {"id": "PrYeu2jl0XzI"}, "execution_count": 12, "outputs": []}, {"cell_type": "code", "source": ["prompt = get_query_constructor_prompt(\n", "    document_content_description,\n", "    metadata_field_info,\n", ")"], "metadata": {"id": "gaFP7pslzt5x"}, "execution_count": 13, "outputs": []}, {"cell_type": "code", "source": ["prompt"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SocePKXYzvj5", "outputId": "51b1c827-e549-4876-c95b-811c2d5a7aad"}, "execution_count": 14, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["FewShotPromptTemplate(input_variables=['query'], examples=[{'i': 1, 'data_source': '```json\\n{{\\n    \"content\": \"Lyrics of a song\",\\n    \"attributes\": {{\\n        \"artist\": {{\\n            \"type\": \"string\",\\n            \"description\": \"Name of the song artist\"\\n        }},\\n        \"length\": {{\\n            \"type\": \"integer\",\\n            \"description\": \"Length of the song in seconds\"\\n        }},\\n        \"genre\": {{\\n            \"type\": \"string\",\\n            \"description\": \"The song genre, one of \"pop\", \"rock\" or \"rap\"\"\\n        }}\\n    }}\\n}}\\n```', 'user_query': 'What are songs by <PERSON> or <PERSON> about teenage romance under 3 minutes long in the dance pop genre', 'structured_request': '```json\\n{{\\n    \"query\": \"teenager love\",\\n    \"filter\": \"and(or(eq(\\\\\"artist\\\\\", \\\\\"Taylor Swift\\\\\"), eq(\\\\\"artist\\\\\", \\\\\"Katy Perry\\\\\")), lt(\\\\\"length\\\\\", 180), eq(\\\\\"genre\\\\\", \\\\\"pop\\\\\"))\"\\n}}\\n```'}, {'i': 2, 'data_source': '```json\\n{{\\n    \"content\": \"Lyrics of a song\",\\n    \"attributes\": {{\\n        \"artist\": {{\\n            \"type\": \"string\",\\n            \"description\": \"Name of the song artist\"\\n        }},\\n        \"length\": {{\\n            \"type\": \"integer\",\\n            \"description\": \"Length of the song in seconds\"\\n        }},\\n        \"genre\": {{\\n            \"type\": \"string\",\\n            \"description\": \"The song genre, one of \"pop\", \"rock\" or \"rap\"\"\\n        }}\\n    }}\\n}}\\n```', 'user_query': 'What are songs that were not published on Spotify', 'structured_request': '```json\\n{{\\n    \"query\": \"\",\\n    \"filter\": \"NO_FILTER\"\\n}}\\n```'}], example_prompt=PromptTemplate(input_variables=['data_source', 'i', 'structured_request', 'user_query'], template='<< Example {i}. >>\\nData Source:\\n{data_source}\\n\\nUser Query:\\n{user_query}\\n\\nStructured Request:\\n{structured_request}\\n'), suffix='<< Example 3. >>\\nData Source:\\n```json\\n{{\\n    \"content\": \"Brief summary of a movie\",\\n    \"attributes\": {{\\n    \"genre\": {{\\n        \"description\": \"The genre of the movie. One of [\\'science fiction\\', \\'comedy\\', \\'drama\\', \\'thriller\\', \\'romance\\', \\'action\\', \\'animated\\']\",\\n        \"type\": \"string\"\\n    }},\\n    \"year\": {{\\n        \"description\": \"The year the movie was released\",\\n        \"type\": \"integer\"\\n    }},\\n    \"director\": {{\\n        \"description\": \"The name of the movie director\",\\n        \"type\": \"string\"\\n    }},\\n    \"rating\": {{\\n        \"description\": \"A 1-10 rating for the movie\",\\n        \"type\": \"float\"\\n    }}\\n}}\\n}}\\n```\\n\\nUser Query:\\n{query}\\n\\nStructured Request:\\n', prefix='Your goal is to structure the user\\'s query to match the request schema provided below.\\n\\n<< Structured Request Schema >>\\nWhen responding use a markdown code snippet with a JSON object formatted in the following schema:\\n\\n```json\\n{{\\n    \"query\": string \\\\ text string to compare to document contents\\n    \"filter\": string \\\\ logical condition statement for filtering documents\\n}}\\n```\\n\\nThe query string should contain only text that is expected to match the contents of documents. Any conditions in the filter should not be mentioned in the query as well.\\n\\nA logical condition statement is composed of one or more comparison and logical operation statements.\\n\\nA comparison statement takes the form: `comp(attr, val)`:\\n- `comp` (eq | ne | gt | gte | lt | lte | contain | like | in | nin): comparator\\n- `attr` (string):  name of attribute to apply the comparison to\\n- `val` (string): is the comparison value\\n\\nA logical operation statement takes the form `op(statement1, statement2, ...)`:\\n- `op` (and | or | not): logical operator\\n- `statement1`, `statement2`, ... (comparison statements or logical operation statements): one or more statements to apply the operation to\\n\\nMake sure that you only use the comparators and logical operators listed above and no others.\\nMake sure that filters only refer to attributes that exist in the data source.\\nMake sure that filters only use the attributed names with its function names if there are functions applied on them.\\nMake sure that filters only use format `YYYY-MM-DD` when handling date data typed values.\\nMake sure that filters take into account the descriptions of attributes and only make comparisons that are feasible given the type of data being stored.\\nMake sure that filters are only used as needed. If there are no filters that should be applied return \"NO_FILTER\" for the filter value.')"]}, "metadata": {}, "execution_count": 14}]}, {"cell_type": "code", "source": ["!pip install lark"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_jLPttCvz63R", "outputId": "74b04d04-4184-4176-ba75-5578d9a1313d"}, "execution_count": 15, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: lark in /usr/local/lib/python3.10/dist-packages (1.1.9)\n"]}]}, {"cell_type": "code", "source": ["output_parser = StructuredQueryOutputParser.from_components()"], "metadata": {"id": "tsz1M9KVz2Ib"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["query_constructor = prompt | llm | output_parser"], "metadata": {"id": "cee-pgJvLxRb"}, "execution_count": 19, "outputs": []}, {"cell_type": "code", "source": ["print(prompt.format(query=\"dummy question\"))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FP1kwZ5G02Wn", "outputId": "334422d1-4f3b-4173-adad-dd24c4ef36c2"}, "execution_count": 20, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Your goal is to structure the user's query to match the request schema provided below.\n", "\n", "<< Structured Request Schema >>\n", "When responding use a markdown code snippet with a JSON object formatted in the following schema:\n", "\n", "```json\n", "{\n", "    \"query\": string \\ text string to compare to document contents\n", "    \"filter\": string \\ logical condition statement for filtering documents\n", "}\n", "```\n", "\n", "The query string should contain only text that is expected to match the contents of documents. Any conditions in the filter should not be mentioned in the query as well.\n", "\n", "A logical condition statement is composed of one or more comparison and logical operation statements.\n", "\n", "A comparison statement takes the form: `comp(attr, val)`:\n", "- `comp` (eq | ne | gt | gte | lt | lte | contain | like | in | nin): comparator\n", "- `attr` (string):  name of attribute to apply the comparison to\n", "- `val` (string): is the comparison value\n", "\n", "A logical operation statement takes the form `op(statement1, statement2, ...)`:\n", "- `op` (and | or | not): logical operator\n", "- `statement1`, `statement2`, ... (comparison statements or logical operation statements): one or more statements to apply the operation to\n", "\n", "Make sure that you only use the comparators and logical operators listed above and no others.\n", "Make sure that filters only refer to attributes that exist in the data source.\n", "Make sure that filters only use the attributed names with its function names if there are functions applied on them.\n", "Make sure that filters only use format `YYYY-MM-DD` when handling date data typed values.\n", "Make sure that filters take into account the descriptions of attributes and only make comparisons that are feasible given the type of data being stored.\n", "Make sure that filters are only used as needed. If there are no filters that should be applied return \"NO_FILTER\" for the filter value.\n", "\n", "<< Example 1. >>\n", "Data Source:\n", "```json\n", "{\n", "    \"content\": \"Lyrics of a song\",\n", "    \"attributes\": {\n", "        \"artist\": {\n", "            \"type\": \"string\",\n", "            \"description\": \"Name of the song artist\"\n", "        },\n", "        \"length\": {\n", "            \"type\": \"integer\",\n", "            \"description\": \"Length of the song in seconds\"\n", "        },\n", "        \"genre\": {\n", "            \"type\": \"string\",\n", "            \"description\": \"The song genre, one of \"pop\", \"rock\" or \"rap\"\"\n", "        }\n", "    }\n", "}\n", "```\n", "\n", "User Query:\n", "What are songs by <PERSON> or <PERSON> about teenage romance under 3 minutes long in the dance pop genre\n", "\n", "Structured Request:\n", "```json\n", "{\n", "    \"query\": \"teenager love\",\n", "    \"filter\": \"and(or(eq(\\\"artist\\\", \\\"Taylor Swift\\\"), eq(\\\"artist\\\", \\\"Katy Perry\\\")), lt(\\\"length\\\", 180), eq(\\\"genre\\\", \\\"pop\\\"))\"\n", "}\n", "```\n", "\n", "\n", "<< Example 2. >>\n", "Data Source:\n", "```json\n", "{\n", "    \"content\": \"Lyrics of a song\",\n", "    \"attributes\": {\n", "        \"artist\": {\n", "            \"type\": \"string\",\n", "            \"description\": \"Name of the song artist\"\n", "        },\n", "        \"length\": {\n", "            \"type\": \"integer\",\n", "            \"description\": \"Length of the song in seconds\"\n", "        },\n", "        \"genre\": {\n", "            \"type\": \"string\",\n", "            \"description\": \"The song genre, one of \"pop\", \"rock\" or \"rap\"\"\n", "        }\n", "    }\n", "}\n", "```\n", "\n", "User Query:\n", "What are songs that were not published on Spotify\n", "\n", "Structured Request:\n", "```json\n", "{\n", "    \"query\": \"\",\n", "    \"filter\": \"NO_FILTER\"\n", "}\n", "```\n", "\n", "\n", "<< Example 3. >>\n", "Data Source:\n", "```json\n", "{\n", "    \"content\": \"Brief summary of a movie\",\n", "    \"attributes\": {\n", "    \"genre\": {\n", "        \"description\": \"The genre of the movie. One of ['science fiction', 'comedy', 'drama', 'thriller', 'romance', 'action', 'animated']\",\n", "        \"type\": \"string\"\n", "    },\n", "    \"year\": {\n", "        \"description\": \"The year the movie was released\",\n", "        \"type\": \"integer\"\n", "    },\n", "    \"director\": {\n", "        \"description\": \"The name of the movie director\",\n", "        \"type\": \"string\"\n", "    },\n", "    \"rating\": {\n", "        \"description\": \"A 1-10 rating for the movie\",\n", "        \"type\": \"float\"\n", "    }\n", "}\n", "}\n", "```\n", "\n", "User Query:\n", "dummy question\n", "\n", "Structured Request:\n", "\n"]}]}, {"cell_type": "code", "source": ["query_constructor.invoke(\n", "    {\n", "        \"query\": \"What are some sci-fi movies from the 90's directed by <PERSON> about taxi drivers\"\n", "    }\n", ")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lVm6aHuK08In", "outputId": "55b1167a-6926-4ed2-858a-11b651bfbeff"}, "execution_count": 21, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["StructuredQuery(query='taxi driver', filter=Operation(operator=<Operator.AND: 'and'>, arguments=[Comparison(comparator=<Comparator.EQ: 'eq'>, attribute='genre', value='science fiction'), Operation(operator=<Operator.AND: 'and'>, arguments=[Comparison(comparator=<Comparator.GTE: 'gte'>, attribute='year', value=1990), Comparison(comparator=<Comparator.LT: 'lt'>, attribute='year', value=2000)]), Comparison(comparator=<Comparator.EQ: 'eq'>, attribute='director', value='<PERSON>')]), limit=None)"]}, "metadata": {}, "execution_count": 21}]}, {"cell_type": "code", "source": ["#StructuredQuery(query='taxi driver', filter=Operation(operator=<Operator.AND: 'and'>, arguments=[Comparison(comparator=<Comparator.EQ: 'eq'>, attribute='genre', value='science fiction'), Operation(operator=<Operator.AND: 'and'>, arguments=[Comparison(comparator=<Comparator.GTE: 'gte'>, attribute='year', value=1990), Comparison(comparator=<Comparator.LT: 'lt'>, attribute='year', value=2000)]), Comparison(comparator=<Comparator.EQ: 'eq'>, attribute='director', value='<PERSON>')]), limit=None)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 158}, "id": "tWRteKd618xQ", "outputId": "04f38e29-f5e5-44bf-a2c2-b645d98d7852"}, "execution_count": 23, "outputs": [{"output_type": "error", "ename": "SyntaxError", "evalue": "invalid syntax (<ipython-input-23-0f04174b694a>, line 1)", "traceback": ["\u001b[0;36m  File \u001b[0;32m\"<ipython-input-23-0f04174b694a>\"\u001b[0;36m, line \u001b[0;32m1\u001b[0m\n\u001b[0;31m    StructuredQuery(query='taxi driver', filter=Operation(operator=<Operator.AND: 'and'>, arguments=[Comparison(comparator=<Comparator.EQ: 'eq'>, attribute='genre', value='science fiction'), Operation(operator=<Operator.AND: 'and'>, arguments=[Comparison(comparator=<Comparator.GTE: 'gte'>, attribute='year', value=1990), Comparison(comparator=<Comparator.LT: 'lt'>, attribute='year', value=2000)]), Comparison(comparator=<Comparator.EQ: 'eq'>, attribute='director', value='<PERSON> Besson')]), limit=None)\u001b[0m\n\u001b[0m                                                                   ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m invalid syntax\n"]}]}, {"cell_type": "code", "source": ["from langchain.retrievers.self_query.chroma import ChromaTranslator\n", "\n", "retriever = SelfQueryRetriever(\n", "    query_constructor=query_constructor,\n", "    vectorstore=vectorstore,\n", "    structured_query_translator=ChromaTranslator(),\n", ")"], "metadata": {"id": "nr05KF5K2E6f"}, "execution_count": 27, "outputs": []}, {"cell_type": "code", "source": ["!pip install -U langchain-community"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sAh8cvDc2aXw", "outputId": "12701520-3077-443e-be87-7d535a2b9d8c"}, "execution_count": 25, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain-community\n", "  Downloading langchain_community-0.2.5-py3-none-any.whl (2.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.2/2.2 MB\u001b[0m \u001b[31m10.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (2.0.31)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (3.9.5)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain-community)\n", "  Downloading dataclasses_json-0.6.7-py3-none-any.whl (28 kB)\n", "Requirement already satisfied: langchain<0.3.0,>=0.2.5 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.2.5)\n", "Requirement already satisfied: langchain-core<0.3.0,>=0.2.7 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.2.9)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (0.1.81)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (1.25.2)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain-community) (8.4.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (4.0.3)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading marshmallow-3.21.3-py3-none-any.whl (49 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.2/49.2 kB\u001b[0m \u001b[31m6.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Requirement already satisfied: langchain-text-splitters<0.3.0,>=0.2.0 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.5->langchain-community) (0.2.1)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain<0.3.0,>=0.2.5->langchain-community) (2.7.4)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.7->langchain-community) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.3.0,>=0.2.7->langchain-community) (24.1)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.0->langchain-community) (3.10.5)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain-community) (2024.6.2)\n", "Requirement already satisfied: typing-extensions>=4.6.0 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain-community) (4.12.2)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain-community) (3.0.3)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.7->langchain-community) (3.0.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.5->langchain-community) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.18.4 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain<0.3.0,>=0.2.5->langchain-community) (2.18.4)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: mypy-extensions, marshmallow, typing-inspect, dataclasses-json, langchain-community\n", "Successfully installed dataclasses-json-0.6.7 langchain-community-0.2.5 marshmallow-3.21.3 mypy-extensions-1.0.0 typing-inspect-0.9.0\n"]}]}, {"cell_type": "code", "source": ["retriever.invoke(\n", "    \"What's a movie after 1990 but before 2005 that's all about toys, and preferably is animated\"\n", ")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Y7Z-lHwp2quP", "outputId": "448ddfc3-95b7-4893-e294-50c81b8c4bbe"}, "execution_count": 28, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='Toys come alive and have a blast doing so', metadata={'genre': 'animated', 'year': 1995})]"]}, "metadata": {}, "execution_count": 28}]}, {"cell_type": "code", "source": ["\n", "from operator import itemgetter\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain.schema.output_parser import StrOutputParser\n", "from langchain.schema.runnable import RunnableLambda, RunnablePassthrough\n", ""], "metadata": {"id": "TBhsAYyg2xju"}, "execution_count": 29, "outputs": []}, {"cell_type": "code", "source": ["\n", "template = \"\"\"Answer the question based only on the following context:\n", "{context}\n", "\n", "Question: {question}\n", "\"\"\"\n", ""], "metadata": {"id": "7n4rEgzd4t9o"}, "execution_count": 30, "outputs": []}, {"cell_type": "code", "source": ["\n", "chain = (\n", "    {\"context\": retriever, \"question\": RunnablePassthrough()}\n", "    | prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")\n", "\n", "text_reply = chain.invoke(\"Tell me about the movie which have rating more than 7.\")\n", "\n", "print(wrap_text(text_reply))"], "metadata": {"id": "rkoVeILY4wjv"}, "execution_count": 32, "outputs": []}, {"cell_type": "code", "source": ["text_reply = chain.invoke(\"Tell me about the movie which have rating more than 7.\")\n", "\n", "print(wrap_text(text_reply))\n", ""], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "C0F9OED54zf3", "outputId": "ad18b957-8524-4e68-9bde-80889403366c"}, "execution_count": 35, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The movies with a rating of more than 7 are \"A bunch of scientists bring back dinosaurs\n", "and mayhem breaks loose\" with a rating of 7.7, \"Three men walk into the Zone, three men\n", "walk out of the Zone\" with a rating of 9.9, \"A bunch of normal-sized women are supremely\n", "wholesome and some men pine after them\" with a rating of 8.3, and \"<PERSON> gets lost\n", "in a dream within a dream within a dream within a ...\" with a rating of 8.2.\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "xcqpk8Qh47a_"}, "execution_count": null, "outputs": []}]}