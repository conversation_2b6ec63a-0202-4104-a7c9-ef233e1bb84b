{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Technical Foundations\n", "\n", "It's crucial that you feel comfortable with the basic technical concepts that we work with. This will make your experience of the entire course so much better - it can be very frustrating if you're not sure what's gong on.\n", "\n", "These guides should build confidence in the underlying technologies we work with."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Topic 1: ChatGPT versus OpenAI API\n", "\n", "### What’s the difference between ChatGPT and the GPT API, both offered by OpenAI?\n", "\n", "#### ChatGPT is an end-user tool. It’s a Chat product designed for consumers who are AI users.\n", "- It has a free plan, and it also has paid subscription plans with more features.\n", "- The subscription plans give the user near-unlimited access to use the Chat product.\n", "\n", "#### The API is a service provided for AI engineers - software engineers and data scientists - working on other commercial products.\n", "- It allows technical people, like you and me, to access the underlying models (like “GPT4.1” and “o3”) so that we can build our own products.\n", "- If we wanted to, we could build our own version of ChatGPT using the API, and charge our end-users for it.\n", "- Like most APIs, OpenAI charges a small amount based on API usage. For most examples on the course using gpt-4o-mini, it’s of the order of $0.001 per API call.\n", "\n", "### I’m paying $20/month for ChatGPT - why do I need to pay more for the API?\n", "\n", "- Hopefully this is now clear. The API is not for consumers; it’s for engineers to build their own platforms that they can charge for.\n", "- If you were to have access to the API based on your subscription, then you could offer ChatGPT tools to others at a cheaper price, and put OpenAI out of business!\n", "- Keep in mind: each API call may require 10,000,000,000,000 floating point calculations - that compute uses electricity!\n", "\n", "Instead of calling the API, you can run open source models locally, but typically they have 1,000 times fewer calculations — and even though it’s tiny, that processing still hits your electricity bill.."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Topic 2: Environment Variables and the `.env` file\n", "\n", "This tutorial walks you through everything you need to know about .env files!\n", "\n", "Obiously you don't need to add the .env file to .gitignore, as I've already done that for you. But it hopefully explains the point well.\n", "\n", "https://chatgpt.com/share/68061e89-dd84-8012-829d-9f4506c7baaa"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Topic 3: Networking basics\n", "\n", "This tutorial covers networking and typical issues with certificates, VPNs, DNS and the like.\n", "\n", "The sections give a summary; you should ask ChatGPT to expand on any section if it's relevant to your situation.\n", "\n", "https://chatgpt.com/share/680620ec-3b30-8012-8c26-ca86693d0e3d"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Topic 4: APIs and Client Libraries - foundational briefing\n", "\n", "We use APIs a lot in this course!\n", "\n", "It's essential to understand the fundamentals of what's going on when we make a call to an API, and to be comfortable with words like \"endpoint\" and \"client library\".\n", "\n", "Please review this guide:\n", "\n", "https://chatgpt.com/share/68062432-43c8-8012-ad91-6311d4ad5858"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}