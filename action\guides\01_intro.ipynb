{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Welcome to the Guides!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Introduction\n", "\n", "I've designed this course to be suitable for a wide range of backgrounds. For those of you relatively new to this, I've prepared some technical briefings to build your expertise.\n", "\n", "These are designed to be self-study; you work through them at your own pace, investigating and experimenting.\n", "\n", "I've heavily taken advantage of our AI friends to write some guides, and I've tried to frame them so they're as useful as possible.\n", "\n", "There's only one requirement for the course: plenty of patience! Keep in mind that one of the best ways to learn is by solving problems - if you feel frustrated with a challenging puzzle, remember that this is where the learning happens! And, get in touch if I can help."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Contents (select from the sub-directory in the explorer on the left)\n", "\n", "1. Intro - this guide!\n", "2. The command line\n", "3. <PERSON><PERSON> and <PERSON><PERSON><PERSON>\n", "4. Technical foundations (environment variables, networks, APIs)\n", "5. Notebooks\n", "6. Python foundations\n", "7. \"Vibe coding\" - successfully coding with the help of LLMs\n", "8. Debugging techniques\n", "9. APIs and Ollama\n", "10. Intermediate level python, including decorators and async  \n", "11. Asyncronous Python\n", "12. Starting your project - 3 crucial pieces of advice"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}